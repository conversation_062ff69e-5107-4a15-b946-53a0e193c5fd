package com.sayweee.weee.module.post.profile;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

class ProfileIntentCreatorImpl implements ProfileIntentCreator.Creator {

    @NonNull
    @Override
    public Intent getIntentOnClickMineProfileAvatar(Context context) {
        return ProfileActivity.getIntent(context);
    }

    @Nullable
    @Override
    public Intent getIntentOnClickReviewAvatar(Context context, String source, String uid) {
        return ProfileActivity.getIntent(context, source, uid);
    }

}
