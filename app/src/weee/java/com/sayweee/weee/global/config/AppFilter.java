package com.sayweee.weee.global.config;

import com.sayweee.weee.global.manager.LanguageManager;

public class AppFilter {

    public static class AuthConfig {
        public static boolean isNotSupport(String authType) {
            return false;
        }
    }

    public static class ShareConfig {
        public static boolean isNotSupport(String shareType) {
            return false;
        }
    }

    public static class PayConfig {

        public static boolean isNotSupport(String payType) {
            return false;
        }

        public static boolean isChannelCodeNotSupport(String channelCode) {
            return false;
        }
    }

    public static class TabConfig {
        public static boolean isNotSupport(int tabIndex) {
            return false;
        }
    }

    public static class LangConfig {
        public static boolean isNotSupport(String lang) {
            boolean support = LanguageManager.Language.ENGLISH.equals(lang)
                    || LanguageManager.Language.CHINESE.equals(lang)
                    || LanguageManager.Language.CHINESE_OLD.equals(lang)
                    || LanguageManager.Language.KOREAN.equals(lang)
                    || LanguageManager.Language.JAPANESE.equals(lang)
                    || LanguageManager.Language.TRADITIONAL_CHINESE.equals(lang)
                    || LanguageManager.Language.VIETNAMESE.equals(lang)
                    || LanguageManager.Language.THAI.equals(lang);
            return !support;
        }
    }

}
