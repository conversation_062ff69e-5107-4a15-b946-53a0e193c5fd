package com.sayweee.weee.global.config;

import com.sayweee.service.payment.PaymentKt;

public final class VariantConfig {

    // Api conf
    public static final String UA_PLATFORM = "WeeeApp";
    public static final String WEEE_CHANNEL = "";
    public static final String APP_BU = null;
    public static final String AUTH_TOKEN_CHANNEL = "main";
    public static final String HOST_AF_LINK = "weeeone.onelink.me";
    public static final String HOST_AF_CUSTOM_LINK = "go.sayweee.com";

    // Module conf
    public static final boolean IS_COMMENT_TRIGGER_ENABLED = true; // review comment 里的 @ # + 功能
    public static final boolean IS_REVIEW_SHARE_ENABLED = true ;// review 分享
    public static final boolean IS_USER_FOLLOW_BTN_ENABLED = true; // 是否显示关注按钮
    public static final boolean IS_REVIEW_TO_PROFILE_ENABLED = true; // 点击头像或昵称进入用户主页
    public static final boolean IS_VIEW_VISIBLE = true; // 视频与profile 控件是否展示

    // Config conf
    public static final String DYNAMIC_CONFIG_CHANNEL = "weee";
    public static final String CONFIG_APP_ICON_KEY = "app_icon_key";

    //home api key
    public static final String HOME_API_KEY = "sayweee_mobile_home";

    // order list button to review
    public static final String ORDER_BUTTON_REVIEW_URL = "/social/post-review/review-order/";

    //last login success mark name
    public static final boolean LAST_LOGIN_MARK_ENABLED = true;

    // Payment channel codes
    public static final String PAYMENT_CHANNEL_CODE_EBT = PaymentKt.PAY_CHANNEL_CODE_EBT;

    private VariantConfig() {

    }

}
