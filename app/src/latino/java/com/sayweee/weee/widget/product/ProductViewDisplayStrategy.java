package com.sayweee.weee.widget.product;

import com.sayweee.weee.module.cart.bean.EntranceTag;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.utils.CollectionUtils;

public class ProductViewDisplayStrategy extends ProductViewDisplayStrategyBase {

    protected ProductViewDisplayStrategy(int displayStyle) {
        super(displayStyle);
    }

    /**
     * 定义是否展示商品的已售数量。
     * <p>
     * 继承了基类的判断逻辑，并增加了额外的条件：
     * 如果商品是 MINI 样式，且带有“再次购买”标签，则不显示已售数量。
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return 如果应显示已售数量，则返回 true；否则返回 false
     * @see <a href="https://sayweee.atlassian.net/browse/PEP-14424">PEP-14424</a>
     */
    @Override
    protected boolean shouldDisplayDynamicSoldNum(ProductBean bean, boolean canShow) {
        boolean shouldDisplay = super.shouldDisplayDynamicSoldNum(bean, canShow);
        if (shouldDisplay && displayStyle == ProductView.STYLE_ITEM_MINI) {
            EntranceTag entranceTag = CollectionUtils.firstOrNull(getDisplayProductTags(bean));
            return entranceTag == null || !"buy_again".equals(entranceTag.tag_key);
        }
        return shouldDisplay;
    }
}
