package com.sayweee.weee.widget.product;

import android.util.SparseIntArray;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.cart.bean.EntranceTag;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 一个抽象基类，用于定义在各种视图中显示商品信息的策略。
 * <p>
 * 该类遵循策略模式，以控制商品卡片上不同元素（例如商品名称、价格、标签和卖家信息）的可见性、布局和样式。
 * 它旨在由实现不同商品风味或视图类型的显示逻辑的具体策略类进行扩展。
 *
 * @see com.sayweee.weee.widget.product.ProductViewDisplayStrategy
 * app/src/latino/java/com/sayweee/weee/widget/product/ProductViewDisplayStrategy.java
 * app/src/weee/java/com/sayweee/weee/widget/product/ProductViewDisplayStrategy.java
 */
public abstract class ProductViewDisplayStrategyBase {

    public static final int DYNAMIC_ITEM_TAG_LIST = 1;
    public static final int DYNAMIC_ITEM_MKPL_SHIPS_FROM = 2;
    public static final int DYNAMIC_ITEM_MKPL_ETA_RANGE = 3;
    public static final int DYNAMIC_ITEM_REMAINING = 4;
    public static final int DYNAMIC_ITEM_SOLD_NUM = 5;
    public static final int DYNAMIC_ITEM_TOP_X = 6;
    public static final int DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC = 7;

    private static final int[] DYNAMIC_ITEMS = new int[]{
            DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC, // Pre-sale delivery desc
            DYNAMIC_ITEM_TAG_LIST, // Tag list - labelsView(iOS)
            DYNAMIC_ITEM_MKPL_SHIPS_FROM, // mkpl ships from - sold by (iOS)
            DYNAMIC_ITEM_TOP_X, // top_x - entrance_tag (iOS)
            DYNAMIC_ITEM_MKPL_ETA_RANGE, // mkpl eta_range - eta (iOS)
            DYNAMIC_ITEM_REMAINING, // remaining_tip - left (iOS)
            DYNAMIC_ITEM_SOLD_NUM, // sold_num - sold count (iOS)
    };

    private static final SparseIntArray VISIBLE_ITEMS = CollectionUtils.intArrayOf(DYNAMIC_ITEMS);

    protected int displayStyle;
    public boolean isShowMkplVendor = true; // show mkpl vendor name, default true

    protected ProductViewDisplayStrategyBase(@ProductView.DisplayStyle int displayStyle) {
        setDisplayStyle(displayStyle);
    }

    public void setDisplayStyle(int displayStyle) {
        this.displayStyle = displayStyle;
    }

    public void setShowMkplVendor(boolean showMkplVendor) {
        isShowMkplVendor = showMkplVendor;
    }

    /**
     * 定义显示在商品卡片上的商品名称行数
     *
     * @return 商品名称行数，> 0 表示固定行数，-1表示不限制行数
     */
    public int getProductNameLines() {
        if (isCompactStyle()) {
            return 2;
        }
        return -1;
    }

    /**
     * 定义显示在商品卡片上的商品名称最大行数
     *
     * @return 商品名称最大行数，> 0 表示固定行数，Integer.MAX_VALUE 表示不限制行数
     */
    public int getProductNameMaxLines() {
        if (isCompactStyle()) {
            return 2;
        }
        return 3;
    }

    /**
     * 定义显示在商品卡片上的动态信息最大数量
     *
     * @return 动态信息最大数量，> 0 表示固定数量，Integer.MAX_VALUE 表示不限制数量
     */
    public int getDynamicItemsMax() {
        if (isCompactStyle()) {
            return 2;
        }
        return Integer.MAX_VALUE;
    }

    /**
     * 定义显示在商品卡片上的动态信息类型，按显示优先级由高到低排列
     *
     * @return 动态信息类型数组
     */
    public final int[] getDynamicItems() {
        return DYNAMIC_ITEMS;
    }

    /**
     * 定义需要显示在商品卡片上的的动态信息类型
     * 通过自定义可实现直接隐藏信息，隐藏的信息不影响信息优先级判断
     *
     * @return 动态信息类型数组
     */
    public SparseIntArray getVisibleItems() {
        return VISIBLE_ITEMS;
    }

    /**
     * 获取商品卡片上需要显示的元素，用于计算商品卡片高度
     *
     * @param bean 商品信息
     * @return 计算后显示的元素信息
     */
    public final ProductViewElements getDisplayElements(@NonNull ProductBean bean) {
        List<Integer> displayItems = getDisplayItems(bean);
        ProductViewElements elements = new ProductViewElements();
        elements.hasVolumePrice = shouldDisplayVolumePrice(bean);
        elements.hasPrice = !elements.hasVolumePrice;
        elements.hasProductBrandName = shouldDisplayBrandName(bean);
        elements.hasName = true;
        elements.hasDeliveryDesc = displayItems.contains(DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC);
        elements.hasProductTags = displayItems.contains(DYNAMIC_ITEM_TAG_LIST);
        elements.hasRemainingTip = displayItems.contains(DYNAMIC_ITEM_REMAINING);
        elements.hasSoldNum = displayItems.contains(DYNAMIC_ITEM_SOLD_NUM);
        elements.hasEtaRange = displayItems.contains(DYNAMIC_ITEM_MKPL_ETA_RANGE);
        elements.hasVendor = displayItems.contains(DYNAMIC_ITEM_MKPL_SHIPS_FROM);
        elements.hasTopX = displayItems.contains(DYNAMIC_ITEM_TOP_X);
        return elements;
    }

    /**
     * 获取商品卡片上需要显示的动态信息
     * 根据 #getDynamicItems() 获取动态信息的优先级
     * 根据 #getDynamicItemsMax() 获取动态信息的最大显示数量
     *
     * @param bean 商品信息
     * @return 显示的动态信息类型列表
     */
    public List<Integer> getDisplayItems(@NonNull ProductBean bean) {
        List<Integer> displayItems = new ArrayList<>();
        int displayMax = getDynamicItemsMax();
        for (int displayItem : getDynamicItems()) {
            boolean canShow = displayItems.size() < displayMax;
            switch (displayItem) {
                case DYNAMIC_ITEM_TAG_LIST: // BOGO - labelsView(iOS)
                    if (shouldDisplayDynamicProductTagList(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_MKPL_SHIPS_FROM: // mkpl ships from - sold by (iOS)
                    if (shouldDisplayDynamicMkplShipsFrom(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_MKPL_ETA_RANGE: // mkpl eta_range - eta (iOS)
                    if (shouldDisplayDynamicMkplEtaRange(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_REMAINING: // remaining_tip - left (iOS)
                    if (shouldDisplayDynamicRemainingTip(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_SOLD_NUM: // sold_num - sold count (iOS)
                    if (shouldDisplayDynamicSoldNum(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_TOP_X: // top_x - entrance_tag (iOS)
                    if (shouldDisplayDynamicTopX(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                case DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC:
                    if (shouldDisplayDynamicPreSaleDeliveryDesc(bean, canShow)) {
                        displayItems.add(displayItem);
                    }
                    break;
                default:
                    break;
            }
        }
        return displayItems;
    }


    /**
     * 定义是否显示商品收藏按钮
     *
     * @return true 显示；false 不显示
     */
    public boolean shouldDisplayProductCollect() {
        return !isCompactStyle();
    }

    /**
     * 定义是否显示商品 Volume Price
     *
     * @param bean 商品信息
     * @return true 显示；false 不显示
     */
    public boolean shouldDisplayVolumePrice(ProductBean bean) {
        return bean.showVolumePrice();
    }

    /**
     * 定义是否显示商品品牌或者是否为赞助
     *
     * @param bean 商品信息
     * @return true 显示；false 不显示
     */
    public boolean shouldDisplayBrandName(ProductBean bean) {
        boolean showSponsored = bean.is_sponsored && !EmptyUtils.isEmpty(bean.sponsored_text);
        boolean showBrand = !EmptyUtils.isEmpty(bean.brand_name);
        return showSponsored || showBrand;
    }


    /**
     * 定义是否限制商品标签显示数量
     *
     * @return true 限制为1个；false 不限制
     */
    public boolean shouldLimitProductTags() {
        return isCompactStyle();
    }

    /**
     * 定义展示的商品标签信息列表
     * 根据 #shouldLimitProductTags() 限制标签数量
     *
     * @param bean 商品信息
     * @return 商品标签信息列表
     */
    @NonNull
    public List<EntranceTag> getDisplayProductTags(ProductBean bean) {
        ProductViewHelper.prependProductTagBuyAgain(bean);
        List<EntranceTag> tagList;
        tagList = shouldLimitProductTags()
                ? CollectionUtils.subList(bean.product_tag_list, 0, 1)
                : CollectionUtils.orEmptyList(bean.product_tag_list);
        return tagList;
    }

    /**
     * 定义是否展示商品标签
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicProductTagList(ProductBean bean, boolean canShow) {
        return isDynamicItemVisible(DYNAMIC_ITEM_TAG_LIST)
                && canShow
                && !EmptyUtils.isEmpty(getDisplayProductTags(bean));
    }

    /**
     * 定义是否展示3p商品的卖家信息
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicMkplShipsFrom(ProductBean bean, boolean canShow) {
        boolean limitTags = shouldLimitProductTags();
        String vendorDeliveryDesc = bean.getVendorDeliveryNewDesc(bean.freeShippingDescShow(bean, limitTags, true));
        boolean deliveryInfoVisible = isShowMkplVendor && !EmptyUtils.isEmpty(vendorDeliveryDesc);
        return isDynamicItemVisible(DYNAMIC_ITEM_MKPL_SHIPS_FROM)
                && canShow
                && deliveryInfoVisible;
    }

    /**
     * 定义是否展示3p商品的预计送达时间
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicMkplEtaRange(ProductBean bean, boolean canShow) {
        boolean hasEtaRange = isShowMkplVendor && bean.isSeller() && bean.vender_info_view != null && !EmptyUtils.isEmpty(bean.vender_info_view.eta_range);
        return isDynamicItemVisible(DYNAMIC_ITEM_MKPL_ETA_RANGE)
                && canShow
                && hasEtaRange;
    }

    /**
     * 定义是否展示商品的即将售罄提示
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicRemainingTip(ProductBean bean, boolean canShow) {
        return isDynamicItemVisible(DYNAMIC_ITEM_REMAINING)
                && canShow
                && OrderHelper.isShowRemainingTip(bean.remaining_count, bean.sold_count);
    }

    /**
     * 定义是否展示商品的已售数量
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicSoldNum(ProductBean bean, boolean canShow) {
        return isDynamicItemVisible(DYNAMIC_ITEM_SOLD_NUM)
                && canShow
                && !EmptyUtils.isEmpty(bean.sold_count_ui);
    }

    /**
     * 定义是否展示商品的配送信息
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicPreSaleDeliveryDesc(ProductBean bean, boolean canShow) {
        return isDynamicItemVisible(DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC)
                && canShow
                && !EmptyUtils.isEmpty(bean.delivery_desc);
    }

    /**
     * 定义是否展示商品的榜单信息
     *
     * @param bean    商品信息
     * @param canShow 如果还有空间，是否可以显示此项
     * @return true 展示；false 不展示
     */
    protected boolean shouldDisplayDynamicTopX(ProductBean bean, boolean canShow) {
        return isDynamicItemVisible(DYNAMIC_ITEM_TOP_X)
                && canShow
                && displayStyle != ProductView.STYLE_ITEM_SEARCH_V2
                && bean.entrance_tag != null && !EmptyUtils.isEmpty(bean.entrance_tag.tag_name);
    }

    /**
     * 检查指定的动态信息类型是否被允许显示
     *
     * @param itemType 要检查的动态信息类型
     * @return 如果允许显示则返回 true；否则返回 false
     */
    protected final boolean isDynamicItemVisible(int itemType) {
        return getVisibleItems().indexOfKey(itemType) >= 0;
    }

    /**
     * 检查当前显示样式是否为紧凑型（例如 small, mini, search_v2）
     *
     * @return 如果是紧凑型样式则返回 true；否则返回 false
     */
    private boolean isCompactStyle() {
        return displayStyle == ProductView.STYLE_ITEM_SMALL
                || displayStyle == ProductView.STYLE_ITEM_MINI
                || displayStyle == ProductView.STYLE_ITEM_SEARCH_V2;
    }

}
