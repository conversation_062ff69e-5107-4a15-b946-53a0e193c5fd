package com.sayweee.weee.widget.banner.ex;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.module.home.provider.banner.PlayerPayload;
import com.sayweee.weee.player.bean.MediaData;
import com.sayweee.weee.player.mute.MutePlayer;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.shuyu.gsyvideoplayer.utils.GSYVideoType;
import com.youth.banner.Banner;
import com.youth.banner.adapter.BannerAdapter;
import com.youth.banner.util.BannerUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2022/10/27.
 * Desc:
 */
public class ExCarouselBanner<T, BA extends BannerAdapter<T, ? extends RecyclerView.ViewHolder>> extends CarouselBanner<T, BA> {

    private static final String TAG = "video =====";
    protected boolean enable = false;

    protected List<MediaData> list;

    protected Runnable dispatchRunnable = new Runnable() {

        @Override
        public void run() {
            Logger.enable(enable).json(TAG, "notifyPlayerStatusChanged: " + lastIndex + " status: true");
            notifyPlayerStatusChanged(lastIndex, true);
        }
    };

    public ExCarouselBanner(Context context) {
        this(context, null);
    }

    public ExCarouselBanner(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExCarouselBanner(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (context instanceof LifecycleOwner) {
            addBannerLifecycleObserver((LifecycleOwner) context);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        onPagePause();
        super.onDetachedFromWindow();
    }

    public ExCarouselBanner setBannerData(
            List<? extends MediaData<?>> list,
            boolean isInfiniteLoop,
            @NonNull ExBannerLayoutProvider provider
    ) {
        List<MediaData> dataList = list != null ? new ArrayList<>(list) : null;
        ExBannerAdapter adapter = new ExBannerAdapter(dataList, /* preLoad= */false);
        adapter.setLayoutProvider(provider);
        ((Banner) this).setAdapter(adapter, isInfiniteLoop);
        lastIndex = BannerUtils.getRealPosition(isInfiniteLoop(), getCurrentItem(), getRealCount());
        return this;
    }

    public boolean isBannerVisible() {
        Rect rect = new Rect();
        boolean isVisible = getLocalVisibleRect(rect);
        return isVisible && rect.height() * 2 > getHeight();
    }

    public void onPageResume() {
        notifyLifecycleStatusChanged(getLastIndex(), true, true);
    }

    public void onPagePause() {
        notifyLifecycleStatusChanged(getLastIndex(), false, false);
    }

    public void onPageDetached() {
        onPagePause();
    }

    @Override
    public void onPageSelected(int position) {
        notifyPlayerStatusChanged(lastIndex, false);
        super.onPageSelected(position);
        removeCallbacks(dispatchRunnable);
        Logger.enable(enable).json(TAG, "onPageSelected old " + lastIndex + " new " + position);
        postDelayed(dispatchRunnable, 600);
        lastIndex = position;
    }

    public void notifyLifecycleChanged(int status) {
        if (status == PlayerPayload.STATUS_NOTIFY) {
            Logger.enable(enable).json(TAG, "notifyLifecycleChanged: scrolled changed ");
            notifyLifecycleStatusChanged(getLastIndex(), true, false);
        } else if (status == PlayerPayload.STATUS_PAUSE) {
            Logger.enable(enable).json(TAG, "notifyLifecycleChanged: onPagePause ");
            onPagePause();
        } else {
            Logger.enable(enable).json(TAG, "notifyLifecycleChanged: onPageResume ");
            onPageResume();
        }
    }

    public void notifyLifecycleStatusChanged(int position, boolean isResume, boolean isForce) {
        removeCallbacks(dispatchRunnable);
        if (isResume) {
            isResume = isBannerVisible();
        }
        notifyPlayerStatusChanged(position, isResume, isForce);
    }

    public void setLastIndex(int lastIndex) {
        this.lastIndex = lastIndex;
    }

    protected int getLastIndex() {
        return lastIndex;
    }

    public void notifyPlayerStatusChanged(int position, boolean bool) {
        notifyPlayerStatusChanged(position, bool, true);
    }

    public void notifyPlayerStatusChanged(int position, boolean isPlay, boolean force) {
        BannerAdapter adapter = getAdapter();
        if (adapter != null) {
            int count = adapter.getRealCount();
            MutePlayer player;
            if (position >= 0 && position < count) {
                player = getCurVideoPlayer(position);
            } else {
                player = null;
            }
            if (player != null) {
                if (isPlay) {
                    player.toPlayStatus(force, GSYVideoType.SCREEN_TYPE_DEFAULT);
                } else {
                    player.toPauseStatus();
                }
            }
        }
    }

    @Nullable
    private MutePlayer getCurVideoPlayer(int position) {
        position = isInfiniteLoop() ? position + 1 : position;
        ViewPager2 viewPager2 = getViewPager2();
        if (viewPager2 == null) {
            return null;
        }
        View recyclerView = viewPager2.getChildAt(0);
        // 判断依据来源于ViewPager2中 attachViewToParent(mRecyclerView, 0, mRecyclerView.getLayoutParams());
        if (!(recyclerView instanceof RecyclerView)) {
            return null;
        }
        RecyclerView.LayoutManager layoutManager = ((RecyclerView) recyclerView).getLayoutManager();
        if (layoutManager == null) {
            return null;
        }
        View child = layoutManager.findViewByPosition(position);
        if (child == null) {
            return null;
        }
        View view = child.findViewById(R.id.player);
        if (view instanceof MutePlayer) {
            return (MutePlayer) view;
        }
        return null;
    }

}
