package com.sayweee.weee.widget.product;

public class ProductViewElements {

    // R.id.layout_volume
    public boolean hasVolumePrice;

    // R.id.tv_price_flow
    public boolean hasPrice;

    // R.id.tv_brand_name
    public boolean hasProductBrandName;

    // R.id.tv_product_name
    public boolean hasName;

    // R.id.tv_delivery_desc
    public boolean hasDeliveryDesc;

    // R.id.layout_tags
    public boolean hasProductTags;

    // R.id.tv_remaining_tip
    public boolean hasRemainingTip;

    // R.id.tv_sold_num
    public boolean hasSoldNum;

    // R.id.tv_eta_range
    public boolean hasEtaRange;

    // R.id.tv_vender
    public boolean hasVendor;

    // R.id.layout_top_x_parent
    public boolean hasTopX;

    public boolean hasVolumePrice() {
        return hasVolumePrice;
    }

    public boolean hasPrice() {
        return hasPrice;
    }

    public boolean hasProductBrandName() {
        return hasProductBrandName;
    }

    public boolean hasName() {
        return hasName;
    }

    public boolean hasDeliveryDesc() {
        return hasDeliveryDesc;
    }

    public boolean hasProductTags() {
        return hasProductTags;
    }

    public boolean hasRemainingTip() {
        return hasRemainingTip;
    }

    public boolean hasSoldNum() {
        return hasSoldNum;
    }

    public boolean hasEtaRange() {
        return hasEtaRange;
    }

    public boolean hasVendor() {
        return hasVendor;
    }

    public boolean hasTopX() {
        return hasTopX;
    }
}
