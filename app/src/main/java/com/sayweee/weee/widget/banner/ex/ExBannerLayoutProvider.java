package com.sayweee.weee.widget.banner.ex;

import androidx.annotation.LayoutRes;

public interface ExBannerLayoutProvider {

    @LayoutRes
    int getImageLayoutRes();

    @LayoutRes
    int getVideoLayoutRes();


    class Factory implements ExBannerLayoutProvider {

        private final int imageRes;
        private final int videoRes;

        public Factory(int imageRes, int videoRes) {
            this.imageRes = imageRes;
            this.videoRes = videoRes;
        }

        @Override
        public int getImageLayoutRes() {
            return imageRes;
        }

        @Override
        public int getVideoLayoutRes() {
            return videoRes;
        }
    }

}
