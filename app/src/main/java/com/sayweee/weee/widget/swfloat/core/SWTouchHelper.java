package com.sayweee.weee.widget.swfloat.core;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.graphics.Rect;
import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.core.math.MathUtils;

import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.swfloat.data.SWFloatConfig;

class SWTouchHelper {

    private final SWFloatConfig config;
    private final WindowManager windowManager;
    private final DisplayMetrics dm = new DisplayMetrics();

    private int leftBorder = 0;
    private int topBorder = 0;
    private int rightBorder = 0;
    private int bottomBorder = 0;
    private float lastX = 0f;
    private float lastY = 0f;

    public SWTouchHelper(WindowManager windowManager, SWFloatConfig config) {
        this.windowManager = windowManager;
        this.config = config;
    }

    public void onTouch(View parentView, MotionEvent event) {
        WindowManager.LayoutParams params = (WindowManager.LayoutParams) parentView.getLayoutParams();

        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                config.setDragging(false);
                lastX = event.getRawX();
                lastY = event.getRawY();
                initFields(parentView);
                break;
            case MotionEvent.ACTION_MOVE:
                float dx = event.getRawX() - lastX;
                float dy = event.getRawY() - lastY;
                if (!config.isDragging() && dx * dx + dy * dy < 81) {
                    return;
                }
                config.setDragging(true);

                int newX = params.x + (int) dx;
                int newY = params.y + (int) dy;

                params.x = MathUtils.clamp(newX, leftBorder, rightBorder);
                params.y = MathUtils.clamp(newY, topBorder, bottomBorder);

                windowManager.updateViewLayout(parentView, params);
                lastX = event.getRawX();
                lastY = event.getRawY();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (config.isDragging()) {
                    animToSide(parentView, params);
                }
                break;
            default:
                break;
        }
    }

    private void initFields(View view) {
        // Always get the latest display metrics
        Rect bounds = CommonTools.getWindowRect(windowManager);
        int parentWidth = bounds.width();
        int parentHeight = bounds.height();
        leftBorder = 0;
        rightBorder = parentWidth - view.getWidth();
        topBorder = CommonTools.getStatusBarHeight(view.getContext());
        bottomBorder = parentHeight - view.getHeight();
    }

    private void animToSide(View parentView, WindowManager.LayoutParams params) {
        int currentX = params.x;
        int endX = (currentX < (rightBorder - leftBorder) / 2) ? leftBorder : rightBorder;

        ValueAnimator animator = ValueAnimator.ofInt(currentX, endX);
        animator.addUpdateListener(animation -> {
            params.x = (int) animation.getAnimatedValue();
            try {
                windowManager.updateViewLayout(parentView, params);
            } catch (Exception ignored) {
                animator.cancel();
            }
        });
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {
                config.setAnim(true);
            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                onDragEnd();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {
                onDragEnd();
            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {
                // No implementation needed
            }
        });
        animator.start();
    }

    private void onDragEnd() {
        config.setAnim(false);
    }
}
