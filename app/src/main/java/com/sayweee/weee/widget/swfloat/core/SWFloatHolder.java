package com.sayweee.weee.widget.swfloat.core;

import android.app.Activity;
import android.content.Context;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.os.IBinder;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.swfloat.data.SWFloatConfig;

import java.lang.ref.WeakReference;

public class SWFloatHolder implements LifecycleEventObserver {

    private final WeakReference<LifecycleOwner> weakLifecycleOwner;
    private final WindowManager windowManager;
    private final SWFloatConfig config;

    private SWTouchHelper touchHelper;
    private final DisplayMetrics dm = new DisplayMetrics();

    private View parentView;

    SWFloatHolder(Activity activity, SWFloatConfig config) {
        this.windowManager = (WindowManager) activity.getSystemService(Context.WINDOW_SERVICE);
        this.config = config;
        if (activity instanceof AppCompatActivity) {
            weakLifecycleOwner = new WeakReference<>((AppCompatActivity) activity);
            ((AppCompatActivity) activity).getLifecycle().addObserver(this);
        } else {
            weakLifecycleOwner = null;
        }
    }

    void create(Activity activity, OnFloatHolderCreatedListener listener) {
        if (getWindowToken(activity) == null) {
            delayCreate(activity, listener);
        } else {
            listener.onFloatHolderCreated(this, createWindowInternal(activity));
        }
    }

    private void delayCreate(Activity activity, OnFloatHolderCreatedListener listener) {
        View contentView = activity.findViewById(android.R.id.content);
        if (contentView != null) {
            contentView.post(() -> {
                listener.onFloatHolderCreated(this, createWindowInternal(activity));
            });
        } else {
            listener.onFloatHolderCreated(this, false);
        }
    }

    private boolean createWindowInternal(Activity activity) {
        try {
            touchHelper = new SWTouchHelper(windowManager, config);
            WindowManager.LayoutParams layoutParams = getWindowLayoutParams(activity);
            addFloatView(layoutParams);
            config.setShowing(true);
            return true;
        } catch (Exception ex) {
            config.setShowing(false);
        }
        return false;
    }

    private WindowManager.LayoutParams getWindowLayoutParams(Activity activity) {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_PANEL;
        layoutParams.token = getWindowToken(activity);
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        return layoutParams;
    }

    private void addFloatView(WindowManager.LayoutParams params) {
        View floatView = config.getFloatView();
        if (floatView == null) {
            return;
        }

        Context context = floatView.getContext();
        SWFloatParentView parentView = new SWFloatParentView(context, config);
        parentView.setTag(config.getFloatViewTag());
        parentView.setOnFloatParentTouchListener(event -> {
            touchHelper.onTouch(parentView, event);
        });
        parentView.setOnFloatParentLayoutListener(() -> {
            layoutParentView(parentView, params);
            parentView.setVisibility(View.VISIBLE);
            if (config.getOnViewCreatedListener() != null) {
                config.getOnViewCreatedListener().onFloatViewCreated(floatView);
            }
        });
        parentView.setVisibility(View.INVISIBLE);
        parentView.addView(floatView);

        this.parentView = parentView;
        windowManager.addView(parentView, params);
    }

    private void layoutParentView(View parentView, WindowManager.LayoutParams params) {
        Rect bounds = CommonTools.getWindowRect(windowManager);
        int parentWidth = bounds.width();
        int parentHeight = bounds.height();
        params.x = parentWidth - parentView.getWidth();
        params.y = parentHeight - parentView.getHeight();

        Rect initialInsets = config.getInitialInsets();
        if (initialInsets != null) {
            params.x -= initialInsets.right;
            params.y -= initialInsets.bottom;
        }
        windowManager.updateViewLayout(parentView, params);
    }

    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            dismissInternal(lifecycleOwner, true);
        }
    }

    public void dismiss() {
        dismissInternal(null, false);
    }

    private void dismissInternal(LifecycleOwner lifecycleOwner, boolean isSelfDismiss) {

        LifecycleOwner owner = lifecycleOwner;
        if (owner == null && weakLifecycleOwner != null) {
            owner = weakLifecycleOwner.get();
        }
        if (owner != null) {
            owner.getLifecycle().removeObserver(this);
        }

        if (parentView != null) {
            windowManager.removeView(parentView);
            parentView = null;
        }
        config.setShowing(false);

        if (isSelfDismiss) {
            SWFloatManager.get().dismiss(config.getFloatViewTag());
        }
    }

    private IBinder getWindowToken(Activity activity) {
        Window window = activity.getWindow();
        if (window != null) {
            return window.getDecorView().getWindowToken();
        }
        return null;
    }

    interface OnFloatHolderCreatedListener {
        void onFloatHolderCreated(SWFloatHolder holder, boolean isSuccess);
    }

}
