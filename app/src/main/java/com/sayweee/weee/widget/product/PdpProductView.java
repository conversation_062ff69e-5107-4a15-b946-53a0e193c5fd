package com.sayweee.weee.widget.product;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;

public class PdpProductView extends ProductView {

    public PdpProductView(Context context) {
        super(context);
    }

    public PdpProductView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PdpProductView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void applyProductName(ProductBean bean) {
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        tvProductName.setMaxLines(LanguageManager.get().isCJK() ? 2 : 3);
        LabelHelper.setTitleLabel(tvProductName, bean, 1f, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (displayStyle == STYLE_ITEM_SEARCH_V2) {
                    helper.getView(R.id.layout_product).callOnClick();
                } else {
                    onProductClick(v.getContext(), bean);
                }
            }
        });

    }

    @Override
    protected void applyProductCollect(ProductBean bean) {
        View ivCollect = helper.getView(R.id.iv_collect);
        ProductViewHelper.setProductCollect(ivCollect, CollectManager.get().isProductCollect(bean.id));
        ViewTools.setViewOnSafeClickListener(ivCollect, v -> {
            onCollectClick(v, bean);
            if (collectClickCallback != null) {
                collectClickCallback.onCollectClick();
            }
        });
        ViewTools.setViewVisibilityIfChanged(ivCollect, true);
    }

}
