package com.sayweee.weee.widget.product;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ImageSpan;
import android.text.style.StrikethroughSpan;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ShotUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.span.RoundBackgroundSpan;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.CartStatusLayout;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.util.Map;


public class PostProductView extends ProductView {

    public PostProductView(Context context) {
        super(context);
    }

    public PostProductView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PostProductView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void init(Context context, @Nullable AttributeSet attrs) {
        int style = 0;
        if (attrs != null) {
            TypedArray ta = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CartOpLayout, 0, 0);
            style = ta.getInt(R.styleable.CartOpLayout_op_style, 0);
            ta.recycle();
        }
        int layoutRes;
        if (style == CartOpLayout.STYLE_SMALL) {
            layoutRes = R.layout.view_product_post;
        } else {
            layoutRes = R.layout.view_product_post_white;
        }
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
    }

    @Override
    protected PostProductView setAttachedProduct(ProductBean bean, int style, String source, OnOpCallback callback, Map<String, Object> element, Map<String, Object> ctx) {
        beforeAttachProduct(bean, style);
        ProductViewHelper.prependProductTagBuyAgain(bean);

        //是否显示受限
        Context context = getContext();
        helper.setVisible(R.id.iv_similar, bean.isSimilar());
        //image
        String imageUrl = bean.getHeadImageUrl();
        ImageLoader.load(context, helper.getView(R.id.iv_icon), WebpManager.get().getConvertUrl(SPEC_PRODUCT, imageUrl), R.mipmap.iv_product_placeholder);
        helper.setText(R.id.tv_product_name, bean.name);
        //price
        helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(bean.price))
                .setText(R.id.tv_price_delete, new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()))
                .setVisible(R.id.tv_price_delete, bean.base_price > 0);
        //status & product op
        CartOpLayout layoutOp = helper.getView(R.id.layout_op);
        Spanny spanny = new Spanny();
        //mark
        if (isPreSell(bean.sold_status)) {
            spanny.append(context.getString(R.string.s_pre_sell));
            spanny.setSpan(new RoundBackgroundSpan(Color.parseColor("#820B6A"),
                            CommonTools.dp2px(4), Color.parseColor("#ffffff"),
                            CommonTools.dp2px(2), CommonTools.dp2px(1),
                            CommonTools.dp2px(2), CommonTools.dp2px(1), 0, CommonTools.dp2px(1)),
                    0, context.getString(R.string.s_pre_sell).length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            spanny.setSpan(new AbsoluteSizeSpan(CommonTools.sp2px(10)), 0, context.getString(R.string.s_pre_sell).length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            spanny.append(" ");

        } else {
            if (bean.label_list != null && bean.label_list.size() > 0) {
                ProductBean.LabelListBean label = bean.label_list.get(0);
                try {
                    spanny.append(" ", getLabelSpan(label.label_name, Color.parseColor(label.label_color),
                            label.label_font_color));
                } catch (Exception ignored) { /**/ }
            }
        }
        if (bean.is_pantry && isShowPantry) {
            Drawable drawable = PantryHelper.getProductPantryDrawable(getContext());
            spanny.append("", new CenterImageSpan(drawable)).append(" ");
        }
        spanny.append(bean.name);
        helper.setText(R.id.tv_product_name, spanny);

        View soldOutView = helper.getView(R.id.tv_sold_out);
        layoutOp.setRightAddStyle();
        if (soldOutView != null) {
            soldOutView.setVisibility(GONE);
        }
        CartStatusLayout layoutStatus = helper.getView(R.id.layout_status);

        boolean showSoldStatus;
        if ((bean.isBundle() || bean.isHotDish())) {
            // no cart status layout
            showSoldStatus = true;
            int titleResId = R.string.s_view;
            ProductViewHelper.setProductStatus(layoutStatus, titleResId, 0, R.color.color_atc_mini_fg_default);
        } else if (OrderManager.get().isReachLimit(bean)) {
            // no cart status layout
            showSoldStatus = true;
            ProductViewHelper.setProductStatusPurchased(layoutStatus);
        } else if (isChangeOtherDay(bean.sold_status)) {
            // no cart status layout
            showSoldStatus = true;
            ProductViewHelper.setProductStatusChangeDate(layoutStatus);
        } else if (isSoldOut(bean.sold_status)) {
            // no cart status layout
            showSoldStatus = true;
            displaySoldStatus(bean, STYLE_ITEM_NORMAL);
        } else if (isPreSell(bean.sold_status)) {
            showSoldStatus = false;
            layoutOp.setDisableStyle();
        } else {
            showSoldStatus = false;
            if (callback != null) {
                callback.onOp(layoutOp, bean);
            } else {
                OpHelper.helperOp(layoutOp, bean, bean, source, element);
            }
        }
        ViewTools.setViewVisible(layoutStatus, showSoldStatus);
        helper.setVisible(R.id.layout_op, !showSoldStatus);

        helper.setOnClickListener(R.id.layout_product, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (pdpClickCallback != null) {
                    pdpClickCallback.onPdpClick();
                } else {
                    onProductClick(context, bean);
                }
            }
        });

        if (layoutStatus != null) {
            helper.setOnClickListener(R.id.layout_status, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    //12.9 已售罄状态不能取消提醒
                    if (CollectManager.get().isProductCollect(bean.getProductId()) && isSoldOut(bean.sold_status)) {
                        if (style == STYLE_ITEM_SEARCH_V2) {
                            toWebPage(Constants.Url.COLLECT);
                        }
                        return;
                    }
                    onStatusClick(v, bean, style);
                }
            });
        }
        return this;
    }

    private ImageSpan getLabelSpan(String tag, int tagColor, String fontColor) {
        View badge = View.inflate(getContext(), R.layout.layout_text_badge, null);
        TextView child = badge.findViewById(R.id.tv_badge);
        child.setText(tag);
        child.setTextSize(9.26f);
        child.setGravity(Gravity.CENTER);
        if (!EmptyUtils.isEmpty(fontColor)) {
            child.setTextColor(Color.parseColor(fontColor));
        } else {
            child.setTextColor(ContextCompat.getColor(getContext(), R.color.color_pricing_surface_1_fg_default_idle));
        }
        child.setIncludeFontPadding(false);
        ViewTools.applyTextStyle(child, R.style.style_fluid_root_badge_label_sm);
        int dp6 = CommonTools.dp2px(7);
        int dp2 = CommonTools.dp2px(1.5f);
        child.setPadding(dp6, dp2, dp6, dp2);
        child.setBackground(ShapeHelper.buildSolidDrawable(tagColor, CommonTools.dp2px(10)));
        Bitmap bitmap = ShotUtils.shotView(badge);
        BitmapDrawable drawable = new BitmapDrawable(getContext().getResources(), bitmap);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        return new CenterImageSpan(drawable);
    }

    public void setNameTextColor(@ColorRes int color) {
        ViewTools.applyTextColor(findViewById(R.id.tv_product_name), color);
    }

    public void setPriceTextColor(@ColorRes int color) {
        ViewTools.applyTextColor(findViewById(R.id.tv_price), color);
    }

    public void setPriceTextSize(float fontSize) {
        ViewTools.applyTextSizeSp(findViewById(R.id.tv_price), fontSize);
    }

    /**
     * inspiration not show pantry
     */
    public boolean isShowPantry = true;

}
