package com.sayweee.weee.widget.swfloat.core;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.widget.swfloat.data.SWFloatConfig;

public class SWFloatParentView extends FrameLayout {

    private OnLayoutListener onFloatParentLayoutListener;
    private OnTouchListener onFloatParentTouchListener;

    private boolean isLaid = false;
    private SWFloatConfig config;

    public SWFloatParentView(@NonNull Context context) {
        this(context, null, 0);
    }

    public SWFloatParentView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SWFloatParentView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, null);
    }

    public SWFloatParentView(@NonNull Context context, SWFloatConfig config) {
        this(context, null, 0);
        init(context, config);
    }

    protected void init(Context context, SWFloatConfig config) {
        if (config == null) {
            this.config = new SWFloatConfig();
        } else {
            this.config = config;
        }
        setClipToPadding(false);
        setClipChildren(false);
    }

    public void setOnFloatParentLayoutListener(OnLayoutListener onFloatParentLayoutListener) {
        this.onFloatParentLayoutListener = onFloatParentLayoutListener;
    }

    public void setOnFloatParentTouchListener(OnTouchListener onFloatParentTouchListener) {
        this.onFloatParentTouchListener = onFloatParentTouchListener;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (!isLaid) {
            isLaid = true;
            if (onFloatParentLayoutListener != null) {
                onFloatParentLayoutListener.onLayout();
            }
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (event != null && onFloatParentTouchListener != null) {
            onFloatParentTouchListener.onTouch(event);
        }
        if (config.isDragging()) {
            return true;
        }
        return super.onInterceptTouchEvent(event);
    }

    @SuppressWarnings("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event != null && onFloatParentTouchListener != null) {
            onFloatParentTouchListener.onTouch(event);
        }
        if (config.isDragging()) {
            return true;
        }
        return super.onTouchEvent(event);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (config.getOnViewDismissListener() != null) {
            config.getOnViewDismissListener().onFloatViewDismiss();
        }
    }

    public interface OnLayoutListener {
        void onLayout();
    }

    public interface OnTouchListener {
        void onTouch(MotionEvent event);
    }
}
