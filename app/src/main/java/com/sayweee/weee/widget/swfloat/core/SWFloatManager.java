package com.sayweee.weee.widget.swfloat.core;

import android.app.Activity;

import com.sayweee.weee.widget.swfloat.data.SWFloatConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SWFloatManager {

    private static final class Builder {
        private static final SWFloatManager INSTANCE = new SWFloatManager();
    }

    public static SWFloatManager get() {
        return Builder.INSTANCE;
    }

    private final Map<String, SWFloatHolder> holderMap;

    private SWFloatManager() {
        holderMap = new ConcurrentHashMap<>();
    }

    public void createHolder(Activity activity, SWFloatConfig config) {
        SWFloatHolder holder = new SWFloatHolder(activity, config);
        holder.create(activity, (h, isSuccess) -> {
            if (isSuccess) {
                holderMap.put(config.getFloatViewTag(), h);
            }
        });
    }

    public void dismiss(String tag) {
        SWFloatHolder holder = holderMap.get(tag);
        if (holder != null) {
            holderMap.remove(tag);
            holder.dismiss();
        }
    }

    public void dismissAll(String tagPrefix) {
        for (String tag : holderMap.keySet()) {
            if (tagPrefix != null && tag.startsWith(tagPrefix)) {
                dismiss(tag);
            } else {
                dismiss(tag);
            }
        }
    }

    public boolean isExists(String tag) {
        return holderMap.containsKey(tag);
    }

}
