package com.sayweee.weee.widget.swfloat;

import android.app.Activity;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;

import com.sayweee.weee.widget.swfloat.core.SWFloatManager;
import com.sayweee.weee.widget.swfloat.data.SWFloatConfig;

public class SWFloat {

    public static Builder with(Activity activity) {
        return new Builder(activity);
    }

    public static boolean isExists(@NonNull String tag) {
        return SWFloatManager.get().isExists(tag);
    }

    public static void dismiss(@NonNull String tag) {
        SWFloatManager.get().dismiss(tag);
    }

    public static void dismissAll(@NonNull String tagPrefix) {
        SWFloatManager.get().dismissAll(tagPrefix);
    }

    public static class Builder {

        private final Activity activity;
        private final SWFloatConfig config = new SWFloatConfig();

        private Builder(Activity activity) {
            this.activity = activity;
        }

        public Builder setView(@NonNull View floatView) {
            config.setFloatView(floatView);
            return this;
        }

        public Builder setTag(@NonNull String tag) {
            config.setFloatViewTag(tag);
            return this;
        }

        public Builder setInitialInsets(Rect insets) {
            config.setInitialInsets(insets);
            return this;
        }

        public void show() {
            SWFloatManager.get().createHolder(activity, config);
        }

    }

}
