package com.sayweee.weee.widget.swfloat.data;

import android.graphics.Rect;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

public class SWFloatConfig {

    private int gravity = Gravity.END | Gravity.BOTTOM; // Default gravity
    private View floatView;
    private String floatViewTag;
    private Rect initialInsets;
    private OnViewCreatedListener onViewCreatedListener;
    private OnViewDismissListener onViewDismissListener;

    private boolean isShowing = false;
    private boolean isDragging = false;
    private boolean isAnim = false;


    public int getGravity() {
        return gravity;
    }

    public void setGravity(int gravity) {
        // TODO: Support other gravity options in the future
        // this.gravity = gravity;
    }

    public View getFloatView() {
        return floatView;
    }

    public void setFloatView(View floatView) {
        this.floatView = floatView;
    }

    public String getFloatViewTag() {
        return floatViewTag;
    }

    public void setFloatViewTag(String floatViewTag) {
        this.floatViewTag = floatViewTag;
    }

    public Rect getInitialInsets() {
        return initialInsets;
    }

    public void setInitialInsets(Rect initialInsets) {
        this.initialInsets = initialInsets;
    }

    public OnViewCreatedListener getOnViewCreatedListener() {
        return onViewCreatedListener;
    }

    public void setOnViewCreatedListener(OnViewCreatedListener onViewCreatedListener) {
        this.onViewCreatedListener = onViewCreatedListener;
    }

    public OnViewDismissListener getOnViewDismissListener() {
        return onViewDismissListener;
    }

    public void setOnViewDismissListener(OnViewDismissListener onViewDismissListener) {
        this.onViewDismissListener = onViewDismissListener;
    }

    public boolean isShowing() {
        return isShowing;
    }

    public void setShowing(boolean showing) {
        isShowing = showing;
    }

    public boolean isDragging() {
        return isDragging;
    }

    public void setDragging(boolean dragging) {
        isDragging = dragging;
    }

    public boolean isAnim() {
        return isAnim;
    }

    public void setAnim(boolean anim) {
        isAnim = anim;
    }


    public interface OnViewCreatedListener {
        void onFloatViewCreated(@NonNull View view);
    }

    public interface OnViewDismissListener {
        void onFloatViewDismiss();
    }
}
