package com.sayweee.weee.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintHelper;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.sayweee.weee.R;

public class ImageShadowHelper extends ConstraintHelper {

    private int shadowOffsetX;
    private int shadowOffsetY;
    private int shadowBlur;
    private int shadowDelta;
    private Drawable shadowDrawable;
    private boolean shadowVisible = true;

    private ConstraintLayout mContainer;
    private View mAnchorView;
    private View mShadowView;

    public ImageShadowHelper(Context context) {
        this(context, null, 0);
    }

    public ImageShadowHelper(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ImageShadowHelper(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @SuppressWarnings("resource")
    @Override
    protected void init(AttributeSet attrs) {
        super.init(attrs);
        // handle click events
        mUseViewMeasure = true;

        TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.ImageShadowHelper);
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_offset_x)) {
            shadowOffsetX = a.getDimensionPixelOffset(R.styleable.ImageShadowHelper_ish_shadow_offset_x, shadowOffsetX);
        }
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_offset_y)) {
            shadowOffsetY = a.getDimensionPixelOffset(R.styleable.ImageShadowHelper_ish_shadow_offset_y, shadowOffsetY);
        }
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_blur)) {
            shadowBlur = a.getDimensionPixelOffset(R.styleable.ImageShadowHelper_ish_shadow_blur, shadowBlur);
        }
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_delta)) {
            shadowDelta = a.getDimensionPixelOffset(R.styleable.ImageShadowHelper_ish_shadow_delta, shadowDelta);
        }
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_drawable)) {
            shadowDrawable = a.getDrawable(R.styleable.ImageShadowHelper_ish_shadow_drawable);
        }
        if (a.hasValue(R.styleable.ImageShadowHelper_ish_shadow_visible)) {
            shadowVisible = a.getBoolean(R.styleable.ImageShadowHelper_ish_shadow_visible, shadowVisible);
        }
        a.recycle();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mContainer = (ConstraintLayout) getParent();
        applyLayoutFeatures();
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        applyLayoutFeatures();
    }

    @Override
    protected void applyLayoutFeaturesInConstraintSet(ConstraintLayout container) {
        applyLayoutFeatures(container);
    }

    @Override
    public void updatePreLayout(ConstraintLayout container) {
        super.updatePreLayout(container);
        if (mShadowView != null) {
            return;
        }

        reCacheView();
        View anchorView = mAnchorView;
        if (anchorView == null) {
            return;
        }
        addShadowToAnchor(container, anchorView);
    }

    private void addShadowToAnchor(ConstraintLayout container, View anchorView) {
        int anchorViewIndex = container.indexOfChild(anchorView);
        int anchorViewId = anchorView.getId();

        int shadowViewId = View.generateViewId();
        View shadowView = new View(getContext());
        mShadowView = shadowView;
        shadowView.setLayoutParams(new ConstraintLayout.LayoutParams(0, 0));
        shadowView.setId(shadowViewId);
        shadowView.setBackground(getShadowDrawable());

        int leftMargin = -(shadowBlur - shadowOffsetX - shadowDelta);
        int topMargin = -(shadowBlur - shadowOffsetY - shadowDelta);
        int rightMargin = -(shadowBlur + shadowOffsetX - shadowDelta);
        int bottomMargin = -(shadowBlur + shadowOffsetY - shadowDelta);

        try {
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(container);
            constraintSet.connect(shadowViewId, ConstraintSet.TOP, anchorViewId, ConstraintSet.TOP, topMargin);
            constraintSet.connect(shadowViewId, ConstraintSet.BOTTOM, anchorViewId, ConstraintSet.BOTTOM, bottomMargin);
            constraintSet.connect(shadowViewId, ConstraintSet.START, anchorViewId, ConstraintSet.START, leftMargin);
            constraintSet.connect(shadowViewId, ConstraintSet.END, anchorViewId, ConstraintSet.END, rightMargin);
            container.addView(shadowView, anchorViewIndex);
            constraintSet.applyTo(container);
        } catch (Exception ex) {
            // no op
            // RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet")
        }
    }

    private void reCacheView() {
        if (mContainer == null) {
            return;
        }
        if (mCount == 0) {
            return;
        }
        if (mAnchorView == null) {
            mAnchorView = mContainer.getViewById(mIds[0]);
        }
    }

    @Override
    protected void applyLayoutFeatures(ConstraintLayout container) {
        super.applyLayoutFeatures(container);
        View shadowView = mShadowView;
        if (shadowView != null) {
            int visibility = getVisibility();
            shadowView.setVisibility(visibility);
        }
    }

    public void updateShadowDrawable(@Nullable Drawable shadowDrawable) {
        this.shadowDrawable = shadowDrawable;
        if (mShadowView != null) {
            mShadowView.setBackground(getShadowDrawable());
        }
    }

    @Nullable
    private Drawable getShadowDrawable() {
        return this.shadowVisible ? this.shadowDrawable : null;
    }

    public void setShadowVisible(boolean isVisible) {
        this.shadowVisible = isVisible;
        if (mShadowView != null) {
            mShadowView.setBackground(isVisible ? shadowDrawable : null);
        }
    }
}
