package com.sayweee.weee.widget.recycler;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

public final class RecyclerViewTools {

    public static String printScrollState(int state) {
        switch (state) {
            case RecyclerView.SCROLL_STATE_IDLE:
                return "SCROLL_STATE_IDLE";
            case RecyclerView.SCROLL_STATE_DRAGGING:
                return "SCROLL_STATE_DRAGGING";
            case RecyclerView.SCROLL_STATE_SETTLING:
                return "SCROLL_STATE_SETTLING";
            default:
                return "SCROLL_STATE_UNKNOWN";
        }
    }

    public static boolean isTopMost(@NonNull RecyclerView recyclerView) {
        return !recyclerView.canScrollVertically(-1);
    }

    public static void smoothScrollTo(@Nullable RecyclerView recyclerView, int position) {
        if (recyclerView == null) {
            return;
        }
        recyclerView.post(() -> {
            RecyclerView.LayoutManager lm = recyclerView.getLayoutManager();
            if (lm != null) {
                RecyclerView.SmoothScroller scroller = new LinearSmoothScroller(recyclerView.getContext());
                scroller.setTargetPosition(position);
                lm.startSmoothScroll(scroller);
            }
        });
    }

    @Nullable
    public static Pair<Integer, Integer> findVisibleItemPositionRange(@Nullable RecyclerView recyclerView, boolean isCompleteVisible) {
        RecyclerView.LayoutManager lm = recyclerView != null ? recyclerView.getLayoutManager() : null;
        if (lm == null) {
            return null;
        }
        Pair<Integer, Integer> range = null;
        if (lm instanceof LinearLayoutManager) {
            range = findVisibleItemPositionRangeInLinearLayoutManager((LinearLayoutManager) lm, isCompleteVisible);
        } else if (lm instanceof StaggeredGridLayoutManager) {
            range = findVisibleItemPositionRangeInStaggeredGridLayoutManager((StaggeredGridLayoutManager) lm, isCompleteVisible);
        }
        return range;
    }

    @Nullable
    private static Pair<Integer, Integer> findVisibleItemPositionRangeInLinearLayoutManager(
            @NonNull LinearLayoutManager layoutManager,
            boolean isCompleteVisible
    ) {
        int first;
        int last;
        if (!isCompleteVisible) {
            first = layoutManager.findFirstVisibleItemPosition();
            last = layoutManager.findLastVisibleItemPosition();
        } else {
            first = layoutManager.findFirstCompletelyVisibleItemPosition();
            last = layoutManager.findLastCompletelyVisibleItemPosition();
        }
        if (first != RecyclerView.NO_POSITION && last != RecyclerView.NO_POSITION) {
            return Pair.create(first, last);
        }
        return null;
    }

    @Nullable
    private static Pair<Integer, Integer> findVisibleItemPositionRangeInStaggeredGridLayoutManager(
            @NonNull StaggeredGridLayoutManager layoutManager,
            boolean isCompleteVisible
    ) {
        int spanCount = layoutManager.getSpanCount();
        int[] positions = new int[spanCount];
        int first;
        if (!isCompleteVisible) {
            layoutManager.findFirstVisibleItemPositions(positions);
        } else {
            layoutManager.findFirstCompletelyVisibleItemPositions(positions);
        }
        first = findMinimumPosition(positions);
        if (first == RecyclerView.NO_POSITION) {
            return null;
        }

        int last;
        if (!isCompleteVisible) {
            layoutManager.findLastVisibleItemPositions(positions);
        } else {
            layoutManager.findLastCompletelyVisibleItemPositions(positions);
        }
        last = findMaximumPosition(positions);
        if (last == RecyclerView.NO_POSITION) {
            return null;
        }

        return Pair.create(first, last);
    }

    public static int findMinimumPosition(int[] positions) {
        int length = positions.length;
        if (length == 0) {
            return RecyclerView.NO_POSITION;
        } else if (length == 1) {
            return positions[0];
        } else if (length == 2) {
            return Math.min(positions[0], positions[1]);
        } else {
            int min = RecyclerView.NO_POSITION;
            for (int pos : positions) {
                min = Math.min(min, pos);
            }
            return min;
        }
    }

    public static int findMaximumPosition(int[] positions) {
        int length = positions.length;
        if (length == 0) {
            return RecyclerView.NO_POSITION;
        } else if (length == 1) {
            return positions[0];
        } else if (length == 2) {
            return Math.max(positions[0], positions[1]);
        } else {
            int max = RecyclerView.NO_POSITION;
            for (int pos : positions) {
                max = Math.max(max, pos);
            }
            return max;
        }
    }

    private RecyclerViewTools() {

    }
}
