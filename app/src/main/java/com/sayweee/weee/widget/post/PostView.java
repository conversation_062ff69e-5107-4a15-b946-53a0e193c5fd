package com.sayweee.weee.widget.post;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.edit.bean.AdapterUploadData;
import com.sayweee.weee.module.post.edit.service.PostUploadManager;
import com.sayweee.weee.module.post.edit.service.bean.PostDraftData;
import com.sayweee.weee.module.post.edit.service.bean.PostUploadData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.CircularProgressView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Desc:社区卡片
 */
public class PostView extends LinearLayout {

    public static final int STYLE_CARD = 1;
    public static final int STYLE_CARD_DRAFT = 2;
    public static final int STYLE_CARD_UPLOADING = 3;

    @IntDef({STYLE_CARD, STYLE_CARD_DRAFT, STYLE_CARD_UPLOADING})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DisplayStyle {

    }

    protected ViewHelper helper;

    public PostView(Context context) {
        this(context, null);
    }

    public PostView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PostView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    protected void init(Context context, @Nullable AttributeSet attrs) {
        int style = STYLE_CARD;
        if (attrs != null) {
            TypedArray ta = context.getTheme().obtainStyledAttributes(attrs, R.styleable.PostView, 0, 0);
            style = ta.getInt(R.styleable.PostView_post_display_style, STYLE_CARD);
            ta.recycle();
        }
        int layoutRes;
        layoutRes = R.layout.view_post_card;
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
    }

    public PostView setAttachedPost(PostBean bean, int id) {
        return setAttachedPost(bean, id, STYLE_CARD);
    }

    private PostView setAttachedPost(PostBean bean, int justViewedId, @DisplayStyle int style) {
        Context context = getContext();
        FrameLayout layoutIcon = helper.getView(R.id.layout_icon);
        if (layoutIcon != null) {
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) layoutIcon.getLayoutParams();
            if (params != null) {
                params.dimensionRatio = String.valueOf(DecimalTools.divide(1, bean.show_rate <= 0 ? 1 : bean.show_rate));
                layoutIcon.setLayoutParams(params);
            }
            layoutIcon.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                @Override
                public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                    layoutIcon.removeOnLayoutChangeListener(this);
                    ImageView ivIcon = helper.getView(R.id.iv_icon);
                    if (ivIcon != null) {
                        ImageLoader.load(context, ivIcon,
                                WebpManager.convert(ImageSpec.SPEC_POST_AUTO,
                                        EmptyUtils.isEmpty(bean.pictures) ? bean.product_image_url : bean.pictures.get(0)),
                                context.getResources().getDrawable(R.mipmap.pic_explore_empty));
                    }
                }
            });
        }
        helper.setVisible(R.id.tv_just_viewed, bean.id == justViewedId);
        //post video 标记
        helper.setVisible(R.id.iv_video, bean.isVideo());
        //post video pin 标记
        helper.setVisible(R.id.tv_pin_video_mark, bean.isPinned());
        helper.setVisible(R.id.tv_view_in_review, bean.isInReview());
        //view count style A
        helper.setVisible(R.id.tv_view_count_mark, bean.isViewCountStyleA());
        helper.setText(R.id.tv_view_count_mark, bean.view_count_label);
        //view count style B
        if (bean.isViewCountStyleB()) {
            if (!EmptyUtils.isEmpty(bean.view_count_label)) {
                helper.setViewVisible(R.id.tv_view_count);
            } else {
                helper.getView(R.id.tv_view_count).setVisibility(INVISIBLE);
            }
        } else {
            helper.setViewGone(R.id.tv_view_count);
        }
        helper.setVisible(R.id.cl_insights, bean.isViewCountStyleB());
        helper.setVisible(R.id.dash_line, bean.isViewCountStyleB());
        helper.setVisible(R.id.tv_view_insights, VariantConfig.IS_VIEW_VISIBLE);
        helper.setText(R.id.tv_view_count, context.getString(R.string.s_views, bean.view_count_label));
        if (VariantConfig.IS_VIEW_VISIBLE) {
            helper.setOnClickListener(R.id.cl_insights, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    toWebPage(bean.insights_url);
                }
            });
        }

        helper.setOnClickListener(R.id.tv_view_in_review, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                SharedViewModel.get().openInReviewDialog();
            }
        });
        ImageLoader.load(context, helper.getView(R.id.iv_icon),
                WebpManager.convert(ImageSpec.SPEC_POST_AUTO,
                        EmptyUtils.isEmpty(bean.pictures) ? bean.product_image_url : bean.pictures.get(0)),
                ContextCompat.getDrawable(context, R.mipmap.pic_explore_empty));
        //标题
        Spanny builder = new Spanny();
        if (bean.featured == PostBean.VIDEO_FEATURED) {
            Drawable drawable = ContextCompat.getDrawable(context, R.mipmap.post_star);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(18), CommonTools.dp2px(18));
            }
            builder.append("", new CenterImageSpan(drawable)).append(" ");
        }
        if (bean.getTitle() != null) {
            builder.append(bean.getTitle());
        }
        helper.setText(R.id.tv_product_name, builder);
        //用户信息
        ImageLoader.load(context, helper.getView(R.id.iv_user),
                WebpManager.get().getConvertUrl(SPEC_AVATAR, bean.user_avatar), R.mipmap.post_user_placeholder);
        helper.setText(R.id.tv_user, bean.user_name);
        //收藏信息
        helper.setVisible(R.id.tv_collection_qty, bean.like_count > 0);
        helper.setText(R.id.tv_collection_qty, PostCollectManager.get().getLikeCountLabel(bean.like_count));
        helper.setImageResource(R.id.iv_collect, bean.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
        helper.setOnClickListener(R.id.iv_collect, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                onPraise(bean);
            }
        });

        helper.setVisible(R.id.layout_progress, false);
        helper.setVisible(R.id.layout_retry, false);
        helper.setViewGone(R.id.tv_draft);
        helper.setViewGone(R.id.iv_delete);
        return this;
    }

    //草稿
    public PostView setAttachedPostDraft(PostDraftData bean) {
        helper.setViewVisible(R.id.iv_video);
        helper.setViewGone(R.id.tv_pin_video_mark);
        helper.setViewGone(R.id.tv_view_count_mark);
        helper.setViewGone(R.id.tv_view_count);
        helper.setViewGone(R.id.cl_insights);
        helper.setViewGone(R.id.dash_line);
        helper.setViewGone(R.id.iv_user);
        helper.setViewGone(R.id.tv_user);
        helper.setViewGone(R.id.tv_collection_qty);
        helper.setViewGone(R.id.iv_collect);
        helper.setViewGone(R.id.layout_progress);
        helper.setViewGone(R.id.layout_retry);

        Context context = getContext();
        float rate = 1;
        FrameLayout layoutIcon = helper.getView(R.id.layout_icon);
        if (layoutIcon != null) {
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) layoutIcon.getLayoutParams();
            if (params != null) {
                params.dimensionRatio = String.valueOf(DecimalTools.divide(1, rate));
                layoutIcon.setLayoutParams(params);
            }
        }
        ImageView ivIcon = helper.getView(R.id.iv_icon);
        if (bean.image != null) {
            ImageLoader.load(context, ivIcon, WebpManager.convert(ImageSpec.Size.SIZE_200, ImageSpec.Size.SIZE_AUTO, bean.image));
        } else {
            ivIcon.setImageDrawable(null);
        }
        helper.setViewVisible(R.id.tv_draft);
        helper.setText(R.id.tv_draft, DateUtils.getFormatTime("MM/dd/yyyy", bean.date, null));
        helper.setViewVisible(R.id.iv_delete);
        return this;
    }

    //上传中
    public PostView setAttachedPostUploading(AdapterUploadData item) {
        PostUploadData bean = item.t;
        helper.setViewVisible(R.id.iv_video);
        helper.setViewGone(R.id.tv_view_count_mark);
        helper.setViewGone(R.id.tv_pin_video_mark);
        helper.setViewGone(R.id.tv_collection_qty);
        helper.setViewGone(R.id.layout_progress);
        helper.setViewGone(R.id.layout_retry);
        Context context = getContext();
        ImageLoader.load(context, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.Size.SIZE_200, ImageSpec.Size.SIZE_AUTO, bean.getCoverPath()), R.color.color_place);
        //标题
        helper.setText(R.id.tv_product_name, bean.title);
        //用户信息
        ImageLoader.load(context, helper.getView(R.id.iv_user),
                WebpManager.get().getConvertUrl(SPEC_AVATAR, item.getSelfAvatar()), R.mipmap.post_user_placeholder);
        helper.setText(R.id.tv_user, item.getSelfName());
        helper.setImageResource(R.id.iv_collect, R.mipmap.post_uncollect_new);

        View layoutProgress = helper.getView(R.id.layout_progress);
        TextView tvProgress = helper.getView(R.id.tv_progress);
        CircularProgressView progressView = helper.getView(R.id.cpv_progress);
        layoutProgress.setVisibility(View.VISIBLE);
        layoutProgress.setTag(item.getUploadId());
        helper.setOnClickListener(R.id.tv_view_in_review, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                SharedViewModel.get().openInReviewDialog();
            }
        });


        PostUploadManager.SimpleProgressChangedListener listener = new PostUploadManager.SimpleProgressChangedListener() {
            @SuppressLint("SetTextI18n")
            @Override
            public void onProgressChanged(int progress) {
                tvProgress.setText(progress + "%");
                progressView.setProgress(progress == 99 ? 98 : progress); //让进度显示明显一点
                helper.setViewVisible(R.id.layout_progress);
                helper.setViewGone(R.id.layout_retry);
            }

            @Override
            public void onStatusChanged(int status) {
                bean.setUploadStatus(status);
                item.updateSelf(PostUploadManager.get().getUploadCacheData(item.getUploadId()));
                if (bean.isStatusError(status)) {
                    helper.setViewVisible(R.id.layout_retry);
                    helper.setViewGone(R.id.layout_progress);
                }
            }

            @Override
            public void onSuccess(PostUploadData data) {
                item.updateSelf(data);
                PostBean postBean = item.convertPostBean();
                postBean.type = "video";
                helper.setViewGone(R.id.layout_retry);
                helper.setViewGone(R.id.layout_progress);
                helper.setVisible(R.id.tv_view_in_review, true);
                helper.setOnClickListener(R.id.iv_collect, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        onPraise(postBean);
                    }
                });
                CmtSharedViewModel.get().postPublishedTotal(data, false);
            }
        };
        PostUploadManager.get().setOnProgressChangedListener(item.getUploadId(), listener);
        return this;
    }

    //点赞 取消点赞
    private void onPraise(PostBean bean) {
        if (AccountManager.get().isLogin()) {
            bean.is_set_like = !bean.is_set_like;
            bean.like_count = bean.is_set_like ? bean.like_count + 1 : bean.like_count - 1;
            PostCollectManager.get().toggleCollect(bean.isVideo(), bean.id, bean.is_set_like, bean.like_count, false);
            helper.setVisible(R.id.tv_collection_qty, bean.like_count > 0);
            helper.setText(R.id.tv_collection_qty, PostCollectManager.get().getLikeCountLabel(bean.like_count));
            helper.setImageResource(R.id.iv_collect, bean.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
        } else {
            login();
        }
    }

    private void toWebPage(String url) {
        Context context = getContext();
        if (context != null) {
            context.startActivity(WebViewActivity.getIntent(context, url));
        }
    }

    protected void login() {
        Context context = getContext();
        if (context != null) {
            context.startActivity(AccountIntentCreator.getIntent(context));
        }
    }
}
