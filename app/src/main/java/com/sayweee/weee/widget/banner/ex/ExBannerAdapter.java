package com.sayweee.weee.widget.banner.ex;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_375_AUTO;
import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.BoundViewHolders;
import com.sayweee.weee.module.product.data.ProductBannerItemData;
import com.sayweee.weee.player.bean.MediaBean;
import com.sayweee.weee.player.bean.MediaData;
import com.sayweee.weee.player.mute.MutePlayer;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.youth.banner.adapter.BannerAdapter;
import com.youth.banner.listener.OnBannerListener;
import com.youth.banner.util.BannerUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2022/10/27.
 * Desc:
 */
public class ExBannerAdapter extends BannerAdapter<MediaData, RecyclerView.ViewHolder> {

    public static final int TYPE_IMAGE = 1;
    public static final int TYPE_VIDEO = 2;

    private Context context;
    private boolean preLoad;
    private OnBannerListener<MediaData> listener;

    protected Drawable preloadDrawable;

    private int mImageLayoutRes = R.layout.item_carousel_banner_normal_image;
    private int mVideoLayoutRes = R.layout.item_carousel_banner_normal_video;

    protected final BoundViewHolders<RecyclerView.ViewHolder> boundViewHolders;

    public ExBannerAdapter(List<MediaData> list) {
        this(list, /* preLoad= */false);
    }

    public ExBannerAdapter(List<MediaData> list, boolean preLoad) {
        super(list);
        this.preLoad = preLoad;
        this.boundViewHolders = new BoundViewHolders<>();
    }

    public void setPreloadDrawable(Drawable drawable) {
        this.preloadDrawable = drawable;
    }

    public void updateItems(List<MediaData> newItems, boolean preLoad) {
        this.preLoad = preLoad;
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new ItemDiffCallback(mDatas, newItems));
        mDatas.clear();
        mDatas.addAll(newItems);
        diffResult.dispatchUpdatesTo(this);
    }

    @SuppressWarnings("UnusedReturnValue")
    public ExBannerAdapter setLayoutProvider(@NonNull ExBannerLayoutProvider layout) {
        if (layout.getImageLayoutRes() != 0) {
            this.mImageLayoutRes = layout.getImageLayoutRes();
        }
        if (layout.getVideoLayoutRes() != 0) {
            this.mVideoLayoutRes = layout.getVideoLayoutRes();
        }
        return this;
    }

    protected void beforeCreateHolder(ViewGroup parent, int viewType) {
        this.context = parent.getContext();
    }

    @Override
    public void setOnBannerListener(OnBannerListener<MediaData> listener) {
        super.setOnBannerListener(listener);
        this.listener = listener;
    }

    @Override
    public RecyclerView.ViewHolder onCreateHolder(ViewGroup parent, int viewType) {
        beforeCreateHolder(parent, viewType);
        RecyclerView.ViewHolder vh;
        if (viewType == TYPE_VIDEO) {
            vh = new VideoHolder(BannerUtils.getView(parent, mVideoLayoutRes));
        } else {
            vh = new ImageHolder(BannerUtils.getView(parent, mImageLayoutRes));
        }
        boundViewHolders.add(vh);
        return vh;
    }

    @Override
    public int getItemViewType(int position) {
        MediaData data = getData(getRealPosition(position));
        if (data != null && data.isVideo()) {
            return TYPE_VIDEO;
        }
        return TYPE_IMAGE;
    }

    @Override
    public void onBindView(RecyclerView.ViewHolder holder, MediaData data, int position, int size) {
        if (holder.getItemViewType() == TYPE_VIDEO) {
            MutePlayer videoPlayer = ((VideoHolder) holder).player;
            videoPlayer.setPlayerData(data);
            View v = ((VideoHolder) holder).temp;
            if (v != null) {
                v.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (listener != null) {
                            listener.OnBannerClick(data, position);
                        }
                    }
                });
            }
        } else {
            ImageView imageView = ((ImageHolder) holder).imageView;
            String convertUrl = WebpManager.get().getConvertUrl(SPEC_375_AUTO, data.getImagePath());
            if (position == 0) {
                if (preLoad) {
                    convertUrl = WebpManager.get().getConvertUrl(SPEC_PRODUCT, data.getImagePath());
                    loadCacheImage(context, imageView, convertUrl, position, true);
                } else {
                    loadCacheImage(context, imageView, convertUrl, position, false);
                }
            } else {
                loadImage(context, imageView, convertUrl, R.mipmap.iv_banner_placeholder_375);
            }
        }
    }

    @Override
    public void onViewRecycled(@NonNull RecyclerView.ViewHolder holder) {
        super.onViewRecycled(holder);
        boundViewHolders.remove(holder);
        if (holder.getItemViewType() == TYPE_VIDEO && holder instanceof VideoHolder) {
            destroyVideoHolder((VideoHolder) holder);
        }
    }

    public static class ImageHolder extends RecyclerView.ViewHolder {
        public ImageView imageView;

        public ImageHolder(@NonNull View view) {
            super(view);
            this.imageView = view.findViewById(R.id.iv_image);
        }
    }

    public static class VideoHolder extends RecyclerView.ViewHolder {
        public MutePlayer player;
        public View temp;

        public VideoHolder(@NonNull View view) {
            super(view);
            player = view.findViewById(R.id.player);
            temp = player.findViewById(R.id.v_temp);
        }
    }

    public boolean getPreload() {
        return this.preLoad;
    }

    protected void loadCacheImage(Context context, ImageView view, String url, int position, boolean preload) {
        loadImage(context, view, url, R.mipmap.iv_banner_placeholder_375);
    }

    protected void loadImage(Context context, ImageView view, String url, int placeholder) {
        ImageLoader.load(context, view, url, placeholder);
    }

    public void destroy() {
        for (RecyclerView.ViewHolder vh : boundViewHolders.getBoundViewHolders()) {
            if (vh.getItemViewType() == TYPE_VIDEO && vh instanceof VideoHolder) {
                destroyVideoHolder((VideoHolder) vh);
            }
        }
    }

    protected void destroyVideoHolder(VideoHolder holder) {
        if (holder.player != null) {
            holder.player.onAutoCompletion();
            holder.player.release();
        }
    }

    protected static class ItemDiffCallback extends DiffUtil.Callback {

        private final List<MediaData> oldItems;
        private final List<MediaData> newItems;

        public ItemDiffCallback(List<MediaData> oldItems, List<MediaData> newItems) {
            this.oldItems = new ArrayList<>();
            this.newItems = new ArrayList<>();
            this.oldItems.add(new ProductBannerItemData(new MediaBean()));
            this.oldItems.addAll(oldItems);
            this.newItems.add(new ProductBannerItemData(new MediaBean()));
            this.newItems.addAll(newItems);
        }

        @Override
        public int getOldListSize() {
            return oldItems.size();
        }

        @Override
        public int getNewListSize() {
            return newItems.size();
        }

        @Override
        public boolean areItemsTheSame(int oldPosition, int newPosition) {
            boolean result = (oldPosition == newPosition);
            return result;
        }

        @Override
        public boolean areContentsTheSame(int oldPosition, int newPosition) {
            MediaData oldItem = oldItems.get(oldPosition);
            if (oldItem.getImagePath() == null && oldItem.getVideoPath() == null) {
                return false;
            }
            MediaData newItem = newItems.get(newPosition);
            return getItemId(oldItem).equals(getItemId(newItem));
        }

        private String getItemId(MediaData item) {
            if (item.isVideo()) {
                return "video_" + item.getVideoPath();
            } else {
                return "image_" + item.getImagePath();
            }
        }

        @Nullable
        @Override
        public Object getChangePayload(int oldItemPosition, int newItemPosition) {
            return null;
        }
    }
}
