package com.sayweee.weee.widget;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ImageSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ShotUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.span.RoundBackgroundSpan;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.CartStatusLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.weee.widget.span.ClickableImageSpan;
import com.sayweee.weee.widget.span.ClickableMovementMethod;
import com.sayweee.weee.widget.tips.TipsBarManager;
import com.sayweee.weee.widget.tips.TipsBean;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.tagflow.TagFlowLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.net.URISyntaxException;
import java.util.Map;


public class PostProductDetailView extends ProductView {

    private OnDialogDismiss onDialogDismiss;

    public PostProductDetailView(Context context) {
        super(context);
    }

    public PostProductDetailView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PostProductDetailView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void init(Context context, @Nullable AttributeSet attrs) {
        int layoutRes;
        layoutRes = R.layout.view_product_small_similar_list;
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
    }

    public void setOnDialogDismiss(OnDialogDismiss onDialogDismiss) {
        this.onDialogDismiss = onDialogDismiss;
    }

    @Override
    protected PostProductDetailView setAttachedProduct(ProductBean bean, int style, String source, OnOpCallback callback, Map<String, Object> element, Map<String, Object> ctx) {
        beforeAttachProduct(bean, style);
        ProductViewHelper.prependProductTagBuyAgain(bean);

        final boolean hasOldBean = !EmptyUtils.isEmpty(bean.getOldBean());
        helper.setVisible(R.id.iv_old_icon, hasOldBean);
        helper.setVisible(R.id.v_margin, hasOldBean);
        helper.setVisible(R.id.iv_similar, hasOldBean);
        helper.setVisible(R.id.v_bg, hasOldBean);
        helper.setVisible(R.id.rfl_sold_out, false);
        helper.setVisible(R.id.v_mask, false);

        setSimilarData(
                /* bean = */bean,
                /* source = */source,
                /* oldBean = */bean.getOldBean(),
                /* callback = */callback,
                /* onDialogDismiss = */onDialogDismiss
        );

        helper.setOnClickListener(R.id.layout_product_view, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                ProductBean mBean = EmptyUtils.isEmpty(bean.getOldBean()) ? bean : bean.getOldBean();
                performProductClick(mBean, onDialogDismiss);
            }
        });
        helper.setOnClickListener(R.id.v_bg, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                performProductClick(bean, onDialogDismiss);
            }
        });
        return this;
    }

    private void performProductClick(final ProductBean bean, OnDialogDismiss onDialogDismiss) {
        onProductClick(getContext(), bean);
        if (bean.isBundle() || bean.isHotDish()) {
            onDialogDismiss.onDismiss();
        }
    }

    private void setSimilarData(ProductBean bean, String source, @Nullable ProductBean oldBean, OnOpCallback callback, OnDialogDismiss onDialogDismiss) {
        //是否显示受限
        Context context = getContext();
        boolean hasOldBean = false;
        //image
        if (!EmptyUtils.isEmpty(oldBean)) {
            hasOldBean = true;
            String oldImage = oldBean.getHeadImageUrl();
            ImageLoader.load(
                    context,
                    helper.getView(R.id.iv_old_icon),
                    WebpManager.get().getConvertUrl(SPEC_PRODUCT, oldImage),
                    R.mipmap.iv_product_placeholder
            );
        }
        String image = bean.getHeadImageUrl();
        ImageLoader.load(
                context,
                helper.getView(R.id.iv_icon),
                WebpManager.get().getConvertUrl(SPEC_PRODUCT, image),
                R.mipmap.iv_product_placeholder
        );


        boolean showBasePrice = bean.base_price > 0;
        ViewTools.setViewVisibleByHelper(helper, false, R.id.layout_volume, R.id.tv_price, R.id.tv_price_delete);

        if (bean.showVolumePrice() && !hasOldBean) {//post卡片
            //Volume Pricing
            helper.setVisible(R.id.layout_volume, true);
            TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
            if (tvPriceVolume != null) {
                Spanny s = new Spanny()
                        .append(OrderHelper.formatUSMoney(bean.price), new TextAppearanceSpan(context, R.style.style_fluid_root_numeral_base))
                        .append(context.getString(R.string.s_volume_threshold_simple, bean.volume_threshold));
                helper.setText(R.id.tv_price_volume, s);
            }
            //Volume single price
            TextView tvPriceSingle = helper.getView(R.id.tv_price_single);
            if (tvPriceSingle != null) {
                helper.setText(R.id.tv_price_single
                        , (showBasePrice ? new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()).append(" ") : new Spanny())
                                .append(context.getString(R.string.s_volume_threshold_one_qty, OrderHelper.formatUSMoney(bean.volume_price))));
            }
        } else {
            //price
            helper.setVisible(R.id.tv_price, true);
            helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(bean.price));
            helper.setText(R.id.tv_price_delete, new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()));
            helper.setVisible(R.id.tv_price_delete, showBasePrice);
        }

        //status & product op
        boolean showSoldStatus = true;
        CartOpLayout layoutOp = helper.getView(R.id.layout_op);
        CartStatusLayout layoutStatus = helper.getView(R.id.layout_status);

        helper.setOnClickListener(R.id.layout_status, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //12.9 已售罄状态不能取消提醒
                if (CollectManager.get().isProductCollect(bean.getProductId()) && isSoldOut(bean.sold_status)) {
                    return;
                }
                onStatusClick(bean, STYLE_ITEM_NORMAL, null);
                if (bean.isBundle() || bean.isHotDish()) {
                    onDialogDismiss.onDismiss();
                }
            }
        });

        //mark
        SpannableStringBuilder builder = new SpannableStringBuilder();
        TextView tvSpecialTag = helper.getView(R.id.tv_special_tag);
        //search list独有属性:bundle或者hot dish
        if (bean.isHotDish()) {
            tvSpecialTag.setVisibility(VISIBLE);
            tvSpecialTag.setText(R.string.s_restaurant_status);
        } else if (bean.isBundle()) {
            tvSpecialTag.setVisibility(VISIBLE);
            tvSpecialTag.setText(R.string.s_bundle_status);
        } else {
            tvSpecialTag.setVisibility(GONE);
        }
        helper.setVisible(R.id.layout_status_tips, false);
        helper.setVisible(R.id.tv_off, false);
        if (isPreSell(bean.sold_status)) {
            builder.append(context.getString(R.string.s_pre_sell));
            builder.setSpan(new RoundBackgroundSpan(Color.parseColor("#820B6A"),
                            CommonTools.dp2px(4), Color.parseColor("#ffffff"),
                            CommonTools.dp2px(2), CommonTools.dp2px(1),
                            CommonTools.dp2px(2), CommonTools.dp2px(1), 0, 0),
                    0, context.getString(R.string.s_pre_sell).length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            builder.setSpan(new AbsoluteSizeSpan(CommonTools.sp2px(10)), 0, context.getString(R.string.s_pre_sell).length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            builder.append(" ");
        } else {
            if (!hasOldBean && !EmptyUtils.isEmpty(bean.label_list)) {
                ProductBean.LabelListBean label = bean.label_list.get(0);
                try {
                    SpannableStringBuilder labelBuilder = new SpannableStringBuilder();
                    Spanny spanny = new Spanny();
                    spanny.append("", getLabelSpan(label.label_name, Color.parseColor(label.label_color), label.label_font_color));
                    labelBuilder.append(spanny);
                    helper.setText(R.id.tv_product_mark, labelBuilder);
                } catch (Exception ignored) {
                    // do nothing
                }
            }
        }
        Spanny spanny = new Spanny();
        if (bean.is_pantry) {
            Drawable drawable = PantryHelper.getCartPantryDrawable(context);
            spanny.append("", new ClickableImageSpan(drawable) {
                @Override
                public void onClick(View view) {
                    PantryHelper.toPantryTerms();
                }
            }).append(" ");
            builder.append(spanny);
        } else if (bean.is_colding_package) {
            LabelHelper.buildColdPackTag(context, spanny, true);
            builder.append(spanny);
        }

        builder.append(bean.name);
        TextView tvProduct = helper.getView(R.id.tv_product_name);
        int productNameLines = bean.showVolumePrice() && !EmptyUtils.isEmpty(bean.product_tag_list) ? 1 : 2;
        tvProduct.setMaxLines(productNameLines);
        tvProduct.setEllipsize(TextUtils.TruncateAt.END);
        tvProduct.setLongClickable(false);
        tvProduct.setMovementMethod(ClickableMovementMethod.getInstance());
        tvProduct.setText(builder);
        boolean hasActivity = !EmptyUtils.isEmpty(bean.product_tag_list);
        helper.setVisible(R.id.layout_tags, hasActivity);
        if (hasActivity) {
            TagFlowLayout tagLayout = helper.getView(R.id.layout_tags);
            tagLayout.setMaxLines(1);
            ProductViewHelper.setProductViewTag(context, helper.getView(R.id.layout_tags), bean.product_tag_list, ProductView.STYLE_LIST);
        }
        ViewTreeObserver vto = tvProduct.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                // 确保只监听一次布局变化
                tvProduct.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                // 在布局完成后获取 Layout 对象
                Layout layout = tvProduct.getLayout();
                if (layout != null) {
                    int maxLines = tvProduct.getMaxLines();
                    int lineCount = layout.getLineCount();

                    if (lineCount > maxLines) {
                        // 获取超出最大行数的文本内容
                        int lineEnd = layout.getLineEnd(maxLines - 1);
                        String textToShow = tvProduct.getText().subSequence(0, lineEnd).toString();

                        // 动态计算截取文本的最大长度
                        int maxLength = calculateMaxLength(tvProduct, textToShow);

                        // 截取文本并添加省略号
                        String trimmedText = textToShow.substring(0, Math.min(textToShow.length(), maxLength)) + "...";

                        tvProduct.setText(trimmedText);
                    }
                }
            }
        });
        View soldOutView = helper.getView(R.id.rfl_sold_out);
        boolean hasRestockTip = !EmptyUtils.isEmpty(bean.restock_tip);
        boolean isCollected = CollectManager.get().isProductCollect(bean.id);
        helper.setVisible(R.id.tv_restock_tip, hasRestockTip && !isCollected && !bean.isHotDish());
        layoutOp.setRightAddStyle();
        if (soldOutView != null) {
            soldOutView.setVisibility(GONE);
        }

        //collect
        helper.setVisible(R.id.iv_collect, false);
        ProductViewHelper.setProductCollect(helper.getView(R.id.iv_collect), isCollected);
        helper.setOnClickListener(R.id.iv_collect, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                onCollectClick(v, bean);
            }
        });

        if ((bean.isBundle() || bean.isHotDish())) {
            int titleResId = R.string.s_view;
            ProductViewHelper.setProductStatus(layoutStatus, titleResId, 0, R.color.color_atc_mini_fg_default);
        } else if (OrderManager.get().isReachLimit(bean)) {
            ProductViewHelper.setProductStatusPurchased(layoutStatus);
        } else if (isChangeOtherDay(bean.sold_status)) {
            ProductViewHelper.setProductStatusChangeDate(layoutStatus);
        } else if (isSoldOut(bean.sold_status)) {
            helper.setVisible(R.id.iv_similar, false);
            helper.setVisible(R.id.rfl_sold_out, true);
            helper.setVisible(R.id.v_mask, true);

            if (hasRestockTip) {
                helper.setText(R.id.tv_restock_tip, bean.restock_tip);
            }
            helper.setOnClickListener(R.id.tv_restock_tip, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    if (CollectManager.get().isProductCollect(bean.getProductId())) {
                        toWebPage(Constants.Url.COLLECT);
                    }
                }
            });
            displaySoldStatus(bean, STYLE_ITEM_NORMAL);
        } else if (isPreSell(bean.sold_status)) {
            showSoldStatus = false;
            layoutOp.setDisableStyle();
        } else {
            showSoldStatus = false;
            if (callback != null) {
                callback.onOp(layoutOp, bean);
            } else {
                OpHelper.helperOp(layoutOp, bean, bean, source);
            }
        }

        // old sold out
        helper.setVisible(R.id.rfl_old_sold_out, !EmptyUtils.isEmpty(oldBean) && isSoldOut(oldBean.sold_status));

        SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(bean.getProductId(), bean.getProductKey());
        int lastNum = simpleOrderItem != null ? simpleOrderItem.quantity : 0;
        int num = OrderHelper.editNum(true, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity(), bean.getVolumeThreshold());
        if (bean.getLimitIsShow()) {
            if (lastNum > 0 && lastNum == num) {
                layoutOp.onlyShowOpTips();
            } else {
                if (bean.min_order_quantity > 1 && lastNum <= bean.min_order_quantity) {
                    layoutOp.onlyShowMinPurchaseTips(lastNum);
                }
            }
            bean.setLimitIsShow(false);
        }
        helper.setVisible(R.id.layout_status, showSoldStatus);
        helper.setVisible(R.id.layout_op, !showSoldStatus);
    }

    // 动态计算截取文本的最大长度的方法
    private int calculateMaxLength(TextView textView, String text) {
        int availableWidth = textView.getWidth() - textView.getPaddingLeft() - textView.getPaddingRight();
        float textWidth = textView.getPaint().measureText(text);
        float ellipsisWidth = textView.getPaint().measureText("..."); // 省略号的宽度

        int maxLength = text.length();
        while (textWidth + ellipsisWidth > availableWidth && maxLength > 0) {
            maxLength--;
            text = text.substring(0, maxLength);
            textWidth = textView.getPaint().measureText(text);
        }

        return maxLength;
    }

    private ImageSpan getLabelSpan(String tag, int tagColor, String fontColor) {
        View badge = View.inflate(getContext(), R.layout.layout_text_badge, null);
        TextView child = badge.findViewById(R.id.tv_badge);
        child.setText(tag);
        child.setGravity(Gravity.CENTER);
        if (!EmptyUtils.isEmpty(fontColor)) {
            child.setTextColor(Color.parseColor(fontColor));
        } else {
            child.setTextColor(ContextCompat.getColor(getContext(), R.color.color_pricing_surface_1_fg_default_idle));
        }
        child.setIncludeFontPadding(false);
        ViewTools.applyTextStyle(child, R.style.style_fluid_root_badge_label_sm);
        child.setTextSize(10f);
        int dp6 = CommonTools.dp2px(8f);
        int dp2 = CommonTools.dp2px(1f);
        child.setPadding(dp6, dp2, dp6, dp2);
        child.setBackground(ShapeHelper.buildDrawable(tagColor, CommonTools.dp2px(10),
                ContextCompat.getColor(getContext(), R.color.color_pricing_surface_1_fg_default_idle),
                CommonTools.dp2px(2f)));
        Bitmap bitmap = ShotUtils.shotView(badge);
        BitmapDrawable drawable = new BitmapDrawable(getContext().getResources(), bitmap);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(),
                drawable.getIntrinsicHeight());
        return new CenterImageSpan(drawable);
    }

    public void onStatusClick(ProductBean bean, int style, OnStatusClickCallback onStatusClickCallback) {
        Context context = getContext();
        if (bean.isBundle() || bean.isHotDish()) {
            String url = bean.view_link;
            try {
                url = CommonTools.appendUri(bean.view_link, "trace_id=" + traceId);
            } catch (URISyntaxException ignored) {
            }
            context.startActivity(WebViewActivity.getIntent(context, url));
        } else if (isChangeOtherDay(bean.sold_status)) {
            context.startActivity(DateActivity.getIntent(context, String.valueOf(bean.id), "product modify me"));
            if (onStatusClickCallback != null) {
                onStatusClickCallback.onStatusClick("product_change_date", EagleTrackEvent.ClickType.VIEW);
            }
        } else if (isSoldOut(bean.sold_status)) {
            if (AccountManager.get().isLogin()) {
                boolean isCollected = CollectManager.get().toggleProductCollect(bean.getProductId());
                displaySoldStatus(bean, style);
                if (isCollected && style == STYLE_CARD) {
                    TipsBarManager.get().notify(new TipsBean(getContext().getString(R.string.s_added_to_my_list), Constants.Url.COLLECT));
                }
                if (onStatusClickCallback != null) {
                    onStatusClickCallback.onStatusClick("product_notify_me", isCollected ? EagleTrackEvent.ClickType.NORMAL : "cancel_selection");
                }
            } else {
                context.startActivity(AccountIntentCreator.getIntent(context));
            }
        }
    }

    public interface OnDialogDismiss {
        void onDismiss();
    }
}
