package com.sayweee.weee.service.track;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.analytics.WeeeAnalyticsIml;
import com.sayweee.weee.R;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.ImpressionHelper;
import com.yanzhenjie.recyclerview.SwipeRecyclerView;


import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/3/26.
 * Desc:
 */
public abstract class ImpressionTracker<T> {

    public static final int IMPRESSION_LIMIT = 1000;

    private boolean disable;
    protected boolean skipFirstResume;
    protected int resumeCount;

    public ImpressionTracker() {
        impressionEventHistory = new ArrayList<>();
    }

    //1. 设置数据变化刷新布局时触发
    //2. 滚动停止时触发
    //3. 事件频率随pv
    public void trackImpression(RecyclerView targetView) {
        if (isImpressionEnable() && targetView != null && targetView.isAttachedToWindow()) {
            setImpressionEnable(targetView, true);
            RecyclerView.Adapter adapter;
            if (targetView instanceof SwipeRecyclerView) {
                adapter = ((SwipeRecyclerView) targetView).getOriginAdapter();
            } else {
                adapter = targetView.getAdapter();
            }
            if ((adapter instanceof Adapter) && adapter.getItemCount() > 0) {
                RecyclerView.LayoutManager layoutManager = targetView.getLayoutManager();
                int firstVisibleItemPosition = RecyclerView.NO_POSITION;
                int firstCompletelyVisibleItemPosition = RecyclerView.NO_POSITION;
                int lastVisibleItemPosition = RecyclerView.NO_POSITION;
                int lastCompletelyVisibleItemPosition = RecyclerView.NO_POSITION;
                if (layoutManager instanceof LinearLayoutManager) {
                    LinearLayoutManager manager = (LinearLayoutManager) layoutManager;
                    int orientation = manager.getOrientation();
                    if (orientation == RecyclerView.HORIZONTAL) {
                        firstVisibleItemPosition = manager.findFirstVisibleItemPosition(); //第一个可见的item
                        View firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                        if (firstVisibleItem != null && ImpressionHelper.isImpressionEnableOnVertical(firstVisibleItem)) {
                            if (!ImpressionHelper.isImpressionEnableOnOnHorizontalAndVertical(firstVisibleItem)) {
                                firstVisibleItemPosition += 1;
                            }
                        } else {
                            firstVisibleItemPosition = RecyclerView.NO_POSITION;
                        }
                        lastVisibleItemPosition = manager.findLastVisibleItemPosition();
                        lastCompletelyVisibleItemPosition = manager.findLastCompletelyVisibleItemPosition();
                        View lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                        if (lastVisibleItem != null && ImpressionHelper.isImpressionEnableOnVertical(lastVisibleItem)) {
                            if (!ImpressionHelper.isImpressionEnableOnOnHorizontalAndVertical(lastVisibleItem)) {
                                lastVisibleItemPosition -= 1;
                            }
                        } else {
                            lastVisibleItemPosition = RecyclerView.NO_POSITION;
                        }
                    } else {
                        firstVisibleItemPosition = manager.findFirstVisibleItemPosition(); //第一个可见的item
                        firstCompletelyVisibleItemPosition = manager.findFirstCompletelyVisibleItemPosition(); //第一个完全可见的item
                        if (firstVisibleItemPosition != firstCompletelyVisibleItemPosition) {
                            View firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                            if (firstVisibleItem != null) {
                                if (!isHalfVisible(firstVisibleItem)) {
                                    firstVisibleItemPosition = firstCompletelyVisibleItemPosition;
                                }
                            }
                        }
                        lastVisibleItemPosition = manager.findLastVisibleItemPosition();
                        lastCompletelyVisibleItemPosition = manager.findLastCompletelyVisibleItemPosition();
                        if (lastVisibleItemPosition != lastCompletelyVisibleItemPosition) {
                            View lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                            if (lastVisibleItem != null) {
                                if (!isHalfVisible(lastVisibleItem)) {
                                    lastVisibleItemPosition = lastCompletelyVisibleItemPosition;
                                }
                            }
                        }
                    }
                } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                    StaggeredGridLayoutManager manager = (StaggeredGridLayoutManager) layoutManager;
                    if (manager.getSpanCount() < 2) {
                        return;
                    }
                    int[] temp = new int[2];
                    manager.findFirstVisibleItemPositions(temp);
                    firstVisibleItemPosition = Math.min(temp[0], temp[1]);
                    int firstVisibleItemPositionMax = Math.max(temp[0], temp[1]);
                    View firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                    if (firstVisibleItem != null) {
                        if (!isHalfVisible(firstVisibleItem)) {
                            firstVisibleItemPosition += 1;
                        }
                    }
                    firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                    if (firstVisibleItem != null) {
                        if (!isHalfVisible(firstVisibleItem)) {
                            firstVisibleItemPosition += 1;
                        }
                    }
                    firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                    if (firstVisibleItem != null) {
                        if (!isHalfVisible(firstVisibleItem)) {
                            firstVisibleItemPosition = RecyclerView.NO_POSITION;
                        }
                    } else {
                        firstVisibleItemPosition = RecyclerView.NO_POSITION;
                    }

                    manager.findLastVisibleItemPositions(temp);
                    lastVisibleItemPosition = Math.max(temp[0], temp[1]);
                    View lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                    if (lastVisibleItem != null) {
                        if (!isHalfVisible(lastVisibleItem)) {
                            lastVisibleItemPosition -= 1;
                        }
                    }
                    lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                    if (lastVisibleItem != null) {
                        if (!isHalfVisible(lastVisibleItem)) {
                            lastVisibleItemPosition -= 1;
                        }
                    }
                    lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                    if (lastVisibleItem != null) {
                        if (!isHalfVisible(lastVisibleItem)) {
                            lastVisibleItemPosition = RecyclerView.NO_POSITION;
                        }
                    } else {
                        lastVisibleItemPosition = RecyclerView.NO_POSITION;
                    }

                    if (lastVisibleItemPosition == RecyclerView.NO_POSITION && firstVisibleItemPosition != RecyclerView.NO_POSITION) {
                        lastVisibleItemPosition = firstVisibleItemPositionMax;
                    }
                }
                if (firstVisibleItemPosition == RecyclerView.NO_POSITION || lastVisibleItemPosition == RecyclerView.NO_POSITION || firstVisibleItemPosition > lastVisibleItemPosition) {
                    return;
                }
                List<T> impressionData = null;
                if (ImpressionTracker.this instanceof EagleImpressionTrackerIml) {
                    if (adapter instanceof EagleImpressionAdapter) {
                        impressionData = ((EagleImpressionAdapter) adapter).getEagleImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                    }
                } else if (adapter instanceof ImpressionAdapter) {
                    impressionData = ((ImpressionAdapter) adapter).getImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                }
                if (impressionData == null || impressionData.isEmpty() || impressionEventHistory.containsAll(impressionData)) {
                    //过滤 此次的埋点已经全部上报
                    return;
                }
                findTargetImpression(targetView, impressionData);
            }
        }
    }

    public void trackImpressionByPosition(RecyclerView targetView, int firstVisibleItemPosition, int lastVisibleItemPosition) {
        if (isImpressionEnable() && targetView != null && targetView.isAttachedToWindow()) {
            setImpressionEnable(targetView, true);
            RecyclerView.Adapter adapter;
            if (targetView instanceof SwipeRecyclerView) {
                adapter = ((SwipeRecyclerView) targetView).getOriginAdapter();
            } else {
                adapter = targetView.getAdapter();
            }
            if ((adapter instanceof Adapter) && adapter.getItemCount() > 0) {
                if (firstVisibleItemPosition == RecyclerView.NO_POSITION || lastVisibleItemPosition == RecyclerView.NO_POSITION || firstVisibleItemPosition > lastVisibleItemPosition) {
                    return;
                }
                List<T> impressionData = null;

                if (ImpressionTracker.this instanceof EagleImpressionTrackerIml) {
                    if (adapter instanceof EagleImpressionAdapter) {
                        impressionData = ((EagleImpressionAdapter) adapter).getEagleImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                    }
                }
                if (impressionData == null || impressionData.isEmpty() || impressionEventHistory.containsAll(impressionData)) {
                    //过滤 此次的埋点已经全部上报
                    return;
                }
                findTargetImpression(targetView, impressionData);
            }
        }
    }

    protected void findTargetImpression(RecyclerView targetView, List<T> lastData) {
        if (targetView != null && targetView.isAttachedToWindow()) {
            targetView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (targetView.isAttachedToWindow() && isImpressionEnable(targetView)) {
                        RecyclerView.Adapter adapter;
                        if (targetView instanceof SwipeRecyclerView) {
                            adapter = ((SwipeRecyclerView) targetView).getOriginAdapter();
                        } else {
                            adapter = targetView.getAdapter();
                        }
                        if (adapter instanceof Adapter && adapter.getItemCount() > 0) {
                            int firstVisibleItemPosition = RecyclerView.NO_POSITION;
                            int firstCompletelyVisibleItemPosition = RecyclerView.NO_POSITION;
                            int lastVisibleItemPosition = RecyclerView.NO_POSITION;
                            int lastCompletelyVisibleItemPosition = RecyclerView.NO_POSITION;
                            RecyclerView.LayoutManager layoutManager = targetView.getLayoutManager();
                            if (layoutManager instanceof LinearLayoutManager) {
                                LinearLayoutManager manager = (LinearLayoutManager) layoutManager;
                                firstVisibleItemPosition = manager.findFirstVisibleItemPosition(); //第一个可见的item
                                firstCompletelyVisibleItemPosition = manager.findFirstCompletelyVisibleItemPosition(); //第一个完全可见的item
                                if (firstVisibleItemPosition != firstCompletelyVisibleItemPosition) {
                                    View firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                                    if (firstVisibleItem != null) {
                                        int height = firstVisibleItem.getHeight();
                                        int top = firstVisibleItem.getTop();
                                        if (Math.abs(top) * 2 > height) {
                                            firstVisibleItemPosition = firstCompletelyVisibleItemPosition;
                                        }
                                    }
                                }
                                lastVisibleItemPosition = manager.findLastVisibleItemPosition();
                                lastCompletelyVisibleItemPosition = manager.findLastCompletelyVisibleItemPosition();
                                if (lastVisibleItemPosition != lastCompletelyVisibleItemPosition) {
                                    View lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                                    if (lastVisibleItem != null) {
                                        if (!isHalfVisible(lastVisibleItem)) {
                                            lastVisibleItemPosition = lastCompletelyVisibleItemPosition;
                                        }
                                    }
                                }
                            } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                                StaggeredGridLayoutManager manager = (StaggeredGridLayoutManager) layoutManager;
                                if (manager.getSpanCount() < 2) {
                                    return;
                                }
                                int[] temp = new int[2];
                                manager.findFirstVisibleItemPositions(temp);
                                firstVisibleItemPosition = Math.min(temp[0], temp[1]);
                                int firstVisibleItemPositionMax = Math.max(temp[0], temp[1]);
                                View firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                                if (firstVisibleItem != null) {
                                    if (!isHalfVisible(firstVisibleItem)) {
                                        firstVisibleItemPosition += 1;
                                    }
                                }
                                firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                                if (firstVisibleItem != null) {
                                    if (!isHalfVisible(firstVisibleItem)) {
                                        firstVisibleItemPosition += 1;
                                    }
                                }
                                firstVisibleItem = manager.findViewByPosition(firstVisibleItemPosition);
                                if (firstVisibleItem != null) {
                                    if (!isHalfVisible(firstVisibleItem)) {
                                        firstVisibleItemPosition = RecyclerView.NO_POSITION;
                                    }
                                } else {
                                    firstVisibleItemPosition = RecyclerView.NO_POSITION;
                                }

                                manager.findLastVisibleItemPositions(temp);
                                lastVisibleItemPosition = Math.max(temp[0], temp[1]);
                                View lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                                if (lastVisibleItem != null) {
                                    if (!isHalfVisible(lastVisibleItem)) {
                                        lastVisibleItemPosition -= 1;
                                    }
                                }
                                lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                                if (lastVisibleItem != null) {
                                    if (!isHalfVisible(lastVisibleItem)) {
                                        lastVisibleItemPosition -= 1;
                                    }
                                }
                                lastVisibleItem = manager.findViewByPosition(lastVisibleItemPosition);
                                if (lastVisibleItem != null) {
                                    if (!isHalfVisible(lastVisibleItem)) {
                                        lastVisibleItemPosition = RecyclerView.NO_POSITION;
                                    }
                                } else {
                                    lastVisibleItemPosition = RecyclerView.NO_POSITION;
                                }

                                if (lastVisibleItemPosition == RecyclerView.NO_POSITION && firstVisibleItemPosition != RecyclerView.NO_POSITION) {
                                    lastVisibleItemPosition = firstVisibleItemPositionMax;
                                }
                            }

                            if (firstVisibleItemPosition == RecyclerView.NO_POSITION || lastVisibleItemPosition == RecyclerView.NO_POSITION || firstVisibleItemPosition > lastVisibleItemPosition) {
                                return;
                            }
                            List<T> impressionData = null;
                            if (ImpressionTracker.this instanceof EagleImpressionTrackerIml) {
                                if (adapter instanceof EagleImpressionAdapter) {
                                    impressionData = ((EagleImpressionAdapter) adapter).getEagleImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                                }
                            } else if (adapter instanceof ImpressionAdapter) {
                                impressionData = ((ImpressionAdapter) adapter).getImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                            }
                            if (impressionData != null) {
                                impressionData.retainAll(lastData);
                                if (impressionData.size() > 0) {
                                    trackImpressionEvent(impressionData);
                                }
                            }
                        }
                    }
                }
            }, IMPRESSION_LIMIT);
        }
    }

    public ImpressionTracker setImpressionEnable(boolean enable) {
        this.disable = !enable;
        return this;
    }

    protected boolean isImpressionEnable() {
        return !disable;
    }

    /**
     * 某些列表数据在其他页面被带入，在onResume前数据已被上报一次，此方法可以避免此情况
     *
     * @param skip
     */
    public void setSkipFirstResume(boolean skip) {
        this.skipFirstResume = skip;
    }

    public void onPageResume(RecyclerView targetView) {
        ++resumeCount;
        if (impressionEventHistory == null) {
            impressionEventHistory = new ArrayList<>();
        }
        if (resumeCount != 1 || !skipFirstResume) {
            impressionEventHistory.clear();
        }
        trackImpression(targetView);
    }

    public void onPageResumeByPosition(RecyclerView targetView, int firstVisiblePosition, int lastVisiblePosition) {
        ++resumeCount;
        if (impressionEventHistory == null) {
            impressionEventHistory = new ArrayList<>();
        }
        if (resumeCount != 1 || !skipFirstResume) {
            impressionEventHistory.clear();
        }
        trackImpressionByPosition(targetView, firstVisiblePosition, lastVisiblePosition);
    }

    public void onPagePause(RecyclerView targetView) {
        setImpressionEnable(targetView, false);
    }

    protected void setImpressionEnable(RecyclerView targetView, boolean enable) {
        if (targetView != null) {
            targetView.setTag(R.id.tag_impression_tracker, enable);
        }
    }

    protected boolean isImpressionEnable(RecyclerView targetView) {
        if (targetView != null) {
            Object tag = targetView.getTag(R.id.tag_impression_tracker);
            return tag == null || (tag instanceof Boolean && (boolean) tag);
        }
        return false;
    }

    public boolean isTracked(String eventKey) {
        return impressionEventHistory != null && eventKey != null && impressionEventHistory.contains(eventKey);
    }

    public void setTracked(String eventKey) {
        if (impressionEventHistory == null) {
            impressionEventHistory = new ArrayList<>();
        }
        if (eventKey != null) {
            impressionEventHistory.add(eventKey);
        }
    }

    protected List<String> impressionEventHistory;

    protected void trackImpressionEvent(List<T> impressionData) {
        for (T event : impressionData) {
            if (event != null) {
                trackImpression(event);
            }
        }
    }

    public abstract void trackImpression(T event);

    public void onScrollStateChanged(RecyclerView view, int newState) {
        if (view != null && view.getVisibility() == View.VISIBLE && view.isAttachedToWindow()) {
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                notifyScrollStateChanged(view);
            }
        }
    }

    private void notifyScrollStateChanged(RecyclerView view) {
        if (view != null && view.getVisibility() == View.VISIBLE) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    trackImpression(view);
                }
            });
        }
    }

    protected boolean isHalfVisible(View view) {
        return ImpressionHelper.isImpressionEnableOnVertical(view);
    }

    /**
     * Determine whether the current imp event is valid,
     * filter when switching page the invalid imp events
     * @param event
     * @return
     */
    public boolean isValid(ImpressionBean event) {
        return event != null && event.getPageKey() != null &&
                event.getPageKey().equalsIgnoreCase(WeeeAnalyticsIml.get().getCleanPageKey());
    }

    protected boolean isHalfVisibleOnHorizontal(View view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.height() * 2 > view.getHeight() && rect.width() * 2 > view.getWidth();
        }
        return false;
    }

    protected boolean isHalfVisibleContainer(View view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.height() * 2 > view.getHeight();
        }
        return false;
    }

    public interface Adapter {
    }
}
