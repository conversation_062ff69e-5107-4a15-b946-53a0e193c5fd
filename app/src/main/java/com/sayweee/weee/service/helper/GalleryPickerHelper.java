package com.sayweee.weee.service.helper;

import android.app.Activity;

import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.sayweee.weee.module.gallery.GalleryPickerActivity;
import com.sayweee.weee.module.gallery.GalleryPickerConfig;

public class GalleryPickerHelper {

    public static OnResultCallbackListener<LocalMedia> listener;

    public static void releaseListener() {
        listener = null;
    }

    public static void showImageSelector(Activity activity, int num, OnResultCallbackListener<LocalMedia> listener) {
        GalleryPickerConfig config = new GalleryPickerConfig();
        config.imageOnly = true;
        config.pickNum = num;
        GalleryPickerHelper.listener = listener;
        activity.startActivity(GalleryPickerActivity.getIntent(activity, config));
    }

    public static void showVideoSelector(Activity activity, int num, OnResultCallbackListener<LocalMedia> listener) {
        GalleryPickerConfig config = new GalleryPickerConfig();
        config.videoOnly = true;
        config.pickNum = num;
        GalleryPickerHelper.listener = listener;
        activity.startActivity(GalleryPickerActivity.getIntent(activity, config));
    }

}
