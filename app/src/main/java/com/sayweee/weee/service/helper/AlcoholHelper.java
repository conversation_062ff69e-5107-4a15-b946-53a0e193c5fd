package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.WrapperPopWindow;
import com.sayweee.weee.widget.op.BubbleLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/8/27.
 * Desc:
 */
public class AlcoholHelper {

    private AlcoholHelper() {
    }

    public static boolean showAlcoholIntroduce(Context context, View view, int extra) {
        if (!AccountManager.get().isAlcoholTipsDisplayed() && context != null && view != null && view.isAttachedToWindow()) {
            view.postDelayed(new Runnable() {
                @Override
                public void run() {
                    execShowAlcoholIntroduce(context, view, extra);
                    AccountManager.get().setAlcoholTipsDisplayed(true);
                }
            }, 150);
            return true;
        }
        return false;
    }

    private static void execShowAlcoholIntroduce(Context context, View view, int extra) {
        if (context != null && view != null && view.isAttachedToWindow()) {
            int[] location = new int[2];
            view.getLocationInWindow(location);
            int width = CommonTools.dp2px(246);
            // width - paddingRight means triangle lands right hand
            int xOff = width - (view.getRight() - view.getLeft() - view.getPaddingRight());
            int x, y;
            int xDiff = 0;
            if (location[0] > xOff) {
                x = location[0] - xOff;
            } else {
                x = CommonTools.dp2px(16);
                xDiff = location[0] - xOff - x;
            }

            View popupView = createPopupView(context, xDiff);
            // Measure the popup view to get its height
            popupView.measure(View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY), View.MeasureSpec.UNSPECIFIED);
            int height = popupView.getMeasuredHeight();
            int yOff = height + CommonTools.dp2px(extra);
            if (location[1] > yOff) {
                y = location[1] - yOff;
            } else {
                y = 0;
            }

            WrapperPopWindow popWindow = new WrapperPopWindow.PopupWindowBuilder(context)
                    .setView(popupView)
                    .size(width, height)
                    .setFocusable(true)
                    .setOutsideTouchable(true)
                    .create();
            Activity activity;
            if (context instanceof Activity) {
                activity = (Activity) context;
            } else {
                activity = LifecycleProvider.get().getTopActivity();
            }
            if (activity == null || activity.getWindow() == null) {
                return;
            }
            View decorView = activity.getWindow().getDecorView();
            popWindow.showAtLocation(decorView, Gravity.TOP | Gravity.LEFT, x, y);
            decorView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (popWindow != null && popWindow.isShow()) {
                        popWindow.dismiss();
                    }
                }
            }, 3000L);
        }
    }

    private static View createPopupView(Context context, int xDiff) {
        return ViewTools.getHelperView(context, R.layout.dialog_pantry, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                ((LinearLayout) helper.getView(R.id.layout_bubble_inner)).setGravity(Gravity.LEFT);
                BubbleLayout layoutBubble = helper.getView(R.id.layout_bubble);
                if (xDiff != 0) {
                    layoutBubble.setTriangleOffset(xDiff + layoutBubble.getTriangleOffset());
                }
                String title = context.getResources().getString(R.string.s_valid_id_needed);
                TextView tvTitle = helper.getView(R.id.tv_title);
                Drawable drawable = getAlcoholDrawable(context);
                if (drawable != null) {
                    tvTitle.setText(new Spanny(" ", new CenterImageSpan(drawable)).append("  ").append(title, new StyleSpan(Typeface.BOLD)));
                } else {
                    tvTitle.setText(title);
                }
                String text = context.getResources().getString(R.string.s_alcohol_introduce_content);
                helper.setText(R.id.tv_content, text);
            }
        });
    }

    public static Drawable getAlcoholDrawable(Context context) {
        return getAlcoholDrawable(context, 1f);
    }

    public static Drawable getAlcoholDrawable(Context context, float scaleFactor) {
        Drawable drawable = ContextCompat.getDrawable(context, R.mipmap.ic_alcohol_flag);
        if (drawable != null) {
            // 75x18
            drawable.setBounds(0, 0, CommonTools.dp2px(75 * scaleFactor), CommonTools.dp2px(18 * scaleFactor));
        }
        return drawable;
    }

    public static void toAlcoholAgreement(int alcoholAgreementType) {
        String url = alcoholAgreementType == Constants.AlcoholAgreementType.SELLER
                ? Constants.Url.AGREEMENT_OF_ALCOHOL_SELLER
                : Constants.Url.AGREEMENT_OF_ALCOHOL;
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            activity.startActivity(WebViewActivity.getIntent(activity, url));
        }
    }

    /**
     * Convenience method to get the alcohol agreement type when upsell
     */
    public static int getAlcoholAgreementTypeUpsell(
            @Nullable NewPreOrderBean preOrder
    ) {
        if (preOrder == null) {
            return Constants.AlcoholAgreementType.NONE;
        }
        return getAlcoholAgreementType(
                /* orderSubType= */null, // convenience store orders do not have a sub type, like upsell
                /* containsAlcohol= */preOrder.contain_alcohol,
                /* isRecordAlcohol= */preOrder.is_record_alcohol,
                /* isRecordSellerAlcohol= */preOrder.is_record_seller_alcohol
        );
    }

    public static int getAlcoholAgreementType(
            @Nullable String orderSubType,
            boolean containsAlcohol,
            boolean isRecordAlcohol,
            boolean isRecordSellerAlcohol
    ) {
        if (Constants.OrderSubType.COMMISSION_PARTNER.equals(orderSubType)) {
            // seller alcohol agreement
            return !isRecordSellerAlcohol
                    ? Constants.AlcoholAgreementType.SELLER
                    : Constants.AlcoholAgreementType.NONE;
        } else {
            // grocery alcohol agreement
            return containsAlcohol && !isRecordAlcohol
                    ? Constants.AlcoholAgreementType.GROCERY
                    : Constants.AlcoholAgreementType.NONE;
        }
    }

    public static boolean isShowAlcoholAgreement(int alcoholAgreementType) {
        return alcoholAgreementType != Constants.AlcoholAgreementType.NONE;
    }

    @NonNull
    public static String getAlcoholFaqUrl(boolean isMkpl, @Nullable Integer sellerId) {
        String url = Constants.Url.FAQ_OF_ALCOHOL;
        Map<String, String> params = new ArrayMap<>();
        if (isMkpl) {
            params.put("type", "seller");
        }
        if (sellerId != null) {
            params.put("seller_id", String.valueOf(sellerId));
        }
        return CommonTools.packetUrlParams(url, params);
    }
}
