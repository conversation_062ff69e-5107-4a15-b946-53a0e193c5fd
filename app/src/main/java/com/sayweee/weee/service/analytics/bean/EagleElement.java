package com.sayweee.weee.service.analytics.bean;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import java.util.Map;

public class EagleElement implements IEagleTraceMap {

    private String modNm;
    private int modPos = -1;
    private String secNm;
    private int secPos = -1;

    public EagleElement() {

    }

    public EagleElement(@NonNull EagleElement other) {
        this.modNm = other.modNm;
        this.modPos = other.modPos;
        this.secNm = other.secNm;
        this.secPos = other.secPos;
    }

    public EagleElement setModNm(String modNm) {
        this.modNm = modNm;
        return this;
    }

    public EagleElement setModPos(int modPos) {
        this.modPos = modPos;
        return this;
    }

    public EagleElement setSecNm(String secNm) {
        this.secNm = secNm;
        return this;
    }

    public EagleElement setSecPos(int secPos) {
        this.secPos = secPos;
        return this;
    }

    public String getModNm() {
        return modNm;
    }

    public int getModPos() {
        return modPos;
    }

    public String getSecNm() {
        return secNm;
    }

    public int getSecPos() {
        return secPos;
    }

    @Nullable
    @Override
    public Map<String, Object> asMap() {
        Map<String, Object> map = new ArrayMap<>(4);
        map.put("mod_nm", modNm);
        if (modPos != -1) {
            map.put("mod_pos", modPos);
        }
        map.put("sec_nm", secNm);
        if (secPos != -1) {
            map.put("sec_pos", secPos);
        }
        return map;
    }
}
