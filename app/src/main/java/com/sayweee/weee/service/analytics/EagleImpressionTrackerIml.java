package com.sayweee.weee.service.analytics;

import androidx.annotation.Nullable;

import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.track.ImpressionTracker;

/**
 *
 */
public class EagleImpressionTrackerIml extends ImpressionTracker<ImpressionBean> {

    @Override
    public void trackImpression(@Nullable ImpressionBean event) {
        if (event == null) return;
        if (isValid(event) && !isTracked(event.getKey())) {
            setTracked(event.getKey());
            AppAnalytics.logEvent(event.eventName, event.params);
        }
    }
}
