package com.sayweee.weee.module.home.provider.banner;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_BANNER_LINE;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsMobileImageInfo;
import com.sayweee.weee.module.home.bean.CarouselBean;
import com.sayweee.weee.module.home.provider.banner.data.CmsBannerLineData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.youth.banner.Banner;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.listener.OnBannerListener;

/**
 * Author:  winds
 * Date:    2023/6/9.
 * Desc:
 */
public class BannerLineProvider extends SimpleSectionProvider<CmsBannerLineData, AdapterViewHolder> {

    @Override
    public int getItemType() {
        return CmsItemType.BANNER_LINE;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_banner_line;
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
        if (holder.getItemViewType() == getItemType()) {
            View view = holder.getView(R.id.banner);
            if (view instanceof Banner) {
                Object tag = view.getTag();
                if (tag instanceof Boolean && ((Boolean) tag)) {
                    ((Banner) view).start();
                }
            }
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull AdapterViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        if (holder.getItemViewType() == getItemType()) {
            View view = holder.getView(R.id.banner);
            if (view instanceof Banner) {
                ((Banner) view).stop();
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsBannerLineData item) {
        CarouselBanner banner = helper.getView(R.id.banner);
        LinearLayout layoutIndicator = helper.getView(R.id.layout_indicator_container);
        banner.removeIndicator();

        boolean isLoop = false;
        boolean isAutoScroll = false;
        int count = item.t.getBannerData().size();
        if (count > 1) {
            isLoop = item.t.loop;
            isAutoScroll = item.t.autoplay;
        }
        banner.setTag(isAutoScroll);
        banner.isAutoLoop(isAutoScroll);
        banner.setLoopTime(item.getLoopInterval());
        helper.setVisibleCompat(R.id.layout_indicator_container, count > 1);
        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal, R.drawable.shape_line_banner_line_selected, true, count, 0);
        banner.setAdapter(
                        new BannerImageAdapter<CarouselBean>(item.t.getBannerData()) {

                            @Override
                            public void onBindView(BannerImageHolder holder, CarouselBean data, int position, int size) {
                                ImageLoader.load(context, holder.imageView, WebpManager.get().getConvertUrl(SPEC_BANNER_LINE, data.img_url), R.color.color_place);
                            }
                        }, isLoop)
                .setOnBannerListener(new OnBannerListener<CarouselBean>() {
                    @Override
                    public void OnBannerClick(CarouselBean data, int position) {
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(item.getEventKey())
                                .setMod_pos(item.position)
                                .setTargetNm(String.valueOf(data.id))
                                .setTargetPos(position)
                                .setTargetType(EagleTrackEvent.BannerType.BANNER_LINE)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setUrl(data.link_url)
                                .build().getParams());
                        context.startActivity(WebViewActivity.getIntent(context, data.link_url));
                    }
                })
                .addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
                    @Override
                    public void onPageSelected(int position, boolean isAuto) {
                        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal, R.drawable.shape_line_banner_line_selected, false, count, position);
                        if (item.t.getBannerData().size() > position) {
                            CarouselBean bean = item.t.getBannerData().get(position);
                            if (bean != null) {
                                if (!isAuto) {
                                    trackEagleBannerImp(item.getEventKey()
                                            , item.position
                                            , null
                                            , -1
                                            , bean.id
                                            , bean.key
                                            , position
                                            , EagleTrackEvent.BannerType.BANNER_LINE
                                            , bean.link_url);
                                }
                            }
                        }
                    }
                });
        //mobileImageInfo
        CmsMobileImageInfo mobileImageInfo = item.property.getMobileImageInfo();
        if (mobileImageInfo != null && mobileImageInfo.isValid()) {
            ViewGroup.LayoutParams params = banner.getLayoutParams();
            params.width = ScreenUtils.getScreenWidth(context);
            params.height = (int) DecimalTools.multiply(params.width, DecimalTools.divide(mobileImageInfo.height, mobileImageInfo.width));
        }
    }

    private void trackEagleBannerImp(String mod_nm, int mod_pos, String sec_nm, int sec_pos, int banner_id, String banner_key, int banner_pos, String banner_type, String value) {
        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_HOME, value)) {
            AppAnalytics.logBannerImp(new EagleTrackModel.Builder()
                    .setMod_nm(mod_nm)
                    .setMod_pos(mod_pos)
                    .setSec_nm(sec_nm)
                    .setSec_pos(sec_pos)
                    .setBanner_id(banner_id)
                    .setBanner_pos(banner_pos)
                    .setBanner_type(banner_type)
                    .setUrl(value)
                    .build().getParams());
            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_HOME, value);
        }
    }
}
