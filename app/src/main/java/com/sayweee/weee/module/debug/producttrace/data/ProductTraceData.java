package com.sayweee.weee.module.debug.producttrace.data;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class ProductTraceData {

    private final ProductTraceKey key;
    private Map<String, CharSequence> traceMap;

    public ProductTraceData(@NonNull ProductTraceKey key) {
        this.key = key;
    }

    @NonNull
    public ProductTraceKey getKey() {
        return key;
    }

    @Nullable
    public CharSequence getData(String key) {
        if (key != null) {
            return traceMap.get(key);
        }
        return null;
    }

    public void putData(String key, CharSequence value) {
        if (key != null && value != null) {
            if (traceMap == null) {
                traceMap = new HashMap<>();
            }
            traceMap.put(key, value);
        }
    }

    @NonNull
    public Collection<String> getTraceDomains() {
        if (traceMap != null) {
            return traceMap.keySet();
        }
        return new ArrayList<>();
    }
}
