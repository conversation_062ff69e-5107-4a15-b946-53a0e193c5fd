package com.sayweee.weee.module.web.handler;

import android.app.Activity;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.MainActivity;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.popup.bean.PopupBean;
import com.sayweee.weee.module.post.PostSearchActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DefaultTools;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    12/31/21.
 * Desc:
 */
public final class UrlTradeProcessor {

    public static final int TRADE_NONE = 0; //未处理
    public static final int TRADE_OFF = 1;  //已处理
    public static final int TRADE_FINISHED = 2; //已处理结束，需finish当前页面

    public static final int PROCESS_FLAG_NORMAL = 1; // 正常跳转
    public static final int PROCESS_FLAG_PRE_PROCESS = 1; // 预拦截跳转

    private UrlTradeProcessor() {

    }

    public static int trade(String url, String sessionType) {
        return UrlTradeProcessor.trade(url, sessionType, PROCESS_FLAG_NORMAL);
    }

    public static int trade(String url, String sessionType, int flag) {
        if (url != null) {
            if (url.matches(Constants.UrlPattern.NATIVE_APP)) {
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.HOME_PAGE)) {
                HandlerHelper.handleHome(url);
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.WEEE_REDIRECT) && !UrlTradeProcessor.isWeee(url)) {
                DefaultTools.toBrowser(LifecycleProvider.get().getTopActivity(), url);
                return TRADE_OFF;
            } else if ((url.matches(Constants.UrlPattern.SHOPPING_LIST)) || (url.matches(Constants.UrlPattern.CATEGORIES))) {
                HandlerHelper.handleCategory(url);
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.CART) || url.matches(Constants.UrlPattern.CART2)) {
                HandlerHelper.handleCart(url);
                return TRADE_OFF;
            } else if (url.matches(Constants.UrlPattern.COMMUNITY_HOME) || url.matches(Constants.UrlPattern.COMMUNITY_HOME_2)) {
                Activity activity = LifecycleProvider.get().getTopActivity();
                if (activity != null) { //当前不在首页 回到post首页
                    if (!(activity instanceof MainActivity)) {
                        HandlerHelper.handlePost();
                        return TRADE_FINISHED;
                    }
                }
            } else if (url.matches(Constants.UrlPattern.ME)) {
                HandlerHelper.handleMe();
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.LOGIN)) { //登陆预处理
                HandlerHelper.handleLogin(LifecycleProvider.get().getTopActivity(), url);
                return TRADE_FINISHED;
            } else if ((url.matches(Constants.UrlPattern.PRODUCT_DETAIL)) || (url.matches(Constants.UrlPattern.PRODUCT_DETAIL2))
                    && !url.contains(Constants.UrlPattern.MKPL_GROUP_ORDER)) {
                boolean processed = HandlerHelper.handleProductDetail(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.CATEGORY_PARAMS)) {
                String target = "category/";
                Map<String, String> params = CommonTools.parseQueryParams(url, true);
                String cate = null;
                if (params.containsKey("filter_sub_category")) {
                    //https://www.sayweee.com/zh/category/instant/Instant?filter_sub_category=trending&offset=0&filters=%7B%22catalogue_num%22%3A%22instant%22%7D&trigger_type=taxonomy_instant
                    cate = params.get("filter_sub_category");
                }
                if (cate == null) {
                    String path = CommonTools.removeQueryPath(url);
                    int index = path.lastIndexOf(target);
                    if (index >= 0) {
                        cate = path.substring(index + target.length());
                    }
                }
                HandlerHelper.handleCategoryParams(cate, params);
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.APP_DOWNLOAD)) {
                Map<String, String> params = CommonTools.parseQueryParams(url);
                String target = params.get("url");
                if (target != null) {
                    try {
                        target = URLDecoder.decode(target, "utf-8");
                    } catch (UnsupportedEncodingException ignored) {
                        // no op
                    }
                    int tradeCode = trade(target, sessionType);
                    if (isProcessed(tradeCode)) {
                        return TRADE_FINISHED;
                    } else {
                        Activity activity = LifecycleProvider.get().getTopActivity();
                        if (activity != null) {
                            activity.startActivity(WebViewActivity.getIntent(activity, target));
                            return TRADE_FINISHED;
                        }
                    }
                }
            } else if (url.matches(Constants.UrlPattern.THEME_LANDING)) {
                boolean processed = HandlerHelper.handleThemeLanding(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_FINISHED;
                }
            } else if (url.matches(Constants.UrlPattern.CONTENT_POPUP)) {
                PopupManager.get().show(PopupBean.builder(true, url, 343, 480));
                return TRADE_FINISHED;
            } else if (url.matches(Constants.UrlPattern.REVIEW_DETAIL) || url.matches(Constants.UrlPattern.REVIEW_DETAIL_2)) {
                //review详情
                boolean processed = HandlerHelper.handleReviewDetail(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.REVIEW_LIST) || url.matches(Constants.UrlPattern.REVIEW_LIST_2)) {
                //review详情
                boolean processed = HandlerHelper.handleReviewList(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.VIDEO_DETAIL) || url.matches(Constants.UrlPattern.VIDEO_DETAIL_2)) {
                //post详情
                boolean processed = HandlerHelper.handlePostVideoDetail(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.COMMUNITY_PORTAL)) {
                //社区消息主页
                boolean processed = HandlerHelper.handleCommunityPortal(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_FINISHED;
                }
            } else if (url.matches(Constants.UrlPattern.COMMUNITY_SEARCH) || url.matches(Constants.UrlPattern.COMMUNITY_SEARCH_2)) {
                //社区搜索
                Activity activity = LifecycleProvider.get().getTopActivity();
                if (activity != null) {
                    activity.startActivity(PostSearchActivity.getIntent(activity));
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.PROFILE) || url.matches(Constants.UrlPattern.PROFILE_2)) {
                //个人主页
                boolean processed = HandlerHelper.handleProfilePage(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.MESSAGE_CENTER)) {
                //社区消息主页new
                boolean processed = HandlerHelper.handleMessageCenter(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_FINISHED;
                }
            } else if (url.matches(Constants.UrlPattern.VENDOR_PAGE)) {
                boolean processed = HandlerHelper.handleVendorPage(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.ACTIVITY_PORTAL)) {
                boolean processed = HandlerHelper.handleActivityPortal(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.SEARCH)) {
                boolean processed = HandlerHelper.handleSearch(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.CHECKOUT) || url.matches(Constants.UrlPattern.CHECKOUT_ORDER)) {
                boolean processed = HandlerHelper.handleCheckout(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.ADDRESS)) {
                boolean processed = HandlerHelper.handleAddressBook(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.ME_TO_FOLLOWERS)) {
                boolean processed = HandlerHelper.handleMeToFollowers(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.MESSAGE_PORTAL)) {
                boolean processed = HandlerHelper.handleMessagePortal(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.STUDENT_VERIFY)) {
                boolean processed = HandlerHelper.handleStudentVerify(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.GLOBAL)
                    || url.matches(Constants.UrlPattern.MKPL_WATERFALL)) {
                boolean processed = HandlerHelper.handleGlobal(
                        LifecycleProvider.get().getTopActivity(),
                        url,
                        /* animate= */flag == PROCESS_FLAG_PRE_PROCESS
                );
                if (processed) {
                    return TRADE_OFF;
                }
                return TRADE_OFF;
            } else if (url.matches(Constants.UrlPattern.MKPL_FBW_LANDING)) {
                boolean processed = HandlerHelper.handleFbwLanding(
                        LifecycleProvider.get().getTopActivity(),
                        url
                );
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.BRAND)) {
                boolean processed = HandlerHelper.handleBrand(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.SOCIAL_EDIT_REVIEW)) {
                boolean processed = HandlerHelper.handleSocialEditReview(
                        LifecycleProvider.get().getTopActivity(),
                        url,
                        /* animate= */flag == PROCESS_FLAG_PRE_PROCESS
                );
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.SOCIAL_POST_REVIEW)) {
                boolean processed = HandlerHelper.handleSocialPostReview(
                        LifecycleProvider.get().getTopActivity(),
                        url,
                        /* animate= */flag == PROCESS_FLAG_PRE_PROCESS
                );
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.SOCIAL_POST_EXPLORE_VIDEO)) {
                boolean processed = HandlerHelper.handleSocialExploreVideo(
                        LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.THEMATIC)) {
                boolean processed = HandlerHelper.handleThematic(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            }
//            else if (url.matches(Constants.UrlPattern.LIVE)) {
//                boolean processed = HandlerHelper.handleLive(LifecycleProvider.get().getTopActivity(), url);
//                if (processed) {
//                    return TRADE_OFF;
//                }
//            }
            else if (url.matches(Constants.UrlPattern.COLLECTION_ACTIVITY)) {
                boolean processed = HandlerHelper.handleCollection(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.COLLECTION_AUTOMATIC)) {
                boolean processed = HandlerHelper.handleAutoCollection(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_OFF;
                }
            } else if (url.matches(Constants.UrlPattern.ORDER_LIST)) {
                // 订单列表
                HandlerHelper.handleOrderList(LifecycleProvider.get().getTopActivity(), url);
                return TRADE_OFF;
            } else if (url.matches(Constants.UrlPattern.SHOW_BY_POPUP)) {
                PopupManager.get().showSlide(LifecycleProvider.get().getTopActivity(), url);
                return TRADE_OFF;
            } else if (url.matches(Constants.UrlPattern.DEAL_PAY_V2)) {
                // https://tb1.sayweee.net/order/dealpay/v2/42656105
                boolean processed = HandlerHelper.handleDealPayV2(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_FINISHED;
                }
            } else if (url.matches(Constants.UrlPattern.PAYMENT_ADD_CARD)) {
                boolean processed = HandlerHelper.handlePaymentAddEbtCard(LifecycleProvider.get().getTopActivity(), url);
                if (processed) {
                    return TRADE_FINISHED;
                }
            } else if (url.matches(Constants.UrlPattern.PUSH_TO_NATIVE)) {
                // 最好在最后处理
                boolean isClosePage = HandlerHelper.handlePushToNative(LifecycleProvider.get().getTopActivity(), url);
                return isClosePage ? TRADE_FINISHED : TRADE_OFF;
            }
        }
        return TRADE_NONE;
    }

    /**
     * popup 加载需要context，但当前context会立即被销毁，故特殊处理
     *
     * @param url
     * @return
     */
    public static int specialTrade(String url) {
        if (url.matches(Constants.UrlPattern.SHOW_BY_POPUP)) {
            List<Activity> list = LifecycleProvider.get().getActivityStack();
            int index = list.size() - 2;
            if (index >= 0) {
                Activity activity = list.get(index);
                if (activity != null) {
                    PopupManager.get().showSlide(activity, url);
                    return TRADE_OFF;
                }
            }
        }
        return TRADE_NONE;
    }

    public static boolean isProcessed(int tradeCode) {
        return tradeCode > TRADE_NONE;
    }

    @SuppressWarnings("squid:S5852")
    public static boolean isWeee(String url) {
        if (url != null) {
            return url.matches("^https?://.+sayweee\\.(com|net|cn).*")
                    || url.matches("^https?://.+masgusto\\.(com|net).*");
        }
        return false;
    }
}
