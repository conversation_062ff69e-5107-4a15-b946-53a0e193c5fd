package com.sayweee.weee.module.cart.bean;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/11/30.
 * Desc:
 */
public class SimpleProductBean implements Serializable {

    public int id;
    public String product_key;
    public String name;
    public String img;
    public List<String> img_urls;
    public double price;
    public double base_price;
    public String category;
    public String is_hotdish;
    public String sold_status;
    public String view_link;

    public boolean volume_price_support;
    public double volume_price;
    public int volume_threshold;

    public String biz_type;
    public ProductBean.VenderInfo vender_info_view;
    public List<EntranceTag> product_tag_list;
    public boolean is_pantry;
    public boolean is_colding_package;

    public String getHeadImageUrl() {
        if (img_urls != null && !img_urls.isEmpty()) {
            return img_urls.get(0);
        }
        return img;
    }

    public boolean isHotDish() {
        return Constants.ProductType.VALUE_HOT_DISH.equals(is_hotdish);
    }

    public String getVendorDeliveryNewDesc(boolean showFreeShipping) {
        if (isSeller() && vender_info_view != null) {
            if (showFreeShipping) {
                return vender_info_view.free_delivery_desc;
            } else {
                return vender_info_view.delivery_desc;
            }
        }
        return null;
    }

    public boolean freeShippingDescShow(SimpleProductBean bean) {
        if (bean.isSeller() && !EmptyUtils.isEmpty(bean.product_tag_list)) {
            for (int i = 0; i < bean.product_tag_list.size(); i++) {
                EntranceTag tag = bean.product_tag_list.get(i);
                if (tag.isFreeShipping()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isSeller() {
        return "seller".equals(biz_type);
    }
}
