package com.sayweee.weee.module.cart.bean.setcion;

import androidx.collection.ArrayMap;

import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.bean.ICartImpression;
import com.sayweee.weee.module.cart.bean.IUnavailable;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.SaveForLaterResponseBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.bean.EagleElement;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.function.BiFunction;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

public class SectionCartTitleData
        extends AdapterWrapperData<NewSectionBean.ShippingInfo>
        implements Serializable, IUnavailable, ICartImpression {

    public String cartId;
    public boolean isCollapsedStatus;
    public int titleRes;
    public int productCount;
    private boolean unavailable;
    public boolean isSeller;
    public boolean isPantry;
    public int vendorId;
    private ImpressionBean impressionBean;
    public boolean isSave4Later;

    private EagleElement eagleElement;

    public SectionCartTitleData(String cartId, boolean isCollapsedStatus) {
        super(CartSectionType.TYPE_TITLE);
        this.cartId = cartId;
        this.isCollapsedStatus = isCollapsedStatus;
    }

    @Override
    public IUnavailable setUnavailable(boolean unavailable) {
        this.unavailable = unavailable;
        return this;
    }

    @Override
    public boolean isUnavailable() {
        return unavailable;
    }

    /**
     * 设置标题资源和商品总数，用于save for later情况
     *
     * @param title
     * @param productCount
     */
    public void setOnlyTitle(int title, int productCount) {
        this.titleRes = title;
        this.productCount = productCount;
    }

    public void setSellerInfo(boolean isSeller, int vendorId) {
        this.isSeller = isSeller;
        this.vendorId = vendorId;
    }


    public void setPantryInfo(boolean isPantry) {
        this.isPantry = isPantry;
    }

    /**
     * save for later情况下 此判断为true
     *
     * @return
     */
    public boolean isOnlyTitle() {
        return getData() == null;
    }

    @Override
    public void setImpressionBean(NewSectionBean bean, int index) {
        Map<String, Object> element = EagleTrackManger.get().getElement(EagleTrackEvent.ModNm.CART, 0, bean.type, index);
        eagleElement = new EagleElement().setModNm(EagleTrackEvent.ModNm.CART).setModPos(0).setSecNm(bean.type).setSecPos(index);
        Map<String, Object> co = new ArrayMap<>();
        co.put("sub_total_price", bean.fee_info != null ? bean.fee_info.sub_total_price : null);
        co.put("quantity", bean.quantity);

        List<Map<String, Object>> itemsMaps = new ArrayList<>();
        BiFunction<NewItemBean, NewSectionBean.ActivityInfo, Map<String, Object>> mapper = (item, activityInfo) -> {
            Map<String, Object> m = new ArrayMap<>();
            m.put("product_id", item.product_id);
            m.put("quantity", item.quantity);
            m.put("source", item.source);
            m.put("price", item.price);
            m.put("base_price", item.base_price > 0 ? item.base_price : null);
            m.put("reason_type", item.reason_type);
            m.put("status", item.status);
            m.put("volume_price_support", item.volume_price_support);
            m.put("promotion_offer", false);
            if (activityInfo != null) {
                m.put("promotion_type", activityInfo.original_type);
                m.put("promotion_offer", activityInfo.is_offer);
            }
            return m;
        };
        //活动商品
        if (bean.hasActivityInfo()) {
            for (NewSectionBean.ActivityInfo activityInfo : bean.activity_info) {
                if (activityInfo.hasItems()) {
                    for (NewItemBean item : activityInfo.items) {
                        itemsMaps.add(mapper.apply(item, activityInfo));
                    }
                }
            }
        }
        //普通商品
        if (!EmptyUtils.isEmpty(bean.items)) {
            for (NewItemBean item : bean.items) {
                itemsMaps.add(mapper.apply(item, null));
                if (item.hasActivityInfo()) {
                    for (NewSectionBean.ActivityInfo activityInfo : item.activity_info) {
                        if (activityInfo.hasItems()) {
                            for (NewItemBean subItem : activityInfo.items) {
                                itemsMaps.add(mapper.apply(subItem, activityInfo));
                            }
                        }
                    }
                }
            }
        }

        co.put("items", itemsMaps);

        co.put("original_shipping_fee", bean.shipping_info != null ? bean.shipping_info.orignal_shipping_fee : null);

        co.put("trade_in_items", null);
        if (!EmptyUtils.isEmpty(bean.activity_info)) {
            List<Map<String, Object>> tradeInMaps = new ArrayList<>();
            for (NewSectionBean.ActivityInfo infoBean : bean.activity_info) {
                List<NewItemBean> tradeInItems = infoBean.items;
                if (EmptyUtils.isEmpty(tradeInItems)) {
                    continue;
                }
                if (OrderHelper.isActivityTradeIn(infoBean.type) || OrderHelper.isActivityTradeInNew(infoBean.type)) {
                    for (NewItemBean item : tradeInItems) {
                        tradeInMaps.add(mapper.apply(item, infoBean));
                    }
                }
            }
            if (!EmptyUtils.isEmpty(tradeInMaps)) {
                co.put("trade_in_items", tradeInMaps);
            }
        }
        co.put("type", bean.type);
        co.put("delivery_pickup_date", bean.shipping_info != null ? bean.shipping_info.delivery_pickup_date : null);
        co.put("deal_id", bean.deal_id);
        co.put("vendor_id", bean.vendor_info != null ? bean.vendor_info.vendor_id : null);
        co.put("delivery_mode", bean.shipping_info != null ? bean.shipping_info.delivery_mode : null);
        co.put("shipping_fee", bean.shipping_info != null ? bean.shipping_info.shipping_fee : null);
        co.put("hotdish_wave", bean.shipping_info != null ? bean.shipping_info.hotdish_wave : null);
        co.put("total_price_with_activity", bean.fee_info != null ? bean.fee_info.total_price_with_activity : null);

        Map<String, Object> ctx = null;
        if (CollectionUtils.isNotEmpty(bean.activity_info)) {
            for (NewSectionBean.ActivityInfo activityInfo : bean.activity_info) {
                if (activityInfo.isCartDealType()) {
                    if ("trade_in".equalsIgnoreCase(activityInfo.original_type)) {
                        ctx = new EagleContext().setDiffPriceUpsell(OrderHelper.formatMoney(activityInfo.diff_amount)).asMap();
                    } else {
                        ctx = new EagleContext().setDiffPrice(OrderHelper.formatMoney(activityInfo.diff_amount)).asMap();
                    }
                    break;
                }
            }
        }
        Map<String, Object> params = new TrackParams()
                .put("co", co)
                .putAll(element)
                .putNonNull("ctx", ctx)
                .get();
        impressionBean = new ImpressionBean(EagleTrackEvent.EventType.CART_IMP, params, bean.cart_id + "_" + index);
    }

    @Override
    public void setSaveForLaterImpressionBean(SaveForLaterResponseBean bean, int index) {
        String saveForLater = EagleTrackEvent.SecNm.SAVE_FOR_LATER;
        Map<String, Object> element = EagleTrackManger.get().getElement(EagleTrackEvent.ModNm.CART, 0, saveForLater, index);
        eagleElement = new EagleElement().setModNm(EagleTrackEvent.ModNm.CART).setModPos(0).setSecNm(saveForLater).setSecPos(index);
        Map<String, Object> co = new ArrayMap<>();
        co.put("quantity", bean.total_count);
        List<Map<String, Object>> itemsMaps = new ArrayList<>();
        List<NewItemBean> items = bean.items;
        if (!EmptyUtils.isEmpty(items)) {
            for (NewItemBean item : items) {
                Map<String, Object> itemMap = new ArrayMap<>();
                itemMap.put("product_id", item.product_id);
                itemMap.put("quantity", item.quantity);
                itemMap.put("source", item.source);
                itemMap.put("price", item.price);
                itemMap.put("base_price", item.base_price > 0 ? item.base_price : null);
                itemMap.put("reason_type", item.reason_type);
                itemMap.put("status", item.status);
                itemMap.put("volume_price_support", item.volume_price_support);
                itemsMaps.add(itemMap);
            }
        }
        co.put("items", itemsMaps);
        co.put("type", saveForLater);
        Map<String, Object> params = new TrackParams().put("co", co).putAll(element).get();
        impressionBean = new ImpressionBean(EagleTrackEvent.EventType.CART_IMP, params, saveForLater + "_" + index);
    }

    @Override
    public ImpressionBean getImpressionBean() {
        return impressionBean;
    }

    public void setIsSave4Later(boolean isSave4Later) {
        this.isSave4Later = isSave4Later;
    }

    @Nullable
    public EagleElement getEagleElement() {
        return eagleElement;
    }
}
