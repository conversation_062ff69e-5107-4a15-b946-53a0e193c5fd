package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.bean.NewItemBean;

import java.util.HashMap;
import java.util.Map;

public class RecommendItemData extends AdapterWrapperData<NewItemBean> {

    public Map<String, Object> element;
    public Map<String, Object> ctx;
    String original_type;
    public boolean onlyShowQty;
    public boolean tradeInLimitReached;

    public RecommendItemData(NewItemBean newItemBean) {
        super(CartSectionType.TYPE_RECOMMEND_ITEM, newItemBean);
    }

    public RecommendItemData setOriginalType(String originalType) {
        this.original_type = originalType;
        return this;
    }

    public RecommendItemData setTrackingInfo(Map<String, Object> element, Map<String, Object> ctx) {
        if (element != null) {
            this.element = new HashMap<>(element);
        }
        if (ctx != null) {
            this.ctx = new HashMap<>(ctx);
        }
        return this;
    }

    public RecommendItemData setTradeInLimitReached(boolean tradeInLimitReached) {
        this.tradeInLimitReached = tradeInLimitReached;
        return this;
    }

    public boolean isHotDishProduct() {
        return t != null && "hotdish".equalsIgnoreCase(t.refer_type);
    }

    public boolean isInvalidProduct() {
        return t != null && !OrderProvider.get().isValidProduct(t.status);
    }

    public String convertSoldStatus() {
        //unavaliable,sold_out,change_date,limit
        if (isInvalidProduct() && t != null && t.reason_type != null) {
            if ("unavaliable".equalsIgnoreCase(t.reason_type) || "sold_out".equalsIgnoreCase(t.reason_type)) {
                return OrderManager.SOLD_OUT;
            } else if ("change_date".equalsIgnoreCase(t.reason_type)) {
                return OrderManager.CHANGE_OTHER_DAY;
            } else if ("limit".equalsIgnoreCase(t.reason_type)) {
                return OrderManager.REACH_LIMIT;
            }
            return t.reason_type;
        }
        return null;
    }

    public String getSourcePage() {
        if ("trade_in".equalsIgnoreCase(original_type)) {
            return t.can_add ? "trade_in_reach" : "trade_in_not_reach";
        } else {
            return null;
        }
    }

    public RecommendItemData setOnlyShowQty(boolean onlyShowQty) {
        this.onlyShowQty = onlyShowQty;
        return this;
    }
}
