package com.sayweee.weee.module.product.adapter;

import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.home.adapter.ProductItemMoreAdapter;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.product.ProductView;

import java.util.List;

public class PdpProductItemAdapter extends ProductItemMoreAdapter {

    public PdpProductItemAdapter(@Nullable List<ProductBean> list, int displayStyle) {
        super(list, displayStyle);
        mLayoutResId = R.layout.item_pdp_product;
    }

    @Override
    protected void convertProduct(@NonNull AdapterViewHolder helper, ProductBean item) {
        super.convertProduct(helper, item);
        ProductView productView = helper.getView(R.id.layout_product_view);
        double productsVisible = 2.8;
        int productsSpaceAmount = (int) Math.ceil(productsVisible) - 1;
        final int imagesFreeSpace = CommonTools.getWindowWidth(mContext) - CommonTools.dp2px(10) - CommonTools.dp2px(10) * productsSpaceAmount;
        int imageWidth = (int) (imagesFreeSpace / productsVisible);
        ViewGroup.LayoutParams layoutParams = helper.itemView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = imageWidth;
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            helper.itemView.setLayoutParams(layoutParams);
        }
        if (productView.getCartOpLayout() != null) {
            productView.getCartOpLayout().setTipsMaxWidth(imageWidth - CommonTools.dp2px(20));
        }
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        tvProductName.setMaxLines(LanguageManager.get().isCJK() ? 2 : 3);
    }

    @Override
    protected void setDisplayMinimumHeight(ProductView view) {
        ViewTools.setMinimumHeight(view, 0);
    }
}
