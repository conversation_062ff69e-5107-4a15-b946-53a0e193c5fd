package com.sayweee.weee.module.launch.service;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.service.ConfigService;
import com.sayweee.service.SessionService;
import com.sayweee.service.context.store.bean.StoreInfo;
import com.sayweee.service.core.IServiceResult;
import com.sayweee.weee.global.App;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.AccountBean;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.debug.log.LogFileManager;
import com.sayweee.weee.module.launch.bean.LanguageBean;
import com.sayweee.weee.module.launch.bean.LaunchHelpBean;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.search.v2.SearchV2Manager;
import com.sayweee.weee.module.search.v2.UserPersonalizationManager;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.helper.PushHelper;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import io.reactivex.Observable;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/16.
 * Desc:
 */
public class LaunchViewModel extends BaseViewModel<LaunchModel> {

    private boolean sessionTokenChanged;
    public MutableLiveData<LaunchHelpBean> launchFlowData = new MutableLiveData<>();

    public MutableLiveData<String> languageData = new MutableLiveData<>();

    public LaunchViewModel(@NonNull Application application) {
        super(application);
    }

    private void toGuideFlow(LanguageBean language) {
        launchFlowData.postValue(new LaunchHelpBean().setToGuideFlow(true, language, false));
    }

    private void toZipCodeFlow() {
        launchFlowData.postValue(new LaunchHelpBean().setToZipCodeFlow(true));
    }

    private void toNormalFlow() {
        launchFlowData.postValue(new LaunchHelpBean().setToNormalFlow(true));
    }

    public void execLaunchFlow(String source, String url) {
        getSessionId(source, url);
    }

    /**
     * 获取session token
     */
    public void getSessionId(String source, String url) {
        String oldSessionId = SessionService.get().getSessionId();
        SessionTokenHelper.getSessionToken(source, url, IServiceResult.asDisposable(this, (sessionId, failureBean) -> {
            if (sessionId != null) {
                sessionTokenChanged = !sessionId.equals(oldSessionId);
            }
            getToken();
        }));
    }

    private void getToken() {
        String oldToken = SessionService.get().getToken();
        RequestParams requestParams = new RequestParams();
        requestParams.putNonNull("auth_token_channel", VariantConfig.AUTH_TOKEN_CHANNEL);
        boolean forceFetch = oldToken == null;
        SessionService.get().fetchToken(forceFetch, requestParams.get(), IServiceResult.asDisposable(this, (token, failureBean) -> {
            onTokenAchieve();
        }));
    }

    private void onTokenAchieve() {
        if (AccountManager.get().isGuided()) {
            //已经引导，获取pre order数据
            onNormalFlow();
        } else {//引导流程
            onGuideFlow();
        }

        ConfigManager.get().fetchServiceConfig();
        ConfigManager.get().fetchDynamicConfig();
        LogFileManager.get().uploadByRetrieval(0);
        PopupCenterManager.get().getPopupConfig();
        TimerBannerManager.get().refreshTimerBanner(sessionTokenChanged);
        RecommendHelper.initRecommend();
        PushHelper.updateAccountInfoIfNeed();
        ConfigService.get().removeExperimentsExcept(ExperimentManager.VALID_IDS);
        UserPersonalizationManager.onAccountInfoAtLaunch(App.application.getApplicationContext());
        SearchV2Manager.get().fetchConfig();
        ConfigService.get().fetchExperiment(ExperimentManager.ID_HOME_PUSH_NEW_CATEGORY);
    }

    public void getStoreInfo() {
        StoreManager.get().fetchStore(false, OrderManager.get().getZipCode(), new ResponseObserver<ResponseBean<StoreInfo>>() {
            @Override
            public void onResponse(ResponseBean<StoreInfo> response) {
                StoreManager.get().setDisableRefreshNext(true);
            }

            @Override
            public void onFinish() {
                super.onFinish();
                toNormalFlow();
            }
        });
    }

    private void onGuideFlow() {
        if (LanguageManager.get().isEnglishDefault()) {
            getLoader().getHttpService()
                    .getSupportLanguage(VariantConfig.AUTH_TOKEN_CHANNEL)
                    .compose(ResponseTransformer.scheduler(this))
                    .subscribe(new ResponseObserver<ResponseBean<LanguageBean>>() {
                        @Override
                        public void onResponse(ResponseBean<LanguageBean> response) {
                            toGuideFlow(response.getData());
                        }

                        @Override
                        public void onError(FailureBean failure) {
                            super.onError(failure);
                            toGuideFlow(null);
                        }
                    });
        } else {
            toGuideFlow(null);
        }
    }

    private void onNormalFlow() {
        Observable<ResponseBean<SimplePreOrderBean>> preOrderObservable = AccountHelper.obtainPreorderData(this);
        Observable<ResponseBean<AccountBean>> accountInfoObservable = AccountHelper.obtainAccountData(this);
        Observable.mergeDelayError(preOrderObservable, accountInfoObservable)
                .subscribe(new ResponseObserver<ResponseBean<?>>() {

                    private boolean lackOfOrder = false;

                    @Override
                    public void onResponse(ResponseBean<?> response) {
                        Object responseData = response.getData();
                        if (responseData instanceof SimplePreOrderBean) {
                            OrderManager.get().setSimpleOrderData((SimplePreOrderBean) responseData);
                        } else if (responseData instanceof AccountBean) {
                            AccountManager.get().setAccountInfo((AccountBean) responseData);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (OrderManager.get().lackOfPreOrder(failure.getMessageId())) {
                            lackOfOrder = true;
                        }
                    }

                    @Override
                    public void onFinish() {
                        if (AccountManager.get().isLogin()) {
                            AccountBean accountInfo = AccountManager.get().getAccountInfo();
                            if (accountInfo != null && accountInfo.language != null && !accountInfo.language.equalsIgnoreCase(LanguageManager.get().getLanguage())) {
                                languageData.postValue(accountInfo.language);
                            }
                        }
                        if (lackOfOrder || OrderManager.get().isLackOfOrder()) {
                            //缺少zipcode
                            if (lackOfOrder) {
                                SimplePreOrderBean bean = AccountHelper.readPreOrderByCache();
                                if (bean != null && bean.zipcode != null) {
                                    createPreOrder(bean.zipcode);
                                    return;
                                }
                            }
                            toZipCodeFlow();
                        } else {
                            getStoreInfo();
                        }
                    }
                });
    }

    public void createPreOrder(String zipcode) {
        getLoader().getHttpService()
                .createPreOrder(new RequestParams().put("zipcode", zipcode).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<SimplePreOrderBean>>() {

                    @Override
                    public void onResponse(ResponseBean<SimplePreOrderBean> response) {
                        OrderManager.get().setSimpleOrderData(response.getData());
                        getStoreInfo();
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        toZipCodeFlow();
                    }
                });

    }

}
