package com.sayweee.weee.module.launch;

import androidx.annotation.Nullable;

import com.auth0.android.jwt.JWT;
import com.sayweee.service.session.sessionid.bean.SessionToken;
import com.sayweee.service.session.token.bean.AccessToken;
import com.sayweee.storage.WStore;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.service.rx.SimpleCompletableObserver;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.concurrent.TimeUnit;

import io.reactivex.Completable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public final class ServiceMigration {

    private static final int CURRENT_VERSION = 1;
    private static final String KEY_SERVICE_VERSION = "service_version";

    private ServiceMigration() {

    }

    public static void migrate(final Runnable onComplete) {
        int version = WStore.mmkv(MMKVManager.ID_CONFIG).getInt(KEY_SERVICE_VERSION, 0);
        if (version >= CURRENT_VERSION) {
            onComplete.run();
            return;
        }
        Completable.fromRunnable(() -> rxMigrate(version))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SimpleCompletableObserver(onComplete));
    }

    private static void rxMigrate(int version) {
        while (version < CURRENT_VERSION) {
            Migration migration = createMigration(version, version + 1);
            if (migration != null) {
                migration.migrate();
            }
            version++;
        }
        WStore.mmkv(MMKVManager.ID_CONFIG).edit().putInt(KEY_SERVICE_VERSION, CURRENT_VERSION);
    }

    @Nullable
    private static Migration createMigration(int version, int targetVersion) {
        if (version == 0 && targetVersion == 1) {
            return new Migration_0_1();
        }
        return null;
    }

    private interface Migration {
        void migrate();
    }

    private static class Migration_0_1 implements Migration {

        private static final String ID_SESSION_CONFIG = "session-config";

        @Override
        public void migrate() {
            // migrateUa(); // cannot migrate
            migrateSessionToken();
            migrateAccessToken();
            // migrateLanguage(); // unnecessary
            // migrateStore(); // unnecessary
            // migrateDeviceId(); // unnecessary
            // migrateFingerprint(); // cannot migrate
            // migrateWafToken(); // unnecessary
            // migratePreOrder(); // unnecessary
        }

        private void migrateSessionToken() {
            String sessionToken = WStore.sp("account_config").getString("weee_session_token", null);
            long lastUpdatedTimestamp = WStore.sp("account_config").getLong("session_token_timestamp", 0L);
            if (!EmptyUtils.isEmpty(sessionToken)) {
                SessionToken bean = new SessionToken();
                bean.sessionToken = sessionToken;
                bean.lastUpdatedTimestamp = lastUpdatedTimestamp;
                WStore.mmkv(ID_SESSION_CONFIG)
                        .edit()
                        .putJson("session_token", bean, SessionToken.class)
                        .apply();
            }
        }

        private void migrateAccessToken() {
            String token = WStore.sp("account_config").getString("token", null);
            if (!EmptyUtils.isEmpty(token)) {
                AccessToken bean = new AccessToken();
                bean.token = token;
                if (parseToken(bean)) {
                    WStore.mmkv(ID_SESSION_CONFIG)
                            .edit()
                            .putJson("access_token", bean, AccessToken.class)
                            .apply();
                }
            }
        }

        private boolean parseToken(AccessToken accessToken) {
            try {
                JWT jwt = new JWT(accessToken.token);
                Boolean loginClaim = jwt.getClaim("is_login").asBoolean();
                Integer expClaim = jwt.getClaim("exp").asInt();
                int exp = expClaim != null ? expClaim : 0;
                boolean isLogin = loginClaim != null && loginClaim;
                accessToken.expireTimestamp = TimeUnit.SECONDS.toMillis(exp);
                accessToken.isLogin = isLogin;
                return true;
            } catch (Exception ignored) {
                return false;
            }
        }
    }

}
