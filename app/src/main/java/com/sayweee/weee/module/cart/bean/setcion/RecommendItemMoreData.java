package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.module.base.adapter.AdapterDataType;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class RecommendItemMoreData implements AdapterDataType, Serializable {

    public Map<String, Object> element;
    public Map<String, Object> ctx;
    public String link_desc, link_url;
    public boolean canAdd;
    public boolean limitReached;

    public RecommendItemMoreData(String link_desc, String link_url) {
        this.link_desc = link_desc;
        this.link_url = link_url;
    }

    public RecommendItemMoreData setTrackingInfo(Map<String, Object> element, Map<String, Object> ctx) {
        if (element != null) {
            this.element = new HashMap<>(element);
        }
        if (ctx != null) {
            this.ctx = new HashMap<>(ctx);
        }
        return this;
    }

    @Override
    public int getType() {
        return CartSectionType.TYPE_RECOMMEND_ITEM_MORE;
    }

    public String getLink() {
        return link_url != null ? link_url : "";
    }
}
