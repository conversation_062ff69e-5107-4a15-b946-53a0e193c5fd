package com.sayweee.weee.module.home.provider.category;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;

public class CategoryCapsuleProvider extends CategoryAutoScrollProvider {

    public CategoryCapsuleProvider(@NonNull RecyclerViewScrollStatePersist scrollStatePersist) {
        super(scrollStatePersist);
    }

    @Override
    public int getItemType() {
        return CmsItemType.CATEGORIES_CAPSULE;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_cms_category_capsule;
    }

    @Override
    protected CmsCategoryBaseAdapter createAdapter() {
        return new CmsCategoryCapsuleAdapter();
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration() {
        return new CategoryItemDecoration();
    }

    @Override
    protected void updateAdapter(RecyclerView recyclerView, CmsCategoryData item) {
        RecyclerView.Adapter<?> a = recyclerView.getAdapter();
        if (a instanceof CmsCategoryBaseAdapter) {
            CmsCategoryBaseAdapter adapter = (CmsCategoryBaseAdapter) a;
            String modNm = item.getEventKey();
            adapter.setModInfo(modNm, item.position);
            adapter.setAdapterData(item.t.category_list, item.getDisplayStyle(), item.t.see_all_img_url);
        }
    }
}
