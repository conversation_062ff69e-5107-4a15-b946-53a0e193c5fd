package com.sayweee.weee.module.home.theme;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.util.Property;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.utils.CollectionUtils;

import java.util.List;

public class BannerThemeHelper {

    private static final int DEFAULT_SOLID_COLOR = Color.WHITE;

    private static final List<Integer> IGNORE_ITEM_TYPES = CollectionUtils.arrayListOf(
            CmsItemType.BLANK,
            CmsItemType.LINE,
            CmsItemType.DIVIDER,
            CmsItemType.BLOCK,
            CmsItemType.MARGIN
    );

    private static final List<Integer> VALID_ITEM_TYPES = CollectionUtils.arrayListOf(
            CmsItemType.SEARCH_BAR,
            CmsItemType.CAROUSEL_BANNER,
            CmsItemType.CATEGORIES,
            CmsItemType.CATEGORIES_CAPSULE,
            CmsItemType.CATEGORIES_BAR
    );

    private final Drawable defaultBackgroundDrawable;

    private final BannerThemeDrawable headerDrawable;
    private final BannerThemeDrawable contentDrawable;

    private int currentColorInt = DEFAULT_SOLID_COLOR;
    private int runningColorInt = DEFAULT_SOLID_COLOR;
    private final Property<BannerThemeHelper, Integer> solidColorProperty =
            new Property<BannerThemeHelper, Integer>(Integer.class, "solidColor") {
                @Override
                public Integer get(BannerThemeHelper object) {
                    return object.runningColorInt;
                }

                @Override
                public void set(BannerThemeHelper object, Integer value) {
                    object.setSolidColorInternal(value);
                }
            };
    private Animator lastAnimator;

    private boolean isEnabled = false;

    public BannerThemeHelper(Context context) {
        defaultBackgroundDrawable = new ColorDrawable(DEFAULT_SOLID_COLOR);
        headerDrawable = new BannerThemeDrawable(DEFAULT_SOLID_COLOR);
        contentDrawable = new BannerThemeDrawable(DEFAULT_SOLID_COLOR);
    }

    public void setHeight(int headerHeight, int contentHeight) {
        int totalHeight = headerHeight + contentHeight;
        if (totalHeight > 0) {
            float ratio = (headerHeight * 1f) / totalHeight;
            headerDrawable.setGradientFractions(0f, ratio);
            contentDrawable.setGradientFractions(ratio, 1f);
        }
    }

    @ColorInt
    public int getSolidColor() {
        return currentColorInt;
    }

    public boolean setSolidColor(@ColorInt int colorInt) {
        int targetColor = mapColor(colorInt);
        if (currentColorInt == targetColor) {
            return false;
        }
        currentColorInt = targetColor;
        setSolidColorInternal(targetColor);
        return true;
    }

    @SuppressWarnings("UnusedReturnValue")
    public boolean setSolidColorAnimate(@ColorInt int colorInt) {
        int targetColor = mapColor(colorInt);
        if (currentColorInt == targetColor) {
            return false;
        }
        currentColorInt = targetColor;
        if (lastAnimator != null) {
            lastAnimator.cancel();
        }
        ObjectAnimator animator = ObjectAnimator.ofArgb(this, solidColorProperty, runningColorInt, targetColor);
        animator.setDuration(200L);
        lastAnimator = animator;
        animator.start();
        return true;
    }

    private int mapColor(@ColorInt int colorInt) {
        return isEnabled ? colorInt : DEFAULT_SOLID_COLOR;
    }

    private void setSolidColorInternal(@ColorInt int colorInt) {
        runningColorInt = colorInt;
        headerDrawable.setSolidColor(colorInt);
        contentDrawable.setSolidColor(colorInt);
    }

    public void setEnabled(boolean isEnabled) {
        this.isEnabled = isEnabled;
        if (!isEnabled) { // turn off
            reset();
        }
    }

    private void reset() {
        currentColorInt = DEFAULT_SOLID_COLOR;
        runningColorInt = DEFAULT_SOLID_COLOR;
        headerDrawable.reset();
        contentDrawable.reset();
    }

    @NonNull
    public Drawable getHeaderDrawable() {
        return headerDrawable.getDrawable();
    }

    @NonNull
    public Drawable getContentDrawable() {
        return contentDrawable.getDrawable();
    }

    public Drawable getDefaultHeaderDrawable() {
        return defaultBackgroundDrawable;
    }

    public boolean isValid(@Nullable List<Integer> list) {
        List<Integer> itemTypes = CollectionUtils.orEmptyList(list);
        int bannerPosition = CollectionUtils.indexOf(itemTypes, CmsItemType.CAROUSEL_BANNER);
        if (bannerPosition > 0) {
            itemTypes = CollectionUtils.subList(itemTypes, 0, bannerPosition + 1);
            itemTypes.removeAll(IGNORE_ITEM_TYPES);
            if (CollectionUtils.none(itemTypes, it -> !VALID_ITEM_TYPES.contains(it))) {
                bannerPosition = CollectionUtils.indexOf(itemTypes, CmsItemType.CAROUSEL_BANNER);
                return bannerPosition >= 0 && bannerPosition < 5;
            }
        }
        return false;
    }
}
