package com.sayweee.weee.module.post.profile;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.bean.AccountBean;
import com.sayweee.weee.module.account.service.AccountHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.edit.bean.AdapterDraftData;
import com.sayweee.weee.module.post.edit.bean.AdapterUploadData;
import com.sayweee.weee.module.post.edit.bean.NotifyBean;
import com.sayweee.weee.module.post.edit.service.PostDraftManager;
import com.sayweee.weee.module.post.edit.service.PostUploadManager;
import com.sayweee.weee.module.post.edit.service.bean.PostDraftData;
import com.sayweee.weee.module.post.edit.service.bean.PostUploadData;
import com.sayweee.weee.module.post.profile.adapter.UserFlowersOutAdapter;
import com.sayweee.weee.module.post.profile.bean.FollowListData;
import com.sayweee.weee.module.post.profile.bean.FollowSearchData;
import com.sayweee.weee.module.post.profile.bean.ProfileFollowerData;
import com.sayweee.weee.module.post.profile.bean.ProfileInformationData;
import com.sayweee.weee.module.post.search.bean.FollowStatusBean;
import com.sayweee.weee.module.post.service.IPostStatus;
import com.sayweee.weee.module.post.service.PostApi;
import com.sayweee.weee.module.post.shared.CmtFailureData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import okhttp3.RequestBody;

/**
 * Author:  winds
 * Date:    2021/8/10.
 * Desc:
 */
public class ProfileViewModel extends BaseViewModel<BaseLoaderModel<PostApi>> {

    public MutableLiveData<ProfileInformationData> informationDataMutableLiveData = new MutableLiveData<>();
    public MutableLiveData<ShareBean> shareData = new MutableLiveData<>();
    public MutableLiveData<PostCategoryBean> postCategoryBean = new MutableLiveData<>();
    public MutableLiveData<ProfileFollowerData> userFollowersBean = new MutableLiveData<>();
    public MutableLiveData<NotifyBean> inReviewBean = new MutableLiveData<>();
    public MutableLiveData<List<PostCategoryBean.ListBean>> editPostDataMutableLiveData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterDataType>> followAdapterData = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> postDraftData = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> postInReviewData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterDataType>> postInReviewAppendData = new MutableLiveData<>();
    public MutableLiveData<Integer> updateReviewTotal = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> postAdapterData = new MutableLiveData<>();

    public ProfileViewModel(@NonNull Application application) {
        super(application);
    }

    public void initProfileInformation() {
        getLoader().getHttpService().getMyInfo()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ProfileInformationData>>() {
                    @Override
                    public void onResponse(ResponseBean<ProfileInformationData> response) {
                        //更新account info信息
                        AccountBean accountInfo = AccountManager.get().getAccountInfo();
                        if (accountInfo != null && response.getData().user_info != null) {
                            accountInfo.head_img_url = response.getData().user_info.head_img_url;
                            accountInfo.alias = response.getData().user_info.alias;
                            accountInfo.user_id = response.getData().user_info.user_id;
                        }
                        informationDataMutableLiveData.postValue(response.object);
                    }
                });

    }

    public void initOtherProfileInformation(String userId, String mid) {
        getLoader().getHttpService().getOtherInfo(userId, mid)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ProfileInformationData>>() {
                    @Override
                    public void onResponse(ResponseBean<ProfileInformationData> response) {
                        informationDataMutableLiveData.postValue(response.object);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (failure != null && AccountHelper.MSG_ID_C90004.equals(failure.getMessageId())) {
                            SharedViewModel.get().loginStatusData.postValue(false);
                        }
                    }
                });
    }

    public void getPostInReviewData(boolean isSilent, boolean loadNewData, String type, String status, String start_id) {
        getLoader().getHttpService().getMyPost(new RequestParams().put("type", type)
                        .put("status", status)
                        .putNonNull("start_id", start_id)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<PostCategoryBean>>() {

                    @Override
                    public void onResponse(ResponseBean<PostCategoryBean> response) {
                        if (loadNewData) {
//                            setLoadingStatus(true);
                            packetAllUploadingData(response.getData().list, response.getData().total);
                        } else {
                            ArrayList<AdapterDataType> list = new ArrayList<>();
                            if (!EmptyUtils.isEmpty(response.getData().list)) {
                                list.addAll(response.getData().list);
                            }
                            postInReviewAppendData.postValue(list);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (loadNewData) {
//                            setLoadingStatus(true);
                            packetAllUploadingData(new ArrayList<>(), 0);
                        }
                    }

                });
    }

    public void getMyPostList(boolean isSilent, String type, String status, String start_id) {
        getLoader().getHttpService().getMyPost(new RequestParams().put("type", type)
                        .put("status", status)
                        .putNonNull("start_id", start_id)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<PostCategoryBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PostCategoryBean> response) {
                        postCategoryBean.postValue(response.object);
                        if ("P".equalsIgnoreCase(status)) {
                            updateReviewTotal.postValue(response.getData().total);
                        }
                    }

                });
    }

    public void getOtherPostList(String uid, String type, String status, String start_id) {
        getLoader().getHttpService().getUserPost(uid, new RequestParams().put("type", type)
                        .put("status", status)
                        .putNonNull("start_id", start_id)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<PostCategoryBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PostCategoryBean> response) {
                        postCategoryBean.postValue(response.object);
                    }
                });
    }

    public void getMyLikes(String start_id) {
        getLoader().getHttpService().getMyLikes(new RequestParams()
                        .putNonNull("start_id", start_id)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<PostCategoryBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PostCategoryBean> response) {
                        postCategoryBean.postValue(response.object);
                    }
                });
    }

    public void getMyCommented(String start_id) {
        getLoader().getHttpService().getMyCommented(start_id)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<PostCategoryBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PostCategoryBean> response) {
                        postCategoryBean.postValue(response.object);
                    }
                });
    }

    public void getProfileShare(String uid) {
        getLoader().getHttpService().getProfileShare(uid)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ShareBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ShareBean> response) {
                        shareData.postValue(response.object);
                    }
                });
    }

    public void postFollow(String uid, String status, String followStatus) {
        if (EmptyUtils.isEmpty(status)) {
            return;
        }
        SharedViewModel.get().saveFollowStatusData(uid, "A".equalsIgnoreCase(status) ? IPostStatus.STATUS_FOLLOWED : IPostStatus.STATUS_UNFOLLOWED);
        getLoader().getHttpService().postFollow(new RequestParams().put("user_id", uid)
                        .put("status", status)
                        .create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        FollowStatusBean bean = new FollowStatusBean();
                        bean.uid = uid;
                        bean.followStatus = followStatus;
                        bean.isSuccess = response.result;
                        SharedViewModel.get().followUser(bean);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        CmtSharedViewModel.get().postSpamFailureData(new CmtFailureData(
                                failure, CmtFailureData.TYPE_SPAM_FOLLOW));
                    }
                });
    }

    public void getUserFollowersList(String uid, String keyword, int page) {
        getLoader().getHttpService().getUserFollowers(uid, new RequestParams().putNonNull("keyword", keyword)
                        .put("page", page)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ProfileFollowerData>>() {
                    @Override
                    public void onResponse(ResponseBean<ProfileFollowerData> response) {
                        userFollowersBean.postValue(response.object);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (failure != null && AccountHelper.MSG_ID_C90004.equals(failure.getMessageId())) {
                            SharedViewModel.get().loginStatusData.postValue(false);
                        }
                    }
                });
    }

    public void getUserFollowingList(String uid, String keyword, int page) {
        getLoader().getHttpService().getUserFollowing(uid, new RequestParams().putNonNull("keyword", keyword)
                        .put("page", page)
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ProfileFollowerData>>() {
                    @Override
                    public void onResponse(ResponseBean<ProfileFollowerData> response) {
                        userFollowersBean.postValue(response.object);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (failure != null && AccountHelper.MSG_ID_C90004.equals(failure.getMessageId())) {
                            SharedViewModel.get().loginStatusData.postValue(false);
                        }
                    }
                });
    }

    public void getInReviewData() {
        getLoader().getHttpService().getInReviewData(new RequestParams()
                        .put("source", "in_review")
                        .get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NotifyBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NotifyBean> response) {
                        inReviewBean.postValue(response.object);
                    }
                });
    }

    public void initFollowData(String keyWord, int num, ProfileFollowerData data, boolean isFollowers) {
        List<AdapterDataType> list = new ArrayList<>();
        if (!EmptyUtils.isEmpty(data.list)) {
            list.add(new FollowSearchData(UserFlowersOutAdapter.TYPE_SEARCH, keyWord, num, isFollowers));
        }
        list.add(new FollowListData(UserFlowersOutAdapter.TYPE_RV, data.list, keyWord, isFollowers));
        followAdapterData.postValue(list);
    }

    public void getDraftData() {
        Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
                    @Override
                    public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                        List<AdapterDataType> list = new ArrayList<>();
                        List<PostDraftData> cache = PostDraftManager.get().getAllDraftData();
                        if (!EmptyUtils.isEmpty(cache)) {
                            for (PostDraftData item : cache) {
                                if (item != null) {
                                    list.add(new AdapterDraftData(item));
                                }
                            }
                        }
                        emitter.onNext(list);
                        emitter.onComplete();
                    }
                })
                .compose(DisposableTransformer.scheduler(this))
                .subscribe(new SimpleObserver<List<AdapterDataType>>() {
                    @Override
                    public void onNext(@NonNull List<AdapterDataType> list) {
                        postDraftData.setValue(list);
                    }
                });
    }

    public void removeDraftData(List<AdapterDataType> list, long draftId) {
        if (!EmptyUtils.isEmpty(list)) {
            Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
                        @Override
                        public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                            AdapterDataType target = null;
                            for (AdapterDataType item : list) {
                                if (item instanceof AdapterDraftData && ((AdapterDraftData) item).getDraftId() == draftId) {
                                    target = item;
                                    break;
                                }
                            }
                            PostDraftManager.get().removePostDraftData(draftId);
                            if (target != null) {
                                list.remove(target);
                            }
                            emitter.onNext(list);
                            emitter.onComplete();
                        }
                    })
                    .compose(DisposableTransformer.scheduler(this))
                    .subscribe(new SimpleObserver<List<AdapterDataType>>() {
                        @Override
                        public void onNext(@NonNull List<AdapterDataType> list) {
                            postDraftData.setValue(list);
                        }
                    });
        }
    }

    public void packetAllUploadingData(List<PostCategoryBean.ListBean> cacheData, int total) {
        Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
                    @Override
                    public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                        List<PostUploadData> uploadData = PostUploadManager.get().getAllUploadData();
                        ArrayList<AdapterDataType> targetList = new ArrayList<>();
                        int uploadCount = 0;
                        if (!EmptyUtils.isEmpty(uploadData)) {
                            for (PostUploadData item : uploadData) {
                                if (item.getUploadId() != 0) {
                                    AdapterUploadData aud = new AdapterUploadData(item);
                                    targetList.add(aud);
                                    if (aud.isStatusSuccess()) {
                                        uploadCount += 1;
                                    }
                                }
                            }
                        }
                        int count = total + uploadCount;
                        CmtSharedViewModel.get().postPublishedTotal.postValue(count);
                        emitter.onNext(targetList);
                        emitter.onComplete();
                    }
                })
                .compose(DisposableTransformer.scheduler(this))
                .subscribe(new SimpleObserver<List<AdapterDataType>>() {

                    @Override
                    public void onNext(@NonNull List<AdapterDataType> list) {
                        if (!EmptyUtils.isEmpty(cacheData)) {
                            list.addAll(cacheData);
                        }
                        postInReviewData.setValue(list);
//                        setLoadingStatus(false);
                    }
                });
    }

    public void removeUploadData(List<AdapterDataType> list, long uploadId) {
        if (!EmptyUtils.isEmpty(list)) {
            Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
                        @Override
                        public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                            AdapterDataType target = null;
                            for (AdapterDataType item : list) {
                                if (item instanceof AdapterUploadData && ((AdapterUploadData) item).getUploadId() == uploadId) {
                                    target = item;
                                    break;
                                }
                            }
                            PostUploadManager.get().removePostUploadData(uploadId);
                            if (target != null) {
                                list.remove(target);
                                CmtSharedViewModel.get().postPublishedTotal(((AdapterUploadData) target).getData(), true);
                            }
                            emitter.onNext(list);
                            emitter.onComplete();
                        }
                    })
                    .compose(DisposableTransformer.scheduler(this))
                    .subscribe(new SimpleObserver<List<AdapterDataType>>() {
                        @Override
                        public void onNext(@NonNull List<AdapterDataType> list) {
                            postInReviewData.setValue(list);
                        }
                    });
        }
    }

    public void postFollowSeller(String vendorId, String status) {
        RequestBody request = new RequestParams()
                .put("seller_id", vendorId)
                .put("status", status)
                .create();
        getLoader().getHttpService().postFollowSeller(request).compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<SimpleResponseBean>() {

                    @Override
                    public void onResponse(SimpleResponseBean response) {

                    }

                    @Override
                    public void onError(FailureBean failure) {

                    }
                });
    }
}