package com.sayweee.weee.module.checkout;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.checkout.service.SectionAlcoholAgreementViewModel;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.page.PageFragment;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

import java.io.Serializable;

public class SectionAlcoholAgreementActivity extends WrapperMvvmActivity<SectionAlcoholAgreementViewModel> {

    private String cartDomain;
    private boolean isBeforeUpsell;
    private double amount;
    private UpSellBean upSellBean;

    public static Intent getIntent(Context context, String cartDomain, int alcoholAgreementType) {
        return getIntent(context, cartDomain, alcoholAgreementType, null);
    }

    public static Intent getIntent(Context context, String cartDomain, int alcoholAgreementType, String nextUrl) {
        return new Intent(context, SectionAlcoholAgreementActivity.class)
                .putExtra("cart_domain", cartDomain)
                .putExtra("alcohol_agreement_type", alcoholAgreementType)
                .putExtra("next_url", nextUrl);
    }

    public static Intent getIntentBeforeUpsell(Context context, UpSellBean bean, double amount) {
        return new Intent(context, SectionAlcoholAgreementActivity.class).putExtra("bean", bean).putExtra("amount", amount);
    }

    private int getExtraAlcoholAgreementType() {
        return getIntent() != null
                ? getIntent().getIntExtra("alcohol_agreement_type", Constants.AlcoholAgreementType.GROCERY)
                : Constants.AlcoholAgreementType.GROCERY;
    }

    @Nullable
    private String getExtraNextUrl() {
        return getIntent() != null ? getIntent().getStringExtra("next_url") : null;
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_webview;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        Intent intent = getIntent();
        cartDomain = intent.getStringExtra("cart_domain");
        Serializable serializable = intent.getSerializableExtra("bean");
        if (serializable instanceof UpSellBean) {
            isBeforeUpsell = true;
            upSellBean = (UpSellBean) serializable;
        }
        amount = intent.getDoubleExtra("amount", 0);
        int page;
        if (Constants.AlcoholAgreementType.SELLER == getExtraAlcoholAgreementType()) {
            page = PageFragment.PAGE_ALCOHOL_AGREEMENT_SELLER;
        } else {
            page = PageFragment.PAGE_ALCOHOL_AGREEMENT;
        }

        getSupportFragmentManager()
                .beginTransaction()
                .add(R.id.layout_content, PageFragment.newInstance(page))
                .commitAllowingStateLoss();
    }

    @Override
    public void loadData() {

    }

    @Override
    public void attachModel() {
        viewModel.recordAlcoholResultData
                .observe(this, new Observer<Boolean>() {
                    @Override
                    public void onChanged(Boolean aBoolean) {
                        OrderManager.get().setAlcoholAgreementAgreed(true);
                        onAlcoholChecked();
                    }
                });
    }

    public void execAlcoholCheck(@Nullable String type) {
        if (viewModel != null) {
            viewModel.recordAlcoholAgreement(type);
        }
    }

    private void onAlcoholChecked() {
        if (isBeforeUpsell) {
            startActivity(SectionUpsellActivity.getIntent(activity, upSellBean, amount, false));
        } else {
            String nextUrl = getExtraNextUrl();
            if (!EmptyUtils.isEmpty(nextUrl)) {
                startActivity(WebViewActivity.getIntent(activity, nextUrl));
            } else {
                startActivity(CheckoutSectionActivity.getIntent(activity, cartDomain, null));
            }
        }
        finish();
    }
}
