package com.sayweee.weee.module.home.adapter;

import android.app.Activity;
import android.content.Context;
import android.util.SparseIntArray;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewDisplayStrategy;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.shapeable.RoundImageView;
import com.sayweee.widget.tagflow.TagFlowLayout;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;

public class BigProductItemAdapter extends ProductItemMoreAdapter {

    private int productItemWidth = 0;
    protected ProductViewDisplayStrategy displayStrategy;

    public BigProductItemAdapter() {
        super(new ArrayList<>());
        Activity context = LifecycleProvider.get().getTopActivity();
        if (context != null) {
            productItemWidth = ScreenUtils.getScreenWidth(context) - CommonTools.dp2px(80);
        }
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        if (layoutParams instanceof RecyclerView.LayoutParams) {
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
            int position = params.getViewLayoutPosition();
            params.leftMargin = CommonTools.dp2px(position == 0 ? 20 : 8);
            params.rightMargin = CommonTools.dp2px(position == getItemCount() - 1 ? 20 : 0);
            params.width = productItemWidth;
        }
    }

    @Override
    protected int getDisplayStyle() {
        return ProductView.STYLE_ITEM_NORMAL;
    }

    @Override
    protected void convertProduct(@NonNull AdapterViewHolder helper, ProductBean item) {
        ProductView productView = helper.getView(R.id.layout_product_view);
        productView.setDisplayStrategy(getDisplayStrategy());
        super.convertProduct(helper, item);

        // Border
        RoundImageView imageView = helper.getView(R.id.iv_icon);
        imageView.setStrokeWidth(CommonTools.dp2px(1));
        imageView.setStrokeColor(ContextCompat.getColor(imageView.getContext(), R.color.color_surface_1_fg_hairline_idle));

        // Limit tag to one line
        if (helper.getView(R.id.layout_tags) instanceof TagFlowLayout) {
            TagFlowLayout tfl = helper.getView(R.id.layout_tags);
            tfl.setMaxLines(1);
        }
    }

    @Override
    protected int getProductItemWidth(Context context, int displayStyle) {
        return productItemWidth;
    }

    private ProductViewDisplayStrategy getDisplayStrategy() {
        if (displayStrategy == null) {
            return new BigProductViewStrategy(getDisplayStyle());
        }
        return displayStrategy;
    }

    private static class BigProductViewStrategy extends ProductViewDisplayStrategy {

        private final SparseIntArray VISIBLE_ITEMS = CollectionUtils.intArrayOf(new int[]{
                DYNAMIC_ITEM_TAG_LIST,
                DYNAMIC_ITEM_TOP_X,
        });

        public BigProductViewStrategy(@ProductView.DisplayStyle int displayStyle) {
            super(displayStyle);
        }

        @Override
        public SparseIntArray getVisibleItems() {
            return VISIBLE_ITEMS;
        }

        @Override
        public int getProductNameMaxLines() {
            return 2;
        }

        @Override
        public boolean shouldDisplayBrandName(ProductBean bean) {
            return false;
        }
    }
}
