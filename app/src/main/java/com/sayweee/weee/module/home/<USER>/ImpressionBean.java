package com.sayweee.weee.module.home.bean;

import com.sayweee.analytics.WeeeAnalyticsIml;
import com.sayweee.weee.utils.JsonUtils;

import java.util.Map;
import java.util.Objects;

/**
 * Author:  winds
 * Date:    2021/6/11.
 * Desc:
 */
public class ImpressionBean {

    public String eventName;
    public Map<String, Object> params;

    private String eventKey;

    private String _pageKey;

    public ImpressionBean(String eventName, Map<String, Object> params, String eventKey) {
        this.eventName = eventName;
        this.params = params;
        this.eventKey = eventKey;
        this._pageKey = WeeeAnalyticsIml.get().getCleanPageKey();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImpressionBean)) return false;
        ImpressionBean that = (ImpressionBean) o;
        return Objects.equals(eventName, that.eventName)
                && Objects.equals(eventKey, that.eventKey)
                && Objects.equals(JsonUtils.toJSONString(params), JsonUtils.toJSONString(that.params));
    }

    @Override
    public int hashCode() {
        return Objects.hash(eventName, eventKey, params);
    }

    public String getKey() {
        return eventKey;
    }

    public String getPageKey() {
        return _pageKey;
    }

}
