package com.sayweee.weee.module.post.profile;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public final class ProfileIntentCreator {

    private static final Creator C = new ProfileIntentCreatorImpl();

    @NonNull
    public static Intent getIntentOnClickMineProfileAvatar(Context context) {
        return C.getIntentOnClickMineProfileAvatar(context);
    }

    @Nullable
    public static Intent getIntentOnClickReviewAvatar(Context context, String source, String uid) {
        return C.getIntentOnClickReviewAvatar(context, source, uid);
    }

    interface Creator {

        @NonNull
        Intent getIntentOnClickMineProfileAvatar(Context context);

        @Nullable
        Intent getIntentOnClickReviewAvatar(Context context, String source, String uid);

    }

}
