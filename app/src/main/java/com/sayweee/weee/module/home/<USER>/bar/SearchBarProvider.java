package com.sayweee.weee.module.home.provider.bar;

import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.SearchTextSwitcher;

import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/6/1.
 * Desc:
 */
public class SearchBarProvider extends SimpleSectionProvider<CmsSearchBarData, AdapterViewHolder> {

    public static final String FROM_PAGE_GLOBAL = "mkpl_global_search";

    @Nullable
    protected String fromPage;

    public SearchBarProvider setFromPage(@Nullable String fromPage) {
        this.fromPage = fromPage;
        return this;
    }

    @Override
    public int getItemType() {
        return CmsItemType.SEARCH_BAR;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_search_bar;
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsSearchBarData item) {
        String color = item.bgColor;
        if (EmptyUtils.isEmpty(color)) {
            // 无主题，透明
            ViewTools.applyViewBgColor(Color.TRANSPARENT, helper.getView(R.id.layout_bg));
            helper.setBackgroundRes(R.id.layout_search_inner, R.drawable.shape_bg_search_bar_enki);
        } else {
            ViewTools.applyViewBgColor(color, helper.getView(R.id.layout_bg));
            helper.setBackgroundRes(R.id.layout_search_inner, R.drawable.shape_bg_search_bar_enki_no_stroke);
        }
        List<String> keywords = item.getKeywords();
        SearchTextSwitcher tvSearchTips = helper.getView(R.id.tv_search_tips);
        View tvSearch = helper.getView(R.id.tv_search_btn);
        TextView tvHint = helper.getView(R.id.tv_search_hint);
        tvSearchTips.stopSwitch(true);
        if (!EmptyUtils.isEmpty(keywords)) {
            tvSearchTips.setVisibility(View.VISIBLE);
            tvHint.setVisibility(View.GONE);
            tvSearchTips.bindData(keywords);
            if (keywords.size() == 1) {
                tvSearchTips.setCurrentText(keywords.get(0));
            } else {
                tvSearchTips.startSwitch();
            }
            tvSearch.setVisibility(View.VISIBLE);
        } else {
            tvSearchTips.setVisibility(View.GONE);
            tvSearch.setVisibility(View.GONE);
            tvHint.setVisibility(View.VISIBLE);
            tvHint.setText(item.property.tips);
        }
        helper.setOnViewClickListener(R.id.layout_search_inner, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (tvHint.getVisibility() == View.VISIBLE) {
                    trackEagleClickAction(helper, item, null);
                    if (FROM_PAGE_GLOBAL.equals(fromPage)) {
                        context.startActivity(SearchPanelActivity.getIntentByGlobal(context, null, null));
                    } else {
                        context.startActivity(SearchPanelActivity.getIntent(context));
                    }
                }
            }
        });
        helper.setOnViewClickListener(R.id.tv_search_tips, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                trackEagleClickAction(helper, item, null);
                if (FROM_PAGE_GLOBAL.equals(fromPage)) {
                    context.startActivity(SearchPanelActivity.getIntentByGlobal(context, null, tvSearchTips.getKeyword()));
                } else {
                    context.startActivity(SearchPanelActivity.getIntent(context, null, tvSearchTips.getKeyword()));//带入hint，到达搜索onboarding页面
                }
            }
        });
        helper.setOnViewClickListener(R.id.tv_search_btn, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                String keyword = tvSearchTips.getKeyword();
                trackEagleClickAction(helper, item, keyword);
                if (!EmptyUtils.isEmpty(keyword)) {
                    if (FROM_PAGE_GLOBAL.equals(fromPage)) {
                        context.startActivity(SearchPanelActivity.getIntentByGlobal(context, keyword, null));
                    } else {
                        context.startActivity(SearchPanelActivity.getIntent(context, keyword, null));//带入关键字，直接到达搜索结果页面
                    }
                }
            }
        });
        helper.setVisibleCompat(R.id.iv_camera, false);
        helper.setOnViewClickListener(R.id.iv_camera, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                EagleTrackManger.get().trackEagleClickAction("image", -1, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
                if (FROM_PAGE_GLOBAL.equals(fromPage)) {
                    context.startActivity(SearchPanelActivity.getIntentByGlobal(context, null, null));
                }
            }
        });
    }

    private void trackEagleClickAction(AdapterViewHolder helper, CmsSearchBarData item, @Nullable String keyword) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, keyword, null);
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm = */item.getEventKey(),
                /* modPos = */item.position,
                /* secNm = */null,
                /* secPos = */-1,
                /* targetNm = */item.getTargetName(),
                /* targetPos = */-1,
                /* targetType = */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType = */EagleTrackEvent.ClickType.VIEW,
                /* ctx = */ctx
        );
    }
}
