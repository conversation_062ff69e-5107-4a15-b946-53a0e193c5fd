package com.sayweee.weee.module.launch.bean;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/26.
 * Desc:
 */
public class LaunchHelpBean {
    public boolean toGuideFlow; //走launch流程
    public boolean toZipCodeFlow; //输入zip code
    public boolean toNormalFlow; //正常流程
    public boolean toLoginFlow; //token刷新失败，去登陆

    @Deprecated
    public boolean isGuideInTest;   //引导是否在ab test中

    public LanguageBean language;

    public LaunchHelpBean() {
    }

    public LaunchHelpBean setToNormalFlow(boolean toNormalFlow) {
        this.toNormalFlow = toNormalFlow;
        return this;
    }

    public LaunchHelpBean setToGuideFlow(boolean toGuideFlow, LanguageBean language, boolean isGuideInTest) {
        this.toGuideFlow = toGuideFlow;
        this.language = language;
        this.isGuideInTest = isGuideInTest;
        return this;
    }

    public LaunchHelpBean setToZipCodeFlow(boolean toZipCodeFlow) {
        this.toZipCodeFlow = toZipCodeFlow;
        return this;
    }

    public LaunchHelpBean setToLoginFlow(boolean toLoginFlow) {
        this.toLoginFlow = toLoginFlow;
        return this;
    }
}
