package com.sayweee.weee.module.cart.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleMultiTypeAdapter;
import com.sayweee.weee.module.cart.bean.CartSelectionData;
import com.sayweee.weee.module.cart.bean.ICartImpression;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.ServiceFeeItem;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnViewHelper;

import java.util.ArrayList;
import java.util.List;

public class CartSelectionAdapter
        extends SimpleMultiTypeAdapter<AdapterDataType, AdapterViewHolder>
        implements EagleImpressionAdapter {

    public static final int CART_SELECTION_TYPE = 100;

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder.getItemViewType() == CART_SELECTION_TYPE) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof RecyclerView.LayoutParams) {
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                int position = holder.getLayoutPosition();
                params.topMargin = CommonTools.dp2px(8);
                params.bottomMargin = CommonTools.dp2px(position == mData.size() - 1 ? 300 : 8);
            }
        }
    }

    @Override
    protected void registerAdapterType() {
        registerItemType(CART_SELECTION_TYPE, R.layout.item_cart_selection);
        mLayoutResId = R.layout.item_cart_blank;
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, AdapterDataType item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        Object orNull = CollectionUtils.firstOrNull(payloads);
        if (orNull instanceof NewSectionBean) {
            NewSectionBean sectionBean = (NewSectionBean) orNull;
            fillCheckbox(helper, sectionBean.selected);
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        if (item.getType() == CART_SELECTION_TYPE) {
            convertCartSectionShippingInfo(helper, (CartSelectionData) item);
        }
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            for (int i = start; i <= end; i++) {
                ImpressionBean event = getEagleImpressionEvent(getItem(i));
                if (event != null) {
                    list.add(event);
                }
            }
        }
        return list;
    }

    public ImpressionBean getEagleImpressionEvent(AdapterDataType item) {
        if (item instanceof ICartImpression) {
            return ((ICartImpression) item).getImpressionBean();
        }
        return null;
    }

    private void convertCartSectionShippingInfo(AdapterViewHolder helper, CartSelectionData item) {
        // CART_SELECTION_TYPE
        // R.layout.item_cart_selection
        NewSectionBean bean = item.t;
        if (bean.isIndividualCheckout()) {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_check), false);
            fillCheckbox(helper, false);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.btn_checkout_individual), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_check), true);
            fillCheckbox(helper, bean.selected);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.btn_checkout_individual), false);
        }
        helper.addOnClickListener(R.id.btn_checkout_individual);
        NewSectionBean.ShippingInfo shippingInfo = bean.shipping_info;
        if (shippingInfo != null) {
            helper.loadImage(mContext, R.id.iv_icon, WebpManager.convert(ImageSpec.SPEC_32, shippingInfo.shipping_icon_url));
            helper.setText(R.id.tv_title, shippingInfo.shipping_type_desc);
            String subTitle;
            if (bean.isPresale()) {
                subTitle = shippingInfo.shipping_shipment_date;
            } else {
                subTitle = shippingInfo.shipping_desc;
            }
            helper.setText(R.id.tv_sub_title, subTitle);
            TalkBackHelper.setContentDescRadio(helper.itemView, shippingInfo.shipping_type_desc, bean.selected);
        } else {
            TalkBackHelper.setContentDesc(helper.itemView, null);
        }
        fillFeeInfo(helper, item);
        //商品缩略图模块
        fillProducts(helper, bean);
    }

    private static void fillCheckbox(AdapterViewHolder helper, boolean selected) {
        Context context = helper.itemView.getContext();
        if (selected) {
            helper.setImageResource(R.id.iv_check, R.drawable.drawable_checkbox_checked);
            Integer strokeColorInt = ViewTools.getColorByResId(context, R.color.color_btn_secondary_hairline);
            if (strokeColorInt != null) {
                ShapeHelper.setBackgroundDrawable(helper.getView(R.id.layout_cart), Color.TRANSPARENT, CommonTools.dp2px(20), strokeColorInt, CommonTools.dp2px(2f));
            }
        } else {
            helper.setImageResource(R.id.iv_check, R.drawable.drawable_checkbox_empty_disabled);
            Integer strokeColorInt = ViewTools.getColorByResId(context, R.color.color_surface_100_hairline);
            if (strokeColorInt != null) {
                ShapeHelper.setBackgroundDrawable(helper.getView(R.id.layout_cart), Color.TRANSPARENT, CommonTools.dp2px(20), strokeColorInt, CommonTools.dp2px(1f));
            }
        }
    }

    private void fillFeeInfo(AdapterViewHolder helper, CartSelectionData item) {
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_delivery_fee), 600);
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_service_fee), 600);

        NewSectionBean bean = item.t;
        fillDeliveryFee(helper, item);
        fillServiceFee(helper, item);

        // free_shipping_diff_desc
        if (bean.hasFreeShippingDiffDesc()) {
            ViewTools.setViewHtml(helper.getView(R.id.tv_free_shipping_diff_desc), bean.shipping_info.free_shipping_diff_desc);
            helper.setVisibleCompat(R.id.tv_free_shipping_diff_desc, true);
        } else {
            helper.setText(R.id.tv_free_shipping_diff_desc, null);
            helper.setVisibleCompat(R.id.tv_free_shipping_diff_desc, false);
        }
    }

    private void fillDeliveryFee(AdapterViewHolder helper, CartSelectionData item) {
        NewSectionBean bean = item.t;
        NewSectionBean.ShippingInfo shippingInfo = bean.shipping_info;
        NewSectionBean.FeeInfo feeInfo = bean.fee_info;
        boolean isSeller = "seller".equalsIgnoreCase(bean.type);
        if (feeInfo != null) {
            helper.setText(R.id.tv_total_price, "$" + feeInfo.total_price_with_activity);

            boolean isFreeDelivery = shippingInfo == null || shippingInfo.isFreeDelivery();
            if (isFreeDelivery) {
                helper.setText(R.id.tv_delivery_fee, mContext.getString(R.string.s_free_delivery));
                ViewTools.applyTextColor(helper.getView(R.id.tv_delivery_fee), R.color.color_success_txt);
            } else {
                helper.setText(R.id.tv_delivery_fee,
                        mContext.getString(isSeller
                                ? R.string.s_shipping_fee
                                : R.string.s_delivery_fee) + " " + OrderHelper.formatUSMoney(DecimalTools.parseDouble(shippingInfo.shipping_fee))
                );
                ViewTools.applyTextColor(helper.getView(R.id.tv_delivery_fee),
                        R.color.color_pricing_bg
                );
            }
        }
    }

    private void fillServiceFee(AdapterViewHolder helper, CartSelectionData item) {
        NewSectionBean bean = item.t;
        ServiceFeeItem serviceFeeItem = CollectionUtils.firstOrNull(
                bean.service_fee_list,
                fee -> ServiceFeeItem.TYPE_DELIVERY_SERVICE_FEE.equals(fee.type)
        );
        TextView tvServiceFee = helper.getView(R.id.tv_service_fee);
        if (serviceFeeItem != null) {
            double amount = DecimalTools.doubleValue(serviceFeeItem.amount, 0.0);
            if (amount > 0) { // free
                ViewTools.applyTextColor(tvServiceFee, R.color.color_pricing_bg);
                tvServiceFee.setText(mContext.getString(R.string.s_service_fee) + " " + OrderHelper.formatUSMoney(amount));
            } else {
                ViewTools.applyTextColor(tvServiceFee, R.color.color_success_txt);
                tvServiceFee.setText(mContext.getString(R.string.s_free_service_fee));
            }
            ViewTools.setViewVisibilityIfChanged(tvServiceFee, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(tvServiceFee, false);
        }
    }

    private void fillProducts(AdapterViewHolder helper, NewSectionBean bean) {
        LinearLayout layoutCartItems = helper.getView(R.id.layout_cart_items);
        ViewTools.removeAllViews(layoutCartItems);

        List<NewItemBean> cartItems = bean.getValidCartItems();
        if (EmptyUtils.isEmpty(cartItems)) {
            return;
        }

        List<NewItemBean> allItems4Show = new ArrayList<>();
        for (NewItemBean data : cartItems) {
            allItems4Show.add(data);
            allItems4Show.addAll(data.getActivityItems());
        }

        Context context = helper.itemView.getContext();
        float containerWidth = getProductContainerWidth(context, ((TextView) helper.getView(R.id.tv_delivery_fee)).getPaint());
        int canShowSize = (int) (containerWidth / CommonTools.dp2px(58));
        List<NewItemBean> cartItems4Show;
        if (allItems4Show.size() <= canShowSize) {
            cartItems4Show = new ArrayList<>(allItems4Show);
        } else {
            cartItems4Show = new ArrayList<>(allItems4Show.subList(0, canShowSize));
            cartItems4Show.add(new NewItemBean());//eg: +99
        }
        for (NewItemBean itemBean : cartItems4Show) {
            layoutCartItems.addView(ViewTools.getHelperView(layoutCartItems, R.layout.item_cart_selection_items, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    if (itemBean.product_id != 0) {
                        ImageView iv = helper.getView(R.id.iv_icon);
                        ImageLoader.load(mContext, iv, WebpManager.convert(ImageSpec.SPEC_PRODUCT, itemBean.img), R.mipmap.iv_product_placeholder);
                        helper.setVisible(R.id.tv_item_qty, itemBean.quantity > 1);
                        helper.setText(R.id.tv_item_qty, "x" + itemBean.quantity);
                    } else {
                        helper.setVisible(R.id.tv_item_qty, false);
                        helper.setVisible(R.id.tv_items_count_left, true);
                        helper.setText(R.id.tv_items_count_left, "+" + (allItems4Show.size() - (cartItems4Show.size() - 1)));
                    }
                }
            }));
        }
    }


    private float windowWidth;
    private float feeTextWidth;

    private float getProductContainerWidth(Context context, Paint paint) {
        if (windowWidth <= 0) {
            windowWidth = CommonTools.getCurrentWindowWidth(context);
        }
        if (feeTextWidth <= 0) {
            if (paint == null) {
                paint = new Paint();
                paint.setTextSize(14);
                paint.setTypeface(Typeface.DEFAULT_BOLD);
            }
            Rect rect = new Rect();
            float width1, width2;
            String text;
            text = context.getString(R.string.s_free_delivery);
            paint.getTextBounds(text, 0, text.length(), rect);
            width1 = rect.width();
            text = context.getString(R.string.s_shipping_fee) + " $100.00";
            paint.getTextBounds(text, 0, text.length(), rect);
            width2 = rect.width();
            feeTextWidth = Math.max(width1, width2);
        }
        // | checkmark | container | fee |
        float consumedWidth = CommonTools.dp2px(
                18 // checkmarkMarginStart
                        + 24 // checkmark
                        + 6 // checkmarkMarginEnd
                        + 12 // containerMarginStart
                        + 3 // containerMarginEnd
                        + 20 // itemEnd
        ) + CommonTools.dp2px(18) // for item qty
                + feeTextWidth; // delivery fee
        return windowWidth - consumedWidth;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void selectAllCart() {
        notifyDataSetChanged();
    }
}
