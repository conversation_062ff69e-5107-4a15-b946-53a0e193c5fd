package com.sayweee.weee.module.cms.service2;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.bean.CmsComponentRange;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.CarouselBannerParser;
import com.sayweee.weee.module.cms.iml.banner.CmsBannerContainerParser;
import com.sayweee.weee.module.cms.iml.banner.CmsBannerParser;
import com.sayweee.weee.module.cms.iml.bannerarray.CmsBannerArrayParser;
import com.sayweee.weee.module.cms.iml.block.CmsBlockParser;
import com.sayweee.weee.module.cms.iml.button.CmsButtonParser;
import com.sayweee.weee.module.cms.iml.countdown.CmsCountdownParser;
import com.sayweee.weee.module.cms.iml.countdown.data.CmsCountdownData;
import com.sayweee.weee.module.cms.iml.coupon.CmsCouponParser;
import com.sayweee.weee.module.cms.iml.coupon.data.CmsCouponBean;
import com.sayweee.weee.module.cms.iml.coupon.data.CmsCouponData;
import com.sayweee.weee.module.cms.iml.couponlist.CmsCouponListParser;
import com.sayweee.weee.module.cms.iml.couponsingle.CmsSingleCouponParser;
import com.sayweee.weee.module.cms.iml.couponsingle.data.CouponBatchClaimRequest;
import com.sayweee.weee.module.cms.iml.couponsingle.data.CouponBatchClaimResponse;
import com.sayweee.weee.module.cms.iml.divider.CmsDividerParser;
import com.sayweee.weee.module.cms.iml.navline.CmsNavLineParser;
import com.sayweee.weee.module.cms.iml.pagenav.CmsPageNavParser;
import com.sayweee.weee.module.cms.iml.pagenav.data.CmsPageNavData;
import com.sayweee.weee.module.cms.iml.pagenav.data.CmsPageNavItemsProperty;
import com.sayweee.weee.module.cms.iml.product.CmsProductLineTabsParser;
import com.sayweee.weee.module.cms.iml.product.CmsProductWaterfallFetcher;
import com.sayweee.weee.module.cms.iml.product.ProductLineParser;
import com.sayweee.weee.module.cms.iml.product.ProductListParser;
import com.sayweee.weee.module.cms.iml.product.ProductWaterfallParser;
import com.sayweee.weee.module.cms.iml.product.data.ProductWaterfallData;
import com.sayweee.weee.module.cms.iml.promotionbanner.CmsPromotionBannerParser;
import com.sayweee.weee.module.cms.iml.seller.data.SellerListData;
import com.sayweee.weee.module.cms.iml.text.CmsTextParser;
import com.sayweee.weee.module.cms.iml.title.CmsComplexTitleParser;
import com.sayweee.weee.module.cms.iml.title.CmsTitleParser;
import com.sayweee.weee.module.cms.iml.video.CmsVideoParser;
import com.sayweee.weee.module.cms.service.ComponentPool;
import com.sayweee.weee.module.cms.service2.bean.CmsPageData;
import com.sayweee.weee.module.cms.service2.loader.CmsPageLoader;
import com.sayweee.weee.module.collection.service.CmsPageMenuLoader;
import com.sayweee.weee.module.home.provider.banner.BannerLineParser;
import com.sayweee.weee.module.home.provider.banner.BannerThemeParser;
import com.sayweee.weee.module.home.provider.bar.SearchBarParser;
import com.sayweee.weee.module.home.provider.category.CategoryParser;
import com.sayweee.weee.module.home.provider.product.FeatureThisWeekParser;
import com.sayweee.weee.module.home.provider.product.LightingDealsParser;
import com.sayweee.weee.module.home.provider.thematic.ThematicParser;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedParser;
import com.sayweee.weee.module.mkpl.provider.parser.CmsProductSingleParser;
import com.sayweee.weee.module.mkpl.provider.parser.CmsTextArrayParser;
import com.sayweee.weee.module.mkpl.provider.parser.CmsTitleRichParser;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.CouponClaimRequest;
import com.sayweee.weee.module.seller.bean.CouponClaimResponse;
import com.sayweee.weee.service.live.UnPeekLiveData;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.function.Transform;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class CmsBottledViewModel<M extends BaseLoaderModel<OrderApi>>
        extends CmsViewModel<M>
        implements IContentFeedSharedViewModel {

    public MutableLiveData<Map<String, Object>> lightningDealsRemindData = new MutableLiveData<>();
    public MutableLiveData<CouponClaimResponse> couponClaimResponseLiveData = new UnPeekLiveData<>();
    public MutableLiveData<CouponBatchClaimResponse> couponBatchClaimResponseLiveData = new UnPeekLiveData<>();
    public MutableLiveData<Long> countdownLiveData = new UnPeekLiveData<>();
    protected final CmsComponentRange componentRange = new CmsComponentRange();

    @Nullable
    public CmsContentFeedPacket contentFeedPacket;

    private boolean hasValidPageNavComponent = false;

    public CmsBottledViewModel(@NonNull Application application) {
        super(application);
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_LINE, new ProductLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_ITEM_LINE, new ProductLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_ITEM_LINE_V2, new ProductLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CAROUSEL_BANNER, new CarouselBannerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_MAIN_BANNER, new CarouselBannerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_WATERFALL, new ProductWaterfallParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_ITEM_LIST, new ProductWaterfallParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_LIST, new ProductListParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER, new CmsBannerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_ARRAY, new CmsBannerArrayParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_CONTAINER, new CmsBannerContainerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_NAV_LINE, new CmsNavLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BLOCK, new CmsBlockParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_DIVIDER, new CmsDividerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BUTTON, new CmsButtonParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TITLE, new CmsTitleParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COMPLEX_TITLE, new CmsComplexTitleParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TEXT, new CmsTextParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_SINGLE, new CmsProductSingleParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_ARRAY, new ProductLineParser());//cm_product_array组件也用product line解析
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_SEARCH_BAR, new SearchBarParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_THEME, new BannerThemeParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CATEGORIES, new CategoryParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_THEME, new ThematicParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TITLE_RICH, new CmsTitleRichParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TEXT_ARRAY, new CmsTextArrayParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_LINE, new BannerLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_LINE_TABS, new CmsProductLineTabsParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_FEATURED_THIS_WEEK, new FeatureThisWeekParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_LIGHTNING_DEALS, new LightingDealsParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED, new CmsContentFeedParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED_V2, new CmsContentFeedParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COUPON, new CmsCouponParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_SINGLE_COUPON, new CmsSingleCouponParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COUPON_LIST, new CmsCouponListParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_VIDEO, new CmsVideoParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COUNTDOWN, new CmsCountdownParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PROMOTION_BANNER, new CmsPromotionBannerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PAGE_NAV, new CmsPageNavParser());
    }

    @Override
    protected void beforeFetchCmsData() {
        super.beforeFetchCmsData();
        componentRange.clear();
    }

    @Override
    protected @NonNull CmsPageData parseCmsResponse(@NonNull CmsBean cmsBean) {
        if (cmsBean.layout != null && cmsBean.layout.page_param != null) {
            pageParamData.postValue(cmsBean.layout.page_param);
        }
        return super.parseCmsResponse(cmsBean);
    }

    @NonNull
    @Override
    protected CmsPageLoader createPageLoader() {
        hasValidPageNavComponent = checkHasValidPageNavComponent(pageData);
        CmsPageLoader loader;
        if (hasValidPageNavComponent) {
            loader = new CmsPageMenuLoader(pageData, /* firstPageLimit= */5);
        } else {
            loader = super.createPageLoader();
        }
        loader.registerComponentFetcherFactory(ComponentPool.Key.COMPONENT_CM_PRODUCT_WATERFALL, CmsProductWaterfallFetcher::new);
        return loader;
    }

    protected boolean checkHasValidPageNavComponent(CmsPageData pageData) {
        boolean disablePaging = false;
        final int MAX_INDEX_OF_PAGE_NAV = 5;
        ComponentData<?, ?> component = pageData.getComponentByKey(ComponentPool.Key.COMPONENT_CM_PAGE_NAV);
        int index = pageData.indexOfComponent(component);
        if (component instanceof CmsPageNavData && component.isValid() && index < MAX_INDEX_OF_PAGE_NAV) {
            disablePaging = true;
        }
        return disablePaging;
    }

    public boolean containsPageNavComponent() {
        return hasValidPageNavComponent;
    }

    @Override
    protected void modifyComponentDataSource(ComponentData<?, ?> data, CmsDataSource dataSource) {
        if (data instanceof SellerListData) {
            dataSource.putQueryParam("page_no", String.valueOf(0));
            dataSource.putQueryParam("page_size", String.valueOf(10));
        } else if (data instanceof ProductWaterfallData) {
            dataSource.putQueryParam("offset", String.valueOf(0));
            dataSource.putQueryParam("limit", String.valueOf(20));
        } else if (data instanceof CmsContentFeedPacket) {
            dataSource.putQueryParam("recommend_session", ((CmsContentFeedPacket) data).recommendSession);
            dataSource.putQueryParam("page_num", "1");
            dataSource.putQueryParamIfAbsent("from_page", "");
            dataSource.putQueryParamIfAbsent("key", "");
            contentFeedPacket = (CmsContentFeedPacket) data;
            contentFeedPacket.setBaseDataSource(new CmsDataSource(dataSource));
        } else if (data instanceof CmsCouponData) {
            String oldPlanIds = dataSource.getQueryParam("plan_ids");
            if (oldPlanIds != null && !oldPlanIds.isEmpty()) {
                try {
                    String newPlanIds = URLDecoder.decode(oldPlanIds, "utf-8");
                    dataSource.putQueryParam("plan_ids", newPlanIds);
                } catch (Exception ignored) {
                    // do nothing
                }
            }
        } else {
            super.modifyComponentDataSource(data, dataSource);
        }
    }

    @NonNull
    @Override
    protected List<AdapterDataType> createAdapterData(int pageNum, List<ComponentData<?, ?>> newDataList) {
        List<AdapterDataType> list = new ArrayList<>();
        int index = componentRange.getLastPosition();
        for (ComponentData<?, ?> component : newDataList) {
            if (component != null && component.isValid()) {
                List<? extends AdapterDataType> adapterData = component.toComponentData();
                if (adapterData != null && !adapterData.isEmpty()) {
                    String componentId = component.getComponentId();
                    CmsComponentRange.Item range = componentRange.getRangeById(componentId);
                    if (range == null) {
                        range = new CmsComponentRange.Item();
                        range.componentId = componentId;
                        range.componentInstanceKey = component.getComponentInstanceKey();
                        componentRange.putRange(range);
                    }
                    range.firstPos = index;
                    range.lastPos = index + adapterData.size();
                    index = range.lastPos;
                    list.addAll(adapterData);
                }
            }
        }

        prepareCountdownData();
        preparePageNavData(pageNum);
        return list;
    }

    private void prepareCountdownData() {
        ComponentData<?, ?> component = pageData.getComponentByKey(ComponentPool.Key.COMPONENT_CM_COUNTDOWN);
        CmsCountdownData item = null;
        if (component instanceof CmsCountdownData) {
            item = (CmsCountdownData) component;
        }
        if (item != null && item.isValid()) {
            long endTime = item.getFixedEndTime();
            if (endTime > 0) {
                endTime += 3L; // 3s delay
                countdownLiveData.postValue(TimeUnit.SECONDS.toMillis(endTime));
            } else {
                countdownLiveData.postValue(0L);
            }
        } else {
            countdownLiveData.postValue(0L);
        }
    }

    protected void preparePageNavData(int pageNum) {
        ComponentData<?, ?> component = pageData.getComponentByKey(ComponentPool.Key.COMPONENT_CM_PAGE_NAV);
        CmsPageNavData item = null;
        if (component instanceof CmsPageNavData) {
            item = (CmsPageNavData) component;
        }
        if (item != null && item.isValid()) {
            List<CmsPageNavItemsProperty> navItems = new ArrayList<>(item.getProperty().getNavProperties());
            CmsComponentRange workingRange = new CmsComponentRange();
            int index = 0;
            Collection<ComponentData<?, ?>> componentDataList = pageData.getComponentDataList();
            for (CmsPageNavItemsProperty navItem : navItems) {
                ComponentData<?, ?> componentData = CollectionUtils.firstOrNull(
                        componentDataList,
                        comp -> navItem.key.equals(comp.componentInstanceKey)
                );
                CmsComponentRange.Item range = componentRange.getRangeByInstanceId(navItem.key);
                if (componentData != null && componentData.isValid() && range != null) {
                    CmsComponentRange.Item rangeItem = new CmsComponentRange.Item();
                    rangeItem.componentId = range.componentId;
                    rangeItem.componentInstanceKey = range.componentInstanceKey;
                    rangeItem.label = navItem.label;
                    rangeItem.firstPos = range.firstPos;
                    rangeItem.lastPos = range.lastPos;
                    rangeItem.navIndex = index;
                    workingRange.putRange(rangeItem);
                    index++;
                }
            }
            item.setRange(workingRange);
            item.showVeil = pageNum < 1;
        }
    }

    @Nullable
    @Override
    public CmsContentFeedPacket getContentFeedPacket() {
        return contentFeedPacket;
    }

    @Nullable
    @Override
    public String getContentFeedFromPageKey() {
        return null;
    }

    public void changeLightningDealsRemind(int productId, boolean isRemind) {
        RequestParams requestParams = new RequestParams()
                .put("product_id", productId)
                .put("status", isRemind ? "A" : "X");
        getLoader().getHttpService()
                .changeLightningDealsRemind(requestParams.create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("product_id", productId);
                        map.put("remind", isRemind);
                        lightningDealsRemindData.postValue(map);
                    }
                });
    }

    public void claimCoupon(final CouponClaimRequest request) {
        ResponseObserver<ResponseBean<CouponClaimBean>> observer;
        observer = new ResponseObserver<ResponseBean<CouponClaimBean>>() {

            @Override
            public void onResponse(ResponseBean<CouponClaimBean> response) {
                couponClaimResponseLiveData.postValue(
                        new CouponClaimResponse(request, response, null)
                );
            }

            @Override
            public void onError(FailureBean failure) {
                couponClaimResponseLiveData.postValue(
                        new CouponClaimResponse(request, null, failure)
                );
            }
        };
        getLoader().getHttpService()
                .claimSellerCoupon(request.getSellerId(), request.asRequestBody())
                .compose(ResponseTransformer.scheduler())
                .subscribe(observer);
    }

    public void batchClaimCoupon(@NonNull final CouponBatchClaimRequest request) {
        getLoader().getHttpService()
                .batchClaimCoupon(request.asRequestBody())
                .compose(ResponseTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<CmsCouponBean>>() {
                    @Override
                    public void onResponse(ResponseBean<CmsCouponBean> response) {
                        couponBatchClaimResponseLiveData.postValue(
                                new CouponBatchClaimResponse(request, response, null)
                        );
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        couponBatchClaimResponseLiveData.postValue(
                                new CouponBatchClaimResponse(request, null, failure)
                        );
                    }
                });
    }

    @Nullable
    public CmsComponentRange.Item findComponentRangeByPosition(int position) {
        return findComponentRangeBy(range -> range.findRangeByPosition(position));
    }

    @Nullable
    public CmsComponentRange.Item findComponentRangeByInstanceId(@NonNull String componentInstanceId) {
        return findComponentRangeBy(range -> range.getRangeByInstanceId(componentInstanceId));
    }

    @Nullable
    private CmsComponentRange.Item findComponentRangeBy(
            @NonNull Transform<CmsComponentRange, CmsComponentRange.Item> transform
    ) {
        ComponentData<?, ?> componentData = pageData.getComponentByKey(ComponentPool.Key.COMPONENT_CM_PAGE_NAV);
        if (componentData instanceof CmsPageNavData) {
            CmsPageNavData item = (CmsPageNavData) componentData;
            CmsComponentRange range = item.getRange();
            if (range != null) {
                return transform.transform(range);
            }
        }
        return null;
    }

}
