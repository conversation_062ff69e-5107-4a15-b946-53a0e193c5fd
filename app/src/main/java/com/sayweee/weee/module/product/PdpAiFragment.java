package com.sayweee.weee.module.product;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.transition.TransitionManager;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentPdpAiBinding;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.product.adapter.PdpAiAdapter;
import com.sayweee.weee.module.product.data.InteractionAnswerData;
import com.sayweee.weee.module.product.data.InteractionQuestionData;
import com.sayweee.weee.module.product.data.InteractionRelateData;
import com.sayweee.weee.module.product.provider.InteractionQuestionProvider;
import com.sayweee.weee.module.product.service.PdpAiViewModel;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.IntentTools;
import com.sayweee.weee.utils.SimpleTextWatcher;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.IconToastView;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.core.view.WrapperMvvmFragment;
import com.sayweee.wrapper.http.support.Utils;
import com.sayweee.wrapper.utils.Spanny;
import com.sayweee.wrapper.widget.Toaster;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class PdpAiFragment extends WrapperMvvmFragment<PdpAiViewModel> {

    private static final String EXTRA_PRODUCT = "EXTRA_PRODUCT";
    private static final String EXTRA_QUESTIONS = "EXTRA_QUESTIONS";
    private static final int BOTTOM_HEIGHT_DP = 80;
    private boolean isHalfExpend = true;
    private boolean querying;//正在查询中
    private boolean canAdd;//加购状态
    private FragmentPdpAiBinding binding;
    private BottomSheetBehavior<View> pdpAiBehavior;
    private PdpAiAdapter adapter;
    private KeyboardChangeHelper helper;
    private BroadcastReceiver networkStateReceiver;
    private Handler handler;
    private Runnable hideVeilRunnable;
    private final OnBackPressedCallback onBackPressedCallback = new OnBackPressedCallback(true) {
        @Override
        public void handleOnBackPressed() {
            close();
        }
    };

    public static int getEnterAnim() {
        return androidx.appcompat.R.anim.abc_slide_in_bottom;
    }

    public static int getExitAnim() {
        return androidx.appcompat.R.anim.abc_slide_out_bottom;
    }

    public static String getFragmentTag(String sellerId) {
        return "com.sayweee.weee.module.product.PdpAiFragment-" + sellerId;
    }

    public static PdpAiFragment getFragment(ProductBean product, List<String> questions) {
        PdpAiFragment fragment = new PdpAiFragment();
        Bundle arguments = new Bundle();
        arguments.putSerializable(EXTRA_PRODUCT, product);
        if (questions != null) {
            arguments.putStringArrayList(EXTRA_QUESTIONS, new ArrayList<>(questions));
        }
        fragment.setArguments(arguments);
        return fragment;
    }

    private int getOffset(Context context) {
        return ScreenUtils.getStatusBarHeight(context) + context.getResources().getDimensionPixelSize(R.dimen.default_title_height);
    }

    @Nullable
    private ProductBean getExtraProduct() {
        return IntentTools.getSerializable(getArguments(), EXTRA_PRODUCT, ProductBean.class);
    }

    @Nullable
    private String getExtraProductId() {
        return getExtraProduct() != null ? String.valueOf(getExtraProduct().id) : null;
    }

    @Nullable
    private List<String> getExtraQuestions() {
        return IntentTools.getSerializable(getArguments(), EXTRA_QUESTIONS, ArrayList.class);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_pdp_ai;
    }

    @Override
    public void loadData() {
        viewModel.setProductId(getExtraProductId());
        //预设问题
        List<String> questions = getExtraQuestions();
        if (CollectionUtils.isNotEmpty(questions)) {
            if (questions.size() == 1) {
                query(questions.get(0));
            } else {
                viewModel.addInteractionQuestion(questions);
            }
        } else {
            if (AccountManager.get().isLogin()) {
                viewModel.getInteractionQuestion(getExtraProductId());
            }
        }
    }

    @Override
    public void attachModel() {
        viewModel.newData.observe(this, adapterDataTypes -> {
            adapter.setNewData(adapterDataTypes);
            smoothScrollToBottom();
        });
        viewModel.appendData.observe(this, adapterDataTypes -> {
            adapter.add(adapterDataTypes);
            smoothScrollToBottom();
        });
        viewModel.historyData.observe(this, adapterDataTypes -> {
            binding.rcvRefresh.finishRefresh();
            adapter.addHistory(adapterDataTypes);
        });
        viewModel.inProgressData.observe(this, inProgress -> {
            setEditState(!inProgress);
            querying = inProgress;
        });//禁止交互
        viewModel.errorData.observe(this, error -> adapter.addNetworkDisconnected());
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        binding = FragmentPdpAiBinding.bind(contentView);

        initPdpAi(view.getContext());
        initRecyclerView(view.getContext());
        initAdapter();
        initListener();
        initProduct(view.getContext());
        registerNetworkStateChangedReceiver();//无网络状态
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        AppAnalytics.logPageView(WeeeEvent.PageView.AI_BOT_ASSISTANT_RESULT, this);
    }

    @Override
    public void onDestroyView() {
        onBackPressedCallback.remove();
        unregisterNetworkStateChangedReceiver();
        if (helper != null) {
            helper.endObserve();
        }
        if (handler != null && hideVeilRunnable != null) {
            handler.removeCallbacks(hideVeilRunnable);
        }
        super.onDestroyView();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initRecyclerView(Context context) {
        adapter = new PdpAiAdapter();
        setFooterView(context);
        binding.rcvPdpAi.setAdapter(adapter);
        LinearLayoutManager layoutManager;
        layoutManager = new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false);
        binding.rcvPdpAi.setLayoutManager(layoutManager);
        binding.rcvPdpAi.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                int lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition();
                int size = CollectionUtils.size(adapter.getData());
                setAtTop(lastVisibleItemPosition < size - 1);
                if (ViewTools.isViewVisible(binding.layoutOp)) {
                    binding.layoutOp.collapseWithAnim();
                }
            }
        });
    }

    public void setFooterView(Context context) {
        View footerView = new Space(context);
        footerView.setLayoutParams(
                new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        CommonTools.dp2px(BOTTOM_HEIGHT_DP) + getOffset(context)
                )
        );
        adapter.setFooterView(footerView);
    }

    private void initPdpAi(Context context) {
        pdpAiBehavior = BottomSheetBehavior.from(binding.clPdpAi);
        int offset = getOffset(context);
        pdpAiBehavior.setExpandedOffset(offset);
        setVBottomMargin();

        pdpAiBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    if (onCloseListener != null) {
                        onCloseListener.onClose();
                    }
                    onBackPressedCallback.setEnabled(false);
                    //关闭后清除预设问题
                    if (!adapter.onlyContainsInteractionQuestions()) {
                        adapter.clearInteractionQuestions();
                    }
                } else if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    isHalfExpend = false;
                    showProduct();
                    //展开后后自动打开 onBackPressedCallback
                    onBackPressedCallback.setEnabled(true);
                    binding.rcvRefresh.setEnableRefresh(true);
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                int[] location = new int[2];
                binding.clPdpAi.getLocationOnScreen(location);
                int top = location[1];
                setVBottomMargin(top - getStatusBarHeight());
            }
        });
        if (CollectionUtils.size(getExtraQuestions()) == 1) {
            showProduct();
        }
        isHalfExpend = CollectionUtils.size(getExtraQuestions()) != 1;
        show(null);
    }

    private void setVBottomMargin() {
        binding.clPdpAi.post(new Runnable() {

            @Override
            public void run() {
                int[] location = new int[2];
                binding.clPdpAi.getLocationOnScreen(location);
                // 距离屏幕顶部的绝对位置
                int clPdpAiTopOnScreen = location[1];
                setVBottomMargin(clPdpAiTopOnScreen - getStatusBarHeight());
            }
        });
    }

    private void initAdapter() {
        adapter.addItemProvider(new InteractionQuestionProvider() {
            @Override
            public void convert(AdapterViewHolder helper, InteractionQuestionData item) {
                super.convert(helper, item);
                helper.setOnViewClickListener(R.id.tv_interaction_question, v -> {
                    if (querying) {
                        return;
                    }
                    if (pdpAiBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
                        pdpAiBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                        adapter.clearInteractionQuestions();
                    }
                    query(item.question);//预设问题
                });
            }
        });
    }

    private void initListener() {
        ViewTools.setViewOnSafeClickListener(binding.ivClose, v -> close());
        ViewTools.setViewOnSafeClickListener(binding.ivNetwork, v -> ViewTools.setViewVisible(binding.layoutNetwork, false));
        ViewTools.setViewOnSafeClickListener(binding.includeVBottom.ivGo, v -> extracted());
        ViewTools.setViewOnSafeClickListener(binding.btnScroll, v -> smoothScrollToBottom());
        adapter.setOnItemChildClickListener(this::handleChildClick);
        //添加返回键监听
        requireActivity().getOnBackPressedDispatcher().addCallback(
                getViewLifecycleOwner(),
                onBackPressedCallback
        );
        setInputAttachWatcher();
        setEditObserver();
        //打开键盘
        setKeyboardObserver();
        //加载历史
        binding.rcvRefresh.setOnRefreshListener(refreshLayout -> {
            AdapterDataType interactionAnswerData = adapter.getFirstInteractionAnswerData();
            viewModel.history(interactionAnswerData);
        });
    }

    private void extracted() {
        if (!Utils.isNetworkConnected(getContext())) {
            return;
        }
        String message = binding.includeVBottom.etInput.getText().toString();
        if (!TextUtils.isEmpty(message)) {
            message = message.trim();
        }
        if (!TextUtils.isEmpty(message)) {
            query(message);//键盘提问
            hideKeyboard();
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setTargetNm("send")
                    .setTargetPos(0)
                    .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .build().getParams());
        }
    }

    public void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        // 隐藏软键盘
        imm.hideSoftInputFromWindow(activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    private void setInputAttachWatcher() {
        binding.includeVBottom.etInput.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                boolean show = s != null && s.length() > 0;
                ViewTools.setViewVisibilityIfChanged(binding.includeVBottom.ivGo, show);
            }
        });
    }

    private void setEditObserver() {
        EditText etInput = binding.includeVBottom.etInput;
        etInput.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                Editable input = etInput.getText();
                boolean hasInput = input != null && input.length() > 0;
                ViewTools.setViewVisible(binding.includeVBottom.tvHint, !hasFocus && !hasInput);
                ViewTools.setViewVisibilityIfChanged(binding.includeVBottom.ivGo, hasFocus & hasInput);
            }
        });

    }

    private void setKeyboardObserver() {
        helper = new KeyboardChangeHelper(getContentView());
        helper.startObserve().setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
                onKeyboardHeightChange(0);//关闭键盘
            }

            @Override
            public void onKeyboardShow() {
                if (isHalfExpend) {
                    pdpAiBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                    isHalfExpend = false;
                }
                onKeyboardHeightChange(helper.getKeyboardHeight());//打开键盘
            }
        });
    }

    /**
     * @noinspection rawtypes
     */
    private void handleChildClick(BaseQuickAdapter adapter, View view, int position) {
        int viewId = view.getId();
        Context context = view.getContext();
        String s = ContextCompat.getString(context, R.string.s_your_feedback);
        Object item = adapter.getItem(position);
        if (viewId == R.id.iv_like || viewId == R.id.iv_dislike) {
            if (item instanceof InteractionAnswerData) {
                boolean like = viewId == R.id.iv_like;
                InteractionAnswerData interactionAnswerData = (InteractionAnswerData) item;
                viewModel.preference(interactionAnswerData, like);
                toast(s, like ? R.mipmap.ic_ai_like : R.mipmap.ic_ai_dislike, context);
                interactionAnswerData.preferenceGone = true;
                adapter.notifyItemChanged(position, interactionAnswerData);
                int messageId = interactionAnswerData.messageId;
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(String.valueOf(messageId))
                        .setMod_pos(-1)
                        .setClickType(like ? EagleTrackEvent.ClickType.LIKE : EagleTrackEvent.ClickType.UNLIKE)
                        .setTargetNm(String.valueOf(messageId))
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .build().getParams());
            }
        } else if (viewId == R.id.tv_relate) {
            if (item instanceof InteractionRelateData) {
                query(((InteractionRelateData) item).relate);//关联问题
            }
        }
    }

    private void initProduct(Context context) {
        ProductBean product = getExtraProduct();
        if (product == null) return;
        ImageLoader.load(context, binding.includeProductTop.ivIcon, WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, product.getHeadImageUrl()), R.mipmap.iv_product_placeholder);
        //name
        LabelHelper.setTitleLabel(binding.includeProductTop.tvProductName, product, null);
        //price
        View layoutVolume = findViewById(R.id.layout_volume);
        ViewTools.setViewVisible(false, binding.includeProductTop.tvPrice, binding.includeProductTop.tvPriceDelete, layoutVolume);
        if (product.showVolumePrice()) {
            //Volume Pricing
            ViewTools.setViewVisible(true, layoutVolume);
            TextView tvPriceVolume = findViewById(R.id.tv_price_volume);
            if (tvPriceVolume != null) {
                Spanny s = new Spanny()
                        .append(OrderHelper.formatUSMoney(product.price), new TextAppearanceSpan(context, R.style.style_fluid_root_numeral_base))
                        .append(context.getString(R.string.s_volume_threshold_simple, product.volume_threshold));
                tvPriceVolume.setText(s);
            }
            //Volume single price
            boolean showBasePrice = product.base_price > 0;
            TextView tvPriceSingle = findViewById(R.id.tv_price_single);
            tvPriceSingle.setText((showBasePrice ? new Spanny(OrderHelper.formatUSMoney(product.base_price), new StrikethroughSpan()).append(" ") : new Spanny())
                    .append(context.getString(R.string.s_volume_threshold_one_qty, OrderHelper.formatUSMoney(product.volume_price))));

        } else {
            ViewTools.setViewVisible(true, binding.includeProductTop.tvPrice);
            binding.includeProductTop.tvPrice.setText(OrderHelper.formatUSMoney(product.price));
            ViewTools.setViewVisible(product.base_price > 0, binding.includeProductTop.tvPriceDelete);
            binding.includeProductTop.tvPriceDelete.setText(new Spanny(OrderHelper.formatUSMoney(product.base_price), new StrikethroughSpan()));
        }
        showCartOp(product);
        OpHelper.helperOp(binding.layoutOp, product, product, "app_ai_shopping", new HashMap<>(), null);
    }

    private void showCartOp(ProductBean product) {
        canAdd = !(OrderManager.get().isReachLimit(product) || ProductView.isChangeOtherDay(product.sold_status) || ProductView.isSoldOut(product.sold_status));
        ViewTools.setViewVisibilityIfChanged(binding.layoutOp, canAdd && ViewTools.isViewVisible(binding.includeProductTop.layoutProduct));
    }

    public void showLoadingVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vi_gen_ai);
        if (vl != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.INVISIBLE);
                vl.unVeil();
            }
        }
    }

    private void close() {
        pdpAiBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
        //关闭后自动禁用 onBackPressedCallback
        onBackPressedCallback.setEnabled(false);
    }

    private void setIsHalfExpend(String question) {
        isHalfExpend = question == null && adapter.onlyContainsInteractionQuestions();
    }

    private void syncProduct(ProductBean product) {
        ProductSyncHelper.helperCartOp(binding.layoutOp, new AdapterProductData(product));
        showCartOp(product);
    }

    public void show(String question) {
        if (isHalfExpend) {
            pdpAiBehavior.setState(BottomSheetBehavior.STATE_HALF_EXPANDED);
            ViewTools.setViewVisibilityIfChanged(binding.vDivider, false);
            ViewTools.setViewVisibilityIfChanged(binding.includeProductTop.layoutProduct, false);
            ViewTools.setViewVisibilityIfChanged(binding.layoutOp, false);
            binding.rcvRefresh.setEnableRefresh(false);
        } else {
            pdpAiBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        }
        if (!TextUtils.isEmpty(question)) {
            query(question);//pdp提问
        }
    }

    public void show(String question, ProductBean product) {
        setIsHalfExpend(question);
        syncProduct(product);
        show(question);
        veil(question);
    }

    private void veil(String question) {
        if (isHalfExpend || !TextUtils.isEmpty(question)) {
            return;
        }
        if (hideVeilRunnable != null) {
            handler.removeCallbacks(hideVeilRunnable);
        }
        showLoadingVeil(true);
        if (handler == null) {
            handler = new Handler();
        }
        hideVeilRunnable = () -> showLoadingVeil(false);
        handler.postDelayed(hideVeilRunnable, 500);
    }

    public interface OnCloseListener {
        void onClose();
    }

    private OnCloseListener onCloseListener;

    public void setOnCloseListener(OnCloseListener listener) {
        this.onCloseListener = listener;
    }

    private void showProduct() {
        ConstraintLayout layoutProduct = binding.includeProductTop.layoutProduct;
        if (layoutProduct.getVisibility() != View.VISIBLE) {
            layoutProduct.setAlpha(0f);
            TransitionManager.beginDelayedTransition((ViewGroup) binding.getRoot());
            layoutProduct.animate()
                    .alpha(1f)
                    .setDuration(100)
                    .start();
        }
        ViewTools.setViewVisibilityIfChanged(binding.vDivider, true);
        ViewTools.setViewVisibilityIfChanged(layoutProduct, true);
        ViewTools.setViewVisibilityIfChanged(binding.layoutOp, canAdd);
    }

    private void toast(String message, int image, Context context) {
        IconToastView toastView = new IconToastView(context);
        toastView.convert(message, image);
        Toaster.showToast(toastView);
    }

    private void onKeyboardHeightChange(int keyboardHeight) {
        int VBottomMargin = keyboardHeight + getOffset(getContext());
        setVBottomMargin(VBottomMargin);
        if (keyboardHeight > 0) {
            //add loading
            adapter.addKeyboardLoading(keyboardHeight);
            smoothScrollToBottom();
        } else {
            //remove loading
            adapter.removeKeyboardLoading();
        }
    }

    private void setVBottomMargin(int bottomMargin) {
        // 设置 v_bottom 的 bottom margin
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) binding.includeVBottom.vBottom.getLayoutParams();
        if (params != null) {
            params.bottomMargin = bottomMargin;
            binding.includeVBottom.vBottom.setLayoutParams(params);
        }
    }

    private void setEditState(boolean canEdit) {
        EditText etInput = binding.includeVBottom.etInput;
        if (!canEdit) {
            etInput.setText(null);
        }
        etInput.setFocusable(canEdit);
        etInput.setFocusableInTouchMode(canEdit);
        etInput.setCursorVisible(canEdit);
        //if (isEdit) etInput.requestFocus();
    }

    public void query(String question) {
        if (querying) {
            return;
        }
        adapter.removeInteractionRelatedQuestions();
        adapter.removeLastPreferenceIcon();
        adapter.addInteractionQueryData(question);
        smoothScrollToBottom();
        viewModel.query(question);
    }

    public void smoothScrollToBottom() {
        //binding.rcvPdpAi.postDelayed(() -> binding.rcvPdpAi.smoothScrollToPosition(adapter.getItemCount() - 1), 100);
        binding.rcvPdpAi.post(() -> binding.rcvPdpAi.smoothScrollToPosition(adapter.getItemCount() - 1));
    }

    @SuppressWarnings("squid:S5322")
    protected void registerNetworkStateChangedReceiver() {
        if (networkStateReceiver == null) {
            networkStateReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    handleNetworkDisconnected(context);
                }
            };
        }
        IntentFilter filter = new IntentFilter();
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        try {
            ContextCompat.registerReceiver(getContext(), networkStateReceiver,
                    filter, ContextCompat.RECEIVER_EXPORTED);
        } catch (Exception ignored) {
        }
    }

    private void handleNetworkDisconnected(Context context) {
        boolean networkConnected = Utils.isNetworkConnected(context);
        ViewTools.setViewVisibilityIfChanged(binding.layoutNetwork, !networkConnected);
        ViewTools.setImageResource(binding.includeVBottom.ivGo, networkConnected ? R.mipmap.ic_ai_input : R.mipmap.ic_ai_input_disable);
        if (!networkConnected) {
            adapter.addNetworkDisconnected();
        }
    }

    protected void unregisterNetworkStateChangedReceiver() {
        if (networkStateReceiver != null && getContext() != null) {
            try {
                getContext().unregisterReceiver(networkStateReceiver);
            } catch (Exception ignored) {
            }
        }
    }

    private void setAtTop(boolean visible) {
        ViewTools.setViewVisible(binding.btnScroll, visible);
    }

    private int getStatusBarHeight() {
        return getContext() != null ? ScreenUtils.getStatusBarHeight(getContext()) : 0;
    }
}

