package com.sayweee.weee.module.home;

import android.Manifest;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.core.order.OrderProvider;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ConfigService;
import com.sayweee.track.model.ExtendEvent;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.DeepLinkManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.PreSaleManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.CartAdapter;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cate.widget.StatusView;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.widget.timer.CmsComponentTimerHandler;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.adapter.HomeAdapter;
import com.sayweee.weee.module.home.adapter.OnRemindListener;
import com.sayweee.weee.module.home.bean.BesideTipsBean;
import com.sayweee.weee.module.home.bean.HomeBannerData;
import com.sayweee.weee.module.home.bean.HomePopHelper;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.bean.TopMessageV2ItemBean;
import com.sayweee.weee.module.home.bean.TrendingPostBean;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.home.provider.community.TrendingPostProvider;
import com.sayweee.weee.module.home.provider.community.data.CmsTrendingPostData;
import com.sayweee.weee.module.home.provider.message.data.CmsTopMessageV2Data;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.home.provider.product.data.CmsRecommendData;
import com.sayweee.weee.module.home.service.HomeViewModel;
import com.sayweee.weee.module.home.theme.BannerThemeHelper;
import com.sayweee.weee.module.home.theme.BannerThemeItemDecoration;
import com.sayweee.weee.module.home.theme.IndicatorHelper;
import com.sayweee.weee.module.home.zipcode.AddressManageActivity;
import com.sayweee.weee.module.home.zipcode.DeliverInfoEnkiActivity;
import com.sayweee.weee.module.launch.StoreOptionActivity;
import com.sayweee.weee.module.launch.StoreOptionFragment;
import com.sayweee.weee.module.launch.service.CategoryHelper;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedModelProvider;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.data.CmsCategoryFeedData;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.service.IPostStatus;
import com.sayweee.weee.module.post.widget.SecureTipsDialog;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.fast.WebPreloadManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.AddressPositioningConfigBean;
import com.sayweee.weee.service.config.bean.HomeConfigBean;
import com.sayweee.weee.service.helper.ProgressBarManager;
import com.sayweee.weee.service.helper.StatusHelper;
import com.sayweee.weee.service.location.GeoInfoManager;
import com.sayweee.weee.service.location.LocationUtils;
import com.sayweee.weee.service.location.SimpleLocationListener;
import com.sayweee.weee.service.timer.RfmBannerManager;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.service.timer.service.TimerChangedListener;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.ColorTools;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.SearchTextSwitcher;
import com.sayweee.weee.widget.banner.ex.MuteManager;
import com.sayweee.weee.widget.banner.ex.PlayerHandler;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.nested.ParentNestedRecyclerView;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.recycler.SafeLinearLayoutManager;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.refresh.HomeRefreshLayout;
import com.sayweee.weee.widget.tab.ITabEventDispatch;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmStatusFragment;
import com.sayweee.wrapper.http.ExceptionHandler;
import com.sayweee.wrapper.listener.OnAdapterChildClickListener;
import com.sayweee.wrapper.utils.PermissionUtils;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.io.Serializable;
import java.lang.ref.SoftReference;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  winds
 * Date:    2023/5/15.
 * Desc:
 */
public class HomeFragment extends WrapperMvvmStatusFragment<HomeViewModel> implements ITabEventDispatch, IContentFeedSharedModelProvider {

    View vStatus, layoutTop, layoutSearch, layoutSearchInner;
    CompatMagicIndicator indicatorPanel;

    View layoutEvent, layoutLogo, layoutLocation;
    ImageView ivEventEntrance, ivLogo, ivLogoArrow;
    TextView tvLocationTop, tvLocationBottom, ivLogoContent;

    TextView tvSearchHint;
    SearchTextSwitcher tvSearchTips;
    View layoutCodeTips, layoutRemindTips, tvSearch;
    HomeRefreshLayout mSmartRefreshLayout;
    ParentNestedRecyclerView rvHome;
    StatusView vCmsError;

    HomeAdapter adapter;

    private boolean refreshTopMessagesOnResume = false;

    private View bannerThemeHeader;
    private View bannerThemeContent;
    private BannerThemeHelper bannerThemeHelper;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;

    ProgressBarManager.ProgressChangedListener progressChangedListener = new ProgressBarManager.ProgressChangedListener() {
        @Override
        public void onProgressChange(int productId, @Nullable String tagType, @Nullable UpdateResultBean tagInfo) {
            ProgressBarManager.get().setProgressBar(WeeeEvent.PageView.HOME, findViewById(R.id.layout_progress), tagInfo);
        }
    };

    TimerChangedListener timerChangedListener = new TimerChangedListener() {
        @Override
        public void onRegister() {
            TimerBannerManager.get().setTimerPage(findViewById(R.id.layout_timer_banner), ExtendEvent.EVENT_PAGE_HOME);
        }

        @Override
        public void onChanged(boolean display, int hour, int min, int sec) {
            TimerBannerManager.get().setTimerInfo(findViewById(R.id.layout_timer_banner), display, hour, min, sec);
        }
    };

    public static HomeFragment newInstance() {
        return new HomeFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.HOME, String.valueOf(hashCode()));
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_home;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        StatusBarManager.setStatusBar(this, vStatus, true);
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT,
                WeeeEvent.PageView.HOME, String.valueOf(hashCode()));
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        vStatus = findViewById(R.id.v_status);
        layoutTop = findViewById(R.id.layout_top);
        layoutSearch = findViewById(R.id.layout_search);
        tvSearchTips = findViewById(R.id.tv_search_tips);
        tvSearchHint = findViewById(R.id.tv_search_hint);

        tvSearch = findViewById(R.id.tv_search_btn);
        layoutSearchInner = findViewById(R.id.layout_search_inner);
        indicatorPanel = findViewById(R.id.indicator_panel);

        ivEventEntrance = findViewById(R.id.iv_event_entrance);
        layoutEvent = findViewById(R.id.layout_event);
        ivLogoContent = findViewById(R.id.tv_logo_content);
        ivLogoArrow = findViewById(R.id.iv_logo_arrow);
        ivLogo = findViewById(R.id.iv_logo);
        layoutLogo = findViewById(R.id.layout_logo);
        layoutLocation = findViewById(R.id.layout_location);
        tvLocationTop = findViewById(R.id.tv_location_top);
        tvLocationBottom = findViewById(R.id.tv_location_bottom);

        layoutCodeTips = findViewById(R.id.layout_code_tips);
        layoutRemindTips = findViewById(R.id.layout_remind_tips);
        mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        rvHome = findViewById(R.id.mRecyclerView);

        vCmsError = findViewById(R.id.v_cms_error);

        setOnClickListener(new OnSafeClickListener() {
                               @Override
                               public void onClickSafely(View v) {
                                   click(v);
                               }
                           }, R.id.iv_tips_action, R.id.layout_top, R.id.tv_remind_revoke, R.id.tv_search_tips, R.id.tv_search_hint, R.id.iv_camera, R.id.tv_search_btn,
                R.id.layout_location, R.id.layout_logo);

        rvHome.setLayoutManager(new SafeLinearLayoutManager(activity));
        rvHome.addItemDecoration(new SimpleSectionItemDecoration());
        rvHome.setHasFixedSize(true);
        rvHome.setItemViewCacheSize(10);
        rvHome.getRecycledViewPool().setMaxRecycledViews(CartAdapter.TYPE_PANEL, 5);

        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);
        adapter = new HomeAdapter();
        adapter.setScrollStatePersist(scrollStatePersist);
        rvHome.setAdapter(adapter);
        adapter.setPageTarget(this);
        adapter.setPreLoadNumber(3);

        adapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                viewModel.fetchMoreData();
            }
        }, rvHome);

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        adapter.setOnLightingDealTimerListener(new CmsComponentTimerHandler(viewLifecycleOwner) {
            @Override
            public void onEndSafely(String componentId) {
                super.onEndSafely(componentId);
                if (!EmptyUtils.isEmpty(componentId)) {
                    CmsDataSource dataSource = viewModel.getDataSource(componentId);
                    if (dataSource != null) {
                        viewModel.requestMultiDataSource(dataSource, 1500L);
                        return;
                    }
                }
                onPagerRefresh(/* isSilent= */true, /* refreshStore= */false, /* isOnlyData= */true);
            }
        });

        adapter.setOnTopMessageTimerListener(new CmsComponentTimerHandler(viewLifecycleOwner) {
            @Override
            public void onEndSafely(String componentId) {
                super.onEndSafely(componentId);
                if (viewModel != null) {
                    viewModel.refreshTopMessage(componentId, 1000L);
                }
            }
        });

        adapter.setOnRemindListener(new OnRemindListener() {

            @Override
            public void onRemind(LightningDealsProductBean bean, boolean remind, int position) {
                if (remind) {
                    showRemindTips(bean.id);
                } else {
                    setRemindEquals(bean.id);
                }
                viewModel.changeLightningDealsRemind(bean.id, remind);
            }
        });

        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                Object item = adapter.getItem(position);
                if (item instanceof CmsTopMessageV2Data) {
                    CmsTopMessageV2Data messageV2Data = (CmsTopMessageV2Data) item;
                    if (view.getId() == R.id.iv_icon_beside_left || view.getId() == R.id.tv_beside_left) {
                        showTopMsgTips(messageV2Data.getBesideLeft());
                        EagleTrackManger.get().trackEagleClickAction(messageV2Data.getEventKey(), messageV2Data.position
                                , null
                                , -1
                                , String.valueOf(messageV2Data.getBesideLeft().id)
                                , 0
                                , messageV2Data.getEventKey()
                                , EagleTrackEvent.ClickType.VIEW);
                    } else if (view.getId() == R.id.iv_icon_beside_right || view.getId() == R.id.tv_beside_right) {
                        showTopMsgTips(messageV2Data.getBesideRight());
                        EagleTrackManger.get().trackEagleClickAction(messageV2Data.getEventKey(), messageV2Data.position
                                , null
                                , -1
                                , String.valueOf(messageV2Data.getBesideRight().id)
                                , 1
                                , messageV2Data.getEventKey()
                                , EagleTrackEvent.ClickType.VIEW);
                    }
                }
            }
        });

        adapter.setCmsMultiDataSourceListener(dataSource -> viewModel.requestMultiDataSource(dataSource));

        setScrollConfig();
        setRefreshConfig();
        setTrendingPostListener();
        setupBannerTheme();
        geoCheck();
        setEventEntrance();

        WebPreloadManager.getInstance(requireContext())
                .preloadDelay(5000, WebPreloadManager.URL.PREHEAT);

        new ProductTraceObserver(this) {
            @Override
            protected void handleProductSalesTraceChange() {
                ProductTraceViewHelper.notify(rvHome);
            }
        }.setExtraTopic(WeeeEvent.PageView.HOME).start();
    }

    @Override
    public void loadData() {
        fillTitleData();
        showVeilTemplate(true);
        WeeeMonitor.getInstance().initAppEnd(WeeeMonitor.KEY_START);
        if (loadDataEnable()) {
            WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD,
                    WeeeEvent.PageView.HOME, String.valueOf(viewModel.hashCode()));
            viewModel.onRefresh(true, true, true);
        } else {
            viewModel.readCache();
        }
        CategoryHelper.get().preloadCategory();
    }

    @Override
    public void attachModel() {
        SharedOrderViewModel.get().preOrderData.observe(this, new Observer<Long>() {
            @Override
            public void onChanged(Long aLong) {
                fillTitleData();
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                showVeilTemplate(true);
                scrollTop();

                MuteManager.get().recoverStatus();
                PlayerHandler.notifyLifecycleStatusChanged(rvHome, /* isResume= */false);

                refreshTopMessagesOnResume = false;
                viewModel.onRefresh(
                        /* readCache= */false,
                        /* refreshStore= */Constants.OrderRecreateType.ACCOUNT_CHANGED == integer,
                        /* isSilent= */true
                );

                SharedViewModel.get().refreshAccountInfo();
                if (Constants.OrderRecreateType.FORCE == integer) {
                    //强制切换时刷新订单
                    OrderProvider.get().refreshSimplePreOrder();
                }
                if (Constants.OrderRecreateType.STORE_CHANGED == integer) {
                    fillTitleData();
                    setEventEntrance();
                }
            }
        });

        SharedViewModel.get().followChangeData.observe(this, new Observer<ArrayMap<String, Integer>>() {
            @Override
            public void onChanged(ArrayMap<String, Integer> map) {
                if (!EmptyUtils.isEmpty(map) && adapter != null) {
                    trendingPostDataUpdated(map);
                }
            }
        });

        //post点赞联动
        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                adapter.toggleCollect(map);
            }
        });

        viewModel.storeChangedData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                fillTitleData();
                setEventEntrance();
            }
        });

        //刷新状态
        viewModel.refreshStatusData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean flag) {
                if (flag != null && !flag) {
                    showVeilTemplate(false);
                    setRefreshFinish();
                }
            }
        });

        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                if (scrollStatePersist != null) {
                    scrollStatePersist.clearScrollState();
                }
                refreshTopMessagesOnResume = true;
                invalidateBannerTheme(list);
                adapter.setAdapterData(list);
                removeStatus();
                removeCmsError();
                dispatchData();
                MuteManager.get().recoverStatus();
                if (isSupportVisible()) {
                    adapter.notifyPageDataSetChanged(rvHome);
                    PlayerHandler.notifyAdapterDataChanged(rvHome);
                }
            }
        });

        viewModel.adapterAppendData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                adapter.addData(list);
                adapter.loadMoreComplete();
            }
        });

        viewModel.loadMoreFinished.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                adapter.loadMoreEnd(true);
            }
        });

        viewModel.cacheData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> adapterHomeData) {
                adapter.setAdapterData(adapterHomeData);
                setRefreshFinish();
                removeStatus();
                dispatchData();
                showVeilTemplate(false);
            }
        });

        viewModel.adapterItemData.observe(this, new Observer<AdapterDataType>() {
            @Override
            public void onChanged(AdapterDataType data) {
                int index = adapter.getData().indexOf(data);
                if (index >= 0) {
                    adapter.notifyItemChanged(index, data);
                }
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                int errorCode = failureBean != null ? failureBean.getErrorCode() : ExceptionHandler.ERROR_UNKNOWN;
                boolean showError = adapter == null || adapter.getItemCount() <= 0;
                if (ExceptionHandler.isConnectError(errorCode)) {
                    StatusHelper.showStatus(getStatusManager(), failureBean, rvHome, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            onPagerRefresh(false, true, false);
                        }
                    });
                } else if (showError) {
                    showCmsError();
                }
            }
        });

        viewModel.geoConflictData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean show) {
                if (Boolean.TRUE.equals(show)) {
                    showGeoDiffer();
                }
            }
        });

        viewModel.requestRefresh.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                viewModel.onRefresh(false, true, true);
            }
        });

        viewModel.remindData.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> map) {
                refreshRemindSet(map);
            }
        });

        viewModel.componentDataUpdateLiveData.observe(this, adapter::notifyMultiDataSourceUpdate);

        viewModel.dialogData.observe(this, new Observer<List<BesideTipsBean>>() {
            @Override
            public void onChanged(List<BesideTipsBean> list) {
                if (CollectionUtils.isNotEmpty(list)) {
                    new SecureTipsDialog(activity).setBottomData(list).show();
                }
            }
        });
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        AppAnalytics.logPageView(WeeeEvent.PageView.HOME, this);
        adapter.onPageResume(rvHome);
        PopupCenterManager.get().onPageResumed(ExtendEvent.EVENT_PAGE_HOME);
        ProgressBarManager.get().registerProgressChangedListener(this.progressChangedListener);
        TimerBannerManager.get().registerAndLog(timerChangedListener, findViewById(R.id.layout_timer_banner));
        EagleTrackManger.get().resetHomeTrackData();
        tvSearchTips.startSwitch();
        refreshTopMessageOnResume();
        PreSaleManager.get().checkSaleStatus();
    }

    private void refreshTopMessageOnResume() {
        if (refreshTopMessagesOnResume && viewModel != null) {
            viewModel.refreshAllTopMessages(1000L); // 切换页面刷新所有 top message 相关 component
        }
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause(rvHome);
        ProgressBarManager.get().unregisterProgressChangedListener(this.progressChangedListener);
        TimerBannerManager.get().unregisterTimerChangedListener(timerChangedListener);
        RfmBannerManager.get().unregisterListener();
        tvSearchTips.stopSwitch(false);
        hideGeoDiffer();
    }

    @Override
    public void onDestroyView() {
        LocationUtils.stopLocation(listener);
        super.onDestroyView();
        StatusBarManager.setStatusBarDestroy(this);
    }

    @Override
    public void onTabDoubleTap() {
        ViewTools.smoothScrollToPosition(rvHome);
    }

    protected void click(View view) {
        int id = view.getId();
        if (id == R.id.layout_location) {
            if (StoreManager.get().hasStore() && OrderManager.get().isSupportChangeDate()) {
                startActivity(DeliverInfoEnkiActivity.getIntent(activity, WeeeAnalytics.get().getCleanPageKey()));
            } else {
                startActivity(AddressManageActivity.getIntent(activity, WeeeAnalytics.get().getCleanPageKey()));
            }
            EagleTrackManger.get().trackEagleClickAction("delivery_option", -1, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
        } else if (id == R.id.layout_logo) {
            if (StoreManager.get().hasStore()) {
                showStoreSelection(false);
                EagleTrackManger.get().trackEagleClickAction(StoreManager.get().getStoreKey(), -1, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
            } else {
                if (OrderManager.get().isSupportChangeDate()) {
                    startActivity(DateActivity.getIntent(activity, null, WeeeAnalytics.get().getCleanPageKey()));
                }
            }
        } else if (id == R.id.layout_top) {
            ViewTools.smoothScrollToPosition(rvHome);
        } else if (id == R.id.iv_tips_action) {
            hideGeoDiffer();
        } else if (id == R.id.tv_remind_revoke) {
            revokeRemind();
        } else if (id == R.id.tv_search_hint) {
            startActivity(SearchPanelActivity.getIntent(activity, null, tvSearchHint.getText() != null ? tvSearchHint.getText().toString() : null));
        } else if (id == R.id.tv_search_tips) {
            startActivity(SearchPanelActivity.getIntent(activity, null, tvSearchTips.getKeyword()));
        } else if (id == R.id.tv_search_btn) {
            String keyword = tvSearchTips.getKeyword();
            if (!EmptyUtils.isEmpty(keyword)) {
                Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, keyword, null);
                EagleTrackManger.get().trackEagleClickAction(null,
                        -1,
                        null,
                        -1,
                        "search",
                        -1,
                        EagleTrackEvent.TargetType.NORMAL_BUTTON,
                        EagleTrackEvent.ClickType.VIEW,
                        ctx);
                startActivity(SearchPanelActivity.getIntent(activity, keyword, null));//带入关键字，直接到达搜索结果页面
            }
        }
    }

    // refresh
    public void setRefreshConfig() {
        setRefreshEnable(true);
        if (mSmartRefreshLayout != null) {
            mSmartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
                @Override
                public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                    onPagerRefresh(true, false, false);
                }
            });
        }
    }

    public void setRefreshFinish() {
        if (mSmartRefreshLayout != null && !mSmartRefreshLayout.isInAnimationOnly()) {
            mSmartRefreshLayout.finishRefresh();
        }
    }

    private void onPagerRefresh(boolean isSilent, boolean refreshStore, boolean isOnlyData) {
        if (viewModel != null) {
            viewModel.onRefresh(false, refreshStore, isSilent);
        }
        if (!isOnlyData) {
            SharedViewModel.get().refreshAccountInfo();
            OrderProvider.get().refreshSimplePreOrder();
            CollectManager.get().forceRefreshProductCollect();
        }
    }

    private void setRefreshEnable(boolean enable) {
        if (rvHome != null) {
            RecyclerView.LayoutManager manager = rvHome.getLayoutManager();
            if (manager instanceof SafeLinearLayoutManager) {
                ((SafeLinearLayoutManager) manager).setScrollVerticallyEnable(enable);
            }
        }
        if (mSmartRefreshLayout != null) {
            mSmartRefreshLayout.setEnableRefresh(enable);
        }
    }

    public void onPageSelected(Map<String, Object> params) {
        Object o = params.get("home");
        if (o instanceof String) {
            String key = (String) o;
            if (Constants.ContentPageKey.STORE_SELECTION.equalsIgnoreCase(key)) {
                showStoreSelection(true);
            }
        }
    }

    private void showVeilTemplate(boolean visible) {
        VeilLayout vlHome = findViewById(R.id.vl_home);
        showVeilTemplate(vlHome, visible);
        setRefreshEnable(!visible);
        if (visible) {
            bannerThemeHelper.setEnabled(false);
        }
    }

    private void showVeilTemplate(VeilLayout veil, boolean visible) {
        if (veil != null) {
            if (visible) {
                veil.requestLayout();
                veil.setVisibility(View.VISIBLE);
                veil.veil();
            } else {
                veil.unVeil();
                veil.setVisibility(View.INVISIBLE);
            }
        }
    }

    private void fillTitleData() {
        SimplePreOrderBean preOrder = OrderManager.get().getSimplePreOrder();
        if (preOrder != null) {
            boolean hasStore = StoreManager.get().hasStore();
            String date = OrderManager.get().getEtaDateDesc();
            boolean hasDate = !EmptyUtils.isEmpty(date);
            if (hasStore) {
                ivLogoContent.setText(getString(R.string.s_store_title, StoreManager.get().getStoreName()));
                TalkBackHelper.setContentDesc(layoutLogo, getString(R.string.a_current_store_intro, StoreManager.get().getStoreName()));
                tvLocationTop.setText(preOrder.addr_zipcode);
                tvLocationBottom.setText(hasDate ? date : preOrder.addr_city);
                if (hasDate) {
                    // zipcode + date
                    TalkBackHelper.setContentDesc(layoutLocation, getString(R.string.a_address_date_intro, preOrder.addr_zipcode, date));
                } else {
                    // zipcode + city
                    TalkBackHelper.setContentDesc(layoutLocation, getString(R.string.a_current_address_intro, preOrder.addr_zipcode + " " + preOrder.addr_city));
                }
            } else {
                if (hasDate) {
                    ivLogoContent.setText(date);
                    TalkBackHelper.setContentDesc(layoutLogo, getString(R.string.a_current_date_intro, date));
                } else {
                    ivLogoContent.setText(null);
                    TalkBackHelper.setContentDesc(layoutLogo, null);
                }
                tvLocationTop.setText(preOrder.addr_zipcode);
                tvLocationBottom.setText(preOrder.addr_city);
                // zipcode + city
                TalkBackHelper.setContentDesc(layoutLocation, getString(R.string.a_current_address_intro, preOrder.addr_zipcode + " " + preOrder.addr_city));
            }
            ivLogoContent.setVisibility(hasStore || hasDate ? View.VISIBLE : View.GONE);
            ivLogoArrow.setVisibility(hasStore || OrderManager.get().isSupportChangeDate() ? View.VISIBLE : View.GONE);
            // lackOfStore
            if (StoreManager.get().lackOfStore()) {
                startActivity(StoreOptionActivity.getIntent(activity));
            }
        }
    }

    private void scrollTop() {
        if (rvHome != null && adapter != null && adapter.getItemCount() > 0) {
            rvHome.scrollToPosition(0);
        }
    }

    private void updateBannerTheme(boolean isAtTop) {
        Drawable headerDrawable;
        if (isAtTop) {
            headerDrawable = bannerThemeHelper.getHeaderDrawable();
        } else {
            headerDrawable = bannerThemeHelper.getDefaultHeaderDrawable();
        }
        bannerThemeHeader.setBackground(headerDrawable);
    }

    private void setAtTop(boolean visible) {
        layoutTop.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    private void setSearchBarDisplay(boolean visible) {
        if (visible && adapter.getSearchBarIndex() >= 0) {
            layoutSearch.setVisibility(View.VISIBLE);
            findViewById(R.id.shadow_search_layout).setVisibility(View.VISIBLE);
        } else {
            layoutSearch.setVisibility(View.GONE);
            findViewById(R.id.shadow_search_layout).setVisibility(View.GONE);
        }
    }

    private void setPanelIndicatorDisplay(boolean visible) {
        if (visible && adapter.getRecommendIndex() >= 0) {
            indicatorPanel.setVisibility(View.VISIBLE);
            findViewById(R.id.shadow_indicator_panel).setVisibility(View.VISIBLE);
        } else {
            indicatorPanel.setVisibility(View.GONE);
            findViewById(R.id.shadow_indicator_panel).setVisibility(View.GONE);
        }
    }

    private void setStickyListener() {
        rvHome.setStickyListener(new ParentNestedRecyclerView.StickyListener() {
            @Override
            public void onSticky(boolean isAtTop) {
                boolean displayStickyRecommend = adapter.displayStickyRecommend();
                //猜你喜欢吸顶 feed不吸顶 默认吸顶
                setPanelIndicatorDisplay(displayStickyRecommend && isAtTop); //支持吸顶时 && 组件吸顶时展示
                setSearchBarDisplay(!displayStickyRecommend || !isAtTop); //不支持吸顶时 ｜｜ 组件不吸顶时展示
            }
        });
    }

    private void setScrollConfig() {
        LinearLayoutManager manager = (LinearLayoutManager) rvHome.getLayoutManager();
        rvHome.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                boolean canScrollUp = recyclerView.canScrollVertically(-1);
                updateBannerTheme(!canScrollUp);

                if (manager != null) {
                    int firstVisibleItemPosition = manager.findFirstVisibleItemPosition();
                    setAtTop(firstVisibleItemPosition > 3);
//                    if (!rvHome.isStickyTop()) {
                    {
                        boolean visible = false;
                        int index = adapter.getSearchBarIndex();
                        if (firstVisibleItemPosition >= index) {
                            visible = true;
                            if (firstVisibleItemPosition == index) {
                                View view = manager.findViewByPosition(index);
                                visible = view != null && view.getY() < 0;
                            }
                        }
                        setSearchBarDisplay(visible);
                    }
                    /*//避免刷新后 状态不一致的问题
                    if (firstVisibleItemPosition < adapter.getRecommendIndex()) {
                        setPanelIndicatorDisplay(false);
                    }*/
                    if (Constants.CanShow$5.show && !EmptyUtils.isEmpty(HomePopHelper.getPopWindow())) {
                        if (postViewReference == null || postViewReference.get() == null) {
                            return;
                        }
                        View postView = postViewReference.get();
                        boolean visible = false;
                        int index = adapter.getTrendingPostIndex();
                        View view = manager.findViewByPosition(index);
                        if (firstVisibleItemPosition >= index) {
                            visible = true;
                            if (firstVisibleItemPosition == index) {
                                visible = view != null && view.getY() < 0;
                            }
                        } else {
                            visible = view == null || view.getTop() <= 250;
                        }
                        if (!visible) {
                            //做显示布局操作
                            HomePopHelper.getPopWindow().getPopupWindow().update(postView, postView.getScrollX(), CommonTools.dp2px(-80), ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                        } else {
                            //做隐藏布局操作
                            HomePopHelper.getPopWindow().getPopupWindow().dismiss();
                            HomePopHelper.clearPopWindow();
                        }
                    }
                }
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (adapter != null) {
                        adapter.onPageScrollStateChanged(recyclerView, newState);
                    }
                    PlayerHandler.notifyScrollStateChanged(rvHome);
                    LabelScrollHandler.notifyScrollStateChanged(recyclerView);
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });

        adapter.setOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                indicatorPanel.getIndicatorHelper().handlePageSelected(position);
            }
        });
    }

    private SoftReference<View> postViewReference;

    private void setTrendingPostListener() {
        adapter.setOnPostPopVisibleListener(new TrendingPostProvider.OnPostPopVisibleListener() {
            @Override
            public void onPopVisible(View view, String promoted_featured_title) {
                if (EmptyUtils.isEmpty(HomePopHelper.getPopWindow())) {
                    postViewReference = new SoftReference<>(view);
                    HomePopHelper.showSelectPopWindow(activity, view
                            , promoted_featured_title);
                }
            }
        });
    }

    private void showRemindTips(int id) {
        if (layoutRemindTips != null) {
            layoutRemindTips.removeCallbacks(hideRemindTipsRunnable);
            layoutRemindTips.postDelayed(hideRemindTipsRunnable, 3000);
            layoutRemindTips.setTag(id);
            layoutRemindTips.setVisibility(View.VISIBLE);
        }
    }

    private void setRemindEquals(int id) {
        if (layoutRemindTips != null && layoutRemindTips.getVisibility() == View.VISIBLE) {
            Object tag = layoutRemindTips.getTag();
            if (tag instanceof Integer) {
                if ((Integer) tag == id) {
                    hideRemindTips();
                }
            }
        }
    }

    private final Runnable hideRemindTipsRunnable = new Runnable() {
        @Override
        public void run() {
            hideRemindTips();
        }
    };

    private void dispatchData() {
        postEvent(new Runnable() {
            @Override
            public void run() {
                setSearchBarData();
                setPanelIndicator();
            }
        });
    }

    private void postEvent(Runnable runnable) {
        View view = getView();
        if (view != null) {
            view.post(runnable);
        }
    }

    private void setSearchBar(CmsSearchBarData item) {
        List<String> keywords = item.getKeywords();
        if (tvSearchTips != null) {
            tvSearchTips.stopSwitch(true);
            if (!EmptyUtils.isEmpty(keywords)) {
                tvSearchTips.setVisibility(View.VISIBLE);
                tvSearchHint.setVisibility(View.GONE);
                tvSearchTips.bindData(keywords);
                if (keywords.size() == 1) {
                    tvSearchTips.setCurrentText(keywords.get(0));
                } else {
                    tvSearchTips.startSwitch();
                }
                tvSearch.setVisibility(View.VISIBLE);
            } else {
                tvSearchTips.setVisibility(View.GONE);
                tvSearch.setVisibility(View.GONE);
                //tvSearchTips.setCurrentText(item.property.tips);
                tvSearchHint.setVisibility(View.VISIBLE);
                tvSearchHint.setText(item.property.tips);
            }
        }
    }

    private void setSearchBarData() {
        AdapterDataType data = adapter.getTargetData(CmsItemType.SEARCH_BAR);
        if (data instanceof CmsSearchBarData) {
            setSearchBar(((CmsSearchBarData) data));
        } else {
            setSearchBarDisplay(false);
        }
    }

    private void setPanelIndicator() {
        AdapterDataType data = adapter.getTargetData(CmsItemType.RECOMMEND);
        if (data instanceof CmsRecommendData) {
            int bgColor = ContextCompat.getColor(activity, R.color.text_main);
            int textColor = Color.WHITE;
            int normalColor = ContextCompat.getColor(activity, R.color.text_lesser);
            List<String> list = ((CmsRecommendData) data).property.validTab;
            IndicatorHelper.fillHomePanelIndicator(activity, indicatorPanel, list, bgColor, textColor, normalColor, new OnAdapterChildClickListener() {
                @Override
                public void onAdapterChildClick(View view, int position) {
                    indicatorPanel.getIndicatorHelper().handlePageSelected(position);
                    ViewPager2 pager2 = rvHome.getInnerViewPager2();
                    if (pager2 != null) {
                        pager2.setCurrentItem(position);
                    }
                }
            });
            indicatorPanel.getIndicatorHelper().setPageSelected(adapter.getPagerIndex());
        }
    }

    private void setFeedIndicator() {
        AdapterDataType data = adapter.getTargetData(CmsItemType.CONTENT_FEED);
        if (data instanceof CmsCategoryFeedData) {
            int bgColor = ContextCompat.getColor(activity, R.color.text_main);
            int textColor = Color.WHITE;
            int normalColor = ContextCompat.getColor(activity, R.color.text_lesser);
            CmsContentFeedPacket packet = ((CmsCategoryFeedData) data).getData();
            List<String> list = null;
            if (packet != null && packet.property != null && !EmptyUtils.isEmpty(packet.property.title)) {
                list = Collections.singletonList(packet.property.title);
            }
            IndicatorHelper.fillHomePanelIndicator(activity, indicatorPanel, list, bgColor, textColor, normalColor, new OnAdapterChildClickListener() {
                @Override
                public void onAdapterChildClick(View view, int position) {
                    indicatorPanel.getIndicatorHelper().handlePageSelected(position);
                    ViewPager2 pager2 = rvHome.getInnerViewPager2();
                    if (pager2 != null) {
                        pager2.setCurrentItem(position);
                    }
                }
            });
            indicatorPanel.getIndicatorHelper().setPageSelected(adapter.getPagerIndex());
        }
    }


    private void revokeRemind() {
        if (layoutRemindTips != null) {
            Object tag = layoutRemindTips.getTag();
            if (tag instanceof Integer) {
                adapter.revokeRemind((Integer) tag);
                hideRemindTips();
                viewModel.changeLightningDealsRemind((Integer) tag, false);
            }
        }
    }

    private void hideRemindTips() {
        if (layoutRemindTips != null) {
            layoutRemindTips.removeCallbacks(hideRemindTipsRunnable);
            layoutRemindTips.setVisibility(View.GONE);
        }
    }

    private void trendingPostDataUpdated(ArrayMap<String, Integer> map) {
        for (int i = 0; i < map.size(); i++) {
            String uid = map.keyAt(i);
            if (EmptyUtils.isEmpty(uid)) {
                return;
            }
            Integer status = map.get(uid);
            if (status == null) {
                return;
            }
            List<AdapterDataType> dataList = adapter.getData();
            for (int index = 0; index < dataList.size(); index++) {
                AdapterDataType sectionData = dataList.get(index);
                if (sectionData instanceof CmsTrendingPostData) {
                    CmsTrendingPostData data = (CmsTrendingPostData) sectionData;
                    List<TrendingPostBean.TrendingPostResponsesBean> tabs = data.t.valid;
                    for (int j = 0; j < tabs.size(); j++) {
                        List<PostBean> postList = tabs.get(j).posts;
                        for (int z = 0; z < postList.size(); z++) {
                            PostBean bean = postList.get(z);
                            if (uid.equalsIgnoreCase(bean.uid) && status != IPostStatus.STATUS_UNDEFINED) {
                                bean.social_status = status == IPostStatus.STATUS_BLOCKED ? IPostStatus.BLOCKED : status == IPostStatus.STATUS_FOLLOWED ? "Followed" : "unFollowed";
                                adapter.notifyItemChanged(index, data);
                            }
                        }
                    }
                }
            }
        }
    }

    private void geoCheck() {
        AddressPositioningConfigBean config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.ADDRESS_POSITIONING);
        if (config != null && config.isHomePositioningEnable()) {
            String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION};
            PermissionUtils.checkMorePermissions(activity, permissions, new PermissionUtils.PermissionCheckCallBack() {
                @Override
                public void onHasPermission() {
                    startLocation();
                }

                @Override
                public void onUserHasAlreadyTurnedDown(String... permission) {
                }

                @Override
                public void onUserHasAlreadyTurnedDownAndNotAsk(String... permission) {
                }
            });
        }
    }

    SimpleLocationListener listener;

    private void startLocation() {
        if (!CommonTools.isGpsOpen(activity)) {
            return;
        }
        if (listener == null) {
            listener = new SimpleLocationListener() {
                @Override
                public void onLocationSuccess(Location location) {
                    LocationUtils.stopLocation(listener);
                    if (viewModel != null && !GeoInfoManager.get().hasLocatedOnHome()) {
                        viewModel.geoCheckHome(location.getLongitude(), location.getLatitude());
                    }
                }

                @Override
                public void onLocationFailed(Exception e) {
                    super.onLocationFailed(e);
                    LocationUtils.stopLocation(listener);
                }
            };
        }
        LocationUtils.startLocation(listener);
    }

    private void showGeoDiffer() {
        layoutCodeTips.postDelayed(hideCodeTipsRunnable, 5 * 1000L);
        layoutCodeTips.setVisibility(View.VISIBLE);
        GeoInfoManager.get().setHasLocatedOnHome(true);
    }

    private void refreshRemindSet(Map<String, Object> map) {
        for (AdapterDataType item : adapter.getData()) {
            if (item instanceof CmsLightingDealsData) {
                for (LightningDealsProductBean productBean : ((CmsLightingDealsData) item).t.products) {
                    if (map.get("product_id") != null && Objects.equals(map.get("product_id"), productBean.id)) {
                        Object remind = map.get("remind");
                        if (remind instanceof Boolean) {
                            productBean.remind_set = (boolean) remind;
                        }
                    }
                }
            }
        }
    }

    private final Runnable hideCodeTipsRunnable = new Runnable() {
        @Override
        public void run() {
            hideGeoDiffer();
        }
    };

    private void hideGeoDiffer() {
        if (layoutCodeTips != null) {
            layoutCodeTips.removeCallbacks(hideCodeTipsRunnable);
            layoutCodeTips.setVisibility(View.GONE);
        }
    }

    @Override
    public IContentFeedSharedViewModel getContentFeedSharedViewModel() {
        return viewModel;
    }

    private boolean loadDataEnable() {
        if (DeepLinkManager.get().isCurrentValid()) {
            viewModel.requestDelayLoadData();
            return false;
        }
        return true;
    }

    private void showTopMsgTips(TopMessageV2ItemBean beside) {
        if (beside != null) {
            if (beside.link_type == 2) {
                viewModel.getBesideDialog();
            } else {
                startActivity(WebViewActivity.getIntent(activity, beside.link));
            }
        }
    }

    private void showStoreSelection(boolean isAutoPopup) {
        String tag = StoreOptionFragment.class.getSimpleName();
        StoreOptionFragment storeOptionFragment = StoreOptionFragment.newInstance(isAutoPopup);
        storeOptionFragment.show(getChildFragmentManager(), tag);
        storeOptionFragment.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                AppAnalytics.logPageView(WeeeEvent.PageView.HOME, HomeFragment.this);
                openFtuCheck();
            }
        });
    }

    private void openFtuCheck() {
        if (DeepLinkManager.get().isCurrentValid()) {
            startActivity(WebViewActivity.getIntent(activity, DeepLinkManager.get().getOnceLink()));
        }
    }

    private void setupBannerTheme() {
        bannerThemeHelper = new BannerThemeHelper(activity);

        bannerThemeHeader = findViewById(R.id.v_banner_theme_header);
        bannerThemeHeader.setBackground(bannerThemeHelper.getHeaderDrawable());
        bannerThemeContent = findViewById(R.id.v_banner_theme_content);
        bannerThemeContent.setBackground(bannerThemeHelper.getContentDrawable());

        BannerThemeItemDecoration bannerThemeItemDecoration = new BannerThemeItemDecoration();
        bannerThemeItemDecoration.setOnBannerThemeBoundsChangeListener(new BannerThemeItemDecoration.OnBannerThemeBoundsChangeListener() {

            @Override
            public void onBannerThemeBoundsChange(int height) {
                if (height <= 0) {
                    return;
                }
                bannerThemeHelper.setHeight(bannerThemeHeader.getHeight(), height);
                ViewTools.updateViewSize(bannerThemeContent, null, height);
            }

            @Override
            public void onBannerThemeOffsetChange(int offsetY) {
                bannerThemeContent.setTranslationY(Math.min(offsetY, 0));
            }

        });
        rvHome.addItemDecoration(bannerThemeItemDecoration);

        adapter.setOnBannerPageChangeListener((position, item) -> {
            if (item == null) {
                return;
            }
            HomeBannerData bannerItem = CollectionUtils.getOrNull(item.t.getBannerData(), position);
            String themeColor = bannerItem != null ? bannerItem.t.color : null;
            int themeColorInt = ViewTools.parseColor(themeColor, Color.WHITE);
            int themeColorOpacity = ColorTools.blend(themeColorInt, Color.WHITE);
            bannerThemeHelper.setSolidColorAnimate(themeColorOpacity);
        });
    }

    private void invalidateBannerTheme(List<AdapterDataType> dataList) {
        List<Integer> itemTypes = CollectionUtils.map(dataList, AdapterDataType::getType);
        boolean isValid = bannerThemeHelper.isValid(itemTypes);
        bannerThemeHelper.setEnabled(isValid);
    }

    private void setEventEntrance() {
        HomeConfigBean dynamicConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.HOME);
        HomeConfigBean.TopBarBean topBar = dynamicConfig != null ? dynamicConfig.top_bar : null;
        String icon = null;
        if (topBar != null) {
            if (topBar.extra != null) {
                if (StoreManager.get().hasStore()) {
                    String key = StoreManager.get().getStoreKey();
                    icon = topBar.extra.get(key);
                }
                if (EmptyUtils.isEmpty(icon)) {
                    String key = LanguageManager.get().getLanguage();
                    icon = topBar.extra.get(key);
                }
            }
            icon = !EmptyUtils.isEmpty(icon) ? icon : topBar.icon;
        }
        boolean showIcon = !EmptyUtils.isEmpty(icon) && !DevConfig.isFlavorLatino();
        ViewTools.setViewVisible(layoutEvent, showIcon);
        if (showIcon) {
            TalkBackHelper.setContentDesc(ivEventEntrance, getString(R.string.a_view_event));
            ImageLoader.load(ivEventEntrance.getContext(), ivEventEntrance, WebpManager.get().getConvertUrl(ImageSpec.SPEC_64, icon), R.mipmap.ten_th_sub_brand_default);
            ViewTools.setViewOnSafeClickListener(ivEventEntrance, v -> toLink(topBar.link));
            if (!EmptyUtils.isEmpty(topBar.link)) {
                TalkBackHelper.setContentDesc(ivEventEntrance, getString(R.string.a_view_event));
            } else {
                TalkBackHelper.disableAccessibility(ivEventEntrance);
            }
        } else {
            TalkBackHelper.setContentDesc(ivEventEntrance, null);
        }
    }

    private void toLink(String link) {
        if (!EmptyUtils.isEmpty(link)) {
            startActivity(WebViewActivity.getIntent(activity, link));
        }
    }

    private void showCmsError() {
        setRefreshFinish();
        showVeilTemplate(false);
        ViewTools.setViewVisibilityIfChanged(vCmsError, true);
        vCmsError.showErrorView(v -> {
            removeCmsError();
            showVeilTemplate(true);
            onPagerRefresh(true, true, false);
        }, R.layout.layout_status_load_failed);
    }

    private void removeCmsError() {
        ViewTools.setViewVisibilityIfChanged(vCmsError, false);
        vCmsError.resetStatus();
    }

}
