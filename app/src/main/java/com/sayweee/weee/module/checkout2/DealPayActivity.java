package com.sayweee.weee.module.checkout2;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelKt;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.service.impl.payment.agents.BraintreeCardPaymentAgent;
import com.sayweee.service.impl.payment.bean.PaymentAgentRequest;
import com.sayweee.service.payment.bean.PaymentChannel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cart.widget.CheckOutBottomView;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.service.CheckOutSectionViewModel;
import com.sayweee.weee.module.checkout2.adapter.OnPurchaseChannelActionListener;
import com.sayweee.weee.module.checkout2.bean.CheckoutV4Bean;
import com.sayweee.weee.module.checkout2.bean.DealPayV2Bean;
import com.sayweee.weee.module.checkout2.data.CheckoutPurchaseChannelData;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.module.checkout2.data.EbtAmountArgs;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelExtraData;
import com.sayweee.weee.module.checkout2.pm.PmEditActivity;
import com.sayweee.weee.module.order.bean.OrderCategory;
import com.sayweee.weee.module.order.list.OrderListActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.handler.HandlerHelper;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.pay.PaymentCallbackHelper;
import com.sayweee.weee.service.pay.PaymentHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.weee.widget.snackbar.ActionToastView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.toast.ToastOptions;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.utils.KeyboardUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;

import java.util.List;
import java.util.Map;

import kotlin.Unit;

public class DealPayActivity extends WrapperMvvmActivity<DealPayViewModel<BaseLoaderModel<OrderApi>>> {

    private static final String EXTRA_CHECKOUT_ID = "EXTRA_CHECKOUT_ID";
    private static final String EXTRA_MSG = "EXTRA_MSG";

    public static Intent getIntent(Context context, String checkoutId, String msg) {
        return new Intent(context, DealPayActivity.class)
                .putExtra(EXTRA_CHECKOUT_ID, checkoutId)
                .putExtra(EXTRA_MSG, msg);
    }

    private RecyclerView rvList;
    private View vShadow;
    private SmartRefreshLayout mSmartRefreshLayout;
    private TextView tvPoints;
    private CheckOutBottomView bottomView;

    private CheckOutSectionAdapter adapter;
    private EagleImpressionTrackerIml eagleImpressionTracker;
    private KeyboardChangeHelper keyboardChangeHelper;

    private int refreshFlag = AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE;

    private ActivityResultLauncher<Intent> purchaseChannelLauncher;

    protected String getExtraCheckoutId() {
        return getIntent() != null ? getIntent().getStringExtra(EXTRA_CHECKOUT_ID) : null;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_dealpay;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        initWrapperTitle();

        vShadow = findViewById(R.id.v_shadow);
        rvList = findViewById(R.id.rv_list);
        tvPoints = findViewById(R.id.tv_points);
        mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        mSmartRefreshLayout.setOnRefreshListener(refreshLayout -> viewModel.refreshData());
        bottomView = findViewById(R.id.bottom_view);
        bottomView.setOnViewClickListener(R.id.tv_checkout, new OnSafeClickListener(1000L) {
            @Override
            public void onClickSafely(View v) {
                trackClickAction(
                        /* targetNm= */EagleTrackEvent.TargetNm.CHECKOUT,
                        /* clickType= */EagleTrackEvent.ClickType.NORMAL
                );
                onPlaceOrderButtonClick();
            }
        });
        ViewTools.setViewVisible(bottomView, false);

        eagleImpressionTracker = new EagleImpressionTrackerIml();
        initRecyclerView();
        initObserver();
        showVeilTemplate(true);
        showToastFromExtraMsg();
    }

    @Override
    public void loadData() {

    }

    @Override
    public void attachModel() {
        String checkoutId = getExtraCheckoutId();
        // Never happen, but just in case
        if (checkoutId == null || checkoutId.isEmpty()) {
            finish();
            return;
        }
        viewModel.setCheckoutId(checkoutId);

        viewModel.getLoadPreCheckoutDataSignal().observe(this, this::loadDealPayData);

        viewModel.adapterData.observe(this, adapterDataTypes -> {
            adapter.setNewData(adapterDataTypes);
            mSmartRefreshLayout.finishRefresh();
            showVeilTemplate(false);
            setImpressionEnable(true);
            reportImpressionEvent();
        });

        // viewModel.dealPayV2() response
        viewModel.dealPayV2LiveData.observe(this, dealPayV2Bean -> {
            if (dealPayV2Bean == null) {
                return;
            }
            viewModel.setDealPayData(dealPayV2Bean);

            bottomView.setCanCheckOut(true, R.string.s_place_order);
            if (dealPayV2Bean.fee_info != null) {
                bottomView.setAmount(dealPayV2Bean.fee_info.final_display_amount, null);
            }
            ViewTools.setViewVisible(bottomView, true);

            ViewTools.setViewVisible(tvPoints, dealPayV2Bean.pointsIsNotNull());
            if (dealPayV2Bean.pointsIsNotNull()) {
                bottomView.setCouponReminder(dealPayV2Bean.point_info.total_reward_points_text);
            }
            bottomView.setAttachedData();

            viewModel.renderAdapterData(dealPayV2Bean);
        });

        viewModel.failureData.observe(this, failure -> {
            String messageId = failure.getMessageId();
            if (DealPayViewModel.ORDER_ABNORMAL.equals(messageId)) {
                handleDealPayAbnormal(failure);
            }
        });

        // viewModel.checkout() response
        viewModel.checkoutLiveData.observe(this, this::handleCheckoutV4Response);

        viewModel.toastLiveData.observe(this, this::showToast);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (refreshFlag != AbstractPreCheckoutViewModel.REFRESH_FLAG_DISABLE) {
            if (refreshFlag == AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE) {
                viewModel.refreshData();
            } else {
                refreshFlag = AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE;
            }
        }
        AppAnalytics.logPageView(WeeeEvent.PageView.CHECKOUT_DEAL_PAY, this);
        setImpressionEnable(false);
        eagleImpressionTracker.onPageResume(rvList);
    }

    @Override
    protected void onPause() {
        super.onPause();
        eagleImpressionTracker.onPagePause(rvList);
    }

    @Override
    protected void onDestroy() {
        keyboardChangeHelper.endObserve();
        super.onDestroy();
    }

    protected void loadDealPayData(boolean showLoading) {
        viewModel.dealPayV2(showLoading);
    }

    private void initWrapperTitle() {
        setWrapperTitle(R.string.s_checkout);
        setWrapperDivider(null);
        if (useWrapper()) {
            TextView tvTitleCenter = getWrapperTitle().findViewById(R.id.tv_title_center);
            tvTitleCenter.setTextColor(getColor(R.color.color_navbar_fg_default));
            ImageView ivTitleLeft = getWrapperTitle().getView(R.id.iv_title_left);
            ViewTools.tintImageView(ivTitleLeft, R.color.color_navbar_fg_default);
        }
    }

    private void initRecyclerView() {
        rvList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        adapter = new CheckOutSectionAdapter();
        adapter.setOnPurchaseChannelActionListener(onPurchaseChannelActionListener);
        rvList.setAdapter(adapter);
        adapter.setImpressionTracker(eagleImpressionTracker);
        rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                ViewTools.setViewVisibilityIfChanged(vShadow, !RecyclerViewTools.isTopMost(recyclerView));
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent();
                }
            }
        });
    }

    private void initObserver() {
        keyboardChangeHelper = new KeyboardChangeHelper(getContentView());
        keyboardChangeHelper.setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
                adapter.updateOtherTipStatus();
            }
        });
        keyboardChangeHelper.startObserve();

        purchaseChannelLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                this::handleActivityResultPurchaseChannel
        );
    }

    private void reportImpressionEvent() {
        if (rvList != null) {
            rvList.postDelayed(() -> {
                if (eagleImpressionTracker != null) {
                    eagleImpressionTracker.trackImpression(rvList);
                }
            }, 200L);
        }
    }

    private void handleActivityResultPurchaseChannel(ActivityResult activityResult) {
        Intent data = activityResult.getData();
        EbtAmountArgs args = data != null ? (EbtAmountArgs) data.getSerializableExtra(PmEditActivity.EXTRA_EBT_AMOUNT_ARGS) : null;
        boolean isPaymentUpdated = data != null && data.getBooleanExtra(PmEditActivity.EXTRA_PAYMENT_UPDATED, false);
        if (args != null) {
            if (isPaymentUpdated) {
                viewModel.getAgent().setEbtUserAmount(args.getUserAmount());
            }
            viewModel.getAgent().setEbtBalance(args.getBalance());
        }
        if (isPaymentUpdated) {
            showToast(R.string.s_checkout_payment_updated);
        }
    }

    private void setKeyboardOnFocus(final View target) {
        if (target != null) {
            target.postDelayed(() -> KeyboardUtils.setKeyboardVisible(activity, target, true), 50L);
        }
    }

    private void setImpressionEnable(boolean isEnabled) {
        if (rvList != null) {
            rvList.setTag(R.id.tag_impression_tracker, isEnabled);
        }
    }

    private void onPlaceOrderButtonClick() {
        DealPayV2Bean dealPayV2Bean = viewModel.getAgent().getData();
        if (dealPayV2Bean == null) {
            return;
        }

        // 使用积分能全额抵扣
        boolean isPointsAllDeducted = viewModel.getAgent().isPointsAllDeducted();
        if (!isPointsAllDeducted && !ensurePointsNotAllDeductedOnPlaceOrder()) {
            return;
        }

        prepareCheckout();
    }

    private boolean ensurePointsNotAllDeductedOnPlaceOrder() {
        DealPayAgent agent = viewModel.getAgent();
        int paymentPosition = CollectionUtils.indexOfFirst(adapter.getData(), item -> item instanceof CheckoutPurchaseChannelData);
        AdapterDataType item = CollectionUtils.getOrNull(adapter.getData(), paymentPosition);
        CheckoutPurchaseChannelData purchaseChannelItem = null;
        if (item instanceof CheckoutPurchaseChannelData) {
            purchaseChannelItem = (CheckoutPurchaseChannelData) item;
        }
        if (purchaseChannelItem == null) {
            return false;
        }

        PurchaseChannelExtraData extraData = agent.getPurchaseChannelExtraDataOnPlaceOrder();
        purchaseChannelItem.setPurchaseChannelExtraData(extraData);
        if (extraData.hasError()) {
            adapter.notifyItemChanged(paymentPosition, extraData);
            rvList.scrollToPosition(paymentPosition);
            return false;
        }
        return true;
    }

    private void prepareCheckout() {
        DealPayAgent agent = viewModel.getAgent();
        agent.setCardTokenizeResult(null);
        boolean isPointsAllDeducted = agent.isPointsAllDeducted();
        if (!isPointsAllDeducted) {
            PaymentChannel cardChannel = agent.getCardChannel();
            String cvcText = agent.cvcText;
            // Obtain deviceData whatever the card channel isCheckCvv
            if (cardChannel != null && DecimalTools.compare(cardChannel.getChannelAmount(), "0") > 0) {
                viewModel.setLoadingStatus(true);
                BraintreeCardPaymentAgent.tokenizeCard(
                        /* coroutineScope= */ViewModelKt.getViewModelScope(viewModel),
                        /* context= */this,
                        /* channelCode= */cardChannel.getChannelCode(),
                        /* isCheckCvv= */CheckoutPreAgent.isCheckCvv(cardChannel),
                        /* cvv= */cvcText,
                        /* callback= */clientPaymentInfo -> {
                            viewModel.setLoadingStatus(false);
                            if (clientPaymentInfo.isSuccess()) {
                                agent.setCardTokenizeResult(clientPaymentInfo);
                                checkout();
                            }
                            return Unit.INSTANCE;
                        }
                );
            } else {
                checkout();
            }
        } else {
            checkout();
        }
    }

    private void checkout() {
        // observe viewModel.checkoutLiveData
        // jump to ::handleCheckoutV4Response
        viewModel.checkout();
    }

    private void handleCheckoutV4Response(@NonNull CheckoutV4Bean bean) {
        String successUrl = !EmptyUtils.isEmpty(bean.success_url)
                ? bean.success_url
                : (bean.pay_payment != null ? bean.pay_payment.getSuccessUrl() : null);
        String cancelUrl = !EmptyUtils.isEmpty(bean.cancel_url)
                ? bean.cancel_url
                : (bean.pay_payment != null ? bean.pay_payment.getCancelUrl() : null);
        if (EmptyUtils.isEmpty(bean.order_ids) || bean.pay_payment == null) {
            // Api error, should never happen
            handlePaymentResult(cancelUrl);
            return;
        }

        refreshFlag = CheckOutSectionViewModel.REFRESH_FLAG_DISABLE;
        PaymentHelper.OnPaymentRedirectCallback c = new PaymentHelper.OnPaymentRedirectCallback() {

            @Override
            public void onPaymentRedirect(String url, String cancelUrl) {
                PaymentCallbackHelper.get().unregisterPaymentCallback(this);
                handlePaymentRedirect(url, cancelUrl);
            }

            @Override
            public void onResult(boolean result, String url) {
                PaymentCallbackHelper.get().unregisterPaymentCallback(this);
                handlePaymentResult(url);
            }
        };
        PaymentCallbackHelper.get().registerPaymentCallback(activity, c);

        PaymentAgentRequest request = new PaymentAgentRequest.Builder(
                /* orderIds= */bean.order_ids,
                /* successUrl= */successUrl,
                /* cancelUrl= */cancelUrl,
                /* payPayment= */bean.pay_payment
        ).build();
        List<String> purchaseChannelCodes = viewModel.getAgent().getPurchaseChannelCodes();
        Intent a = PayPaymentActivity.getIntent(
                this,
                request,
                purchaseChannelCodes,
                viewModel.getAgent().getCardTokenizeResult()
        );
        startActivity(a);
    }

    public void handlePaymentResult(String url) {
        if (url == null || url.isEmpty()) {
            finish();
            return;
        }

        // If the url is already in DealPayActivity, just refresh the data
        if (url.matches(Constants.UrlPattern.DEAL_PAY_V2)) {
            Intent a = HandlerHelper.getDealPayV2Intent(this, url);
            if (a != null && a.getComponent() != null && TextUtils.equals(a.getComponent().getClassName(), getClass().getName())) {
                setIntent(a);
                showToastFromExtraMsg();
                bottomView.loading();
                refreshFlag = AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE;
                return;
            }
        }

        startActivity(WebViewActivity.getIntent(activity, url));
        finish();
    }

    private void handlePaymentRedirect(String url, String cancelUrl) {
        if (url != null && !url.isEmpty()) {
            startActivity(PayPaymentWebViewActivity.getIntent(activity, url, cancelUrl));
        }
        finish();
    }

    private void toPaymentMethodsPage() {
        DealPayAgent agent = viewModel.getAgent();
        if (!agent.isPaymentMethodSelectable()) {
            return;
        }

        EbtAmountArgs ebtAmountArgs;
        ebtAmountArgs = agent.getEbtAmountArgs();
        Intent a = PmEditActivity.getIntent(
                /* context= */DealPayActivity.this,
                /* source= */PmEditActivity.SOURCE_DEAL_PAY,
                /* usePoints= */agent.isUsePoints(),
                /* isAvailableEbt= */agent.isAvailableEbt,
                /* ebtAmountArgs= */ebtAmountArgs,
                /* firstPurchasedAmount= */agent.firstPurchasedAmount
        );
        purchaseChannelLauncher.launch(a);
    }

    private void handleDealPayAbnormal(@NonNull FailureBean failure) {
        String message = failure.getMessage();
        if (message != null && !message.isEmpty()) {
            DealPayViewModel.showToast(message);
        }
        // If the failure is due to ORDER_ABNORMAL, redirect to OrderListActivity
        startActivity(OrderListActivity.getIntent(this, OrderCategory.TYPE_ALL));
        finish();
    }

    private void onUsePointsChecked(CheckoutUsePointsData item) {
        trackClickAction(
                /* targetNm= */EagleTrackEvent.TargetNm.WEEE_POINTS,
                /* clickType= */EagleTrackEvent.ClickType.VIEW
        );
        if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_MEMBER_PLAN) {
            Toaster.showToast(getString(R.string.s_checkout_rewards_toast));
        } else if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_USE_EBT) {
            trackPopup(/* name= */EagleTrackEvent.PopupNm.WEEE_POINTS_POPUP);
            showConfirmUsePointsDialog(() -> updateUsePoints(item, /* usePoints= */true, /* removeEbt= */true));
        } else if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_COMMISSION_PARTNER) {
            // do nothing, just show the unavailable state
            if (DevConfig.isDevelop() && !EmptyUtils.isEmpty(item.getUnavailableDesc())) {
                Toaster.showToast(item.getUnavailableDesc());
            }
        } else {
            updateUsePoints(item, /* usePoints= */!item.checked, /* removeEbt= */false);
        }
    }

    private void showConfirmUsePointsDialog(Runnable onConfirm) {
        new WrapperDialog(this, R.style.BottomDialogTheme) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_checkout_use_points_confirm;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }

            @Override
            public void help(ViewHelper helper) {
                ViewTools.setViewOnClickListener(helper.getView(R.id.iv_close), v -> dismiss());
                ViewTools.setViewOnClickListener(helper.getView(R.id.btn_confirm), v -> {
                    onConfirm.run();
                    dismiss();
                });
            }
        }.show();
    }

    private void updateUsePoints(CheckoutUsePointsData item, boolean usePoints, boolean removeEbt) {
        item.checked = usePoints;
        item.pointsCanChecked = false;
        int pointsPosition = CollectionUtils.indexOfFirst(adapter.getData(), it -> it instanceof CheckoutUsePointsData);
        adapter.notifyItemChanged(pointsPosition);
        viewModel.getAgent().setCustomerUsePoints(usePoints);
        viewModel.updatePaymentCategory(usePoints, removeEbt);
        bottomView.setCanCheckOut(false, R.string.s_place_order);
        bottomView.loading();
    }

    private final OnPurchaseChannelActionListener onPurchaseChannelActionListener = new OnPurchaseChannelActionListener() {

        @Override
        public void onPurchaseChannelClick() {
            toPaymentMethodsPage();
        }

        @Override
        public void onCardCvvInputFocusChange(View view, boolean hasFocus) {
            if (hasFocus) {
                setKeyboardOnFocus(view);
            } else {
                if (view instanceof TextView) {
                    CharSequence cs = ((TextView) view).getText();
                    viewModel.setCvcText(cs != null ? cs.toString() : null);
                }
            }
        }

        @Override
        public void onUsePointsChecked(CheckoutUsePointsData item) {
            DealPayActivity.this.onUsePointsChecked(item);
        }
    };

    private void showVeilTemplate(boolean visible) {
        VeilTools.show(findViewById(R.id.vl_checkout), visible);
        VeilTools.show(findViewById(R.id.vl_checkout_seil), visible);
        ViewTools.setViewVisible(findViewById(R.id.iv_shadow_veil), visible);
    }

    private void showToastFromExtraMsg() {
        String extraMsg = getIntent() != null ? getIntent().getStringExtra(EXTRA_MSG) : null;
        showToast(extraMsg, 0);
    }

    private void showToast(@StringRes int resId) {
        String msg;
        try {
            msg = ContextCompat.getString(this, resId);
        } catch (Exception ex) {
            return;
        }
        int iconResId = 0;
        if (resId == R.string.s_checkout_payment_updated) {
            iconResId = R.drawable.drawable_checkmark_circle_outline_green_1;
        }
        showToast(msg, iconResId);
    }

    private void showToast(String msg, @DrawableRes int iconResId) {
        if (msg == null || msg.isEmpty()) {
            return;
        }
        ActionSnackBarData.Builder dataBuilder = new ActionSnackBarData.Builder()
                .setTitle(msg);
        if (iconResId != 0) {
            dataBuilder = dataBuilder.setActionIconResId(iconResId);
        }
        ActionToastView toastView = new ActionToastView(this);
        toastView.convert(dataBuilder.build());
        Toaster.asToast(this).setView(toastView)
                .setOptions(new ToastOptions().setGravity(Gravity.FILL_HORIZONTAL | Gravity.BOTTOM))
                .build()
                .show();
    }

    private void trackClickAction(String targetNm, String clickType) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(targetNm)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(clickType)
                .build().getParams());
    }

    private void trackPopup(String name) {
        Map<String, Object> co = new TrackParams()
                .put("action", "view")
                .put("name", name)
                .put("content_type", null)
                .put("id", null)
                .put("url", null)
                .put("target_url", null)
                .put("other_parameter", null)
                .get();
        AppAnalytics.logEvent(
                EagleTrackEvent.EventType.POPUP_IMP,
                new EagleTrackModel.Builder().addContent(co).build().getParams()
        );
    }

}