package com.sayweee.weee.module.debug.producttrace.data;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.Objects;

public class ProductTraceKey {

    @NonNull
    public static ProductTraceKey of(int productId, String traceId, String uniqueKey) {
        return new ProductTraceKey(productId, traceId, uniqueKey);
    }

    @NonNull
    public static String generateUniqueKey(String modNm, String secNm) {
        return modNm + "-" + secNm;
    }

    private final int productId;
    private final String traceId;
    private final String uniqueKey;

    private ProductTraceKey(int productId, String traceId, String uniqueKey) {
        this.productId = productId;
        this.traceId = traceId != null ? traceId : "";
        this.uniqueKey = uniqueKey;
    }

    public int getProductId() {
        return productId;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ProductTraceKey that = (ProductTraceKey) o;
        return productId == that.productId
                && Objects.equals(traceId, that.traceId)
                && Objects.equals(uniqueKey, that.uniqueKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(productId, traceId, uniqueKey);
    }

    public String dump() {
        return "ProductTraceKey{" +
                "productId=" + productId +
                ", traceId='" + traceId + '\'' +
                ", uniqueKey='" + uniqueKey + '\'' +
                '}';
    }

    public interface Provider {

        @Nullable
        ProductTraceKey getProductTraceKey();
    }
}
