package com.sayweee.weee.module.home.service;

import android.app.Activity;
import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ConfigService;
import com.sayweee.service.context.store.bean.StoreInfo;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductListBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.data.LayoutComponentData;
import com.sayweee.weee.module.cms.iml.product.CmsBigProductLineParser;
import com.sayweee.weee.module.cms.iml.product.CmsProductLineTabsParser;
import com.sayweee.weee.module.cms.service.CmsApi;
import com.sayweee.weee.module.cms.service.CmsPagingViewModel;
import com.sayweee.weee.module.cms.service.ComponentPool;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.module.home.bean.BesideTipsBean;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.module.home.bean.KeywordsBean;
import com.sayweee.weee.module.home.bean.RecommendBean;
import com.sayweee.weee.module.home.provider.banner.BannerLineParser;
import com.sayweee.weee.module.home.provider.banner.BannerThemeParser;
import com.sayweee.weee.module.home.provider.banner.CmsLayoutLarParser;
import com.sayweee.weee.module.home.provider.banner.NoticeBannerParser;
import com.sayweee.weee.module.home.provider.banner.data.CmsBannerThemeData;
import com.sayweee.weee.module.home.provider.bar.SearchBarParser;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.home.provider.brand.CardLineParser;
import com.sayweee.weee.module.home.provider.category.CategoryParser;
import com.sayweee.weee.module.home.provider.community.CmsTrendingPostParser;
import com.sayweee.weee.module.home.provider.coupon.CmsPersonalizedCouponParser;
import com.sayweee.weee.module.home.provider.message.NewTopMessageParser;
import com.sayweee.weee.module.home.provider.message.TopMessageV2Parser;
import com.sayweee.weee.module.home.provider.message.TopMessageV3Parser;
import com.sayweee.weee.module.home.provider.message.data.CmsNewTopMessageData;
import com.sayweee.weee.module.home.provider.message.data.CmsTopMessageV3Data;
import com.sayweee.weee.module.home.provider.product.CollectionParser;
import com.sayweee.weee.module.home.provider.product.FeatureThisWeekParser;
import com.sayweee.weee.module.home.provider.product.LightingDealsParser;
import com.sayweee.weee.module.home.provider.product.RecommendParser;
import com.sayweee.weee.module.home.provider.product.data.CmsRecommendData;
import com.sayweee.weee.module.home.provider.thematic.ThematicParser;
import com.sayweee.weee.module.launch.ZipCodeInputActivity;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.data.CmsCategoryFeedData;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedParser;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.AddressPositioningConfigBean;
import com.sayweee.weee.service.config.bean.HomeConfigBean;
import com.sayweee.weee.service.experiment.DynamicExperimentHelper;
import com.sayweee.weee.service.location.LocationUtils;
import com.sayweee.weee.service.location.bean.GeoBean;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.ExceptionHandler;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;
import com.sayweee.wrapper.http.support.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * Author:  winds
 * Date:    2023/5/15.
 * Desc:
 */
@SuppressWarnings("squid:S110")
public class HomeViewModel extends CmsPagingViewModel<BaseLoaderModel<OrderApi>> implements IContentFeedSharedViewModel {

    private static final boolean DEFAULT_IS_LOAD_BY_PAGING = true;
    private static final int DEFAULT_PAGING_SIZE = 8;

    protected boolean mIsLoadByPaging = DEFAULT_IS_LOAD_BY_PAGING;
    protected int mPagingSize = DEFAULT_PAGING_SIZE;

    //下拉刷新状态提醒
    public MutableLiveData<Boolean> refreshStatusData = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> cacheData = new MutableLiveData<>(); //分发缓存数据

    public MutableLiveData<AdapterDataType> adapterItemData = new MutableLiveData<>(); //用于单数据更新

    public MutableLiveData<Boolean> requestRefresh = new MutableLiveData<>();

    public MutableLiveData<Integer> storeChangedData = new MutableLiveData<>();

    //订单geo地理位置校验结果
    public MutableLiveData<Boolean> geoConflictData = new MutableLiveData<>();
    public MutableLiveData<Map<String, Object>> remindData = new MutableLiveData<>();
    public MutableLiveData<List<BesideTipsBean>> dialogData = new MutableLiveData<>();
    private Map<String, CmsBean.DataSourceBean> cmsDatasource;


    public HomeViewModel(@NonNull Application application) {
        super(application);
        configParser();
        configPaging();
    }

    protected void configParser() {
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_SEARCH_BAR, new SearchBarParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_THEME, new BannerThemeParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TOP_MESSAGE_V3, new TopMessageV3Parser());//已弃用
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TOP_MESSAGE_V2, new TopMessageV2Parser());//已弃用
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TOP_MESSAGE, new NewTopMessageParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_NOTICE_BANNER, new NoticeBannerParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CATEGORIES, new CategoryParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_THEME, new ThematicParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_LIGHTNING_DEALS, new LightingDealsParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_FEATURED_THIS_WEEK, new FeatureThisWeekParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COLLECTION_V2, new CollectionParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BANNER_LINE, new BannerLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_TRENDING_POST, new CmsTrendingPostParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_RECOMMEND, new RecommendParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CARD_LINE, new CardLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED, new CmsContentFeedParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PRODUCT_LINE_TABS, new CmsProductLineTabsParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_BIG_PRODUCT_LINE, new CmsBigProductLineParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_LAYOUT_LAR, new CmsLayoutLarParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_PERSONALIZED_COUPON, new CmsPersonalizedCouponParser());
    }

    protected void configPaging() {
        HomeConfigBean dynamicConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.HOME);
        Integer pageLimit = dynamicConfig != null ? dynamicConfig.page_limit_android : null;
        if (pageLimit != null) {
            if (pageLimit <= 0) { // disable load by paging
                mIsLoadByPaging = false;
            } else { // enable load by paging & set page limit
                mIsLoadByPaging = true;
                mPagingSize = pageLimit;
            }
        } else {
            // default paging config
            mIsLoadByPaging = DEFAULT_IS_LOAD_BY_PAGING;
            mPagingSize = DEFAULT_PAGING_SIZE;
        }
    }

    @Override
    protected int getPagingSize() {
        return mPagingSize;
    }

    @Override
    protected boolean isLoadByPaging() {
        return mIsLoadByPaging;
    }

    public boolean isNetworkConnected() {
        return Utils.isNetworkConnected(RetrofitIml.get().getAttachContext());
    }

    public void retryGetPreOrder() {
        getLoader().getHttpService().getSimplePreOrder()
                .compose(DisposableTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<SimplePreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<SimplePreOrderBean> response) {
                        OrderManager.get().setSimpleOrderData(response.getData());
                        SharedOrderViewModel.get().onPreOrderRecreate();
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        Activity activity = LifecycleProvider.get().getTopActivity();
                        if (activity != null) {
                            activity.startActivity(ZipCodeInputActivity.getIntent(activity));
                        }
                    }
                });
    }

    public void onRefresh(boolean readCache, boolean refreshStore, boolean isSilent) {
        if (readCache) {
            readCache();
        }
        if (!isNetworkConnected()) {
            //分发失败状态和刷新状态
            failureData.postValue(FailureBean.handle(false, null, ExceptionHandler.handleException(ExceptionHandler.ERROR_NETWORK, "network not connect")));
            refreshStatusData.postValue(false);
            return;
        }
        if (OrderManager.get().isLackOfOrder()) {
            //缺少zipCode
            retryGetPreOrder();
            failureData.postValue(FailureBean.handle(false, null, ExceptionHandler.handleException(ExceptionHandler.ERROR_NETWORK, "network not connect")));
            refreshStatusData.postValue(false);
            return;
        }
        if (StoreManager.get().disableRefreshNext()) {
            StoreManager.get().setDisableRefreshNext(false);
            getHomeData(isSilent);
        } else if (refreshStore) {
            StoreManager.get().fetchStore(OrderManager.get().getZipCode(), new ResponseObserver<ResponseBean<StoreInfo>>() {

                @Override
                public void onResponse(ResponseBean<StoreInfo> response) {
                    storeChangedData.postValue(1);
                }

                @Override
                public void onFinish() {
                    getHomeData(isSilent);
                }
            });
        } else {
            getHomeData(isSilent);
        }
    }

    public void onRefresh(boolean isSilent) {
        onRefresh(false, false, isSilent);
    }

    private void getHomeData(boolean isSilent) {
        requestCount = 0;
        requestStart = System.currentTimeMillis();
        startTimer();
        RequestParams requestParams = new RequestParams()
                .put("lang", LanguageManager.get().getLanguage())
                .put("sales_org_id", OrderManager.get().getSaleOrgId())
                .put("zipcode", OrderManager.get().getZipCode());
        getLoader()
                .getHttpService()
                .getHomeDataByCms(VariantConfig.HOME_API_KEY, requestParams.get())
                .compose(DisposableTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<CmsBean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (!isSilent) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<CmsBean> response) {
                        cmsDatasource = response.getData().datasource;
                        parseCmsData(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        cancelTimer();
                        refreshStatusData.postValue(false);
                        failureData.postValue(FailureBean.handle(false, null, ExceptionHandler.handleException(ExceptionHandler.ERROR_SERVICE_UNAVAILABLE, "home cms error")));
                        if (!isSilent) { //request error时关闭loading 请求结束会在分发数据时结束loading
                            setLoadingStatus(false);
                        }
                    }
                });
    }

    @Override
    protected void appendData(ComponentData parseData, CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean component, List<CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean> components) {
        super.appendData(parseData, component, components);
        if (parseData instanceof CmsSearchBarData) {
            perfectSearchBarData((CmsSearchBarData) parseData);
        }
    }

    @Override
    protected void beforePreCreateData() {
        if (componentDataMap != null) {
            int pos = 0;
            List<ComponentData> cache = new ArrayList<>();
            for (Map.Entry<String, ComponentData> entry : componentDataMap.entrySet()) {
                ComponentData data = entry.getValue();
                if (data != null) {
                    data.filter = true;
                    if (data.isValid()) {
                        data.position = pos;
                        pos++;

                        if (data instanceof ICache) {
                            cache.add(data);
                        }
                    }
                    if (data instanceof CmsContentFeedPacket) {
                        contentFeedPacket = (CmsContentFeedPacket) data;
                    }
                }
            }

            HomeCacheHelper.writeNewCache(cache);
        }
    }

    @Override
    protected void dispatchDataChanged() {
        cancelTimer();
        super.dispatchDataChanged();
        refreshStatusData.postValue(false);
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_LOAD,
                WeeeEvent.PageView.HOME, String.valueOf(HomeViewModel.this.hashCode()));
        WeeeMonitor.getInstance().initAppEnd(WeeeMonitor.KEY_ACTIVE);
    }

    @Override
    protected void beforeFetchCmsChildDataByUrl(String componentKey, ComponentData data, String path, Map<String, String> params, Class<?> clazz) {
        if (data instanceof CmsContentFeedPacket) {
            params.put("recommend_session", ((CmsContentFeedPacket) data).recommendSession);
            params.put("page_num", "1");

            CmsDataSource dataSource = CmsContentFeedParser.parseDataSource(data, path, params);
            ((CmsContentFeedPacket) data).setBaseDataSource(dataSource);
        } else {
            super.beforeFetchCmsChildDataByUrl(componentKey, data, path, params, clazz);
        }
    }

    @Override
    protected void fetchCmsChildDataByUrl(ComponentData data, String path, Map<String, String> params, Class<?> clazz) {
        if (data instanceof CmsRecommendData && ComponentPool.Key.COMPONENT_CM_RECOMMEND.equalsIgnoreCase(data.componentKey)) {
            RecommendBean bean = new RecommendBean();
            bean.setContent(path.replace(AppConfig.HOST, ""));
            data.setData(bean);
            parseHomeRecommendData((CmsRecommendData) data, data.componentKey);
            return;
        }
        super.fetchCmsChildDataByUrl(data, path, params, clazz);
    }

    @Override
    protected List<AdapterDataType> beforeDispatchData(List<AdapterDataType> data) {
        List<AdapterDataType> list = super.beforeDispatchData(data);
        Pair<Integer, AdapterDataType> entry;
        entry = CollectionUtils.firstOrNullWithIndex(list, item -> item instanceof CmsBannerThemeData);
        if (entry != null) {
            int themeIndex = entry.first;
            AdapterDataType maybeThemeItem = entry.second;
            if (maybeThemeItem instanceof CmsBannerThemeData) {
                String searchBarColor = ((CmsBannerThemeData) maybeThemeItem).t.color;
                entry = CollectionUtils.firstOrNullWithIndex(list, item -> item instanceof CmsSearchBarData);
                if (entry != null) {
                    AdapterDataType maybeSearchBarItem = entry.second;
                    if (maybeSearchBarItem instanceof CmsSearchBarData) {
                        ((CmsSearchBarData) maybeSearchBarItem).bgColor = searchBarColor;
                    }
                }
                //移除CmsBannerThemeData与CmsTopMessageV3Data之间的CmsBlankData
                int msgV3Index = themeIndex + 2;
                if (msgV3Index < list.size() && list.get(msgV3Index) instanceof CmsTopMessageV3Data) {
                    list.remove(themeIndex + 1);
                }
            }
        }

        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(list);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(WeeeEvent.PageView.HOME, WeeeEvent.PageView.HOME));

        return list;
    }

    @Override
    protected void onMultiDataSourceDataUpdated(String componentId) {
        ComponentData<?, ?> componentData = CollectionUtils.getOrNull(componentDataMap, componentId);
        if (componentData != null) {
            ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
            taskAssembler.add(componentData);
            ProductTraceManager.get().addTasks(taskAssembler.assemble(WeeeEvent.PageView.HOME, WeeeEvent.PageView.HOME));
        }

        super.onMultiDataSourceDataUpdated(componentId);
    }

    /*******/
    @Nullable
    private CmsContentFeedPacket contentFeedPacket;

    @Nullable
    @Override
    public CmsContentFeedPacket getContentFeedPacket() {
        return contentFeedPacket;
    }

    @Nullable
    @Override
    public String getContentFeedFromPageKey() {
        return WeeeEvent.PageView.HOME;
    }

    /*******/
    public void readCache() {
        HomeCacheHelper.readNewCache()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<List<AdapterDataType>>() {
                    @Override
                    public void onResponse(List<AdapterDataType> response) {
                        if (response != null && !response.isEmpty()) {
                            beforePostCache(response);
                            cacheData.postValue(response);
                        }
                    }
                });
    }

    private void beforePostCache(List<AdapterDataType> response) {
        AdapterDataType adapterDataType = CollectionUtils.firstOrNull(
                response,
                item -> item instanceof CmsCategoryFeedData
        );
        if (adapterDataType instanceof CmsCategoryFeedData) {
            if (contentFeedPacket == null) {
                contentFeedPacket = ((CmsCategoryFeedData) adapterDataType).t;
                CmsDataSource cmsDataSource = CmsContentFeedParser.parseDataSource(contentFeedPacket, null, null);
                contentFeedPacket.setBaseDataSource(cmsDataSource);
                contentFeedPacket.setData(contentFeedPacket.t);
            }
        }
    }

    private void parseHomeRecommendData(CmsRecommendData data, String key) {
        if (data.t != null && data.property != null && data.property.keys != null) {
            for (String url : data.property.keys) {
                String path = data.t.getUrl(url);
                getHomeRecommend(data, key, path, url);
            }
        }
    }

    public void getHomeRecommend(CmsRecommendData data, String itemKey, String path, String propertyKey) {
        getLoader()
                .getHttpService()
                .getHomeRecommend(path, 20, 0)
                .compose(DisposableTransformer.scheduler(this, true))
                .subscribe(new LoadingResponseObserver<ResponseBean<ProductListBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ProductListBean> response) {
                        putRecommendData(data, itemKey, propertyKey, response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        putRecommendData(data, itemKey, propertyKey, null);
                    }
                });
    }

    private void putRecommendData(CmsRecommendData data, String itemKey, String propertyKey, ProductListBean bean) {
        if (data instanceof CmsRecommendData) {
            if (data.property != null) {
                data.property.putRecommendData(propertyKey, bean != null ? bean.products : null);
            }
            if (data.t != null && data.property != null && data.property.isTrigger()) {
                data.property.prepareData(data.t.content);
            }
        }
    }

    public void refreshTopMessage(@NonNull String componentId, long delayMillis) {
        refreshTopMessages(CollectionUtils.arrayListOf(componentId), delayMillis);
    }

    public void refreshAllTopMessages(long delayMillis) {
        List<String> componentIds = new ArrayList<>();
        for (ComponentData<?, ?> item : componentDataMap.values()) {
            String componentId = item.getComponentId();
            if (componentId == null || componentId.isEmpty()) {
                continue;
            }
            if (item instanceof CmsNewTopMessageData && ComponentPool.Key.COMPONENT_CM_TOP_MESSAGE.equalsIgnoreCase(item.componentKey)) {
                componentIds.add(componentId);
            }
        }
        refreshTopMessages(componentIds, delayMillis);
    }

    private void refreshTopMessages(@NonNull List<String> componentIds, long delayMillis) {
        for (String componentId : componentIds) {
            CmsDataSource dataSource = getDataSource(componentId);
            if (dataSource != null) {
                requestMultiDataSource(dataSource, delayMillis);
            }
        }
    }

    public void perfectSearchBarData(CmsSearchBarData searchBarData) {
        DynamicExperimentHelper.fetchResult(DynamicExperimentHelper.Key.KEY_XP_SEARCH_BAR_BUTTON_VISIBLE, new SimpleObserver<Boolean>() {
            @Override
            public void onNext(Boolean flag) {
                if (flag != null && flag) {
                    getLoader()
                            .getHttpService()
                            .getSearchBarTips()
                            .compose(DisposableTransformer.scheduler())
                            .subscribe(new LoadingResponseObserver<ResponseBean<KeywordsBean>>() {
                                @Override
                                public void onResponse(ResponseBean<KeywordsBean> response) {
                                    searchBarData.setKeywords(response.getData().keywords);
                                }
                            });
                }
            }
        });
    }

    private Disposable timerDisposable;

    private void cancelTimer() {
        if (timerDisposable != null && !timerDisposable.isDisposed()) {
            timerDisposable.dispose();
        }
    }

    public void startTimer() {
        int total = 15;
        cancelTimer();
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(total)
                .map(new Function<Long, Long>() {
                    @Override
                    public Long apply(@NonNull Long l) throws Exception {
                        return total - l;
                    }
                })
                .doOnSubscribe(new Consumer<Disposable>() {
                    @Override
                    public void accept(Disposable disposable) throws Exception {
                        timerDisposable = disposable;
                    }
                })
                .subscribe(new SimpleObserver<Long>() {

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        forceResetLoadingCount();
                    }
                });
    }

    private void forceResetLoadingCount() {
        if (loadingCount > 0) {
            loadingCount = 0;
            setLoadingCount(false);
        }
    }

    public void geoCheckHome(double lng, double lat) {
        getLoader().getHttpService()
                .getOrderLocation()
                .compose(DisposableTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<GeoBean>>() {
                    @Override
                    public void onResponse(ResponseBean<GeoBean> geoBean) {
                        GeoBean bean = geoBean.getData();
                        AddressPositioningConfigBean config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.ADDRESS_POSITIONING);
                        if (bean != null && config != null) {
                            geoConflictData.postValue(LocationUtils.isDistanceReachLimit(lng, lat, bean.lng, bean.lat, config.home_positioning_check_distance));
                        }
                    }
                });
    }

    public void requestDelayLoadData() {
        Observable.timer(1000L, TimeUnit.MILLISECONDS)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new SimpleObserver<Long>() {
                    @Override
                    public void onNext(Long aLong) {
                        requestRefresh.postValue(true);
                    }
                });
    }

    public void changeLightningDealsRemind(int productId, boolean isRemind) {
        getLoader().getHttpService()
                .changeLightningDealsRemind(new RequestParams()
                        .put("product_id", productId)
                        .put("status", isRemind ? "A" : "X")
                        .create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("product_id", productId);
                        map.put("remind", isRemind);
                        remindData.postValue(map);
                    }
                });
    }

    public void getBesideDialog() {
        getLoader().getHttpService()
                .getSecureTips()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<List<BesideTipsBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<BesideTipsBean>> response) {
                        dialogData.postValue(response.getData());
                    }
                });
    }

    @Override
    protected boolean parseCmsItemData(ComponentData data, CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean component, CmsBean.DataSourceBean source, Class<?> clazz) {
        if ("layout".equalsIgnoreCase(component.component_type)
                && CollectionUtils.size(component.sub_components) >= 2
                && data instanceof LayoutComponentData) {
            //布局组件,内含子组件
            LayoutComponentData layoutData = (LayoutComponentData) data;
            //只取前两个sub_component
            List<CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean> subList = component.sub_components.subList(0, 2);
            //子组件
            for (CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean subComponent : subList) {
                int position = subList.indexOf(subComponent);
                if (CollectionUtils.isNotEmpty(subComponent.datasource)) {
                    String temp = subComponent.datasource.get(0);
                    if (!EmptyUtils.isEmpty(temp)) {
                        CmsBean.DataSourceBean subSource = cmsDatasource.get(temp);
                        if (subSource != null) {
                            long current = System.currentTimeMillis() / 1000;
                            String subUrl = current > subSource.time ? subSource.next : subSource.now;
                            if (subUrl == null) {
                                subUrl = subSource.now;
                            }
                            if (!TextUtils.isEmpty(subUrl)) {
                                if (subSource.is_static) {
                                    layoutData.jsonStrings.put(subComponent.component_key, subUrl);
                                    layoutData.parseSubComponentData(subComponent.component_key, position, subComponent.properties);
                                    return true;
                                } else {
                                    String path = AppConfig.HOST + CommonTools.removeQueryPath(subUrl);
                                    Map<String, String> params = CommonTools.parseQueryParams(subUrl);
                                    String datasource = subComponent.datasource.get(0);
                                    if (datasource != null) {
                                        params.put("dataobject_key", datasource);
                                    }
                                    getLoader().createHttpService(CmsApi.class)
                                            .getCmsChildDataByUrl(path, params)
                                            .compose(DisposableTransformer.scheduler(this, false))
                                            .subscribe(new LoadingResponseObserver<ResponseBean<String>>() {
                                                @Override
                                                public void onResponse(ResponseBean<String> response) {
                                                    String data1 = response.getData();
                                                    layoutData.jsonStrings.put(subComponent.component_key, data1);
                                                    layoutData.parseSubComponentData(subComponent.component_key, position, subComponent.properties);
                                                }
                                            });
                                }
                            }
                        }
                    }
                }
            }
            return false;
        } else {
            return super.parseCmsItemData(data, component, source, clazz);
        }
    }
}
