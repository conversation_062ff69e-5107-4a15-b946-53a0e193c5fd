package com.sayweee.weee.module.checkout2.pm;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelKt;

import com.joinforage.forage.android.ecom.services.CheckBalanceParams;
import com.joinforage.forage.android.ecom.services.DeferPaymentCaptureParams;
import com.joinforage.forage.android.ecom.services.TokenizeEBTCardParams;
import com.joinforage.forage.android.ecom.ui.element.ForagePANEditText;
import com.joinforage.forage.android.ecom.ui.element.ForagePINEditText;
import com.sayweee.service.PaymentService;
import com.sayweee.service.impl.payment.agents.EbtPaymentAgent;
import com.sayweee.service.impl.payment.bean.EbtPaymentInfo;
import com.sayweee.service.impl.payment.sdk.EbtCheckBalanceResult;
import com.sayweee.service.impl.payment.sdk.EbtException;
import com.sayweee.service.impl.payment.sdk.EbtExceptionExt;
import com.sayweee.service.payment.bean.CardAttachBean;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

public class PmActionViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    final MutableLiveData<CardAttachBean> attachCardBeanData = new MutableLiveData<>();
    final MutableLiveData<String> cvcErrorData = new MutableLiveData<>();
    final MutableLiveData<String> cardErrorData = new MutableLiveData<>();
    final MutableLiveData<String> attachErrorData = new MutableLiveData<>();
    final MutableLiveData<EbtCheckBalanceResult> ebtBalanceResultLiveData = new MutableLiveData<>();
    final MutableLiveData<EbtPaymentInfo> ebtDeferResultLiveData = new MutableLiveData<>();
    final MutableLiveData<FailureBean> ebtFailureData = new MutableLiveData<>();
    final MutableLiveData<List<CardAttachBean>> ebtProfilesData = new MutableLiveData<>();

    public PmActionViewModel(@NonNull Application application) {
        super(application);
    }

    public void attachStripeCard(String channelCode, String id) {
        Map<String, Serializable> params = new RequestParams()
                .put("payment_method_id", id)
                .get();
        String requestJson = JsonUtils.toJSONString(params);
        setLoadingStatus(true);
        PaymentService.get().attachPaymentMethod(
                ViewModelKt.getViewModelScope(this),
                channelCode,
                params,
                (response, failure) -> {
                    setLoadingStatus(false);
                    handleAttachStripeCardResult(channelCode, requestJson, response, failure);
                });
    }

    private void handleAttachStripeCardResult(
            String channelCode,
            @Nullable String requestJson,
            @Nullable ResponseBean<CardAttachBean> response,
            @Nullable FailureBean failure
    ) {
        if (response != null) {
            if (response.result) {
                //log
                String responseJson = JsonUtils.toJSONString(response);
                logAttachCard(channelCode, "success", requestJson, responseJson);
            }
            EagleTrackManger.get().trackEagleInfoUpdate(
                    /* info_name= */EagleTrackEvent.InfoName.PAYMENT_METHOD,
                    /* action_type= */EagleTrackEvent.ActionType.ADD,
                    /* result= */response.result,
                    /* payment_type= */channelCode,
                    /* map= */null
            );
            attachCardBeanData.postValue(response.getData());
        } else if (failure != null) {
            //error
            if ("PY10114".equals(failure.getMessageId()) && !EmptyUtils.isEmpty(failure.getMessage())) {
                cvcErrorData.postValue(failure.getMessage());
            }
            //log
            String responseJson = JsonUtils.toJSONString(failure);
            logAttachCard(channelCode, "fail", requestJson, responseJson);
            EagleTrackManger.get().trackEagleInfoUpdate(
                    /* info_name= */EagleTrackEvent.InfoName.PAYMENT_METHOD,
                    /* action_type= */EagleTrackEvent.ActionType.ADD,
                    /* result= */false,
                    /* payment_type= */channelCode,
                    /* map= */null
            );
        }
    }

    /*****************************************************/
    //braintree
    public void attachBraintreeCard(String channelCode, String cardNonce, String deviceData) {
        Map<String, Serializable> params = new RequestParams()
                .put("payment_method_id", cardNonce)
                .putNonNull("device_data", deviceData) //braintree device data
                .get();
        String requestJson = JsonUtils.toJSONString(params);
        setLoadingStatus(true);
        PaymentService.get().attachPaymentMethod(
                ViewModelKt.getViewModelScope(this),
                channelCode,
                params,
                (response, failure) -> {
                    setLoadingStatus(false);
                    handleAttachBraintreeCardResult(channelCode, requestJson, response, failure);
                });
    }

    private void handleAttachBraintreeCardResult(
            String channelCode,
            @Nullable String requestJson,
            @Nullable ResponseBean<CardAttachBean> response,
            @Nullable FailureBean failure
    ) {
        if (response != null) {
            if (response.result) {
                //log
                String responseJson = JsonUtils.toJSONString(response);
                logAttachCard(channelCode, "success", requestJson, responseJson);
            }
            EagleTrackManger.get().trackEagleInfoUpdate(
                    /* info_name= */EagleTrackEvent.InfoName.PAYMENT_METHOD,
                    /* action_type= */EagleTrackEvent.ActionType.ADD,
                    /* result= */response.result,
                    /* payment_type= */channelCode,
                    /* map= */null
            );
            attachCardBeanData.postValue(response.getData());
        } else if (failure != null) {
            //error
            if ("PY10114".equals(failure.getMessageId()) && !EmptyUtils.isEmpty(failure.getMessage())) {
                cvcErrorData.postValue(failure.getMessage());
            } else if ("PY10004".equalsIgnoreCase(failure.getMessageId())) {
                cardErrorData.postValue(failure.getMessage());
            } else if (!EmptyUtils.isEmpty(failure.getMessage())) {
                attachErrorData.postValue(failure.getMessage());
            }
            //log
            String responseJson = JsonUtils.toJSONString(failure);
            logAttachCard(channelCode, "fail", requestJson, responseJson);
            EagleTrackManger.get().trackEagleInfoUpdate(
                    /* info_name= */EagleTrackEvent.InfoName.PAYMENT_METHOD,
                    /* action_type= */EagleTrackEvent.ActionType.ADD,
                    /* result= */false,
                    /* payment_type= */channelCode,
                    /* map= */null
            );
        }
    }

    private void logAttachCard(String channelCode, String status, String request, String response) {
        getLoader().getHttpService()
                .attachCardLog(new RequestParams()
                        .put("status", status)
                        .put("request", request)
                        .put("response", response)
                        .put("opt_type", "attach")
                        .put("pay_platform", channelCode)
                        .get())
                .compose(DisposableTransformer.scheduler())
                .subscribe(new SimpleObserver<SimpleResponseBean>() {
                });
    }

    public void attachEbtCard(String channelCode, ForagePANEditText etPan) {
        setLoadingStatus(true);
        TokenizeEBTCardParams tokenizeEBTCardParams;
        tokenizeEBTCardParams = new TokenizeEBTCardParams(etPan, AccountManager.get().getUserId(), true);
        PaymentService.get().attachEbtCard(
                ViewModelKt.getViewModelScope(this),
                channelCode,
                tokenizeEBTCardParams,
                (result, failure) -> {
                    boolean isSuccess = result != null;
                    if (result != null) {
                        attachCardBeanData.postValue(result.getData());
                    } else {
                        if (failure != null) {
                            ebtFailureData.postValue(failure);
                        }
                    }
                    EagleTrackManger.get().trackEagleInfoUpdate(
                            /* info_name= */EagleTrackEvent.InfoName.PAYMENT_METHOD,
                            /* action_type= */EagleTrackEvent.ActionType.ADD,
                            /* result= */isSuccess,
                            /* payment_type= */channelCode,
                            /* map= */null
                    );
                    setLoadingStatus(false);
                });
    }

    public void checkEbtBalance(String channelCode, ForagePINEditText etPin, String paymentRefEncrypted) {
        setLoadingStatus(true);
        PaymentService.get().checkEbtBalance(
                ViewModelKt.getViewModelScope(this),
                channelCode,
                new CheckBalanceParams(etPin, paymentRefEncrypted),
                (result, failure) -> {
                    setLoadingStatus(false);
                    if (result != null) {
                        ebtBalanceResultLiveData.postValue(result);
                    } else if (failure != null) {
                        ebtFailureData.postValue(failure);
                    }
                });
    }

    public void deferEbtPaymentCapture(String channelCode, ForagePINEditText etPin, String paymentRefDecrypted) {
        setLoadingStatus(true);
        EbtPaymentAgent.deferEbtPaymentCapture(
                ViewModelKt.getViewModelScope(this),
                channelCode,
                new DeferPaymentCaptureParams(etPin, paymentRefDecrypted),
                clientPaymentInfo -> {
                    setLoadingStatus(false);
                    ebtDeferResultLiveData.postValue(clientPaymentInfo);
                    return Unit.INSTANCE;
                }
        );

    }

    @NonNull
    public static String getEbtExceptionMessage(@Nullable Context context, @Nullable Throwable throwable) {
        String message = "";
        if (context == null || throwable == null) {
            return message;
        }
        if (throwable.getCause() instanceof EbtException) {
            EbtException ebtException = (EbtException) throwable.getCause();
            message = EbtExceptionExt.localizedMessage(ebtException, context);
        }
        return message;
    }

    public void getEbtPaymentProfiles() {
        getLoader().getHttpService()
                .getEbtPaymentProfiles()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<List<CardAttachBean>>>() {

                    @Override
                    public void onResponse(ResponseBean<List<CardAttachBean>> response) {
                        ebtProfilesData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        ebtProfilesData.postValue(CollectionUtils.emptyList());
                    }
                });

    }
}
