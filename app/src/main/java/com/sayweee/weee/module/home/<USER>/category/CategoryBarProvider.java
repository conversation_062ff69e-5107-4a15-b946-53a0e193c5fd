package com.sayweee.weee.module.home.provider.category;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;

public class CategoryBarProvider extends CategoryAutoScrollProvider {

    public CategoryBarProvider(@NonNull RecyclerViewScrollStatePersist scrollStatePersist) {
        super(scrollStatePersist);
    }

    @Override
    public int getItemType() {
        return CmsItemType.CATEGORIES_BAR;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_cms_category_bar;
    }

    @Override
    protected CmsCategoryBaseAdapter createAdapter() {
        return new CmsCategoryBarAdapter();
    }

    @Override
    protected RecyclerView.ItemDecoration createItemDecoration() {
        return new CategoryItemDecoration(/* spaceMargin= */2);
    }

    @Override
    protected void updateAdapter(RecyclerView recyclerView, CmsCategoryData item) {
        RecyclerView.Adapter<?> a = recyclerView.getAdapter();
        if (a instanceof CmsCategoryBaseAdapter) {
            CmsCategoryBaseAdapter adapter = (CmsCategoryBaseAdapter) a;
            String modNm = item.getEventKey();
            adapter.setModInfo(modNm, item.position);
            adapter.setAdapterData(item.t.category_list, item.getDisplayStyle(), item.t.see_all_img_url);
        }
    }
}
