package com.sayweee.weee.module.home.provider.message.data;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.module.home.bean.TopMessageV2Bean;
import com.sayweee.weee.module.home.bean.TopMessageV2ItemBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

@Deprecated
public class CmsTopMessageV2Data extends ComponentData<TopMessageV2Bean, CmsProperty> implements ICache {

    public CmsTopMessageV2Data() {
        super(CmsItemType.TOP_MESSAGE_V2);
    }

    public long systemTime;

    @Override
    public boolean isValid() {
        return t != null && (CollectionUtils.isNotEmpty(t.beside_list) || CollectionUtils.isNotEmpty(t.vertical_list));
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        if (isValid()) {
            ArrayList<AdapterDataType> list = new ArrayList<>();
            list.add(this);
            list.add(new CmsBlankData());
            return list;
        }
        return null;
    }

    @Override
    public void setData(TopMessageV2Bean newTopMessageBeans) {
        systemTime = System.currentTimeMillis() / 1000;
        super.setData(newTopMessageBeans);
    }

    public boolean hasBesideListList() {
        return CollectionUtils.isNotEmpty(t.beside_list);
    }

    public boolean hasVerticalList() {
        return CollectionUtils.isNotEmpty(t.vertical_list);
    }

    public boolean isSkipPopup() {
        //只有一个的情况才允许跳过
        return isValid() && hasVerticalList() && t.vertical_list.size() == 1 && t.vertical_list.get(0).link_type == 1 && !EmptyUtils.isEmpty(t.vertical_list.get(0).link);
    }

    public boolean onlyTitle() {
        if (!isValid() && !hasVerticalList()) {
            return true;
        }
        TopMessageV2ItemBean bean = t.vertical_list.get(0);
        return EmptyUtils.isEmpty(bean.icon_img) && EmptyUtils.isEmpty(bean.countdown) && EmptyUtils.isEmpty(bean.content.sub_content);
    }

    public boolean contentIsVisible() {
        return isValid() && hasVerticalList() && (!EmptyUtils.isEmpty(t.vertical_list.get(0).content) && !EmptyUtils.isEmpty(t.vertical_list.get(0).content.sub_content));
    }

    public boolean rightImgIsVisible() {
        return isValid() && hasVerticalList() && !EmptyUtils.isEmpty(t.vertical_list.get(0).right_arrow) && t.vertical_list.get(0).right_arrow.type == 1 && !EmptyUtils.isEmpty(t.vertical_list.get(0).right_arrow.img);
    }

    public boolean rightTextIsVisible() {
        return isValid() && hasVerticalList() && !EmptyUtils.isEmpty(t.vertical_list.get(0).right_arrow) && t.vertical_list.get(0).right_arrow.type == 2 && !EmptyUtils.isEmpty(t.vertical_list.get(0).right_arrow.title);
    }

    public TopMessageV2ItemBean getBesideLeft() {
        return hasBesideListList() ? t.beside_list.get(0) : null;
    }

    public TopMessageV2ItemBean getBesideRight() {
        return (hasBesideListList() && t.beside_list.size() > 1) ? t.beside_list.get(1) : null;
    }

}
