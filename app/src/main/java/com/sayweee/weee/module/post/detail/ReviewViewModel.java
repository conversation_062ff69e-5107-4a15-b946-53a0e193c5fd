package com.sayweee.weee.module.post.detail;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cate.product.bean.GroupProduct;
import com.sayweee.weee.module.post.detail.bean.CommentStatsData;
import com.sayweee.weee.module.post.detail.bean.PostBannerData;
import com.sayweee.weee.module.post.detail.bean.PostCommentData;
import com.sayweee.weee.module.post.detail.bean.PostContentData;
import com.sayweee.weee.module.post.detail.bean.ReviewCommentBean;
import com.sayweee.weee.module.post.detail.bean.ReviewDeleteReplyData;
import com.sayweee.weee.module.post.detail.bean.ReviewDetailBean;
import com.sayweee.weee.module.post.detail.bean.ReviewReplyData;
import com.sayweee.weee.module.post.service.PostApi;
import com.sayweee.weee.module.post.shared.CmtFailureData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.ExceptionHandler;
import com.sayweee.wrapper.http.support.RequestParams;

import okhttp3.RequestBody;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2021/7/3.
 * Desc:
 */
public class ReviewViewModel extends BaseViewModel<BaseLoaderModel<PostApi>> {

    public MutableLiveData<ReviewDetailBean> reviewOriginData = new MutableLiveData<>();

    public MutableLiveData<ReviewReplyData> reviewReplyData = new MutableLiveData<>();

    public MutableLiveData<ReviewCommentBean> reviewCommentData = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> reviewAdapterData = new MutableLiveData<>();

    public MutableLiveData<ShareBean> shareData = new MutableLiveData<>();

    public MutableLiveData<SimpleResponseBean> parseData = new MutableLiveData<>();

    public MutableLiveData<Integer> failDetailData = new MutableLiveData<>();

    public MutableLiveData<ReviewDeleteReplyData> deleteData = new MutableLiveData<>();

    public MutableLiveData<ProductDetailBean> productDetailData = new MutableLiveData<>();


    public ReviewViewModel(@NonNull Application application) {
        super(application);
    }

    public void getReviewDetail(boolean isSilent, int reviewId, int page, int limit, String mid) {
        getLoader().getHttpService().getReviewDetail(reviewId, mid)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ReviewDetailBean>>() {

                    @Override
                    public void onBegin() {
                        if (!isSilent) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<ReviewDetailBean> response) {
                        if (response.getData().status.equalsIgnoreCase("X")) {
                            failDetailData.postValue(0);
                        } else {
                            getReviewComment(isSilent, reviewId, page, response.getData(), limit, mid);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (failure.getErrorCode() == ExceptionHandler.ERROR_RESPONSE_DATA_NULL) {
                            failDetailData.postValue(failure.getErrorCode());
                        }
                    }

                    @Override
                    public void onFinish() {
                        if (!isSilent) {
                            setLoadingStatus(false);
                        }
                    }
                });

    }

    public void getReviewComment(boolean isSilent, int reviewId, int page, ReviewDetailBean detail, int limit, String mid) {
        getLoader().getHttpService().getReviewComments(reviewId, page, limit, mid)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ReviewCommentBean>>() {

                    @Override
                    public void onBegin() {
                        if (!isSilent) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<ReviewCommentBean> response) {
                        if (detail != null) {
                            processPostData(reviewId, detail, response.getData());
                        } else {
                            reviewCommentData.postValue(response.getData());
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (detail != null) {
                            processPostData(reviewId, detail, null);
                        }
                    }

                    @Override
                    public void onFinish() {
                        if (!isSilent) {
                            setLoadingStatus(false);
                        }
                    }
                });
    }

    public void getReviewComment(boolean isSilent, int reviewId, int page, int limit, String mid) {
        getReviewComment(isSilent, reviewId, page, null, limit, mid);
    }

    private void processPostData(int reviewId, ReviewDetailBean detail, ReviewCommentBean bean) {
        reviewOriginData.postValue(detail);
        List<AdapterDataType> list = new ArrayList<>();
        list.add(new PostBannerData(ReviewDetailAdapter.TYPE_BANNER, detail.pictures)
                .setLikeNum(detail.like_count)
                .setLikeStatus(detail.is_set_like)
                .setCommentNum(detail.comments_count));
        list.add(new PostContentData(detail));
        if (detail.product != null) {
            list.add(new AdapterProductData(ReviewDetailAdapter.TYPE_PRODUCT, detail.product).setProductSource(String.format(Constants.Source.PORTAL_POST_DETAIL, "p", reviewId)));
        }
        if (bean != null) {
            list.add(new CommentStatsData(ReviewDetailAdapter.TYPE_COMMENT_STATS, detail.comments_count, bean.total));
            if (bean.total <= 0 && EmptyUtils.isEmpty(bean.list)) {
                list.add(new SimpleAdapterDataType(ReviewDetailAdapter.TYPE_COMMENT_EMPTY));
            } else {
                for (ReviewCommentBean.CommentItemBean item : bean.list) {
                    PostCommentData comment = new PostCommentData(item);
                    list.addAll(comment.getAdapterData());
                }
            }
        }
        reviewAdapterData.postValue(list);
    }

    /**
     * 评论
     *
     * @param reviewId
     * @param content
     */
    public void postComment(int reviewId, String content, int limit) {
        getLoader().getHttpService().postReviewComment(reviewId, new RequestParams().put("content", content).create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        getReviewDetail(true, reviewId, 1, limit, null);
                    }

                });

    }

    /**
     * 回复
     *
     * @param reviewId
     * @param commentId
     * @param parentId
     * @param content
     */
    public void postReply(int reviewId, int commentId, int parentId, String content, int limit, String mid) {
        getLoader().getHttpService().postReviewtoComment(reviewId, commentId, new RequestParams().put("content", content).create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        getMoreReplyData(reviewId, parentId, 1, true, limit, mid);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    public void deleteReviewComment(int reviewId, int targetId, int parentId) {
        getLoader().getHttpService().deleteReviewComment(reviewId, targetId)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        deleteData.postValue(new ReviewDeleteReplyData(targetId, parentId));
                    }
                });

    }

    public void postCommentPraise(int reviewId, int commentId, boolean isPraise) {
        getLoader().getHttpService().reviewCommentPraise(reviewId, commentId, new RequestParams().put("status", !isPraise ? "C" : "A").create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        parseData.postValue(response);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    public void getMoreReplyData(int reviewId, int parentCommentId, int page, int limit, String mid) {
        getMoreReplyData(reviewId, parentCommentId, page, false, limit, mid);
    }

    public void getMoreReplyData(int reviewId, int parentCommentId, int page, boolean isReset, int limit, String mid) {
        getLoader().getHttpService().getMoreReviewComment(reviewId, parentCommentId, page, limit, mid)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ReviewCommentBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ReviewCommentBean> response) {
                        ReviewReplyData replyData = new ReviewReplyData(parentCommentId, response.getData());
                        replyData.setResetData(isReset);
                        reviewReplyData.postValue(replyData);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });
    }

    /**
     * 分享
     *
     * @param reviewId
     */
    public void getReviewShare(int reviewId) {
        getLoader().getHttpService().getReviewShare(reviewId)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ViewModelResponseObserver<ResponseBean<ShareBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ShareBean> response) {
                        shareData.postValue(response.getData());
                    }
                });
    }

    /**
     * 关注 / 取消关注
     */
    public void followUser(String uid, boolean isFollow) {
        RequestBody requestBody =
                new RequestParams().put("user_id", uid).put("status", isFollow ? "A" : "C").create();
        getLoader().getHttpService().postFollow(requestBody)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        parseData.postValue(response);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        CmtSharedViewModel.get().postSpamFailureData(new CmtFailureData(
                                failure, CmtFailureData.TYPE_SPAM_FOLLOW));
                    }
                });
    }

    public void getProductDetail(int productId, boolean isSilent) {
        getLoader().getHttpService().getProductDetail(productId, null)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<ProductDetailBean>>() {

                    @Override
                    public void onBegin() {
                        if (!isSilent) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<ProductDetailBean> response) {
                        //对product group安全校验
                        ProductDetailBean data = response.getData();
                        if (data.product != null && data.product.hasGroup()) {
                            ArrayList<GroupProduct> invalid = new ArrayList<>();
                            int size = data.product.group.propertyList.size();
                            for (GroupProduct product : data.product.group.groupProductList) {
                                List<String> ids = product.splitValueIds();
                                if (ids == null || ids.size() == 0 || ids.size() != size) {
                                    invalid.add(product);
                                }
                            }
                            if (invalid.size() > 0) {
                                data.product.group.groupProductList.removeAll(invalid);
                            }
                        }

                        productDetailData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showMessage(failure);
                    }

                    @Override
                    public void onFinish() {
                        if (!isSilent) {
                            setLoadingStatus(false);
                        }
                    }
                });
    }
}
