package com.sayweee.weee.module.cart;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.adapter.CartAdapter;
import com.sayweee.weee.module.cart.adapter.OnSectionCartEditListener;
import com.sayweee.weee.module.cart.adapter.OnSectionCartSave4LaterListener;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.adapter.SectionCartAdapter;
import com.sayweee.weee.module.cart.bean.AdapterPanelData;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.CartTopMessageData;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.WrapperUpSellBean;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartSectionData;
import com.sayweee.weee.module.cart.bean.setcion.CartSectionType;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartCheckoutButtonData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartCollapsedData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartDealData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartFreeShippingData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartProductData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartSave4LaterMoreData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartStatsData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartTipsData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartTitleData;
import com.sayweee.weee.module.cart.service.CartSectionViewModel;
import com.sayweee.weee.module.cart.service.MkplHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cart.widget.CheckOutBottomView;
import com.sayweee.weee.module.cate.bean.VendorIntroductionBean;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.checkout.SectionAlcoholAgreementActivity;
import com.sayweee.weee.module.checkout.SectionUpsellActivity;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.module.seller.common.mpager.MPagerEntity;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.VibratorManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.bean.EagleElement;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.timer.ReminderBannerManager;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ScrollTopScroller;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.view.WrapperMvvmFragment;
import com.sayweee.wrapper.listener.OnAdapterChildClickListener;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CartSectionFragment extends WrapperMvvmFragment<CartSectionViewModel> {

    private SmartRefreshLayout refreshLayout;
    private View indicatorRoot;
    private CompatMagicIndicator indicatorPanel;
    private View indicatorShadow;
    private RecyclerView rvCart;
    private SectionCartAdapter adapter;
    private EagleImpressionTrackerIml eagleImpressionTracker;
    private NewPreOrderBean preOrder;
    private int thresholdValue; //可见阙值

    private WrapperDialog popupDialog;
    private StaggeredGridLayoutManager layoutManager;
    private RecyclerView.OnScrollListener onScrollListener;

    public static Fragment newInstance() {
        CartSectionFragment fragment = new CartSectionFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_cart_pager;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        eagleImpressionTracker = new EagleImpressionTrackerIml();
        refreshLayout = findViewById(R.id.mSmartRefreshLayout);
        rvCart = findViewById(R.id.mRecyclerView);
        indicatorRoot = findViewById(R.id.fl_indicator_panel);
        indicatorPanel = findViewById(R.id.indicator);
        indicatorShadow = findViewById(R.id.v_shadow_indicator_panel);
        adapter = new SectionCartAdapter();
        adapter.setOnMkplShopMoreActionListener(this::prepareShowGroupOrderDialog);
        layoutManager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        rvCart.setLayoutManager(layoutManager);
        rvCart.setHasFixedSize(true);
        rvCart.setItemViewCacheSize(30);
        rvCart.getRecycledViewPool().setMaxRecycledViews(CartAdapter.TYPE_PANEL, 5);
        rvCart.setAdapter(adapter);
        setOnAdapterItemChildClickListener();
        setOnCartEditListener();
        setOnCartSave4LaterListener();
        setOnIndicatorClickListener();
        setRefreshListener();
        setLoadMoreConfig();
        thresholdValue = CommonTools.getWindowHeight(activity) * 2 / 3;
        setBottomViewVisible(/* isVisible= */false);
        showVeil(true);

        new ProductTraceObserver(this) {
            @Override
            protected void handleProductSalesTraceChange() {
                ProductTraceViewHelper.notify(rvCart);
            }
        }.setExtraTopic(WeeeEvent.PageView.CART).start();
    }

    @Override
    public void loadData() {
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD,
                WeeeEvent.PageView.CART, String.valueOf(viewModel.hashCode()));
        viewModel.getPanelData();
    }

    @Override
    public void attachModel() {
        viewModel.cartData.observe(this, new Observer<NewPreOrderBean>() {
            @Override
            public void onChanged(NewPreOrderBean preOrder) {
                CartSectionFragment.this.preOrder = preOrder;
                fillBottomView(preOrder);
            }
        });
        viewModel.adapterCartData.observe(this, new Observer<List<AdapterCartSectionData>>() {
            @Override
            public void onChanged(List<AdapterCartSectionData> adapterCartSectionDataList) {
                adapter.setPacketCartData(adapterCartSectionDataList);
                reportEagleImpressionEvent();
                showVeil(false);
                refreshLayout.finishRefresh();
                ensureOnScrollListener();
            }
        });
        viewModel.adapterCartDataSilent.observe(this, new Observer<List<AdapterCartSectionData>>() {
            @Override
            public void onChanged(List<AdapterCartSectionData> adapterCartSectionDataList) {
                adapter.setPacketCartDataSilent(adapterCartSectionDataList);
                reportEagleImpressionEvent();
            }
        });
        viewModel.upSellData.observe(this, new Observer<WrapperUpSellBean>() {
            @Override
            public void onChanged(WrapperUpSellBean bean) {
                int alcoholAgreementType = AlcoholHelper.getAlcoholAgreementTypeUpsell(preOrder);
                boolean isShowAlcoholAgreement = alcoholAgreementType != Constants.AlcoholAgreementType.NONE;
                setBottomViewCanCheckout(/* canCheckout= */true);
                Intent a;
                if (bean.dispatchUpSell() && preOrder != null) {
                    a = SectionUpsellActivity.getIntent(activity, bean.upSellBean, Double.parseDouble(preOrder.final_amount), isShowAlcoholAgreement);
                } else {
                    if (isShowAlcoholAgreement) {
                        a = SectionAlcoholAgreementActivity.getIntent(
                                activity,
                                viewModel.domainValue,
                                /* alcoholAgreementType= */ Constants.AlcoholAgreementType.GROCERY
                        );
                    } else {
                        a = CheckoutSectionActivity.getIntent(activity, viewModel.domainValue, null);
                    }
                }
                startActivity(a);
            }
        });

        viewModel.vendorIntroductionData.observe(this, new Observer<VendorIntroductionBean>() {
            @Override
            public void onChanged(VendorIntroductionBean bean) {
                MkplHelper.showVendorIntroduction(activity, bean);
            }
        });
        viewModel.adapterPanelData.observe(this, new Observer<AdapterPanelData>() {
            @Override
            public void onChanged(AdapterPanelData panelData) {
                if (panelData.isFirstLoad()) {
                    adapter.setPacketPanelData(panelData);
                    setIndicatorPanel(panelData);
                } else {
                    adapter.addPacketPanelData(panelData);
                }

                if (panelData.isFirstLoad()) {
                    adapter.setEnableLoadMore(true);
                }
                if (viewModel.isMultiData) {
                    //双panel，两种数据
                    if (indicatorPanel.getSelectedIndex() == 0) {
                        if (panelData.isLoadRecommendDataEnd()) {
                            adapter.loadMoreEnd();
                        } else {
                            adapter.loadMoreComplete();
                        }
                    } else if (indicatorPanel.getSelectedIndex() == 1) {
                        if (panelData.isLoadBoughtDataEnd()) {
                            adapter.loadMoreEnd();
                        } else {
                            adapter.loadMoreComplete();
                        }
                    }
                } else {
                    //单panel，仅有其中一种数据
                    boolean end = panelData.isLoadBoughtDataEnd() && panelData.isLoadRecommendDataEnd();
                    if (end) {
                        //加载已完成
                        adapter.loadMoreEnd();
                    } else {
                        //还有更多数据
                        adapter.loadMoreComplete();
                    }
                }
            }
        });

        viewModel.adapterCartTopSectionData.observe(this, data -> adapter.setTopSectionData(data));

        // Group order
        viewModel.sellerGroupStatusLiveData.observe(this, this::handleSellerGroupStatusLiveData);

        viewModel.sectionCartDealChangeData.observe(this, new Observer<SectionCartDealData>() {
            @Override
            public void onChanged(SectionCartDealData sectionCartDealData) {
                adapter.changeSectionCartDealData(sectionCartDealData);
            }
        });

        // https://sayweee.atlassian.net/browse/PEP-7106
        // Reload panel data when address changed
        SharedOrderViewModel.get().preOrderRecreateData.observe(this, type -> viewModel.getPanelData());

        // Individual checkout
        viewModel.sectionCheckoutData.observe(this, section -> {
            NewPreOrderBean cart = preOrder;
            Context act = getActivity();
            if (act != null && section != null && cart != null) {
                int alcoholAgreementType = AlcoholHelper.getAlcoholAgreementType(
                        /* orderSubType= */section.sub_type,
                        /* containsAlcohol= */cart.contain_alcohol,
                        /* isRecordAlcohol= */cart.is_record_alcohol,
                        /* isRecordSellerAlcohol= */cart.is_record_seller_alcohol
                );
                boolean isShowAlcoholAgreement = AlcoholHelper.isShowAlcoholAgreement(alcoholAgreementType);
                Intent a;
                if (isShowAlcoholAgreement) {
                    a = SectionAlcoholAgreementActivity.getIntent(
                            act,
                            Constants.CartDomain.DOMAIN_GROCERY,
                            /* alcoholAgreementType= */ alcoholAgreementType
                    );
                } else {
                    a = CheckoutSectionActivity.getIntent(act, Constants.CartDomain.DOMAIN_GROCERY, null);
                }
                startActivity(a);
            }
        });

        viewModel.failureData.observe(this, failureBean -> {
            if (CartSectionViewModel.FAILURE_SECTION_CHECKOUT.equals(failureBean.getObject())) {
                // 单独结算流程，选择购物车接口失败，重新载入底部结算栏
                if (preOrder != null) {
                    fillBottomView(preOrder);
                }
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getActivity() != null) {
            WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.CART,
                    String.valueOf(getActivity().hashCode()));
        }
        setBottomViewLoading();
        refreshTopBanner();
        viewModel.refreshCartData();
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        adapter.onPageResume();
        eagleImpressionTracker.onPageResume(rvCart);
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause();
        eagleImpressionTracker.onPagePause(rvCart);
        if (popupDialog != null && popupDialog.isShowing()) {
            popupDialog.dismiss();
        }
    }

    @Nullable
    protected CheckOutBottomView getSafeBottomView() {
        Fragment fragment = getParentFragment();
        if (fragment instanceof CartSectionPanelFragment) {
            return ((CartSectionPanelFragment) getParentFragment()).getSafeBottomView();
        }
        return null;
    }

    protected void setBottomViewLoading() {
        CheckOutBottomView view = getSafeBottomView();
        if (view != null) {
            view.loading();
        }
    }

    protected void setBottomViewCanCheckout(boolean canCheckout) {
        CheckOutBottomView view = getSafeBottomView();
        if (view != null) {
            view.setCanCheckOut(canCheckout);
        }
    }

    protected void setBottomViewVisible(boolean isVisible) {
        CheckOutBottomView view = getSafeBottomView();
        if (view != null) {
            view.setVisibility(isVisible ? View.VISIBLE : View.GONE);
        }
    }

    private boolean isBottomViewInteractivity() {
        CheckOutBottomView view = getSafeBottomView();
        if (view != null) {
            return view.isInteractivity();
        }
        return false;
    }

    private void setOnAdapterItemChildClickListener() {
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter quickAdapter, View view, int position) {
                removeCartEditMode();
                AdapterDataType item = adapter.getItem(position);
                int id = view.getId();
                if (id == R.id.btn_to_shopping) {
                    SharedViewModel.get().toHome();
                } else if (id == R.id.tv_delivery_date) { //切换日期
                    if (item instanceof SectionCartStatsData && ((SectionCartStatsData) item).isSupportChangeDate) {
                        changeDateTrackClickAction(((SectionCartStatsData) item).sectionType);
                        startActivity(DateActivity.getIntent(activity, null, "cart"));
                    }
                } else if (id == R.id.layout_tips) { //ShopMoreInfo按钮
                    if (item instanceof SectionCartTipsData) {
                        startActivity(WebViewActivity.getIntent(activity, ((SectionCartTipsData) item).shopMoreInfo.url));
                    }
                } else if (id == R.id.iv_collect) { //收藏
                    if (item instanceof AdapterProductData && ((AdapterProductData) item).t instanceof ProductBean) {
                        if (AccountManager.get().isLogin()) {
                            int productId = (((AdapterProductData) item).t).id;
                            CollectManager.get().toggleProductCollect(productId);
                            adapter.notifyItemChanged(position, item);
                        } else {
                            startActivity(AccountIntentCreator.getIntent(activity));
                        }
                    }
                } else if (id == R.id.layout_product) { //商品
                    if (item instanceof SectionCartProductData) {
                        SectionCartProductData productData = (SectionCartProductData) item;

                        Map<String, Object> element = productData.getElement();
                        EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                                .addElement(element)
                                .setTargetNm(String.valueOf(productData.t.product_id))
                                .setTargetPos(position)
                                .setTargetType(EagleTrackEvent.TargetType.PRODUCT)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setIsSelect(null)
                                .setIsMkpl(productData.isMkplProduct())
                                .addCtx(new TrackParams().put("volume_price_support", productData.t.volume_price_support).get());
                        AppAnalytics.logClickAction(builder.build().getParams());

                        //PDP click
                        ProductBean productBean = OrderHelper.toProductBeanV5(productData.t, productData.convertSoldStatus());
                        productBean.price = -1;
                        startActivity(ProductIntentCreator.getIntent(activity, productBean));
                    } else if (item instanceof AdapterProductData) {
                        startActivity(ProductIntentCreator.getIntent(activity, ((AdapterProductData) item).t));
                    }
                } else if (id == R.id.layout_status) { //失效商品 切换日期
                    if (item instanceof SectionCartProductData && ((SectionCartProductData) item).isInvalidProduct()) {
                        SectionCartProductData productData = (SectionCartProductData) item;
                        NewItemBean bean = productData.t;
                        if (OrderHelper.isInvalidByChangeDate(bean.reason_type)) {
                            startActivity(DateActivity.getIntent(activity, String.valueOf(bean.product_id), "cart"));
                        } else if (OrderHelper.isInvalidBySoldOut(bean.reason_type)) {
                            if (AccountManager.get().isLogin()) {
                                if (!CollectManager.get().isProductCollect(bean.product_id)) {
                                    CollectManager.get().toggleProductCollect(bean.product_id);
                                    adapter.notifyItemChanged(position);
                                    //购物车失效商品点击上架提醒
                                    EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                                            .addElement(productData.getElement())
                                            .setTargetNm(String.valueOf(bean.product_id))
                                            .setTargetPos(position)
                                            .setTargetType(EagleTrackEvent.TargetType.PRODUCT_NOTIFY_ME)
                                            .setClickType(EagleTrackEvent.ClickType.NORMAL)
                                            .setIsMkpl(productData.isMkplProduct());
                                    AppAnalytics.logClickAction(builder.build().getParams());
                                }
                            } else {
                                startActivity(AccountIntentCreator.getIntent(activity));
                            }
                        }
                    }
                } else if (id == R.id.btn_arrow) {//展开收起
                    if (item instanceof SectionCartTitleData) {
                        SectionCartTitleData data = (SectionCartTitleData) item;
                        viewModel.collapseSection(data.cartId, !data.isCollapsedStatus);
                        if (!data.isCollapsedStatus) {//收起行为
                            RecyclerView.LayoutManager layoutManager = rvCart.getLayoutManager();
                            if (layoutManager == null) return;
                            RecyclerView.SmoothScroller scroller = new ScrollTopScroller(activity);
                            scroller.setTargetPosition(position);
                            layoutManager.startSmoothScroll(scroller);
                        }
                        vibrate();//展开收起
                    }
                } else if (id == R.id.layout_cart_items) {//展开
                    if (item instanceof SectionCartCollapsedData) {
                        SectionCartCollapsedData data = (SectionCartCollapsedData) item;
                        viewModel.collapseSection(data.cartId, false);//展开
                        vibrate();//展开
                    }
                } else if (id == R.id.iv_terms_title) {
                    if (item instanceof SectionCartTitleData) {
                        SectionCartTitleData data = (SectionCartTitleData) item;
                        if (data.isSeller) {
                            viewModel.getVendorIntroduction(String.valueOf(data.vendorId));
                        } else if (data.isPantry) {
                            PantryHelper.toPantryTerms();
                        }
                    }
                } else if (id == R.id.layout_section_title) {
                    if (item instanceof SectionCartTitleData) {
                        SectionCartTitleData data = (SectionCartTitleData) item;
                        if (data.isSeller) {
                            startActivity(SellerActivity.getIntent(activity, String.valueOf(data.vendorId)));
                        }
                    }
                } else if (id == R.id.cl_free_shipping_root) {
                    if (item instanceof SectionCartFreeShippingData) {
                        handleActivityFreeShippingClick(((SectionCartFreeShippingData) item));
                    }
                } else if (id == R.id.layout_top_message) {
                    if (item instanceof CartTopMessageData) {
                        handleTopMessageClick(((CartTopMessageData) item));
                    }
                } else if (id == R.id.iv_delivery_fee || id == R.id.iv_service_fee) {
                    if (item instanceof SectionCartStatsData) {
                        handleFeeInfoClick((SectionCartStatsData) item, id);
                    }
                } else if (id == R.id.btn_checkout_individual) {
                    if (item instanceof SectionCartCheckoutButtonData) {
                        onIndividualCheckoutButtonClick((SectionCartCheckoutButtonData) item);
                    }
                }
            }
        });
    }

    private void setOnCartEditListener() {
        adapter.setOnSectionCartEditListener(new OnSectionCartEditListener() {
            @Override
            public void editProductData(SectionCartProductData data, int index, boolean isAdd) {
                viewModel.editProductData(data, data.t.quantity);
                if (isAdd) {
                    PopupManager.get().showAdd2PurchaseNotificationTips();
                    //add_to_cart 埋点 FirebaseAnalytics.Event.ADD_TO_CART
                    AppTracker.get().trackByGA(FirebaseAnalytics.Event.ADD_TO_CART, new TrackParams()
                            .put(FirebaseAnalytics.Param.QUANTITY, 1)
                            .put(FirebaseAnalytics.Param.ITEM_ID, data.t.product_id)
                            .put(FirebaseAnalytics.Param.ITEM_NAME, data.t.title)
                            .put(FirebaseAnalytics.Param.ITEM_CATEGORY, null)
                            .get());
                }
            }

            @Override
            public void editProductData(AdapterProductData data, boolean isAdd) {
                viewModel.editProductData(data, isAdd);
            }

            @Override
            public void removeProductData(SectionCartProductData data) {
                removeCartEditMode();
                viewModel.removeCartProductData(data);
                vibrate();//删除
            }
        });

    }

    private void setOnCartSave4LaterListener() {
        adapter.setOnSectionCartSave4LaterListener(new OnSectionCartSave4LaterListener() {
            @Override
            public void moveSave4Later(String productKey, boolean isToSave4Later) {
                removeCartEditMode();
                if (!AccountManager.get().isLogin()) {
                    startActivity(AccountIntentCreator.getIntent(activity));
                    return;
                }
                if (isToSave4Later) {
                    viewModel.cartToSave4Later(productKey);
                } else {
                    viewModel.save4LaterToCart(productKey);
                }
                vibrate();//稍后再买
            }

            @Override
            public void removeSave4Later(String productKey) {
                removeCartEditMode();
                viewModel.deleteSave4Later(productKey);
                vibrate();//删除稍后再买
            }

            @Override
            public void loadMore(SectionCartSave4LaterMoreData item) {
                removeCartEditMode();
                viewModel.loadMoreSave4LaterV2(item.pageSize);
                vibrate();//稍后再买加载更多
            }
        });
    }

    private void setOnIndicatorClickListener() {
        adapter.setOnIndicatorClickListener(new OnIndicatorClickListener() {
            @Override
            public void onClick(int index, MPagerEntity entity) {
                if (indicatorPanel != null) {
                    handleIndicatorSelected(index);
                }
            }
        });
    }

    private void setRefreshListener() {
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                refreshTopBanner();
                viewModel.refreshCartData();
                viewModel.getPanelData();
            }
        });
    }

    private void setLoadMoreConfig() {
        if (adapter != null && rvCart != null) {
            adapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
                @Override
                public void onLoadMoreRequested() {
                    viewModel.getMorePanelData(adapter.indexIndicator);
                }
            }, rvCart);
            adapter.setEnableLoadMore(false);
        }
    }

    private void fillBottomView(NewPreOrderBean preOrder) {
        CheckOutBottomView bottomView = getSafeBottomView();
        if (bottomView == null) {
            return;
        }

        int validSectionCount = 0;
        int individualSectionCount = 0;
        for (NewSectionBean section : CollectionUtils.orEmpty(preOrder.sections)) {
            boolean isValid = !section.isAllInvalid();
            if (isValid) {
                validSectionCount++;
                if (section.isIndividualCheckout()) {
                    individualSectionCount++;
                }
            }
        }
        boolean isShow = validSectionCount > 0;
        boolean isShowFakeCheckOut = false;
        boolean isAllIndividualCheckout = individualSectionCount > 0 && individualSectionCount == validSectionCount;
        if (isAllIndividualCheckout) {
            // 如果全都是独立结算的 section，则不显示底部结算栏
            isShow = false;
        }
        if (preOrder.hasTopPromotions()) {
            isShowFakeCheckOut = true;//新版赠品也显示bottom view
        }
        setBottomViewVisible(/* isVisible= */isShow || isShowFakeCheckOut);
        setBottomViewCanCheckout(/* canCheckout= */isShow || isShowFakeCheckOut || isAllIndividualCheckout);
        bottomView.setAmount(preOrder.final_amount, preOrder.base_final_amount)
                .setCouponReminder(preOrder.coupon_reminder)
                .setAttachedData();
        boolean finalIsShowFakeCheckOut = isShowFakeCheckOut;
        boolean finalIsShow = isShow;
        bottomView.setOnViewClickListener(R.id.tv_checkout, new OnSafeClickListener(1800) {
            @Override
            public void onClickSafely(View v) {
                Map<String, Object> content = new TrackParams()
                        .put("click_result", null)
                        .put("target_pos", null)
                        .put("is_mkpl", null)
                        .put("is_select", null)
                        .put("click_type", "normal")
                        .put("target_nm", "Checkout")
                        .put("target_type", "normal_button").get();
                Map<String, Object> context = new TrackParams()
                        .put("purchase_amount", preOrder.final_amount).get();
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .addElement(EagleTrackManger.get().getElement(null, -1, null, -1))
                        .addContent(content)
                        .addCtx(context)
                        .build().getParams());
                if (AccountManager.get().isLogin()) {
                    if (finalIsShowFakeCheckOut && !finalIsShow) {
                        Toaster.showToast(getString(R.string.add_at_least_1_item_to_cart));
                    } else {
                        onCheckoutButtonClick(preOrder);
                    }
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }
        });
    }

    private void onCheckoutButtonClick(NewPreOrderBean preOrder) {
        if (isSingleCartSection(preOrder)) {
            viewModel.upSell();
            setBottomViewLoading();
        } else {
            String tag = CartSelectionFragment.class.getSimpleName();
            CartSelectionFragment fragment = CartSelectionFragment.newInstance(preOrder);
            fragment.show(getChildFragmentManager(), tag);
        }
    }

    private void onIndividualCheckoutButtonClick(SectionCartCheckoutButtonData item) {
        EagleElement eagleElement = item.getEagleElement();
        Map<String, Object> content = new TrackParams()
                .put("target_nm", EagleTrackEvent.TargetNm.CHECKOUT_CART)
                .put("target_type", EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .put("target_pos", eagleElement != null ? eagleElement.getSecPos() : -1)
                .put("click_type", EagleTrackEvent.ClickType.VIEW)
                .get();
        Map<String, Object> ctx = new TrackParams()
                .putNonNull("vendor_id", item.getVendorId())
                .get();
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .addElement(eagleElement != null ? eagleElement.asMap() : null)
                .addContent(content)
                .addCtx(ctx)
                .build()
                .getParams()
        );

        Activity act = getActivity();
        if (act == null) {
            return;
        }
        if (!isBottomViewInteractivity()) {
            // 在选中购物车接口没有回来之前，防止频繁操作
            return;
        }
        if (AccountManager.get().isLogin()) {
            vibrate();
            setBottomViewLoading();
            viewModel.requestIndividualSectionCheckout(item.getData());
        } else {
            startActivity(AccountIntentCreator.getIntent(act));
        }
    }

    private boolean isSingleCartSection(NewPreOrderBean preOrder) {
        int qty = 0;
        if (preOrder.sections != null) {
            for (NewSectionBean section : preOrder.sections) {
                if (!section.isAllInvalid()) {
                    qty = qty + 1;
                }
            }
        }
        return qty == 1;
    }

    public void removeCartEditMode() {
        if (adapter != null) {
            adapter.clearEditMode();
        }
    }

    private void reportEagleImpressionEvent() {
        if (rvCart != null) {
            rvCart.post(new Runnable() {
                @Override
                public void run() {
                    if (isAdded() && isVisible() && eagleImpressionTracker != null) {
                        eagleImpressionTracker.trackImpression(rvCart);
                    }
                }
            });
        }
    }

    public void vibrate() {
        VibratorManager.vibrate();
    }

    private void calcAtTopVisible(int position) {
        if (position < 4) {
            int offset = rvCart.computeVerticalScrollOffset();
            if (getParentFragment() instanceof CartSectionPanelFragment) {
                ((CartSectionPanelFragment) getParentFragment()).showLayoutTop(offset >= thresholdValue);
            }
        } else {
            if (getParentFragment() instanceof CartSectionPanelFragment) {
                ((CartSectionPanelFragment) getParentFragment()).showLayoutTop(true);
            }
        }
    }

    public void scrollToTop() {
        if (rvCart != null && adapter != null && !adapter.getData().isEmpty()) {
            rvCart.smoothScrollToPosition(0);
        }
    }

    private int getPanelTitleIndex() {
        if (adapter != null) {
            return CollectionUtils.indexOfFirst(
                    adapter.getData(),
                    it -> it.getType() == CartSectionType.CART_SECTION_PANEL_TITLE
            );
        }
        return -1;
    }

    private void setIndicatorPanel(AdapterPanelData panelData) {
        List<String> list = new ArrayList<>();
        if (panelData.boughtData != null && !panelData.boughtData.isEmpty()
                && panelData.recommendData != null && !panelData.recommendData.isEmpty()
        ) {
            //两种商品都有数据
            list.add(getString(R.string.s_cart_recommend_for_u));
            list.add(getString(R.string.s_cart_bought));
        } else {
            if (panelData.boughtData != null && !panelData.boughtData.isEmpty()) {
                list.add(getString(R.string.s_cart_bought));
            } else if (panelData.recommendData != null && !panelData.recommendData.isEmpty()) {
                list.add(getString(R.string.s_cart_recommend_for_u));
            }
        }
        CartIndicatorHelper.fillPanelIndicator(
                activity,
                indicatorPanel,
                CommonTools.dp2px(12),
                list,
                ContextCompat.getColor(activity, R.color.root_color_white_static),
                ContextCompat.getColor(activity, R.color.color_surface_1_fg_default_idle),
                ContextCompat.getColor(activity, R.color.text_lesser),
                new OnAdapterChildClickListener() {
                    @Override
                    public void onAdapterChildClick(View view, int position) {
                        handleIndicatorSelected(position);
                        if (adapter != null) {
                            adapter.handleIndicatorSelected(position);
                        }
                        int panelTitleIndex = getPanelTitleIndex();
                        if (position == 1 && panelTitleIndex != -1) {
                            layoutManager.scrollToPositionWithOffset(panelTitleIndex, 0);
                        }
                    }
                }
        );
        handleIndicatorSelected(0);
    }

    private void handleIndicatorSelected(int position) {
        indicatorPanel.getIndicatorHelper().handlePageSelected(position);
        if (viewModel != null && viewModel.isMultiData) {
            //双panel，两种数据
            if (position == 0) {
                if (!viewModel.isRecommendDataLoadEnd) {
                    adapter.loadMoreComplete();
                } else {
                    adapter.loadMoreEnd();
                }
            } else if (position == 1) {
                if (!viewModel.isBoughtDataLoadEnd) {
                    adapter.loadMoreComplete();
                } else {
                    adapter.loadMoreEnd();
                }
            }
        }
    }

    private void showVeil(boolean visible) {
        VeilTools.show(findViewById(R.id.vl_section_cart_fragment), visible);
    }

    private void setPanelIndicatorDisplay(boolean visible) {
        if (getParentFragment() instanceof CartSectionPanelFragment) {
            ((CartSectionPanelFragment) getParentFragment()).setInnerShadowShow(visible);
        }
        ViewTools.setViewVisibilityIfChanged(indicatorRoot, visible);
        ViewTools.setViewVisibilityIfChanged(indicatorShadow, visible);
    }

    private void changeDateTrackClickAction(String secNm) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(EagleTrackEvent.TargetNm.DELIVERY_DATE)
                .setMod_nm(EagleTrackEvent.ModNm.CART)
                .setSec_nm(secNm)
                .setMod_pos(0)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    public void prepareShowGroupOrderDialog(String vendorId) {
        Context context = getContext();
        if (context == null) return;
        if (EmptyUtils.isEmpty(vendorId)) return;

        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */"cart",
                /* modPos= */-1,
                /* secNm= */"seller",
                /* secPos= */-1,
                /* targetNm= */EagleTrackEvent.TargetNm.GROUP_ORDER,
                /* targetPos= */-1,
                /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType= */EagleTrackEvent.ClickType.VIEW,
                /* ctx= */new EagleContext().setGlobalVendor(vendorId).asMap()
        );

        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(context));
            return;
        }
        viewModel.checkGroupOrderExists(vendorId);
    }

    private void handleSellerGroupStatusLiveData(@Nullable SellerGroupStatusBean response) {
        if (response == null) return;
        String currentVendorId = response.currentVendorId;
        if (!EmptyUtils.isEmpty(currentVendorId)) {
            showGroupOrderPopup(currentVendorId, response);
        }
    }

    private void showGroupOrderPopup(String currentVendorId, SellerGroupStatusBean statusBean) {
        String vendorName = statusBean.vendor_name;
        try {
            vendorName = URLEncoder.encode(statusBean.vendor_name, "UTF-8");
        } catch (Exception ignored) {
        }
        String url = String.format(
                Constants.Url.SELLER_GROUP_ORDER_POPUP,
                statusBean.vendor_id, vendorName, statusBean.status, statusBean.key, currentVendorId
        );
        int dialogHeight = (int) (getResources().getDisplayMetrics().heightPixels * 0.5);
        if (statusBean.status == 3) {
            dialogHeight = CommonTools.dp2px(335);
        } else if (statusBean.status == 2) {
            dialogHeight = CommonTools.dp2px(425);
        } else if (statusBean.status == 1) {
            dialogHeight = CommonTools.dp2px(440);
        }
        showPopupSlideDialog(AppConfig.HOST_WEB + url, dialogHeight);
    }

    private void showPopupSlideDialog(String popupUrl, int dialogHeight) {
        if (popupDialog != null && popupDialog.isShowing()) {
            popupDialog.dismiss();
        }

        PopupSlideDialog dialog = new PopupSlideDialog();
        dialog.loadUrl(popupUrl);
        if (dialogHeight <= 0) {
            dialogHeight = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        dialog.callDialogSize(-1, dialogHeight);
        dialog.show();
        this.popupDialog = dialog;
    }

    private void ensureOnScrollListener() {
        if (this.onScrollListener != null) {
            return;
        }

        RecyclerView.OnScrollListener onScrollListener = new StatefulRecyclerViewOnScrollListener() {

            private final int[] positions = new int[2];
            private int totalScrolled = 0;

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                invalidate();
                extracted(dy);
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                OpActionHelper.notifyScrollStateChanged(newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportEagleImpressionEvent();
                }
                if (oldState == RecyclerView.SCROLL_STATE_IDLE && newState != RecyclerView.SCROLL_STATE_IDLE) {
                    adapter.notifyProviderEditModeChanged();
                }
            }

            private void invalidate() {
                layoutManager.findFirstVisibleItemPositions(positions);
                int i = positions[0];
                calcAtTopVisible(i);
                int panelTitleIndex = getPanelTitleIndex();
                boolean visible = panelTitleIndex > 0 && i >= panelTitleIndex;
                setPanelIndicatorDisplay(visible);
            }

            private void extracted(int dy) {
                boolean unpaidBannerVisible = false;
                if (getParentFragment() instanceof CartSectionPanelFragment) {
                    unpaidBannerVisible = ((CartSectionPanelFragment) getParentFragment()).isUnpaidBannerVisible();
                }
                if (unpaidBannerVisible) {
                    totalScrolled += Math.abs(dy);
                    if (totalScrolled >= thresholdValue) {
                        ReminderBannerManager.get().dismissBanner();
                        totalScrolled = 0;
                    }
                }
            }
        };
        this.onScrollListener = onScrollListener;
        rvCart.addOnScrollListener(onScrollListener);
    }

    private void handleActivityFreeShippingClick(@NonNull SectionCartFreeShippingData item) {
        NewSectionBean.ActivityInfo activityInfo = item.activityInfo;
        if (activityInfo == null) {
            return;
        }
        String linkUrl = activityInfo.url;
        if (EmptyUtils.isEmpty(linkUrl)) {
            return;
        }

        if (activityInfo.diff_amount > 0) { // 未免邮
            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                    .addElement(item.element)
                    .setTargetNm(EagleTrackEvent.TargetNm.VIEW_MORE)
                    .setTargetPos(0)
                    .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .addCtx(item.ctx);
            AppAnalytics.logClickAction(builder.build().getParams());
        } else {
            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                    .addElement(item.element)
                    .setTargetNm(EagleTrackEvent.TargetNm.FREE_DELIVERY_INFO)
                    .setTargetPos(0)
                    .setTargetType(EagleTrackEvent.TargetType.BANNER_LINE)
                    .setClickType(EagleTrackEvent.ClickType.VIEW);
            AppAnalytics.logClickAction(builder.build().getParams());
        }

        if (activityInfo.is_popup) {
            showPopupSlideDialog(linkUrl, CommonTools.dp2px(250));
        } else {
            startActivity(WebViewActivity.getIntent(activity, linkUrl));
        }
    }

    private void handleTopMessageClick(@NonNull CartTopMessageData item) {
        String linkUrl = item.getLink();
        if (EmptyUtils.isEmpty(linkUrl)) {
            return;
        }
        if (item.t != null && item.t.message != null) {
            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                    .setMod_nm(EagleTrackEvent.ModNm.REWARDS_ANNCMNT)
                    .setTargetNm(item.t.message.short_message)
                    .setTargetPos(0)
                    .setTargetType(EagleTrackEvent.TargetType.MESSAGE)
                    .setClickType(EagleTrackEvent.ClickType.VIEW);
            AppAnalytics.logClickAction(builder.build().getParams());
        }
        startActivity(WebViewActivity.getIntent(activity, linkUrl));
    }

    private void handleFeeInfoClick(SectionCartStatsData item, int viewId) {
        String linkUrl;
        String targetName;
        int dialogHeight;
        if (viewId == R.id.iv_delivery_fee) {
            linkUrl = item.shippingFeePopupUrl;
            targetName = EagleTrackEvent.TargetNm.SHIPPING_FEE;
            dialogHeight = CommonTools.dp2px(170);
        } else if (viewId == R.id.iv_service_fee) {
            linkUrl = item.serviceFeePopupUrl;
            targetName = EagleTrackEvent.TargetNm.SERVICE_FEE;
            dialogHeight = 0;
            Context context = getContext();
            if (context != null) {
                dialogHeight = context.getResources().getDimensionPixelSize(R.dimen.prop_size_cart_service_fee_dialog_height);
            }
            if (dialogHeight == 0) {
                dialogHeight = CommonTools.dp2px(308);
            }
        } else {
            linkUrl = null;
            targetName = null;
            dialogHeight = 0;
        }
        if (!EmptyUtils.isEmpty(linkUrl)) {
            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                    .setMod_nm(EagleTrackEvent.ModNm.CART)
                    .setSec_nm(EagleTrackEvent.SecNm.NORMAL)
                    .setTargetNm(targetName)
                    .setTargetPos(-1)
                    .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                    .setClickType(EagleTrackEvent.ClickType.VIEW);
            AppAnalytics.logClickAction(builder.build().getParams());

            showPopupSlideDialog(linkUrl, dialogHeight);
        }
    }

    private void refreshTopBanner() {
        // fixme: use live data instead
        Fragment fragment = getParentFragment();
        if (fragment instanceof CartSectionPanelFragment) {
            ((CartSectionPanelFragment) getParentFragment()).refreshTopBanner();
        }
    }
}
