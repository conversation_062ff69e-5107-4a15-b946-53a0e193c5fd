package com.sayweee.weee.module.product.service;


import android.content.Context;
import android.graphics.drawable.Drawable;

import androidx.collection.ArrayMap;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cate.product.ProductGroupHelper;
import com.sayweee.weee.module.cate.product.adapter.PostMediaData;
import com.sayweee.weee.module.cate.product.bean.AdapterProductGroupData;
import com.sayweee.weee.module.cate.product.bean.GroupProduct;
import com.sayweee.weee.module.cate.product.bean.PromotionListBean;
import com.sayweee.weee.module.cate.product.bean.TitleAnchorBean;
import com.sayweee.weee.module.category.bean.CategoryCarouselData;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.post.bean.PdpVideoListBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.video.provider.data.PostVideoItemData;
import com.sayweee.weee.module.product.bean.BnplBean;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.PdpGiftCardBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.module.product.bean.PdpQuickLinksBean;
import com.sayweee.weee.module.product.data.GiftCardPriceRefreshBean;
import com.sayweee.weee.module.product.data.PdpAiQuestionData;
import com.sayweee.weee.module.product.data.PdpBarInfoData;
import com.sayweee.weee.module.product.data.PdpBlankData;
import com.sayweee.weee.module.product.data.PdpEmptyData;
import com.sayweee.weee.module.product.data.PdpGiftCardBannerData;
import com.sayweee.weee.module.product.data.PdpGiftCardContentData;
import com.sayweee.weee.module.product.data.PdpGiftCardThemeData;
import com.sayweee.weee.module.product.data.PdpGlobalData;
import com.sayweee.weee.module.product.data.PdpItemData;
import com.sayweee.weee.module.product.data.PdpNoProductVeilData;
import com.sayweee.weee.module.product.data.PdpPostData;
import com.sayweee.weee.module.product.data.PdpProductBannerData;
import com.sayweee.weee.module.product.data.PdpProductDetailData;
import com.sayweee.weee.module.product.data.PdpProductDetailItemData;
import com.sayweee.weee.module.product.data.PdpProductGroupData;
import com.sayweee.weee.module.product.data.PdpProductTitleData;
import com.sayweee.weee.module.product.data.PdpProductsData;
import com.sayweee.weee.module.product.data.PdpQuickLinksData;
import com.sayweee.weee.module.product.data.PdpReviewData;
import com.sayweee.weee.module.product.data.PdpSummaryData;
import com.sayweee.weee.module.product.data.PdpTagData;
import com.sayweee.weee.module.product.provider.summary.PdpSummaryInfoData;
import com.sayweee.weee.module.product.provider.summary.PdpSummaryMoreData;
import com.sayweee.weee.module.product.provider.summary.PdpSummaryReviewData;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

//
// Created by Thomsen on 24/03/2025.
//
public class PdpResponseParser {

    class ProductParseResult {
        List<AdapterDataType> list;
        int modPos;
        boolean isGetAlcoholTips;
    }

    public ProductParseResult parseTransferData(ProductBean product, String traceId, int modPos) {
        List<AdapterDataType> list = new ArrayList<>();
        if (product != null) {
            list.add(new PdpProductBannerData(PdpItemType.PDP_BANNER, product, null,
                    false, traceId, modPos, null, null, false));
            list.add(new PdpEmptyData());
        } else {
            list.add(new PdpNoProductVeilData());
        }
        ProductParseResult result = new ProductParseResult();
        result.list = list;
        return result;
    }

    protected ProductParseResult parseProductData(ProductBean product, Drawable preloadDrawable,
                                                  ArrayMap<Integer, BnplBean> bnplMapData, PdpCombineData combineData) {
        PromotionListBean promotion = null;
        PostCategoryBean review = null;
        PdpVideoListBean post = null;
        PdpModulesBean modules = null;
        CmsContentFeedListBean contentFeedListBean = null;
        String referralDesc = null;
        String traceId = null;
        String cartSource = null;
        boolean changeGroupFilter = false;
        if (combineData != null) {
            promotion = combineData.promotion;
            review = combineData.review;
            post = combineData.post;
            modules = combineData.modules;
            contentFeedListBean = combineData.contentFeedListBean;
            if (combineData.canShowSharePopup()) {
                referralDesc = combineData.referralDesc;
            }
            traceId = combineData.traceId;
            cartSource = combineData.cartSource;
            changeGroupFilter = combineData.changeGroupFilter;
        }
        int modPos = 0;
        Context context = LifecycleProvider.get().getTopActivity();
        List<AdapterDataType> list = new ArrayList<>();
        PdpProductBannerData productBannerData = new PdpProductBannerData(PdpItemType.PDP_BANNER, product, referralDesc, true, traceId, modPos, combineData != null ? combineData.detail : null, cartSource, combineData != null && combineData.disableOtherPage);//最新接口返回数据
        ProductDetailBean.ProductFeatureBean productFeatureBean = null;
        if (product instanceof ProductDetailBean.ProductFeatureBean) {
            productFeatureBean = (ProductDetailBean.ProductFeatureBean) product;
        }
        String attachIds = "0";
        List<AdapterProductGroupData> groupData = new ArrayList<>();
        if (productFeatureBean != null && productFeatureBean.hasGroup()) {
            for (GroupProduct groupProduct : productFeatureBean.group.groupProductList) {
                if (product.id == groupProduct.product_id) {
                    attachIds = groupProduct.property_value_ids;
                    break;
                }
            }
            groupData = ProductGroupHelper.convert(productFeatureBean.group.propertyList, productFeatureBean.group.groupProductList, attachIds);
            productBannerData.setGroupFilter(ProductGroupHelper.groupFilterName(groupData));
            productBannerData.setChangeGroupFilter(changeGroupFilter);
        }
        productBannerData.preloadDrawable = preloadDrawable;
        list.add(productBannerData);
        if (!EmptyUtils.isEmpty(product.barInfoModules)) {
            if (promotion != null && !EmptyUtils.isEmpty(promotion.barInfoModules)) {
                product.barInfoModules.addAll(promotion.barInfoModules);
            }

        } else {
            if (promotion != null && !EmptyUtils.isEmpty(promotion.barInfoModules)) {
                product.barInfoModules = new ArrayList<>();
                product.barInfoModules.addAll(promotion.barInfoModules);
            }
        }
        Collections.sort(product.barInfoModules, new Comparator<ProductBean.BarInfoModules>() {
            @Override
            public int compare(ProductBean.BarInfoModules a, ProductBean.BarInfoModules b) {
                // 升序：a 的 sort 在前，b 的 sort 在后
                return Integer.compare(a.sort, b.sort);
            }
        });
        for (ProductBean.BarInfoModules barInfoModules : product.barInfoModules) {
            list.add(new PdpBarInfoData(barInfoModules));
        }

        if (!EmptyUtils.isEmpty(product.label_list)) {
            list.add(new PdpTagData(product).setTraceId(traceId).setModPos(modPos));
        }

        boolean hasGroup = false;
        // product group
        if (productFeatureBean != null && productFeatureBean.hasGroup()) {

            hasGroup = true;
            modPos++;
            if (productFeatureBean.isGroupV1()) {
                list.add(PdpBlankData.defaultBlank());
                list.add(new PdpProductGroupData(PdpItemType.PDP_PRODUCT_PRODUCT_GROUPING, (ProductDetailBean.ProductFeatureBean) product, modPos, traceId));
            } else {
                int i = -1;
                for (AdapterProductGroupData group : groupData) {
                    i++;
                    if (i == 0) {
                        list.add(PdpBlankData.defaultBlank().height(13));
                        list.add(PdpBlankData.defaultLine().marginHorizontal(20));
                    }
                    list.add(PdpBlankData.defaultBlank());

                    list.add(new PdpProductGroupData(PdpItemType.PDP_NEW_PRODUCT_PRODUCT_GROUPING, (ProductDetailBean.ProductFeatureBean) product, modPos, traceId).setGroupData(group));
                }
            }
        }

        boolean hasMkpl = false;
        // mkpl vendor
        if (productFeatureBean != null) {
            ProductBean.VenderInfo venderInfoView = productFeatureBean.vender_info_view;
            if (productFeatureBean.isSeller() && venderInfoView != null) {
                hasMkpl = true;
                modPos++;
                list.add(PdpBlankData.defaultBlank().height(10));
                list.add(PdpBlankData.defaultLine().marginHorizontal(20));
                list.add(PdpBlankData.defaultBlank());
                list.add(new PdpGlobalData(PdpItemType.PDP_PRODUCT_GLOBAL, productFeatureBean, modPos, traceId));
            }
        }

        // FBW
        if (productFeatureBean != null) {
            if (!EmptyUtils.isEmpty(productFeatureBean.fullfillment_info)) {
                hasMkpl = true;
                modPos++;
                list.add(PdpBlankData.create().height(20));
                list.add(new PdpItemData().fromFulfillment(productFeatureBean, product.id, modPos, traceId));
            }
        }
        boolean hasSummary = false;
        // pdp summary
        if (combineData != null && combineData.summaryBean != null
                && combineData.summaryBean.isValid()) {
            modPos++;
            list.add(PdpBlankData.defaultBlank());
            list.add(PdpBlankData.defaultLine().marginHorizontal(20));
            list.add(PdpBlankData.defaultBlank());
            PdpSummaryData summaryData = new PdpSummaryData(combineData.summaryBean);
            summaryData.productId = product.id;
            summaryData.traceId = traceId;
            summaryData.cartSource = cartSource;
            summaryData.detail = product;
            summaryData.modPos = modPos;
            summaryData.dataList = buildSummaryData(summaryData);
            list.add(summaryData);
            hasSummary = combineData.summaryBean.hasDesc();
        }

        if (!hasSummary && hasGroup && !hasMkpl) {
            list.add(PdpBlankData.create().height(12));
            list.add(PdpBlankData.defaultLine().marginHorizontal(20));
        }
        // 判断quick links后面是否紧跟着product detail模块
        boolean hasReview = !EmptyUtils.isEmpty(review) && (!EmptyUtils.isEmpty(review.list) || (!EmptyUtils.isEmpty(review.tip) && review.show_entrance));
        boolean hasAi = !EmptyUtils.isEmpty(combineData) && !EmptyUtils.isEmpty(combineData.aiQuestionBean);
        boolean hasPost = !EmptyUtils.isEmpty(post);
        boolean skipQuickLinks = false;
        if (productFeatureBean != null &&
                (!EmptyUtils.isEmpty(productFeatureBean.product_properties) ||
                        !EmptyUtils.isEmpty(productFeatureBean.description_html))) {

            // 检查quick links和product detail之间是否有其他内容模块
            boolean hasModule = false;
            if (modules != null && modules.isValid()) {
                for (PdpProductsBean module : modules.modules) {
                    if (!EmptyUtils.isEmpty(module.product_list)) {
                        hasModule = true;
                        break;
                    }
                }
            }
            boolean hasIntermediateModules =
                    (hasModule) ||  // carousel模块
                            hasPost ||             // post模块
                            hasAi || // AI模块
                            (hasReview && !hasSummary);              // review模块

            // 如果没有中间模块，则跳过quick links
            skipQuickLinks = !hasIntermediateModules;
        }
        if (!skipQuickLinks) {
            // quick links
            modPos++;
            List<PdpQuickLinksBean> quickLinks = new ArrayList<>();
            quickLinks.add(new PdpQuickLinksBean(context.getString(R.string.s_title_details), R.mipmap.pic_product_detail_icon, 0, TitleAnchorBean.DETAIL, "product_details"));

            if (hasAi) {
                quickLinks.add(new PdpQuickLinksBean(context.getString(R.string.s_question), 0, combineData.aiQuestionBean.size(), TitleAnchorBean.AI, "questions"));
            }
            if (hasReview && !hasSummary) {
                quickLinks.add(new PdpQuickLinksBean(context.getString(R.string.reviews), R.mipmap.pic_review_icon, review.total, TitleAnchorBean.REVIEW, "reviews"));
            }

            list.add(PdpBlankData.create().height(12));
            list.add(new PdpQuickLinksData(quickLinks).setProductId(product.id).setTraceId(traceId).setModPos(modPos));
        }
        list.add(PdpBlankData.create().height(8));

        ProductParseResult result = new ProductParseResult();

        // carousel modules
        if (modules != null && modules.isValid()) {
            boolean hasVendorId = !EmptyUtils.isEmpty(productFeatureBean) && !EmptyUtils.isEmpty(
                    productFeatureBean.vender_info_view) && productFeatureBean.vender_info_view.vender_id > 0;
            List<? extends AdapterDataType> pdpModulesData = modules.toAdapterData();
            for (AdapterDataType datum : pdpModulesData) {
                if (datum instanceof PdpProductsData) {
                    if (hasVendorId && ((PdpProductsData) datum).isSeller()) {
                        ((PdpProductsData) datum).setVendorId(productFeatureBean.vender_info_view.vender_id);
                    }
                    modPos = modPos + 1;//加购横向组件部分
                    ((PdpProductsData) datum).modPos = modPos;
                    ((PdpProductsData) datum).traceId = traceId;
                }
            }
            list.add(PdpBlankData.defaultLine().height(6));
            list.addAll(pdpModulesData);//一鍵加购和product line模块
        }

        // post
        if (hasPost) {
            modPos = modPos + 1;//video部分
            if (!EmptyUtils.isEmpty(post.list)) {
                list.add(PdpBlankData.defaultLine().height(6));
            } else {
                list.add(PdpBlankData.defaultLine().marginHorizontal(20));
            }
            list.add(new PdpPostData(PdpItemType.PDP_PRODUCT_POST, post, product.id, traceId, product, modPos));
            list.add(PdpBlankData.defaultLine().marginHorizontal(20));
        }

        if (hasAi) {
            modPos = modPos + 1;//ai部分
            list.add(PdpBlankData.defaultBlank());
            list.add(new PdpAiQuestionData(PdpItemType.PDP_AI_QUESTION, combineData.aiQuestionBean, product.id, modPos));
            list.add(PdpBlankData.defaultBlank().height(8));
            list.add(PdpBlankData.defaultBlank().height(6).bgColor(R.color.color_surface_200_bg));
        }

        // review
        if (hasReview) {
            modPos = modPos + 1;//review部分
            if (!EmptyUtils.isEmpty(review.list)) {
                review.list = review.list.subList(0, Math.min(review.list.size(), 3));
            }
            list.add(new PdpReviewData(PdpItemType.PDP_PRODUCT_REVIEW, review, product.id, traceId, modPos).setDetailData(product).setCartSource(cartSource));
            list.add(PdpBlankData.defaultBlank().height(8));
        }

        if (!EmptyUtils.isEmpty(combineData.middleBarInfoBean)) {
            modPos = modPos + 1;
            PdpMiddleBannerBean middleBarInfoBean = combineData.middleBarInfoBean;
            list.add(PdpBlankData.defaultBlank().height(6).bgColor(R.color.color_surface_200_bg));
            list.add(PdpBlankData.defaultBlank().height(20));
            CategoryCarouselData categoryCarouselData = new CategoryCarouselData(middleBarInfoBean.banner_info.carousel);
            categoryCarouselData.setBannerParams(middleBarInfoBean.banner_info.autoplay, middleBarInfoBean.banner_info.loop, middleBarInfoBean.banner_info.loop_interval);
            categoryCarouselData.setId(product.id, traceId);
            categoryCarouselData.setModInfo(EagleTrackEvent.ModNm.MIDDLE_CAROUSEL_BANNER, modPos, "carousel", -1);
            list.add(categoryCarouselData);
            if (middleBarInfoBean.banner_info.carousel.size() == 1) {
                list.add(PdpBlankData.defaultBlank().height(5));
            }
            list.add(PdpBlankData.defaultBlank().height(6).bgColor(R.color.color_surface_200_bg));
        } else if (hasReview) {
            list.add(PdpBlankData.defaultLine().marginHorizontal(20));
        }

        // product detail
        if (productFeatureBean != null) {
            if (!EmptyUtils.isEmpty(productFeatureBean.product_properties) || !EmptyUtils.isEmpty(productFeatureBean.description_html)) {
                modPos = modPos + 1;//商品细节部分
                if (productFeatureBean.product_properties != null) {
                    for (ProductDetailBean.ProductFeatureBean.ProductPropertiesBean property : productFeatureBean.product_properties) {
                        property.itemPropertyValue = PdpProductDetailItemData.splitHtml(property.property_value);
                    }
                }

                list.add(new PdpProductDetailData(PdpItemType.PDP_PRODUCT_DETAIL, productFeatureBean)
                        .setProductId(productFeatureBean.id)
                        .setHasTitle(true)
                        .setTraceId(traceId));
            }

        }

        result.list = list;
        result.modPos = modPos;
        return result;

    }

    private List<AdapterDataType> buildSummaryData(PdpSummaryData item) {
        List<AdapterDataType> list = new ArrayList<>();

        int imageSize = 8;

        int infoIndex = 0;
        if (item.t.summary != null) {
            list.add(PdpBlankData.create().width(20).bgColor(R.color.white));
            PdpSummaryInfoData infoData = new PdpSummaryInfoData(item.t.summary);
            infoData.impressionBean = getSummaryImpressionBean(item.modPos, "summary",
                    null, String.valueOf(item.detail.id), 0);
            list.add(infoData);
            infoIndex = 1;
        }
        int videoCount = 0;
        int reviewCount = 0;
        for (PostCategoryBean.ListBean content : item.t.contents) {
            int index = item.t.contents.indexOf(content) + infoIndex;
            if ("video".equals(content.type)) {
                if (videoCount == 0) {
                    list.add(PdpBlankData.create().width(16));
                } else {
                    list.add(PdpBlankData.create().width(imageSize));
                }
                videoCount++;
                PostMediaData md = new PostMediaData();
                md.t = content;
                md.itemPosition = item.t.contents.indexOf(content);
                PostVideoItemData itemData = new PostVideoItemData(md);
                itemData.startGone = true;
                itemData.impressionBean = getSummaryImpressionBean(item.modPos,
                        content.type, content.ref_url, String.valueOf(content.id), index);
                list.add(itemData);
            }
            if ("review".equals(content.type)) {
                list.add(PdpBlankData.create().width(imageSize));
                PdpSummaryReviewData reviewData = new PdpSummaryReviewData(content);
                reviewData.impressionBean = getSummaryImpressionBean(item.modPos,
                        content.type, content.ref_url, String.valueOf(content.id), index);
                list.add(reviewData);
                reviewCount++;
            }
        }
        if (reviewCount > 0) {
            list.add(PdpBlankData.create().width(16));
            list.add(new PdpSummaryMoreData(item.t));
        }
        list.add(PdpBlankData.create().width(20));

        return list;
    }

    private static ImpressionBean getSummaryImpressionBean(
            int modPos, String bannerType,
            String nextPageUrl, String targetId, int bannerPos) {

        String modName = "product_summary";
        String key = modName + "_" + targetId;

        Map<String, Object> ctx = EagleTrackManger.get().getCtx(new EagleContext()
                .setPageTarget(targetId));
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(modName)
                .setMod_pos(modPos)
                .setBannerId("-1")
                .setBanner_key(null)
                .setBanner_pos(bannerPos)
                .setBanner_type(bannerType)
                .setUrl(nextPageUrl)
                .addCtx(ctx)
                .build().getParams();
        return new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, key);
    }


    public ProductParseResult parseGiftCardData(ProductDetailBean detail, String traceId,
                                                GiftCardPriceRefreshBean refreshBean, boolean isFirst) {
        ProductDetailBean.ProductFeatureBean productFeatureBean = detail.product;
        PdpGiftCardBean giftCardInfo = detail.gift_card_info;
        if (giftCardInfo == null) return null;

        int modPos = 0;
        List<AdapterDataType> list = new ArrayList<>();

        String themeCoverImg;
        try {
            themeCoverImg = giftCardInfo.infos.get(0).categories.get(0).themes.get(0).cover_img;
        } catch (Exception e) {
            themeCoverImg = detail.product.img;
        }
        PdpGiftCardBannerData giftCardBannerData = new PdpGiftCardBannerData(themeCoverImg);
        list.add(giftCardBannerData);

        list.add(PdpBlankData.create().height(4));
        list.add(new PdpProductTitleData(PdpItemType.PDP_PRODUCT_TITLE, detail.product.name));
        list.add(PdpBlankData.create().height(4));

        // gift card theme
        PdpGiftCardThemeData giftCardThemeData = new PdpGiftCardThemeData(PdpItemType.PDP_GIFT_CARD_THEME, giftCardInfo).setCategoryName(refreshBean.categoryName, refreshBean.languageName, refreshBean.picIndex);
        if (giftCardThemeData.isValid()) {
            list.add(giftCardThemeData);
        }

        // product group
        if (productFeatureBean != null && productFeatureBean.hasGroup()) {
            list.add(new PdpProductGroupData(PdpItemType.PDP_PRODUCT_GIFT_CARD_GROUPING, productFeatureBean, modPos, traceId).setRefresh());
        }
        list.add(PdpBlankData.create().height(20));

        list.add(new PdpGiftCardContentData(giftCardInfo, isFirst, refreshBean.senderName)
                .setQuantity(detail.product.min_order_quantity, giftCardInfo.quantity_limit)
                .setRefreshData(refreshBean));
        list.add(PdpBlankData.create().height(20));
        list.add(new PdpProductDetailData(PdpItemType.PDP_PRODUCT_DETAIL, productFeatureBean)
                .setProductId(detail.product.id)
                .setHasTitle(false)
                .setTraceId(traceId));
        list.add(PdpBlankData.create().height(60));

        ProductParseResult result = new ProductParseResult();
        result.list = list;
        return result;
    }
}
