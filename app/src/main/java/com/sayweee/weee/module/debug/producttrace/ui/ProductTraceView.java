package com.sayweee.weee.module.debug.producttrace.ui;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceData;
import com.sayweee.weee.utils.ViewTools;

public class ProductTraceView extends FrameLayout {

    private TextView textView;

    public ProductTraceView(@NonNull Context context) {
        this(context, null, 0);
    }

    public ProductTraceView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductTraceView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    protected void init(Context context) {
        setBackgroundResource(R.color.color_product_trace_view_background); // 80% alpha white

        // Content
        TextView tv = new TextView(context);
        FrameLayout.LayoutParams tvLp = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        tv.setLayoutParams(tvLp);
        ViewTools.applyTextStyleAndColor(tv, R.style.style_body_3xs, R.color.color_product_trace_content);
        tv.setEllipsize(TextUtils.TruncateAt.END);
        addView(tv);
        this.textView = tv;
    }

    public void bindData(String domain, ProductTraceData data) {
        CharSequence cs = data.getData(domain);
        cs = cs != null ? cs : ProductTraceViewHelper.formatUndefinedDomain(getContext(), domain, 0);
        textView.setText(cs);
    }

    public void setMaxHeight(int maxHeight) {
        if (maxHeight > 0 && textView.getMaxHeight() != maxHeight) {
            textView.setMaxHeight(maxHeight);
        }
    }

}
