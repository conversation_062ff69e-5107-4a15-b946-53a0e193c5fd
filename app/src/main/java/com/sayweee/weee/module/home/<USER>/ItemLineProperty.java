package com.sayweee.weee.module.home.bean;

import androidx.annotation.Nullable;

import com.sayweee.weee.module.cms.bean.CmsBackgroundStyle;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.utils.JsonUtils;

import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/6/9.
 * Desc:
 */
public class ItemLineProperty extends CmsProperty {

    protected transient CmsBackgroundStyle backgroundStyle;

    public ItemLineProperty() {
        super();
    }

    public ItemLineProperty(@Nullable CmsPageParam pageParam) {
        super(pageParam);
    }

    @Override
    public ItemLineProperty parseProperty(Map<String, String> map) {
        super.parseProperty(map);
        if (background != null && !background.isEmpty()) {
            backgroundStyle = JsonUtils.parseObject(background, CmsBackgroundStyle.class);
        }
        return this;
    }

    @Nullable
    public CmsBackgroundStyle getBackgroundStyle() {
        return backgroundStyle;
    }
}
