package com.sayweee.weee.module.launch.service;

import androidx.collection.ArrayMap;

import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.module.account.bean.AccountBean;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.RetrofitIml;

import java.io.Serializable;

import javax.annotation.Nullable;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

public class AccountHelper {

    public static Observable<ResponseBean<AccountBean>> obtainAccountData(Consumer<? super Disposable> consumer) {
        String json = MMKVManager.obtain(MMKVManager.ID_LAUNCH).decodeString("account_info", "");
        AccountBean bean = JsonUtils.parseObject(json, AccountBean.class);
        if (bean != null) {
            SharedViewModel.get().refreshAccountInfo();

            ResponseBean<AccountBean> responseBean = new ResponseBean<>();
            responseBean.result = true;
            responseBean.object = bean;
            return Observable.just(responseBean);
        }
        return RetrofitIml.get().getHttpService(OrderApi.class)
                .getAccountInfo()
                .compose(DisposableTransformer.scheduler(consumer, true));
    }

    public static void ensurePreorderData() {
        if (OrderManager.get().isLackOfOrder()) {
            SimplePreOrderBean bean = readPreOrderByCache();
            if (bean != null) {
                OrderProvider.get().setSimpleOrderData(bean);
            }
        }
    }

    @Nullable
    public static SimplePreOrderBean readPreOrderByCache() {
        String s = MMKVManager.obtain(MMKVManager.ID_LAUNCH).decodeString("preorder", "");
        SimplePreOrderBean bean = null;
        if (s != null && !s.isEmpty()) {
            bean = JsonUtils.parseObject(s, SimplePreOrderBean.class);
        }
        return bean;
    }

    public static Observable<ResponseBean<SimplePreOrderBean>> obtainPreorderData(Consumer<? super Disposable> consumer) {
        SimplePreOrderBean bean = readPreOrderByCache();
        if (bean != null && bean.delivery_date_expire_dtm > System.currentTimeMillis()) {
            getSimplePreOrderAsync(consumer);

            bean.delivery_date_expire_dtm = 0;
            ResponseBean<SimplePreOrderBean> responseBean = new ResponseBean<>();
            responseBean.result = true;
            responseBean.object = bean;
            return Observable.just(responseBean);
        }
        return RetrofitIml.get().getHttpService(OrderApi.class)
                .getSimplePreOrder()
                .compose(DisposableTransformer.scheduler(consumer, true));
    }

    private static void getSimplePreOrderAsync(Consumer<? super Disposable> consumer) {
        RetrofitIml.get().getHttpService(OrderApi.class)
                .getSimplePreOrder()
                .compose(DisposableTransformer.scheduler(consumer, true))
                .subscribe(new ResponseObserver<ResponseBean<SimplePreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<SimplePreOrderBean> response) {
                        OrderManager.get().setSimpleOrderData(response.getData());
                    }
                });
    }

    public static void saveAccountInfo(AccountBean account) {
        MMKVManager.obtain(MMKVManager.ID_LAUNCH).encode("account_info", JsonUtils.toJSONString(account));
    }

    public static void savePreorderData(SimplePreOrderBean bean) {
        if (bean != null) {
            ArrayMap<String, Serializable> params = new ArrayMap<>();
            params.put("id", bean.id);
            params.put("token", bean.token);
            params.put("user_id", bean.user_id);
            params.put("zipcode", bean.zipcode);
            params.put("delivery_pickup_date", bean.delivery_pickup_date);
            params.put("status", bean.status);
            params.put("address", bean.address);
            params.put("addr_address", bean.addr_address);
            params.put("addr_apt", bean.addr_apt);
            params.put("addr_country", bean.addr_country);
            params.put("addr_state", bean.addr_state);
            params.put("addr_city", bean.addr_city);
            params.put("addr_zipcode", bean.addr_zipcode);
            params.put("addr_firstname", bean.addr_firstname);
            params.put("addr_lastname", bean.addr_lastname);
            params.put("quantity", bean.quantity);
            params.put("email", bean.email);
            params.put("phone", bean.phone);
            params.put("comment", bean.comment);
            params.put("coupon_code", bean.coupon_code);
            params.put("delivery_mode", bean.delivery_mode);
            params.put("shipping_free_fee", bean.shipping_free_fee);
            params.put("pantry_free_fee", bean.pantry_free_fee);
            params.put("shipping_fee", bean.shipping_fee);
            params.put("pantry_shipping_fee", bean.pantry_shipping_fee);
            params.put("deal_id", bean.deal_id);
            params.put("sales_org_id", bean.sales_org_id);
            params.put("is_shipping_order", bean.is_shipping_order); //1 为直邮 0 为本地配送
            params.put("is_support_hotdish", bean.is_support_hotdish); //是否支持餐馆菜
            params.put("is_record_alcohol", bean.is_record_alcohol);
            params.put("is_mof", bean.is_mof);
            params.put("is_support_change_date", bean.is_support_change_date); //首页标题栏能否切换日期
            params.put("is_show_tab_global_plus", bean.is_show_tab_global_plus);
            params.put("eta_date", bean.eta_date);
            params.put("eta_date_desc", bean.eta_date_desc);//格式化后的日期
            params.put("delivery_date_expire_dtm", bean.delivery_date_expire_dtm > 0 ? DateUtils.convertServerTime(bean.delivery_date_expire_dtm) - DateUtils.convertServerTime(bean.server_dtm) + System.currentTimeMillis() : 0);
            MMKVManager.obtain(MMKVManager.ID_LAUNCH).encode("preorder", JsonUtils.toJSONString(params));
        }
    }

}
