package com.sayweee.weee.module.cart.bean;

import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.List;

public class NewPreOrderBean implements Serializable {

    public SaveForLaterResponseBean save_for_later_response;
    public List<NewItemBean> invalid_to_valid_items;
    public List<NewItemBean> valid_to_invalid_items;
    public boolean is_record_alcohol;
    public String final_amount;
    public String base_final_amount;
    public CouponReminder coupon_reminder;
    public boolean coupon_reminder_experiment_result;
    public boolean can_checkout;
    public boolean contain_alcohol;
    public int selected_cart_quantity;
    public int selected_item_quantity;
    public boolean display_save_for_later;
    public List<?> invalid_items;
    public List<NewSectionBean> sections;
    @Deprecated
    public String cart_domain;
    public List<NewSectionBean.ActivityInfo> top_promotions;
    public boolean is_refresh_porder;
    public boolean is_record_seller_alcohol;

    public boolean isShowAlcoholAgreement() {
        return isShowAlcoholAgreement(/* isSeller= */false);
    }

    public boolean isShowAlcoholAgreement(boolean isSeller) {
        if (isSeller) {
            return !is_record_seller_alcohol;
        } else {
            return contain_alcohol && !is_record_alcohol;
        }
    }

    public boolean isShowCouponReminder() {
        return coupon_reminder != null && !EmptyUtils.isEmpty(coupon_reminder.tag_text);
    }

    public boolean isContainMKpl() {
        if (!EmptyUtils.isEmpty(sections)) {
            for (NewSectionBean section : sections) {
                if (section.isSeller()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean hasTopPromotions() {
        return CollectionUtils.isNotEmpty(top_promotions);
    }
}
