package com.sayweee.weee.module.cart.service;

import com.sayweee.service.payment.bean.CardAttachBean;
import com.sayweee.weee.module.account.bean.AccountBean;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.AddOnDetailBean;
import com.sayweee.weee.module.cart.bean.BoughtListBean;
import com.sayweee.weee.module.cart.bean.CollectBean;
import com.sayweee.weee.module.cart.bean.GroupBuySellerDetailBean;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.bean.ProductListBean;
import com.sayweee.weee.module.cart.bean.ReminderBean;
import com.sayweee.weee.module.cart.bean.SaveForLaterResponseBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cart.bean.TopInfoBean;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cate.bean.CateBean;
import com.sayweee.weee.module.cate.bean.CateWindowBean;
import com.sayweee.weee.module.cate.bean.LoyaltyIntroductionBean;
import com.sayweee.weee.module.cate.bean.VendorIntroductionBean;
import com.sayweee.weee.module.cate.product.ReferralTitleBean;
import com.sayweee.weee.module.cate.product.bean.AffiliateListBean;
import com.sayweee.weee.module.cate.product.bean.PromotionListBean;
import com.sayweee.weee.module.cate.product.bean.RelatedBean;
import com.sayweee.weee.module.category.bean.CategoryBannerV2Bean;
import com.sayweee.weee.module.category.bean.CategoryPagerBean;
import com.sayweee.weee.module.checkout.bean.AliPayBean;
import com.sayweee.weee.module.checkout.bean.CardListBean;
import com.sayweee.weee.module.checkout.bean.CheckoutBean;
import com.sayweee.weee.module.checkout.bean.CitconPayBean;
import com.sayweee.weee.module.checkout.bean.CitconTokenBean;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.PayStatusBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout.bean.UpsellMoreBean;
import com.sayweee.weee.module.checkout2.bean.CheckoutV4Bean;
import com.sayweee.weee.module.checkout2.bean.DealPayV2Bean;
import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.iml.coupon.data.CmsCouponBean;
import com.sayweee.weee.module.cms.iml.product.data.ProductWaterfallBean;
import com.sayweee.weee.module.cms.iml.seller.data.SellerListBean;
import com.sayweee.weee.module.home.bean.BesideTipsBean;
import com.sayweee.weee.module.home.bean.KeywordsBean;
import com.sayweee.weee.module.home.bean.LightningDealsBean;
import com.sayweee.weee.module.home.bean.NewTopMessageBean;
import com.sayweee.weee.module.home.bean.ReviewBean;
import com.sayweee.weee.module.home.bean.RfmBean;
import com.sayweee.weee.module.home.theme.bean.ThemeBean;
import com.sayweee.weee.module.home.theme.bean.ThemeVersionBean;
import com.sayweee.weee.module.launch.bean.ConfigListBean;
import com.sayweee.weee.module.launch.bean.ReferrerDescBean;
import com.sayweee.weee.module.launch.bean.SplashScreenBean;
import com.sayweee.weee.module.message.bean.ActivityCenterData;
import com.sayweee.weee.module.message.bean.MessageCenterBean;
import com.sayweee.weee.module.message.bean.MessagePortalBean;
import com.sayweee.weee.module.message.bean.NotificationCenterBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListBean;
import com.sayweee.weee.module.mkpl.bean.GlobalTabBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.navigate.bean.NavigateLabelBean;
import com.sayweee.weee.module.order.bean.OrderBoughtBean;
import com.sayweee.weee.module.order.bean.OrderCancelBean;
import com.sayweee.weee.module.order.bean.OrderCancelReasonBean;
import com.sayweee.weee.module.order.bean.OrderCancelRefundBean;
import com.sayweee.weee.module.order.bean.OrderListBean;
import com.sayweee.weee.module.order.bean.OrderTrackInfoBean;
import com.sayweee.weee.module.order.bean.ReorderBean;
import com.sayweee.weee.module.popup.bean.PopupCenterBean;
import com.sayweee.weee.module.post.bean.AtBean;
import com.sayweee.weee.module.post.bean.PdpVideoListBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.bean.PostGoldBean;
import com.sayweee.weee.module.post.bean.ProductNewBean;
import com.sayweee.weee.module.post.bean.ToReviewInfoBean;
import com.sayweee.weee.module.post.edit.bean.LanguageBean;
import com.sayweee.weee.module.post.edit.bean.NotifyBean;
import com.sayweee.weee.module.post.edit.bean.SuggestTranslationsBean;
import com.sayweee.weee.module.post.edit.service.bean.PostCommitBean;
import com.sayweee.weee.module.presale.bean.SaleEventBean;
import com.sayweee.weee.module.product.bean.AffiliateListNewBean;
import com.sayweee.weee.module.product.bean.BnplBean;
import com.sayweee.weee.module.product.bean.InteractionMessageBean;
import com.sayweee.weee.module.product.bean.PdpContactButtonTextBean;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.PdpSummaryBean;
import com.sayweee.weee.module.search.bean.AiShoppingResponseBean;
import com.sayweee.weee.module.search.bean.UploadResponseBean;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.SellerFeedBackListBean;
import com.sayweee.weee.module.seller.bean.SellerGroupDetailBean;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.module.seller.bean.SellerInfoBean;
import com.sayweee.weee.module.seller.bean.SellerSummaryBean;
import com.sayweee.weee.module.seller.bean.SellerTopBean;
import com.sayweee.weee.module.seller.bean.UnReadBean;
import com.sayweee.weee.module.web.bean.UploadImageBean;
import com.sayweee.weee.module.web.bean.UploadJsonBean;
import com.sayweee.weee.service.location.bean.GeoBean;
import com.sayweee.weee.service.timer.bean.TimerBannerBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.HTTP;
import retrofit2.http.Header;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/11/18.
 * Desc:
 */
public interface OrderApi {

    //account 简要信息（Me）
    @GET("/ec/customer/account/info")
    Observable<ResponseBean<AccountBean>> getAccountInfo();

    @GET("/ec/content/navigate/label/list")
    Observable<ResponseBean<NavigateLabelBean>> getNavigateLabel(@Query("zipcode") String key);

    @POST("/ec/content/navigate/label/click")
    Observable<SimpleResponseBean> onNavigateClick(@Body Map<String, Serializable> body);

    @POST("/ec/so/porder/record/alcohol")
    Observable<SimpleResponseBean> recordAlcohol(@Body Map<String, Serializable> body);

    //修改支付方式
    @PUT("ec/so/porder/payment_category")
    Observable<SimpleResponseBean> changePayment(@Body Map<String, Serializable> body);

    //准备结算v2
    @POST
    Observable<ResponseBean<PreCheckoutV2Bean>> preCheckoutV2(@Url String url, @Body Map<String, Serializable> body);

    //结算
    @POST("/ec/so/order/checkout")
    Observable<ResponseBean<CheckoutBean>> checkout(@Body Map<String, Serializable> body);

    //结算V3
    @POST("/ec/so/order/checkout/v3")
    Observable<ResponseBean<CheckoutBean>> checkoutV3(@Body Map<String, Serializable> body);

    //结算V4
    @POST("/ec/so/order/checkout/v4")
    Observable<ResponseBean<CheckoutV4Bean>> checkoutV4(@Body Map<String, Serializable> body);

    //DealPayV2
    @POST("/ec/so/order/query/deal/pay/v2")
    Observable<ResponseBean<DealPayV2Bean>> dealPayV2(@Body Map<String, Serializable> body);

    //DealPayV2 checkout
    @POST("/ec/so/payment/enroll/pay_payments")
    Observable<ResponseBean<CheckoutV4Bean>> enrollPayPayments(@Body Map<String, Serializable> body);

    //stripe alipay校验
    @POST("/ec/payment/alipay/stripe/enroll")
    Observable<ResponseBean<AliPayBean>> stripeAlipay(@Body Map<String, Serializable> body);

    //stripe alipay校验
    @POST("/ec/payment/alipay/stripe/v2/enroll")
    Observable<ResponseBean<AliPayBean>> stripeAlipayV2(@Body Map<String, Serializable> body);

    //citcon alipay校验
    @GET("/ec/payment/alipay/citcon/v2/environment")
    Observable<ResponseBean<CitconTokenBean>> getCitconToken();

    //citcon alipay校验
    @POST("/ec/payment/alipay/citcon/v2/prepare")
    Observable<ResponseBean<CitconPayBean>> getAlipayDataByCitcon(@Body Map<String, Serializable> body);

    //citcon alipay校验
    @POST("/ec/payment/alipay/citcon/prepare")
    Observable<ResponseBean<CitconPayBean>> getAlipayDataByCitconV1(@Body Map<String, Serializable> body);

    //citcon wechat pay校验
    @POST("/ec/payment/wechat/citcon/prepare")
    Observable<ResponseBean<CitconPayBean>> getWechatPayDataByCitconV1(@Body Map<String, Serializable> body);

    //citcon wechat pay校验
    @POST("/ec/payment/wechat/citcon/v2/prepare")
    Observable<ResponseBean<CitconPayBean>> getWechatPayDataByCitcon(@Body Map<String, Serializable> body);

    //citcon cash app pay 获取环境
    @GET("/ec/payment/cashApp/citcon/v2/environment")
    Observable<ResponseBean<CitconTokenBean>> getCashAppCitconToken();

    //citcon cash app pay校验
    @POST("/ec/payment/cashApp/citcon/v2/prepare")
    Observable<ResponseBean<CitconPayBean>> getCashAppPayDataByCitcon(@Body Map<String, Serializable> body);

    //支付检查结果
    @GET("/ec/so/order/query/status")
    Observable<ResponseBean<String>> checkOrderStatus(@Query("order_id") int orderId);

    //支付检查结果
    @POST("/ec/so/order/query/status/v3")
    Observable<ResponseBean<PayStatusBean>> checkOrderStatusV3(@Body Map<String, Serializable> map);

    //查询曾经购买
    @GET("/ec/item/v5/recommend/bought/product/list")
    Observable<ResponseBean<BoughtListBean>> boughtOnce(@Query("limit") int limit, @Query("offset") int offset);

    //猜你喜欢
    @GET("/ec/item/v5/preference/home")
    Observable<ResponseBean<ProductListBean>> preference(@Query("limit") int limit, @Query("offset") int offset);

    //购物车页面猜你喜欢
    @GET("/ec/item/v5/preference/cart")
    Observable<ResponseBean<ProductListBean>> cartPreference(@Query("limit") int limit, @Query("offset") int offset);

    //绑卡-保存
    @POST("/ec/payment/card/profile/attach")
    Observable<SimpleResponseBean> attachCard(@Body Map<String, Serializable> body);

    //绑卡-提交日志
    @POST("/ec/payment/card/profile/log")
    Observable<SimpleResponseBean> attachCardLog(@Body Map<String, Serializable> body);

    //绑卡-列表查询
    @GET("/ec/payment/card/profile/list")
    Observable<ResponseBean<List<CardListBean.CardBean>>> getCards();

    //braintree gain token
    @GET("/ec/payment/card/braintree/token/generate")
    Observable<ResponseBean<String>> generateBraintreeToken();

    //绑卡-保存 braintree
    @POST("/ec/payment/card/braintree/profile/attach")
    Observable<SimpleResponseBean> attachCardByBraintree(@Body Map<String, Serializable> body);

    //绑卡-列表查询
    @POST("/ec/payment/card/braintree/profiles")
    Observable<ResponseBean<List<CardListBean.CardBean>>> getCardsByBraintree();

    /**
     * 购物车简要查询
     */
    @GET("/ec/so/porder/simple")
    Observable<ResponseBean<SimplePreOrderBean>> getSimplePreOrder();

    //商品加入和取消收藏
    @POST("/ec/item/v5/me/favorites/operate")
    Observable<SimpleResponseBean> setCollect(@Body RequestBody body);

    //获取收藏信息商品
    @GET("/ec/item/v5/me/favorites/product_ids")
    Observable<ResponseBean<CollectBean>> getCollect();

    //商品详细信息获取 bundle_id 有这个字段表示是bundle中的商品详情,不是bundle中的商品不传
    @GET("/ec/item/v5/product/detail")
    Observable<ResponseBean<ProductDetailBean>> getProductDetail(@Query("product_id") int productId, @Query("bundle_id") String bundleId, @Query("source_page") String source_page);

    @GET("/ec/item/v5/product/detail/v2")
    Observable<ResponseBean<ProductDetailBean>> getProductDetailV2(@Query("product_id") int productId, @Query("bundle_id") String bundleId, @Query("source_page") String source_page);

    @GET("/ec/content/config/{type}")
    Observable<ResponseBean<String>> getContentConfig(@Path("type") String type);

    @GET("/ec/content/config/getConfig")
    Observable<ResponseBean<ConfigListBean>> getConfig(@Query("configKeyList") String config);

    //商品分享弹窗
    @GET("/ec/growth/share/product")
    Observable<ResponseBean<ShareBean>> getProductShare(@Query("product_id") int id);

    //PDP页面活动信息
    @GET("/ec/promotion/promotions/product/{productId}/v2")
    Observable<ResponseBean<PromotionListBean>> getProductPromotion(@Path("productId") int productId);

    @GET("/ec/social/ugc/product/{productId}/summary")
    Observable<ResponseBean<PdpSummaryBean>> getSummary(@Path("productId") int productId);

    //砍单
    @GET("/ec/mkt/coupons/top_info")
    Observable<ResponseBean<TopInfoBean>> getTopInfo(@Query("page") String page);

    //推荐初始化
    @GET("/ec/recommend/prepare/init")
    Observable<SimpleResponseBean> initRecommend();

    //获取商家introduction
    @GET("ec/marketplace/vendor/seller/introduction/{vendorId}")
    Observable<ResponseBean<VendorIntroductionBean>> getVendorIntroduction(@Path("vendorId") String vendorId);

    @GET("ec/mkt/message/new_user_message?special_message=loyalty&project_name=lightning")
    Observable<ResponseBean<LoyaltyIntroductionBean>> getLoyaltyIntroduction();


    //上传图片
    @POST("/ec/customer/admin_image/upload_image")
    Observable<ResponseBean<UploadImageBean>> uploadImage(@Body UploadJsonBean bean);

    /**********************************************************************************************/
    //首页接口
    @GET("/ec/content/cms/page/{key}")
    Observable<ResponseBean<CmsBean>> getHomeDataByCms(@Path("key") String key, @QueryMap Map<String, Serializable> params);

    //按指定数据源刷新缓存数据接口  需要二次解析
    //url传参，ds_key定义如下
    //    ds_main_banner ： 首页轮播图
    //    ds_notice_banner ： 首页轮播图下边的banner
    //    ds_categories ： 首页商品分类展示
    //    ds_bundles ： 首页bundle子分类展示
    //    ds_feature : 首页特色专区
    //    ds_share_banner ： 首页下方邀请好友/分享 banner
    @GET("/ec/content/admin/refresh/ds/{ds_key}")
    Observable<SimpleResponseBean> refreshHomeData(@Path("ds_key") String dsKey);

    //主题版本检测接口
    @GET("/ec/content/theme/v2/check")
    Observable<ResponseBean<ThemeVersionBean>> checkThemeV2();

    //主题接口
    @GET("/ec/mkt/theme")
    Observable<ResponseBean<ThemeBean>> getTheme();

    //主题接口
    @GET("/ec/content/theme/v2")
    Observable<ResponseBean<ThemeBean>> getThemeV2();

    //获取推荐商品
    @GET
    Observable<ResponseBean<ProductListBean>> getHomeRecommend(@Url String url, @Query("limit") int limit, @Query("offset") int offset);

    //seller list
    @GET
    Observable<ResponseBean<SellerListBean>> getMkplSellerList(@Url String url, @QueryMap Map<String, String> params);

    //seller list
    @GET
    Observable<ResponseBean<ProductWaterfallBean>> getWaterfallData(@Url String url, @QueryMap Map<String, String> params);

    // global plus waterfall
    @GET
    Observable<ResponseBean<CmsContentFeedListBean>> getContentFeed(
            @Url String url, @QueryMap Map<String, String> params);


    /**********************************************************************************************/
    //是否展示评分检测接口
    @GET("/ec/customer/review/check")
    Observable<ResponseBean<ReviewBean>> isShowReview();

    //新版上传资源（图片、文件等）
    @Multipart
    @POST
    Observable<UploadResponseBean> uploadResource(@Url String url, @Header("Authorization") String authorization, @Part List<MultipartBody.Part> partList);

    /**********************************************************************************************/
    //晒单
    //删除结果为空时，提交产品需求
    @POST("/ec/item/noresult")
    Observable<SimpleResponseBean> noResult(@Body RequestBody body);

    /**********************************************************************************************/

    // Affiliate List
    @POST("/ec/customer/affiliate/list")
    Observable<ResponseBean<List<AffiliateListBean>>> queryAffiliateLists(@Body RequestBody body);

    // Affiliate List
    @GET("/ec/growth/share_list/get/all")
    Observable<ResponseBean<List<AffiliateListNewBean>>> queryNewAffiliateLists(@Query("product_id") int product_id);

    @POST("/ec/customer/affiliate/edit/")
    Observable<SimpleResponseBean> createAffiliateList(@Body RequestBody body);

    @POST("/ec/customer/affiliate/add_product/")
    Observable<SimpleResponseBean> addProducts(@Body RequestBody body);

    @POST("/ec/growth/share_list/list/add/pdp/product")
    Observable<ResponseBean<SimpleProductBean>> addNewProducts(@Body RequestBody body);

    //上传资源（图片、文件等）
    //@Headers({"channel: ec", "type: social", "local: social"})
    @Multipart
    @POST
    Observable<UploadResponseBean> uploadVideoResource(@Url String url, @Header("Authorization") String authorization, @Part List<MultipartBody.Part> partList);

    //提交post
    @POST("/ec/social/post")
    Observable<ResponseBean<PostCommitBean>> execCommitPost(@Body RequestBody body);

    //编辑post
    @POST("/ec/social/post/{id}")
    Observable<SimpleResponseBean> editPost(@Path("id") String id, @Body RequestBody body);

    /**********************************************************************************************/

    /**
     * 晒单分类列表
     */
    @GET("/ec/social/post")
    Observable<ResponseBean<PdpVideoListBean>> getPostCategory(@Query("product_id") int product_id, @Query("type") String type);

    /**
     * GenAi列表
     */
    @GET("ec/social/interaction/question/{productId}")
    Observable<ResponseBean<List<String>>> getGenAiQuestion(@Path("productId") int productId);

    /**
     * 获取PDP中间banner信息 和顶部信息使用一个接口，
     * 传参不同做区分，但是top数据可能也包含banner，无法与中间的banner做区分，只能通过数据类型做区分
     * 故而写了两个一样的接口
     */
    @GET("/ec/growth/ads/info")
    Observable<ResponseBean<PdpMiddleBannerBean>> getBannerInfo(@QueryMap Map<String, Serializable> map);
    @GET("/ec/growth/ads/info")
    Observable<ResponseBean<PdpSectionBean>> getPdpSection(@QueryMap Map<String, Serializable> map);

    /**********************************************************************************************/
    //message center
    @GET("/ec/customer/message/category")
    Observable<ResponseBean<List<MessagePortalBean>>> getMessagePortal();

    @GET("/ec/customer/message/list")
    Observable<ResponseBean<MessageCenterBean>> getMessageCenter(@QueryMap Map<String, Serializable> map);

    @GET("/ec/customer/message/sub_category")
    Observable<ResponseBean<List<MessagePortalBean>>> getMessageCenterTab(@QueryMap Map<String, Serializable> map);

    @GET("/ec/customer/message/list")
    Observable<ResponseBean<NotificationCenterBean>> getNotificationCenter(@QueryMap Map<String, Serializable> map);

    @POST("/ec/customer/message/read")
    Observable<SimpleResponseBean> setCustomerMessageRead(@Body RequestBody body);

    @POST("/ec/customer/message/clear")
    Observable<SimpleResponseBean> deleteMessage(@Body RequestBody body);

    @POST("ec/social/message/read")
    Observable<SimpleResponseBean> setMessageRead();

    /**
     * 艾特用户功能
     * ec/social/user/search
     */
    @GET("ec/social/user/search")
    Observable<ResponseBean<List<AtBean>>> getAtList(@Query("keyword") String keyword, @Query("author_id") String author_id, @Query("page") String post_desc);

    /**********************************************************************************************/
    // mkpl(Marketplace)

    /**
     * 商家商品查询--商家页面
     */
    @GET("/ec/item/v3/search/vender")
    Observable<ResponseBean<CateWindowBean>> getShopGood(@QueryMap Map<String, String> map);

    /**
     * editPost 发布视频 进Profile显示的弹窗数据
     */
    @GET("/ec/social/post/flow/notify")
    Observable<ResponseBean<NotifyBean>> getNotify();

    /**
     * editPost 发布视频 email 进入获取商品信息
     */
    @GET("/ec/social/post/products")
    Observable<ResponseBean<List<SimpleProductBean>>> getPostProductList(@Query("product_ids") String product_ids);


    /**
     * popup center需求 页面popup 预验证
     */
    @GET("/ec/activity/popup/precheck")
    Observable<ResponseBean<String>> getPopupCenterPreConfig();

    /**
     * popup center需求 页面popup 内容
     */
    @GET("/ec/activity/popup/page")
    Observable<ResponseBean<PopupCenterBean>> getPopupCenterContent(@Query("page") String pageName, @Query("tag") String tag);

    /**
     * popup center需求 弹框弹出成功
     */
    @PUT("/ec/activity/popup/success")
    Observable<SimpleResponseBean> setPopupDisplayed(@Body RequestBody body);

    //activity center
    @GET("/ec/activity/activity/list")
    Observable<ResponseBean<ActivityCenterData>> getActivityCenter();

    //activity center delete
    @DELETE("/ec/activity/activity/{activity_id}")
    Observable<SimpleResponseBean> deleteActivityCenterItem(@Path("activity_id") int id);

    //activity center delete
    @PUT("/ec/activity/activity/{activity_id}/read")
    Observable<SimpleResponseBean> readActivityCenterItem(@Path("activity_id") int id);

    /**
     * 分享
     */
    @POST("/ec/social/track/share")
    Observable<SimpleResponseBean> trackShare(@Body RequestBody body);

    @GET("/ec/growth/bar/info")
    Observable<ResponseBean<List<TimerBannerBean>>> getTimerBanner();

    @POST("/ec/growth/bar/user/close")
    Observable<SimpleResponseBean> dismissTimerBanner(@Body Map<String, Serializable> body);

    //referrer
    @GET("/ec/customer/invite/track_pop")
    Observable<ResponseBean<ReferrerDescBean>> getReferrerDesc(@Query("id") String id);

    //文案返回接口
    @GET("/ec/customer/invite/get_referral_title")
    Observable<ResponseBean<ReferralTitleBean>> getReferralTitle();

    /**
     * review列表
     */
    @GET("/ec/social/review")
    Observable<ResponseBean<PostCategoryBean>> getReviewCategory(@QueryMap Map<String, Serializable> map);

    //当前支持得语言
    @GET("/ec/social/translate/language")
    Observable<ResponseBean<LanguageBean>> getLanguageList();

    @POST("/ec/social/translate")
    Observable<ResponseBean<SuggestTranslationsBean>> getTranslateList(@Body RequestBody body);

    //搜索轮播关键字返回接口
    @GET("/ec/search/suggestion/search_bar/tips")
    Observable<ResponseBean<KeywordsBean>> getSearchBarTips();

    /**
     * rfm banner 配置
     */
    @GET("/ec/mkt/message/rfm_coupon_message")
    Observable<ResponseBean<RfmBean>> getRfmBanner();

    @GET("/ec/marketplace/global/origin/list")
    Observable<ResponseBean<List<GlobalTabBean>>> getGlobalTab();

    @GET("/ec/content/cms/page/share")
    Observable<ResponseBean<ShareBean>> getGlobalShare(@Query("page_key") String pageKey, @Query("page_type") String pageType);

    @GET("ec/growth/share/{pageKey}")
    Observable<ResponseBean<ShareBean>> getFbwLandingShare(@Path("pageKey") String pageKey);

    @GET("/ec/growth/share/vendor")
    Observable<ResponseBean<ShareBean>> getSellerShare(@QueryMap Map<String, String> params);

    //vendor detail
    @GET("/ec/marketplace/vendor/seller/store/detail/{vendorId}")
    Observable<ResponseBean<SellerInfoBean>> getVendorDetail(@Path("vendorId") String id);

    //vendor detail list
    @GET("/ec/social/seller/{id}/feedback")
    Observable<ResponseBean<SellerFeedBackListBean>> getFeedBackList(@Path("id") String id, @Query("start_id") String startId);

    @GET("/ec/marketplace/vendor/seller/homepage/{seller_id}")
    Observable<ResponseBean<SellerTopBean>> getSellerTop(@Path("seller_id") String id, @QueryMap Map<String, Serializable> params);

    @GET("/ec/marketplace/lightning/carousel")
    Observable<ResponseBean<LightningDealsBean>> getSellerLightningDeals(@QueryMap Map<String, Serializable> params);

    @GET("/ec/so/groupbuy/seller/check/exist")
    Observable<ResponseBean<SellerGroupStatusBean>> getSellerGroupStatus();

    @POST("/ec/so/groupbuy/seller/create")
    Observable<ResponseBean<SellerGroupDetailBean>> createSellerGroupOrder(@Body RequestBody body);

    //follow seller
    @POST("/ec/social/seller/follow")
    Observable<SimpleResponseBean> postFollowSeller(@Body RequestBody body);

    //调用一键开启关闭权限接口
    @POST("/ec/customer/account/notificationV2")
    Observable<SimpleResponseBean> postAccountNotificationV2(@Body RequestBody body);

    //vendor detail star
    @GET("ec/social/seller/{id}/summary")
    Observable<ResponseBean<SellerSummaryBean>> getSellerSummary(@Path("id") String id);

    @GET("ec/item/v2/items/{product_id}/modules?page=pdp_v2")
    Observable<ResponseBean<PdpModulesBean>> getPdpModules(@Path("product_id") String product_id);

    @GET("ec/item/v2/items/{product_id}/modules/second")
    Observable<ResponseBean<PdpModulesBean>> getRecentViewed(@Path("product_id") String product_id, @Query("from_page") String from_page);

    @GET("ec/so/groupbuy/seller/detail")
    Observable<ResponseBean<GroupBuySellerDetailBean>> getGroupBuySellerDetail();

    @GET("ec/so/addon/session")
    Observable<ResponseBean<AddOnDetailBean>> getAddOnDetail();

    @GET("ec/customer/messages/home_header")
    Observable<ResponseBean<List<NewTopMessageBean>>> getCartTopMessage(@QueryMap Map<String, Serializable> map);

    /**
     * seller 晒单视频列表
     */
    @GET("/ec/social/seller/{sellerId}/post")
    Observable<ResponseBean<PostCategoryBean>> getSellerPostVideo(
            @Path("sellerId") int sellerId, @QueryMap Map<String, Serializable> map);

    /**
     * seller 晒单评论列表
     */
    @GET("/ec/social/seller/{sellerId}/review")
    Observable<ResponseBean<PostCategoryBean>> getSellerPostReview(
            @Path("sellerId") int sellerId, @QueryMap Map<String, Serializable> map);

    //获取分类列表
    @GET("ec/item/v5/categories")
    Observable<ResponseBean<CateBean>> getCategory();

    @GET("ec/item/v3/search/content/catalogue")
    Observable<ResponseBean<CategoryPagerBean>> getCategoryPagerData(@QueryMap Map<String, String> params);

    /**
     * 合并结算购物车新接口
     */
    @GET("/ec/so/porder/v5")
    Observable<ResponseBean<NewPreOrderBean>> getPreOrderV5(@QueryMap Map<String, Serializable> params);

    @POST("/ec/so/porder/items/select")
    Observable<ResponseBean<NewPreOrderBean>> selectCartItems(@Body Map<String, Serializable> map);

    //商品增删改  合并结算购物车页面
    @PUT("/ec/so/porder/items/cart/v3")
    Observable<ResponseBean<NewPreOrderBean>> updateCartV3(@Body RequestBody body);

    @POST("/ec/so/activity/gift/cart/delete/v2")
    Observable<ResponseBean<NewPreOrderBean>> deleteActivityGiftV2(@Body RequestBody requestBody);

    @GET("/ec/item/v5/checkout/upsell/v2")
    Observable<ResponseBean<UpSellBean>> upSellV2(@Query("cart_domain") String cartDomain);

    @GET("ec/so/address/location")
    Observable<ResponseBean<GeoBean>> getOrderLocation();

    @POST("ec/so/address/verify/address/record")
    Observable<SimpleResponseBean> recordLocation(@Body Map<String, Serializable> body);

    // seller mini cart recommend
    @GET("/ec/item/v1/recommend/modules/cart/seller/{sellerId}")
    Observable<ResponseBean<PdpModulesBean>> getSellerCartRecommend(@Path("sellerId") String seller_id);

    // 获取指定商家购物车
    @GET("ec/so/seller/cart/float/v2")
    Observable<ResponseBean<GlobalCartListBean>> getSellerCartFloatList(@QueryMap Map<String, Serializable> params);

    @HTTP(method = "DELETE", path = "/ec/so/save4later", hasBody = true)
    Observable<SimpleResponseBean> deleteSave4Later(@Body Map<String, Serializable> map);

    //购物车商品===》save 4 later
    @POST("/ec/so/save4later/from_cart")
    Observable<ResponseBean<NewPreOrderBean>> cartToSave4Later(@Body Map<String, Serializable> map);

    //save 4 later商品重新加回购物车
    @POST("/ec/so/save4later/to_cart")
    Observable<ResponseBean<NewPreOrderBean>> save4LaterToCart(@Body Map<String, Serializable> map);

    // 商品增删改 - Marketplace 页面
    @PUT("ec/so/seller/items/v2")
    Observable<ResponseBean<GlobalCartListBean>> updateGlobalCartV2(
            @Query("vendor_id") String sellerId,
            @Body RequestBody body
    );

    @POST("ec/marketplace/coupons/sellers/{seller_id}")
    Observable<ResponseBean<CouponClaimBean>> claimSellerCoupon(
            @Path("seller_id") String sellerId,
            @Body RequestBody body
    );

    @GET("/ec/so/save4later/v2")
    Observable<ResponseBean<SaveForLaterResponseBean>> getSave4later(@Query("offset") int offset, @Query("page_size") int page_size);

    @GET("/ec/social/post/{postId}/products/v3")
    Observable<ResponseBean<List<ProductNewBean>>> getPostNewProducts(@Path("postId") String postId);

    @GET("ec/social/message/stat")
    Observable<ResponseBean<PostGoldBean>> getPostGold();

    @GET("/ec/payment/paypal/bnpl")
    Observable<ResponseBean<BnplBean>> getBnplData(@Query("amount") double amount);

    @GET("/ec/content/banner")
    Observable<ResponseBean<List<SplashScreenBean>>> getSplashScreen(@Query("type") String type, @Query("sales_org_id") String salesOrgId);

    @POST("/ec/mkt/activity/lightning_deals/remind")
    Observable<SimpleResponseBean> changeLightningDealsRemind(@Body RequestBody requestBody);

    @POST("/ec/growth/affiliate/page_review")
    Observable<SimpleResponseBean> reportReferralInfo(@Body Map<String, Serializable> params);

    @POST("/central/notification/ai_shopping/message")
    Observable<ResponseBean<AiShoppingResponseBean>> aiShoppingMessage(@Body Map<String, Serializable> map);

    @POST("ec/item/ai/message/like")
    Observable<SimpleResponseBean> aiShoppingMessageLike(@Body Map<String, Serializable> map);

    @GET("/ec/so/coupons/list/v2")
    Observable<ResponseBean<CouponBean>> getCoupons(@Query("cart_domain") String type);

    @GET("/ec/mkt/coupons/checkout")
    Observable<ResponseBean<CouponBean>> getCoupons(@Query("type") String type,
                                                    @Query("deal_id") String deal_id,
                                                    @Query("vendor_id") String vendor_id,
                                                    @Query("amount") double amount);

    @GET
    Observable<ResponseBean<List<ProductBean>>> getCartRecommend(@Url String url, @Header("zipcode") String zipCode, @Header("date") String date);

    @GET("/ec/growth/message/get_safe_secure_tips")
    Observable<ResponseBean<List<BesideTipsBean>>> getSecureTips();

    @POST("/ec/customer/user/address/update_email")
    Observable<SimpleResponseBean> updateEmail(@Body Map<String, Serializable> map);

    @POST("/ec/promotion/mkpl/coupon/obtain")
    Observable<ResponseBean<CouponClaimBean>> obtainSkuCoupon(@Body Map<String, Serializable> map);

    @GET("/ec/content/collection/{key}")
    Observable<ResponseBean<CmsBean>> getCollectionAutoData(@Path("key") String key, @QueryMap Map<String, Serializable> params);

    @GET
    Observable<ResponseBean<UpsellMoreBean>> getMoreUpsell(@Url String url, @Query("offset") int offset);

    @POST("/ec/so/porder/trade_in/tag")
    Observable<ResponseBean<UpdateResultBean.TagInfoBean>> getTradeInTag(@Body Map<String, Serializable> map);

    @GET("/ec/cs/seller/{sellerId}/unread")
    Observable<ResponseBean<UnReadBean>> getUnRead(@Path("sellerId") String sellerId, @Query("sellerShopMode") String sellerShopMode);

    @GET("/ec/cs/seller/{sellerId}/contactButtonText")
    Observable<ResponseBean<PdpContactButtonTextBean>> getContactButtonText(@Path("sellerId") String sellerId, @QueryMap Map<String, Serializable> map);

    //前端调用时机： 当用户点击卡片进入pdp，未加购 -返回后 调用
    @GET("/ec/item/v3/items/{id}/related")
    Observable<ResponseBean<RelatedBean>> getProductPdpRelated(@Path("id") int id);

    //前端调用时机：当用户点击卡片从 0-1加购调用
    @GET("/ec/item/v3/items/{id}/buy_together")
    Observable<ResponseBean<RelatedBean>> getProductBuyTogether(@Path("id") int id);

    @POST("/ec/promotion/coupon/cms/claim/batch")
    Observable<ResponseBean<CmsCouponBean>> batchClaimCoupon(@Body RequestBody body);

    // ========== Pre-sale ========== //
    @GET("ec/item/sale_events/presale/queryById")
    Observable<ResponseBean<SaleEventBean>> getSaleEventById(@Query("sale_event_id") int salesEventId);


    @POST("/ec/so/order/query/listMyOrder/v2")
    Observable<ResponseBean<OrderListBean>> getOrderList(@Body Map<String, Serializable> map);

    @GET("/ec/item/v5/recommend/bought/simple")
    Observable<ResponseBean<OrderBoughtBean>> getOrderBoughtSimple(@Query("limit") int limit);

    @GET("/ec/so/order/query/buyAgain/info/v2")
    Observable<ResponseBean<ReorderBean>> getOrderByAgain(@Query("order_id") String orderId);

    @GET("/ec/social/review/to_review/info")
    Observable<ResponseBean<ToReviewInfoBean>> getToReviewInfo();

    @POST("/ec/so/order/all/cancel")
    Observable<ResponseBean<OrderCancelBean>> cancelAllOrder(@Body Map<String, Serializable> params);

    @POST("ec/so/order/unpaid/cancel")
    Observable<SimpleResponseBean> cancelUnpaidOrder(@Body List<String> params);

    @GET("/ec/so/order/query/{order_id}/track_order")
    Observable<ResponseBean<OrderTrackInfoBean>> getTrackInfo(@Path("order_id") String orderId);

    @PUT("/ec/so/order/confirm_receipt/{order_id}")
    Observable<SimpleResponseBean> confirmReceipt(@Path("order_id") String orderId);

    @GET("/ec/so/order/query/cancel/reason")
    Observable<ResponseBean<List<OrderCancelReasonBean>>> getOrderCancelReasons();

    @POST("/ec/so/order/cache/cancel_request")
    Observable<ResponseBean<OrderCancelRefundBean>> cancelCacheOrder(@Body Map<String, Serializable> params);

    @GET("/ec/so/config/refund_points_flag/value")
    Observable<SimpleResponseBean> checkPointsRefund();

    @GET("ec/growth/banner/get_category_banner/v2")
    Observable<ResponseBean<CategoryBannerV2Bean>> getCategoryBannerV2(@QueryMap Map<String, Serializable> params);

    //collection fallback页面猜你喜欢
    @GET("/ec/item/v5/preference/home")
    Observable<ResponseBean<ProductListBean>> homePreference(@Query("limit") int limit, @Query("offset") int offset);

    @POST("ec/payment/braintree/paypal/v2/pay")
    Observable<SimpleResponseBean> paypalBraintreePayV2(@Body RequestBody body);

    @POST("ec/payment/venmo/braintree/pay")
    Observable<SimpleResponseBean> venmoBraintreePay(@Body RequestBody body);

    @POST("ec/payment/venmo/braintree/v2/pay")
    Observable<SimpleResponseBean> venmoBraintreePayV2(@Body RequestBody body);

    @PUT("/ec/so/porder/apply_with_order_id/{order_id}")
    Observable<SimpleResponseBean> updateAddress(@Path("order_id") int order_id);

    //查询预设问题
    @GET("/ec/social/interaction/question/{id}")
    Observable<ResponseBean<List<String>>> getInteractionQuestion(@Path("id") String id);

    @POST("ec/social/interaction/message")
    Observable<ResponseBean<InteractionMessageBean>> interactionMessage(@Body Map<String, Serializable> map);

    @GET("/ec/so/order/query/unpaid/reminder")
    Observable<ResponseBean<ReminderBean>> getUnpaidReminder();

    @GET("/ec/payment/ebt/profiles")
    Observable<ResponseBean<List<CardAttachBean>>> getEbtPaymentProfiles();
}
