package com.sayweee.weee.module.home.provider.category;

import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;

class CategoryAutoScroller {

    private static final long SCROLL_DELAY_MILLIS = 300L;
    private static final long SCROLL_AUTO_DELAY_MILLIS = 22L;
    private static final int SCROLL_PIXELS_X = 1;

    private enum Direction {
        None,
        Left,
        Right,
    }

    private final RecyclerView recyclerView;

    private Direction direction = Direction.Right;
    private Direction wantDirection = Direction.None;
    private boolean isEnable = false;
    private boolean isRunning = false;
    private boolean isDisabled = false;

    private final Runnable autoScrollRunnable;

    public CategoryAutoScroller(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
        autoScrollRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isEnable) {
                    return;
                }

                int dx;
                if (direction == Direction.Right) {
                    dx = SCROLL_PIXELS_X;
                } else if (direction == Direction.Left) {
                    dx = -SCROLL_PIXELS_X;
                } else {
                    dx = 0;
                }
                recyclerView.scrollBy(dx, 0);
                recyclerView.postDelayed(this, SCROLL_AUTO_DELAY_MILLIS);
            }
        };
    }

    public void setDisabled(boolean isDisabled) {
        this.isDisabled = isDisabled;
        if (isDisabled) {
            stopAutoScroll();
        }
    }

    public boolean isDisabled() {
        return isDisabled;
    }

    public void init() {
        RecyclerView.OnScrollListener onScrollListener = new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    stopAutoScroll();
                    return;
                }

                if (oldState != RecyclerView.SCROLL_STATE_IDLE && newState == RecyclerView.SCROLL_STATE_IDLE) {
                    checkDirection(wantDirection);
                    prepareAutoScroll();
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (dx == 0) {
                    return;
                }
                Direction direction = dx < 0 ? Direction.Left : Direction.Right;
                checkDirection(direction);
            }

        };
        recyclerView.addOnScrollListener(onScrollListener);

        RecyclerView.OnItemTouchListener onItemTouchListener = new RecyclerView.OnItemTouchListener() {

            private float lastX;

            @Override
            public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                if (e.getAction() == MotionEvent.ACTION_DOWN) {
                    lastX = e.getX();
                } else if (e.getAction() == MotionEvent.ACTION_UP) {
                    wantDirection = Direction.None;
                    float delta = e.getX() - lastX;
                    if (Math.abs(delta) > 10) {
                        wantDirection = delta > 0 ? Direction.Left : Direction.Right;
                    }
                }
                return false;
            }

            @Override
            public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                // no op
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
                // no op
            }
        };
        recyclerView.addOnItemTouchListener(onItemTouchListener);
    }

    public void attach() {
        recyclerView.postDelayed(this::prepareAutoScroll, SCROLL_DELAY_MILLIS);
    }

    private void prepareAutoScroll() {
        if (isDisabled) {
            return;
        }

        boolean canScrollRight = recyclerView.canScrollHorizontally(1);
        boolean canScrollLeft = recyclerView.canScrollHorizontally(-1);
        if (!canScrollRight && !canScrollLeft) {
            isEnable = false;
            stopAutoScroll();
            return;
        }

        isEnable = true;
        checkDirection(direction);
        recyclerView.removeCallbacks(autoScrollRunnable);
        recyclerView.post(autoScrollRunnable);
        isRunning = true;
    }

    private void checkDirection(Direction expectDirection) {
        if (expectDirection == Direction.None) {
            return;
        }
        direction = expectDirection;
        if (expectDirection == Direction.Right) {
            boolean canScrollRight = recyclerView.canScrollHorizontally(1);
            if (!canScrollRight) {
                direction = Direction.Left;
            }
        } else if (expectDirection == Direction.Left) {
            boolean canScrollLeft = recyclerView.canScrollHorizontally(-1);
            if (!canScrollLeft) {
                direction = Direction.Right;
            }
        }
    }

    public void detach() {
        isEnable = false;
        stopAutoScroll();
    }

    private void stopAutoScroll() {
        if (isRunning) {
            recyclerView.removeCallbacks(autoScrollRunnable);
            isRunning = false;
        }
    }
}
