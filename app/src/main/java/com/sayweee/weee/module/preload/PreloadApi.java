package com.sayweee.weee.module.preload;

import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cate.product.ReferralTitleBean;
import com.sayweee.weee.module.cate.product.bean.PromotionListBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.order.bean.OrderBoughtBean;
import com.sayweee.weee.module.order.bean.OrderListBean;
import com.sayweee.weee.module.post.bean.PdpVideoListBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.PdpSummaryBean;
import com.sayweee.wrapper.bean.ResponseBean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;

//
// Created by Thomsen on 31/05/2024.
// Copyright (c) 2024 Weee LLC. All rights reserved.
//
public interface PreloadApi {

    @GET("/ec/item/v5/recommend/bought/simple")
    Call<ResponseBean<OrderBoughtBean>> getOrderBoughtSimple(@Query("limit") int limit);

    @POST("/ec/so/order/query/listMyOrder/v2")
    Call<ResponseBean<OrderListBean>> getOrderList(@Body Map<String, Serializable> map);


    // pdp 产品详情
    @GET("/ec/item/v5/product/detail/v2")
    Call<ResponseBean<ProductDetailBean>> getProductDetail(@QueryMap Map<String, Object> params);

    // pdp 活动信息
    @GET("/ec/promotion/promotions/product/{productId}/v2")
    Call<ResponseBean<PromotionListBean>> getProductPromotion(@Path("productId") int productId);

    // pdp review
    @GET("/ec/social/review")
    Call<ResponseBean<PostCategoryBean>> getReviewCategory(@QueryMap Map<String, Serializable> map);

    // pdp post
    @GET("/ec/social/post")
    Call<ResponseBean<PdpVideoListBean>> getPostCategory(@Query("product_id") int product_id, @Query("type") String type);

    // pdp modules
    @GET("/ec/item/v2/items/{product_id}/modules?page=pdp_v2")
    Call<ResponseBean<PdpModulesBean>> getPdpModules(@Path("product_id") String product_id);

    // pdp referral 文案
    @GET("/ec/customer/invite/get_referral_title")
    Call<ResponseBean<ReferralTitleBean>> getReferralTitle();

    // pdp waterfall
    @GET
    Call<ResponseBean<CmsContentFeedListBean>> getContentFeed(
            @Url String url, @QueryMap Map<String, String> params);

    // pdp summary
    @GET("/ec/social/ugc/product/{productId}/summary")
    Call<ResponseBean<PdpSummaryBean>> getSummary(@Path("productId") int productId);

    /**
     * GenAi列表
     */
    @GET("ec/social/interaction/question/{productId}")
    Call<ResponseBean<List<String>>> getGenAiQuestion(@Path("productId") int productId);

    /**
     * 获取PDP中间banner信息 和顶部信息使用一个接口，
     * 传参不同做区分，但是top数据可能也包含banner，无法与中间的banner做区分，只能通过数据类型做区分
     * 故而写了两个一样的接口
     */
    @GET("/ec/growth/ads/info")
    Call<ResponseBean<PdpMiddleBannerBean>> getBannerInfo(@QueryMap Map<String, Serializable> map);
    @GET("/ec/growth/ads/info")
    Call<ResponseBean<PdpSectionBean>> getPdpSectionInfo(@QueryMap Map<String, Serializable> map);
}
