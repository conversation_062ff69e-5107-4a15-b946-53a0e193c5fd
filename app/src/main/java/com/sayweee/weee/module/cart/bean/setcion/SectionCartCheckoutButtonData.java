package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.adapter.SectionCartAdapter;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.IFrameUi;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.service.analytics.bean.EagleElement;

import java.io.Serializable;

public class SectionCartCheckoutButtonData
        extends AdapterWrapperData<NewSectionBean>
        implements Serializable, IFrameUi {

    private CartFrameUiData uiData;
    private EagleElement eagleElement;

    public SectionCartCheckoutButtonData(NewSectionBean data) {
        super(CartSectionType.TYPE_CHECKOUT_BUTTON, data);
        this.uiData = new CartFrameUiData(
                /* innerFrame= */-1,
                /* outerFrame= */SectionCartAdapter.BOTTOM_FRAME,
                /* hasTopDivider= */false
        );
    }

    @Override
    public void setFrameUiData(CartFrameUiData uiData) {
        this.uiData = uiData;
    }

    @Override
    public CartFrameUiData getFrameUiData() {
        return uiData;
    }

    public Integer getVendorId() {
        return t != null && t.vendor_info != null ? t.vendor_info.vendor_id : -1;
    }

    public EagleElement getEagleElement() {
        return eagleElement;
    }

    public void setEagleElement(EagleElement eagleElement) {
        this.eagleElement = eagleElement != null ? new EagleElement(eagleElement) : null;
    }
}
