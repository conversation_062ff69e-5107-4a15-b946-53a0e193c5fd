package com.sayweee.weee.module.seller;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.text.style.StrikethroughSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Space;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentContainerView;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.AppBarLayout;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ActivitySellerBinding;
import com.sayweee.weee.databinding.DialogMkplCouponClaimedFollowSellerBinding;
import com.sayweee.weee.databinding.DialogMkplUnfollowSellerBinding;
import com.sayweee.weee.databinding.ItemSellerPromotionGiftBinding;
import com.sayweee.weee.databinding.LayoutSellerBottomPromotionBarBinding;
import com.sayweee.weee.databinding.LayoutSellerReminderContentBinding;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.CartActivity;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cms.iml.title.data.SimpleTitleData;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.mkpl.GlobalMiniCartFragment;
import com.sayweee.weee.module.mkpl.GlobalMiniCartViewModel;
import com.sayweee.weee.module.mkpl.GlobalOnCartEditListener;
import com.sayweee.weee.module.mkpl.base.MkplBaseActivity;
import com.sayweee.weee.module.mkpl.bean.GlobalCartBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.post.helper.SocialStatusHelper;
import com.sayweee.weee.module.post.service.IPostStatus;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.module.seller.adapter.SellerItemAdapter;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.CouponClaimRequest;
import com.sayweee.weee.module.seller.bean.CouponClaimResponse;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.module.seller.bean.SellerPromotionDetailBean;
import com.sayweee.weee.module.seller.bean.SellerTopBean;
import com.sayweee.weee.module.seller.common.mpager.MPagerEntity;
import com.sayweee.weee.module.seller.common.mpager.MPagerTabAdapter;
import com.sayweee.weee.module.seller.common.mpager.MPagerViewAdapter;
import com.sayweee.weee.module.seller.service.SellerViewModel;
import com.sayweee.weee.module.seller.sheet.SellerPromotionDetailFragment;
import com.sayweee.weee.module.seller.view.SellerCouponView;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.SellerConfigBean;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.span.AppHtmlSpanner;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.snackbar.ToastySnackBarView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.weee.widget.timer.OnSimpleSafelyTimerListener;
import com.sayweee.weee.widget.timer.OnSimpleTimerListener;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.snackbar.SnackBarOptions;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Author:  winds
 * Date:    2023/4/12.
 * Desc:Seller Home
 */
public class SellerActivity extends MkplBaseActivity<SellerViewModel> {

    private static final String EXTRA_SELLER_ID = "sellerId";

    private GlobalMiniCartViewModel cartViewModel;

    private ImageView ivAddFollow;
    private ShapeTextView tvFollow;
    private LinearLayout layoutCoupons;
    private LinearLayout layoutPromotions;
    private CompatMagicIndicator indicator;
    private ViewPager2 pager2;
    private View btnGotoTop;
    private FragmentContainerView miniCartContainer;
    private ShareDialog shareDialog;

    public String sellerId;
    @Nullable
    private SellerTopBean sellerTopBean;
    private String tabByUrl;
    public String filter;
    public String sort;
    public String filtersByUrl;
    public String sortByUrl, emailUrl;
    public String bizType;
    private boolean isGroupNeedRefresh = true;
    private final List<MPagerEntity> tabEntities = new ArrayList<>();

    private ActivitySellerBinding binding;

    private SellerItemAdapter lightningDealsAdapter;

    public static Intent getIntent(Context context, String sellerId) {
        return getIntentByUrl(context, sellerId, null, null);
    }

    public static Intent getIntentByUrl(Context context, String sellerId,
                                        SellerPageParams params, String url) {
        return new Intent(context, SellerActivity.class)
                .putExtra(EXTRA_SELLER_ID, sellerId)
                .putExtra(SellerPageParams.EXTRA_SELLER_PARAMS, params)
                .putExtra("url", url);
    }

    @Nullable
    private String getExtraSellerId() {
        return getIntent() != null ? getIntent().getStringExtra(EXTRA_SELLER_ID) : null;
    }

    @Nullable
    private String getExtraBizType() {
        return bizType;
    }

    private boolean getExtraIsFbw() {
        return "fbw".equals(getExtraBizType());
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }

    @Override
    protected void beforeCreate() {
        super.beforeCreate();
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, getPageKey(),
                String.valueOf(hashCode()));
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_seller;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        binding = ActivitySellerBinding.bind(contentView);
        ivAddFollow = findViewById(R.id.iv_add_follow);
        tvFollow = findViewById(R.id.tv_follow);
        layoutCoupons = findViewById(R.id.layout_coupons);
        layoutPromotions = findViewById(R.id.layout_promotions);
        btnGotoTop = findViewById(R.id.layout_goto_top);
        indicator = findViewById(R.id.indicator);
        pager2 = findViewById(R.id.pager);
        pager2.setUserInputEnabled(false);
        miniCartContainer = findViewById(R.id.fragment_container);

        initAppBarLayout();
        initListener();
        initObserver();
        initLightningDealsCarousel();
    }

    private void initListener() {
        View.OnClickListener onClickListener = new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                handleOnClick(v);
            }
        };
        setOnClickListener(
                onClickListener,
                R.id.iv_back, R.id.iv_search, R.id.iv_share, R.id.v_cart,
                R.id.btn_follow, R.id.layout_top_info, R.id.layout_goto_top,
                R.id.in_group_order_card,
                R.id.tv_remind_revoke, R.id.iv_dots, R.id.iv_email
        );

        ViewTools.setViewOnSafeClickListener(
                binding.inTitleGroupOrderEnhanceBtn.getRoot(),
                v -> prepareShowGroupOrderDialog()
        );
    }

    private void initObserver() {
        getGlobalCartFragmentManager().setFragmentResultListener(
                GlobalMiniCartFragment.REQUEST_KEY,
                this,
                (requestKey, result) -> {
                    boolean isCheckout = result.getBoolean(GlobalMiniCartFragment.RESULT_BUNDLE_CHECKOUT, false);
                    boolean isGroupOrder = result.getBoolean(GlobalMiniCartFragment.RESULT_BUNDLE_GROUP_ORDER, false);
                    if (isCheckout || isGroupOrder) {
                        isGroupNeedRefresh = true;
                    }
                });
    }

    private void initLightningDealsCarousel() {
        String sellerId = getExtraSellerId();
        SafeStaggeredGridLayoutManager manager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        binding.rcvLightningDeals.setLayoutManager(manager);
        binding.rcvLightningDeals.setNestedScrollingEnabled(false);
        GlobalOnCartEditListener onCartEditListener = new GlobalOnCartEditListener(
                sellerId,
                new GlobalOnCartEditListener.SimpleOnGlobalCartUpdateListener() {
                    @Override
                    public void onGlobalCartListUpdate(@NonNull GlobalCartListResponse response) {
                        if (cartViewModel != null) {
                            cartViewModel.notifyGlobalCartListResponseLiveDataChange(response);
                        }
                    }
                });
        onCartEditListener.setEnabled(!getExtraIsFbw());
        SellerItemAdapter adapter = new SellerItemAdapter(onCartEditListener);
        lightningDealsAdapter = adapter;
        adapter.setEnableLoadMore(false);
        adapter.onCtxAdded(null, null, null, null, sellerId, null, sellerId);
        adapter.setOnLightingDealTimerListener(new OnSimpleSafelyTimerListener() {
            @Override
            public void onEndSafely() {
                viewModel.getSellerLightningDeals(getExtraSellerId(), getExtraBizType());
            }
        });
        adapter.setOnRemindListener((product, isRemind, position) -> {
            if (isRemind) {
                showRemindTips(product.id);
            } else {
                setRemindEquals(product.id);
            }
            viewModel.changeLightningDealsRemind(product.id, isRemind);
        });
        binding.rcvLightningDeals.setAdapter(adapter);
        binding.rcvLightningDeals.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    lightningDealsAdapter.onPageScrollStateChanged(recyclerView, newState);
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });
    }

    @Override
    public void loadData() {
        String url = getIntent().getStringExtra("url");
        if (!EmptyUtils.isEmpty(url)) {
            Map<String, String> paramsByUrl = CommonTools.parseQueryParams(url, true);
            tabByUrl = paramsByUrl.get("tab");
            filtersByUrl = paramsByUrl.get(Constants.UrlMapParams.FILTERS);
            sortByUrl = paramsByUrl.get(Constants.UrlMapParams.SORT);
            bizType = paramsByUrl.get(Constants.UrlMapParams.BIZ_TYPE);
        }
        sellerId = getIntent().getStringExtra(EXTRA_SELLER_ID);
        viewModel.setCurSellerId(sellerId);
        showVeilTemplated(true);
    }

    @Override
    public <M> M createModel() {
        cartViewModel = ViewModelProviders.of(this).get(GlobalMiniCartViewModel.class);
        cartViewModel.injectLifecycle(getLifecycle());
        return super.createModel();
    }

    @Override
    public void attachModel() {
        viewModel.sellerCombinedData.observe(this, data -> {
            handleSellerTopData(data != null ? data.sellerTopBean : null);
            handleLightningDeals(data != null ? data.lightingDealsData : null);
        });
        viewModel.sellerTopData.observe(this, this::handleSellerTopData);
        viewModel.sellerLightningDealsData.observe(this, this::handleLightningDeals);

        viewModel.shareData.observe(this, shareBean -> {
            if (shareBean == null) return;
            if (shareDialog == null) {
                shareDialog = new ShareDialog(SellerActivity.this);
            }
            if (!shareDialog.isShowing()) {
                shareDialog.setShareData(shareBean);
                shareDialog.show();
            }
        });

        viewModel.sellerGroupStatus.observe(this, this::handleSellerGroupStatusLiveData);

        viewModel.sellerGroupDetail.observe(this, groupDetailBean -> {
            Map<String, Object> ctxEvent = viewModel.getOrderOrderTrackingCtx(
                    groupDetailBean.vendor_id,
                    groupDetailBean.count_users,
                    groupDetailBean.count_items,
                    groupDetailBean.is_creator,
                    groupDetailBean.key
            );
            EagleTrackManger.get().trackEagleClickAction(
                    /* modNm= */null,
                    /* modPos= */-1,
                    /* secNm= */null,
                    /* secPos= */-1,
                    /* targetNm= */"start_group_order",
                    /* targetPos= */0,
                    /* targetType= */EagleTrackEvent.TargetType.POPUP,
                    /* clickType= */EagleTrackEvent.ClickType.VIEW,
                    /* ctx= */ctxEvent
            );

            Intent intent = WebViewActivity.getIntent(activity, groupDetailBean.group_buy_url_host);
            startActivity(intent);
            loadSellerData();
        });

        SharedViewModel.get().followChangeData.observe(this, map -> {
            if (map == null || map.isEmpty()) return;
            if (sellerTopBean == null) return;
            SellerTopBean.SellerFollowInfo sellerFollowInfo = sellerTopBean.seller_follow_info;
            if (sellerFollowInfo != null && sellerId != null) {
                String uid = map.keyAt(0);
                if (sellerId.equalsIgnoreCase(uid)) {
                    Integer status = map.get(uid);
                    if (status == null) {
                        return;
                    }
                    sellerFollowInfo.status = status == IPostStatus.STATUS_FOLLOWED ? "A" : "C";
                    updateSocialStatus(binding.btnFollow.getRoot(), ivAddFollow, tvFollow, sellerFollowInfo.getFollowStatus());
                }
            }
        });

        viewModel.sellerCouponClaimResponseLiveData.observe(this, this::handleSellerCouponClaimResponse);

        viewModel.remindData.observe(this, this::refreshRemindSet);

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, integer -> {
            if (Constants.OrderRecreateType.ACCOUNT_CHANGED == integer) {
                // login for refresh follow status
                isGroupNeedRefresh = true;
                viewModel.getSellerTop(sellerId, getExtraBizType());
            } else if (Constants.OrderRecreateType.DATE_CHANGED == integer) {
                // force reload this page
                finish();
                overridePendingTransition(0, 0);
                startActivity(getIntent());
                overridePendingTransition(0, 0);
            }
        });

        viewModel.unReadData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                ViewTools.setViewVisible(binding.ivDot, aBoolean);
            }
        });

        attachCartViewModel();
    }

    private void attachCartViewModel() {
        if (cartViewModel == null) return;
        cartViewModel.getGlobalCartListResponseLiveData().observe(this, this::handleGlobalCartListResponse);
        cartViewModel.getGlobalMiniCartPageStateLiveData().observe(this, pageState -> {
            String sellerId = getExtraSellerId();
            if (pageState.isExpended()) {
                Map<String, Object> ctx = new EagleContext()
                        .setPageTarget(sellerId)
                        .setGlobalVendor(sellerId)
                        .asMap();
                EagleTrackManger.get().trackEagleClickAction(
                        /* modNm= */null,
                        /* modPos= */-1,
                        /* secNm= */null,
                        /* secPos= */-1,
                        /* targetNm= */"mkpl_mini_cart",
                        /* targetPos= */-1,
                        /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                        /* clickType= */EagleTrackEvent.ClickType.VIEW,
                        /* ctx= */ctx
                );
                lightningDealsAdapter.onPagePause(binding.rcvLightningDeals);
            } else {
                logPageView();
                lightningDealsAdapter.onPageResume(binding.rcvLightningDeals);
                ProductSyncHelper.onPageResume(lightningDealsAdapter);
            }
        });
    }

    private void handleSellerTopData(@Nullable SellerTopBean data) {
        if (data == null) {
            ViewTools.setViewVisible(false, findViewById(R.id.layout_top_info));
            fillEmpty();
            return;
        }

        sellerTopBean = data;
        fillSellerTop(data);
        // group order entrance
        fillGroupOrderBar(data);
        ViewTools.setViewVisibilityIfChanged(miniCartContainer, !getExtraIsFbw());
        if (isGroupNeedRefresh && !isVeilShowing()) {
            return;
        }
        isGroupNeedRefresh = false;
        fillPager(data);

    }

    private void fillGroupOrderBar(@Nullable SellerTopBean seller) {
        if (seller == null) return;
        resetGroupOrderViews();
        if (getExtraIsFbw()) {
            return;
        }

        SellerTopBean.GroupOrderBar groupOrderBar = seller.group_order_bar;
        binding.inGroupOrderCard.getRoot().setTag(groupOrderBar);
        boolean showGroupOrder = seller.show_group_order;
        boolean isGroupOrderExist = seller.show_group_order && groupOrderBar != null && !EmptyUtils.isEmpty(groupOrderBar.key);

        // tip
        String groupOrderTip = null;
        if (seller.show_group_order && groupOrderBar != null) {
            groupOrderTip = groupOrderBar.pts_tip;
        }
        if (!isGroupOrderExist) {
            binding.inTitleGroupOrderEnhanceBtn.tvGroupOrderTip.setText(groupOrderTip);
            ViewTools.setViewVisibilityIfChanged(binding.inTitleGroupOrderEnhanceBtn.tvGroupOrderTip, !EmptyUtils.isEmpty(groupOrderTip));
        }
        ViewTools.setViewVisibilityIfChanged(binding.ivShare, true);
        ViewTools.setViewVisibilityIfChanged(binding.inTitleGroupOrderEnhanceBtn.getRoot(), showGroupOrder && !isGroupOrderExist);

        if (isGroupOrderExist) {
            binding.inGroupOrderCard.tvGroupOrder.setText(groupOrderBar.title);
            binding.inGroupOrderCard.tvInfoGroupOrder.setText(groupOrderBar.description);
            binding.inGroupOrderCard.tvGroupOrderTip.setText(groupOrderTip);
            ViewTools.setViewVisibilityIfChanged(binding.inGroupOrderCard.flGroupOrderTip, !EmptyUtils.isEmpty(groupOrderTip));
            reportGroupOrderBannerImp(seller, groupOrderBar);
        }
        ViewTools.setViewVisibilityIfChanged(binding.inGroupOrderCard.getRoot(), isGroupOrderExist);

        ViewTools.setViewVisibilityIfChanged(
                binding.flGroupOrder,
                ViewTools.isViewVisible(binding.inGroupOrderCard.getRoot())
        );
    }

    private void resetGroupOrderViews() {
        ViewTools.setViewVisibilityIfChanged(binding.inTitleGroupOrderEnhanceBtn.getRoot(), false);
        ViewTools.setViewVisibilityIfChanged(binding.inGroupOrderCard.getRoot(), false);
        ViewTools.setViewVisibilityIfChanged(binding.flGroupOrder, false);
    }

    private void reportGroupOrderBannerImp(
            @NonNull SellerTopBean seller,
            @NonNull SellerTopBean.GroupOrderBar groupOrderBar
    ) {
        Map<String, Object> ctx = viewModel.getOrderOrderTrackingCtx(
                seller.vendor_id,
                groupOrderBar.count_users,
                groupOrderBar.count_items,
                groupOrderBar.is_creator,
                groupOrderBar.key
        );
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setBanner_key(groupOrderBar.key)
                .setBanner_pos(0)
                .setBanner_type(EagleTrackEvent.TargetNm.SELLER_GROUP_ORDER)
                .setUrl(groupOrderBar.url)
                .addCtx(ctx)
                .build().getParams();
        AppAnalytics.logBannerImp(params);
    }

    @Override
    protected void onResume() {
        super.onResume();
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, getPageKey(),
                String.valueOf(hashCode()));
        String sellerId = getExtraSellerId();
        GlobalMiniCartFragment globalMiniCartFragment = getGlobalMiniCartFragment();
        if (globalMiniCartFragment == null || !globalMiniCartFragment.isExpended()) {
            logPageView();
        }
        if (cartViewModel != null) {
            cartViewModel.getSellerCartFloat(sellerId);
        }
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD, getPageKey(),
                String.valueOf(viewModel.hashCode()));
        loadSellerData();
        ProductSyncHelper.onPageResume(lightningDealsAdapter);
        if (getEmailShow()) {
            viewModel.postUnRead(sellerId);
        }
    }

    private void logPageView() {
        if (getExtraIsFbw()) {
            AppAnalytics.logPageView(WeeeEvent.PageView.FBW_SELLER, this, null, AppAnalytics.putPvPageTarget(null, sellerId));
        } else {
            AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_SELLER, this, null, AppAnalytics.putPvPageTarget(null, sellerId));
        }
    }

    private String getPageKey() {
        if (getExtraIsFbw()) {
            return WeeeEvent.PageView.FBW_SELLER;
        } else {
            return WeeeEvent.PageView.MKPL_SELLER;
        }
    }

    @SuppressLint("NonConstantResourceId")
    private void handleOnClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.iv_back) {
            finish();
        } else if (viewId == R.id.iv_search) {
            String hint = getString(R.string.s_search);
            if (sellerTopBean != null) {
                hint += " " + sellerTopBean.title;
            }
            startActivity(SearchPanelActivity.getIntentBySeller(SellerActivity.this, sellerId, hint));
        } else if (viewId == R.id.iv_share) {
            share();
        } else if (viewId == R.id.btn_follow) {
            followSeller(EagleTrackEvent.TargetType.MKPL_SELLER);
        } else if (viewId == R.id.layout_top_info) {
            if (getExtraIsFbw()) return;
            if (sellerTopBean == null) return;
            EagleTrackManger.get().trackEagleClickAction(
                    /* modNm= */null,
                    /* modPos= */-1,
                    /* secNm= */null,
                    /* secPos= */-1,
                    /* targetNm= */"explore_more",
                    /* targetPos= */0,
                    /* targetType= */EagleTrackEvent.TargetType.MKPL_SELLER,
                    /* clickType= */EagleTrackEvent.ClickType.VIEW,
                    /* ctx= */new EagleContext().setPageTarget(sellerId).asMap()
            );
            startActivity(SellerInfoActivity.getIntent(SellerActivity.this, sellerId));
        } else if (viewId == R.id.layout_goto_top) {
            scrollToTop();
            binding.appBarLayout.postDelayed(
                    () -> binding.appBarLayout.setExpanded(true, false),
                    100
            );
        } else if (viewId == R.id.in_group_order_card) {
            Object tag = view.getTag();
            if (tag instanceof SellerTopBean.GroupOrderBar) {
                SellerTopBean.GroupOrderBar bar = (SellerTopBean.GroupOrderBar) tag;
                Map<String, Object> ctx = viewModel.getOrderOrderTrackingCtx(
                        DecimalTools.parseInt(sellerId), bar.count_users, bar.count_items, bar.is_creator, bar.key
                );
                EagleTrackManger.get().trackEagleClickAction(
                        /* modNm= */null,
                        /* modPos= */-1,
                        /* secNm= */null,
                        /* secPos= */-1,
                        /* targetNm= */EagleTrackEvent.TargetNm.SELLER_GROUP_ORDER,
                        /* targetPos= */0,
                        /* targetType= */EagleTrackEvent.TargetType.BANNER_LINE,
                        /* clickType= */EagleTrackEvent.ClickType.VIEW,
                        /* ctx= */ctx
                );
                startActivity(WebViewActivity.getIntent(SellerActivity.this, bar.url));
                isGroupNeedRefresh = true;
            }
        } else if (viewId == R.id.v_cart) {
            startActivity(CartActivity.getIntent(SellerActivity.this));
        } else if (viewId == R.id.tv_remind_revoke) {
            revokeRemind();
        } else if (viewId == R.id.iv_email) {
            if (AccountManager.get().isLogin()) {
                String url = String.format(Constants.Url.SELLER_ORDER_CASE,
                        "seller", sellerId, bizType != null ? bizType : "seller", sellerId, "");
                startActivity(WebViewActivity.getIntent(activity, AppConfig.HOST_WEB + url));
            } else {
                startActivity(AccountIntentCreator.getIntent(activity));
            }
        }
    }

    private void scrollToTop() {
        Fragment fragment = ViewTools.getViewPager2CurrentFragment(getSupportFragmentManager(), pager2);
        if (fragment instanceof SellerFragment) {
            SellerFragment sellerFragment = (SellerFragment) fragment;
            sellerFragment.scrollTop();
        } else if (fragment instanceof SellerProductFragment) {
            SellerProductFragment sellerProductFragment = (SellerProductFragment) fragment;
            sellerProductFragment.scrollTop();
        } else if (fragment instanceof SellerReviewFragment) {
            SellerReviewFragment reviewFragment = (SellerReviewFragment) fragment;
            reviewFragment.scrollTop();
        }
    }

    public void prepareShowGroupOrderDialog() {
        String sellerId = getExtraSellerId();
        if (EmptyUtils.isEmpty(sellerId)) return;

        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(this));
            return;
        }

        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(sellerId)
                .asMap();
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */EagleTrackEvent.TargetNm.GROUP_ORDER,
                /* targetPos= */-1,
                /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType= */EagleTrackEvent.ClickType.VIEW,
                /* ctx= */ctx
        );
        viewModel.checkGroupOrderExists();
    }

    private void handleSellerGroupStatusLiveData(@Nullable SellerGroupStatusBean response) {
        String currentVendorId = getExtraSellerId();
        if (!EmptyUtils.isEmpty(currentVendorId) && response != null) {
            showGroupOrderPopup(currentVendorId, response);
        }
    }

    private void showGroupOrderPopup(String currentVendorId, SellerGroupStatusBean statusBean) {
        String vendorName = statusBean.vendor_name;
        try {
            vendorName = URLEncoder.encode(statusBean.vendor_name, "UTF-8");
        } catch (Exception ignored) {
        }
        String url = String.format(
                Constants.Url.SELLER_GROUP_ORDER_POPUP,
                statusBean.vendor_id, vendorName, statusBean.status, statusBean.key, currentVendorId
        );
        PopupSlideDialog dialog = new PopupSlideDialog();
        dialog.loadUrl(AppConfig.HOST_WEB + url);
        int dialogHeight = (int) (getResources().getDisplayMetrics().heightPixels * 0.5);
        if (statusBean.status == 3) {
            dialogHeight = CommonTools.dp2px(335);
        } else if (statusBean.status == 2) {
            dialogHeight = CommonTools.dp2px(425);
        } else if (statusBean.status == 1) {
            dialogHeight = CommonTools.dp2px(440);
        }
        dialog.callDialogSize(-1, dialogHeight);
        dialog.setOnDismissListener(d -> {
            if (!isGroupNeedRefresh) {
                isGroupNeedRefresh = true;
            }
        });
        dialog.show();
    }

    private void initAppBarLayout() {
        binding.appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {

            private final int tabHeight = CommonTools.dp2px(42);

            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                int totalScrollRange = appBarLayout.getTotalScrollRange();
                int absVerticalOffset = Math.abs(verticalOffset);

                // show top title
                boolean titleViewVisible = absVerticalOffset > 0.3 * totalScrollRange;
                ViewTools.setViewVisibilityIfChanged(binding.tvTitleTop, titleViewVisible);
                showBottomPromotionBar(absVerticalOffset >= totalScrollRange - tabHeight);
            }
        });
    }

    private void fillSellerTop(@NonNull SellerTopBean sellerTopBean) {
        boolean isFbw = getExtraIsFbw();
        ViewTools.setViewVisibilityIfChanged(findViewById(R.id.v_cart), isFbw);
        ViewTools.setViewVisibilityIfChanged(findViewById(R.id.iv_share), true);
        ViewTools.setViewVisibilityIfChanged(findViewById(R.id.iv_email), getEmailShow());
        ViewTools.setViewVisibilityIfChanged(findViewById(R.id.iv_search), !isFbw);

        ViewTools.setViewVisible(true, findViewById(R.id.layout_top_info));
        binding.tvTitleTop.setText(sellerTopBean.title);
        ImageLoader.load(
                this,
                binding.ivSeller,
                WebpManager.convert(ImageSpec.SPEC_64, sellerTopBean.image_url),
                R.color.color_place
        );
        binding.tvTitle.setText(sellerTopBean.title);
        if (!getExtraIsFbw() && sellerTopBean.isShowFollowBtn()) {
            SellerTopBean.SellerFollowInfo sellerFollowInfo = sellerTopBean.seller_follow_info;
            ViewTools.setViewVisibilityIfChanged(binding.btnFollow.getRoot(), true);
            updateSocialStatus(binding.btnFollow.getRoot(), ivAddFollow, tvFollow, sellerFollowInfo.getFollowStatus());
        } else {
            ViewTools.setViewVisibilityIfChanged(binding.btnFollow.getRoot(), false);
        }

        fillDescriptions(sellerTopBean);
        fillPromotionGroup(sellerTopBean);
        fillBottomPromotions(sellerTopBean);
    }

    private void fillDescriptions(@NonNull SellerTopBean seller) {
        if (getExtraIsFbw()) {
            fillFbwDescriptions(seller);
            return;
        }

        ViewTools.removeAllViews(binding.llSellerDescription);
        Context context = binding.llSellerDescription.getContext();

        List<SellerTopBean.ReminderContent> reminderContents = CollectionUtils.newListOf(seller.reminder_contents);
        SellerTopBean.ReminderContent eta = null;
        Iterator<SellerTopBean.ReminderContent> iterator = reminderContents.iterator();
        while (iterator.hasNext()) {
            SellerTopBean.ReminderContent content = iterator.next();
            if ("estimate_range".equals(content.key)) {
                eta = content;
                iterator.remove();
                break;
            }
        }

        // Ratings & Sales & Eta
        fillRatingSalesEta(context, seller, eta);

        // Reminders
        for (SellerTopBean.ReminderContent content : reminderContents) {
            if (binding.llSellerDescription.getChildCount() != 0) {
                View space = new Space(context);
                space.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, CommonTools.dp2px(12)));
                binding.llSellerDescription.addView(space);
            }
            LayoutSellerReminderContentBinding b;
            b = LayoutSellerReminderContentBinding.inflate(LayoutInflater.from(context), binding.llSellerDescription, false);
            ImageLoader.load(context, b.ivImage, content.iconUrl);
            b.tvContent.setMaxLines("shipping".equals(content.key) ? 2 : 1);
            if (!EmptyUtils.isEmpty(content.titleFull)) {
                b.tvContent.setText(ViewTools.fromHtml(content.titleFull));
            } else if (!EmptyUtils.isEmpty(content.title)) {
                b.tvContent.setText(content.title);
            } else {
                continue;
            }
            binding.llSellerDescription.addView(b.getRoot());
        }

        // Arrow
        ViewTools.setViewVisibilityIfChanged(
                findViewById(R.id.iv_arrow),
                binding.llSellerDescription.getChildCount() != 0
        );

        // Description root
        ViewTools.setViewVisibilityIfChanged(binding.llSellerDescription, binding.llSellerDescription.getChildCount() != 0);
    }

    private void fillRatingSalesEta(@NonNull Context context, @NonNull SellerTopBean seller, @Nullable SellerTopBean.ReminderContent eta) {
        LinearLayout viewGroup = new LinearLayout(context);
        viewGroup.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        viewGroup.setOrientation(LinearLayout.HORIZONTAL);

        // Rating & Sales
        String rating = "";
        if (seller.overall_rating > 0) {
            rating += seller.overall_rating;
        }
        String ratingAndSales = rating;
        String salesVolume = seller.sales_volume != null ? seller.sales_volume.trim() : null;
        if (!EmptyUtils.isEmpty(salesVolume)) {
            if (!EmptyUtils.isEmpty(ratingAndSales)) {
                ratingAndSales += " • ";
            }
            ratingAndSales += salesVolume;
        }
        if (!EmptyUtils.isEmpty(ratingAndSales)) {
            LayoutSellerReminderContentBinding b;
            b = LayoutSellerReminderContentBinding.inflate(LayoutInflater.from(context), binding.llSellerDescription, false);
            b.getRoot().setLayoutParams(new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            ));
            b.ivImage.setImageResource(R.mipmap.ic_seller_rating_star);
            ViewTools.setViewVisibilityIfChanged(b.ivImage, !EmptyUtils.isEmpty(rating));
            b.tvContent.setText(ratingAndSales);
            ViewTools.applyTextStyleAndColor(
                    b.tvContent,
                    R.style.style_body_xs_medium,
                    R.color.color_surface_100_fg_default
            );
            viewGroup.addView(b.getRoot());
        }

        // Eta
        if (eta != null) {
            LayoutSellerReminderContentBinding b;
            b = LayoutSellerReminderContentBinding.inflate(LayoutInflater.from(context), binding.llSellerDescription, false);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
            if (viewGroup.getChildCount() != 0) {
                layoutParams.setMarginStart(CommonTools.dp2px(12));
            }
            b.getRoot().setLayoutParams(layoutParams);
            ImageLoader.load(context, b.ivImage, eta.iconUrl);
            if (!EmptyUtils.isEmpty(eta.titleFull)) {
                b.tvContent.setText(ViewTools.fromHtml(eta.titleFull));
            } else if (!EmptyUtils.isEmpty(eta.title)) {
                b.tvContent.setText(eta.title);
            }
            viewGroup.addView(b.getRoot());
        }

        if (viewGroup.getChildCount() != 0) {
            binding.llSellerDescription.addView(viewGroup);
        }
    }

    private void fillFbwDescriptions(@NonNull SellerTopBean sellerTopBean) {
        ViewTools.removeAllViews(binding.llSellerDescription);

        Context context = binding.llSellerDescription.getContext();

        Object descriptionHtml = sellerTopBean.description_html;
        String description = null;
        if (descriptionHtml instanceof String) {
            description = (String) descriptionHtml;
        }

        if (!EmptyUtils.isEmpty(description)) {
            LayoutSellerReminderContentBinding b;
            b = LayoutSellerReminderContentBinding.inflate(LayoutInflater.from(context), binding.llSellerDescription, false);
            ViewTools.setViewVisibilityIfChanged(b.ivImage, false);
            b.tvContent.setMaxLines(Integer.MAX_VALUE);
            b.tvContent.setText(ViewTools.fromHtml(description));
            binding.llSellerDescription.addView(b.getRoot());
        }

        // Arrow
        ViewTools.setViewVisibilityIfChanged(findViewById(R.id.iv_arrow), false);

        // Description root
        ViewTools.setViewVisibilityIfChanged(binding.llSellerDescription, binding.llSellerDescription.getChildCount() != 0);
    }

    private void fillPromotionGroup(@NonNull SellerTopBean seller) {
        List<SellerTopBean.CouponPromotion> couponList = CollectionUtils.newListOf(seller.coupon_promotions);
        List<SellerTopBean.PromotionBanner> promotionList = CollectionUtils.newListOf(seller.promotion_banners);

        long currentTimestamp = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        long serverTimestamp = seller.server_timestamp;
        Iterator<SellerTopBean.PromotionBanner> promotionIterator = promotionList.iterator();
        while (promotionIterator.hasNext()) {
            SellerTopBean.PromotionBanner promotion = promotionIterator.next();
            if (promotion.end_time - seller.server_timestamp <= 0) {
                promotionIterator.remove();
            }
            if (promotion.isGift() && EmptyUtils.isEmpty(promotion.products)) {
                promotionIterator.remove();
            }
        }

        boolean showCoupons = !EmptyUtils.isEmpty(couponList);
        boolean showPromotions = !EmptyUtils.isEmpty(promotionList);

        boolean anyShow = showCoupons || showPromotions;
        ViewGroup rootView = findViewById(R.id.layout_seller_promotion_group);
        if (rootView == null) return;
        if (anyShow) {
            // coupons
            fillCoupons(layoutCoupons, couponList, currentTimestamp, serverTimestamp);
            ViewTools.setViewVisibilityIfChanged(layoutCoupons, layoutCoupons.getChildCount() > 0);

            // promotions
            fillPromotions(layoutPromotions, promotionList, currentTimestamp, serverTimestamp);
            LinearLayout.LayoutParams lpPromotions = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
            lpPromotions.topMargin = ViewTools.isViewVisible(layoutCoupons) ? CommonTools.dp2px(5) : 0;
            layoutPromotions.setLayoutParams(lpPromotions);
            ViewTools.setViewVisibilityIfChanged(layoutPromotions, layoutPromotions.getChildCount() > 0);

            ViewTools.setViewVisibilityIfChanged(rootView, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(rootView, false);
        }
    }

    private void fillCoupons(
            @NonNull ViewGroup parentView,
            List<SellerTopBean.CouponPromotion> couponList,
            long currentTimestamp,
            long serverTimestamp
    ) {
        ViewTools.removeAllViews(parentView);
        int targetPosition = 0;
        for (SellerTopBean.CouponPromotion promotion : couponList) {
            SellerCouponView childView = new SellerCouponView(this);
            childView.bind(promotion, currentTimestamp, serverTimestamp);
            childView.setTargetPosition(targetPosition);
            childView.setOnSellerCouponViewActionListener(new SellerCouponView.OnSellerCouponViewActionListener() {

                @Override
                public void onSellerCouponViewClick(
                        @NonNull SellerCouponView view,
                        @Nullable SellerTopBean.CouponPromotion coupon,
                        int position
                ) {
                    gotoWebViewActivity(view.getContext(), coupon != null ? coupon.useUrl : null);
                }

                @Override
                public void onSellerCouponViewClaim(
                        @NonNull SellerCouponView view,
                        @Nullable SellerTopBean.CouponPromotion coupon,
                        int position
                ) {
                    reportCouponClaim(coupon, position);
                    claimSellerCoupon(coupon, position);
                }
            });
            parentView.addView(childView);
            reportCouponImpression(promotion, targetPosition);
            targetPosition++;
        }
    }

    private void claimSellerCoupon(@Nullable SellerTopBean.CouponPromotion coupon, int targetPosition) {
        if (!AccountManager.get().isLogin()) {
            isGroupNeedRefresh = true;
            startActivity(AccountIntentCreator.getIntent(SellerActivity.this));
            return;
        }

        if (coupon == null || !coupon.isCouponPlan()) return;
        int couponPlanId = coupon.id;
        CouponClaimRequest request = new CouponClaimRequest(sellerId, couponPlanId, coupon.isAutoFollow);
        request.setTargetPosition(targetPosition);
        viewModel.claimSellerCoupon(request);
    }

    private void handleSellerCouponClaimResponse(@Nullable CouponClaimResponse response) {
        if (response == null) return;
        if (!response.isSuccess()) {
            loadData();
            viewModel.getSellerTop(getExtraSellerId(), getExtraBizType());
            return;
        }

        CouponClaimBean data = response.requireResponse();
        int currentTimestamp = (int) TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        for (int i = 0; i < layoutCoupons.getChildCount(); i++) {
            View childView = layoutCoupons.getChildAt(i);
            if (childView instanceof SellerCouponView) {
                ((SellerCouponView) childView).update(data, currentTimestamp, data.server_timestamp);
            }
        }
        SellerTopBean.CouponPromotion coupon = new SellerTopBean.CouponPromotion();
        coupon.type = SellerTopBean.CouponPromotion.TYPE_COUPON;
        coupon.id = data.coupon_id;
        coupon.endTime = data.apply_expiration_time;
        reportCouponImpression(coupon, response.getRequest().getTargetPosition());

        boolean isAutoFollow = response.getRequest().isAutoFollow();
        if (!isAutoFollow && !EmptyUtils.isEmpty(data.message)) {
            showToast(data.message);
        }

        SellerTopBean sellerTopBean = this.sellerTopBean;
        if (isAutoFollow
                && sellerTopBean != null
                && sellerTopBean.seller_follow_info != null
                && sellerTopBean.seller_follow_info.isUnFollow()) {
            binding.getRoot().postDelayed(
                    () -> showFollowDialogAfterCouponClaimed(sellerTopBean.title),
                    1000L
            );
        }
    }

    private void reportCouponImpression(@NonNull SellerTopBean.CouponPromotion coupon, int targetPosition) {
        Map<String, Object> ctx = new EagleContext().setPageTarget(getExtraSellerId()).asMap();
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.SELLER_TOP_MESSAGE)
                .setMod_pos(0)
                .setBanner_id(coupon.id)
                .setBanner_key(coupon.type)
                .setBanner_pos(targetPosition)
                .setBanner_type(EagleTrackEvent.BannerType.BANNER_LINE)
                .addCtx(ctx)
                .build()
                .getParams();
        AppAnalytics.logBannerImp(params);
    }

    private void reportCouponClaim(@Nullable SellerTopBean.CouponPromotion coupon, int targetPosition) {
        if (coupon == null) return;
        Map<String, Object> ctx = new EagleContext().setPageTarget(getExtraSellerId()).asMap();
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.SELLER_TOP_MESSAGE)
                .setMod_pos(0)
                .setTargetType(coupon.type)
                .setTargetNm(String.valueOf(coupon.id))
                .setTargetPos(targetPosition)
                .setClickType(EagleTrackEvent.ClickType.NORMAL)
                .addCtx(ctx)
                .build()
                .getParams();
        AppAnalytics.logClickAction(params);
    }

    private void fillPromotions(
            @NonNull ViewGroup parentView,
            List<SellerTopBean.PromotionBanner> promotionList,
            long currentTimestamp,
            long serverTimestamp
    ) {
        ViewTools.removeAllViews(parentView);
        int targetPosition = 0;
        for (SellerTopBean.PromotionBanner promotion : promotionList) {
            if (parentView.getChildCount() != 0) {
                Space spaceView = new Space(parentView.getContext());
                ViewGroup.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        CommonTools.dp2px(5f)
                );
                spaceView.setLayoutParams(layoutParams);
                parentView.addView(spaceView);
            }

            if (promotion.isGift()) {
                fillGiftPromotionView(parentView, promotion, targetPosition);
            } else {
                fillNormalPromotionView(parentView, promotion, currentTimestamp, serverTimestamp);
            }
            reportPromotionImpression(promotion, targetPosition);
            targetPosition++;
        }
    }

    private void fillNormalPromotionView(
            @NonNull ViewGroup parentView,
            @NonNull SellerTopBean.PromotionBanner promotion,
            long currentTimestamp,
            long serverTimestamp
    ) {
        long timeInterval = promotion.end_time - serverTimestamp;
        if (timeInterval <= 0) {
            return; // skip expired
        }

        OnViewHelper viewHelper = helper -> {
            helper.setText(R.id.tv_content, promotion.promote_title);
            boolean isShow = timeInterval < 60 * 60 * 24;
            if (isShow) {
                final TimerTextView tvTimer = helper.getView(R.id.tv_timer);
                tvTimer.start(timeInterval + currentTimestamp);
                tvTimer.setOnTimerListener(new OnSimpleTimerListener() {
                    @Override
                    public void onEnd() {
                        // 刷新页面
                        loadData();
                        viewModel.getSellerTop(sellerId, getExtraBizType());
                    }
                });
            }
            ViewTools.setViewVisible(isShow, helper.getView(R.id.tv_timer));
            ViewTools.setViewOnSafeClickListener(
                    helper.getView(R.id.layout),
                    v -> gotoWebViewActivity(v.getContext(), promotion.use_url)
            );
        };

        View childView = ViewTools.getHelperView(parentView, R.layout.item_seller_promotion, viewHelper);
        parentView.addView(childView);
    }

    private void fillGiftPromotionView(
            @NonNull ViewGroup parentView,
            @NonNull SellerTopBean.PromotionBanner promotion,
            int targetPosition
    ) {
        SellerTopBean.PromotionBanner.Product product = CollectionUtils.firstOrNull(promotion.products);
        if (product == null) {
            return;
        }

        ItemSellerPromotionGiftBinding b;
        b = ItemSellerPromotionGiftBinding.inflate(LayoutInflater.from(parentView.getContext()), parentView, false);

        Spanny title = new Spanny(promotion.promote_title);
        Drawable infoDrawable = ResourcesCompat.getDrawable(parentView.getResources(), R.mipmap.ic_info_filled_20x20, null);
        if (infoDrawable != null) {
            title.append(" ");
            infoDrawable.setBounds(0, 0, CommonTools.dp2px(10), CommonTools.dp2px(10));
            infoDrawable.setTint(ResourcesCompat.getColor(parentView.getResources(), R.color.color_surface_1_fg_subtle_idle, null));
            title.append("", new CenterImageSpan(infoDrawable));
        }
        b.tvContent.setText(title);

        ImageLoader.load(
                parentView.getContext(),
                b.ivPromotionProduct,
                WebpManager.convert(ImageSpec.SPEC_PRODUCT, product.image_url)
        );

        double price = product.price;
        double basePrice = product.base_price;
        if (price <= 0) {
            b.tvPromotionPrice.setText(R.string.s_free);
        } else {
            b.tvPromotionPrice.setText(OrderHelper.formatUSMoney(price));
        }
        if (basePrice != price) {
            b.tvPromotionBasePrice.setText(new Spanny(OrderHelper.formatUSMoney(basePrice), new StrikethroughSpan()));
        } else {
            b.tvPromotionBasePrice.setText(null);
        }

        String linkUrl = promotion.use_url;
        ViewTools.setViewOnSafeClickListener(b.getRoot(), v -> {
            reportPromotionClick(promotion, targetPosition);
            gotoWebViewActivity(v.getContext(), linkUrl);
        });

        parentView.addView(b.getRoot());
    }

    private void reportPromotionImpression(@NonNull SellerTopBean.PromotionBanner promotion, int targetPosition) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, sellerId, null);
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.SELLER_TOP_MESSAGE)
                .setMod_pos(0)
                .setBanner_id(promotion.id)
                .setBanner_key(promotion.type)
                .setBanner_pos(targetPosition)
                .setBanner_type(EagleTrackEvent.BannerType.BANNER_LINE)
                .addCtx(ctx)
                .build().getParams();
        AppAnalytics.logBannerImp(params);
    }

    private void reportPromotionClick(@NonNull SellerTopBean.PromotionBanner promotion, int targetPosition) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, sellerId, null);
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.SELLER_TOP_MESSAGE)
                .setMod_pos(0)
                .setTargetNm(String.valueOf(promotion.id))
                .setTargetPos(targetPosition)
                .setTargetType(promotion.type)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .addCtx(ctx)
                .build()
                .getParams();
        AppAnalytics.logClickAction(params);
    }

    private void fillBottomPromotions(@NonNull SellerTopBean sellerTopBean) {
        LayoutSellerBottomPromotionBarBinding clPromotionBar = binding.clPromotionBar;
        clPromotionBar.tvPromotionContent.setText(null);
        ViewTools.setViewOnSafeClickListener(clPromotionBar.getRoot(), v -> {
        });

        List<SellerTopBean.RichInfo> richInfoList = new ArrayList<>();
        if (sellerTopBean.shipping_rich_info != null)
            richInfoList.add(sellerTopBean.shipping_rich_info);
        if (sellerTopBean.discount_rich_info != null)
            richInfoList.add(sellerTopBean.discount_rich_info);
        if (sellerTopBean.promotion_rich_info != null)
            richInfoList.addAll(sellerTopBean.promotion_rich_info);

        ArrayList<SellerPromotionDetailBean> promotions = new ArrayList<>();
        CollectionUtils.mapNotNullTo(
                /* destination= */promotions,
                /* iterable= */richInfoList,
                /* transform= */richInfo -> {
                    SellerPromotionDetailBean result;
                    result = new SellerPromotionDetailBean();
                    result.setTitle(richInfo.getText());
                    result.setIconResourceId(R.mipmap.ic_tag_filled_20x20);
                    result.setMoreLink(richInfo.use_url);
                    return result;
                }
        );

        if (EmptyUtils.isEmpty(promotions)) return;

        final Spanny content = new Spanny();
        AppHtmlSpanner spanner = new AppHtmlSpanner();
        int index = 0;
        for (SellerPromotionDetailBean item : promotions) {
            if (index != 0) {
                content.append(
                        " • ",
                        Spans.textAppearanceSpan(this, R.style.style_fluid_root_utility_sm)
                );
            }
            String text = !EmptyUtils.isEmpty(item.getTitle()) ? item.getTitle().trim() : null;
            if (!EmptyUtils.isEmpty(text)) {
                content.append(
                        spanner.fromHtml(text),
                        Spans.textAppearanceSpan(this, R.style.style_fluid_root_utility_sm)
                );
                index++;
            }
        }
        clPromotionBar.tvPromotionContent.setText(index != 0 ? content : null);

        if (promotions.size() == 1) {
            SellerPromotionDetailBean firstPromotion = promotions.get(0);
            if (!EmptyUtils.isEmpty(firstPromotion.getMoreLink())) {
                clPromotionBar.tvPromotionContent.setEllipsize(null);
                clPromotionBar.groupPromotionArrow.setVisibility(View.VISIBLE);
                ViewTools.setViewOnSafeClickListener(
                        clPromotionBar.getRoot(),
                        v -> {
                            reportBottomPromotionBarClick();
                            gotoWebViewActivity(v.getContext(), firstPromotion.getMoreLink());
                        }
                );
            } else {
                clPromotionBar.tvPromotionContent.setEllipsize(TextUtils.TruncateAt.END);
                clPromotionBar.groupPromotionArrow.setVisibility(View.GONE);
            }
        } else {
            clPromotionBar.tvPromotionContent.setEllipsize(null);
            clPromotionBar.groupPromotionArrow.setVisibility(View.VISIBLE);
            ViewTools.setViewOnSafeClickListener(
                    clPromotionBar.getRoot(),
                    v -> {
                        reportBottomPromotionBarClick();
                        showSellerPromotionDetailFragment(promotions);
                    }
            );
            clPromotionBar.tvPromotionAction.setVisibility(View.GONE);
        }
    }

    private void reportBottomPromotionBarClick() {
        Map<String, Object> ctx = new EagleContext().setPageTarget(getExtraSellerId()).asMap();
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm("seller_bottom_message")
                .setMod_pos(-1)
                .setTargetNm(null)
                .setTargetType(null)
                .setTargetPos(0)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .addCtx(ctx)
                .build()
                .getParams();
        AppAnalytics.logClickAction(params);
    }

    @Nullable
    private SellerPromotionDetailFragment getSellerPromotionDetailFragment() {
        String fragmentTag = SellerPromotionDetailFragment.TAG;
        return (SellerPromotionDetailFragment) getSupportFragmentManager().findFragmentByTag(fragmentTag);
    }

    private void showSellerPromotionDetailFragment(@NonNull ArrayList<SellerPromotionDetailBean> promotions) {
        SellerPromotionDetailFragment fragment;
        fragment = getSellerPromotionDetailFragment();
        if (fragment != null) {
            return;
        }

        fragment = SellerPromotionDetailFragment.newInstance(promotions);
        fragment.show(getSupportFragmentManager(), SellerPromotionDetailFragment.TAG);
    }

    private void showBottomPromotionBar(boolean isVisible) {
        if (!isVisible) {
            ViewTools.setViewVisibilityIfChanged(binding.llPromotionBar, false);
            return;
        }
        boolean isShow;
        isShow = !EmptyUtils.isEmpty(binding.clPromotionBar.tvPromotionContent.getText());
        ViewTools.setViewVisibilityIfChanged(binding.llPromotionBar, isShow);
    }

    private void handleLightningDeals(@Nullable CmsLightingDealsData lightningDealsData) {
        List<AdapterDataType> adapterData = new ArrayList<>();
        boolean hasLightningDeals = lightningDealsData != null && lightningDealsData.isValid();
        if (hasLightningDeals) {
            adapterData.add(lightningDealsData);
        }
        boolean hasTabs = CollectionUtils.size(tabEntities) > 1;
        if (hasLightningDeals && !hasTabs) {
            // add all products title
            SimpleTitleData titleData = new SimpleTitleData();
            titleData.setTitle(getString(R.string.s_all_products));
            adapterData.add(titleData);
        }
        ViewTools.setViewVisibilityIfChanged(binding.rcvLightningDeals, !CollectionUtils.isEmpty(adapterData));
        lightningDealsAdapter.setAdapterData(adapterData);
    }

    private void fillPager(@NonNull SellerTopBean seller) {
        boolean showExplore = !getExtraIsFbw() && seller.isShowExplore();
        boolean showReviews = !getExtraIsFbw() && seller.isShowReviews();

        tabEntities.clear();
        String bizType = getExtraBizType();
        if (showExplore) {
            MPagerEntity explorePager = new MPagerEntity(
                    getString(R.string.s_explore_seller),
                    SellerFragment.newInstance(sellerId, bizType)
            );
            explorePager.data = "explore";
            tabEntities.add(explorePager);
        }

        MPagerEntity allPager = new MPagerEntity(
                getString(R.string.s_all_products),
                SellerProductFragment.newInstance(sellerId, bizType)
        );
        allPager.data = "all_products";
        tabEntities.add(allPager);

        if (showReviews) {
            MPagerEntity reviewPager = new MPagerEntity(
                    getString(R.string.s_seller_reviews),
                    SellerReviewFragment.newInstance(sellerId, bizType)
            );
            reviewPager.data = "reviews";
            tabEntities.add(reviewPager);
        }

        pager2.setAdapter(new MPagerViewAdapter(this, tabEntities));

        ViewTools.setViewVisible(false, findViewById(R.id.layout_empty));
        CommonNavigator navigator = new CommonNavigator(SellerActivity.this);
        navigator.setAdjustMode(true);
        navigator.setFollowTouch(true);
        MPagerTabAdapter indicatorAdapter = new MPagerTabAdapter(tabEntities) {

            @Override
            public IPagerIndicator getIndicator(Context context) {
                LinePagerIndicator indicator = new LinePagerIndicator(context);
                indicator.setLineHeight(CommonTools.dp2px(2f));
                indicator.setRoundRadius(0);
                indicator.setXOffset(CommonTools.dp2px(12));
                indicator.setMode(LinePagerIndicator.MODE_MATCH_EDGE);
                indicator.setColors(ViewTools.getColorByResId(context, R.color.color_navbar_fg_default));
                return indicator;
            }

            @Override
            protected int getTitleTextStyle() {
                return R.style.style_body_sm_medium;
            }

            @Override
            protected Drawable getSelectedBackground(@NonNull View view) {
                return null;
            }
        };
        indicatorAdapter.setOnClickListener((index, entity) -> {
            pager2.setCurrentItem(index, false);
            String tabType = (String) entity.data;
            Map<String, Object> ctx = new EagleContext().setPageTarget(sellerId).setGlobalVendor(sellerId).asMap();
            EagleTrackManger.get().trackEagleClickAction(
                    /* modNm= */EagleTrackEvent.ModNm.FILTER_TABS,
                    /* modPos= */-1,
                    /* secNm= */null,
                    /* secPos= */-1,
                    /* targetNm= */tabType,
                    /* targetPos= */index,
                    /* targetType= */EagleTrackEvent.TargetType.FILTER_BUTTON,
                    /* clickType= */EagleTrackEvent.ClickType.VIEW,
                    /* ctx= */ctx
            );
        });
        navigator.setAdapter(indicatorAdapter);
        indicator.setNavigator(navigator);
        indicator.bindSafely(pager2);
        if ("all".equalsIgnoreCase(tabByUrl)) {
            showVeilTemplated(false);
            int index = tabEntities.indexOf(allPager);
            pager2.setCurrentItem(index, false);
        } else if (!showExplore) {
            showVeilTemplated(false);
        }

        ViewTools.setViewVisibilityIfChanged(binding.layoutIndicator, tabEntities.size() > 1);
    }

    private void fillEmpty() {
        ViewTools.setViewVisible(true, findViewById(R.id.layout_empty));
        showVeilTemplated(false);
    }

    public void showVeilTemplated(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_seller);
        if (vl != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.INVISIBLE);
                vl.unVeil();
                // check show mini cart from url params
                showGlobalMiniCart();
            }
        }
    }

    private boolean isVeilShowing() {
        VeilLayout vl = findViewById(R.id.vl_seller);
        if (vl != null) {
            return vl.getVisibility() == View.VISIBLE;
        }
        return false;
    }

    public void updateContentFirstVisiblePosition(int firstVisiblePosition) {
        if (firstVisiblePosition >= 0) {
            ViewTools.setViewVisibilityIfChanged(btnGotoTop, firstVisiblePosition > 3);
        }
    }

    public void share() {
        boolean isFbw = getExtraIsFbw();
        String targetType = isFbw ? EagleTrackEvent.TargetType.FBW_SELLER : EagleTrackEvent.TargetType.MKPL_SELLER;
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */sellerId,
                /* targetPos= */0,
                /* targetType= */targetType,
                /* clickType= */EagleTrackEvent.ClickType.SHARE,
                /* ctx= */(Map<String, Object>) null
        );

        Map<String, String> params = new ArrayMap<>();
        params.put("vendor_id", sellerId);
        if (getExtraIsFbw()) {
            params.put("biz_type", "fbw");
        } else {
            params.put("tab", pager2.getCurrentItem() == 0 ? "explore" : "all");
        }
        if (!EmptyUtils.isEmpty(filter)) {
            params.put("filters", filter);
        }
        if (!EmptyUtils.isEmpty(sort)) {
            params.put("sort", sort);
        }
        viewModel.getShareData(params);
    }

    private void loadSellerData() {
        if (isGroupNeedRefresh) {
            viewModel.getSellerCombinedData(sellerId, getExtraBizType());
        }
    }

    public void handleUrl(String url) {
        if (!EmptyUtils.isEmpty(url)) {
            Map<String, String> paramsByUrl = CommonTools.parseQueryParams(url, true);
            tabByUrl = paramsByUrl.get("tab");
            filtersByUrl = paramsByUrl.get(Constants.UrlMapParams.FILTERS);
            sortByUrl = paramsByUrl.get(Constants.UrlMapParams.SORT);
            // explore to see all
            if ("all".equalsIgnoreCase(tabByUrl)) {
                pager2.setCurrentItem(1, false);
            }
            Fragment fragment = ViewTools.getViewPager2CurrentFragment(getSupportFragmentManager(), pager2);
            if (fragment instanceof SellerProductFragment) {
                SellerProductFragment sellerProductFragment = (SellerProductFragment) fragment;
                sellerProductFragment.handleUrl();
            }
        }
    }

    private static void gotoWebViewActivity(@Nullable Context context, @Nullable String linkUrl) {
        if (context != null && !EmptyUtils.isEmpty(linkUrl)) {
            context.startActivity(WebViewActivity.getIntent(context, linkUrl));
        }
    }

    private void handleGlobalCartListResponse(@Nullable GlobalCartListResponse response) {
        if (response == null) return;
        if (!EmptyUtils.isEmpty(response.getRequest().getToken())) return;
        if (!response.isSuccess(false)) return;
        GlobalCartListBean cartListBean = response.getResponse();
        if (cartListBean == null || cartListBean.isEmpty()) {
            hideGlobalMiniCart();
        } else {
            showGlobalMiniCart(cartListBean.getGlobalCartBean(getExtraSellerId()));
        }
    }

    @NonNull
    private FragmentManager getGlobalCartFragmentManager() {
        return getSupportFragmentManager();
    }

    @Nullable
    private GlobalMiniCartFragment getGlobalMiniCartFragment() {
        String fragmentTag = GlobalMiniCartFragment.getFragmentTag(sellerId);
        return (GlobalMiniCartFragment) getGlobalCartFragmentManager().findFragmentByTag(fragmentTag);
    }

    public boolean hasGlobalMiniCart() {
        return getGlobalMiniCartFragment() != null;
    }

    private void hideGlobalMiniCart() {
        String sellerId = this.sellerId;
        if (EmptyUtils.isEmpty(sellerId)) return;

        GlobalMiniCartFragment fragment = getGlobalMiniCartFragment();
        if (fragment != null) {
            fragment.dismissCart();
        }
    }

    private void showGlobalMiniCart() {
        SellerPageParams pageParams = getIntent().getParcelableExtra(SellerPageParams.EXTRA_SELLER_PARAMS);
        if (pageParams != null) {
            if (pageParams.openCart == 1) {
                new Handler().postDelayed(() -> {
                    GlobalMiniCartFragment fragment = getGlobalMiniCartFragment();
                    if (fragment != null) {
                        fragment.showCartInitial();
                    }
                }, 800);
            } else if (!TextUtils.isEmpty(pageParams.toastMsg)) {
                showToast(pageParams.toastMsg);
            }
        }

    }

    private void showGlobalMiniCart(@Nullable GlobalCartBean cart) {
        String sellerId = this.sellerId;
        if (EmptyUtils.isEmpty(sellerId)) return;

        GlobalMiniCartFragment fragment = getGlobalMiniCartFragment();
        if (fragment == null) {
            SellerPageParams sellerParams = getIntent().getParcelableExtra(SellerPageParams.EXTRA_SELLER_PARAMS);
            fragment = GlobalMiniCartFragment.getFragment(sellerId, cart, sellerParams);
            getGlobalCartFragmentManager().beginTransaction()
                    .setCustomAnimations(GlobalMiniCartFragment.getEnterAnim(), GlobalMiniCartFragment.getExitAnim())
                    .add(R.id.fragment_container, fragment, GlobalMiniCartFragment.getFragmentTag(sellerId))
                    .commitNowAllowingStateLoss();
        } else {
            fragment.updateCartData(cart);
        }
    }

    private void showToast(@NonNull String title) {
        ToastySnackBarView snackBarView = new ToastySnackBarView(this);
        snackBarView.convert(new ActionSnackBarData(title));
        Toaster.asSnackBar(binding.clFloatingViews)
                .setView(snackBarView)
                .setOptions(new SnackBarOptions.Builder().duration(TimeUnit.SECONDS.toMillis(5)).build())
                .build()
                .show(this);
    }

    public void resetRemindTipsParams(boolean hasMiniCart) {
        int marginBottom = hasMiniCart ? CommonTools.dp2px(108) : CommonTools.dp2px(38);
        ViewGroup.LayoutParams lp = binding.layoutRemindTips.getRoot().getLayoutParams();
        if (lp instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) lp;
            if (layoutParams.bottomMargin != marginBottom) {
                layoutParams.bottomMargin = marginBottom;
                binding.layoutRemindTips.getRoot().postDelayed(
                        () -> binding.layoutRemindTips.getRoot().setLayoutParams(layoutParams),
                        20
                );
            }
        }
    }

    public void showRemindTips(int id) {
        resetRemindTipsParams(hasGlobalMiniCart());
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().postDelayed(hideRemindTipsRunnable, 3000);
        binding.layoutRemindTips.getRoot().setTag(id);
        binding.layoutRemindTips.getRoot().setVisibility(View.VISIBLE);
    }

    public void setRemindEquals(int id) {
        if (binding.layoutRemindTips.getRoot().getVisibility() == View.VISIBLE) {
            Object tag = binding.layoutRemindTips.getRoot().getTag();
            if (tag instanceof Integer) {
                if ((Integer) tag == id) {
                    hideRemindTips();
                }
            }
        }
    }

    private void revokeRemind() {
        Object tag = binding.layoutRemindTips.getRoot().getTag();
        if (tag instanceof Integer) {
            try {
                int productId = (int) tag;
                if (pager2.getAdapter() instanceof MPagerViewAdapter) {
                    Fragment fragment = tabEntities.get(pager2.getCurrentItem()).fragment;
                    if (fragment instanceof SellerFragment) {
                        ((SellerFragment) fragment).revokeRemind((Integer) tag);
                    }
                }
                if (lightningDealsAdapter != null) {
                    lightningDealsAdapter.revokeRemind(productId);
                    viewModel.changeLightningDealsRemind(productId, false);
                }
            } catch (Exception ignored) {
                // do nothing
            }
            hideRemindTips();
        }
    }

    private void hideRemindTips() {
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().setVisibility(View.GONE);
    }

    private final Runnable hideRemindTipsRunnable = this::hideRemindTips;

    private void refreshRemindSet(Map<String, Object> map) {
        Object productId = CollectionUtils.getOrNull(map, "product_id");
        Object isRemind = CollectionUtils.getOrNull(map, "isRemind");
        if (!(productId instanceof Integer) || !(isRemind instanceof Boolean)) {
            return;
        }
        try {
            if (lightningDealsAdapter != null) {
                lightningDealsAdapter.refreshRemindSet((int) productId, (boolean) isRemind);
            }
            if (pager2.getAdapter() instanceof MPagerViewAdapter) {
                Fragment fragment = tabEntities.get(pager2.getCurrentItem()).fragment;
                if (fragment instanceof SellerFragment) {
                    ((SellerFragment) fragment).refreshRemindSet((int) productId, (boolean) isRemind);
                }
            }
        } catch (Exception ignored) {
        }
    }

    private void followSeller(String targetType) {
        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(SellerActivity.this));
            return;
        }

        if (sellerId == null || sellerTopBean == null) return;
        SellerTopBean.SellerFollowInfo sellerFollowInfo = sellerTopBean.seller_follow_info;
        if (sellerFollowInfo == null) return;

        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */EagleTrackEvent.TargetNm.FOLLOW,
                /* targetPos= */0,
                /* targetType= */targetType,
                /* clickType= */sellerFollowInfo.isUnFollow() ? EagleTrackEvent.ClickType.FOLLOW : EagleTrackEvent.ClickType.UNFOLLOW,
                /* ctx= */new EagleContext().setPageTarget(sellerId).asMap()
        );

        if (sellerFollowInfo.isUnFollow()) {
            sellerFollowInfo.status = "A";
            viewModel.followSeller(sellerId, sellerFollowInfo.status);
            updateSocialStatus(binding.btnFollow.getRoot(), ivAddFollow, tvFollow, sellerFollowInfo.getFollowStatus());
            showToast(getString(R.string.s_mkpl_follow_store_message));
        } else {
            unFollowSeller(sellerId, sellerFollowInfo);
        }
    }

    private void unFollowSeller(@NonNull String sellerId, @NonNull SellerTopBean.SellerFollowInfo sellerFollowInfo) {
        new CompatDialog(activity) {

            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_mkpl_unfollow_seller;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                int width = CommonTools.getWindowWidth(context) - CommonTools.dp2px(20);
                setDialogParams(dialog, width, ViewGroup.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
            }

            @Override
            public void help(ViewHelper viewHelper) {
                DialogMkplUnfollowSellerBinding b;
                b = DialogMkplUnfollowSellerBinding.bind(viewHelper.itemView);
                ViewTools.setTextFontWeight(b.tvTitle, 500);
                ViewTools.setViewOnSafeClickListener(b.ivClose, v -> dialog.dismiss());
                ViewTools.setViewOnSafeClickListener(b.tvCancel, v -> dialog.dismiss());
                ViewTools.setViewOnSafeClickListener(b.tvConfirm, v -> {
                    dialog.dismiss();
                    sellerFollowInfo.status = "C";
                    viewModel.followSeller(sellerId, sellerFollowInfo.status);
                    updateSocialStatus(binding.btnFollow.getRoot(), ivAddFollow, tvFollow, sellerFollowInfo.getFollowStatus());
                });
            }
        }.show();
    }

    private static void updateSocialStatus(View rootView, ImageView ivFollow, ShapeTextView stvFollow, String followStatus) {
        if (SocialStatusHelper.isFollowed(followStatus)) {
            ivFollow.setImageResource(R.mipmap.ic_follow_check);
            ViewTools.setViewVisibilityIfChanged(ivFollow, true);
            stvFollow.setText(R.string.s_review_following);
            ViewTools.applyTextColor(stvFollow, R.color.color_tertiary_surface_1_fg_default_idle);
            rootView.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else if (SocialStatusHelper.isUnFollow(followStatus)) {
            ivFollow.setImageResource(R.mipmap.pic_add_follow);
            ViewTools.setViewVisibilityIfChanged(ivFollow, true);
            stvFollow.setText(R.string.follow);
            ViewTools.applyTextColor(stvFollow, R.color.color_tertiary_surface_1_fg_default_idle);
            rootView.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else {
            ViewTools.setViewVisibilityIfChanged(ivFollow, false);
            stvFollow.setText(R.string.follow_back);
            stvFollow.setTextColor(Color.WHITE);
            rootView.setBackgroundResource(R.drawable.bg_profile_008ed6_bg_shape);
        }
    }

    private void showFollowDialogAfterCouponClaimed(String vendorName) {
        String storeName = vendorName != null ? vendorName : "";
        new BottomDialog(activity) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_mkpl_coupon_claimed_follow_seller;
            }
        }.addHelperCallback((Dialog dialog, ViewHelper viewHelper) -> {
            DialogMkplCouponClaimedFollowSellerBinding binding;
            binding = DialogMkplCouponClaimedFollowSellerBinding.bind(viewHelper.itemView);
            binding.tvContent.setText(getString(R.string.s_mkpl_follow_store_title, storeName));
            ViewTools.setViewOnSafeClickListener(binding.ivCancel, v -> dialog.dismiss());
            ViewTools.setViewOnSafeClickListener(binding.btnFollow, v -> {
                followSeller(EagleTrackEvent.TargetType.POPUP);
                dialog.dismiss();
            });
        }).show();
    }

    private boolean getEmailShow() {
        boolean emailVisible = false;
        Object obj = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.SELLER);
        if (obj instanceof SellerConfigBean) {
            emailVisible = ((SellerConfigBean) obj).show_contact_button;
        }
        return emailVisible;
    }
}
