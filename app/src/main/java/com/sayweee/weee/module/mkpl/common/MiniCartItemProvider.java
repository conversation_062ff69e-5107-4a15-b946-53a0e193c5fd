package com.sayweee.weee.module.mkpl.common;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ItemMkplCartProductBinding;
import com.sayweee.weee.databinding.ItemSectionCartProductActivityItemBinding;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.bean.EntranceTag;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.TagInfo;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.mkpl.GlobalOnCartEditListener;
import com.sayweee.weee.module.mkpl.bean.GlobalMiniCartAction;
import com.sayweee.weee.module.post.base.adapter.BindingAdapterViewHolder;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.SpannyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.ExCartOpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.util.List;
import java.util.Map;
import java.util.Objects;

//
// Created by Thomsen on 12/07/2023.
// Copyright (c) 2023 Weee LLC. All rights reserved.
//
public class MiniCartItemProvider extends SimpleSectionProvider<MiniCartItemData, AdapterViewHolder> {

    @Override
    public int getItemType() {
        return R.layout.item_mkpl_cart_product;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_mkpl_cart_product;
    }

    @NonNull
    protected final GlobalOnCartEditListener onCartEditListener;

    public MiniCartItemProvider(@NonNull GlobalOnCartEditListener onCartEditListener) {
        this.onCartEditListener = onCartEditListener;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        helper.addOnClickListener(R.id.layout_cart_item_root);

        BindingAdapterViewHolder.getBindingOrCreate(
                helper,
                () -> ItemMkplCartProductBinding.bind(helper.itemView)
        );
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
    }

    @Override
    public void convert(AdapterViewHolder helper, MiniCartItemData item) {
        NewItemBean bean = item.t;
        ItemMkplCartProductBinding binding;
        binding = BindingAdapterViewHolder.getBinding(helper);
        if (binding == null) return;

        ImageLoader.load(
                context,
                binding.ivIcon,
                WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.img),
                R.mipmap.iv_product_placeholder
        );

        Spanny nameBuilder = new Spanny();
        if (bean.is_gift) {
            SpannyUtils.buildStartTagWithSm(
                    /* context = */context,
                    /* builder = */nameBuilder,
                    /* tag = */context.getString(R.string.s_gift),
                    /* textColor = */Color.WHITE,
                    /* tagDrawable = */ShapeHelper.buildSolidDrawable(ContextCompat.getColor(context,
                            R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)),
                    9, 0, 6
            );
        }
        TagInfo alcoholTag = CollectionUtils.firstOrNull(bean.tag_infos, tagInfo -> "alcohol".equalsIgnoreCase(tagInfo.tag_type));
        if (alcoholTag != null) {
            Drawable drawable = AlcoholHelper.getAlcoholDrawable(context);
            nameBuilder.append(" ", new CenterImageSpan(drawable));
        }
        nameBuilder.append(bean.title, new TextAppearanceSpan(context, R.style.style_fluid_root_card_label_sm));
        binding.tvName.setText(nameBuilder);

        if (bean.is_gift) {
            binding.tvRemove.setVisibility(View.GONE);
            binding.vDividerFull.setVisibility(View.INVISIBLE);
        } else {
            binding.tvRemove.setVisibility(View.VISIBLE);
            binding.vDividerFull.setVisibility(View.VISIBLE);
        }

        bindPartly(binding, item);
        bindTags(binding, item);
        bindActivityItems(binding, item);
        bindCartOpLayout(binding, item);

        ViewTools.setViewOnSafeClickListener(binding.tvRemove, v -> {
            onCartEditListener.prepareEditProductData(item);
            onCartEditListener.resetEditData();

            SimplePreOrderBean.ItemsBean simpleOrderItem;
            simpleOrderItem = OrderManager.get().getSimpleOrderItem(item.t.product_id, item.t.product_key);
            int lastNum = simpleOrderItem != null ? simpleOrderItem.quantity : 0;
            logCartAction(item, lastNum, 0);

            onCartEditListener.removeAdapterProductData(item);
            onCartEditListener.removeProductData(item);
        });
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, MiniCartItemData item, @NonNull List<Object> payloads) {
        ItemMkplCartProductBinding binding = BindingAdapterViewHolder.getBinding(helper);
        if (binding == null) return;
        for (Object payload : payloads) {
            if (payload instanceof GlobalMiniCartAction.Collapse) {
                int productId = item.t.product_id;
                int affectedId = ((GlobalMiniCartAction.Collapse) payload).getProductId();
                if (productId == -1 || productId == affectedId) {
                    bindCartOpLayout(binding, item);
                }
            } else if (payload instanceof GlobalMiniCartAction.PartlyUpdate) {
                GlobalMiniCartAction.PartlyUpdate partlyUpdate = ((GlobalMiniCartAction.PartlyUpdate) payload);
                boolean isSameItem = item.t.product_id == partlyUpdate.getProductId();
                if (isSameItem) {
                    bindPartly(binding, item);
                    bindActivityItems(binding, item);
                }
            }
        }
    }

    private void bindPartly(ItemMkplCartProductBinding binding, MiniCartItemData item) {
        final NewItemBean bean = item.t;

        String priceText;
        if (bean.price != 0) {
            priceText = OrderHelper.formatUSMoney(bean.price);
        } else {
            priceText = binding.tvPrice.getContext().getString(R.string.s_free);
        }
        binding.tvPrice.setText(priceText);

        if (bean.base_price > 0 && bean.base_price != bean.price) {
            String basePriceText = OrderHelper.formatUSMoney(bean.base_price);
            if (!Objects.equals(basePriceText, priceText)) {
                binding.tvPriceDelete.setText(new Spanny(basePriceText, Spans.strikethrough()));
            }
        } else {
            binding.tvPriceDelete.setText(null);
        }

        boolean isEditing = onCartEditListener.isEditMode(item.t.product_id, item.t.product_key);
        if (!isEditing) {
            binding.layoutOp.setOpNumDirect(bean.quantity);
        }
    }

    private void bindTags(ItemMkplCartProductBinding binding, MiniCartItemData item) {
        List<EntranceTag> tagList = CollectionUtils.subList(item.t.product_tag_list, 0, 1);
        if (!CollectionUtils.isEmpty(tagList)) {
            ProductViewHelper.setProductViewTag(context, binding.layoutTags, tagList, ProductView.STYLE_LIST);
            ViewTools.setViewVisibilityIfChanged(binding.layoutTags, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(binding.layoutTags, false);
        }
    }

    private void bindActivityItems(ItemMkplCartProductBinding binding, MiniCartItemData item) {
        LinearLayout layoutActivityItems = binding.layoutActivityItems;

        NewItemBean bean = item.t;
        List<NewItemBean> activityItems = bean.getActivityItems();
        if (activityItems.isEmpty()) {
            ViewTools.setViewVisibilityIfChanged(layoutActivityItems, false);
            return;
        }

        int itemCount = activityItems.size();
        int childCount = layoutActivityItems.getChildCount();
        // remove unnecessary views
        if (childCount - itemCount > 0) {
            layoutActivityItems.removeViews(childCount - itemCount, childCount - itemCount);
        }

        LayoutInflater inflater = LayoutInflater.from(context);
        ItemSectionCartProductActivityItemBinding b;
        for (int i = 0; i < itemCount; i++) {
            NewItemBean activityItem = activityItems.get(i);
            View childView = layoutActivityItems.getChildAt(i);
            if (childView != null) {
                b = ItemSectionCartProductActivityItemBinding.bind(childView);
            } else {
                b = ItemSectionCartProductActivityItemBinding.inflate(inflater, layoutActivityItems, false);
                ViewTools.updateMargins(b.ivIcon, CommonTools.dp2px(40), null, null, null);
                ViewTools.updatePaddings(b.getRoot(), CommonTools.dp2px(10), null, CommonTools.dp2px(10), null);
                layoutActivityItems.addView(b.getRoot());
            }
            ViewTools.setViewOnSafeClickListener(b.getRoot(), v -> {
                Context context = v.getContext();
                if (context != null) {
                    context.startActivity(ProductIntentCreator.getIntent(context, OrderHelper.toProductBeanV5(activityItem, null)));
                }
            });
            bindActivityItem(b, activityItem, i);
        }

        ViewTools.setViewVisibilityIfChanged(layoutActivityItems, layoutActivityItems.getChildCount() > 0);
    }

    public void bindActivityItem(ItemSectionCartProductActivityItemBinding binding, NewItemBean item, int itemPosition) {
        // header image
        ImageLoader.load(
                context,
                binding.ivIcon,
                WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, item.img),
                R.mipmap.iv_product_placeholder
        );

        // title
        Spanny title = new Spanny();
        if (item.isGift()) {
            SpannyUtils.buildStartTagWithSm(
                    /* context = */context,
                    /* builder = */title,
                    /* tag = */context.getString(R.string.s_gift),
                    /* textColor = */Color.WHITE,
                    /* tagDrawable = */ShapeHelper.buildSolidDrawable(ContextCompat.getColor(context,
                            R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)),
                    9, 0, 6
            );
        }
        title.append(item.title, new TextAppearanceSpan(context, R.style.style_fluid_root_card_label_sm));
        binding.tvName.setText(title);

        // price & base price
        binding.tvPrice.setText(context.getString(R.string.s_free));
        if (item.base_price > 0) { // 大于0显示划线价格
            binding.tvPriceDelete.setText(new Spanny(OrderHelper.formatUSMoney(item.base_price), new StrikethroughSpan()));
        } else {
            binding.tvPriceDelete.setText(null);
        }

        // quantity
        binding.tvProductNum.setText(String.valueOf(item.quantity));

        // splitter
        ViewTools.setViewVisibilityIfChanged(binding.vDividerTop, itemPosition == 0);
        ViewTools.setViewVisibilityIfChanged(binding.vDividerBottom, true);
    }

    private void bindCartOpLayout(ItemMkplCartProductBinding binding, MiniCartItemData item) {
        final ExCartOpLayout layoutOp = binding.layoutOp;
        Context context = layoutOp.getContext();

        final NewItemBean bean = item.t;
        layoutOp.setOpStyle(bean.quantity, bean.min_order_quantity, bean.getOrderMaxQuantity());
        if (bean.is_gift) {
            layoutOp.setNumberBackground(null);
            layoutOp.setOpNumDirect(bean.quantity);
            layoutOp.collapse();
            layoutOp.setOnOperateListener(null);
            return;
        }

        layoutOp.setNumberBackground(ResourcesCompat.getDrawable(context.getResources(), R.drawable.drawable_atc_mini_cart_num_bg, null));
        boolean isEditing = onCartEditListener.isEditMode(item.t.product_id, item.t.product_key);
        if (isEditing) {
            layoutOp.setOpNumDirect(onCartEditListener.getEditProductNum());
            layoutOp.expand();
        } else {
            layoutOp.setOpNumDirect(bean.quantity);
            layoutOp.collapse();
        }
        layoutOp.setOnOperateListener(createOnCartOpListener(layoutOp, item));
    }

    private CartOpLayout.OnCartOpListener createOnCartOpListener(final ExCartOpLayout layoutOp, final MiniCartItemData item) {
        final NewItemBean bean = item.t;
        final int productId = bean.product_id;
        final String productKey = bean.product_key;
        return new CartOpLayout.OnCartOpListener() {
            @Override
            public void operateLeft(View view) {
                SharedOrderViewModel.get().productOpStatusData.postValue(productId);
                onCartEditListener.prepareEditProductData(item);
                int lastNum = getOldQty();
                int num = OrderHelper.editNum(false, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity());
                onCartEditListener.setEditData(productId, productKey, num);

                logCartAction(item, lastNum, num);

                if ((bean.min_order_quantity > 0 && num < bean.min_order_quantity) || num < 1) {
                    onCartEditListener.removeAdapterProductData(item);
                } else {
                    layoutOp.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
                    layoutOp.setOpNumDirect(num);
                }
                onCartEditListener.editProductData(item, false);
            }

            @Override
            public void operateRight(View view) {
                SharedOrderViewModel.get().productOpStatusData.postValue(productId);
                onCartEditListener.prepareEditProductData(item);
                int lastNum = getOldQty();
                int num = OrderHelper.editNum(true, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity());
                onCartEditListener.setEditData(productId, productKey, num);
                logCartAction(item, lastNum, num);

                bean.quantity = num;
                layoutOp.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
                if (lastNum > 0 && lastNum == num) {
                    layoutOp.setOpNumDirect(num);
                    layoutOp.showReachedTips(); // 已達限量
                } else {
                    if (lastNum <= 0 && num > 1) {
                        layoutOp.setOpNumDirect(num);
                        layoutOp.showMinPurchaseTips(num);
                    } else {
                        layoutOp.setOpNumDirect(num);
                    }
                    onCartEditListener.editProductData(item, true);
                }
            }

            @Override
            public void onNumClick(View view) {
                int lastNum = getOldQty();
                if (lastNum > 0) {
                    SharedOrderViewModel.get().productOpStatusData.postValue(productId);
                    onCartEditListener.prepareEditProductData(item);
                    onCartEditListener.setEditData(productId, productKey, lastNum);
                    layoutOp.setOpStyle(lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity());
                    layoutOp.expand();
                } else {
                    operateRight(view);
                }
            }

            @Override
            public int getProductId() {
                return productId;
            }

            private int getOldQty() {
                SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(productId, productKey);
                return simpleOrderItem != null ? simpleOrderItem.quantity : 0;
            }
        };
    }

    private static void logCartAction(MiniCartItemData item, int oldQty, int newQty) {
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(item.modNm)
                .setMod_pos(0)
                .setSec_nm(null)
                .setSec_pos(-1)
                .build()
                .getElement();
        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(item.pageTarget)
                .asMap();

        NewItemBean bean = item.t;
        Map<String, Object> content = new TrackParams()
                .put("prod_pos", item.prodPos)
                .put("prod_id", bean.product_id)
                .put("old_qty", oldQty)
                .put("new_qty", newQty)
                .put("refer_type", bean.refer_type)
                .put("source", bean.source)
                .put("is_mkpl", "seller".equals(bean.refer_type))
                .put("is_fbw", bean.isFbw())
                .put("biz_type", bean.biz_type)
                .put("volume_price_support", bean.volume_price_support)
                .put("is_presale", bean.isPresale())
                .put("sale_event_id", bean.getSaleEventId())
                .get();
        AppAnalytics.logCartAction(
                new EagleTrackModel.Builder()
                        .addElement(element)
                        .addContent(content)
                        .addCtx(ctx)
                        .build()
                        .getParams()
        );
    }


}
