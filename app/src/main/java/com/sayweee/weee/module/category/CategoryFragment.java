package com.sayweee.weee.module.category;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;

import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.AppBarLayout;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.track.model.ExtendEvent;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.CateActivity;
import com.sayweee.weee.module.cate.bean.CateBean;
import com.sayweee.weee.module.cate.tools.CategoryUrlTool;
import com.sayweee.weee.module.category.adapter.CategoryAdapter;
import com.sayweee.weee.module.category.bean.WebFilterData;
import com.sayweee.weee.module.category.service.CategoryProgressBarViewModel;
import com.sayweee.weee.module.category.service.CategoryViewModel;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.helper.ProgressBarManager;
import com.sayweee.weee.service.helper.StatusHelper;
import com.sayweee.weee.service.timer.RfmBannerManager;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.service.timer.service.TimerChangedListener;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.function.Consumer;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.ImageShadowHelper;
import com.sayweee.weee.widget.ProgressBarContainer;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.indicator.TrackCommonPagerTitleView;
import com.sayweee.weee.widget.indicator.TrackNavigator;
import com.sayweee.weee.widget.tab.ITabEventDispatch;
import com.sayweee.weee.widget.tab.ITabSelectedEventDispatch;
import com.sayweee.weee.widget.tips.TipsBarManager;
import com.sayweee.weee.widget.tips.TipsBean;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmStatusFragment;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CategoryFragment extends WrapperMvvmStatusFragment<CategoryViewModel>
        implements ITabSelectedEventDispatch, ProgressBarManager.ProgressChangedListener {

    View vStatus, ivSearchShadow;
    ViewPager2 viewPager;
    CompatMagicIndicator indicator;
    AppBarLayout abl;
    ProgressBarContainer layoutProgress;
    String key;
    Map<String, String> params;

    private CategoryProgressBarViewModel progressBarViewModel;
    private int resumeCount;

    TipsBarManager.TipsChangedListener tipsChangedListener = new TipsBarManager.TipsChangedListener() {

        @Override
        public void onChanged(TipsBean tips, boolean show) {
            TipsBarManager.get().displayTipsBar(findViewById(R.id.layout_tips_bar), tips, show);
        }
    };

    TimerChangedListener timerChangedListener = new TimerChangedListener() {
        @Override
        public void onRegister() {
            TimerBannerManager.get().setTimerPage(findViewById(R.id.layout_timer_banner), ExtendEvent.EVENT_PAGE_CATEGORY);
        }

        @Override
        public void onChanged(boolean display, int hour, int min, int sec) {
            TimerBannerManager.get().setTimerInfo(findViewById(R.id.layout_timer_banner), display, hour, min, sec);
        }
    };

    private ViewPager2.OnPageChangeCallback onPageChangeCallback;
    private FirstAddItemDialog firstAddItemDialog;//first_add_seller_item_new 弹窗

    public static Fragment newInstance() {
        return new CategoryFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.CATEGORY, String.valueOf(hashCode()));
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_category;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        outState.clear();
        super.onSaveInstanceState(outState);
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        vStatus = findViewById(R.id.v_status);
        viewPager = findViewById(R.id.vp_category);//内容view pager
        indicator = findViewById(R.id.tab_category);//主标题
        abl = findViewById(R.id.abl_cate);//AppBarLayout
        ivSearchShadow = findViewById(R.id.iv_cate_search_shadow);
        layoutProgress = findViewById(R.id.layout_progress);
        ViewTools.setViewOnSafeClickListener(findViewById(R.id.tv_search), v -> gotoSearchPage());
        viewPager.setOffscreenPageLimit(1);
        abl.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                ViewTools.setViewVisible(ivSearchShadow, verticalOffset == 0);
            }
        });
        showVeil(true);
        setUi();
    }

    @Override
    public void loadData() {
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.CATEGORY,
                String.valueOf(viewModel.hashCode()));
        onPageRefresh(true);
    }

    @Override
    public <VM> VM createModel() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            progressBarViewModel = new ViewModelProvider(this).get(CategoryProgressBarViewModel.class);
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        viewModel.categoryData.observe(this, new Observer<CateBean>() {
            @Override
            public void onChanged(CateBean data) {
                fillCategory(data);
                removeStatus();
                showVeil(false);
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean bean) {
                StatusHelper.showStatus(getStatusManager(), bean, true, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        onPageRefresh(false);
                    }
                });
                showVeil(false);
            }
        });

        viewModel.toggleCategoryData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String key) {
                onPageSelected(key, null);//点击bottom按钮切换category
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                onPreOrderRecreated();

            }
        });

        viewModel.getTradeInTagLiveData().observe(this, tagInfo -> onProgressChange(0, null, tagInfo));

        if (progressBarViewModel != null) {
            progressBarViewModel.getLoadMoreRequestSignal().observe(this, sig -> {
                int count = progressBarViewModel.getLoadMoreCount();
                if (count >= 2) {
                    ProgressBarManager.get().hideProgressBar(layoutProgress);
                }
            });
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        resumeCount++;
        StatusBarManager.setStatusBar(this, vStatus, true);
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.CATEGORY, String.valueOf(hashCode()));
        AppAnalytics.logPageView(WeeeEvent.PageView.CATEGORY, this);
        ProgressBarManager.get().registerProgressChangedListener(this);
        TipsBarManager.get().registerTipsChangedListener(tipsChangedListener);
        PopupCenterManager.get().onPageResumed(ExtendEvent.EVENT_PAGE_CATEGORY);
        TimerBannerManager.get().registerAndLog(timerChangedListener, findViewById(R.id.layout_timer_banner));
        EagleTrackManger.get().resetTrackData(EagleTrackManger.PAGE_CATEGORY);
        if (indicator != null && indicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) indicator.getNavigator()).onPageResume();
        }
        getTradeInTag();
    }

    @Override
    public void onFragmentPause() {
        super.onFragmentPause();
        ProgressBarManager.get().unregisterProgressChangedListener(this);
        TipsBarManager.get().unregisterTipsChangedListener(tipsChangedListener);
        TimerBannerManager.get().unregisterTimerChangedListener(timerChangedListener);
        RfmBannerManager.get().unregisterListener();
        if (indicator != null && indicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) indicator.getNavigator()).onPagePause();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        StatusBarManager.setStatusBarDestroy(this);
        if (viewPager != null && onPageChangeCallback != null) {
            viewPager.unregisterOnPageChangeCallback(onPageChangeCallback);
        }
    }

    @Override
    public void onTabDoubleTap() {
        Fragment fragment = ViewTools.getViewPager2CurrentFragment(getChildFragmentManager(), viewPager);
        if (fragment instanceof ITabEventDispatch && fragment.isAdded()) {
            ((ITabEventDispatch) fragment).onTabDoubleTap();
        }
    }

    @Override
    public void onTabUnselected(int position) {
        if (position == 1) {
            Fragment fragment = ViewTools.getViewPager2CurrentFragment(getChildFragmentManager(), viewPager);
            if (fragment instanceof ITabSelectedEventDispatch && fragment.isAdded()) {
                ((ITabSelectedEventDispatch) fragment).onTabUnselected(position);
            }
        }
    }

    private void onPageRefresh(boolean isSilent) {
        if (viewModel != null) {
            viewModel.onRefresh(isSilent);
        }
    }

    private void fillCategory(CateBean data) {
        if (data == null || EmptyUtils.isEmpty(data.category_list)) {
            return;
        }
        List<CateBean.CategoryListBean> indicatorList = data.category_list;//indicator数据
        List<CateBean.CategoryListBean> vpList = new ArrayList<>();//view pager数据
        for (CateBean.CategoryListBean bean : indicatorList) {
            if (!bean.isLinkType()) {
                vpList.add(bean);//剔除特殊跳转url icon
            }
        }
        CategoryAdapter vpAdapter = new CategoryAdapter(this, vpList);
        viewPager.setAdapter(vpAdapter);
        CommonNavigator navigator = new TrackNavigator(getActivity());
        int size = indicatorList.size();
        navigator.setAdapter(new CommonNavigatorAdapter() {

            private final int DEFAULT_MIN_WIDTH = CommonTools.dp2px(64);
            private final int DEFAULT_MAX_WIDTH = CommonTools.dp2px(80);

            @Override
            public int getCount() {
                return size;
            }

            @Override
            public IPagerTitleView getTitleView(final Context context, final int index) {
                final CateBean.CategoryListBean cateBean = indicatorList.get(index);
                CommonPagerTitleView titleView = new TrackCommonPagerTitleView(context) {
                    @Override
                    public void onImpressionTrigger() {
                        String key = index + "_" + cateBean.key;
                        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_CATEGORY, key)) {
                            Map<String, Object> params = new EagleTrackModel.Builder()
                                    .setMod_nm("ellipse_carousel")
                                    .setMod_pos(0)
                                    .setEllipse_label(cateBean.key)
                                    .setEllipse_pos(index)
                                    .setEllipse_type(EagleTrackEvent.BannerType.CATEGORY)
                                    .setIs_new(cateBean.is_new)
                                    .build().getParams();
                            AppAnalytics.logEllipseImp(params);
                            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_CATEGORY, key);
                        }
                    }
                };
                View customLayout = LayoutInflater.from(context).inflate(R.layout.tab_cate, titleView, false);
                ImageView categoryImg = customLayout.findViewById(R.id.iv_category);
                TextView categoryText = customLayout.findViewById(R.id.tv_category);
                TextView tvLabel = customLayout.findViewById(R.id.tv_label);
                ViewTools.setViewVisible(tvLabel, cateBean.is_new);
                ImageLoader.load(context, categoryImg, WebpManager.get().getConvertUrl(SPEC_64, cateBean.img_url), R.drawable.shape_oval_place_size_50);
                ImageLoader.preload(context, CommonTools.dp2px(58), WebpManager.get().getConvertUrl(SPEC_64, cateBean.active_img_url));//预加载
                ViewTools.applyTextColor(categoryText, R.color.color_surface_1_fg_default_idle);
                ViewTools.applyTextStyle(categoryText, R.style.style_fluid_root_utility_sm_subdued);

                int maxWidth = Integer.MAX_VALUE;
                if (LanguageManager.get().isEnglish()) {
                    int width = CategoryLabelHelper.calculateWidth(categoryText, cateBean.name, DEFAULT_MIN_WIDTH);
                    ViewTools.updateViewSize(categoryText, width, null);
                } else {
                    maxWidth = DEFAULT_MAX_WIDTH;
                    ViewTools.updateViewSize(categoryText, ViewGroup.LayoutParams.WRAP_CONTENT, null);
                }
                if (categoryText.getMaxWidth() != maxWidth) {
                    categoryText.setMaxWidth(maxWidth);
                }
                categoryText.setText(cateBean.name);

                ViewTools.setViewVisible(customLayout.findViewById(R.id.layout_out_reward), !EmptyUtils.isEmpty(cateBean.category_label));
                if (!EmptyUtils.isEmpty(cateBean.category_label)) {
                    ShapeTextView tvReward = customLayout.findViewById(R.id.tv_reward);
                    tvReward.setBackgroundSolidDrawable(ViewTools.parseColor(cateBean.category_label.label_color, Color.WHITE), CommonTools.dp2px(5));
                    tvReward.setTextColor(ViewTools.parseColor(cateBean.category_label.label_font_color, Color.WHITE));
                    tvReward.setText(String.valueOf(cateBean.category_label.label_name));
                }

                int padding = CommonTools.dp2px(12);
                FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                layoutParams.leftMargin = index == 0 ? padding : 0;
                layoutParams.rightMargin = index == size - 1 ? padding : 0;
                titleView.setContentView(customLayout, layoutParams);
                titleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {

                    @Override
                    public void onSelected(int index, int totalCount) {
                        ImageLoader.load(context, categoryImg, WebpManager.get().getConvertUrl(SPEC_64, cateBean.active_img_url));
                        ViewTools.applyTextStyle(categoryText, R.style.style_fluid_root_utility_sm);
                        int color = ContextCompat.getColor(context, R.color.color_navbar_fg_default);
                        categoryText.setTextColor(ViewTools.parseColor(cateBean.title_color, color));
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        ImageLoader.load(context, categoryImg, WebpManager.get().getConvertUrl(SPEC_64, cateBean.img_url));
                        ViewTools.applyTextStyle(categoryText, R.style.style_fluid_root_utility_sm_subdued);
                        ViewTools.applyTextColor(categoryText, R.color.color_surface_1_fg_default_idle);
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {

                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {

                    }
                });

                titleView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //圆形大分类图标ellipse click
                        EagleTrackManger.get().trackEagleClickAction("ellipse_carousel",
                                0,
                                null,
                                -1,
                                cateBean.key,
                                index,
                                EagleTrackEvent.TargetType.CATEGORY,
                                EagleTrackEvent.ClickType.VIEW);
                        if (cateBean.isLinkType()) {
                            toWeb(cateBean.url);
                        } else {
                            key = cateBean.key;//记录当前分类，刷新整个分类时定位使用
                            viewPager.setCurrentItem(getRealIndex(vpList, key), false);
                        }
                        deleteRelatedData();
                    }
                });

                return titleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });
        navigator.setReselectWhenLayout(false);//===》无法滑动bug
        indicator.setNavigator(navigator);
        //indicator.bindSafely(viewPager);
        if (onPageChangeCallback != null) {
            viewPager.unregisterOnPageChangeCallback(onPageChangeCallback);
        }
        onPageChangeCallback = new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
                String vpKey = vpList.get(position).key;
                indicator.onPageScrolled(getRealIndex(indicatorList, vpKey), positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                String vpKey = vpList.get(position).key;
                indicator.onPageSelected(getRealIndex(indicatorList, vpKey));
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
                indicator.onPageScrollStateChanged(state);
            }
        };
        viewPager.registerOnPageChangeCallback(onPageChangeCallback);
        if (!EmptyUtils.isEmpty(key)) {
            onPageSelected(key, params);
        }
    }

    public void onPageSelected(Map<String, Object> params) {
        if (params != null) {
            Set<Map.Entry<String, Object>> entries = params.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                Object value = entry.getValue();
                onPageSelected(entry.getKey(), value instanceof Map ? (Map<String, String>) value : null);

                if (value instanceof Map) {
                    Map<String, String> map = (Map<String, String>) value;
                    if (!EmptyUtils.isEmpty(map) && "true".equalsIgnoreCase(map.get(Constants.UrlMapParams.COMBINE_FREE_SHIPPING))) {
                        indicator.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                showShippingPopUp();
                            }
                        }, 1000);
                    }
                }
            }
        }
    }

    private void onPageSelected(String num, Map<String, String> params) {
        if (viewModel != null && viewModel.categoryData != null && viewModel.categoryData.getValue() != null) {
            CateBean cateBean = viewModel.categoryData.getValue();
            for (int i = 0; i < cateBean.category_list.size(); i++) {
                CateBean.CategoryListBean categoryBean = cateBean.category_list.get(i);
                if (categoryBean.key.equalsIgnoreCase(num)) {
                    if (categoryBean.isLinkType()) {
                        toWeb(categoryBean.url);//特殊type分类直接load link
                    } else {
                        indicator.handlePageSelected(i, false);
                        viewPager.setCurrentItem(getRealIndex(categoryBean.key), false);
                        SharedViewModel.get().newWebFilter.postValue(new WebFilterData(num, params));
                    }
                    break;
                }
            }
        } else {
            this.key = num;
            this.params = params;
        }
    }

    private void showShippingPopUp() {
        if (!AccountManager.get().isCombineFreeShippingPopUpShowed()) {
            String popUpUrl = AppConfig.HOST_WEB + Constants.Url.COMBINE_REMINDER;
            new PopupSlideDialog().showOnQueueLoaded(popUpUrl);
            AccountManager.get().setCombineFreeShippingPopUpShowed(true);
            AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                    .addContent(new TrackParams()
                            .put("action", "view")
                            .put("name", "add_on_items")
                            .put("id", null)
                            .put("url", popUpUrl)
                            .put("target_url", popUpUrl)
                            .get())
                    .build()
                    .getParams());
        }
    }

    private void setUi() {
        boolean isActivityUi = activity instanceof CateActivity;
        ViewTools.setViewVisible(findViewById(R.id.iv_back), isActivityUi);
        findViewById(R.id.iv_back).setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                activity.finish();
            }
        });
        findViewById(R.id.cl_cate).setPadding(0, 0, 0, CommonTools.dp2px(isActivityUi ? 0 : 52));
    }

    private void showVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_cate_ellipse);
        VeilLayout vlTag = findViewById(R.id.vl_category_tag);
        VeilLayout vlList = findViewById(R.id.vl_category_list);
        if (vl != null && vlTag != null && vlList != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
                vlTag.setVisibility(View.VISIBLE);
                vlTag.veil();
                vlList.setVisibility(View.VISIBLE);
                vlList.veil();
            } else {
                vl.setVisibility(View.GONE);
                vl.unVeil();
                vlTag.setVisibility(View.GONE);
                vlTag.unVeil();
                vlList.setVisibility(View.GONE);
                vlList.unVeil();
            }
        }
    }

    private void gotoSearchPage() {
        String fromBusiness = CategoryUrlTool.getFromBusiness(params);
        boolean isFromGlobalPlus = "global_plus".equals(fromBusiness);
        if (isFromGlobalPlus) {
            startActivity(SearchPanelActivity.getIntentByGlobal(activity, null, null));
        } else {
            startActivity(SearchPanelActivity.getIntent(activity));
        }
    }

    private int getRealIndex(List<CateBean.CategoryListBean> list, String key) {
        int index = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                if (key.equalsIgnoreCase(list.get(i).key)) {
                    index = i;
                    break;
                }
            }
        }
        return index;
    }

    private int getRealIndex(String key) {
        if (viewPager.getAdapter() instanceof CategoryAdapter) {
            CategoryAdapter adapter = (CategoryAdapter) viewPager.getAdapter();
            return getRealIndex(adapter.getAdapterData(), key);
        } else {
            return 0;
        }
    }

    private void toWeb(String url) {
        activity.startActivity(WebViewActivity.getIntent(activity, url));
    }

    private void getTradeInTag() {
        viewModel.getTradeInTag(/* referType= */"normal", /* source= */null);
    }

    /**
     * Implementation of ProgressBarManager.ProgressChangedListener
     */
    @Override
    public void onProgressChange(int productId, @Nullable String tagType, @Nullable UpdateResultBean resultBean) {
        UpdateResultBean.TagInfoBean tagInfo;
        tagInfo = resultBean != null ? resultBean.tagInfo : null;
        if (ProgressBarManager.get().isFirstAddSellerItemNew(tagType)) {
            if (tagInfo == null) {
                ProgressBarManager.get().hideProgressBar(layoutProgress);
                return;
            }
            if (firstAddItemDialog == null) {
                firstAddItemDialog = new FirstAddItemDialog(activity);
            }
            firstAddItemDialog.setData(productId, tagType, tagInfo).show();
            return;
        } else if (ProgressBarManager.get().isSellerItemExistNew(tagType)) {
            if (tagInfo == null) {
                ProgressBarManager.get().hideProgressBar(layoutProgress);
                return;
            }
            View rootView = getView();
            ProgressBarManager.get().showSellerItemExistNew(rootView, productId, tagType, tagInfo);
            return;
        }
        int loadMoreCount = 0;
        if (progressBarViewModel != null) {
            if (tagInfo == null) {
                progressBarViewModel.resetLoadMoreCount();
                progressBarViewModel.setLoadMoreCountEnabled(false);
            } else {
                if (tagInfo.isTradeInUpsell()) {
                    if (tagInfo.diff_price > 0) { // unreached
                        progressBarViewModel.resetLoadMoreCount();
                        progressBarViewModel.setLoadMoreCountEnabled(false);
                    } else {
                        progressBarViewModel.setLoadMoreCountEnabled(true);
                    }
                }
            }
            loadMoreCount = progressBarViewModel.getLoadMoreCount();
        }
        boolean isTradeInUpsell = tagInfo != null && tagInfo.isTradeInUpsell();
        if (isTradeInUpsell && loadMoreCount >= 2) {
            ProgressBarManager.get().hideProgressBar(layoutProgress);
            return;
        }
        boolean isProgressBarHidden = !layoutProgress.isShowing();
        boolean isShow = ProgressBarManager.get().setProgressBar(WeeeEvent.PageView.CATEGORY, layoutProgress, resultBean);
        if (isTradeInUpsell && isShow) {
            String diffPriceUpsell = OrderHelper.formatMoney(tagInfo.diff_price >= 0 ? tagInfo.diff_price : 0);
            String popupUrl = tagInfo.turn_url;
            if (isProgressBarHidden || resumeCount > 1) {
                AppAnalytics.logBannerImp(
                        new EagleTrackModel.Builder()
                                .setMod_nm(EagleTrackEvent.ModNm.UPSELL_POPUP)
                                .setBanner_type(EagleTrackEvent.BannerType.UPSELL_MESSAGE)
                                .setBanner_pos(0)
                                .setUrl(popupUrl)
                                .addCtx(new EagleContext().setDiffPriceUpsell(diffPriceUpsell).asMap())
                                .build()
                                .getParams()
                );
            }
            configTradeInUpsellProgressBar(layoutProgress, tagInfo);
        }
    }

    private void configTradeInUpsellProgressBar(View progressBar, @NonNull UpdateResultBean.TagInfoBean tagInfo) {
        Context context = getContext();
        if (context == null) {
            return;
        }
        Consumer<View> onClickListener;
        String diffPriceUpsell = OrderHelper.formatMoney(tagInfo.diff_price >= 0 ? tagInfo.diff_price : 0);
        String popupUrl = tagInfo.turn_url;
        if (tagInfo.isTurn() && !EmptyUtils.isEmpty(popupUrl)) {
            onClickListener = v -> {
                AppAnalytics.logClickAction(
                        new EagleTrackModel.Builder()
                                .setMod_nm(EagleTrackEvent.ModNm.UPSELL_POPUP)
                                .setTargetNm(EagleTrackEvent.TargetNm.VIEW_MORE)
                                .setTargetType(EagleTrackEvent.TargetType.UPSELL_MESSAGE)
                                .setTargetPos(-1)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setUrl(popupUrl)
                                .addCtx(new EagleContext().setDiffPriceUpsell(diffPriceUpsell).asMap())
                                .build()
                                .getParams()
                );
                showTradeInUpsellPopupDialog(context, popupUrl, dialog -> {
                    AppAnalytics.logClickAction(
                            new EagleTrackModel.Builder()
                                    .setMod_nm(EagleTrackEvent.ModNm.UPSELL_POPUP)
                                    .setTargetNm(EagleTrackEvent.TargetNm.CLOSE)
                                    .setTargetType(EagleTrackEvent.TargetType.UPSELL_MESSAGE)
                                    .setTargetPos(-1)
                                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                                    .setUrl(popupUrl)
                                    .addCtx(new EagleContext().setDiffPriceUpsell(diffPriceUpsell).asMap())
                                    .build()
                                    .getParams()
                    );
                });
            };
        } else {
            onClickListener = null;
        }

        View clickView = progressBar.findViewById(R.id.layout_progress_bar_upsell_root);
        ViewTools.setViewOnSafeClickListener(clickView, onClickListener);
    }

    private WrapperDialog upsellPopupDialog = null;

    private void dismissTradeInUpsellPopupDialog() {
        if (upsellPopupDialog != null) {
            upsellPopupDialog.dismiss();
            upsellPopupDialog = null;
        }
    }

    private void showTradeInUpsellPopupDialog(Context context, String popupUrl, Consumer<DialogInterface> onCancel) {
        dismissTradeInUpsellPopupDialog();

        Consumer<Void> onHidden = sig -> {
            if (progressBarViewModel != null) {
                progressBarViewModel.notifyProgressBarDismissed();
            }
            getTradeInTag();
        };

        PopupSlideDialog dialog = new PopupSlideDialog() {

            private boolean oneShotHidden = false;

            @Override
            public void onDialogCancel(DialogInterface dialog) {
                oneShotHidden = true;
                onCancel.accept(dialog);
                onHidden.accept(null);
            }

            @Override
            public void onDialogDismiss(DialogInterface dialog) {
                super.onDialogDismiss(dialog);
                // onDismiss will call after cancel action
                if (oneShotHidden) {
                    oneShotHidden = false;
                    return;
                }
                onHidden.accept(null);
            }
        };
        dialog.bindParentLifecycle(getViewLifecycleOwnerLiveData().getValue());
        dialog.loadUrl(popupUrl);
        dialog.show();
        upsellPopupDialog = dialog;
    }

    private void deleteRelatedData() {
        onTabUnselected(1);//切换大分类删除关联商品
    }

    private void onPreOrderRecreated() {
        if (viewModel != null) {
            viewModel.shareablePart.clear();
        }
        abl.setExpanded(true, false);//滚到顶部
        onPageRefresh(true);//订单状态改变

        // hide progress bar upsell
        if (progressBarViewModel != null) {
            progressBarViewModel.resetLoadMoreCount();
            progressBarViewModel.setLoadMoreCountEnabled(false);
        }
        ProgressBarManager.get().hideProgressBar(layoutProgress);

        // hide upsell popup dialog
        dismissTradeInUpsellPopupDialog();
    }

}
