package com.sayweee.weee.module.debug.producttrace.ui;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.sayweee.core.order.OrderRecreateType;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.module.debug.DebugViewModel;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.widget.SimpleSwitch;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

public class ProductTraceTestActivity extends WrapperMvvmActivity<DebugViewModel> {

    public static Intent getIntent(Context context) {
        return new Intent(context, ProductTraceTestActivity.class);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.layout_product_trace_settings;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        setWrapperTitle("Product Trace Settings");
        SimpleSwitch simpleSwitch = findViewById(R.id.switch_enable);
        simpleSwitch.setChecked(ProductTraceManager.get().isEnabled());
        simpleSwitch.setOnCheckedChangeListener(this::onSwitchEnableChanged);
    }

    @Override
    public void loadData() {

    }

    @Override
    public void attachModel() {

    }

    private void onSwitchEnableChanged(SimpleSwitch view, boolean isChecked, boolean isFromUser) {
        if (isFromUser) {
            boolean oldValue = ProductTraceManager.get().isEnabled();
            if (oldValue == isChecked) {
                return;
            }
            ProductTraceManager.get().setEnabled(isChecked);
            if (isChecked) { // off -> on
                restartApp();
            }
        }
    }

    private void restartApp() {
        getView().postDelayed(this::restart, 500L);
    }

    private void restart() {
        SharedOrderViewModel.get().onPreOrderRecreate(OrderRecreateType.FORCE);
    }
}
