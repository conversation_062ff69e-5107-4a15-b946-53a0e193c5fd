package com.sayweee.weee.module.product.provider;


import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.base.adapter.payload.RecyclerItemVisiblePositions;
import com.sayweee.weee.module.cms.track.IPageLifecycle;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.TrackingInfoAdapter;
import com.sayweee.weee.module.product.adapter.PdpRecommendAdapter;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.module.product.data.PdpProductsData;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.analytics.ImpressionHelper;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Deprecated
public class PdpRecommendProvider extends SimpleSectionProvider<PdpProductsData, AdapterViewHolder> implements TrackingInfoAdapter, IPageLifecycle {
    public String filterSubCategory, pageTarget, sort;
    protected EagleImpressionTrackerIml tracker = new EagleImpressionTrackerIml();
    private final List<SoftReference<BaseQuickAdapter>> childAdapterCaches = new ArrayList<>();

    @Override
    public int getItemType() {
        return PdpItemType.PDP_SELLER_RECOMMEND;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_pdp_recommend;
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        setLayoutParamsMargin(holder, CommonTools.dp2px(16));
        super.onViewAttachedToWindow(holder);
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);

        RecyclerView rv = helper.getView(R.id.rv_list);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(context, 3);
        rv.setLayoutManager(gridLayoutManager);
        PdpRecommendAdapter adapter = new PdpRecommendAdapter();
        adapter.setAttachView(rv);
        childAdapterCaches.add(new SoftReference<>(adapter));
        rv.setAdapter(adapter);
    }

    protected void setLayoutParamsMargin(AdapterViewHolder holder, int margin) {
        if (holder != null) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof RecyclerView.LayoutParams) {
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                params.leftMargin = margin;
                params.rightMargin = margin;
            }
        }
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, PdpProductsData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        RecyclerView rv = helper.getView(R.id.rv_list);
        if (rv == null) {
            return;
        }
        PdpRecommendAdapter adapter = null;
        if (rv.getAdapter() instanceof PdpRecommendAdapter) {
            adapter = (PdpRecommendAdapter) rv.getAdapter();
        }
        if (adapter == null) {
            return;
        }
        Object payload = CollectionUtils.firstOrNull(payloads);
        if (payload instanceof String && PayloadKey.COLLECT.equalsIgnoreCase((String) payload)) {
            adapter.notifyItemRangeChanged(0, adapter.getItemCount(), PayloadKey.COLLECT);
        } else if (payload instanceof RecyclerItemVisiblePositions) {
            int myPosition = helper.getBindingAdapterPosition() - adapter.getHeaderLayoutCount();
            RecyclerItemVisiblePositions positions = (RecyclerItemVisiblePositions) payload;
            if (positions.isVisible(myPosition)) {
                LabelScrollHandler.notifyScrollStateChanged(rv);
                adapter.notifyItemRangeChanged(0, adapter.getItemCount(), "TOP_X");
            } else {
                adapter.notifyItemRangeChanged(0, adapter.getItemCount(), RecyclerItemVisiblePositions.EMPTY);
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpProductsData item) {
        PdpProductsBean bean = item.t;
        helper.setText(R.id.tv_title, bean.title);
        RecyclerView rvList = helper.getView(R.id.rv_list);
        RecyclerView.Adapter<?> a = rvList.getAdapter();
        if (a instanceof PdpRecommendAdapter) {
            PdpRecommendAdapter adapter = (PdpRecommendAdapter) a;
            if (item.t.product_list != null && item.t.product_list.size() > 9) {
                item.t.product_list = item.t.product_list.subList(0, 9);
            }
            adapter.setNewData(item.t.product_list);
            adapter.setModInfo(item.modNm, item.modPos);//item line
            adapter.setCtxInfo(filterSubCategory, null, sort, null, pageTarget, null);
            adapter.setCtxInfo(item.traceId);
            adapter.setProductSource("app_product-" + bean.module_key);
            if (rvList.getItemDecorationCount() == 0) {
                rvList.addItemDecoration(new RecyclerView.ItemDecoration() {
                    @Override
                    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                        int layoutPosition = parent.getChildAdapterPosition(view);

                        outRect.top = layoutPosition < 3 ? 0 : CommonTools.dp2px(20);
                        outRect.left = CommonTools.dp2px(4);
                    }
                });
            }
        }

        helper.setOnViewClickListener(R.id.tv_store, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickTrack(item.productId, item.modPos, item.modNm, item.traceId);
                context.startActivity(SellerActivity.getIntent(context, String.valueOf(item.vendorId)));
            }
        });

    }


    @Override
    public void onCtxAdded(String filterSubCategory, String catalogueNum, String sort, Map<String, String> filters, String pageTarget, String pageTab, String globalVendor) {
        this.filterSubCategory = filterSubCategory;
        this.pageTarget = pageTarget;
        this.sort = sort;
    }

    @Override
    public void notifyPageDataSetChanged(RecyclerView view) {

    }

    private void clickTrack(int productId, int modPos, String modNm, String traceId) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(productId), null, null, null, traceId);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .setTargetNm(EagleTrackEvent.TargetNm.EXPLORE_MORE)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    @Override
    public void onPageResume(RecyclerView view) {
        if (tracker != null && !EmptyUtils.isEmpty(childAdapterCaches)) {
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter instanceof ImpressionChild) {
                        PdpRecommendAdapter recommendAdapter = (PdpRecommendAdapter) adapter;
                        RecyclerView attachView = recommendAdapter.getAttachView();
                        List<Integer> list = getVisibleList(attachView);
                        if (!EmptyUtils.isEmpty(list)) {
                            tracker.onPageResumeByPosition(attachView, list.get(0), list.get(list.size() - 1));
                        }
                    }
                    ProductSyncHelper.onPageResume(adapter);
                }
            }
        }
    }

    @Override
    public void onPagePause(RecyclerView view) {
        if (tracker != null) {
            tracker.onPagePause(view);
        }
    }

    @Override
    public void onPageScrollStateChanged(RecyclerView view, int status) {
        if (tracker != null && !EmptyUtils.isEmpty(childAdapterCaches) && status == RecyclerView.SCROLL_STATE_IDLE) {
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter instanceof ImpressionChild) {
                        PdpRecommendAdapter recommendAdapter = (PdpRecommendAdapter) adapter;
                        RecyclerView attachView = recommendAdapter.getAttachView();
                        List<Integer> list = getVisibleList(attachView);
                        if (!EmptyUtils.isEmpty(list)) {
                            tracker.trackImpressionByPosition(attachView, list.get(0), list.get(list.size() - 1));
                        }
                    }
                }
            }
        }
    }

    private List<Integer> getVisibleList(RecyclerView attachView) {
        RecyclerView.LayoutManager layout = attachView.getLayoutManager();
        List<Integer> list = new ArrayList<>();
        if (layout instanceof GridLayoutManager) {
            int firstVisibleItemPosition = ((GridLayoutManager) layout).findFirstVisibleItemPosition();
            int lastVisibleItemPosition = ((GridLayoutManager) layout).findLastVisibleItemPosition();
            for (int i = firstVisibleItemPosition; i <= lastVisibleItemPosition; i++) {
                View s = layout.findViewByPosition(i);
                boolean visible = ImpressionHelper.isImpressionEnableOnVertical(s);
                if (visible) {
                    list.add(i);
                }
            }
            return list;
        }
        return Collections.emptyList();
    }
}
