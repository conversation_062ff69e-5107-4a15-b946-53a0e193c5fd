package com.sayweee.weee.module.checkout.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

public class PreCheckoutOrderReviewsBean implements Serializable {

    public String id;
    public String type;
    public String biz_type;
    public ShippingInfoBean shipping_info;
    public int quantity;
    public String alcohol_desc;
    public VendorInfoBean vendor_info;
    public FeeInfoBeanX fee_info;
    public String tip_info;
    public PointInfoBeanX point_info;
    public String order_note;
    public boolean contain_forzen;
    public PreCheckoutV2Bean.CouponInfoBean coupon_info;
    public String sub_order_type;
    public boolean is_filter_pre;
    public int deal_id;
    public List<GroupBuyUserProduct> group_buy_user_products;
    public List<OrderLinesBean> order_lines;

    public static class GroupBuyUserProduct implements Serializable {
        public String adding_status;
        public TagInfoBean adding_tag;
        public int in_dtm;
        public boolean is_creator;
        public boolean is_owner;
        public String nick_name;
        public int sub_total;
        public String user_id;
        public List<OrderLinesBean> items;

        public static class ItemsBean implements Serializable {
            public int base_price;
            public boolean can_operate;
            public String img;
            public int in_dtm;
            public boolean is_mkpl;
            public boolean is_options;
            public int max_order_quantity;
            public int min_order_quantity;
            public String options_title;
            public int options_update_time;
            public int original_base_price;
            public int original_price;
            public int price;
            public int product_id;
            public String product_key;
            public int quantity;
            public String slug;
            public String source;
            public String title;
            public int total_price;
            public List<GroupBuyUserProduct.ItemsBean.OptionsBean> options;

            public static class OptionsBean implements Serializable {
                public String name_en;
                public String name_zh;
                public String name_zht;
                public int offset;
                public int op_id;
                public int op_qty;
                public int unit_price;
            }
        }
    }

    public static class ShippingInfoBean implements Serializable {
        public String delivery_mode;
        public String delivery_pickup_date;
        public String estimated_time;
        public String shipping_shipment_date;
        public int delivery_time_id;
        public String original_shipping_free_fee;
        public String shipping_free_fee;
        public String free_shipping_desc;
        public String free_shipping_desc_url;
        public String orignal_shipping_fee;
        public String shipping_fee;
        public String shipping_icon_url;
        public String shipping_type_desc;
        public String shipping_desc;
        public String shipping_delay_desc;
        public String short_shipping_delay_desc;
        public String cold_package_fee;
        public String orignal_cold_package_fee;
        public String free_cold_package_fee;
        public int shipping_fee_type;
        public String delivery_time;
        public String delivery_time_desc;
        public String delivery_content;
        public String shipping_minimum_threshold;
        public String hotdish_wave;
        public String hotdish_delivery_time_content;
        public String pickup_time;
        public String pickup_date;
        public String self_pickup_address;
        public String pick_up_point;
        public String eta_time_content;
        public boolean is_support_change_date;
        public List<DeliveryWindowBean> delivery_windows;
        public String delivery_window_content;

        @JSONField(serialize = false, deserialize = false)
        public boolean isShowShippingDialog() {
            return shipping_fee_type == 2;
        }
    }

    public static class FeeInfoBeanX implements Serializable {
        public String sub_total_price;
        public String shipping_fee;
        public String tax;
        public String tip;
        public String service_fee;
        public String points_price;
        public String activity_save_amount;
        public String total_price_with_activity;
        public String coupon_discount;
        public String final_amount;
        public String discount;
    }

    public static class PointInfoBeanX implements Serializable {
        public String points_current;
        public String points_price;
        public String all_deduction;
        public String order_reward_points;
        public String order_reward_points_desc;
        public String order_reward_points_desc_v2;
        public String total_reward_points_text;
        public String new_user_order_reward_points;
        public String order_reward_points_list;
    }

    public static class OrderLinesBean implements Serializable {
        public String title;
        public String sub_title;
        public int product_id;
        public String product_key;
        public String img;
        public int quantity;
        public String price_type;
        public double price;
        public double base_price;
        public String source;
        public String refer_type;
        public String storage_type;
        public String options_title;
        public String item_tip;
        public boolean is_colding_package;
        public String options;
        public List<TagInfoBean> tag_infos;
    }

    public static class VendorInfoBean implements Serializable {
        public int vendor_id;
        public String vendor_name;
    }
}
