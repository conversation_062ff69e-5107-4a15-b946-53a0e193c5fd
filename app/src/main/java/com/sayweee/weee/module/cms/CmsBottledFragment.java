package com.sayweee.weee.module.cms;

import android.app.Dialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.DialogMkplCouponClaimedBottomBinding;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.adapter.CmsBottledAdapter;
import com.sayweee.weee.module.cms.bean.CmsBackgroundStyle;
import com.sayweee.weee.module.cms.bean.CmsComponentRange;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.iml.coupon.data.CmsCouponBean;
import com.sayweee.weee.module.cms.iml.coupon.data.CmsCouponItemBean;
import com.sayweee.weee.module.cms.iml.coupon.widget.CmsCouponClaimHandler;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponClaimBean;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponListData;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponListItemBean;
import com.sayweee.weee.module.cms.iml.couponsingle.data.CmsSingleCouponData;
import com.sayweee.weee.module.cms.iml.couponsingle.data.CouponBatchClaimRequest;
import com.sayweee.weee.module.cms.iml.couponsingle.data.CouponBatchClaimResponse;
import com.sayweee.weee.module.cms.iml.pagenav.CmsPageNavProvider;
import com.sayweee.weee.module.cms.service2.CmsBottledViewModel;
import com.sayweee.weee.module.cms.utils.CmsTools;
import com.sayweee.weee.module.cms.widget.timer.CmsComponentTimerHandler;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedModelProvider;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.CouponClaimRequest;
import com.sayweee.weee.module.seller.bean.CouponClaimResponse;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.NestedRecyclerView;
import com.sayweee.weee.widget.banner.ex.PlayerHandler;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.snackbar.ToastySnackBarView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.weee.widget.timer.LifecycleAwareCountdownTimer;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.view.WrapperMvvmFragment;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public abstract class CmsBottledFragment<VM extends CmsBottledViewModel<? extends BaseLoaderModel<OrderApi>>>
        extends WrapperMvvmFragment<VM>
        implements BaseQuickAdapter.RequestLoadMoreListener,
        IContentFeedSharedModelProvider {

    protected CmsBottledAdapter adapter;
    private RecyclerViewScrollStatePersist scrollStatePersist;

    private final Runnable hideRemindTipsRunnable = this::hideRemindTips;

    private LifecycleAwareCountdownTimer countdownTimer;

    @Nullable
    protected final LifecycleOwner getSafeViewLifecycleOwner() {
        return getViewLifecycleOwnerLiveData().getValue();
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        initRecyclerViewAdapter(savedInstanceState);
        initRecyclerView(getRecyclerView());
    }

    @CallSuper
    @Override
    public void attachModel() {
        LifecycleOwner lifecycleOwner = getSafeViewLifecycleOwner();
        if (lifecycleOwner == null) {
            return;
        }

        // data related
        viewModel.pageParamData.observe(lifecycleOwner, this::onPageParamDataLoaded);
        viewModel.adapterData.observe(lifecycleOwner, this::onAdapterDataLoadedInternal);
        viewModel.adapterAppendData.observe(lifecycleOwner, this::onAdapterDataAppendInternal);
        viewModel.componentDataUpdateLiveData.observe(lifecycleOwner, adapter::notifyMultiDataSourceUpdate);

        // lightning deals related
        viewModel.lightningDealsRemindData.observe(lifecycleOwner, this::refreshRemindSet);

        // coupon claim related
        viewModel.couponClaimResponseLiveData.observe(lifecycleOwner, this::handleCouponClaimResponse);

        // cms single coupon claim related
        viewModel.couponBatchClaimResponseLiveData.observe(lifecycleOwner, this::handleCouponBatchClaimResponse);

        // cms countdown
        viewModel.countdownLiveData.observe(lifecycleOwner, this::handleCmsCountdownTimer);

        // product collect related
        SharedViewModel.get().collectsData.observe(lifecycleOwner, this::refreshProductCollect);
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (adapter != null) {
            adapter.onPageResume(getRecyclerView());
            ProductSyncHelper.onPageResume(adapter);
        }
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        if (adapter != null) {
            adapter.onPagePause(getRecyclerView());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        hideRemindTips();
    }

    @Override
    public void onLoadMoreRequested() {

    }

    protected void onPageParamDataLoaded(@Nullable CmsPageParam pageParam) {
        if (pageParam != null) {
            CmsBackgroundStyle backgroundStyle = pageParam.getBackgroundStyle();
            CmsTools.applyBackgroundStyle(getRecyclerView(), backgroundStyle, Color.WHITE);
        }
    }

    private void onAdapterDataLoadedInternal(@Nullable List<AdapterDataType> list) {
        if (scrollStatePersist != null) {
            scrollStatePersist.clearScrollState();
        }
        onAdapterDataLoaded(list);
    }

    protected abstract void onAdapterDataLoaded(@Nullable List<AdapterDataType> list);

    protected void onAdapterDataAppendInternal(@Nullable List<AdapterDataType> list) {
        onAdapterDataAppend(CollectionUtils.orEmptyList(list));
    }

    protected abstract void onAdapterDataAppend(@NonNull List<AdapterDataType> list);

    @NonNull
    protected abstract NestedRecyclerView getRecyclerView();

    protected boolean isLoadMoreEnable() {
        return false;
    }

    protected void initRecyclerViewAdapter(Bundle savedInstanceState) {
        adapter = new CmsBottledAdapter();
        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);
        adapter.setScrollStatePersist(scrollStatePersist);
        adapter.setOnLoadMoreListener(this, getRecyclerView());
        adapter.setEnableLoadMore(isLoadMoreEnable());
        adapter.setAttachedManager(CmsBottledFragment.this);
        adapter.setCmsMultiDataSourceListener(dataSource -> viewModel.requestMultiDataSource(dataSource));
        adapter.setOnIndicatorClickListener((index, entity) -> {
            int position = adapter.findContentFeedPosition();
            if (position > -1) {
                getRecyclerView().smoothScrollToPosition(position);
            }
        });
        adapter.setOnLightingDealTimerListener(new CmsComponentTimerHandler(getSafeViewLifecycleOwner()) {
            @Override
            public void onEndSafely(String componentId) {
                super.onEndSafely(componentId);
                onLightningDealsReloadRequested(componentId);
            }
        });
        adapter.setOnLightningDealRemindListener(this::onLightningDealRemind);
        adapter.setOnCmsCouponListItemClaimListener(this::onCmsCouponListItemClaim);
        adapter.setOnCmsCouponClaimListener(new CmsCouponClaimHandler(this));
        adapter.setOnCmsSingleCouponClaimListener(this::onCmsSingleCouponClaim);
        adapter.setOnCmsPageNavClickListener(new CmsPageNavProvider.OnCmsPageNavProviderClickListener() {
            @Override
            public void onIndicatorClick(int index, @Nullable CmsComponentRange.Item item) {
                onCmsNavClick(index, item);
            }

            @Override
            public void onPopUpClick(View view, List<CmsComponentRange.Item> rangeItems) {
                onCmsNavShowMoreClick(view, rangeItems);
            }
        });
    }

    protected void initRecyclerView(NestedRecyclerView recyclerView) {
        SafeStaggeredGridLayoutManager layoutManager;
        layoutManager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new SimpleSectionItemDecoration());
        recyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                onRecyclerViewScrolled(recyclerView, dx, dy);
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                onRecyclerViewScrollStateChanged(recyclerView, newState, oldState);
            }
        });
        recyclerView.setStickyHeight(getNestedRecyclerViewStickyHeight());
        recyclerView.setStickyListener(this::onChildNestedRecyclerViewStickyStateChange);
    }

    protected abstract int getNestedRecyclerViewStickyHeight();

    protected void onRecyclerViewScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
        boolean isTopMost = RecyclerViewTools.isTopMost(recyclerView);
        Pair<Integer, Integer> visibleItemPositionRange;
        visibleItemPositionRange = RecyclerViewTools.findVisibleItemPositionRange(recyclerView, false);
        onRecyclerViewScrolled(isTopMost, visibleItemPositionRange);
    }

    protected void onRecyclerViewScrolled(boolean isTop, Pair<Integer, Integer> visibleItemPositionRange) {
        // no op
    }

    protected void onRecyclerViewScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            if (adapter != null) {
                adapter.onPageScrollStateChanged(recyclerView, newState);
            }
            PlayerHandler.notifyScrollStateChanged(recyclerView);
            LabelScrollHandler.notifyScrollStateChanged(recyclerView);
        }
        OpActionHelper.notifyScrollStateChanged(newState, oldState);
    }

    protected void onChildNestedRecyclerViewStickyStateChange(boolean isChildStickyTop) {
        adapter.showContentFeedShadow(isChildStickyTop);
    }

    protected void onLightningDealsReloadRequested(String componentId) {

    }

    protected void onLightningDealRemind(LightningDealsProductBean bean, boolean remind, int position) {
        int productId = bean.id;
        View layoutRemindTips = getLightningDealsRemindLayout();
        if (layoutRemindTips != null) {
            if (remind) {
                layoutRemindTips.setTag(productId);
                layoutRemindTips.removeCallbacks(hideRemindTipsRunnable);
                layoutRemindTips.postDelayed(hideRemindTipsRunnable, 3000L);
                layoutRemindTips.setVisibility(View.VISIBLE);
            } else {
                if (layoutRemindTips.getVisibility() == View.VISIBLE) {
                    Object tag = layoutRemindTips.getTag();
                    if (tag instanceof Integer) {
                        if ((Integer) tag == productId) {
                            hideRemindTips();
                        }
                    }
                }
            }
        }
        viewModel.changeLightningDealsRemind(bean.id, remind);
    }

    @Nullable
    protected abstract View getLightningDealsRemindLayout();

    protected void hideRemindTips() {
        View layoutRemindTips = getLightningDealsRemindLayout();
        if (layoutRemindTips != null) {
            layoutRemindTips.removeCallbacks(hideRemindTipsRunnable);
            layoutRemindTips.setVisibility(View.GONE);
        }
    }

    protected void revokeRemind() {
        View layoutRemindTips = getLightningDealsRemindLayout();
        if (layoutRemindTips != null) {
            Object tag = layoutRemindTips.getTag();
            if (tag instanceof Integer) {
                adapter.revokeLightningDealRemind((Integer) tag);
                hideRemindTips();
                viewModel.changeLightningDealsRemind((Integer) tag, false);
            }
        }
    }

    protected void refreshRemindSet(Map<String, Object> map) {
        Object productId = CollectionUtils.getOrNull(map, "product_id");
        Object isRemind = CollectionUtils.getOrNull(map, "isRemind");
        if (!(productId instanceof Integer) || !(isRemind instanceof Boolean)) {
            return;
        }
        adapter.refreshLightningDealRemindSet((int) productId, (boolean) isRemind);
    }

    protected void refreshProductCollect(@Nullable Integer changeType) {
        adapter.refreshProductCollect();
    }

    private void onCmsCouponListItemClick(@NonNull CmsCouponListItemBean item) {
        FragmentActivity activity = getActivity();
        String linkUrl = item.useUrl;
        if (activity != null && linkUrl != null) {
            activity.startActivity(WebViewActivity.getIntent(activity, linkUrl));
        }
    }

    public void onCmsCouponListItemClaim(@NonNull CmsCouponListItemBean item, int position) {
        claimCoupon(item, position);
    }

    private void claimCoupon(@Nullable CmsCouponListItemBean coupon, int targetPosition) {
        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(activity));
            return;
        }

        if (coupon == null || !coupon.isCouponPlan()) return;
        int couponPlanId = coupon.planId;
        String sellerId = String.valueOf(coupon.sellerId);
        CouponClaimRequest request = new CouponClaimRequest(sellerId, couponPlanId);
        request.setTargetPosition(targetPosition);
        viewModel.claimCoupon(request);
    }

    protected void handleCouponClaimResponse(@Nullable CouponClaimResponse response) {
        if (response == null || !response.isSuccess()) {
            return;
        }

        CouponClaimBean data = response.requireResponse();
        CmsCouponClaimBean preload = new CmsCouponClaimBean();
        preload.coupon_id = data.coupon_id;
        preload.coupon_plan_id = data.coupon_plan_id;
        preload.apply_expiration_time = data.apply_expiration_time;
        preload.server_timestamp = data.server_timestamp;

        Pair<Integer, AdapterDataType> withIndex;
        withIndex = CollectionUtils.firstOrNullWithIndex(
                adapter.getData(),
                CmsCouponListData.class::isInstance
        );

        if (withIndex != null) {
            adapter.notifyItemChanged(withIndex.first, preload);

            CmsCouponListData coupons = (CmsCouponListData) withIndex.second;
            Pair<Integer, CmsCouponListItemBean> withIndex2;
            withIndex2 = CollectionUtils.firstOrNullWithIndex(
                    coupons.t.coupons,
                    c -> c != null && c.planId == data.coupon_plan_id
            );
            if (withIndex2 != null) {
                CmsCouponListItemBean couponItem = withIndex2.second;
                couponItem.id = data.coupon_id;
                couponItem.planId = data.coupon_plan_id;
                couponItem.endTime = data.apply_expiration_time;
                couponItem.serverTimestamp = data.server_timestamp;
                showCouponClaimedDialog(withIndex2.second, withIndex2.first);
            }
        }
    }

    private void showCouponClaimedDialog(CmsCouponListItemBean couponItem, int position) {
        new BottomDialog(activity) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_mkpl_coupon_claimed_bottom;
            }
        }.addHelperCallback((Dialog dialog, ViewHelper viewHelper) -> {
            DialogMkplCouponClaimedBottomBinding binding;
            binding = DialogMkplCouponClaimedBottomBinding.bind(viewHelper.itemView);
            Resources resources = binding.getRoot().getResources();

            binding.tvContent.setText(couponItem.promoteTitle);
            binding.tvContentSub.setText(couponItem.subTitle);

            String btnTitle = resources.getString(R.string.s_mkpl_coupon_shop_seller, couponItem.sellerTitle);
            binding.btnToSeller.setText(btnTitle);

            long deadline = TimeUnit.SECONDS.toMillis(couponItem.endTime);
            String date = DateUtils.getFormatTime(DateUtils.MM_DD_YY, deadline, "");
            String expireDate = resources.getString(R.string.s_mkpl_coupon_expires_date, date);
            binding.tvExpiredDate.setText(expireDate);

            ViewTools.setViewOnSafeClickListener(binding.btnToSeller, v -> {
                Map<String, Object> params;
                Map<String, Object> ctx;
                ctx = new EagleContext()
                        .setGlobalVendor("" + couponItem.sellerId)
                        .setTagId("" + couponItem.getMixedId())
                        .asMap();
                params = new EagleTrackModel.Builder()
                        .setMod_nm("coupon_popup")
                        .setMod_pos(-1)
                        .setTargetNm(EagleTrackEvent.TargetNm.USE)
                        .setTargetPos(position)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.NORMAL)
                        .setUrl(couponItem.useUrl)
                        .addCtx(ctx)
                        .build()
                        .getParams();
                AppAnalytics.logClickAction(params);

                onCmsCouponListItemClick(couponItem);
                dialog.dismiss();
            });

            ViewTools.setViewOnSafeClickListener(binding.ivCancel, v -> dialog.dismiss());
        }).show();
    }

    protected void onCmsSingleCouponClaim(@NonNull CmsSingleCouponData item) {
        claimCmsSingleCoupon(item);
    }

    private void claimCmsSingleCoupon(@NonNull CmsSingleCouponData item) {
        Context context = getContext();
        if (context == null) {
            return;
        }

        CmsCouponBean coupon = item.t;
        CmsCouponItemBean couponItem = CollectionUtils.firstOrNull(item.t.coupon_list);
        if (couponItem == null || !couponItem.canClaim()) {
            return;
        }

        if (AccountManager.get().isLogin()) {
            CouponBatchClaimRequest request;
            request = new CouponBatchClaimRequest(
                    coupon.all_plan_ids,
                    CollectionUtils.arrayListOf(couponItem.plan_id));
            request.setComponentId(item.getComponentId());
            viewModel.batchClaimCoupon(request);
        } else {
            context.startActivity(AccountIntentCreator.getIntent(context));
        }
    }

    protected void handleCouponBatchClaimResponse(@NonNull CouponBatchClaimResponse response) {
        if (!response.isSuccess()) {
            return;
        }
        CmsCouponBean newData = response.requireResponse();
        String tips = CollectionUtils.firstOrNull(newData.tips);
        if (!EmptyUtils.isEmpty(tips)) {
            showToast(tips);
        }
        String componentId = response.getRequest().getComponentId();
        if (!EmptyUtils.isEmpty(componentId)) {
            CmsDataSource dataSource = viewModel.getDataSource(componentId);
            if (dataSource != null) {
                viewModel.updateComponentData(dataSource, newData, null);
            }
        }
    }

    protected void handleCmsCountdownTimer(long endTime) {
        LifecycleAwareCountdownTimer countDownTimer = this.countdownTimer;
        if (countDownTimer == null) {
            countDownTimer = new LifecycleAwareCountdownTimer(this::onCmsCountdownReloadRequested);
            getLifecycle().addObserver(countDownTimer);
            this.countdownTimer = countDownTimer;
        }
        countdownTimer.start(endTime);
    }

    protected void onCmsCountdownReloadRequested() {

    }

    protected void showToast(@NonNull String title) {
        View rootView = getView();
        if (rootView == null) {
            return;
        }

        ToastySnackBarView snackBarView = new ToastySnackBarView(rootView.getContext());
        snackBarView.convert(new ActionSnackBarData(title));
        Toaster.asSnackBar(rootView)
                .setView(snackBarView)
                .build()
                .show(this);
    }

    protected void onCmsNavClick(int i, @Nullable CmsComponentRange.Item item) {

    }

    protected void onCmsNavShowMoreClick(View view, List<CmsComponentRange.Item> items) {

    }
}
