package com.sayweee.weee.module.product.manager;


import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cate.product.AffiliateListDialog;
import com.sayweee.weee.module.cate.product.bean.AffiliateListBean;
import com.sayweee.weee.module.product.bean.AffiliateListNewBean;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.weee.module.product.data.PdpAffiliateNewData;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.AffiliateDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Thomsen on 11/04/2025.
 */
public class ProductAffiliateManager implements AffiliateListDialog.AffiliateListDialogHandler {

    // 通知类型
    public static final String NOTIFY_SHOW_AFFILIATE_TOAST = "show_affiliate_toast";
    public static final String NOTIFY_REFRESH_PRODUCT_DATA = "refresh_product_data";

    private final FragmentActivity activity;
    private final ProductAffiliateRepository repository;

    private final ImageView ivAffiliate;
    private final ImageView ivToast;
    private final TextView tvToast;
    private final View layoutToast;

    private AffiliateListDialog affiliateListDialog; // old affiliate
    private AffiliateDialog dialog;
    private Runnable toastRunnable;

    private ProductPageParams pageParams;

    private boolean isAddNewAffiliate;
    private int newCreateAffiliateListID = 0;
    private final List<Boolean> serverAffiliateListBeanActives = new ArrayList<>();
    
    private final MutableLiveData<String> affiliateNotifyLiveData = new MutableLiveData<>();
    

    public ProductAffiliateManager(
            @NonNull FragmentActivity activity,
            @NonNull ProductAffiliateRepository repository,
            @NonNull ImageView ivAffiliate,
            @NonNull ImageView ivToast,
            @NonNull TextView tvToast,
            @NonNull View layoutToast,
            @NonNull ProductPageParams pageParams) {
        this.activity = activity;
        this.repository = repository;
        this.ivAffiliate = ivAffiliate;
        this.ivToast = ivToast;
        this.tvToast = tvToast;
        this.layoutToast = layoutToast;
        this.pageParams = pageParams;
        initObservers(activity);

    }

    /**
     * 设置Affiliate UI配置
     */
    public void setAffiliateConfig(@NonNull ProductPageParams pageParams) {
        ProductBean product = pageParams.product;
        if (product == null) return;

        // affiliate
        if (product instanceof ProductDetailBean.ProductFeatureBean) {
            ProductDetailBean.ProductFeatureBean pfb = (ProductDetailBean.ProductFeatureBean) product;
            ViewTools.setViewVisible(ivAffiliate, pfb.affiliate_show && !pfb.isGiftCard());
            ViewTools.setImageResource(ivAffiliate, pfb.affiliate_in ?
                    R.mipmap.ic_pdp_listchecks : R.mipmap.ic_pdp_listplus);

            ivAffiliate.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    getAffiliateNewLists();
                }
            });
        }
    }

    /**
     * 初始化观察者
     */
    private void initObservers(LifecycleOwner lifecycleOwner) {
        // 使用Repository的LiveData
        repository.getAffiliateListData().observe(lifecycleOwner, this::handleAffiliateListData);
        repository.getAffiliateListNewData().observe(lifecycleOwner, this::handleAffiliateListNewData);
        repository.getAffiliateListErrorData().observe(lifecycleOwner, this::handleAffiliateListErrorData);
        repository.getCreateAffiliateListData().observe(lifecycleOwner, this::handleCreateAffiliateListData);
        repository.getAddProduct2AffiliateListData().observe(lifecycleOwner, this::handleAddProduct2AffiliateListData);
        repository.getAddAffiliateData().observe(lifecycleOwner, this::handleAddAffiliateData);
    }
    
    /**
     * 处理获取到的旧版联盟列表数据
     */
    private void handleAffiliateListData(List<AffiliateListBean> affiliateListBeans) {
        serverAffiliateListBeanActives.clear();
        for (AffiliateListBean bean : affiliateListBeans) {
            serverAffiliateListBeanActives.add(bean.active);
        }
        if (affiliateListDialog == null) {
            affiliateListDialog = new AffiliateListDialog(activity)
                    .setHandler(this);
        }
        affiliateListDialog.updateData(affiliateListBeans, newCreateAffiliateListID);
        if (newCreateAffiliateListID > 0) {
            newCreateAffiliateListID = 0;
        }
        affiliateListDialog.show();
    }
    
    /**
     * 处理获取到的新版联盟列表数据
     */
    private void handleAffiliateListNewData(List<AffiliateListNewBean> affiliateListNewBeans) {
        showAffiliateDialog(affiliateListNewBeans);
    }
    
    /**
     * 处理联盟列表获取错误
     */
    private void handleAffiliateListErrorData(Boolean hasError) {
        if (hasError) {
            showAffiliateDialog(null);
        }
    }
    
    /**
     * 处理创建联盟列表结果
     */
    private void handleCreateAffiliateListData(Integer newAffiliateListID) {
        if (newAffiliateListID > 0) {
            this.newCreateAffiliateListID = newAffiliateListID;
            repository.getAffiliateLists(pageParams.productId);
        }
    }
    
    /**
     * 处理添加产品到旧版联盟列表结果
     */
    private void handleAddProduct2AffiliateListData(Boolean success) {
        if (success && affiliateListDialog != null) {
            affiliateListDialog.dismiss();
            affiliateListDialog = null;
            refreshProductData();
        }
    }
    
    /**
     * 处理添加产品到新版联盟列表结果
     */
    private void handleAddAffiliateData(String viewLink) {
        if (!EmptyUtils.isEmpty(viewLink)) {
            showAffiliateToast(viewLink);
        }
        refreshProductData();
    }


    /**
     * 获取通知 LiveData
     */
    public LiveData<String> getAffiliateNotifyLiveData() {
        return affiliateNotifyLiveData;
    }



    /**
     * 获取新的Affiliate列表
     */
    public void getAffiliateNewLists() {
        isAddNewAffiliate = true;
        repository.getAffiliateNewLists(pageParams.productId);
    }

    /**
     * 刷新产品数据
     */
    private void refreshProductData() {
        affiliateNotifyLiveData.postValue(NOTIFY_REFRESH_PRODUCT_DATA);
    }

    /**
     * 显示Affiliate对话框
     */
    private void showAffiliateDialog(@Nullable List<AffiliateListNewBean> affiliateList) {
        if (dialog == null) {
            dialog = (AffiliateDialog) new AffiliateDialog(activity).setAffiliateData(affiliateList, false).show();
            dialog.setOnCommitListener(new AffiliateDialog.OnCommitListener() {
                @Override
                public void onCommitResponse(int[] intIDs) {
                    repository.addProduct2NewAffiliateList(pageParams.productId, intIDs);
                }

                @Override
                public void onCommitToast(PdpAffiliateNewData item, int total) {
                    ProductBean product = pageParams.product;
                    if (!EmptyUtils.isEmpty(product)) {
                        String url = !EmptyUtils.isEmpty(product.media_urls) ? 
                                product.media_urls.get(0).url : product.img;
                        ImageLoader.load(activity, ivToast,
                                WebpManager.get().getConvertUrl(ImageSpec.SPEC_64, url),
                                R.color.color_place);
                    }
                    String title = total > 1 ? activity.getString(R.string.s_add_to_affiliate_list) : 
                            String.format(activity.getString(R.string.s_add_to_affiliate), item.t.title);
                    tvToast.setText(ViewTools.fromHtml(title));
                    ViewTools.setViewVisible(layoutToast, false);
                }
            });
        } else {
            if (isAddNewAffiliate) {
                if (affiliateList != null && !affiliateList.isEmpty()) {
                    affiliateList.get(0).active = true;
                }
            }
            dialog.setAffiliateData(affiliateList, isAddNewAffiliate);
            isAddNewAffiliate = false;
            if (!dialog.isShowing()) {
                dialog.show();
            }
        }
    }


    /**
     * 显示Affiliate Toast
     */
    private void showAffiliateToast(String toastLink) {
        ViewTools.setViewVisible(layoutToast, true);
        layoutToast.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                affiliateNotifyLiveData.postValue(NOTIFY_SHOW_AFFILIATE_TOAST + "|" + toastLink);
            }
        });
        toastRunnable = new Runnable() {
            @Override
            public void run() {
                ViewTools.setViewVisible(layoutToast, false);
            }
        };
        layoutToast.postDelayed(toastRunnable, 3000);
    }

    /**
     * 清理资源
     */
    public void onDestroy() {
        if (layoutToast != null && toastRunnable != null) {
            layoutToast.removeCallbacks(toastRunnable);
        }
        affiliateListDialog = null;
        dialog = null;
    }

    /**
     * 标记添加新Affiliate
     */
    public void markAddNewAffiliate() {
        isAddNewAffiliate = true;
    }

    @Override
    public void createAffiliate(String name, boolean isShareToEveryone) {
        repository.createAffiliateList(name, isShareToEveryone);
    }

    @Override
    public void addedToAffiliateList(List<AffiliateListBean> affiliateListBeans) {
        List<Integer> changedAffiliateListBeanIDs = new ArrayList<>();
        for (int i = 0; i < affiliateListBeans.size(); i++) {
            AffiliateListBean bean = affiliateListBeans.get(i);
            if (bean.active != serverAffiliateListBeanActives.get(i)) {
                changedAffiliateListBeanIDs.add(bean.id);
            }
        }
        if (changedAffiliateListBeanIDs.isEmpty()) {
            affiliateListDialog.dismiss();
            affiliateListDialog = null;
            return;
        }

        int[] intIDs = new int[changedAffiliateListBeanIDs.size()];
        for (int i = 0; i < changedAffiliateListBeanIDs.size(); i++) {
            intIDs[i] = changedAffiliateListBeanIDs.get(i);
        }
        repository.addProduct2AffiliateList(pageParams.productId, intIDs);
    }
}
