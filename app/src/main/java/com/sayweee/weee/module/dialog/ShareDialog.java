package com.sayweee.weee.module.dialog;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.util.Base64;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.Target;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.post.helper.CommentHelper;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.share.ShareHelper;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.widget.component.DrawableTextView;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.zhy.view.flowlayout.FlowLayout;

import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/19.
 * Desc:
 */
public class ShareDialog extends WrapperDialog {
    protected static final String UTM_SOURCE = "utm_source=";

    protected ShareBean shareBean;
    protected String shareLanguageString;

    protected OnShareListener listener;
    protected OnShareListener channelListener;
    protected ShareBean.ShareContentBean currentData;
    protected ShareHelper.ShareCallback callback;

    public ShareDialog(Context context) {
        super(context, R.style.BottomDialogTheme);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.dialog_share;
    }

    @Override
    protected void setDialogParams(Dialog dialog) {
        setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
    }

    @Override
    public void help(ViewHelper helper) {
        int corner = CommonTools.dp2px(20);
        helper.setBackgroundDrawable(R.id.layout_share, ShapeHelper.buildSolidDrawable(ContextCompat.getColor(context, R.color.color_fore), corner, corner, 0, 0));
        helper.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (v.getId() == R.id.iv_close) {
                    dismiss();
                }
            }
        }, R.id.iv_close);
    }

    private static boolean isShareTypeImage(ShareBean bean) {
        if (bean != null && ShareBean.SHARE_TYPE_IMAGE.equals(bean.share_type)) {
            return bean.share_data != null && !EmptyUtils.isEmpty(bean.share_data.image_data);
        }
        return false;
    }

    @Nullable
    protected static byte[] parseImageData(@Nullable String data) {
        if (data == null) {
            return null;
        }
        if (data.startsWith("data:image/png;base64") || data.startsWith("data:image/jpeg;base64")) {
            data = data.split(",")[1];
        }
        byte[] imageByteArray;
        try {
            imageByteArray = Base64.decode(data, Base64.DEFAULT);
        } catch (Exception ignored) {
            imageByteArray = null;
        }
        return imageByteArray;
    }

    public ShareDialog setShareData(ShareBean bean) {
        this.shareBean = bean;
        addHelperCallback((dialog, helper) -> {
            if (isShareTypeImage(bean)) {
                bindShareImageData(dialog, helper, bean);
            } else {
                bindShareDataNormal(dialog, helper, bean);
            }
        });
        return this;
    }

    private void bindShareImageData(Dialog dialog, ViewHelper helper, final ShareBean bean) {
        Context context = helper.itemView.getContext();
        ShareHelper.cleanupSharedImages(context);

        ShareBean.ShareDataBean shareData = bean.share_data;
        ImageView imageView = helper.getView(R.id.iv_share_image);
        if (shareData != null && shareData.image_data != null) {
            byte[] imageByteArray = parseImageData(shareData.image_data);
            Glide.with(context)
                    .load(imageByteArray)
                    .override(Target.SIZE_ORIGINAL, CommonTools.dp2px(230))
                    .into(imageView);
            helper.setVisible(R.id.layout_image_content, true);
        } else {
            helper.setVisible(R.id.layout_image_content, false);
        }

        helper.setVisible(R.id.tv_share_tips, false);
        helper.setVisible(R.id.layout_language, false);
        helper.setVisible(R.id.layout_content, false);
        helper.setVisible(R.id.divider, false);

        fillShareChannels(helper, ShareBean.SHARE_TYPE_IMAGE, bean.share_channels);
    }

    protected void bindShareDataNormal(Dialog dialog, ViewHelper helper, final ShareBean bean) {
        HtmlTextView tvShareTips = helper.getView(R.id.tv_share_tips);
        boolean displayTips = (bean.share_tips != null) && (!bean.share_tips.isEmpty());
        helper.setVisible(R.id.tv_share_tips, displayTips);
        if (displayTips) {
            tvShareTips.setHtmlText(bean.share_tips);
        }

        List<ShareBean.ShareContentBean> list = bean.share_content;
        boolean displayContent = list != null && !list.isEmpty();
        helper.setVisible(R.id.layout_language, bean.show_language && displayContent);
        helper.setVisible(R.id.layout_content, displayContent);

        if (displayContent) {
            if (bean.show_language) {
                String language = LanguageManager.get().getLanguage();
                currentData = getDataByLanguage(list, language);
                fillShareLanguage(helper.getView(R.id.layout_language), list);
            } else {
                currentData = list.get(0);
                fillProductData(helper, currentData);
            }
        }

        helper.setVisible(R.id.divider, displayContent || displayTips);

        fillShareChannels(helper, null, bean.share_channels);
    }

    protected void fillShareLanguage(FlowLayout container, List<ShareBean.ShareContentBean> list) {
        list = CollectionUtils.orEmptyList(list);
        container.removeAllViews();
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, CommonTools.dp2px(32));
        params.rightMargin = CommonTools.dp2px(8);
        params.topMargin = CommonTools.dp2px(12);
        int selectedIndex = -1;
        String languageName;
        for (int i = 0; i < list.size(); i++) {
            ShareBean.ShareContentBean bean = list.get(i);
            if (bean.language != null) {
                switch (bean.language) {
                    case LanguageManager.Language.CHINESE:
                    case LanguageManager.Language.CHINESE_OLD:
                        if (LanguageManager.get().isSimpleChinese()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_chinese);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.TRADITIONAL_CHINESE:
                        if (LanguageManager.get().isTraditionalChinese()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_traditional_chinese);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.KOREAN: //移除韩语支持
                        if (LanguageManager.get().isKorean()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_korea);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.JAPANESE: //移除韩语支持
                        if (LanguageManager.get().isJapanese()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_japanese);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.SPANISH:
                        if (LanguageManager.get().isSpanish()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_spanish);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.PORTUGUESE:
                        if (LanguageManager.get().isPortuguese()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_portuguese);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.ENGLISH:
                        if (LanguageManager.get().isEnglish()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_english);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.VIETNAMESE:
                        if (LanguageManager.get().isVietnamese()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_vietnamese);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    case LanguageManager.Language.THAI:
                        if (LanguageManager.get().isThai()) {
                            selectedIndex = i;
                        }
                        languageName = context.getString(R.string.s_share_language_thai);
                        addLanguageView(container, params, list, bean.language, languageName, i);
                        break;
                    default:
                        break;
                }
            }
        }
        if (list.size() == 1) {
            ShareBean.ShareContentBean bean = list.get(0);
            if (bean != null && bean.language != null) {
                selectedIndex = 0;
                onLanguageViewClick(container, list, bean.language, selectedIndex);
            }
        } else {
            onLanguageViewClick(container, list, LanguageManager.get().getLanguage(), selectedIndex);
        }
    }

    protected void fillShareChannels(ViewHelper helper, String shareType, List<String> channels) {
        LinearLayout layout = helper.getView(R.id.layout_share_channels);
        layout.removeAllViews();
        LinearLayout secondLayout = helper.getView(R.id.layout_second_channels);
        secondLayout.removeAllViews();
        for (int i = 0, len = channels != null ? channels.size() : 0; i < len; i++) {
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(CommonTools.dp2px(70), LinearLayout.LayoutParams.WRAP_CONTENT);
            String channel = channels.get(i);
            if (AppFilter.ShareConfig.isNotSupport(channel)) {
                continue;
            }
            switch (channel) {
                case Constants.ShareChannel.FACEBOOK:
                    if (isAvailable(shareType, channel)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_facebook), R.mipmap.share_facebook), params);
                    }
                    break;
                case Constants.ShareChannel.MESSENGER:
                    if (isAvailable(shareType, channel) && ShareHelper.isMessengerInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_messenger), R.mipmap.share_messenger), params);
                    }
                    break;
                case Constants.ShareChannel.SMS:
                    if (isAvailable(shareType, channel)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_sms), R.mipmap.share_sms), params);
                    }
                    break;
                case Constants.ShareChannel.WECHAT:
                    if (isAvailable(shareType, channel) && ShareHelper.isWxInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_wx), R.mipmap.share_wx), params);
                    }
                    break;
                case Constants.ShareChannel.WECHAT_MOMENT:
                    if (isAvailable(shareType, channel) && ShareHelper.isWxInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_moments), R.mipmap.share_moment), params);
                    }
                    break;
                case Constants.ShareChannel.WHATSAPP:
                    if (isAvailable(shareType, channel) && ShareHelper.isWhatsAppInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_whatsapp), R.mipmap.share_whatsapp), params);
                    }
                    break;
                case Constants.ShareChannel.KAKAO:
                    if (isAvailable(shareType, channel) && ShareHelper.isKakaoInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_kakao), R.mipmap.share_kakao), params);
                    }
                    break;
                case Constants.ShareChannel.INSTAGRAM:
                    if (isAvailable(shareType, channel) && ShareHelper.isInstagramInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_instagram), R.mipmap.share_instagram), params);
                    }
                    break;
                case Constants.ShareChannel.LINE:
                    if (isAvailable(shareType, channel) && ShareHelper.isLineInstall(context)) {
                        layout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_line), R.mipmap.share_line), params);
                    }
                    break;
                case Constants.ShareChannel.IMAGE:
                    if (isAvailable(shareType, channel)) {
                        secondLayout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_get_image), R.mipmap.share_image), params);
                    }
                    break;
                case Constants.ShareChannel.COPY_LINK:
                    if (isAvailable(shareType, channel)) {
                        secondLayout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_copy_link), R.mipmap.share_link), params);
                    }
                    break;
                case Constants.ShareChannel.MORE:
                    if (isAvailable(shareType, channel)) {
                        secondLayout.addView(createChannelView(channel, context.getString(R.string.s_share_channel_more), R.mipmap.share_more), params);
                    }
                    break;
                default:
                    break;
            }
        }
        helper.setVisible(R.id.layout_share_channels_parent, layout.getChildCount() > 0);
        helper.setVisible(R.id.layout_second_channels_parent, secondLayout.getChildCount() > 0);
    }

    protected static boolean isAvailable(String shareType, String channel) {
        if (ShareBean.SHARE_TYPE_IMAGE.equals(shareType)) {
            switch (EmptyUtils.orEmpty(channel)) {
                case Constants.ShareChannel.FACEBOOK:
                case Constants.ShareChannel.MESSENGER:
                case Constants.ShareChannel.WECHAT:
                case Constants.ShareChannel.WECHAT_MOMENT:
                case Constants.ShareChannel.WHATSAPP:
                case Constants.ShareChannel.INSTAGRAM:
                case Constants.ShareChannel.IMAGE:
                case Constants.ShareChannel.MORE:
                    return true;
                default:
                    return false;
            }
        }
        return true;
    }

    protected void fillProductData(ViewHelper helper, ShareBean.ShareContentBean bean) {
        helper.setViewVisible(R.id.layout_content);
        ImageLoader.load(context, helper.getView(R.id.iv_image), WebpManager.get().getConvertUrl(SPEC_PRODUCT, bean.share_img_url), R.color.color_back);
        helper.setText(R.id.tv_name, bean.title);
        if (!EmptyUtils.isEmpty(bean.description)) {
            helper.setText(R.id.tv_desc, CommentHelper.resolveStyle(bean.description, Color.parseColor("#666666"), false, null));
        }
    }

    protected View createChannelView(String channel, String channelName, int channelImage) {
        DrawableTextView view = new DrawableTextView(context);
        view.setText(channelName);
        view.setCompoundDrawablePadding(CommonTools.dp2px(8));
        view.setTopDrawable(ContextCompat.getDrawable(context, channelImage), CommonTools.dp2px(52), CommonTools.dp2px(52));
        view.setTextAppearance(context, R.style.style_fluid_root_heading_2xs);
        view.setGravity(Gravity.CENTER_HORIZONTAL);
        view.setTextColor(ContextCompat.getColor(context, R.color.color_surface_1_fg_minor_idle));
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onChannelClick(channel);
            }
        });
        return view;
    }

    protected void addLanguageView(FlowLayout container, LinearLayout.LayoutParams params, List<ShareBean.ShareContentBean> list, String language, String languageName, int index) {
        ShapeTextView view = new ShapeTextView(context);
        view.setText(languageName);
        view.setTag(language);
        view.setGravity(Gravity.CENTER);
        view.setTextColor(Color.WHITE);
        view.setMinimumWidth(CommonTools.dp2px(48));
        view.setTextAppearance(context, R.style.style_fluid_root_button_label_xs);
        view.setPadding(CommonTools.dp2px(12), 0, CommonTools.dp2px(12), 0);
        view.setBackgroundResource(R.drawable.shape_corner_back);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onLanguageViewClick(container, list, language, index);
            }
        });
        container.addView(view, params);
    }

    protected void onLanguageViewClick(FlowLayout container, List<ShareBean.ShareContentBean> list, String language, int selectIndex) {
        currentData = getDataByLanguage(list, language);

        shareLanguageString = language;

        if (currentData == null) {
            currentData = list != null ? list.get(0) : null;
        }

        if (currentData != null) {
            fillProductData(helper, currentData);
        }

        int count = container.getChildCount();
        for (int i = 0; i < count; i++) {
            View view = container.getChildAt(i);
            if (view instanceof TextView) {
                if (i == selectIndex) {
                    ((TextView) view).setTextColor(ContextCompat.getColor(context, R.color.color_primary_surface_1_fg_default_idle));
                    view.setBackgroundResource(R.drawable.shape_corner_blue);
                } else {
                    ((TextView) view).setTextColor(ContextCompat.getColor(context, R.color.color_tertiary_surface_1_fg_default_idle));
                    view.setBackgroundResource(R.drawable.shape_corner_back);
                }
            }
        }
    }

    protected void onChannelClick(String channel) {
        if (listener != null) {
            listener.share(this, channel);
        } else {
            if (isShareTypeImage(shareBean)) {
                onChannelClickImage(shareBean, channel);
            } else if (channel != null) {
                onChannelClickNormal(channel);
            }
            dismiss();
        }
        //点击事件埋点
        int targetPos = 0;
        if (!EmptyUtils.isEmpty(shareBean.share_channels)) {
            targetPos = shareBean.share_channels.indexOf(channel);
        }
        Map<String, Object> ctx = new ArrayMap<>();
        ctx.put("share_language", shareLanguageString);
        EagleTrackManger.get().trackEagleClickAction(null,
                -1,
                null,
                -1,
                channel,
                targetPos,
                "share_channel",
                EagleTrackEvent.ClickType.SHARE,
                ctx);
    }

    protected void onChannelClickImage(@NonNull ShareBean shareBean, @NonNull String channel) {
        byte[] imageByteArray = parseImageData(shareBean.share_data.image_data);
        if (imageByteArray != null) {
            Bitmap bitmap = BitmapFactory.decodeByteArray(imageByteArray, 0, imageByteArray.length);
            ShareHelper.execShareImage(channel, bitmap, resultTip, callback);
        }
        if (channelListener != null) {
            channelListener.share(this, channel);
        }
    }

    protected void onChannelClickNormal(@NonNull String channel) {
        ShareBean.ShareContentBean data = getCurrentData();
        if (data != null) {
            String linkUrl = data.link_url;

            if (attachedId > 0) {
                if (linkUrl.contains("?")) {
                    linkUrl = linkUrl + "&" + UTM_SOURCE + channel;
                } else {
                    linkUrl = linkUrl + "?" + UTM_SOURCE + channel;
                }

                SharedViewModel.get().trackShare(attachedId, attachedType, channel);
            }

            String title = data.title;
            String description = data.description;

            if (attachedId > 0 && Constants.ShareChannel.SMS.equalsIgnoreCase(channel)) {
                title = null;
                description = null;
            }
            ShareHelper.execShare(channel, data.share_img_url, title, description, linkUrl, shareBean != null ? shareBean.view_link : null, data.language, resultTip, callback);

            if (channelListener != null) {
                channelListener.share(this, channel);
            }
        }
    }

    protected ShareBean.ShareContentBean getDataByLanguage(List<ShareBean.ShareContentBean> list, String language) {
        if (list != null && list.size() > 0) {
            for (ShareBean.ShareContentBean bean : list) {
                if (language.equalsIgnoreCase(bean.language)) {
                    return bean;
                }
            }
        }
        return null;
    }

    public ShareBean.ShareContentBean getCurrentData() {
        return currentData;
    }

    //用于埋点
    protected int attachedId;
    protected String attachedType;
    protected String resultTip;
    protected String screenName;

    public ShareDialog setScreenName(String screenName) {
        this.screenName = screenName;
        return this;
    }

    public ShareDialog setAttachedData(int attachedId, String attachedType) {
        this.attachedId = attachedId;
        this.attachedType = attachedType;
        return this;
    }

    public ShareDialog setResultTip(String resultTip) {
        this.resultTip = resultTip;
        return this;
    }

    public ShareDialog setOnShareListener(OnShareListener listener) {
        this.listener = listener;
        return this;
    }

    public interface OnShareListener {
        void share(ShareDialog dialog, String type);
    }

    public ShareDialog setOnShareChannelListener(OnShareListener listener) {
        this.channelListener = listener;
        return this;
    }

    public ShareDialog setOnShareResultCallback(ShareHelper.ShareCallback callback) {
        this.callback = callback;
        return this;
    }


}
