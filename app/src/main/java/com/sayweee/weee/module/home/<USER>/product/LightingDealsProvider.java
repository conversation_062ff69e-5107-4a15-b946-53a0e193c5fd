package com.sayweee.weee.module.home.provider.product;

import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.cart.adapter.OnCartEditListener;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LoyaltyHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.adapter.payload.CmsMultiDataSourceUpdate;
import com.sayweee.weee.module.cms.config.CmsConstants;
import com.sayweee.weee.module.cms.widget.timer.WrapperOnCmsComponentTimerListener;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.adapter.LightningDealsNewAdapter;
import com.sayweee.weee.module.home.adapter.OnRemindListener;
import com.sayweee.weee.module.home.bean.LightningDealsBean;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.timer.OnTimerListener;

import java.util.List;

public class LightingDealsProvider extends SimpleHorizontalImpressionProvider<CmsLightingDealsData, AdapterViewHolder> {

    @Override
    public int getItemType() {
        return CmsItemType.LIGHTNING_DEALS;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_lightning_deals;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        LoyaltyHelper.get().remove((LifecycleOwner) context);
        LoyaltyHelper.get().attach((LifecycleOwner) context); // this 是 LifecycleOwner
        RecyclerView recyclerView = helper.getView(R.id.rv_lightning_deals);
        recyclerView.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        recyclerView.setNestedScrollingEnabled(false);
        LightningDealsNewAdapter adapter = new LightningDealsNewAdapter();
        adapter.setProductDisplayStyle(productDisplayStyle);
        adapter.setAttachView(recyclerView);
        recyclerView.setAdapter(adapter);
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    RecyclerView.Adapter<?> a = recyclerView.getAdapter();
                    if (a instanceof BaseQuickAdapter) {
                        onPageScrollStateChangedImpression((BaseQuickAdapter<?, ?>) a);
                    }
                }
            }
        });
        addAdapterToCache(adapter);
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, CmsLightingDealsData item, @NonNull List<Object> payloads) {
        RecyclerView recyclerView = helper.getView(R.id.rv_lightning_deals);
        if (recyclerView == null) {
            return;
        }
        RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        if (adapter == null) {
            return;
        }
        for (Object object : payloads) {
            if (object instanceof Integer) {
                recyclerView.getAdapter().notifyItemChanged((Integer) object, item);
            } else if (object instanceof CmsMultiDataSourceUpdate) {
                convert(helper, item);
            } else if (ProductTraceViewHelper.shouldConvertPayload(object)) {
                ProductTraceViewHelper.notify(recyclerView);
            } else {
                for (int i = 0; i < item.t.products.size(); i++) {
                    ProductBean bean = item.t.products.get(i);
                    if (bean.isDirty()) {
                        recyclerView.getAdapter().notifyItemChanged(i, item);
                    }
                }
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsLightingDealsData item) {
        View rootView = helper.getView(R.id.item_home_lightning_deals_root);
        RecyclerView rvLightningDeals = helper.getView(R.id.rv_lightning_deals);
        RecyclerView.Adapter<?> a = rvLightningDeals.getAdapter();
        if (!item.isValid()) {
            ViewTools.setViewVisibilityIfChanged(rootView, false);
            if (a instanceof LightningDealsNewAdapter) {
                ((LightningDealsNewAdapter) a).setOnTimerListener(null);
                ((LightningDealsNewAdapter) a).setNewData(CollectionUtils.emptyList());
            }
            return;
        }

        ViewTools.setViewVisibilityIfChanged(rootView, true);
        String linkUrl = item.getLinkUrl();
        helper.setVisibleCompat(item.isShowLoyalty(), R.id.iv_icon, R.id.iv_info);
        if (item.isShowLoyalty()) {
            ImageLoader.load(context, helper.getView(R.id.iv_icon), item.getIcon(), R.color.color_place);
            helper.setOnViewClickListener(new OnSafeClickListener(2000) {
                @Override
                public void onClickSafely(View v) {
                    LoyaltyHelper.get().getLoyaltyIntroduction(context);
                }
            }, R.id.v_loyalty);
        }
        View view = helper.getView(R.id.v_title);
        if (!EmptyUtils.isEmpty(linkUrl) && !item.isShowLoyalty()) {
            view.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    trackEagleClickAction(item.getEventKey(), item.position
                            , null
                            , -1
                            , EagleTrackEvent.TargetNm.EXPLORE_MORE
                            , -1
                            , null
                            , EagleTrackEvent.ClickType.VIEW);
                    toWebViewWithEvent(item.getEventKey(), linkUrl);
                }
            });
        } else {
            view.setOnClickListener(null);
        }
        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                trackEagleClickAction(item.getEventKey(), item.position
                        , null
                        , -1
                        , EagleTrackEvent.TargetNm.EXPLORE_MORE
                        , -1
                        , null
                        , EagleTrackEvent.ClickType.VIEW);
                toWebViewWithEvent(item.getEventKey(), linkUrl);
            }
        }, R.id.iv_arrow);
        helper.setVisibleCompat(R.id.iv_arrow, !EmptyUtils.isEmpty(linkUrl));
        ViewTools.applyTextColor2(helper.getView(R.id.iv_arrow), item.getProperty().getTitleColor(), R.color.color_surface_100_fg_default);
        helper.setText(R.id.tv_title, item.getTitle());
        ViewTools.applyTextColor2(helper.getView(R.id.tv_title), item.getProperty().getTitleColor(), R.color.color_surface_100_fg_default);
        int titleStyle;
        switch (item.getTitleFontSize()) {
            case CmsConstants.TITLE_FONT_SIZE_LARGE:
                titleStyle = R.style.style_fluid_root_heading_lg;
                break;
            case CmsConstants.TITLE_FONT_SIZE_SMALL:
                titleStyle = R.style.style_fluid_root_heading_sm;
                break;
            default:
                titleStyle = R.style.style_heading_sm;
                break;
        }
        ViewTools.applyTextStyle(helper.getView(R.id.tv_title), titleStyle);

        if (a instanceof LightningDealsNewAdapter) {
            LightningDealsNewAdapter adapter = (LightningDealsNewAdapter) a;
            LightningDealsBean bean = item.t;
            adapter.setShowProgress(bean.show_progress);
            adapter.setModInfo(item.getEventKey(), item.position);
            adapter.setOnTimerListener(new WrapperOnCmsComponentTimerListener(item.getComponentId(), onTimerListener));
            adapter.setOnRemindListener(onRemindListener);
            adapter.setOnCartEditListener(onCartEditListener);
            adapter.setAttachView(rvLightningDeals);
            adapter.setAdapterModule(item.getEventKey());
            boolean showMore = bean.total_count > bean.products.size();
            adapter.setData(bean.products, bean.current_timestamp, bean.system_timestamp, showMore, linkUrl);
            onPageScrollStateChangedImpression(adapter);
        }
    }

    @Override
    public void onPageResumeImpression(BaseQuickAdapter adapter) {
        super.onPageResumeImpression(adapter);
        ProductSyncHelper.onPageResume(adapter);
    }

    private void toWebViewWithEvent(String moduleName, String url) {
        if (!TextUtils.isEmpty(url)) {
            context.startActivity(WebViewActivity.getIntent(context, url));
        }
    }

    private void trackEagleClickAction(
            String modNm, int modPos, String secNm, int secPos, String targetNm,
            int targetPos, String targetType, String clickType
    ) {
        EagleTrackManger.get().trackEagleClickAction(modNm, modPos, secNm, secPos, targetNm, targetPos, targetType, clickType);
    }

    OnTimerListener onTimerListener;

    public void setOnTimerListener(OnTimerListener listener) {
        this.onTimerListener = listener;
    }

    OnRemindListener onRemindListener;

    public void setOnRemindListener(OnRemindListener onRemindListener) {
        this.onRemindListener = onRemindListener;
    }

    private OnCartEditListener onCartEditListener = null;

    public LightingDealsProvider setOnCartEditListener(OnCartEditListener onCartEditListener) {
        this.onCartEditListener = onCartEditListener;
        return this;
    }

    protected int productDisplayStyle = ProductView.STYLE_ITEM_SMALL;

    public LightingDealsProvider setProductDisplayStyle(@ProductView.DisplayStyle int productDisplayStyle) {
        this.productDisplayStyle = productDisplayStyle;
        return this;
    }
}
