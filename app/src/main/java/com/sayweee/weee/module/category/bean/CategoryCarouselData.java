package com.sayweee.weee.module.category.bean;

import com.sayweee.weee.module.category.adapter.CategoryItemType;
import com.sayweee.weee.module.home.bean.adapter.BannerHelper;

import java.util.List;

public class CategoryCarouselData extends CategoryItemData<List<CategoryCarouselBean>> {

    protected boolean autoplay;
    protected boolean loop;
    protected int loop_interval;
    public int productId;
    public String traceId;
    public int bannerId, bannerPosition;
    public String bannerUrl;
    public CategoryCarouselData(List<CategoryCarouselBean> list) {
        super(CategoryItemType.CAROUSEL, list);
    }

    public void setBannerParams(boolean autoplay, boolean loop, int loop_interval) {
        this.autoplay = autoplay;
        this.loop = loop;
        this.loop_interval = loop_interval;
    }

    public void setId(int productId, String traceId){
        this.productId = productId;
        this.traceId = traceId;
    }

    public void setBannerData(int bannerId, int bannerPosition, String url) {
        this.bannerId = bannerId;
        this.bannerPosition = bannerPosition;
        this.bannerUrl = url;
    }

    public boolean isAutoplay() {
        return t != null && t.size() > 1 && autoplay;
    }

    public boolean isLoop() {
        return t != null && t.size() > 1 && (loop || autoplay);
    }

    public int getLoopInterval() {
        return BannerHelper.getBannerLoopInterval(loop_interval);
    }
}
