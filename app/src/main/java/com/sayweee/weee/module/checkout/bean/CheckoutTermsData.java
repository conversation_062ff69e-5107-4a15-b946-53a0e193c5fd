package com.sayweee.weee.module.checkout.bean;

import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;

public class CheckoutTermsData extends SimpleAdapterDataType {

    public final boolean containFrozen;
    public final int alcoholAgreementType; //酒精协议类型
    public boolean showDefaultTerm = true;//默认显示

    public CheckoutTermsData(int type, boolean containFrozen, int alcoholAgreementType) {
        super(type);
        this.containFrozen = containFrozen;
        this.alcoholAgreementType = alcoholAgreementType;
    }
}
