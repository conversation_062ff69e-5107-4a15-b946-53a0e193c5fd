package com.sayweee.weee.module.gallery;

import static android.os.ext.SdkExtensions.getExtensionVersion;

import android.Manifest;
import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.luck.picture.lib.dialog.PictureCustomDialog;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.luck.picture.lib.permissions.PermissionChecker;
import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.service.helper.GalleryPickerHelper;
import com.sayweee.weee.utils.FileUtils;
import com.sayweee.wrapper.utils.PermissionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class GalleryPickerActivity extends ComponentActivity {

    private static final boolean logEnable = false;
    private static final int requestCode = 1000;

    ActivityResultLauncher<Intent> multiLauncher;
    GalleryPickerConfig config;

    public static Intent getIntent(Context context, GalleryPickerConfig config) {
        return new Intent(context, GalleryPickerActivity.class).putExtra("config", config);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Serializable serializable = getIntent().getSerializableExtra("config");
        if (serializable instanceof GalleryPickerConfig) {
            config = (GalleryPickerConfig) serializable;
        }
        if (config == null) {
            dispatchResult(null);
            return;
        }
        multiLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback<ActivityResult>() {
            @Override
            public void onActivityResult(ActivityResult result) {
                List<Uri> list = new ArrayList<>();
                if (result.getResultCode() == Activity.RESULT_OK) {
                    Intent data = result.getData();
                    ClipData clipData = data.getClipData();
                    if (clipData != null && clipData.getItemCount() > 0) {
                        //多张图片
                        for (int i = 0; i < clipData.getItemCount(); i++) {
                            ClipData.Item item = clipData.getItemAt(i);
                            Uri uri = item.getUri();
                            if (uri != null) {
                                list.add(uri);
                            }
                        }
                    } else {
                        //单张图片
                        Uri uri = data.getData();
                        if (uri != null) {
                            list.add(uri);
                        }
                    }
                }
                dispatchResult(list);
            }
        });
        boolean isNoNeedPermission = isPhotoPickerAvailable();
        if (!isNoNeedPermission) {
            boolean hasExternalStorage = PermissionChecker.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE);
            if (!hasExternalStorage) {
                PermissionUtils.requestPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE, requestCode);
                return;
            }
        }
        loadData();
    }

    @Override
    public void onRequestPermissionsResult(int code, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(code, permissions, grantResults);
        if (code != requestCode) {
            return;
        }
        PermissionUtils.onRequestPermissionResult(this, Manifest.permission.READ_EXTERNAL_STORAGE, grantResults, new PermissionUtils.PermissionCheckCallBack() {
            @Override
            public void onHasPermission() {
                Logger.enable(logEnable).json("===========>", "onHasPermission");
                loadData();
            }

            @Override
            public void onUserHasAlreadyTurnedDown(String... permission) {
                showPermissionsDialog();
                Logger.enable(logEnable).json("===========>", "onUserHasAlreadyTurnedDown");
            }

            @Override
            public void onUserHasAlreadyTurnedDownAndNotAsk(String... permission) {
                showPermissionsDialog();
                Logger.enable(logEnable).json("===========>", "onUserHasAlreadyTurnedDownAndNotAsk");
            }
        });
    }


    @Override
    protected void onDestroy() {
        GalleryPickerHelper.releaseListener();
        super.onDestroy();
    }

    public void loadData() {
        if (config != null && multiLauncher != null) {
            multiLauncher.launch(getMultiPickerIntent());
            return;
        }
        dispatchResult(null);
    }

    @NonNull
    private Intent getMultiPickerIntent() {
        Intent intent = new Intent();
        if (isPhotoPickerAvailable()) {
            intent.setAction(MediaStore.ACTION_PICK_IMAGES);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && config.pickNum > 1) {
                //MediaStore.EXTRA_PICK_IMAGES_MAX 是在 Android 13 (API 33) 中引入的
                intent.putExtra(MediaStore.EXTRA_PICK_IMAGES_MAX, config.pickNum);
            } else {
                intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, config.pickNum != 1);
            }
        } else if (isSystemFallbackPickerAvailable()) {
            ActivityInfo activityInfo = getSystemFallbackPicker(this).activityInfo;
            intent.setClassName(activityInfo.applicationInfo.packageName, activityInfo.name);
            intent.setAction("androidx.activity.result.contract.action.PICK_IMAGES");
            intent.putExtra("androidx.activity.result.contract.extra.PICK_IMAGES_MAX", config.pickNum);
        } else if (isGmsPickerAvailable()) {
            ActivityInfo activityInfo = getGmsPicker(this).activityInfo;
            intent.setClassName(activityInfo.applicationInfo.packageName, activityInfo.name);
            intent.setAction("com.google.android.gms.provider.action.PICK_IMAGES");
            intent.putExtra("com.google.android.gms.provider.extra.PICK_IMAGES_MAX", config.pickNum);
        } else {
            intent.setAction(Intent.ACTION_PICK);
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, config.pickNum != 1);
        }
        if (config.imageOnly) {
            intent.setType("image/*");
        } else if (config.videoOnly) {
            intent.setType("video/*");
        }
        return intent;
    }

    public void dispatchResult(List<Uri> list) {
        OnResultCallbackListener<LocalMedia> listener = GalleryPickerHelper.listener;
        if (listener != null) {
            if (list == null || list.isEmpty()) {
                Logger.enable(logEnable).json("===========>", "cancel");
                listener.onCancel();
            } else {
                List<LocalMedia> data = new ArrayList<>();
                for (Uri uri : list) {
                    try {
                        getContentResolver().takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    } catch (Exception e) {/**/}
                    String path = FileUtils.getFilePathByUri(this, uri);
                    if (path != null) {
                        LocalMedia localMedia = new LocalMedia();
                        localMedia.setPath(path);
                        localMedia.setRealPath(path);
                        data.add(localMedia);
                    }
                }
                Logger.enable(logEnable).json("===========>", "result", data, list);
                listener.onResult(data);
            }
        }
        finish();
    }

    private void showPermissionsDialog() {
        showPermissionsDialog(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE}, getString(R.string.picture_jurisdiction));
    }

    protected void showPermissionsDialog(String[] permissions, String errorMsg) {
        PictureCustomDialog dialog = new PictureCustomDialog(this, R.layout.picture_weee_base_dialog);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        TextView btn_cancel = dialog.findViewById(R.id.tv_cancel);
        TextView btn_commit = dialog.findViewById(R.id.tv_confirm);
        btn_commit.setText(getString(R.string.picture_go_setting));
        TextView tvTitle = dialog.findViewById(R.id.tv_title);
        TextView tv_content = dialog.findViewById(R.id.tv_content);
        tvTitle.setText(getString(R.string.picture_prompt));
        tv_content.setText(errorMsg);
        btn_cancel.setOnClickListener(v -> {
            if (!isFinishing()) {
                dialog.dismiss();
            }
            dispatchResult(null);
        });
        btn_commit.setOnClickListener(v -> {
            if (!isFinishing()) {
                dialog.dismiss();
            }
            PermissionChecker.launchAppDetailsSettings(this);
            dispatchResult(null);
        });
        dialog.show();
    }

    protected boolean isPhotoPickerAvailable() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && getExtensionVersion(Build.VERSION_CODES.R) >= 2;
    }

    protected boolean isSystemFallbackPickerAvailable() {
        return getSystemFallbackPicker(this) != null;
    }

    protected ResolveInfo getSystemFallbackPicker(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                return context.getPackageManager().resolveActivity(new Intent("androidx.activity.result.contract.action.PICK_IMAGES"), PackageManager.MATCH_DEFAULT_ONLY | PackageManager.MATCH_SYSTEM_ONLY);
            } catch (Exception e) {/**/}
        }
        return null;
    }

    protected boolean isGmsPickerAvailable() {
        return getGmsPicker(this) != null;
    }

    protected ResolveInfo getGmsPicker(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                return context.getPackageManager().resolveActivity(new Intent("com.google.android.gms.provider.action.PICK_IMAGES"), PackageManager.MATCH_DEFAULT_ONLY | PackageManager.MATCH_SYSTEM_ONLY);
            } catch (Exception e) {/**/}
        }
        return null;
    }
}
