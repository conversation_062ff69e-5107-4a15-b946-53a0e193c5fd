package com.sayweee.weee.module.debug.producttrace.ui;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.RelativeCornerSize;
import com.google.android.material.shape.ShapeAppearanceModel;
import com.google.android.material.shape.Shapeable;
import com.sayweee.weee.R;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.WrapperPopWindow;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

public class ProductTraceFloatView extends FrameLayout implements Shapeable {

    private ShapeAppearanceModel shapeAppearanceModel;

    private TextView tvToggle;
    private ImageView ivAction;
    private WrapperPopWindow popWindow;

    private final int SHADOW_MARGIN;
    private final int WIDTH;
    private final int HEIGHT;

    public ProductTraceFloatView(@NonNull Context context) {
        this(context, null, 0);
    }

    public ProductTraceFloatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductTraceFloatView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(context).inflate(R.layout.view_product_trace_float, this, true);

        SHADOW_MARGIN = CommonTools.dp2px(8);
        WIDTH = context.getResources().getDimensionPixelOffset(R.dimen.prop_product_trace_float_width);
        HEIGHT = context.getResources().getDimensionPixelOffset(R.dimen.prop_product_trace_float_height);

        init(context);
    }

    private void init(Context context) {
        MarginLayoutParams params = new LayoutParams(WIDTH, HEIGHT);
        params.setMargins(SHADOW_MARGIN, SHADOW_MARGIN, SHADOW_MARGIN, SHADOW_MARGIN);
        setLayoutParams(params);

        MaterialShapeDrawable drawable = new MaterialShapeDrawable(getShapeAppearanceModel());
        drawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        drawable.initializeElevationOverlay(context);
        drawable.setShadowCompatibilityMode(MaterialShapeDrawable.SHADOW_COMPAT_MODE_ALWAYS);
        drawable.setElevation(CommonTools.dp2px(4));
        drawable.setShadowColor(ContextCompat.getColor(context, R.color.root_color_black_tint_4));
        setBackground(drawable);

        ivAction = findViewById(R.id.iv_action);
        ShapeHelper.setBackgroundSolidDrawable(
                ivAction,
                ContextCompat.getColor(context, R.color.color_product_trace_float_icon_bg),
                CommonTools.dp2px(context, R.dimen.prop_size_radius_full, 99f)
        );

        tvToggle = findViewById(R.id.tv_toggle);

        update();

        ViewTools.setViewOnSafeClickListener(tvToggle, this::onClick);
        ViewTools.setViewOnSafeClickListener(ivAction, this::onClick);
    }

    @NonNull
    @Override
    public ShapeAppearanceModel getShapeAppearanceModel() {
        if (shapeAppearanceModel == null) {
            shapeAppearanceModel = ShapeAppearanceModel.builder()
                    .setAllCornerSizes(new RelativeCornerSize(.5f))
                    .build();
        }
        return shapeAppearanceModel;
    }

    @Override
    public void setShapeAppearanceModel(@NonNull ShapeAppearanceModel shapeAppearanceModel) {
        // no op
    }

    private void onClick(View v) {
        if (v == tvToggle) {
            toggle();
        } else if (v == ivAction) {
            showSettingsPopup(this);
        } else if (v.getId() == R.id.id_product_trace_float_close) {
            if (this.popWindow != null) {
                this.popWindow.dismiss();
            }
            ProductTraceManager.get().setEnabled(false);
        } else if (v.getId() == R.id.id_product_trace_float_pause) {
            if (this.popWindow != null) {
                this.popWindow.dismiss();
            }
            boolean isRunning = !ProductTraceManager.get().isRunning();
            ProductTraceManager.get().setRunning(isRunning);
        }
    }

    private void toggle() {
        if (ProductTraceManager.get().isRunning()) {
            ProductTraceManager.get().toggleTraceDomain();
        } else {
            ProductTraceManager.get().setRunning(true);
        }
        update();
    }

    public void update() {
        if (ProductTraceManager.get().isRunning()) {
            tvToggle.setText(R.string.s_product_trace_float_toggle);
        } else {
            tvToggle.setText(R.string.s_product_trace_float_resume);
        }
    }

    @NonNull
    private View createSettingsPopupView(Context context) {
        LinearLayout viewGroup = new LinearLayout(context);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                WIDTH,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        viewGroup.setLayoutParams(lp);
        viewGroup.setOrientation(LinearLayout.VERTICAL);
        MaterialShapeDrawable drawable = new MaterialShapeDrawable(
                ShapeAppearanceModel.builder()
                        .setAllCorners(CornerFamily.ROUNDED, CommonTools.dp2px(4))
                        .build()
        );
        drawable.setFillColor(ColorStateList.valueOf(Color.BLACK));
        viewGroup.setBackground(drawable);

        // Pause / Resume
        TextView tvPause = new TextView(context);
        tvPause.setId(R.id.id_product_trace_float_pause);
        if (ProductTraceManager.get().isRunning()) {
            tvPause.setText(R.string.s_product_trace_float_pause);
        } else {
            tvPause.setText(R.string.s_product_trace_float_resume);
        }
        ViewTools.applyTextStyleAndColor(tvPause, R.style.style_body_3xs_medium, R.color.white);
        lp = new LinearLayout.LayoutParams(lp);
        tvPause.setPadding(CommonTools.dp2px(12), CommonTools.dp2px(8), CommonTools.dp2px(12), CommonTools.dp2px(8));
        tvPause.setLayoutParams(lp);
        viewGroup.addView(tvPause);
        ViewTools.setViewOnSafeClickListener(tvPause, this::onClick);

        // Exit
        TextView tvClose = new TextView(context);
        tvClose.setId(R.id.id_product_trace_float_close);
        tvClose.setText(R.string.s_product_trace_float_close);
        ViewTools.applyTextStyleAndColor(tvClose, R.style.style_body_3xs_medium, R.color.white);
        lp = new LinearLayout.LayoutParams(lp);
        tvClose.setPadding(CommonTools.dp2px(12), CommonTools.dp2px(8), CommonTools.dp2px(12), CommonTools.dp2px(8));
        tvClose.setLayoutParams(lp);
        viewGroup.addView(tvClose);
        ViewTools.setViewOnSafeClickListener(tvClose, this::onClick);

        viewGroup.measure(
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        );
        return viewGroup;
    }

    private void showSettingsPopup(View view) {
        Context context = view.getContext();
        View popupView = createSettingsPopupView(context);
        LocParams loc = new LocParams().calc(view);

        int popMarginHorizontal = 0;
        int popMarginVertical = CommonTools.dp2px(4);
        int popWidth = popupView.getMeasuredWidth();
        int popHeight = popupView.getMeasuredHeight();
        int selfWidth = view.getWidth();
        int selfHeight = view.getHeight();

        boolean isLeft = true;
        boolean isTop = true;
        if (loc.x + SHADOW_MARGIN + popWidth + popMarginHorizontal > loc.rightBorder) {
            isLeft = false;
        }
        if (loc.y + SHADOW_MARGIN - popHeight - popMarginVertical < loc.topBorder) {
            isTop = false;
        }

        int x, y;
        if (isLeft) {
            x = loc.x + SHADOW_MARGIN + popMarginHorizontal;
        } else {
            x = loc.x + SHADOW_MARGIN + selfWidth - popWidth - popMarginHorizontal;
        }
        if (isTop) {
            y = loc.y + SHADOW_MARGIN - popHeight - popMarginVertical;
        } else {
            y = loc.y + SHADOW_MARGIN + selfHeight + popMarginVertical;
        }

        WrapperPopWindow popWindow;
        popWindow = new WrapperPopWindow.PopupWindowBuilder(context)
                .setView(popupView)
                .size(popWidth, popHeight)
                .setFocusable(true)
                .setOutsideTouchable(true)
                .create();
        realShowPopup(context, popWindow, x, y);
    }

    private void realShowPopup(Context context, WrapperPopWindow popWindow, int x, int y) {
        if (this.popWindow != null) {
            this.popWindow.dismiss();
        }

        Activity activity;
        if (context instanceof Activity) {
            activity = (Activity) context;
        } else {
            activity = LifecycleProvider.get().getTopActivity();
        }
        if (activity == null) {
            return;
        }
        View decorView = activity.getWindow().getDecorView();
        popWindow.showAtLocation(decorView, Gravity.TOP | Gravity.START, x, y);
        this.popWindow = popWindow;
    }

    private static class LocParams {

        private int leftBorder;
        private int topBorder;
        private int rightBorder;
        private int bottomBorder;
        private int x;
        private int y;

        private LocParams calc(View view) {
            ViewParent viewParent = view.getParent();
            if (!(viewParent instanceof View)) {
                return this;
            }

            View parentView = (View) viewParent;
            if (parentView.getLayoutParams() instanceof WindowManager.LayoutParams) {
                WindowManager.LayoutParams lp = (WindowManager.LayoutParams) parentView.getLayoutParams();
                x = lp.x;
                y = lp.y;

                Rect bounds = CommonTools.getWindowRect(view.getContext());
                leftBorder = 0;
                topBorder = CommonTools.getStatusBarHeight(view.getContext());
                rightBorder = bounds.width();
                bottomBorder = bounds.height();
            }

            return this;
        }
    }
}
