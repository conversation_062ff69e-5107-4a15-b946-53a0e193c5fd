package com.sayweee.weee.module.home.provider.message;

import android.graphics.Color;
import android.view.View;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.bean.TopMessageV3Bean;
import com.sayweee.weee.module.home.provider.message.data.CmsTopMessageV3Data;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.widget.shape.ShapeFrameLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Deprecated
public class TopMessageV3Provider extends SimpleSectionProvider<CmsTopMessageV3Data, AdapterViewHolder> implements ImpressionProvider<CmsTopMessageV3Data> {

    @Override
    public int getItemType() {
        return CmsItemType.TOP_MESSAGE_V3;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_announcement_message;
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsTopMessageV3Data item) {
        TopMessageV3Bean bean = item.t;
        TopMessageV3Bean.Background background = bean.background;
        TopMessageV3Bean.Content content = bean.content;
        TopMessageV3Bean.RightArrow rightArrow = bean.right_arrow;
        if (background != null) {
            helper.setBackgroundColor(R.id.layout_announcement_message, ViewTools.parseColor(background.color, Color.WHITE));
        }
        if (content != null) {
            helper.setVisibleCompat(R.id.tv_message, !EmptyUtils.isEmpty(content.message));
            helper.setTextHtml(R.id.tv_message, content.message);
            helper.setVisibleCompat(R.id.tv_short_message, !EmptyUtils.isEmpty(content.short_message));
            helper.setTextHtml(R.id.tv_short_message, content.short_message);
            helper.setVisibleCompat(R.id.tv_sub_message, !EmptyUtils.isEmpty(content.sub_message));
            helper.setTextHtml(R.id.tv_sub_message, content.sub_message);
        }
        if (rightArrow != null) {
            helper.setText(R.id.tv_title, rightArrow.title);
            helper.setTextColor(R.id.tv_title, ViewTools.parseColor(rightArrow.title_color, Color.BLACK));
            ShapeFrameLayout label = helper.getView(R.id.layout_label);
            label.setBackgroundSolidDrawable(ViewTools.parseColor(rightArrow.background_color, Color.BLACK), CommonTools.dp2px(16));
        }
        helper.setOnViewClickListener(R.id.layout_announcement_message, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(item.getEventKey())
                        .setMod_pos(item.position)
                        .setTargetNm(bean.content != null ? bean.content.short_message : "")
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.MESSAGE)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .setUrl(bean.link)
                        .build().getParams());
                context.startActivity(WebViewActivity.getIntent(context, bean.link));
            }
        });
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CmsTopMessageV3Data item, int position) {
        String module = item.getEventKey();
        int pos = item.position;
        String key = pos + "_" + module;
        ArrayList<ImpressionBean> list = new ArrayList<>();
        TopMessageV3Bean bean = item.t;
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(module)
                .setMod_pos(pos)
                .setBanner_key(bean.content != null ? bean.content.short_message : "")
                .setBanner_pos(0)
                .setBanner_type(EagleTrackEvent.TargetType.MESSAGE)
                .setUrl(bean.link)
                .build().getParams();
        ImpressionBean impressionBean = new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, key);
        list.add(impressionBean);
        return list;
    }
}
