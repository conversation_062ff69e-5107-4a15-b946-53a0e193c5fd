package com.sayweee.weee.module.product.provider;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_375_AUTO;
import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.animation.ObjectAnimator;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.bumptech.glide.request.RequestOptions;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cate.product.ImagePreviewActivity;
import com.sayweee.weee.module.cms.track.IPageLifecycle;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.product.data.PdpProductBannerData;
import com.sayweee.weee.module.product.data.ProductBannerItemData;
import com.sayweee.weee.module.web.bean.PdpConfigBean;
import com.sayweee.weee.player.bean.MediaBean;
import com.sayweee.weee.player.bean.MediaData;
import com.sayweee.weee.player.mute.MutePlayer;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.weee.widget.banner.ex.ExBannerAdapter;
import com.sayweee.weee.widget.banner.ex.ExCarouselBanner;
import com.sayweee.weee.widget.banner.ex.PlayerHandler;
import com.sayweee.weee.widget.banner.ex.PlayerTriggerAdapter;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.round.RoundImageView;
import com.sayweee.wrapper.base.view.WrapperActivity;
import com.youth.banner.Banner;
import com.youth.banner.indicator.CircleIndicator;
import com.youth.banner.listener.OnBannerListener;
import com.youth.banner.util.BannerUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/5/12.
 * Desc:
 */
public abstract class ExBannerProvider<T extends AdapterDataType, V extends AdapterViewHolder>
        extends SimpleSectionProvider<T, V>
        implements IPageLifecycle, PlayerTriggerAdapter, ImpressionProvider<PdpProductBannerData> {

    protected List<ProductBannerItemData> list;
    protected WeakReference<Drawable> weakDrawable;
    protected WeakReference<ExCarouselBanner> weakBanner;
    protected OnBannerListener<ProductBannerItemData> listener;
    protected EagleImpressionTrackerIml tracker;
    protected int lastBannerIndex = 0;

    private static final int BANNER_HIDE_MASK = -1;

    @Override
    public void notifyPageDataSetChanged(RecyclerView view) {
        PlayerHandler.notifyAdapterDataChanged(view);
    }

    @Override
    public void onPageResume(RecyclerView view) {
        PlayerHandler.notifyLifecycleStatusChanged(view, /* isResume= */true);
    }

    @Override
    public void onPagePause(RecyclerView view) {
        PlayerHandler.notifyLifecycleStatusChanged(view, /* isResume= */false);
    }

    @Override
    public void onPageScrollStateChanged(RecyclerView view, int status) {
        PlayerHandler.notifyScrollStateChanged(view);
    }

    @Override
    public void onViewDetachedFromWindow(V holder) {
        super.onViewDetachedFromWindow(holder);
        if (weakBanner != null) {
            ExCarouselBanner banner = weakBanner.get();
            if (banner != null) {
                banner.onPageDetached();
            }
        }
    }

    @Override
    public void notifyItemPlayByPosition(int start, int end, int status) {
        if (weakBanner != null && start <= 0) {
            ExCarouselBanner banner = weakBanner.get();
            if (banner != null) {
                banner.notifyLifecycleChanged(status);
            }
        }
    }

    protected void fillBannerData(AdapterViewHolder helper, PdpProductBannerData item) {
        ExCarouselBanner banner = helper.getView(R.id.banner);
        weakBanner = new WeakReference<>(banner);
        fillBanner(banner, convertBannerData(item), !item.isNetWork, item);
        setBannerMask(banner.getViewPager2().getScrollState(), banner, 0);
        fillIndicator(helper, banner);
    }

    protected void fillIndicator(AdapterViewHolder helper, ExCarouselBanner banner) {
        CircleIndicator indicator = helper.getView(R.id.indicator);
        banner.setIndicator(indicator, false)
                .setIndicatorNormalColorRes(R.color.color_tint_black_150)
                .setIndicatorSelectedColorRes(R.color.color_tint_black_900)
                .setIndicatorWidth(CommonTools.dp2px(4), CommonTools.dp2px(4))
                .setIndicatorHeight(CommonTools.dp2px(4))
                .setIndicatorSpace(CommonTools.dp2px(4));
    }

    private PdpBannerAdapter bannerAdapter;
    private boolean isAdapterNotify = false;

    protected void fillBanner(final ExCarouselBanner banner, List<ProductBannerItemData> data,
                              boolean preload, final PdpProductBannerData item) {
        this.list = data;
        banner.isAutoLoop(false);
        banner.setStartPosition(1);
        setBannerGallery(banner, data);

        if (bannerAdapter == null || banner.getAdapter() == null) {
            bannerAdapter = new PdpBannerAdapter(new ArrayList<>(data), preload);
            bannerAdapter.attachBanner(banner, item);
            banner.addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
                @Override
                public void onPageScrollStateChanged(int state) {
                    super.onPageScrollStateChanged(state);
                    setBannerMask(state, banner, 300);
                }

                @Override
                public void onPageSelected(int position, boolean isAuto) {
                    lastBannerIndex = position;
                    if (!isAuto) {
                        trackImpression(position, item);
                    }
                }
            });
            isAdapterNotify = true;
        } else {
            bannerAdapter.setPreloadDrawable(item.preloadDrawable);
            bannerAdapter.updateItems(data != null ? new ArrayList<>(data) : null, preload);
            if (isResetBanner) {
                banner.getViewPager2().post(() -> {
                    bannerAdapter.notifyDataSetChanged();
                });
                isResetBanner = false;
            }
            if (isAdapterNotify) {
                // first load
                banner.setCurrentItem(banner.getStartPosition(), false);
                banner.setIndicatorPageChange();
                isAdapterNotify = false;
            } else {
                // scroll up load
                banner.post(() -> {
                    int index = banner.getStartPosition();
                    int originIndex = lastBannerIndex + 1;
                    if (lastBannerIndex > 0 && originIndex < banner.getItemCount()) {
                        index = originIndex;
                    }
                    banner.setCurrentItem(index, false);
                    banner.setIndicatorPageChange();
                });
            }
        }
    }

    private void setBannerGallery(ExCarouselBanner banner, List<ProductBannerItemData> data) {
        if (data != null) {
            int parentWidth = ScreenUtils.getScreenWidth(context) - ( 2 * CommonTools.dp2px(20));
            int width = CommonTools.dp2px(getBannerWidth()); //
            int rightWidth = (int) ((parentWidth - width) / ScreenUtils.getScreenDensity(context));
            if (data.size() > 1) {
                banner.setBannerGalleryEffect(0, rightWidth + 10, 10, 1);
            } else {
                int leftItemWidth = rightWidth / 2;
                int rightItemWidth = rightWidth - leftItemWidth;
                banner.setBannerGalleryEffect(leftItemWidth, rightItemWidth + 20, 0, 1);
            }
        }
    }

    private int getBannerWidth() {
        Object config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.WEBVIEW);
        int width = 0;
        if (config instanceof PdpConfigBean) {
            if (((PdpConfigBean) config).banner != null) {
                width = ((PdpConfigBean) config).banner.size;
            }
        }
        if (width <= 0) {
            width = 255;
        }
        return width;
    }

    private void setBannerMask(int state, Banner banner, int duration) {
        banner.getViewPager2().post(() -> {
            if (bannerAdapter.getRealCount() > 1) {
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    banner.getViewPager2().post(() -> {
                        int position = banner.getViewPager2().getCurrentItem();
                        int nextPosition = (position + 1) % bannerAdapter.getItemCount();
                        if (banner.getViewPager2().getScrollState() == ViewPager2.SCROLL_STATE_IDLE) {
                            bannerAdapter.notifyItemChanged(nextPosition, duration);
                        }
                    });
                } else {
                    int position = banner.getViewPager2().getCurrentItem();
                    int nextPosition = (position + 1) % bannerAdapter.getItemCount();
                    RecyclerView.ViewHolder holder = getViewAtPosition(banner.getViewPager2(), nextPosition);
                    if (holder instanceof ExBannerAdapter.VideoHolder) {
                        setThumbMask(BANNER_HIDE_MASK, ((ExBannerAdapter.VideoHolder) holder).player);
                    }
                }
            }
        });

    }

    private void setThumbMask(int duration, MutePlayer videoPlayer) {
        try {
            RoundImageView maskView = videoPlayer.getThumbMaskView();
            maskView.setAlpha(0f);
            if (duration > BANNER_HIDE_MASK) {
                maskView.setBackgroundColor(Color.WHITE);
                ObjectAnimator fadeAnim = ObjectAnimator.ofFloat(maskView, "alpha", 0f, 0.7f);
                fadeAnim.setDuration(duration);
                fadeAnim.start();
            } else {
                maskView.setBackgroundColor(Color.TRANSPARENT);
            }
        } catch (Exception e) {
        }
    }

    public RecyclerView.ViewHolder getViewAtPosition(ViewPager2 viewPager, int position) {
        View view = viewPager.getChildAt(0);
        if (view instanceof RecyclerView) {
            RecyclerView recyclerView = (RecyclerView) view;
            RecyclerView.ViewHolder viewHolder = recyclerView
                    .findViewHolderForAdapterPosition(position);
            return viewHolder;
        }
        return null;
    }

    private void trackImpression(int position, PdpProductBannerData item) {
        final MediaBean firstMedia = !EmptyUtils.isEmpty(item.t.media_urls) ? item.t.media_urls.get(0) : null;
        String bannerType = firstMedia != null ? firstMedia.media_type : "image";
        String url = firstMedia != null ? firstMedia.url : item.t.img;
        String productId = String.valueOf(item.t.id);
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, productId, null, null, null, item.traceId);
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(PdpProductBannerData.MOD_NM)
                .setMod_pos(item.mod_pos)
                .addCtx(ctx)
                .addContent(new TrackParams()
                        .put("banner_pos", lastBannerIndex)
                        .put("banner_type", bannerType)
                        .put("url", url)
                        .get())
                .build().getParams();
        tracker.trackImpression(new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, position + PdpProductBannerData.MOD_NM + productId));
    }

    protected List<ProductBannerItemData> convertBannerData(List<MediaBean> list) {
        List<ProductBannerItemData> data = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                MediaBean bean = list.get(i);
                if (bean != null) {
                    ProductBannerItemData child = new ProductBannerItemData(bean);
                    data.add(child);
                }
            }
        }
        return data;
    }

    protected List<ProductBannerItemData> convertBannerData(PdpProductBannerData item) {
        if (!item.isNetWork) {
            MediaBean bean = new MediaBean();
            bean.url = item.t.img;
            ArrayList<ProductBannerItemData> data = new ArrayList<>();
            ProductBannerItemData child = new ProductBannerItemData(bean);
            data.add(child);
            if (item.t.media_urls != null && item.t.media_urls.size() > 1) {
                for (int i=1; i<item.t.media_urls.size(); i++) {
                    data.add(new ProductBannerItemData(item.t.media_urls.get(i)));
                }
            } else {
                MediaBean mediaBean = new MediaBean();
                mediaBean.media_type = MediaBean.TYPE_IMAGE;
                mediaBean.url = "";
                data.add(new ProductBannerItemData(mediaBean));
            }
            return data;
        } else {
            return convertBannerData(item.t.media_urls);
        }
    }

    private boolean isResetBanner = false;
    public void resetBanner() {
        lastBannerIndex = 0;
        isResetBanner = true;
    }

    class PdpBannerAdapter extends ExBannerAdapter {

        private ExCarouselBanner banner;
        private PdpProductBannerData bannerData;

        public PdpBannerAdapter(List<MediaData> list, boolean preLoad) {
            super(list, preLoad);
        }

        public void attachBanner(ExCarouselBanner banner, PdpProductBannerData bannerData) {
            this.banner = banner;
            this.bannerData = bannerData;

            banner.getViewPager2().setOffscreenPageLimit(2);
            banner.addBannerLifecycleObserver((LifecycleOwner) context)
                    .setAdapter(bannerAdapter, true);
            banner.setOnBannerListener(new OnBannerListener<ProductBannerItemData>() {
                @Override
                public void OnBannerClick(ProductBannerItemData data, int position) {
                    clickBanner(data, bannerData.t.id);
                }
            });

        }

        @Override
        public RecyclerView.ViewHolder onCreateHolder(ViewGroup parent, int viewType) {
            beforeCreateHolder(parent, viewType);
            return new VideoHolder(BannerUtils.getView(parent, R.layout.item_pdp_banner_video));
        }

        @Override
        public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position, @NonNull List<Object> payloads) {
            if (CollectionUtils.isNotEmpty(payloads)) {
                for (Object p : payloads) {
                    if (p instanceof Integer) {
                        MutePlayer videoPlayer = ((VideoHolder) holder).player;
                        setThumbMask((int) p, videoPlayer);
                    }
                }
            } else {
                super.onBindViewHolder(holder, position, payloads);
            }
        }

        @Override
        public void onBindView(RecyclerView.ViewHolder holder, MediaData data, int position, int size) {
            if (!(holder instanceof VideoHolder)) return;
            MutePlayer videoPlayer = ((VideoHolder) holder).player;
            videoPlayer.setWebpSpec(SPEC_375_AUTO);
            videoPlayer.setRadius(CommonTools.dp2px(8));
            videoPlayer.showPlayingBlur(data.isVideo());

            setThumbMask(BANNER_HIDE_MASK, ((ExBannerAdapter.VideoHolder) holder).player);
            videoPlayer.setThumbDrawable(new ColorDrawable(ContextCompat.getColor(context, R.color.color_place)));
            if (position == 0 && getPreload()) {
                banner.setLastIndex(position);
                String thumbUrl = WebpManager.get().getConvertUrl(SPEC_PRODUCT, data.getImagePath());
                videoPlayer.setThumbnailUrl(thumbUrl, 200, 200);
            } else {
                if (preloadDrawable != null && position == 0) {
                    videoPlayer.setThumbDrawable(preloadDrawable);
                    videoPlayer.setPlaceholder(new RequestOptions().placeholder(preloadDrawable));
                } else {
                    videoPlayer.setThumbnailUrl(null, 0, 0);
                    videoPlayer.setPlaceholder(new RequestOptions().placeholder(R.color.color_place));
                }
            }

            videoPlayer.setPlayerData(data);

            View v = ((VideoHolder) holder).temp;
            if (v != null) {
                v.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        clickBanner(data, bannerData.t.id);
                    }
                });
            }
        }

        @Override
        public int getItemViewType(int position) {
            return TYPE_VIDEO;
        }

        private void clickBanner(MediaData data, int id) {
            if (!(data instanceof ProductBannerItemData)) return;
            if (!data.isVideo() && !EmptyUtils.isEmpty(list) && context instanceof WrapperActivity) {
                ArrayList<String> temp = new ArrayList<>();
                int index = 0;
                for (int i = 0; i < list.size(); i++) {
                    ProductBannerItemData child = list.get(i);
                    if (!child.isVideo()) {
                        temp.add(child.getImagePath());
                        if (child == data) {
                            index = temp.size() - 1;
                            if (id > 0) {
                                clickTrack(id, child, index);
                            }
                        }
                    }
                }

                ((WrapperActivity) context).startActivityForResult(ImagePreviewActivity.getIntent(context, temp, index), 1001);
                ((WrapperActivity) context).overridePendingTransition(0, 0);
            } else if (data.isVideo() && banner != null) {
                // video click - toggle play/pause
                int currentPosition = banner.getViewPager2().getCurrentItem();
                RecyclerView.ViewHolder holder = getViewAtPosition(banner.getViewPager2(), currentPosition);

                if (holder instanceof VideoHolder) {
                    MutePlayer videoPlayer = ((VideoHolder) holder).player;
                    if (videoPlayer.getCurrentState() == MutePlayer.CURRENT_STATE_PLAYING ||
                            videoPlayer.getCurrentState() == MutePlayer.CURRENT_STATE_PLAYING_BUFFERING_START) {
                        videoPlayer.onVideoPause();
                    } else if (videoPlayer.getCurrentState() == MutePlayer.CURRENT_STATE_PAUSE) {
                        videoPlayer.onVideoResume();
                    } else {
                        videoPlayer.startPlayLogic();
                    }
                }
            }
        }

        private void clickTrack(int id, ProductBannerItemData child, int index) {
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                    String.valueOf(id), null, null, null, null);
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm(PdpProductBannerData.MOD_NM)
                    .setTargetNm(child.getImagePath())
                    .setTargetPos(index)
                    .setTargetType(child.t.media_type)
                    .addCtx(ctx)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .build().getParams());
        }
    }
}
