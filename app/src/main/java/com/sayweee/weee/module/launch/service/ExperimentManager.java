package com.sayweee.weee.module.launch.service;

import com.sayweee.weee.utils.CollectionUtils;

import java.util.List;

/**
 * Author:  winds
 * Date:    2021/5/25.
 * Desc:AB test 管理器
 */
@SuppressWarnings("squid:S6548")
public final class ExperimentManager {

    public static final int ID_SEARCH_RESULTS_GRID = ExperimentId.ID_SEARCH_RESULTS_GRID;
    public static final int ID_SEARCH_AUTOCOMPLETE_SUGGESTIONS_REDESIGN_V1 = ExperimentId.ID_SEARCH_AUTOCOMPLETE_SUGGESTIONS_REDESIGN_V1;
    public static final int ID_SEARCH_AUTOCOMPLETE_HOME_SUGGESTIONS_FEATURE = ExperimentId.ID_SEARCH_AUTOCOMPLETE_HOME_SUGGESTIONS_FEATURE;
    public static final int ID_BUY_AGAIN_BADGE = ExperimentId.ID_BUY_AGAIN_BADGE;
    public static final int ID_ONBOARDING_LAUNCH_AB = ExperimentId.ID_ONBOARDING_LAUNCH_AB;
    public static final int ID_HOME_PUSH_NEW_CATEGORY = ExperimentId.ID_HOME_PUSH_NEW_CATEGORY;

    public static final List<Integer> VALID_IDS = CollectionUtils.arrayListOf(
            ID_SEARCH_RESULTS_GRID,
            ID_SEARCH_AUTOCOMPLETE_SUGGESTIONS_REDESIGN_V1,
            ID_SEARCH_AUTOCOMPLETE_HOME_SUGGESTIONS_FEATURE,
            ID_BUY_AGAIN_BADGE,
            ID_ONBOARDING_LAUNCH_AB,
            ID_HOME_PUSH_NEW_CATEGORY
    );
}

