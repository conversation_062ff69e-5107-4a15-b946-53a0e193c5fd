package com.sayweee.weee.module.dialog;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/11/20.
 * Desc:
 */
public class LanguagePickerDialog extends WrapperDialog {
    private OnLanguageListener listener;

    public LanguagePickerDialog(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.dialog_language_picker;
    }

    @Override
    protected void setDialogParams(Dialog dialog) {
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        setDialogParams(dialog, CommonTools.dp2px(270), WindowManager.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
    }

    @Override
    public void help(ViewHelper helper) {

    }

    public LanguagePickerDialog displayKorean() {
        addHelperCallback(new HelperCallback() {
            @Override
            public void help(Dialog dialog, ViewHelper helper) {
                helper.setVisible(R.id.tv_korean, true);
                setDialogParams(dialog, CommonTools.dp2px(270), WindowManager.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
            }
        });
        return this;
    }

    public LanguagePickerDialog displayDefault() {
        addHelperCallback(new HelperCallback() {
            @Override
            public void help(Dialog dialog, ViewHelper helper) {
                helper.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                        int id = v.getId();
                        if (id == R.id.tv_english) {
                            changeLanguage(LanguageManager.Language.ENGLISH);
                        } else if (id == R.id.tv_simple_chinese) {
                            changeLanguage(LanguageManager.Language.CHINESE);
                        } else if (id == R.id.tv_traditional_chinese) {
                            changeLanguage(LanguageManager.Language.TRADITIONAL_CHINESE);
                        } else if (id == R.id.tv_spanish) {
                            changeLanguage(LanguageManager.Language.SPANISH);
                        } else if (id == R.id.tv_korean) {
                            changeLanguage(LanguageManager.Language.KOREAN);
                        } else if (id == R.id.tv_japanese) {
                            changeLanguage(LanguageManager.Language.JAPANESE);
                        } else if (id == R.id.tv_vietnamese) {
                            changeLanguage(LanguageManager.Language.VIETNAMESE);
                        } else if (id == R.id.tv_thai) {
                            changeLanguage(LanguageManager.Language.THAI);
                        }
                    }
                }, R.id.tv_english, R.id.tv_simple_chinese, R.id.tv_traditional_chinese, R.id.tv_spanish, R.id.tv_korean, R.id.tv_japanese, R.id.tv_vietnamese, R.id.tv_thai);
            }
        });
        return this;
    }

    public void show(OnLanguageListener listener) {
        this.listener = listener;
        this.show();
    }

    private void changeLanguage(String flag) {
        if (context != null) {
            boolean recreate = LanguageManager.get().changeLanguage(context, flag);
            if (listener != null) {
                listener.onLanguageChanged(recreate);
            }
        }
    }

    public LanguagePickerDialog setSupportLanguage(List<String> language) {

        addHelperCallback(new HelperCallback() {
            @Override
            public void help(Dialog dialog, ViewHelper helper) {
                List<String> list = new ArrayList<>();
                if (EmptyUtils.isEmpty(language)) {
                    list.addAll(getDefaultLanguage());
                } else {
                    list.addAll(language);
                }
                LinearLayout layoutContainer = helper.getView(R.id.layout_container);
                layoutContainer.removeAllViews();
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonTools.dp2px(50));
                for (int i = 0; i < list.size(); i++) {
                    String s = list.get(i);
                    addItemView(layoutContainer, layoutParams, s, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            changeLanguage(s);
                        }
                    });
                }
            }
        });
        return this;
    }

    private void addItemView(LinearLayout layoutContainer, LinearLayout.LayoutParams layoutParams, String language, View.OnClickListener listener) {
        if (language != null) {
            switch (language) {
                case LanguageManager.Language.ENGLISH:
                    layoutContainer.addView(getLanguageView(R.string.s_language_english, listener), layoutParams);
                    break;
                case LanguageManager.Language.CHINESE:
                    layoutContainer.addView(getLanguageView(R.string.s_language_simple_chinese, listener), layoutParams);
                    break;
                case LanguageManager.Language.TRADITIONAL_CHINESE:
                    layoutContainer.addView(getLanguageView(R.string.s_language_traditional_chinese, listener), layoutParams);
                    break;
                case LanguageManager.Language.KOREAN:
                    layoutContainer.addView(getLanguageView(R.string.s_language_korean, listener), layoutParams);
                    break;
                case LanguageManager.Language.SPANISH:
                    layoutContainer.addView(getLanguageView(R.string.s_language_spanish, listener), layoutParams);
                    break;
                case LanguageManager.Language.JAPANESE:
                    layoutContainer.addView(getLanguageView(R.string.s_language_japanese, listener), layoutParams);
                    break;
                case LanguageManager.Language.VIETNAMESE:
                    layoutContainer.addView(getLanguageView(R.string.s_language_vietnamese, listener), layoutParams);
                    break;
                case LanguageManager.Language.THAI:
                    layoutContainer.addView(getLanguageView(R.string.s_language_thai, listener), layoutParams);
                    break;
            }
        }
    }


    private View getLanguageView(int language, View.OnClickListener listener) {
        TextView view = new TextView(context);
        view.setGravity(Gravity.CENTER);
        view.setText(language);
        view.setTextSize(17);
        view.setTextColor(context.getResources().getColor(R.color.text_main));
        view.setOnClickListener(listener);
        return view;
    }

    public static List<String> getDefaultLanguage() {
        List<String> list = new ArrayList<>();
        list.add(LanguageManager.Language.ENGLISH);
        list.add(LanguageManager.Language.CHINESE);
        list.add(LanguageManager.Language.TRADITIONAL_CHINESE);
        list.add(LanguageManager.Language.SPANISH);
        return list;
    }

    public interface OnLanguageListener {
        void onLanguageChanged(boolean recreate);
    }

}
