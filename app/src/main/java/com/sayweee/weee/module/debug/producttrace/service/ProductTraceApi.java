package com.sayweee.weee.module.debug.producttrace.service;

import com.sayweee.weee.module.debug.producttrace.bean.ProductTraceBean;
import com.sayweee.wrapper.bean.ResponseBean;

import io.reactivex.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface ProductTraceApi {

    String API_GET_PRODUCT_TRACE = "/ec/recommend/record/trace/product";

    @POST(API_GET_PRODUCT_TRACE)
    Observable<ResponseBean<ProductTraceBean>> getProductTrace(@Body RequestBody body);

}
