package com.sayweee.weee.module.home.bean;

import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.Map;

/**
 * Author:  ycy
 */
public class CategoriesProperty extends CmsProperty {

    public static final String STYLE_LINE_1 = "line1";
    public static final String STYLE_LINE_2 = "line2";
    public static final String STYLE_GRID = "grid";
    public static final String STYLE_CAPSULE_1 = "capsule1";
    public static final String STYLE_CAPSULE_2 = "capsule2";
    public static final String STYLE_BAR_1 = "bar1";

    public String style;
    public int scrollFlag;

    @Override
    public CategoriesProperty parseProperty(Map<String, String> map) {
        super.parseProperty(map);
        if (!EmptyUtils.isEmpty(map)) {
            this.style = map.get("style"); // 默认"line2"
            this.scrollFlag = DecimalTools.intValue(map.get("scroll_flag"), 0);
        }
        return this;
    }

    public boolean displayGridStyle() {
        return STYLE_GRID.equalsIgnoreCase(style);
    }

    public boolean displayCapsuleStyle() {
        return STYLE_CAPSULE_1.equalsIgnoreCase(style) || STYLE_CAPSULE_2.equalsIgnoreCase(style);
    }

    public boolean displayBarStyle() {
        return STYLE_BAR_1.equalsIgnoreCase(style);
    }

    public boolean isAutoScroll() {
        return scrollFlag == 0;
    }
}