package com.sayweee.weee.module.product.manager;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.ads.bean.AdsCreativeBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.presale.PresaleOpHelper;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.weee.module.product.service.ProductOpHelper;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.search.v2.SearchV2Manager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.player.bean.MediaBean;
import com.sayweee.weee.service.VibratorManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.function.Consumer;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.BottomOpLayout;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 产品操作管理器 - 负责处理产品的添加、删除、数量修改等购物车操作
 * Created by Thomsen on 27/03/2025.
 */
public class ProductOperationManager {

    // 通知一键加购同步
    public static final String NOTIFY_OFTEN_PAIRED_WITH = "notify_often_paired_with";

    public static final String NOTIFY_SHOW_COLLECT_TIPS = "notify_show_collect_tips";

    private final FragmentActivity activity;
    private final BottomOpLayout layoutOp;
    private final ProductPageParams pageParams;
    private String productKey;
    private Map<String, String> groupFilter;
    private int num;
    private int min;
    private int max;
    private String soldStatus;
    private int volumeThreshold;
    private int lastQuantity = -1;
    private boolean changeGroupFilter = false;

    private JSONObject adsTrackPendingInfo;

    private final MutableLiveData<String> cartNotifyLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> cartOftenNotifyLiveData = new MutableLiveData<>();

    public ProductOperationManager(
            @NonNull FragmentActivity activity,
            @NonNull BottomOpLayout layoutOp,
            ProductPageParams pageParams
    ) {
        this.activity = activity;
        this.layoutOp = layoutOp;
        this.pageParams = pageParams;
        if (pageParams != null) {
            adsTrackPendingInfo = AdsManager.getAdsTrackPendingInfo(String.valueOf(pageParams.productId));
        }
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public void setChangeGroupFilter() {
        changeGroupFilter = true;
    }

    public void setGroupFilter(Map<String, String> groupFilter) {
        this.groupFilter = groupFilter;
    }

    public MutableLiveData<String> getCartNotifyLiveData() {
        return cartNotifyLiveData;
    }
    public MutableLiveData<String> getCartOftenNotifyLiveData() {
        return cartOftenNotifyLiveData;
    }

    /**
     * 刷新产品操作相关的数字显示
     */
    public void refreshProductOpNum() {
        if (layoutOp != null) {
            layoutOp.setCartNum(OrderManager.get().getCartNum());
        }
    }

    /**
     * 设置产品操作配置
     */
    public void setProductOpConfig(ProductBean product, Consumer<String> notifyCartButtonData) {
        if (product == null) return;
        refreshProductOpNum();
        SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(pageParams.productId, productKey);
        num = item != null ? item.quantity : 0;
        boolean reachLimit = false;
        if (product != null) {
            min = product.min_order_quantity;
            max = product.getOrderMaxQuantity();
            soldStatus = product.sold_status;
            volumeThreshold = product.volume_threshold;
            reachLimit = OrderManager.get().isReachLimit(product);
        }
        layoutOp.dismissTips();

        if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(soldStatus)) {
            // 修改日期
            num = BottomOpLayout.TYPE_CHANGED_DATE;
            String changeDateTips = activity.getString(R.string.s_product_change_date_tips);
            notifyCartButtonData.accept(changeDateTips);
            layoutOp.showTips(changeDateTips, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    layoutOp.dismissTips();
                    notifyCartButtonData.accept(ProductOpHelper.DISMISS_TIPS);

                }
            });
        } else if (reachLimit) {
            // 购买已达限量
            num = BottomOpLayout.TYPE_PURCHASED;
            String purchasedTips = activity.getString(R.string.s_product_purchased_tips);
            notifyCartButtonData.accept(purchasedTips);
            layoutOp.showTips(purchasedTips, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    layoutOp.dismissTips();
                    notifyCartButtonData.accept(ProductOpHelper.DISMISS_TIPS);

                }
            });
        } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(soldStatus)) {
            // 售罄
            boolean isCollect = CollectManager.get().isProductCollect(pageParams.productId);
            num = isCollect ? BottomOpLayout.TYPE_REMINDED_YET : BottomOpLayout.TYPE_REMINDED;
            if (!isCollect) {
                String tip = activity.getString(R.string.s_product_sold_out_tips);
                if (product instanceof ProductDetailBean.ProductFeatureBean) {
                    if (!EmptyUtils.isEmpty(((ProductDetailBean.ProductFeatureBean) product).restockInfo)) {
                        tip = ((ProductDetailBean.ProductFeatureBean) product).restockInfo;
                    }
                }
                notifyCartButtonData.accept(tip);
                layoutOp.showTips(tip, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        layoutOp.dismissTips();
                        notifyCartButtonData.accept(ProductOpHelper.DISMISS_TIPS);
                    }
                });
            }
        }

        // 68换购toast
        if (product instanceof ProductDetailBean.ProductFeatureBean) {
            if (!EmptyUtils.isEmpty(((ProductDetailBean.ProductFeatureBean) product).toast_info)) {
                String toastInfo = ((ProductDetailBean.ProductFeatureBean) product).toast_info;
                notifyCartButtonData.accept(toastInfo);
                layoutOp.showTips(toastInfo, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        layoutOp.dismissTips();
                        notifyCartButtonData.accept(ProductOpHelper.DISMISS_TIPS);
                    }
                }, true);

                View tipsAction = layoutOp.findViewById(R.id.iv_tips_action);
                if (tipsAction != null) {
                    tipsAction.setVisibility(View.GONE);
                }
            }

        }

        layoutOp.setOpStyle(num, min, max);

        setLayoutOpListener(product);
    }

    private void setLayoutOpListener(ProductBean product) {
        layoutOp.setOnCartActionListener(new BottomOpLayout.OnCartActionListener() {

            @SuppressWarnings("UnnecessaryLocalVariable")
            @Override
            public void onClickLeft(View view) {
                ProductBean nonNullProduct = product;
                if (nonNullProduct == null) {
                    return;
                }
                layoutOp.dismissReachedTips();
                editProduct(/* isAdd= */false, pageParams.productId, nonNullProduct);
            }

            @SuppressWarnings("UnnecessaryLocalVariable")
            @Override
            public void onClickRight(View view) {
                ProductBean nonNullProduct = product;
                if (nonNullProduct == null) {
                    return;
                }
                int lastNum = getProductNum(pageParams.productId);
                int currentNum = editProductNum(true, pageParams.productId);
                if (currentNum > 0 && lastNum == currentNum) {
//                    adapter.notifyCartButtonData(ProductOpHelper.SHOW_REACHED_TIPS);
                    cartNotifyLiveData.postValue(ProductOpHelper.SHOW_QTY_LIMIT_REACHED);
                    layoutOp.showReachedTips(activity.getString(R.string.s_qty_limit_reached));
                } else {
                    editProduct(/* isAdd= */true, pageParams.productId, nonNullProduct);
                }
            }

            @Override
            public void onClickCart(View view) {
                if (pageParams.disableOtherPage) {
                    activity.finish();
                    return;
                }
                SharedViewModel.get().toCart();
                activity.finish();
            }

            @SuppressWarnings("UnnecessaryLocalVariable")
            @Override
            public void onClickPanel(View view) {
                ProductBean nonNullProduct = product;
                if (nonNullProduct == null) {
                    return;
                }
                boolean isReachLimit = OrderManager.get().isReachLimit(nonNullProduct);
                if (isReachLimit) {
                    // 购买已达限量
                    return;
                }
                if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(nonNullProduct.sold_status)) {
                    bottomBtnClickTrack("product_change_date", EagleTrackEvent.ClickType.VIEW);
                    //修改日期
                    activity.startActivity(DateActivity.getIntent(activity, String.valueOf(pageParams.productId), "product_modify_me"));
                } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(nonNullProduct.sold_status)) {
                    if (AccountManager.get().isLogin()) {
                        bottomBtnClickTrack("product_notify_me", EagleTrackEvent.ClickType.NORMAL);
                        layoutOp.dismissTips();
                        cartNotifyLiveData.postValue(ProductOperationManager.NOTIFY_SHOW_COLLECT_TIPS);
                    } else {
                        toLoginPage();
                    }
                } else { //正常商品
                    int num = getProductNum(pageParams.productId);
                    if (num <= 0) {
                        editProduct(/* isAdd= */true, pageParams.productId, nonNullProduct);
                    }
                }
            }
        });
    }

    private void toLoginPage() {
        activity.startActivity(AccountIntentCreator.getIntent(activity));
    }


    private void bottomBtnClickTrack(String targetType, String clickType) {
        ProductBean product = pageParams.product;
        if (product != null) {
            Map<String, Object> content = new TrackParams()
                    .put("prod_name", product.name)
                    .put("prod_id", product.id)
                    .put("price", product.price)
                    .put("sold_status", product.sold_status)
                    .put("is_pantry", product.is_pantry)
                    .put("is_limit_product", product.is_limit_product)
                    .put("is_sponsored", product.is_sponsored)
                    .put("is_hotdish", product.is_hotdish)
                    .get();
            MediaBean mediaBean = product.getFirstMedia();
            if (mediaBean != null && !TextUtils.isEmpty(mediaBean.media_url)) {
                content.put("media_url", mediaBean.media_url);
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(product.id), null, null, null, pageParams.traceId);
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setIsMkpl(product.isSeller())
                    .setMod_nm(EagleTrackEvent.ModNm.PRODUCT_DETAIL)
                    .setMod_pos(0)
                    .setTargetNm(String.valueOf(product.id))
                    .setTargetPos(0)
                    .setTargetType(targetType)
                    .addContent(content)
                    .addCtx(ctx)
                    .setClickType(clickType)
                    .build().getParams());
        }
    }

    /**
     * 编辑产品 - 添加或减少数量
     */
    public void editProduct(boolean isAdd, int productId, @NonNull ProductBean product) {
        VibratorManager.vibrate();
        int lastNum = getProductNum(productId);
        int num = editProductNum(isAdd, productId);
        lastQuantity = lastNum;

        if (lastNum <= 0 && num > 0 && PresaleOpHelper.shouldShowPresaleBottomSheetDialog(product)) {
            // 预售商品处理
            showPresaleBottomSheetDialog(isAdd, productId, lastNum, num, product);
        } else {
            layoutOp.setOpStyle(num, min, max);
            if (num == volumeThreshold - 1 && lastNum > num && product.volume_price_support) {
                cartNotifyLiveData.postValue(ProductOpHelper.SHOW_REACHED_TIPS);
                layoutOp.showReachedTips(
                        activity.getString(R.string.s_save_ea, product.getDiff()),
                        activity.getString(R.string.s_when_you_buy_more, product.volume_threshold)
                );
            } else if (lastNum <= 0 && num > 1) {
                if (volumeThreshold != num) {
                    cartNotifyLiveData.postValue(ProductOpHelper.SHOW_MIN_PURCHASE_TIPS);
                    layoutOp.showMinPurchaseTips(num);
                }
            } else {
                if (num <= 0) {
                    cartNotifyLiveData.postValue(ProductOpHelper.DISMISS_MIN_PURCHASE_TIPS);
                    layoutOp.dismissMinPurchaseTips();
                }
                cartNotifyLiveData.postValue(ProductOpHelper.SHOW_FIRST_ADD_INTRODUCE);
                // PDP 底部大按钮不显示酒类协议弹窗
                if (lastNum <= 0 && num > 0 && !product.isAlcohol()) {
                    OpHelper.showFirstAddIntroduce(activity, layoutOp.findViewById(R.id.iv_edit_right),
                            product, lastNum, num, /* extraBottom= */10);
                }
            }
            afterEditProduct(isAdd, productId, lastNum, num, product);
        }
    }

    /**
     * 显示预售商品底部对话框
     */
    private void showPresaleBottomSheetDialog(boolean isAdd, int productId, int lastNum, int num, @NonNull ProductBean product) {
        Consumer<Integer> afterAddToCart = targetQty -> {
            if (targetQty > 0) {
                layoutOp.setOpStyle(targetQty, product.min_order_quantity, product.getOrderMaxQuantity());
                product.setProductQuantity(targetQty);
                OpHelper.showFirstAddIntroduce(activity, layoutOp.findViewById(R.id.iv_edit_right),
                        product, lastNum, targetQty, /* extraBottom= */10);
                afterEditProduct(isAdd, productId, lastNum, targetQty, product);
            }
        };
        PresaleOpHelper.showPresaleBottomSheetDialog(activity, product, afterAddToCart);
    }

    /**
     * 在编辑产品后执行的操作
     */
    private void afterEditProduct(boolean isAdd, int productId, int lastNum, int num, @NonNull ProductBean product) {
        String referType = product.getProductType();
        String referValue = null;
        if (Constants.ProductType.GLOBAL.equals(referType) && product.vender_info_view != null) {
            referValue = String.valueOf(product.vender_info_view.vender_id);
        }

        Map<String, Object> element = EagleTrackManger.get().getElement(
                EagleTrackEvent.ModNm.PRODUCT_DETAIL, 0, null, -1);
        String source = product.isSeller() ? Constants.Source.PRODUCT_MKPL_CART : Constants.Source.PRODUCT_CART;
        OrderManager.get().setProductChanged(productId, num,
                EmptyUtils.isEmpty(pageParams.cartSource) ? source : pageParams.cartSource,
                referType, referValue, productKey, null, OpHelper.buildNewSource(element, product.prod_pos));

        if (isAdd) {
            PopupManager.get().showAdd2PurchaseNotificationTips();
            String name = product.name;
            String category = product.category;
            // add_to_cart 埋点 FirebaseAnalytics.Event.ADD_TO_CART
            AppTracker.get().trackByGA(FirebaseAnalytics.Event.ADD_TO_CART, new TrackParams()
                    .put(FirebaseAnalytics.Param.QUANTITY, 1)
                    .put(FirebaseAnalytics.Param.ITEM_ID, productId)
                    .put(FirebaseAnalytics.Param.ITEM_NAME, name)
                    .put(FirebaseAnalytics.Param.ITEM_CATEGORY, category)
                    .get());
        }

        // eagle track
        Map<String, Object> content = new TrackParams()
                .put("prod_pos", 0)
                .put("prod_id", productId)
                .put("old_qty", lastNum)
                .put("new_qty", num)
                .put("refer_type", product.getProductType())
                .put("source", Constants.Source.PRODUCT_CART)
                .put("is_mkpl", product.isSeller())
                .put("is_fbw", product.isFbw())
                .put("biz_type", product.biz_type)
                .put("volume_price_support", product.volume_price_support)
                .put("is_sponsored", product.is_sponsored)
                .put("is_manual", product.is_manual)
                .put("is_presale", product.is_presale)
                .put("sale_event_id", product.sale_event_id)
                .get();

        MediaBean mediaBean = product.getFirstMedia();
        if (mediaBean != null && !TextUtils.isEmpty(mediaBean.media_url)) {
            content.put("media_url", mediaBean.media_url);
        }
        if (CollectionUtils.isNotEmpty(product.barInfoModules)) {
            ProductBean.BarInfoModules infoModule = CollectionUtils.firstOrNull(product.barInfoModules, ProductBean.BarInfoModules::hasTop);
            if (infoModule != null) {
                content.put("tag_key", infoModule.key);
                content.put("tag_name", infoModule.info_html);
            }
        }

        String sort = pageParams.isSearchV2 ? pageParams.searchV2Sort : null;
        Map filters = pageParams.isSearchV2 ? pageParams.searchV2Filters : null;
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, sort, filters,
                String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
        if (!EmptyUtils.isEmpty(groupFilter)) {
            ctx.put("group_filter", groupFilter);
            ctx.put("change_group_filter", changeGroupFilter);
        }
        if (pageParams.isSearchV2) {
            product.setProductQuantity(num);
            SearchV2Manager.get().appendEagleTrackContextParams(ctx,
                    pageParams.searchV2SectionTag, pageParams.searchV2SectionId, pageParams.searchV2SectionType,
                    pageParams.searchV2SectionDisplayType, pageParams.searchV2SubSectionId, pageParams.searchV2SubSectionTag);

            Intent intent = new Intent(SearchResultsFragmentV2.ACTION_CART_UPDATE);
            intent.putExtra("productId", productId);
            intent.putExtra("productBean", product);
            intent.putExtra("position", pageParams.searchV2Position);
            intent.putExtra("isAdd", isAdd);
            LocalBroadcastManager.getInstance(activity)
                    .sendBroadcast(intent);
        }

        AppAnalytics.logCartAction(new EagleTrackModel.Builder()
                .addElement(element)
                .addContent(content)
                .addCtx(ctx)
                .build().getParams());

        cartOftenNotifyLiveData.postValue(NOTIFY_OFTEN_PAIRED_WITH);

        if (isAdd && product.ads_creative != null) {
            AdsManager.get().trackAddToCart(
                    /* productBean= */product,
                    /* position= */product.prod_pos,
                    /* dbgInfo= */"NewProductDetailActivity.afterEditProduct"
            );
        } else if (isAdd && adsTrackPendingInfo != null) {
            if (adsTrackPendingInfo.has("ads_creative") && adsTrackPendingInfo.opt("ads_creative") instanceof AdsCreativeBean) {
                AdsCreativeBean adsCreative = (AdsCreativeBean) adsTrackPendingInfo.opt("ads_creative");
                if (adsCreative != null) {
                    product.ads_creative = adsCreative;
                    Map<String, Object> logParams = new HashMap<>();
                    logParams.put(AdsManager.PARAM_ADS_TRACK_SKU_NUM, num);
                    logParams.put(AdsManager.PARAM_ADS_TRACK_SKU_LAST_NUM, lastNum);
                    AdsManager.get().trackAddToCart(
                            /* productBean= */product,
                            /* position= */product.prod_pos,
                            /* dbgInfo= */"weeeProductAddToCartFromPdp"
                    );
                }
            } else {
                try {
                    final String url = adsTrackPendingInfo.optString(AdsManager.PARAM_ADS_TRACK_URL, null);
                    if (url != null && !url.isEmpty()) {
                        final int position = adsTrackPendingInfo.optInt(AdsManager.PARAM_ADS_TRACK_POSITION);
                        adsTrackPendingInfo.put(AdsManager.PARAM_ADS_TRACK_SKU_NUM, num);
                        adsTrackPendingInfo.put(AdsManager.PARAM_ADS_TRACK_SKU_LAST_NUM, lastNum);
                        AdsManager.get().track(url, AdsManager.TRACK_ADD_TO_CART_URL, position, null);
                        adsTrackPendingInfo.remove(AdsManager.PARAM_ADS_TRACK_SKU_NUM);
                        adsTrackPendingInfo.remove(AdsManager.PARAM_ADS_TRACK_SKU_LAST_NUM);
                        if (!SearchResultsFragmentV2.ENABLE_SEARCH_RESULTS_ALWAYS_TRACK_ADD_TO_CART_CONVERSION) {
                            adsTrackPendingInfo.remove(AdsManager.PARAM_ADS_TRACK_URL);
                            adsTrackPendingInfo.remove(AdsManager.PARAM_ADS_TRACK_POSITION);
                        }
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }

    /**
     * 获取产品数量
     */
    public int getProductNum(int productId) {
        SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(productId, productKey);
        return item != null ? item.quantity : 0;
    }

    /**
     * 编辑产品数量
     */
    public int editProductNum(boolean isAdd, int productId) {
        int num = getProductNum(productId);
        return OrderHelper.editNum(isAdd, num, min, max, volumeThreshold);
    }


    public void setNum(int num) {
        this.num = num;
    }

    public int getNum() {
        return num;
    }

    public int getMin() {
        return min;
    }

    public int getMax() {
        return max;
    }

    public boolean isAddToCart(int productId) {
        return lastQuantity <= getProductNum(productId);
    }

    public void setLastQuantity(int lastQuantity){
        this.lastQuantity = lastQuantity;
    }
}