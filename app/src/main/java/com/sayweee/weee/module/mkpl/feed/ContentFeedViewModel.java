package com.sayweee.weee.module.mkpl.feed;

import android.annotation.SuppressLint;
import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentCategoryBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.mkpl.provider.data.CmsContentFeedStore;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedParser;
import com.sayweee.weee.module.post.bean.ProductNewBean;
import com.sayweee.weee.module.post.explore.provider.data.PostExploreItemData;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Observer;

/**
 * Desc:
 */
public class ContentFeedViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public MutableLiveData<List<AdapterDataType>> adapterFeedData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterDataType>> adapterAppendData = new MutableLiveData<>();
    public MutableLiveData<FailureBean> failureData = new MutableLiveData<>();

    public MutableLiveData<List<ProductNewBean>> postProductsNewBean = new MutableLiveData<>();
    private boolean isRelateProductLoading = false;

    public String recommendSession;
    public String fromPage;

    @Nullable
    public CmsContentFeedPacket contentFeedPacket;
    @Nullable
    public CmsContentCategoryBean categoryBean;

    @Nullable
    public CmsContentFeedStore dataStore;

    public ContentFeedViewModel(@NonNull Application application) {
        super(application);
    }

    public void initData() {
        List<AdapterDataType> items = new ArrayList<>();
        CmsContentFeedStore store = this.dataStore;
        if (store != null) {
            CmsContentFeedParser.parseContents(
                    /* items= */items,
                    /* contents= */store.getData(),
                    /* packet= */contentFeedPacket,
                    /* categoryBean= */categoryBean,
                    /* fromPage= */fromPage
            );
        }
        assembleProductTrace(items);
        adapterFeedData.postValue(items);
    }

    public void fetchFeedData(CmsContentCategoryBean bean) {
        CmsContentFeedStore store = this.dataStore;
        if (store == null) {
            return;
        }
        CmsDataSource source = store.getDataSource();
        String url = source.getUrl();
        Map<String, String> params = source.getQueryParams();
        getLoader().getHttpService()
                .getContentFeed(url, params)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(getContentFeedObserver(/* isAppend= */false));
    }

    public void fetchMoreFeedData() {
        CmsContentFeedStore store = this.dataStore;
        if (store == null) {
            adapterAppendData.postValue(Collections.emptyList());
            return;
        }

        CmsDataSource source = store.getDataSource();
        String url = source.getUrl();
        Map<String, String> params = source.getQueryParams();
        int pageNum = DecimalTools.intValue(params.get("page_num"), 1) + 1;
        source.putQueryParam("page_num", String.valueOf(pageNum));
        params.put("page_num", String.valueOf(pageNum));
        getLoader()
                .getHttpService()
                .getContentFeed(url, params)
                .compose(DisposableTransformer.scheduler(ContentFeedViewModel.this))
                .subscribe(getContentFeedObserver(/* isAppend= */true));
    }

    protected Observer<ResponseBean<CmsContentFeedListBean>> getContentFeedObserver(boolean isAppend) {
        return new ResponseObserver<ResponseBean<CmsContentFeedListBean>>() {
            @Override
            public void onResponse(ResponseBean<CmsContentFeedListBean> response) {
                CmsContentFeedListBean listBean = response.getData();

                List<AdapterDataType> items = new ArrayList<>();
                CmsContentFeedParser.parseContents(
                        /* items= */items,
                        /* contents= */listBean.contents,
                        /* packet= */contentFeedPacket,
                        /* categoryBean= */categoryBean,
                        /* fromPage= */fromPage
                );
                assembleProductTrace(items);

                if (ContentFeedViewModel.this.dataStore != null) {
                    ContentFeedViewModel.this.dataStore.appendData(listBean.contents);
                }

                if (isAppend) {
                    CmsContentFeedParser.setLightningDealsSystemTime(listBean);
                    if (contentFeedPacket != null) {
                        contentFeedPacket.lastPageSize += CollectionUtils.size(listBean.contents);
                    }
                }

                if (isAppend) {
                    adapterAppendData.postValue(items);
                } else {
                    adapterFeedData.postValue(items);
                }
            }

            @Override
            public void onError(FailureBean failure) {
                if (isAppend) {
                    adapterAppendData.postValue(Collections.emptyList());
                } else {
                    failureData.postValue(failure);
                }
            }
        };
    }

    private void assembleProductTrace(List<AdapterDataType> items) {
        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(items);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(getProductTraceTopic(), fromPage));
    }

    public boolean getPostNewProducts(PostExploreItemData data, String ids, boolean isShow) {
        if (isRelateProductLoading && isShow) return false;
        if (isShow) {
            isRelateProductLoading = true;
        }
        getLoader().getHttpService()
                .getPostNewProducts(ids)
                .compose(ResponseTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<List<ProductNewBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<ProductNewBean>> response) {
                        data.relateProducts = response.getData();
                        if (isShow) {
                            postProductsNewBean.postValue(response.getData());
                        }
                    }

                    /** @noinspection ResultOfMethodCallIgnored*/
                    @SuppressLint("CheckResult")
                    @Override
                    public void onFinish() {
                        super.onFinish();
                        Observable.just(1)
                                .delay(200L, TimeUnit.MILLISECONDS)
                                .compose(DisposableTransformer.scheduler(ContentFeedViewModel.this, true))
                                .subscribe(sig -> isRelateProductLoading = false);
                    }
                });
        return isRelateProductLoading;
    }

    public String getProductTraceTopic() {
        String topic = fromPage;
        if (contentFeedPacket != null && contentFeedPacket.componentKey != null) {
            topic += "-" + contentFeedPacket.componentKey;
        }
        return topic;
    }

}
