package com.sayweee.weee.module.account;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.viewpager.widget.ViewPager;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.WeChatManager;
import com.sayweee.weee.module.account.bean.LoginBean;
import com.sayweee.weee.module.account.bean.LoginOptionBean;
import com.sayweee.weee.module.account.bean.LoginOptionsBean;
import com.sayweee.weee.module.account.bean.LoginShowMessageBean;
import com.sayweee.weee.module.account.service.AccountHelper;
import com.sayweee.weee.module.account.service.AccountViewModel;
import com.sayweee.weee.module.account.service.LoginMethodViewModel;
import com.sayweee.weee.module.debug.info.HostActivity;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.launch.service.ReferrerManager;
import com.sayweee.weee.service.share.ShareHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DefaultTools;
import com.sayweee.weee.utils.SimpleTextWatcher;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.indicator.CompatColorTransitionPagerTitleView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.adapter.SimplePagerAdapter;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.KeyboardUtils;

import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.ViewPagerHelper;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LoginActivity extends LoginCoreActivity<LoginMethodViewModel> {

    static final int TYPE_ACCOUNT = 101;
    static final int TYPE_MOBILE = 102;
    int type = TYPE_ACCOUNT;
    private EditText etAccount, etMobile;
    private TextView tvTips, tvPhoneNext, tvAccountNext, tvPhonePrefix;
    private View layoutAccount, layoutPhone;
    private LinearLayout layoutTypeContainer;
    private MagicIndicator indicator;
    private ViewPager pager;
    private ImageView ivAccountClear, ivPhoneClear, ivAccountIcon;
    private View loadingSpinner;
    private View tvMarkEmail, tvMarkMobile;

    public static Intent getIntent(Context context) {
        ReferrerManager.get().recordStartPage();
        return new Intent(context, LoginActivity.class).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_login;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        AccountHelper.putActivity(activity);
        setWrapperDivider(null);
        referrerId = ReferrerManager.get().getLatestReferrerId();
        indicator = findViewById(R.id.indicator);
        pager = findViewById(R.id.pager);
        tvTips = findViewById(R.id.tv_tips);
        layoutTypeContainer = findViewById(R.id.layout_type_container);
        tvMarkEmail = findViewById(R.id.tv_mark_email);
        tvMarkMobile = findViewById(R.id.tv_mark_mobile);
        fillInputView();

        findViewById(R.id.tv_title).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toHost();
            }
        });
    }

    @Override
    public void loadData() {
        viewModel.getLoginOption();
    }

    @Override
    public void attachModel() {
        viewModel.btnEnable.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean bool) {
                setNextButtonEnable(bool);
                loadingSpinner(false);
            }
        });

        viewModel.loginOptionData.observe(this, new Observer<LoginOptionsBean>() {
            @Override
            public void onChanged(LoginOptionsBean result) {
                processOption(result);
            }
        });

        viewModel.loginData.observe(this, new Observer<LoginBean>() {
            @Override
            public void onChanged(LoginBean bean) {
                processLogin(bean);
            }
        });

        viewModel.accountCheckData.observe(this, new Observer<LoginShowMessageBean>() {
            @Override
            public void onChanged(LoginShowMessageBean loginShowMessageBean) {
                if (loginShowMessageBean.is_first_time_user) {
                    startActivityForResult(NewAccountVerifyActivity.getIntentByEmail(activity, loginShowMessageBean.code, etAccount.getText().toString(), true, false), 100);//新版邮箱校验页面
                } else {
                    startActivityForResult(PasswordActivity.getIntent(activity, false, etAccount.getText().toString(), null), 100);
                }
            }
        });

        viewModel.registerResultData.observe(this, new Observer<LoginBean>() {
            @Override
            public void onChanged(LoginBean loginBean) {
                String account = etAccount.getText().toString();
                if (loginBean.is_edu_email_check) {
                    startActivity(StudentRuleActivity.getIntent(activity, account, loginBean, channel));
                } else {
                    startActivity(PasswordActivity.getIntent(activity, true, account, loginBean));
                }
                setResult(RESULT_OK);
                finish();
            }
        });

        viewModel.failureLiveData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                String message = failureBean.getMessage();
                if (message != null && message.length() > 0) {
                    if (AccountViewModel.CODE_TIPS.equals(failureBean.getMessageId())) {
                        showErrorDialog(ViewTools.fromHtml(message));
                    } else if (AccountViewModel.CODE_TIPS_10031.equals(failureBean.getMessageId())) {
                        startActivityForResult(NewAccountVerifyActivity.getIntentByEmail(activity, message, etAccount.getText().toString(), false, true), 100);
                    } else {
                        if (showThirdLoginErrorToast(message)) {
                            return;
                        }
                        CharSequence sequence = DefaultTools.processHtmlLink(message);
                        if (isAccountMode()) {
                            setErrorTips(etAccount, tvTips, layoutAccount, ivAccountClear, tvAccountNext, sequence);
                        } else {
                            setErrorTips(etMobile, tvTips, layoutPhone, ivPhoneClear, tvPhoneNext, sequence);
                        }
                    }
                }
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (onActivityResultByFinish(requestCode, resultCode, data)) {
            return;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onDestroy() {
        AccountHelper.removeActivity(activity);
        super.onDestroy();
    }

    @Override
    protected String getAccount() {
        if (isAccountMode() && etAccount != null) {
            return etAccount.getText().toString();
        }
        return super.getAccount();
    }

    protected void fillIndicatorView(final List<String> list) {
        CommonNavigator navigator = new CommonNavigator(activity);
        navigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return list == null ? 0 : list.size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int i) {
                CompatColorTransitionPagerTitleView view = new CompatColorTransitionPagerTitleView(context);
                ViewTools.applyTextStyle(view, R.style.style_fluid_root_utility_base);
                view.setText(list.get(i));
                int padding = CommonTools.dp2px(12);
                view.setPadding(padding, 0, padding, 0);
                view.setNormalColor(ContextCompat.getColor(activity, R.color.color_surface_1_fg_minor_idle));
                view.setSelectedColor(ContextCompat.getColor(activity, R.color.color_primary_atmosphere_blue));

                view.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        pager.setCurrentItem(i);
                    }
                });
                return view;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                LinePagerIndicator indicator = new LinePagerIndicator(context);
                indicator.setLineHeight(CommonTools.dp2px(2.5f));
                indicator.setRoundRadius(CommonTools.dp2px(1.5f));
                indicator.setLineWidth(CommonTools.dp2px(32));
                indicator.setMode(LinePagerIndicator.MODE_MATCH_EDGE);
                indicator.setColors(ContextCompat.getColor(activity, R.color.color_primary_atmosphere_blue));
                return indicator;
            }
        });
        navigator.setAdjustMode(true);
        indicator.setNavigator(navigator);
        ViewPagerHelper.bind(indicator, pager);
    }

    protected void fillInputView() {
        List<View> list = new ArrayList<>();
        list.add(getAccountInputView());
        list.add(getMobileInputView());
        SimplePagerAdapter adapter = new SimplePagerAdapter(list);
        pager.setAdapter(adapter);
        pager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                type = position == 0 ? TYPE_ACCOUNT : TYPE_MOBILE;
                showSoftInput(position == 0 ? etAccount : etMobile);
                clearInputStatus();
                setNextButtonEnable(true);
            }
        });
        String[] title = {getString(R.string.s_email), getString(R.string.s_mobile)};
        fillIndicatorView(Arrays.asList(title));
        ViewTools.setViewVisibilityIfChanged(tvMarkEmail, isLastLogin(WeeeEvent.TYPE_EMAIL));
        ViewTools.setViewVisibilityIfChanged(tvMarkMobile, isLastLogin(WeeeEvent.TYPE_PHONE));
    }

    protected boolean isAccountMode() {
        return type == TYPE_ACCOUNT;
    }

    private View getAccountInputView() {
        return DefaultTools.getHelperView(pager, R.layout.view_input_new_account, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                etAccount = helper.getView(R.id.et_account);
                layoutAccount = helper.getView(R.id.layout_account);
                ivAccountClear = helper.getView(R.id.iv_account_clear);
                ivAccountIcon = helper.getView(R.id.iv_account_icon);
                tvAccountNext = helper.getView(R.id.tv_account_next);
                loadingSpinner = helper.getView(R.id.loading_progressbar);
                showSoftInput(etAccount);
                etAccount.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_NEXT) {
                            onAccountTrigger();
                            return true;
                        }
                        return false;
                    }
                });
                etAccount.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        attachInputFocusStatus(hasFocus);
                    }
                });
                etAccount.addTextChangedListener(new SimpleTextWatcher() {
                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        super.onTextChanged(s, start, before, count);
                        clearInputStatus();
                    }
                });
                tvAccountNext.setOnClickListener(new OnSafeClickListener(1000) {
                    @Override
                    public void onClickSafely(View v) {
                        onAccountTrigger();
                    }
                });

                helper.setOnClickListener(R.id.iv_account_clear, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        etAccount.setText("");
                        clearInputStatus();
                    }
                });
            }
        });
    }

    private View getMobileInputView() {
        return DefaultTools.getHelperView(pager, R.layout.view_input_new_phone, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                etMobile = helper.getView(R.id.et_mobile);
                layoutPhone = helper.getView(R.id.layout_phone);
                ivPhoneClear = helper.getView(R.id.iv_phone_clear);
                tvPhoneNext = helper.getView(R.id.tv_phone_next);
                tvPhonePrefix = helper.getView(R.id.tv_mobile_prefix);
                etMobile.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_NEXT) {
                            onCodeTrigger();
                            return true;
                        }
                        return false;
                    }
                });
                etMobile.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        attachInputFocusStatus(hasFocus);
                    }
                });
                etMobile.addTextChangedListener(new SimpleTextWatcher() {
                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        super.onTextChanged(s, start, before, count);
                        clearInputStatus();
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        if (s != null && s.length() > 0) {
                            etMobile.removeTextChangedListener(this);
                            int start = etMobile.getSelectionStart();
                            boolean inEnd = start >= s.length() - 1;
                            etMobile.setText(DefaultTools.formatMobile(s.toString()));
                            int index = inEnd ? etMobile.getText().toString().length() : start;
                            if (index >= 0) {
                                etMobile.setSelection(index);
                            }
                            etMobile.addTextChangedListener(this);
                        }

                    }
                });
                tvPhoneNext.setOnClickListener(new OnSafeClickListener(1000) {
                    @Override
                    public void onClickSafely(View v) {
                        onCodeTrigger();
                    }
                });

                helper.setOnClickListener(R.id.iv_phone_clear, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        etMobile.setText("");
                        clearInputStatus();
                    }
                });
            }
        });
    }


    /*********************************************************************************/
    /**
     * fill third auth type
     **/
    private void processOption(LoginOptionsBean options) {
        if (options != null) {
            fillOptionView(options.connects);
        }
    }

    protected void fillOptionView(List<LoginOptionBean> list) {
        layoutTypeContainer.removeAllViews();
        if (list != null && !list.isEmpty()) {
            int height = LanguageManager.get().isVietnamese() ? 85 : 80;//mark越南语展示两行
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(CommonTools.dp2px(78), CommonTools.dp2px(height));
            for (int i = 0; i < list.size(); i++) {
                LoginOptionBean item = list.get(i);
                if (item.name != null && !AppFilter.AuthConfig.isNotSupport(item.name)) {
                    fillItemCircleView(layoutTypeContainer, params, item.name);
                }
            }
        }
    }

    protected void fillItemCircleView(LinearLayout container, LinearLayout.LayoutParams params, String name) {
        switch (name) {
            case Constants.AuthOption.OPTION_WECHAT:
                if (WeChatManager.getInstance().isWXAppInstalled()) {
                    fillItemCircleView(container, params, name, getString(R.string.s_auth_wechat), R.mipmap.auth_type_wechat, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            execWeChatLogin();
                            saveLastLogin(name);
                        }
                    });
                }
                break;
            case Constants.AuthOption.OPTION_FACEBOOK:
                fillItemCircleView(layoutTypeContainer, params, name, getString(R.string.s_auth_facebook), R.mipmap.auth_type_facebook, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        execFacebookLogin();
                        saveLastLogin(name);
                    }
                });
                break;
            case Constants.AuthOption.OPTION_KAKAO:
                if (ShareHelper.isKakaoInstall(activity)) {
                    fillItemCircleView(layoutTypeContainer, params, name, getString(R.string.s_auth_kakao), R.mipmap.login_type_kakao, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            execKakaoLogin();
                            saveLastLogin(name);
                        }
                    });
                }
                break;
            case Constants.AuthOption.OPTION_GOOGLE:
                fillItemCircleView(layoutTypeContainer, params, name, getString(R.string.s_auth_google), R.mipmap.auth_type_google, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        execGoogleLogin();
                        saveLastLogin(name);
                    }
                });
                break;

            case Constants.AuthOption.OPTION_LINE:
                if (ShareHelper.isLineInstall(activity)) {
                    fillItemCircleView(layoutTypeContainer, params, name, getString(R.string.s_auth_line), R.mipmap.auth_type_line, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            execLineLogin();
                            saveLastLogin(name);
                        }
                    });
                }
            case Constants.AuthOption.OPTION_TIKTOK:
                if (ShareHelper.isTiktokInstall(activity)) {
                    fillItemCircleView(layoutTypeContainer, params, name, getString(R.string.s_auth_tiktok), R.mipmap.auth_type_tiktok, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            execTikTokLogin();
                            saveLastLogin(name);
                        }
                    });
                }
                break;

        }
    }

    protected void fillItemCircleView(LinearLayout parent, LinearLayout.LayoutParams params, String name, String title, @DrawableRes int imageRes, View.OnClickListener listener) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_new_login_type_circle, parent, false);
        ((ImageView) view.findViewById(R.id.iv_login_type)).setImageResource(imageRes);
        TalkBackHelper.setContentDesc(view, getString(R.string.a_login_via, title));
        if (listener != null) {
            view.setOnClickListener(listener);
        }
        parent.addView(view, params);
        View tvMark = view.findViewById(R.id.tv_mark);
        ViewTools.setViewVisibilityIfChanged(tvMark, isLastLogin(name));
    }

    /*********************************************************************************/

    protected boolean onActivityResultByFinish(int requestCode, int resultCode, Intent data) {
        if (resultCode == RESULT_OK && requestCode == 100) {
            if (data != null && data.getBooleanExtra("clearEmail", false)) {
                etAccount.setText("");
                etMobile.setText("");
                pager.setCurrentItem(0);
            } else {
                setResult(RESULT_OK);
                finish();
            }
            return true;
        }
        return false;
    }

    protected void onAccountTrigger() {
        String account = DefaultTools.getText(etAccount);
        if (TextUtils.isEmpty(account)) {
            setErrorTips(etAccount, tvTips, layoutAccount, ivAccountClear, tvAccountNext, getString(R.string.s_input_email));
            return;
        }
        if (!CommonTools.isEmail(account)) {
            setErrorTips(etAccount, tvTips, layoutAccount, ivAccountClear, tvAccountNext, getString(R.string.s_input_valid_email));
            return;
        }
        setNextButtonEnable(false);
        channel = WeeeEvent.TYPE_EMAIL;
        viewModel.checkAccount(account);
        //loading
        loadingSpinner(true);
        saveLastLogin(channel);
    }

    private void loadingSpinner(boolean isLoading) {
        ViewTools.setViewVisible(loadingSpinner, isLoading);
        ViewTools.applyTextColor(tvAccountNext, isLoading ? R.color.color_btn_primary_bg : R.color.color_primary_surface_1_fg_default_idle);
    }

    protected void setErrorTips(TextView target, TextView tipsView, View parentView, ImageView ivClear, TextView tvCommit, CharSequence msg) {
        int errorColor = ContextCompat.getColor(activity, R.color.color_critical_surface_1_bg_idle);
        if (isAccountMode()) {
            ivAccountIcon.setImageResource(R.mipmap.pic_account_user_error);
        } else {
            tvPhonePrefix.setTextColor(errorColor);
        }
        target.setSelected(true);
        showSoftInput((EditText) target);
        ivClear.setVisibility(View.VISIBLE);
        tvCommit.setVisibility(View.GONE);
        tipsView.setTextColor(errorColor);
        parentView.setBackground(ShapeHelper.buildStrokeDrawable(errorColor, CommonTools.dp2px(2), CommonTools.dp2px(11)));
        setErrorTips(msg);
    }

    protected void setErrorTips(CharSequence msg) {
        boolean empty = TextUtils.isEmpty(msg);
        tvTips.setVisibility(empty ? View.GONE : View.VISIBLE);
        tvTips.setText(msg);
        tvTips.setMovementMethod(LinkMovementMethod.getInstance());
    }

    protected void showErrorDialog(CharSequence msg) {
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setUp(new OnDialogClickListener() {
                    @Override
                    public void onClick(WrapperDialog dialog, View view) {
                        dialog.dismiss();
                        setResult(RESULT_OK, new Intent().putExtra("clearEmail", true));
                        finish();
                    }
                }, msg, getString(R.string.s_i_known)).setCanceledOnTouchOutside(false)
                .show();
    }

    protected void onCodeTrigger() {
        String mobile = DefaultTools.getText(etMobile);
        if (TextUtils.isEmpty(mobile)) {
            setErrorTips(etMobile, tvTips, layoutPhone, ivPhoneClear, tvPhoneNext, getString(R.string.s_input_mobile));
            return;
        }
        if (!CommonTools.isValidPhoneNumber(mobile)) {
            setErrorTips(etMobile, tvTips, layoutPhone, ivPhoneClear, tvPhoneNext, getString(R.string.s_input_valid_mobile));
            return;
        }
        channel = WeeeEvent.TYPE_PHONE;
        startActivityForResult(NewAccountVerifyActivity.getIntentByPhone(activity, mobile, false, null, false), 100);
        saveLastLogin(channel);
    }

    public void showSoftInput(EditText etInput) {
        if (etInput == null || activity == null) return;
        etInput.setFocusable(true);
        etInput.setFocusableInTouchMode(true);
        etInput.requestFocus();
        etInput.postDelayed(new Runnable() {
            @Override
            public void run() {
                InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
                KeyboardUtils.showSoftInput(imm, etInput);
            }
        }, 200);
    }

    protected void clearInputStatus() {
        if (isAccountMode()) {
            attachInputFocusStatus(etAccount.isFocusable());
        } else {
            attachInputFocusStatus(etMobile.isFocusable());
        }
        tvTips.setVisibility(View.GONE);
    }

    protected void attachInputFocusStatus(boolean focusable) {
        if (isAccountMode()) {
            tvAccountNext.setVisibility(View.VISIBLE);
            ivAccountClear.setVisibility(View.GONE);
            ivAccountIcon.setImageResource(focusable ? R.mipmap.pic_user_black : R.mipmap.pic_account_user);
            layoutAccount.setBackground(ShapeHelper.buildStrokeDrawable(ContextCompat.getColor(activity, focusable ? R.color.color_surface_1_fg_default_idle : R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(2), CommonTools.dp2px(11)));
        } else {
            tvPhoneNext.setVisibility(View.VISIBLE);
            ivPhoneClear.setVisibility(View.GONE);
            tvPhonePrefix.setTextColor(ContextCompat.getColor(activity, focusable ? R.color.color_surface_1_fg_default_idle : R.color.color_surface_1_fg_subtle_idle));
            layoutPhone.setBackground(ShapeHelper.buildStrokeDrawable(ContextCompat.getColor(activity, focusable ? R.color.color_surface_1_fg_default_idle : R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(2), CommonTools.dp2px(11)));
        }
    }

    private void setNextButtonEnable(boolean enable) {
        if (tvAccountNext != null) {
            tvAccountNext.setEnabled(enable);
        }
    }

    /*********************************************************************************/
    static final int COUNTS = 7;// 点击次数
    static final long DURATION = 1000;// 规定有效时间
    long[] mHits = new long[COUNTS];

    void toHost() {
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        mHits[mHits.length - 1] = System.currentTimeMillis();
        if (mHits[0] >= (System.currentTimeMillis() - DURATION)) {
            mHits = new long[COUNTS];//重新初始化数组
            new CompatDialog(activity) {
                @Override
                protected void setDialogParams(Dialog dialog) {
                    super.setDialogParams(dialog);
                    dialog.setCanceledOnTouchOutside(false);
                }
            }
                    .setTitleUp(new OnDialogClickListener() {
                        @Override
                        public void onClick(WrapperDialog dialog, View view) {
                            dialog.dismiss();
                            startActivity(HostActivity.getIntent(activity));
                        }
                    }, "Warm prompt", "This operate is only for testers, if not, please click \'Cancel\' to exit.", "OK", "Cancel")
                    .show();
        }
    }

    public boolean isLastLogin(String name) {
        return VariantConfig.LAST_LOGIN_MARK_ENABLED && name != null && name.equals(getLastLoginSuccessName());
    }
}
