package com.sayweee.weee.module.launch;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewStub;
import android.webkit.WebSettings;
import android.widget.ImageView;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.bumptech.glide.Glide;
import com.gyf.immersionbar.ImmersionBar;
import com.sayweee.logger.Logger;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.SessionService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.DeepLinkManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.MainActivity;
import com.sayweee.weee.module.account.LoginActivity;
import com.sayweee.weee.module.account.helper.TiktokHelper;
import com.sayweee.weee.module.launch.bean.LanguageBean;
import com.sayweee.weee.module.launch.bean.LaunchHelpBean;
import com.sayweee.weee.module.launch.service.LaunchApi;
import com.sayweee.weee.module.launch.service.LaunchModel;
import com.sayweee.weee.module.launch.service.LaunchViewModel;
import com.sayweee.weee.module.launch.service.SessionTokenHelper;
import com.sayweee.weee.module.launch.service.SplashManager;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.launch.service.UpgradeHelper;
import com.sayweee.weee.module.oauth.OAuthService;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.helper.WebHelper;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.utils.KeyboardTrackHelper;
import com.sayweee.weee.service.helper.AppStatusManager;
import com.sayweee.weee.service.helper.PushHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/16.
 * Desc:
 * 流程 >>
 * 1.判断是否deep link唤醒 > 否: 后续流程
 * > 是：解析数据 > 判断是否已打开首页 >  未打开 后续流程  > 打开 直接进入
 * 2.检验获取sessionToken
 * 3.获取或者刷新token
 * 4.是否新用户 >> 选择语言 >> 引导流程  >> 登陆 >> 选择zip code >> 生成preOrder  首页
 * 4.老用户 >> 获取preOrder 进入首页
 */
public class SplashActivity extends WrapperMvvmActivity<LaunchViewModel> {

    public static final String CODE_EXTRA = "code_extra";
    public static final int CODE_EXIT = 10101;

    public static Intent getIntent(Context context) {
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        return intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
    }

    long initTimestamp;
    long delayTime = 600;
    String type; //唤醒类型，用于创建session
    String url; //唤醒的url，用于创建session
    boolean retryLaunchByIntent;
    boolean isPaused;

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_splash;
    }

    @Override
    protected void initStatusBar() {
        ImmersionBar.with(this)
                .statusBarDarkFont(true)
                .transparentNavigationBar()
                .init();
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent.getIntExtra(CODE_EXTRA, -1) == CODE_EXIT) {
            // 主动退出
            finish();
            return;
        }
        if ((intent.getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) > 0) {
            // 为了防止重复启动多个闪屏页面
            finish();
            return;
        }
        if (DevConfig.isFlavorHW() && !AccountManager.get().isGuided()) {
            AppConfig.attachSubsequently(getApplication(), false);
        }
        initTimestamp = System.currentTimeMillis();

        ImageView ivLaunch = findViewById(R.id.iv_launch);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Glide.with(this)
                    .load(SplashManager.get().getSplashImagePath())
                    .error(Glide.with(this).load(SplashManager.get().getDefaultImageRes()))
                    .into(ivLaunch);
        } else {
            ivLaunch.setVisibility(View.GONE);
            View layoutSplash = findViewById(R.id.layout_splash);
            layoutSplash.setBackground(SplashManager.get().getDefaultImage());
        }
        WeeeMonitor.getInstance().initAppEnd(WeeeMonitor.KEY_INIT);
    }

    @Override
    public LaunchViewModel createModel() {
        LaunchModel model = new LaunchModel();
        model.inject(LaunchApi.class);
        LaunchViewModel vm = new ViewModelProvider(this).get(LaunchViewModel.class);
        vm.injectModel(model);
        return vm;
    }

    @Override
    public void loadData() {
        url = null;
        type = Constants.SessionSource.SOURCE_NORMAL;
        ServiceMigration.migrate(() -> ensureUserAgent(this::prepareStartup));
    }

    private void ensureUserAgent(final Runnable onNext) {
        String userAgent = SessionService.get().getUserAgent();
        if (!EmptyUtils.isEmpty(userAgent)) {
            onNext.run();
        } else {
            obtainUserAgentFromWebView(onNext);
        }
    }

    private void obtainUserAgentFromWebView(final Runnable onNext) {
        ViewStub viewStub = findViewById(R.id.view_stub);
        viewStub.setOnInflateListener((stub, v) -> {
            String systemUserAgent = WebSettings.getDefaultUserAgent(SplashActivity.this);
            SessionService.get().updateSystemUserAgent(systemUserAgent);
            onNext.run();
        });
        viewStub.inflate();
    }

    private void prepareStartup() {
        //deep link process
        if (checkStatus(getIntent())) {
            AppStatusManager.get().setLaunchFlowAbnormal();
            if (retryLaunchByIntent) {
                Intent intent = getPackageManager().getLaunchIntentForPackage(getPackageName());
                if (intent != null) {
                    startActivity(intent);
                }
            }
            finish();
            return;
        }
        //launch flow
        viewModel.execLaunchFlow(type, url);
        //clear cookie
        WebHelper.removeAllCookie();
    }

    @Override
    public void attachModel() {
        viewModel.launchFlowData.observe(this, new Observer<LaunchHelpBean>() {
            @Override
            public void onChanged(LaunchHelpBean bean) {
                //先处理自定义scheme
                boolean processed = OAuthService.dispatchInterceptScheme(activity, url);
                if (!processed) {
                    if (bean.toGuideFlow && DevConfig.isFlavorHW()) {
                        onPageReadyOnHw(bean);
                    } else {
                        onPageReady(bean);
                    }
                }
            }
        });

        viewModel.languageData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                LanguageManager.get().changeLanguage(activity, s);
            }
        });

        SharedViewModel.get().oAuthResultData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                OAuthService.handleOAuthResult(activity, s, url);
                url = null;
                finish();
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (OAuthService.interceptSchemeSelf(url)) {
            OAuthService.onOAuthCanceled();
        }
        super.onBackPressed();
    }

    @Override
    protected void onPause() {
        super.onPause();
        isPaused = true;
    }

    private void onPageReady(LaunchHelpBean bean) {
        long time = System.currentTimeMillis() - initTimestamp - delayTime;
        if (time > 0) { //保证停留时间
            toFlow(bean);
        } else {
            getView().postDelayed(new Runnable() {
                @Override
                public void run() {
                    toFlow(bean);
                }
            }, Math.abs(time));
        }
    }

    private void onPageReadyOnHw(LaunchHelpBean bean) {
        PopupManager.get().showOnQueue(new UserClauseDialog(activity).addHelperCallback(new WrapperDialog.HelperCallback() {
            @Override
            public void help(Dialog dialog, ViewHelper helper) {
                helper.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dialog.dismiss();
                        if (v.getId() == R.id.tv_confirm) {
                            onPageReady(bean);
                        } else if (v.getId() == R.id.tv_cancel) {
                            finish();
                        }
                    }
                }, R.id.tv_confirm, R.id.tv_cancel);
            }
        }));
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void toFlow(LaunchHelpBean bean) {//检查更新
        AppAnalytics.logPageView(WeeeEvent.PageView.PAGE_START, activity);
        UpgradeHelper.checkUpgrade();
        KeyboardTrackHelper.trackKeyboardInfo();
        DeepLinkManager.get().filterUrlBeforeProcess();
        //页面没有paused && 仅限normal流程 && 流程不缺少store && 不涉及deeplink跳转
        if (bean == null || isPaused || !bean.toNormalFlow || StoreManager.get().lackOfStore() || DeepLinkManager.get().isCurrentValid()) {
            AppStatusManager.get().setLaunchFlowAbnormal();
        }
        if (bean != null) {
            if (bean.toGuideFlow) {
                if (DeepLinkManager.get().hasPrepareJump()) {
                    DeepLinkManager.get().processSelfJump();
                    finish();
                    return;
                }
                if (LanguageManager.get().isEnglishDefault()) {
                    // 如果系统语言或者本地设置的语言是英语，获取键盘的语言
                    String language = KeyboardTrackHelper.getKeyboardFirstLanguage();
                    language = discardLanguageInExperiment(language);
                    LanguageManager.get().changeLanguage(activity, language);
                    toGuidePage(bean.language);
                } else {
                    // 系统或者本地设置不是英语，获取系统语言并设置
                    String language = LanguageManager.get().getSystemLanguageOnNull();
                    language = discardLanguageInExperiment(language);
                    LanguageManager.get().changeLanguage(activity, language);
                    toGuidePage(null);
                }
            } else if (bean.toZipCodeFlow) {
                startActivity(ZipCodeInputActivity.getIntent(activity));
                finish();
            } else if (bean.toNormalFlow) {
                //修正近期两个版本导致的onboarding lang可能错误的问题
                String language = LanguageManager.get().getLanguage();
                boolean notSupport = AppFilter.LangConfig.isNotSupport(language);
                if (notSupport) {
                    LanguageManager.get().changeLanguage(activity, LanguageManager.Language.ENGLISH);
                }

                startActivity(MainActivity.getResetIntent(activity));
                overridePendingTransition(R.anim.activity_alpha_in, R.anim.activity_alpha_out);
                finish();
            } else if (bean.toLoginFlow) {
                startActivity(LoginActivity.getIntent(activity));
                finish();
            }
        }
    }

    // **********************************************************************************************
    // 唤醒判断

    /**
     * 检查唤醒方式是否通过deep link
     */
    private boolean checkStatus(Intent intent) {
        if (intent != null) {
            String sessionType = intent.getStringExtra("sessionType");
            //判断启动的intent是否带有指定参数
            if (Constants.SessionSource.SOURCE_NOTIFICATION.equals(sessionType)) {
                //属于通知唤醒
                type = Constants.SessionSource.SOURCE_NOTIFICATION;
                url = intent.getStringExtra("url");
            } else {
                type = Constants.SessionSource.SOURCE_NORMAL;
                Uri data = intent.getData();
                if (data != null) {
                    //判断intent中Uri中数据
                    String path = data.getPath();
                    if (path != null) {
                        String host = data.getHost();
                        //若此处url未带host，会在最终请求时根据当前环境拼接host
                        url = data.toString();
                        type = Constants.SessionSource.SOURCE_DEEP_LINK;
                        if (PushHelper.interceptKlaviyoIntent(intent)) {
                            //klaviyo跳转过来
                            type = Constants.SessionSource.SOURCE_NOTIFICATION;
                            url = PushHelper.processUrlParams(intent, url);
                        }

                        Logger.toJson("=====>", type + " " + url);
                        if (host != null) {
                            //filter AppsFlyer link，判断host是否为appflys指定的主机名
                            if (host.contains(DeepLinkManager.HOST_AF_LINK) || host.contains(DeepLinkManager.HOST_AF_CUSTOM_LINK)) {
                                //通过appflys link唤醒
                                boolean activityAlive = LifecycleProvider.get().isActivityAlive(MainActivity.class.getName());
                                if (activityAlive) {
                                    retryLaunchByIntent = host.contains(DeepLinkManager.HOST_AF_CUSTOM_LINK);
                                }
                                return activityAlive;
                            } else if (OAuthService.interceptSchemeSelf(url)) { //自定义scheme此处不处理，在后续处理
                                return false;
                            } else {
                                if (DeepLinkManager.get().isSelfSchemeUrl(url)) {
                                    if (DeepLinkManager.get().interceptSelfScheme(url)) {
                                        String target = DeepLinkManager.get().getSelfSchemeTargetUrl(url);
                                        if (EmptyUtils.isEmpty(target)) {
                                            return false;
                                        }
                                        url = target;
                                    } else {
                                        //不支持的自定义协议
                                        return LifecycleProvider.get().isActivityAlive(MainActivity.class.getName());
                                    }
                                }
                                //tiktok auth login
                                if (TiktokHelper.getInstance().isAuthUrl(url) && TiktokHelper.getInstance().onAuthResult(intent)) {
                                    return true;
                                }
                                //deep link process
                                DeepLinkManager.ProcessResult result = DeepLinkManager.get().onUrlReceived(type, url);
                                url = result.targetUrl;
                                if (result.isProcessed) {
                                    boolean isMagicSignIn = url.matches(Constants.UrlPattern.ACCOUNT_AUTO_LOGIN);
                                    if (isMagicSignIn) {
                                        retryLaunchByIntent = true;
                                    }
                                    SessionTokenHelper.execSessionCheck(type, url);
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private void toGuidePage(LanguageBean language) {
        startActivity(NewGuideActivity.getIntent(activity, language));
        finish();
    }

    /**
     * Remove me if THAI language experiment is over!
     * Only call it when is newly installed app.
     *
     * @param lang language code
     * @return if system or keyboard language is in experiment, such as THAI, replace it with default language ENGLISH
     */
    private String discardLanguageInExperiment(String lang) {
        return lang == null || LanguageManager.Language.THAI.equals(lang) ? LanguageManager.Language.ENGLISH : lang;
    }

}
