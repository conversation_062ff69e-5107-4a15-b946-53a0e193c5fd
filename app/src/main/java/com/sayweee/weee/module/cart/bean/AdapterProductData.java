package com.sayweee.weee.module.cart.bean;

import androidx.annotation.Nullable;

import com.sayweee.weee.module.base.adapter.AdapterDataRefresh;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.adapter.CartAdapter;
import com.sayweee.weee.module.cms.bean.CmsBackgroundStyle;
import com.sayweee.weee.module.cms.widget.CmsBackgroundItemDecoration;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/11/30.
 * Desc:    购物车底部商品数据
 */
public class AdapterProductData extends AdapterWrapperData<ProductBean>
        implements Serializable, AdapterDataRefresh,
        CmsBackgroundItemDecoration.AdapterItemData {

    public String source;

    //用于埋点
    public String category;

    //用于加购 场景 购物车底部推荐使用
    public String newSource;

    protected CmsBackgroundStyle backgroundStyle;

    public AdapterProductData(ProductBean productBean) {
        super(CartAdapter.TYPE_PANEL_PRODUCT, productBean);
    }

    public AdapterProductData(int type, ProductBean productBean) {
        super(type, productBean);
    }

    public AdapterProductData setProductSource(String source) {
        this.source = source;
        return this;
    }

    /**
     * 判断当前商品是否是活动商品
     *
     * @return
     */
    public boolean isActivityProduct() {
        return t != null && !EmptyUtils.isEmpty(t.product_tag_list);
    }

    /**
     * 判断当前活动是否是换购
     *
     * @return
     */
    public boolean isActivityTradeIn() {
        //app 换购暂时全为h5
        return false;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AdapterProductData)) return false;
        AdapterProductData that = (AdapterProductData) o;
        return t.id == that.t.id;
    }

    @Override
    public int hashCode() {
        if (t != null) {
            return Objects.hash(t.id);
        }
        return super.hashCode();
    }


    private int lastQuantity;
    private int currentQuantity;

    @Override
    public int getProductId() {
        return t != null ? t.id : 0;
    }

    @Override
    public String getProductKey() {
        return t != null ? t.product_key : null;
    }

    @Override
    public void setProductQuantity(int quantity) {
        this.lastQuantity = currentQuantity;
        this.currentQuantity = quantity;
    }


    @Override
    public int getProductQuantity() {
        return currentQuantity;
    }

    @Override
    public int getOrderMaxQuantity() {
        return t != null ? t.getOrderMaxQuantity() : 0;
    }

    @Override
    public int getOrderMinQuantity() {
        return t != null ? t.min_order_quantity : 0;
    }

    @Override
    public boolean isDirty() {
        return getProductId() > 0 && lastQuantity != currentQuantity;
    }

    public String getProductType() {
        return t != null ? t.getProductType() : null;
    }

    public String getReferValue() {
        return t != null ? t.getReferValue() : null;
    }

    public AdapterDataType setBackgroundStyle(CmsBackgroundStyle backgroundStyle) {
        this.backgroundStyle = backgroundStyle;
        return this;
    }

    @Nullable
    @Override
    public CmsBackgroundStyle getBackgroundStyle() {
        return backgroundStyle;
    }
}
