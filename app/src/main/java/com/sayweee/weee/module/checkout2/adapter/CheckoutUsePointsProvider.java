package com.sayweee.weee.module.checkout2.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.bean.PreCheckoutPointInfoBean;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.widget.shape.helper.ShapeHelper;

import java.util.List;

public class CheckoutUsePointsProvider
        extends SimpleHorizontalImpressionProvider<CheckoutUsePointsData, AdapterViewHolder> {

    private OnPurchaseChannelActionListener actionListener;

    @Override
    public int getItemType() {
        return CheckOutSectionAdapter.TYPE_USE_POINTS;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_checkout_section_use_points;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
    }

    @Override
    public void convert(AdapterViewHolder helper, CheckoutUsePointsData item) {
        // R.layout.item_checkout_section_use_points
        Context context = helper.itemView.getContext();
        View clPoints = helper.getView(R.id.cl_points);
        PreCheckoutPointInfoBean bean = item.pointInfo;

        TextView tvPointsPaymentTitle = helper.getView(R.id.tv_points_payment_title);
        TextView tvPointsPaymentValue = helper.getView(R.id.tv_points_payment_value);
        TextView tvPointsPaymentError = helper.getView(R.id.tv_points_payment_error);
        ImageView ivPointsPaymentUse = helper.getView(R.id.iv_points_payment_use);

        boolean isUnavailable;
        String unavailableDesc;
        if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_MEMBER_PLAN) {
            isUnavailable = true;
            unavailableDesc = null;
        } else if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_USE_EBT
                || item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_COMMISSION_PARTNER
        ) {
            isUnavailable = true;
            unavailableDesc = bean.unavailable_alert.desc;
        } else {
            isUnavailable = false;
            unavailableDesc = null;
        }
        tvPointsPaymentTitle.setText(context.getString(R.string.s_use_weee_points));
        ViewTools.setViewHtml(tvPointsPaymentValue, context.getString(R.string.s_order_value_of_points_new,
                String.valueOf(bean.points_current),
                OrderHelper.formatUSMoney(OrderHelper.calcPointsPrice(bean.points_current))
        ));
        tvPointsPaymentError.setText(unavailableDesc);
        if (isUnavailable) {
            ShapeHelper.setBackgroundDrawable(
                    clPoints,
                    ContextCompat.getColor(context, R.color.color_surface_200_bg),
                    CommonTools.dp2px(context, R.dimen.prop_size_radius_600, 20f),
                    ContextCompat.getColor(context, R.color.color_surface_200_bg),
                    CommonTools.dp2px(1f)
            );
            ViewTools.applyTextColor(tvPointsPaymentTitle, R.color.color_btn_disabled_fg_minor);
            ViewTools.applyTextColor(tvPointsPaymentValue, R.color.color_btn_disabled_fg_minor);
            ViewTools.setViewVisibilityIfChanged(tvPointsPaymentError, !EmptyUtils.isEmpty(unavailableDesc));
            ivPointsPaymentUse.setImageResource(R.drawable.drawable_checkmark_circle_empty_200_hairline);
        } else {
            ShapeHelper.setBackgroundDrawable(
                    clPoints,
                    ContextCompat.getColor(context, R.color.color_surface_100_bg),
                    CommonTools.dp2px(context, R.dimen.prop_size_radius_600, 20f),
                    ContextCompat.getColor(context, R.color.color_surface_100_hairline),
                    CommonTools.dp2px(1f)
            );
            ViewTools.applyTextColor(tvPointsPaymentTitle, R.color.color_surface_100_fg_default);
            ViewTools.applyTextColor(tvPointsPaymentValue, R.color.color_surface_100_fg_default);
            ViewTools.setViewVisibilityIfChanged(tvPointsPaymentError, false);
            ivPointsPaymentUse.setImageResource(item.checked
                    ? R.drawable.drawable_checkmark_circle_filled
                    : R.drawable.drawable_checkmark_circle_empty
            );
        }
        helper.setOnViewClickListener(R.id.cl_points, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (actionListener != null && item.pointsCanChecked) {
                    actionListener.onUsePointsChecked(item);
                }
            }
        });
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, CheckoutUsePointsData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        convert(helper, item);
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CheckoutUsePointsData item, int position) {
        return CollectionUtils.emptyList();
    }

    public void setOnPurchaseChannelActionListener(OnPurchaseChannelActionListener listener) {
        actionListener = listener;
    }

}
