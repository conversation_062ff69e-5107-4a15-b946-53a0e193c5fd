package com.sayweee.weee.module.product.provider;


import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.home.bean.MainBannerBean;
import com.sayweee.weee.module.mkpl.view.IndicatorDotView;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.data.GiftCardBannerItemData;
import com.sayweee.weee.module.product.data.PdpGiftCardBannerData;
import com.sayweee.weee.player.bean.MediaData;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.weee.widget.banner.ex.ExBannerLayoutProvider;
import com.sayweee.weee.widget.banner.ex.ExCarouselBanner;
import com.youth.banner.adapter.BannerAdapter;

import java.util.ArrayList;
import java.util.List;

//
// Created by Thomsen on 13/09/2024.
//
public class PdpGiftCardBannerProvider extends SimpleSectionProvider<PdpGiftCardBannerData, AdapterViewHolder> {

    public int lastBannerIndex;

    @Override
    public int getItemType() {
        return PdpItemType.PDP_GIFT_CARD_BANNER;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_pdp_gift_card_banner;
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpGiftCardBannerData item) {
        if (item == null || item.themeCoverImg == null) return;
        helper.getView(R.id.v_foreground).setBackground(null);

        ExCarouselBanner<MediaData<?>, BannerAdapter<MediaData<?>, RecyclerView.ViewHolder>> banner;
        banner = helper.getView(R.id.banner);
        List<GiftCardBannerItemData> banners = bindBannerData(banner, item);
        int count = banners.size();

        banner.setOnBannerListener((data, position) -> handleBannerClick(item, position));

        boolean isShowIndicator = count > 1;
        IndicatorDotView indicatorDot = helper.getView(R.id.indicator_dot);
        banner.addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
            @Override
            public void onPageSelected(int position, boolean isAuto) {
                if (isShowIndicator) {
                    indicatorDot.setSelectedIndex(position);
                }
                if (banners.size() > position) {
                    lastBannerIndex = position;
                }
            }
        });

        if (lastBannerIndex > 0 && count > lastBannerIndex) {
            banner.setCurrentItem(lastBannerIndex, false);
        }
    }

    private List<GiftCardBannerItemData> bindBannerData(ExCarouselBanner<MediaData<?>,
            BannerAdapter<MediaData<?>, RecyclerView.ViewHolder>> banner, PdpGiftCardBannerData item) {
        List<GiftCardBannerItemData> banners = getBanners(item);
        int count = banners.size();
        if (count == 1) {
            int width = 20;
            banner.setBannerGalleryEffect(width, width, 0, 1);
        } else {
            int width = 10;
            banner.setBannerGalleryEffect(width, width, width, 1);
        }

        BannerAdapter adapter = banner.getAdapter();
        if (adapter == null) {
            banner.setBannerData(banners, false, new ExBannerLayoutProvider.Factory(
                    R.layout.item_gift_card_images,
                    R.layout.item_gift_card_videos
            ));
        } else {
            adapter.setDatas(banners);
        }
        return banners;
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, PdpGiftCardBannerData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        Object payload = CollectionUtils.firstOrNull(payloads);
        if (payload instanceof PdpGiftCardBannerData) {
            bindBannerData(helper.getView(R.id.banner), item);
        }
    }

    private static @NonNull List<GiftCardBannerItemData> getBanners(PdpGiftCardBannerData item) {
        List<GiftCardBannerItemData> banners = new ArrayList<>();
        GiftCardBannerItemData bannerItem;
        MainBannerBean.CarouselBean bannerBean;
        if (item.themeCoverImg != null) {
            bannerBean = new MainBannerBean.CarouselBean();
            bannerBean.media_url = item.themeCoverImg;
            bannerBean.media_type = MainBannerBean.CarouselBean.TYPE_IMAGE;
            bannerItem = new GiftCardBannerItemData(bannerBean);
            banners.add(bannerItem);
        }
        return banners;
    }

    private void handleBannerClick(PdpGiftCardBannerData item, int position) {

    }

}
