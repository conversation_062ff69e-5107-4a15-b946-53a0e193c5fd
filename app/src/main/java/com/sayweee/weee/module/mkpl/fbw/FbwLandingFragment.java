package com.sayweee.weee.module.mkpl.fbw;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentFbwLandingBinding;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsBackgroundStyle;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.utils.CmsTools;
import com.sayweee.weee.module.cms.widget.timer.CmsComponentTimerHandler;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.mkpl.GlobalMiniCartViewModel;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedModelProvider;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.feed.IContentScrollTop;
import com.sayweee.weee.module.mkpl.provider.data.CmsTitleRichData;
import com.sayweee.weee.module.mkpl.provider.data.CmsTitleRichV2Data;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.seller.common.mpager.MPagerEntity;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.player.mute.PostCoverVideoHelper;
import com.sayweee.weee.player.mute.PostCoverVideoManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.helper.StatusHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.nested.ParentNestedRecyclerView;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.core.view.WrapperMvvmStatusFragment;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Desc:
 */
public class FbwLandingFragment
        extends WrapperMvvmStatusFragment<FbwLandingViewModel>
        implements OnRefreshListener, IContentFeedSharedModelProvider, IContentScrollTop {

    public static final String TAG = "com.sayweee.weee.module.mkpl.fbw.FbwLandingFragment";
    public static final String FROM_PAGE_BAKERY = "bakery";

    private static final String EXTRA_PAGE_KEY = "pageKey";

    private GlobalMiniCartViewModel cartViewModel;

    private FragmentFbwLandingBinding binding;
    private FbwLandingAdapter adapter;

    private long backgroundTime;
    private boolean needForceRefresh;

    private WrapperDialog shareDialog;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;

    public static FbwLandingFragment newInstance() {
        return newInstance(FROM_PAGE_BAKERY);
    }

    public static FbwLandingFragment newInstance(String pageKey) {
        FbwLandingFragment fragment = new FbwLandingFragment();
        Bundle bundle = new Bundle();
        bundle.putString(EXTRA_PAGE_KEY, pageKey);
        fragment.setArguments(bundle);
        return fragment;
    }

    @NonNull
    public String getExtraPageKey() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return arguments.getString(EXTRA_PAGE_KEY, FROM_PAGE_BAKERY);
        }
        return FROM_PAGE_BAKERY;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_fbw_landing;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);

        binding = FragmentFbwLandingBinding.bind(contentView);
        setupPage();
        initListener();

        showListVeilTemplated(true);
        setTitleBarVisible(false);
    }

    private void initListener() {
        ViewTools.setViewOnSafeClickListener(binding.ivBack, v -> backAction());
        ViewTools.setViewOnSafeClickListener(binding.ivShare, imageView -> shareAction());
        ViewTools.setViewOnSafeClickListener(binding.layoutTop, v -> scrollToTop());
        ViewTools.setViewOnSafeClickListener(binding.layoutRemindTips.tvRemindRevoke, v -> revokeRemind());
    }

    private void shareAction() {
        if (shareDialog == null) {
            viewModel.getFbwLandingShare(getExtraPageKey());
        } else {
            if (!shareDialog.isShowing()) {
                shareDialog.show();
                trackShare();
            }
        }
    }

    private void trackShare() {
        String targetType = "normal_button";
        EagleTrackManger.get().trackEagleClickAction(
                getExtraPageKey(),
                -1,
                targetType,
                EagleTrackEvent.ClickType.SHARE
        );
    }

    private void backAction() {
        Activity activity = getActivity();
        if (activity != null) {
            activity.finish();
        }
    }

    public void scrollToTop() {
        binding.recyclerView.scrollToPosition(0);
        adapter.scrollToTop();
        // title show gone when has title rich data
        setTitleBarVisible(!(adapter.getTitleRichV2Position() > -1));
    }

    private void setupPage() {
        Context context = getContext();
        if (context == null) return;
        binding.smartRefreshLayout.setOnRefreshListener(this);

        RecyclerView.LayoutManager layoutManager;
        layoutManager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        binding.recyclerView.setLayoutManager(layoutManager);

        adapter = new FbwLandingAdapter();
        adapter.setEnableLoadMore(false);
        adapter.setOnIndicatorClickListener(new OnIndicatorClickListener() {
            @Override
            public void onClick(int index, MPagerEntity entity) {
                int pos = adapter.getCategoryPosition();
                if (pos > -1) {
                    binding.recyclerView.smoothScrollToPosition(pos);
                }
            }
        });

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        adapter.setOnLightingDealTimerListener(new CmsComponentTimerHandler(viewLifecycleOwner) {
            @Override
            public void onEndSafely(String componentId) {
                super.onEndSafely(componentId);
                if (!EmptyUtils.isEmpty(componentId)) {
                    CmsDataSource dataSource = viewModel.getDataSource(componentId);
                    if (dataSource != null) {
                        viewModel.requestMultiDataSource(dataSource, 1500L);
                        return;
                    }
                }
                loadData();
            }
        });
        adapter.setOnRemindListener((bean, remind, position) -> {
            if (remind) {
                showRemindTips(bean.id);
            } else {
                setRemindEquals(bean.id);
            }
            viewModel.changeLightningDealsRemind(bean.id, remind);
        });

        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.addItemDecoration(new SimpleSectionItemDecoration());
        binding.recyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    adapter.onPageScrollStateChanged(recyclerView, newState);
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView rv, int dx, int dy) {
                ParentNestedRecyclerView recyclerView = (ParentNestedRecyclerView) rv;
                showScrollTop(recyclerView);

                if (!recyclerView.isStickyTop()) {
                    int index = setTitleRichV2Visible(rv);
                    if (index < 0) {
                        setTitleRichVisible(rv);
                    }
                }
            }
        });

        binding.recyclerView.setStickyListener(new ParentNestedRecyclerView.StickyListener() {
            @Override
            public void onSticky(boolean isAtTop) {
                adapter.setCategoryShadow(isAtTop);
            }
        });

        PostCoverVideoHelper.attachScrollPlay(binding.recyclerView);
    }

    private int setTitleRichV2Visible(@NonNull RecyclerView rv) {
        boolean visible = false;

        int index = adapter.getTitleRichV2Position();
        RecyclerView.ViewHolder viewHolder = rv.findViewHolderForLayoutPosition(index);
        if (viewHolder != null) {
            View titleView = viewHolder.itemView.findViewById(R.id.tv_title);
            if (titleView != null) {
                Rect rect = new Rect();
                titleView.getGlobalVisibleRect(rect);
                float itemY = rect.bottom;
                int offset = binding.layoutTitle.getHeight();
                if (index > -1 && itemY < offset) {
                    visible = true;
                }
            }
        } else {
            visible = scrollBottomTitleVisible(rv, index);
        }

        setTitleBarVisible(visible);
        return index;
    }

    private static boolean scrollBottomTitleVisible(@NonNull RecyclerView rv, int index) {
        boolean visible = false;
        // scroll to bottom and first item view recycle
        RecyclerView.LayoutManager layoutManager = rv.getLayoutManager();
        if ((layoutManager instanceof StaggeredGridLayoutManager)) {
            StaggeredGridLayoutManager gridLayoutManager = (StaggeredGridLayoutManager) layoutManager;

            int[] firstPos = new int[gridLayoutManager.getSpanCount()];
            gridLayoutManager.findFirstCompletelyVisibleItemPositions(firstPos);
            int firstVisibleItemPosition = CollectionUtils.min(firstPos);

            if (firstVisibleItemPosition >= index) {
                View view = layoutManager.findViewByPosition(index);
                visible = (view == null || view.getY() < 0);
            }
        }
        return visible;
    }

    private void setTitleRichVisible(@NonNull RecyclerView rv) {
        boolean visible = false;

        int index = adapter.getTitleRichPosition();
        RecyclerView.ViewHolder viewHolder = rv.findViewHolderForLayoutPosition(index);
        if (viewHolder != null) {
            View titleView = viewHolder.itemView.findViewById(R.id.tv_title);
            if (titleView != null) {
                Rect rect = new Rect();
                titleView.getGlobalVisibleRect(rect);
                float itemY = rect.bottom;
                int offset = binding.layoutTitle.getHeight();
                if (index > -1 && itemY < offset) {
                    visible = true;
                }
            }
        } else {
            visible = scrollBottomTitleVisible(rv, index);
        }
        setTitleBarVisible(visible);
    }

    private void initTitleBar(List<AdapterDataType> list) {
        Object data = CollectionUtils.firstOrNull(list, it -> it.getType() == CmsItemType.TITLE_RICH);
        if (data instanceof CmsTitleRichData) {
            CmsTitleRichData richData = (CmsTitleRichData) data;
            if (richData.t != null) {
                binding.tvTitle.setText(richData.t.title);
            }
        }
        Object dataV2 = CollectionUtils.firstOrNull(list, it -> it.getType() == CmsItemType.TITLE_RICH_V2);
        if (dataV2 instanceof CmsTitleRichV2Data) {
            binding.tvTitle.setText(((CmsTitleRichV2Data) dataV2).getTitle());
        }

    }

    private void setTitleBarHeight() {
        int stickyHeight = CommonTools.getStatusBarHeight(getContext());
        stickyHeight += getResources().getDimensionPixelOffset(R.dimen.default_tab_height);
        binding.recyclerView.setStickyHeight(stickyHeight);
    }

    private void setTitleBarVisible(boolean visible) {
        binding.vStatus.setBackgroundResource(visible ? R.color.root_color_white_static : 0);
        binding.layoutTitle.setBackgroundResource(visible ? R.color.root_color_white_static : 0);
        ViewTools.setViewVisibilityIfChanged(binding.tvTitle, visible);
    }

    private int[] visiblePositions = null;
    private int visibleLastPosition = 0;

    @Override
    public void showScrollTop(@Nullable RecyclerView recyclerView) {
        if (recyclerView == null) return;
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager lm = (StaggeredGridLayoutManager) layoutManager;
            if (visiblePositions == null || visiblePositions.length != lm.getSpanCount()) {
                visiblePositions = new int[lm.getSpanCount()];
            }
            // reset position when scroll to content waterfall
            visiblePositions = lm.findFirstCompletelyVisibleItemPositions(visiblePositions);
            int firstVisiblePosition = CollectionUtils.min(visiblePositions, -1);

            final int MAX_POS = 8;
            if (firstVisiblePosition > MAX_POS) {
                visibleLastPosition = firstVisiblePosition;
                ViewTools.setViewVisibilityIfChanged(binding.layoutTop, true);
            } else if (visibleLastPosition != 0) {
                // false 对象View不能向下滚动
                boolean parentRvBottom = !binding.recyclerView.canScrollVertically(1);
                if (parentRvBottom) {
                    firstVisiblePosition = Math.max(firstVisiblePosition, visibleLastPosition);
                }
                ViewTools.setViewVisibilityIfChanged(binding.layoutTop, firstVisiblePosition > MAX_POS);
            }
        }
    }

    @Override
    public void loadData() {
        String pageKey = getExtraPageKey();
        viewModel.getCmsPage(pageKey, null);
    }

    @Override
    public <VM> VM createModel() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            cartViewModel = ViewModelProviders.of(activity).get(GlobalMiniCartViewModel.class);
            cartViewModel.injectLifecycle(getLifecycle());
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        viewModel.setPageKey(getExtraPageKey());
        viewModel.shareData.observe(this, shareBean -> {
            if (shareDialog == null) {
                shareDialog = new ShareDialog(activity)
                        .setShareData(shareBean);
                shareDialog.show();
                trackShare();
            } else {
                if (!shareDialog.isShowing()) {
                    shareDialog.show();
                    trackShare();
                }
            }
        });
        viewModel.pageParamData.observe(this, pageParam -> {
            if (pageParam != null) {
                CmsBackgroundStyle backgroundStyle = pageParam.getBackgroundStyle();
                CmsTools.applyBackgroundStyle(binding.recyclerView, backgroundStyle, Color.WHITE);
            }
        });
        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                binding.smartRefreshLayout.finishRefresh();

                binding.recyclerView.removeAllViews();
                binding.recyclerView.removeAllViewsInLayout();
                binding.recyclerView.getRecycledViewPool().clear();
                binding.recyclerView.swapAdapter(adapter, true);
                adapter.setNewData(list);
                adapter.loadMoreComplete();
                adapter.notifyPageDataSetChanged(binding.recyclerView);
                ViewTools.setViewVisible(EmptyUtils.isEmpty(list), findViewById(R.id.layout_empty));
                showListVeilTemplated(false);
                initTitleBar(list);

                binding.ivShare.setVisibility(View.VISIBLE);
                binding.vCart.setVisibility(View.VISIBLE);
                setTitleBarHeight();
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                showListVeilTemplated(false);
                binding.smartRefreshLayout.setVisibility(View.GONE);
                StatusHelper.showStatus(getStatusManager(), failureBean, true,
                        new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                getStatusManager().hideStatus();
                                showListVeilTemplated(true);
                                binding.smartRefreshLayout.setVisibility(View.VISIBLE);
                                loadData();
                            }
                        });
            }
        });

        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                adapter.toggleCollect(map);
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                showListVeilTemplated(true);
                loadData();//订单状态改变
            }
        });

        viewModel.remindData.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> map) {
                refreshRemindSet(map);
            }
        });

        viewModel.componentDataUpdateLiveData.observe(this, adapter::notifyMultiDataSourceUpdate);

        attachCartViewModel();
    }

    private void attachCartViewModel() {
        if (cartViewModel == null) return;
        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;

        cartViewModel.getGlobalMiniCartPageStateLiveData().observe(viewLifecycleOwner, pageState -> {
            if (!isSupportVisible()) return;
            if (pageState.isExpended()) {
                Map<String, Object> ctx = null;
                EagleTrackManger.get().trackEagleClickAction(
                        /* modNm= */"mkpl_mini_cart",
                        /* modPos= */-1,
                        /* secNm= */null,
                        /* secPos= */-1,
                        /* targetNm= */pageState.getTargetId(),
                        /* targetPos= */pageState.getTargetPosition(),
                        /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                        /* clickType= */EagleTrackEvent.ClickType.VIEW,
                        /* ctx= */ctx
                );
            } else {
                AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_WATERFALL, this);
                adapter.onPageResume(binding.recyclerView);
                ProductSyncHelper.onPageResume(adapter);
            }
        });
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
        AppAnalytics.logPageView(getExtraPageKey() + "_landing", this);
        PopupCenterManager.get().onPageResumed(WeeeEvent.POPUP_PAGE_BAKERY_LANDING);

        adapter.onPageResume(binding.recyclerView);
        ProductSyncHelper.onPageResume(adapter);
        autoRefresh();
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause(binding.recyclerView);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PostCoverVideoManager.clearVideo(String.valueOf(hashCode()));
    }

    private void autoRefresh() {
        long now = System.currentTimeMillis();
        boolean refresh = false;
        long fiveMin = (5 * 60 * 1000);
        if (backgroundTime > 0 && (now - backgroundTime) > fiveMin) {
            refresh = true;
        }
        backgroundTime = 0;
        if (refresh || needForceRefresh) {
            needForceRefresh = false;
            scrollToTop();
            binding.smartRefreshLayout.autoRefresh();
        }
    }

    public void setBackgroundTime() {
        backgroundTime = System.currentTimeMillis();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        adapter.setEnableLoadMore(false);
        loadData();
    }

    protected void loadMore() {

    }

    private void showListVeilTemplated(boolean visible) {
        binding.layoutTitle.setBackgroundResource(visible ? R.color.root_color_white_static : 0);
        ViewTools.setViewVisibilityIfChanged(binding.vCart, !visible);
        ViewTools.setViewVisibilityIfChanged(binding.ivShare, !visible);
        VeilTools.show(findViewById(R.id.vl_global_list), visible);
    }

    @Override
    public IContentFeedSharedViewModel getContentFeedSharedViewModel() {
        return viewModel;
    }

    private void showRemindTips(int id) {
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().postDelayed(hideRemindTipsRunnable, 3000);
        binding.layoutRemindTips.getRoot().setTag(id);
        binding.layoutRemindTips.getRoot().setVisibility(View.VISIBLE);
    }

    private void setRemindEquals(int id) {
        if (binding.layoutRemindTips.getRoot().getVisibility() == View.VISIBLE) {
            Object tag = binding.layoutRemindTips.getRoot().getTag();
            if (tag instanceof Integer) {
                if ((Integer) tag == id) {
                    hideRemindTips();
                }
            }
        }
    }

    private void revokeRemind() {
        Object tag = binding.layoutRemindTips.getRoot().getTag();
        if (tag instanceof Integer) {
            adapter.revokeRemind((Integer) tag);
            hideRemindTips();
            viewModel.changeLightningDealsRemind((Integer) tag, false);
        }
    }

    private void hideRemindTips() {
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().setVisibility(View.GONE);
    }

    private final Runnable hideRemindTipsRunnable = this::hideRemindTips;

    private void refreshRemindSet(Map<String, Object> map) {
        for (AdapterDataType item : adapter.getData()) {
            if (item instanceof CmsLightingDealsData) {
                for (LightningDealsProductBean productBean : ((CmsLightingDealsData) item).t.products) {
                    if (map.get("product_id") != null && Objects.equals(map.get("product_id"), productBean.id)) {
                        Object remind = map.get("remind");
                        if (remind instanceof Boolean) {
                            productBean.remind_set = (boolean) remind;
                        }
                    }
                }
            }
        }
    }
}
