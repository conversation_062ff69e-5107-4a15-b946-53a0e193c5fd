package com.sayweee.weee.module.web.handler;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.LoginRouteActivity;
import com.sayweee.weee.module.account.StudentVerifyActivity;
import com.sayweee.weee.module.cate.product.BrandActivity;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.cate.tools.CategoryUrlTool;
import com.sayweee.weee.module.checkout.DeliveryAddressPickerActivity;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.checkout2.DealPayActivity;
import com.sayweee.weee.module.checkout2.pm.EbtCardAddActivity;
import com.sayweee.weee.module.collection.CollectionActivity;
import com.sayweee.weee.module.collection.CollectionAutoActivity;
import com.sayweee.weee.module.message.ActivityCenterActivity;
import com.sayweee.weee.module.message.MessageCenterActivity;
import com.sayweee.weee.module.message.MessagePortalActivity;
import com.sayweee.weee.module.message.NotificationCenterActivity;
import com.sayweee.weee.module.mkpl.GlobalActivity;
import com.sayweee.weee.module.mkpl.fbw.FbwLandingActivity;
import com.sayweee.weee.module.mkpl.home.GlobalPlusActivity;
import com.sayweee.weee.module.order.bean.OrderCategory;
import com.sayweee.weee.module.order.list.OrderListActivity;
import com.sayweee.weee.module.post.PostListActivity;
import com.sayweee.weee.module.post.PostVideoDetailActivity;
import com.sayweee.weee.module.post.detail.ReviewDetailActivity;
import com.sayweee.weee.module.post.edit.PostEditorActivity;
import com.sayweee.weee.module.post.edit.service.bean.HashTagItemBean;
import com.sayweee.weee.module.post.explore.PostExploreActivity;
import com.sayweee.weee.module.post.explore.PostExploreParams;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.module.post.profile.ProfileFollowActivity;
import com.sayweee.weee.module.post.review.ReviewEditActivity;
import com.sayweee.weee.module.post.review.ToReviewHostActivity;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.seller.SellerPageParams;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.thematic.ThematicActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.experiment.ExperimentRouteActivity;
import com.sayweee.weee.service.helper.PictureSelectorHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.MapUtils;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/1/21.
 * Desc:
 */
public class HandlerHelper {

    public static void handleLogin(Context context, String url) {
        if (!TextUtils.isEmpty(url) && url.contains("?tab")) {
            startActivity(context, AccountIntentCreator.getIntent(context));
        } else {
            startActivity(context, AccountIntentCreator.getResigterIntent(context));
        }
    }

    public static void handleHome(String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        if (!EmptyUtils.isEmpty(params)) {
            Map<String, Object> map = new ArrayMap<>();
            map.put("home", params);
            SharedViewModel.get().toHome(map);
        } else {
            SharedViewModel.get().toHome();
        }
    }

    public static void handleCategory(String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        String category = params.get("category");
        Logger.json(url, category);
        handleCategoryParams(category, params);
    }

    public static void handleCategoryParams(String cate, Map<String, String> params) {
        if (cate != null) {
            if (LifecycleProvider.get().getTopActivity() != null && CategoryUrlTool.handlePushNewCategoryPage(params, LifecycleProvider.get().getTopActivity())) {
                return;
            }
            SharedViewModel.get().toCateTab(cate, params);
        } else {
            SharedViewModel.get().toCate();
        }
    }

    public static void handleCart(String url) {
        SharedViewModel.get().toCart(url);
    }


    public static void handlePost() {
        SharedViewModel.get().toPost();
    }

    public static void handleMe() {
        SharedViewModel.get().toMe();
    }

    public static boolean handleProductDetail(Context context, String url) {
        //跳转商品详情
        //https://tb1.sayweee.net/en/product/view/39519?ws=app_push
        Map<String, String> params = CommonTools.parseQueryParams(url);
        String fromPage = EmptyUtils.isEmpty(params) ? null : params.get("from_page");
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        String[] strings = url.split("/");
        if (strings.length > 0) {
            String target = strings[strings.length - 1];
            try {
                int no = DecimalTools.parseInt(target.trim());
                startActivity(context, ProductIntentCreator.getIntentByFromPage(context, no, fromPage));
                return true;
            } catch (Exception ignored) { /**/ }
        }
        return false;
    }

    /**
     * 处理晒单详情链接
     *
     * @param context
     * @param url
     * @return
     */
    public static boolean handleReviewDetail(Context context, String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        String[] strings = url.split("/");
        if (strings.length > 0) {
            String target = strings[strings.length - 1];
            try {
                int no = DecimalTools.parseInt(target.trim());
                String msgId = params.get(Constants.MessageParams.MSG_ID);
                String msgType = params.get(Constants.MessageParams.MSG_TYPE);
                if (!EmptyUtils.isEmpty(msgId)) {
                    startActivity(context, ReviewDetailActivity.getIntent(context, ReviewDetailActivity.FROM_MESSAGE_CENTER, no, msgId, msgType, null, null));
                } else {
                    startActivity(context, ReviewDetailActivity.getIntent(context, no));
                }
                return true;
            } catch (Exception ignored) { /**/ }
        }
        return false;
    }

    public static boolean handleReviewList(Context context, String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        String usp_id = params.get(Constants.UrlMapParams.USP_ID);
        String review_id = params.get(Constants.UrlMapParams.REVIEW_ID);
        String[] strings = url.split("/");
        if (strings.length > 0) {
            boolean isNewUrl = url.matches(Constants.UrlPattern.REVIEW_LIST_2);
            String target = null;
            if (isNewUrl) {
                if (strings.length > 1) {
                    target = strings[strings.length - 2];
                }
            } else {
                target = strings[strings.length - 1];
            }
            if (target != null) {
                int id = DecimalTools.parseInt(target);
                if (id > 0) {
                    if (!EmptyUtils.isEmpty(usp_id) || !EmptyUtils.isEmpty(review_id)) {
                        startActivity(context, PostListActivity.getIntent(context, target, usp_id, review_id, null));
                    } else {
                        startActivity(context, PostListActivity.getIntent(context, target, null, false, null));
                    }
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean handlePostVideoDetail(Context context, String url) {
        Map<String, String> map = CommonTools.parseQueryParams(url);
        String source = null;
        String eventId = null;
        String eventSort = null;
        String tagId = null;
        String pageSource = null;
        String ws = null;
        String isLink = null;
        String commentId = null;
        String productId = null;
        String category = null;
        if (!EmptyUtils.isEmpty(map)) {
            source = map.get(PostVideoDetailActivity.KEY_SOURCE);
            //event
            eventId = map.get(PostVideoDetailActivity.KEY_EVENT_ID);
            eventSort = map.get(PostVideoDetailActivity.KEY_EVENT_SORT);
            //hash tag
            tagId = map.get(PostVideoDetailActivity.KEY_TAG_ID);
            pageSource = map.get(PostVideoDetailActivity.KEY_PAGE_SOURCE);
            commentId = map.get(PostVideoDetailActivity.KEY_COMMENT_ID);
            productId = map.get(PostVideoDetailActivity.KEY_PRODUCT_ID);
            category = map.get(PostVideoDetailActivity.KEY_CATEGORY);
            //push,share,system,msg center
            ws = map.get("ws");
            isLink = map.get("is_link");
        }

        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }

        String[] strings = url.split("/");
        if (strings.length > 0) {
            String target = strings[strings.length - 1];
            try {
                int postId = DecimalTools.parseInt(target.trim());
                String msgId = map.get(Constants.MessageParams.MSG_ID);
                String msgType = map.get(Constants.MessageParams.MSG_TYPE);

                if (EmptyUtils.isEmpty(map)) {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_H5_REVIEW_DETAIL, postId));
                } else if ("1".equals(isLink)) {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_IS_LINK, postId, commentId));
                } else if ("message_center".equals(ws) && !EmptyUtils.isEmpty(msgId)) {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_MESSAGE_CENTER, postId, msgId, msgType));
                } else if ("system".equals(ws)) {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_SYSTEM, postId));
                } else if ("app_push".equals(ws) || "share".equals(ws)) {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_SHARE_OR_PUSH, postId));
                } else {
                    startActivity(context, PostVideoDetailActivity.getIntent(context, PostVideoDetailActivity.FROM_H5_EVENT_OR_HASHTAG,
                            postId, source, eventId, eventSort, tagId, pageSource, null, null, null, null,
                            null, EmptyUtils.isEmpty(productId) ? -1 : DecimalTools.parseInt(productId), category, null,
                            null, 0, null));

                }
                return true;
            } catch (Exception ignored) { /**/ }
        }
        return false;
    }

    private static void startActivityNormal(@NonNull Context context, @NonNull Intent intent) {
        try {
            context.startActivity(intent);
        } catch (Exception ignored) {

        }
    }

    private static void startActivity(Context context, Intent intent) {
        if (context != null) {
            try {
                context.startActivity(intent);
                if (context instanceof Activity) {
                    ((Activity) context).overridePendingTransition(0, 0);
                }
            } catch (Exception e) {/**/}
        }
    }

    //url跳转到编辑发视频界面
    public static void handleEditPost(Activity activity, Map<String, String> params) {
        if (!EmptyUtils.isEmpty(params) && activity != null) {
            String temp = params.get("tag_name");
            String tag_name = Uri.decode(temp);
            String tag_id = params.get("tag_id");
            String source = params.get("video_source");
            PictureSelectorHelper.showVideoSelector(activity, new OnResultCallbackListener<LocalMedia>() {
                @Override
                public void onResult(List<LocalMedia> list) {
                    if (!EmptyUtils.isEmpty(list)) {
                        LocalMedia media = list.get(0);
                        HashTagItemBean bean = new HashTagItemBean();
                        bean.label = tag_name;
                        bean.tag_id = DecimalTools.parseInt(tag_id);
                        startActivity(activity, PostEditorActivity.getIntentOnAdd(activity, media, Collections.singletonList(bean), source));
                    }
                }

                @Override
                public void onCancel() {

                }

            });
        }
    }

    //email跳转到编辑发视频界面
    public static void handleEmailEditPost(Activity activity, Map<String, String> params) {
        if (!EmptyUtils.isEmpty(params) && activity != null) {
            if (AccountManager.get().isLogin()) {
                String productIds = params.get("product_ids");
                String source = params.get("source");
                startActivity(activity, PostEditorActivity.getIntentOnEmailAdd(activity, productIds, source));
            } else {
                startActivity(activity, AccountIntentCreator.getIntent(activity));
            }
        }
    }

    public static boolean handleThemeLanding(Context context, String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        int id = -1;
        String source = null;
        if (!EmptyUtils.isEmpty(params)) {
            if (params.containsKey("theme_id")) {
                id = DecimalTools.parseInt(params.get("theme_id"));
            }
            if (params.containsKey("ws")) {
                source = params.get("ws");
            }
        }
        if (context == null) {
            context = LifecycleProvider.get().getTopActivity();
        }
        if (context != null) {
            context.startActivity(ThematicActivity.getIntent(context, null, id, source));
            return true;
        }
        return false;
    }

    public static boolean handleMessagePortal(Context context, String url) {
        if (context != null) {
            context.startActivity(MessagePortalActivity.getIntent(context));
            if (context instanceof Activity) {
                ((Activity) context).overridePendingTransition(0, 0);
            }
            return true;
        }
        return false;
    }

    public static boolean handleCommunityPortal(Context context, String url) {
        if (context != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }
            String[] strings = url.split("/");
            if (strings.length > 0) {
                try {
                    String type = params.get(Constants.MessageParams.type);
                    if (!EmptyUtils.isEmpty(type)) {
                        startActivity(context, MessageCenterActivity.getIntent(context, type));
                    } else {
                        startActivity(context, MessageCenterActivity.getIntent(context));
                    }
                } catch (Exception ignored) { /**/ }

            }

            return true;
        }
        return false;
    }

    public static boolean handleMessageCenter(Context context, String url) {
        if (context != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }
            String[] strings = url.split("/");
            if (strings.length > 0) {
                try {
                    String category = params.get(Constants.MessageParams.CATEGORY);
                    String categoryId = params.get(Constants.MessageParams.CATEGORY_ID);
                    String subCategory = params.get(Constants.MessageParams.SUB_CATEGORY);
                    if (!EmptyUtils.isEmpty(category)) {
                        if ("community".equalsIgnoreCase(category)) {
                            startActivity(context, MessageCenterActivity.getIntent(context, categoryId, subCategory));
                        } else {
                            startActivity(context, NotificationCenterActivity.getIntent(context, categoryId, category));
                        }
                    }
//                    }
                } catch (Exception ignored) { /**/ }

            }

            return true;
        }
        return false;
    }

    public static boolean handleProfilePage(Context context, String url) {
        if (url.contains("url")) {
            url = url.substring(url.indexOf("url"), url.length());
        }
        Map<String, String> params = CommonTools.parseQueryParams(url);
        String mSource = params.get("source");
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        String[] strings = url.split("/");
        if (strings.length > 0) {
            String target = null;
            boolean isNewUrl = url.matches(Constants.UrlPattern.PROFILE_2);
            if (isNewUrl) {
                if (strings.length > 1) {
                    target = strings[strings.length - 2];
                }
            } else {
                target = strings[strings.length - 1];
            }
            try {
                String msgId = params.get(Constants.MessageParams.MSG_ID);
                String msgType = params.get(Constants.MessageParams.MSG_TYPE);
                String tabType = params.get(Constants.UrlMapParams.TAB_TYPE);
                String tabStatus = params.get(Constants.UrlMapParams.TAB_STATUS);
                String source = params.get("ws");
                if (!EmptyUtils.isEmpty(msgId)) {
                    startActivity(context, ProfileActivity.getIntent(context, ProfileActivity.FROM_MESSAGE_CENTER, target, "notification", msgId, msgType));
                } else if (!EmptyUtils.isEmpty(tabType)) {
                    startActivity(context, ProfileActivity.getIntent(context, tabType, EmptyUtils.isEmpty(tabStatus) ? "P" : tabStatus, source));
                } else {
                    startActivity(context, ProfileActivity.getIntent(context, ProfileActivity.FROM_NORMAL, target, source, null, null));
                }
                return true;
            } catch (Exception ignored) { /**/ }
        }
        return false;
    }

    public static boolean handleMeToFollowers(Context context, String url) {
        if (url.contains("url")) {
            url = url.substring(url.indexOf("url"), url.length());
        }
        try {
            url = URLDecoder.decode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // e.printStackTrace();
        }
        Map<String, String> params = CommonTools.parseQueryParams(url);
        try {
            String type = params.get(Constants.MeToPage.TYPE);
            String nickName = params.get(Constants.MeToPage.NICK_NAME);

            if (!EmptyUtils.isEmpty(type)) {
                startActivity(context, ProfileFollowActivity.getIntent(context, AccountManager.get().getUID(), type.contains("follower"), nickName));
            }
            return true;
        } catch (Exception ignored) { /**/ }
        return false;
    }

    public static boolean handleVendorPage(Context context, String url) {
        //https://tb1.sayweee.net/zh/vendor/3365
        String originalUrl = url;
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        String[] strings = url.split("/");

        if (strings.length > 0) {
            String target = strings[strings.length - 1];
            try {
                Map<String, String> params = CommonTools.parseQueryParams(originalUrl);
                SellerPageParams pageParams = new SellerPageParams();
                pageParams.openCart = MapUtils.getInt(params, "open_cart");
                String msg = MapUtils.getString(params, "toast_msg");
                if (!TextUtils.isEmpty(msg)) {
                    pageParams.toastMsg = URLDecoder.decode(msg, "utf-8");
                }
                int no = DecimalTools.parseInt(target.trim());
                startActivity(context, SellerActivity.getIntentByUrl(context,
                        String.valueOf(no), pageParams, originalUrl));
                /*if (true) {
                    startActivity(context, SellerActivity.getIntent(context, String.valueOf(no)));
                }else {
                    startActivity(context, VendorActivity.getIntent(context, String.valueOf(no)));
                }*/
                return true;
            } catch (Exception ignored) { /**/ }
        }
        return false;
    }

    public static boolean handleActivityPortal(Context context, String url) {
        if (context != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            String source = null;
            String[] strings = url.split("/");
            if (strings.length > 0) {
                source = params.get("ws");
            }
            startActivity(context, ActivityCenterActivity.getIntent(context, source));
            return true;
        }
        return false;
    }

    public static boolean handleSearch(Context context, String url) {
        if (context != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            String keyword = params.get("keyword");
            if (!EmptyUtils.isEmpty(keyword)) {
                try {
                    keyword = URLDecoder.decode(keyword, "utf-8");
                } catch (UnsupportedEncodingException ignored) {
                }
            }
            String searchResultsType = params.get("search_ui_type");
            if (!EmptyUtils.isEmpty(searchResultsType)) {
                startActivity(context, SearchPanelActivity.getIntent(context, keyword, searchResultsType, null));
            } else {
                startActivity(context, SearchPanelActivity.getIntent(context, keyword, null));
            }
            return true;
        }
        return false;
    }

    public static boolean handleAddressBook(Context context, String url) {
        if (context != null) {
            context.startActivity(DeliveryAddressPickerActivity.getIntent(context, "me_setting"));
            return true;
        }
        return false;
    }

    public static boolean handleStudentVerify(Context context, String url) {
        if (context != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            String email = params.get("email");
            if (!EmptyUtils.isEmpty(email)) {
                context.startActivity(StudentVerifyActivity.getIntent(context, email));
                return true;
            }
        }
        return false;
    }

    public static boolean handleBrand(Context context, String url) {
        if (context != null) {
            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }
            String[] strings = url.split("/");
            if (strings.length > 0) {
                String target = strings[strings.length - 1];
                if (target != null) {
                    startActivity(context, BrandActivity.getIntent(context, null, null, target));
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean handleSocialPostReview(@Nullable Context context, String url, boolean animate) {
        if (context == null) return false;
        if (EmptyUtils.isEmpty(url)) return false;

        Map<String, String> params = CommonTools.parseQueryParams(url);
        String tab = params.get("tab");
        String source = params.get("review_source");
        Intent a = ToReviewHostActivity.getIntent(context, tab, source);
        if (animate) {
            startActivityNormal(context, a);
        } else {
            startActivity(context, a);
        }
        return true;
    }

    public static boolean handleSocialEditReview(@Nullable Context context, String url, boolean animate) {
        if (context == null) return false;
        if (EmptyUtils.isEmpty(url)) return false;

        Map<String, String> params = CommonTools.parseQueryParams(url);
        long orderId = DecimalTools.parseLong(params.get("order_id"));
        int productId = DecimalTools.parseInt(params.get("product_id"));
        if (productId == 0) return false;
        String source = params.get("review_source");

        String backUrl = params.get("back_url");

        Intent a = ReviewEditActivity.getIntent(context, orderId, productId,
                source, false, backUrl);
        if (animate) {
            startActivityNormal(context, a);
        } else {
            startActivity(context, a);
        }
        return true;
    }

    public static boolean handleSocialExploreVideo(@Nullable Context context, String url) {
        if (context == null) return false;
        if (EmptyUtils.isEmpty(url)) return false;
        String parameter = url.substring(url.lastIndexOf("/") + 1);
        int sellerId = DecimalTools.parseInt(parameter);
        if (sellerId == 0) return false;
        PostExploreParams params = new PostExploreParams();
        params.type = PostExploreParams.EXPLORE_SELLER;
        params.sellerId = sellerId;
        context.startActivity(PostExploreActivity.getIntent(context, params));

        return true;
    }

    public static boolean handleGlobal(@Nullable Activity activity, String url, boolean animate) {
        if (activity == null) return false;
        if (EmptyUtils.isEmpty(url)) return false;

        Map<String, String> params = CommonTools.parseQueryParams(url);
        String key = params.get("key");
        String mode = params.get("mode");

        Intent a;
        if ("sub_page".equals(mode)) {
            a = GlobalActivity.getIntent(activity, /* tab= */key);
            startActivity(activity, a);
        } else {
            a = GlobalPlusActivity.getIntent(activity);
            // support global plus and in home page
            if (!SharedViewModel.get().toGlobalTab(url)) {
                startActivity(activity, a);
            }
        }
//        if (animate) {
//            startActivityNormal(activity, a);
//        } else {
//            startActivity(activity, a);
//        }

        return true;

    }

    /**
     * bakery landing page
     *
     * @param activity
     * @param url
     * @return
     */
    public static boolean handleFbwLanding(@Nullable Activity activity, String url) {
        if (activity == null) return false;
        if (EmptyUtils.isEmpty(url)) return false;

        Pattern pattern = Pattern.compile(Constants.UrlPattern.MKPL_FBW_LANDING);
        Matcher matcher = pattern.matcher(url);
        if (!matcher.matches()) return false;
        if (matcher.groupCount() < 2) return false;

        String pageKey = matcher.group(2);
        if (EmptyUtils.isEmpty(pageKey)
                || "coupon".equals(pageKey)
                || "group-order".equals(pageKey)
        ) {
            return false;
        }

        Intent a = FbwLandingActivity.getIntent(activity, pageKey);
        startActivity(activity, a);
        return true;
    }

    public static boolean handleCheckout(Context context, String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        if (context != null && !params.isEmpty()) {
            String cartDomain = params.get("cart_domain");
            String cartId = params.get("cart_id");
            if (AccountManager.get().isLogin()) {
                context.startActivity(CheckoutSectionActivity.getIntent(context, cartDomain, cartId));
            } else {
                Intent intent = LoginRouteActivity.getIntent(context, url, true);
                context.startActivity(intent);
            }
            return true;
        }
        return false;
    }

    public static boolean handleThematic(Context context, String url) {
        Map<String, String> params = CommonTools.parseQueryParams(url);
        int themeId = 0;
        if (context != null && !params.isEmpty()) {
            String themeIdStr = params.get("theme_id");
            themeId = DecimalTools.parseInt(themeIdStr);
        }
        if (context != null) {
            context.startActivity(ThematicActivity.getIntent(context, null, themeId == 0 ? -1 : themeId));
        }
        return false;
    }

    public static boolean handleAutoCollection(Context context, String url) {
        String urlPath = url;
        if (url.contains("?")) {
            urlPath = url.substring(0, url.indexOf("?"));
        }
        String key = urlPath.substring(urlPath.lastIndexOf("/") + 1);
        if (context != null && !EmptyUtils.isEmpty(key)) {
            context.startActivity(CollectionAutoActivity.getIntent(context, key, url));
        }
        return true;
    }

    public static boolean handleLive(Context context, String url) {
        // TODO context.startActivity(LiveShowActivity.getIntent(context));
        return true;
    }

    public static boolean handleCollection(Context context, String url) {
        String urlWithoutFragment = CommonTools.removeUrlFragment(url);
        Map<String, String> params = CommonTools.parseQueryParams(urlWithoutFragment);
        String urlWithoutQueries = CommonTools.removeQueryPath(urlWithoutFragment);
        String[] pathSegments = urlWithoutQueries.split("/");
        if (pathSegments.length > 1) {
            String pageKey = pathSegments[pathSegments.length - 1];
            String pageType = pathSegments[pathSegments.length - 2];
            if (pageKey != null && !pageKey.isEmpty() && pageType != null && !pageType.isEmpty()) {
                params.put("page_key", pageKey);
                params.put("page_type", pageType);
                Intent a = CollectionActivity.getIntent(context, url, pageKey, pageType, params);
                context.startActivity(a);
                return true;
            }
        }
        return false;
    }

    public static boolean handleOrderList(Context context, String url) {
        if (!AccountManager.get().isLogin()) {
            context.startActivity(AccountIntentCreator.getIntent(context));
            return true;
        }
        Map<String, String> params = CommonTools.parseQueryParams(url);
        if (context != null) {
            String filterStatus = params.get("filter_status");
            if (filterStatus == null) {
                filterStatus = OrderCategory.TYPE_ALL;
            }

            Intent intent = OrderListActivity.getIntent(context, filterStatus);
            context.startActivity(intent);
        }
        return true;
    }

    @Nullable
    public static Intent getDealPayV2Intent(Context context, String url) {
        if (context == null) {
            return null;
        }

        Map<String, String> params = CommonTools.parseQueryParams(url);
        String[] pathSegments = parsePathSegments(url);
        String checkoutId = pathSegments.length > 0 ? pathSegments[pathSegments.length - 1] : null;
        if (checkoutId != null && !checkoutId.isEmpty()) {
            String msg;
            try {
                msg = URLDecoder.decode(params.get("msg"), "utf-8");
            } catch (Exception e) {
                msg = null;
            }
            return DealPayActivity.getIntent(context, checkoutId, msg);
        }
        return null;
    }

    public static boolean handleDealPayV2(Context context, String url) {
        Intent a = getDealPayV2Intent(context, url);
        if (a != null) {
            context.startActivity(a);
            return true;
        }
        return false;
    }

    public static boolean handlePaymentAddEbtCard(Context context, String url) {
        Intent a = getPaymentAddEbtCardIntent(context, url);
        if (a != null) {
            if (!AccountManager.get().isLogin()) {
                context.startActivity(AccountIntentCreator.getIntent(context));
                return true;
            }
            context.startActivity(a);
            return true;
        }
        return false;
    }

    @Nullable
    private static Intent getPaymentAddEbtCardIntent(Context context, String url) {
        if (context == null) {
            return null;
        }

        Map<String, String> params = CommonTools.parseQueryParams(url);
        String payPlatform = params.get("pay_platform");
        if ("EBT".equals(payPlatform)) {
            return EbtCardAddActivity.getUrlTradeIntent(context, url);
        }
        return null;
    }

    public static boolean handleExperimentRoute(Context context, int experimentId, String url) {
        Intent intent = ExperimentRouteActivity.getIntent(context, experimentId, url);
        context.startActivity(intent);
        return true;
    }

    public static boolean handlePushToNative(Context context, String url) {
        //删除标记，避免重复生效
        String endPath = "";
        if (url.contains("#")) {
            int i = url.indexOf("#");
            endPath = url.substring(i);
            url = url.substring(0, i);
        }
        Map<String, String> params = CommonTools.parseQueryParams(url);
        params.remove(Constants.UrlMapParams.PUSH_TO_STYLE);
        String path = CommonTools.removeQueryPath(url);
        startActivity(context, WebViewActivity.getIntent(context, CommonTools.packetUrlParams(path, params) + endPath));
        return url.matches(Constants.UrlPattern.PUSH_TYPE_CLOSE);
    }

    @NonNull
    private static String[] parsePathSegments(String url) {
        if (url == null || url.isEmpty()) {
            return new String[0];
        }

        String urlWithoutQueries = url;
        int queryIndex = urlWithoutQueries.indexOf('?');
        if (queryIndex != -1) {
            urlWithoutQueries = urlWithoutQueries.substring(0, queryIndex);
        }
        return urlWithoutQueries.split("/");
    }
}
