package com.sayweee.weee.module.checkout.adapter;


import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.style.StrikethroughSpan;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.WeChatManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.cart.adapter.SimpleMultiTypeSectionAdapter;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.checkout.OrderSummaryIncludeFeeDialog;
import com.sayweee.weee.module.checkout.bean.CheckOutPointsData;
import com.sayweee.weee.module.checkout.bean.CheckOutTrackSummaryBean;
import com.sayweee.weee.module.checkout.bean.CheckoutCouponData;
import com.sayweee.weee.module.checkout.bean.CheckoutCurrentBenefitsData;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutOrderSummaryData;
import com.sayweee.weee.module.checkout.bean.CheckoutPaymentData;
import com.sayweee.weee.module.checkout.bean.CheckoutRemindTopData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CheckoutTermsData;
import com.sayweee.weee.module.checkout.bean.CheckoutTipData;
import com.sayweee.weee.module.checkout.bean.CheckoutVipData;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.DealPayMemberPlanData;
import com.sayweee.weee.module.checkout.bean.OrderSummaryBean;
import com.sayweee.weee.module.checkout.bean.OrderSummaryIncludeFeeBean;
import com.sayweee.weee.module.checkout.bean.OrderSummaryLoyaltyVipSavingBean;
import com.sayweee.weee.module.checkout.bean.OrderSummaryOptionBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutAddressInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutFeeInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutPointInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutTipInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout.bean.TagInfoBean;
import com.sayweee.weee.module.checkout2.adapter.CheckoutPurchaseChannelProvider;
import com.sayweee.weee.module.checkout2.adapter.CheckoutUsePointsProvider;
import com.sayweee.weee.module.checkout2.adapter.OnPurchaseChannelActionListener;
import com.sayweee.weee.module.checkout2.data.CheckoutPurchaseChannelData;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelExtraData;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.location.LocationUtils;
import com.sayweee.weee.service.pay.BraintreePaymentHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BitmapUtils;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.DefaultTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.SimpleTextWatcher;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.bubble.BubbleLayout;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.widget.shape.ShapeFrameLayout;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;
import com.stripe.android.view.CvcEditText;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class CheckOutSectionAdapter extends SimpleMultiTypeSectionAdapter<AdapterDataType, AdapterViewHolder> implements EagleImpressionAdapter {

    public static final int TYPE_DELIVERY_INFORMATION = 100;
    public static final int TYPE_PAYMENT_METHOD = 200;
    public static final int TYPE_PURCHASE_CHANNEL = 201;
    public static final int TYPE_USE_POINTS = 202;
    public static final int TYPE_REVIEW_ORDER = 300;
    public static final int TYPE_APPLY_COUPON = 400;
    public static final int TYPE_DELIVERY_TIP = 500;
    public static final int TYPE_ORDER_SUMMARY = 600;
    public static final int TYPE_VIP_MEMBER = 700;
    public static final int TYPE_TERMS = 800;
    public static final int TYPE_REVIEW_ORDER_LIST = 900;
    public static final int TYPE_PLACE = 1000;
    public static final int TYPE_REMIND_TOP = 1100;
    public static final int TYPE_SAVE_MORE_REWARDS = 1200;
    public static final int TYPE_SAVE_MORE_REWARDS_TITLE = 1201;
    public static final int TYPE_CURRENT_BENEFITS = 1300;
    public static final int TYPE_MEMBER_PLAN = 1400;

    @Override
    protected void registerAdapterType() {
        super.registerAdapterType();
        registerItemType(TYPE_REMIND_TOP, R.layout.item_remind_top_tips);
        registerItemType(TYPE_DELIVERY_INFORMATION, R.layout.item_checkout_section_delivery_information);
        registerItemType(TYPE_PAYMENT_METHOD, R.layout.item_checkout_section_payment_method);
        registerItemType(TYPE_REVIEW_ORDER, R.layout.item_checkout_section_review_order);
        registerItemType(TYPE_APPLY_COUPON, R.layout.item_checkout_section_apply_coupon);
        registerItemType(TYPE_DELIVERY_TIP, R.layout.item_checkout_section_delivery_tip);
        registerItemType(TYPE_ORDER_SUMMARY, R.layout.item_checkout_section_order_summary);
        registerItemType(TYPE_CURRENT_BENEFITS, R.layout.item_checkout_section_points_summary);
        registerItemType(TYPE_VIP_MEMBER, R.layout.item_checkout_section_vip_member);
        registerItemType(TYPE_TERMS, R.layout.item_checkout_section_payment_total);
        registerItemType(TYPE_PLACE, R.layout.item_review_detail_place);
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, AdapterDataType item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        if (!payloads.isEmpty()) {
            Object data = payloads.get(0);
            if (data instanceof CheckoutCouponData) {
                convertCoupon(helper, (CheckoutCouponData) item);
            } else if (data instanceof Boolean) {
                LinearLayout layoutTipList = helper.getView(R.id.layout_tip_list);
                TextView tvTipOther = (TextView) layoutTipList.getChildAt(layoutTipList.getChildCount() - 1).findViewById(R.id.tv_tip_other);
                if (tvTipOther != null && tvTipOther.hasFocus()) {
                    tvTipOther.clearFocus();
                }
            } else if (data instanceof CheckOutPointsData) {
                if (providers != null) {
                    int itemType = helper.getItemViewType();
                    ISectionProvider provider = providers.get(itemType);
                    if (provider != null) {
                        provider.convertPayloads(helper, item, payloads);
                    }
                }
            }
        }
    }

    @Override
    protected void addAdapterProvider() {
        super.addAdapterProvider();
        addItemProvider(CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS_TITLE, new CheckOutPointsTitleProvider());
        addItemProvider(CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS, new CheckOutPointsProvider());
        addItemProvider(CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST, new CheckoutOrderItemProvider());
        addItemProvider(CheckOutSectionAdapter.TYPE_PURCHASE_CHANNEL, new CheckoutPurchaseChannelProvider());
        addItemProvider(CheckOutSectionAdapter.TYPE_USE_POINTS, new CheckoutUsePointsProvider());
        addItemProvider(CheckOutSectionAdapter.TYPE_MEMBER_PLAN, new MemberPlanProvider());
    }

    @Override
    protected void convertNormal(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        switch (helper.getItemViewType()) {
            case TYPE_REMIND_TOP:
                convertRemindTop(helper, (CheckoutRemindTopData) item);
                break;
            case TYPE_DELIVERY_INFORMATION:
                convertDeliveryInformation(helper, (CheckoutDeliveryData) item);
                break;
            case TYPE_PAYMENT_METHOD:
                convertPaymentMethod(helper, (CheckoutPaymentData) item);
                break;
            case TYPE_APPLY_COUPON:
                convertCoupon(helper, (CheckoutCouponData) item);
                break;
            case TYPE_DELIVERY_TIP:
                convertTip(helper, (CheckoutTipData) item);
                break;
            case TYPE_ORDER_SUMMARY:
                convertFeeInfo(helper, (CheckoutOrderSummaryData) item);
                break;
            case TYPE_VIP_MEMBER:
                convertVip(helper, (CheckoutVipData) item);
                break;
            case TYPE_TERMS:
                convertTerms(helper, (CheckoutTermsData) item);
                break;
            case TYPE_CURRENT_BENEFITS:
                convertCurrentBenefits(helper, (CheckoutCurrentBenefitsData) item);
                break;
            default:
                break;
        }
    }

    private void convertCurrentBenefits(AdapterViewHolder helper, CheckoutCurrentBenefitsData item) {
        // TYPE_CURRENT_BENEFITS
        // R.layout.item_checkout_section_points_summary
        fillCurrentBenefits(item.currentList, helper.getView(R.id.layout_summary));
    }

    private void convertRemindTop(AdapterViewHolder helper, CheckoutRemindTopData item) {
        // TYPE_REMIND_TOP
        // R.layout.item_remind_top_tips
        helper.setText(R.id.tv_remind, ViewTools.fromHtml(item.remindTop.reminder_text));
        ShapeConstraintLayout layoutRemind = helper.getView(R.id.layout_remind);
        if (!EmptyUtils.isEmpty(item.remindTop.reminder_bg_color)) {
            layoutRemind.setBackgroundSolidDrawable(ViewTools.parseColor(item.remindTop.reminder_bg_color, Color.WHITE), CommonTools.dp2px(8));
        }
        boolean hasIcon = !EmptyUtils.isEmpty(item.remindTop.reminder_icon_url);
        ImageView ivRemind = helper.getView(R.id.iv_remind);
        ViewTools.setViewVisible(hasIcon, ivRemind);
        if (!hasIcon) {
            ImageLoader.load(mContext, ivRemind, WebpManager.convert(ImageSpec.SPEC_32, item.remindTop.reminder_icon_url), R.color.color_place);
        }
    }

    private void convertTerms(AdapterViewHolder helper, CheckoutTermsData item) {
        // TYPE_TERMS
        // R.layout.item_checkout_section_payment_total
        LinearLayout layoutTermsContainer = helper.getView(R.id.layout_terms_container);
        ViewTools.removeAllViews(layoutTermsContainer);
        int itemIndex = 0;
        if (item.containFrozen) {
            // 展示冷冻提示
            fillTermsItem(
                    layoutTermsContainer,
                    itemIndex++,
                    mContext.getString(R.string.s_order_frozen_tips),
                    false,
                    ViewTools.getDefaultLinkHandler(Constants.Url.TERMS_OF_SERVICE)
            );
        }
        int alcoholAgreementType = item.alcoholAgreementType;
        if (AlcoholHelper.isShowAlcoholAgreement(alcoholAgreementType)) {
            fillTermsItem(layoutTermsContainer, itemIndex++, mContext.getString(R.string.s_alcohol_agreement), true, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    AlcoholHelper.toAlcoholAgreement(alcoholAgreementType);
                }
            });
        }

        // Jira: https://sayweee.atlassian.net/browse/PCORE-2487
        if (item.showDefaultTerm) {
            fillTermsItem(
                    layoutTermsContainer,
                    itemIndex,
                    mContext.getString(R.string.s_checkout_place_agreement),
                    true,
                    ViewTools.getDefaultLinkHandler(AppConfig.HOST_WEB + Constants.Url.TERMS_AND_CONDITIONS)
            );
        }
    }

    private void convertVip(AdapterViewHolder helper, CheckoutVipData item) {
        PreCheckoutPointInfoBean bean = item.pointInfoBean;
        fillVip(bean, helper.getView(R.id.layout_vip));
    }

    private void convertFeeInfo(AdapterViewHolder helper, CheckoutOrderSummaryData item) {
        // TYPE_ORDER_SUMMARY
        // R.layout.item_checkout_section_order_summary
        OrderSummaryBean bean = item.orderSummaryBean;
        helper.setVisible(R.id.tv_title, true);
        // Loyalty icon
        String loyaltyVipIcon = bean.loyalty_vip_icon;
        ImageView ivIcon = helper.getView(R.id.iv_icon);
        if (!EmptyUtils.isEmpty(loyaltyVipIcon)) {
            ImageLoader.load(mContext, ivIcon, WebpManager.get().getConvertUrl(ImageSpec.SPEC_AVATAR, loyaltyVipIcon), R.color.color_place);
            ViewTools.setViewVisibilityIfChanged(ivIcon, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(ivIcon, false);
        }
        // Loyalty thanks
        helper.setText(R.id.tv_subtitle, ViewTools.fromHtml(bean.loyalty_vip_thanks_content));
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_subtitle), !EmptyUtils.isEmpty(bean.loyalty_vip_thanks_content));
        // Savings
        BubbleLayout bubbleLayout = helper.getView(R.id.layout_current_saving_root);
        if (bean.loyalty_vip_saving != null) {
            OrderSummaryLoyaltyVipSavingBean saving = bean.loyalty_vip_saving;

            // background & stroke color
            Integer backgroundColorInt = ViewTools.getColorByString(saving.background_color);
            Integer strokeColorInt;
            if (backgroundColorInt != null) { // solid color
                strokeColorInt = backgroundColorInt;
                bubbleLayout.setBubbleColor(backgroundColorInt);
                bubbleLayout.setStrokeColor(strokeColorInt);
            } else {
                strokeColorInt = ViewTools.getColorByResId(mContext, R.color.color_surface_1_fg_hairline_idle);
                if (strokeColorInt == null) {
                    strokeColorInt = Color.WHITE;
                }
                bubbleLayout.setBubbleColor(Color.WHITE);
                bubbleLayout.setStrokeColor(strokeColorInt);
            }

            // content
            TextView tvContent = bubbleLayout.findViewById(R.id.tv_content);
            tvContent.setText(ViewTools.fromHtml(saving.content));

            // info icon
            String suffixIconUrl = saving.include_icon;
            ImageView ivSuffix = bubbleLayout.findViewById(R.id.iv_icon_suffix);
            if (!EmptyUtils.isEmpty(suffixIconUrl)) {
                ImageLoader.load(mContext, ivSuffix, suffixIconUrl);
                ViewTools.setViewVisibilityIfChanged(ivSuffix, true);
            } else {
                ViewTools.setViewVisibilityIfChanged(ivSuffix, false);
            }

            // popup
            ViewTools.setViewOnSafeClickListener(helper.getView(R.id.v_info_popup), v -> {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(EagleTrackEvent.ModNm.PURCHASE_SUMMARY)
                        .setTargetNm("loyalty_vip_saving")
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .build().getParams());
                showBottomDialog(saving.order_summary_include_fee);
            });

            ViewTools.setViewVisibilityIfChanged(bubbleLayout, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(bubbleLayout, false);
        }
        // Summary
        fillOrderSummary(bean, helper.getView(R.id.layout_summary));
    }

    private void convertTip(AdapterViewHolder helper, CheckoutTipData item) {
        // TYPE_DELIVERY_TIP
        // R.layout.item_checkout_section_delivery_tip
        fillTipList(item, helper.getView(R.id.layout_tip_list));
    }

    private void convertCoupon(AdapterViewHolder helper, CheckoutCouponData item) {
        PreCheckoutV2Bean.CouponInfoBean bean = item.couponInfoBean;
        TextView tvCouponChange = helper.getView(R.id.tv_coupon_change);
        ViewTools.setViewVisible(false, tvCouponChange);
        helper.setText(R.id.tv_coupon_code, R.string.s_order_pick_coupon_tips);
        helper.setVisibleCompat(R.id.tv_coupon_applied, false);
        helper.setVisibleCompat(R.id.iv_arrow, true);
        helper.setVisibleCompat(R.id.tv_coupon_size, EmptyUtils.isEmpty(bean) && item.size > 0);
        if (!EmptyUtils.isEmpty(bean)) {
            ViewTools.setViewHtml(tvCouponChange, mContext.getString(R.string.s_coupon_change_tip));
            ViewTools.setViewVisible(!TextUtils.isEmpty(bean.code), tvCouponChange);
            helper.setText(R.id.tv_coupon_code, bean.title);
            helper.setGone(R.id.tv_coupon_applied, !EmptyUtils.isEmpty(bean.code));
            helper.setGone(R.id.iv_arrow, EmptyUtils.isEmpty(bean.code));
            helper.setVisibleCompat(R.id.tv_coupon_size, EmptyUtils.isEmpty(bean.code) && item.size > 0);
        } else {
            helper.setVisibleCompat(R.id.tv_coupon_size, item.size > 0);
        }
        if (item.size > 0) {
            String tips = item.size == 1
                    ? mContext.getString(R.string.s_coupon_single_tip, 1)
                    : mContext.getString(R.string.s_coupon_tips, item.size);
            helper.setText(R.id.tv_coupon_size, tips);
        }
        helper.addOnClickListener(R.id.layout_coupon);
        helper.addOnClickListener(R.id.tv_coupon_change);
    }

    private void convertPaymentMethod(AdapterViewHolder helper, CheckoutPaymentData item) {
        // TYPE_PAYMENT_METHOD
        // R.layout.item_checkout_section_payment_method
        fillPayment(helper, item);
        fillPointsCash(helper, item);
    }

    private void convertDeliveryInformation(AdapterViewHolder helper, CheckoutDeliveryData item) {
        PreCheckoutAddressInfoBean bean = item.addressInfoDTO;
        boolean lackOfAddress = TextUtils.isEmpty(bean.addr_firstname)
                || TextUtils.isEmpty(bean.addr_lastname)
                || TextUtils.isEmpty(bean.phone)
                || TextUtils.isEmpty(bean.address);
        TextView tvDeliveryRemark = helper.getView(R.id.tv_delivery_alert);
        if (!lackOfAddress) {
            helper.setText(R.id.tv_name, bean.addr_firstname + " " + bean.addr_lastname);
        } else {
            helper.setText(R.id.tv_name, R.string.s_order_pick_address_tips);
        }
        helper.setVisibleCompat(R.id.iv_error_no_address, lackOfAddress);
        helper.setText(R.id.tv_address, bean.address);
        helper.setText(R.id.tv_phone, DefaultTools.formatMobileOnDelivery(bean.phone));
        tvDeliveryRemark.setTextColor(!EmptyUtils.isEmpty(bean.comment) ?
                ContextCompat.getColor(mContext, R.color.color_highlight_standalone_idle)
                : ContextCompat.getColor(mContext, R.color.color_surface_1_fg_subtle_idle));
        boolean showGeoDistanceWarning = LocationUtils.isDistanceReachLimit(bean.addr_longitude, bean.addr_latitude, bean.away_from_distance);
//        helper.setGone(R.id.layout_geo_distance_warning, showGeoDistanceWarning);
        if (showGeoDistanceWarning && !EmptyUtils.isEmpty(bean.away_from_distance_content)) {
            helper.setText(R.id.tv_geo_warning, ViewTools.fromHtml(bean.away_from_distance_content));
        }
        if (!EmptyUtils.isEmpty(bean.comment)) {
            helper.setText(R.id.tv_delivery_alert, bean.comment);
        } else {
            helper.setText(R.id.tv_delivery_alert, mContext.getString(R.string.s_order_remark_hint));
        }
        if (!EmptyUtils.isEmpty(bean.delivery_instruction_tip)) {
            helper.setText(R.id.tv_instruction, bean.delivery_instruction_tip);
        }
        helper.setGone(R.id.tv_delivery_error, !EmptyUtils.isEmpty(item.errorTipMessage));
        if (!EmptyUtils.isEmpty(item.errorTipMessage)) {
            helper.setText(R.id.tv_delivery_error, item.errorTipMessage);
        }
        helper.setGone(R.id.layout_full_name, false);
        helper.setGone(R.id.tv_instruction, !EmptyUtils.isEmpty(bean.delivery_instruction_tip) && !lackOfAddress);
        helper.setGone(R.id.tv_address, !lackOfAddress);
        helper.setGone(R.id.tv_email, false);
        helper.setGone(R.id.tv_phone, !lackOfAddress);
        helper.setGone(R.id.tv_delivery_alert, !lackOfAddress);
        helper.addOnClickListener(R.id.layout_information);
        helper.setVisibleCompat(R.id.iv_arrow, !item.noEdit);
    }

    private void fillCurrentBenefits(List<PreCheckoutV2Bean.CurrentBenefitsBean> currentList, LinearLayout layout) {
        // TYPE_CURRENT_BENEFITS
        // R.layout.item_checkout_section_points_summary
        layout.removeAllViews();
        for (int i = 0; i < currentList.size(); i++) {
            PreCheckoutV2Bean.CurrentBenefitsBean item = currentList.get(i);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            int finalI = i;
            layout.addView(ViewTools.getHelperView(layout, R.layout.item_checkout_points_summary, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    boolean hasIcon = !EmptyUtils.isEmpty(item.icon_url);
                    helper.setVisible(R.id.top_view, finalI == 1);
                    helper.setVisible(R.id.iv_title_icon, hasIcon);
                    if (hasIcon) {
                        ImageLoader.load(mContext, helper.getView(R.id.iv_title_icon), WebpManager.convert(ImageSpec.SPEC_32, item.icon_url), R.color.color_place);
                    }
                    TextView tvTitle = helper.getView(R.id.tv_title);
                    TextView tvPrice = helper.getView(R.id.tv_price);
                    int style = finalI == 0 ? R.style.style_fluid_root_heading_sm : R.style.style_fluid_root_body_base;
                    int priceStyle = finalI == 0 ? R.style.style_fluid_root_numeral_base : R.style.style_fluid_root_body_base;
                    ViewTools.applyTextStyleAndColor(tvTitle, style, R.color.color_surface_1_fg_minor_idle);
                    ViewTools.applyTextStyleAndColor(tvPrice, priceStyle, R.color.brand_color_primary_1);
                    helper.setText(R.id.tv_title, item.isNullPoint() ? new Spanny(item.desc, new StrikethroughSpan()) : item.desc);
                    helper.setText(R.id.tv_price, item.isNullPoint() ? new Spanny(item.amount
                            , new StrikethroughSpan()) : item.amount);
                }
            }), params);
        }
    }

    private void fillPayment(AdapterViewHolder helper, CheckoutPaymentData item) {
        // TYPE_PAYMENT_METHOD
        // R.layout.item_checkout_section_payment_method
        Context context = helper.itemView.getContext();
        String profile = null;
        String type = null;
        PreCheckoutV2Bean.PaymentInfoBean bean = ((CheckoutPaymentData) item).paymentInfoDTO;
        if (Constants.PayType.STRIPE.equals(bean.payment_category) || Constants.PayType.BRAINTREE.equals(bean.payment_category)) {
            if (bean.default_payment_info != null) {
                profile = bean.default_payment_info.tail_num;
                type = bean.default_payment_info.card_type;
            } else {
                bean.payment_category = null;
            }
        }
        // Don't show venmo when venmo app is not installed
        if (Constants.PayType.VENMO.equals(bean.payment_category)
                && !BraintreePaymentHelper.isVenmoInstalled(context)
        ) {
            bean.payment_category = null;
        }
        TextView tvPaymentCode = helper.getView(R.id.tv_payment_code);
        TextView tvPaymentError = helper.getView(R.id.tv_payment_error);
        ImageView ivPayment = helper.getView(R.id.iv_payment);
        ImageView ivPaymentCard = helper.getView(R.id.iv_payment_card);
        LinearLayout layoutPaymentCard = helper.getView(R.id.layout_payment_card);
        View dividerCvc = helper.getView(R.id.divider_cvc);
        ShapeFrameLayout layoutCvc = helper.getView(R.id.layout_cvc);
        ShapeConstraintLayout layoutPayment = helper.getView(R.id.layout_payment);
        ShapeConstraintLayout layoutCvcOut = helper.getView(R.id.layout_cvc_out);
        layoutCvcOut.setBackground(null);
        CvcEditText etCvc = helper.getView(R.id.et_cvc);
        ViewTools.setViewVisible(false, helper.getView(R.id.tv_payment_code), ivPayment, ivPaymentCard, layoutPaymentCard, dividerCvc, layoutCvc);
        ViewTools.setViewVisible(false, tvPaymentError);
        layoutPayment.setBackground(ShapeHelper.buildStrokeDrawable(context.getResources().getColor(R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(1), CommonTools.dp2px(20)));
        helper.addOnClickListener(R.id.layout_payment);
        if ((Constants.PayType.ALIPAY.equals(bean.payment_category) || Constants.PayType.ALIPAY_E.equals(bean.payment_category) || Constants.PayType.ALIPAY_K.equals(bean.payment_category)) && !AppFilter.PayConfig.isNotSupport(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_alipay);
        } else if (Constants.PayType.WECHAT.equals(bean.payment_category) && WeChatManager.getInstance().isWXAppInstalled() && !AppFilter.PayConfig.isNotSupport(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_wechat);
        } else if (Constants.PayType.PAYPAL.equals(bean.payment_category) || Constants.PayType.PAYPAL_BRAINTREE.equals(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_paypal);
        } else if (Constants.PayType.CREDIT_CARD.equals(bean.payment_category)) {
            layoutPaymentCard.setVisibility(View.VISIBLE);
        } else if (Constants.PayType.STRIPE.equals(bean.payment_category) || Constants.PayType.BRAINTREE.equals(bean.payment_category)) {
            if (item.isCheckCvv()) {
                ViewTools.setViewVisible(true, tvPaymentError);
                layoutPayment.setBackground(null);
                layoutCvcOut.setBackground(ShapeHelper.buildStrokeDrawable(context.getResources().getColor(R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(1), CommonTools.dp2px(20)));
                ViewTools.setViewVisible(View.VISIBLE, dividerCvc, layoutCvc);
                etCvc.setHint(context.getString(R.string.enter_cvc));
                tvPaymentError.setTextColor(context.getResources().getColor(R.color.text_main));
                ViewTools.setViewText(tvPaymentError, context.getString(R.string.confirm_cvc_number));
                etCvc.setErrorColor(context.getResources().getColor(R.color.text_main));
                etCvc.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_DONE) {
                            clearFocus(etCvc);
                        }
                        return false;
                    }
                });

                etCvc.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        if (hasFocus) {
                            layoutCvc.setBackground(ShapeHelper.buildStrokeDrawable(Color.parseColor("#ffffff"), 0, 0, CommonTools.dp2px(20), CommonTools.dp2px(20), Color.parseColor("#008ED6"), CommonTools.dp2px(1)));
                            tvPaymentError.setTextColor(context.getResources().getColor(R.color.text_main));
                            ViewTools.setViewText(tvPaymentError, context.getString(R.string.confirm_cvc_number));
                            if (callback != null) {
                                callback.onEditTextFocusChanged(v);
                            }
                        } else {
                            String s = etCvc.getText() != null ? etCvc.getText().toString() : null;
                            boolean isCvcInvalid = s != null && !s.isEmpty() && s.length() < 3;
                            if (isCvcInvalid) {
                                item.isCvcInvalid = true;
                                ViewTools.setViewText(tvPaymentError, context.getString(R.string.please_enter_valid_cvc_number));
                                tvPaymentError.setTextColor(context.getResources().getColor(R.color.text_error));
                                layoutCvc.setBackground(ShapeHelper.buildStrokeDrawable(Color.parseColor("#ffffff"), 0, 0, CommonTools.dp2px(20), CommonTools.dp2px(20), Color.parseColor("#DF2C2E"), CommonTools.dp2px(1)));
                            } else {
                                item.isCvcInvalid = false;
                                layoutCvc.setBackground(null);
                            }
                            if (callback != null) {
                                callback.onCvcEditTextNoFocus(s, isCvcInvalid);
                            }
                        }
                    }
                });
            }
            tvPaymentCode.setTextColor(context.getResources().getColor(R.color.color_surface_1_fg_default_idle));
            tvPaymentCode.setText(String.format(context.getString(R.string.s_card_tail_profile), profile));
            ViewTools.applyTextStyle(tvPaymentCode, R.style.style_fluid_root_body_base);
            ivPaymentCard.setImageResource(OrderHelper.getCartDrawableByType(type));
            ViewTools.setViewVisible(true, tvPaymentCode, ivPaymentCard);
        } else if (Constants.PayType.EBT.equals(bean.payment_category) || Constants.PayType.EBT_LATINO.equals(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_ebt);
        } else if (Constants.PayType.VENMO.equals(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_venmo);
        } else if (Constants.PayType.CASH_APP.equals(bean.payment_category)) {
            ivPayment.setVisibility(View.VISIBLE);
            ivPayment.setImageResource(R.mipmap.pay_cash_app);
        } else { //不支持的支付方式或者未设置支付方式
            tvPaymentCode.setVisibility(View.VISIBLE);
            ViewTools.applyTextStyle(tvPaymentCode, R.style.style_fluid_root_heading_sm);
            tvPaymentCode.setText(R.string.s_order_pick_payment_method_tips);
            tvPaymentCode.setTextColor(context.getResources().getColor(R.color.color_secondary_standalone_idle));
            if (item.seeErrorCard) {
                item.seeErrorCard = false;
                layoutPayment.setBackground(ShapeHelper.buildDrawable(context.getResources().getColor(R.color.root_color_red_spectrum_2), CommonTools.dp2px(20), context.getResources().getColor(R.color.root_color_red_spectrum_14), CommonTools.dp2px(1)));
                ViewTools.setViewVisible(true, tvPaymentError);
                tvPaymentError.setTextColor(context.getResources().getColor(R.color.text_error));
                tvPaymentError.setText(context.getString(R.string.s_please_select_payment_method));
            }
        }
    }

    private void fillPointsCash(AdapterViewHolder helper, CheckoutPaymentData item) {
        // TYPE_PAYMENT_METHOD
        // R.layout.item_checkout_section_payment_method
        PreCheckoutPointInfoBean bean = item.pointInfoDTO;
        ShapeConstraintLayout layoutPointsPayment = helper.getView(R.id.layout_points_payment);
        TextView tvPointsPaymentTitle = helper.getView(R.id.tv_points_payment_title);
        TextView tvPointsPaymentValue = helper.getView(R.id.tv_points_payment_value);
        ImageView ivPointsPaymentUse = helper.getView(R.id.iv_points_payment_use);
        layoutPointsPayment.setAlpha(1f);
        if (bean != null) {
            boolean hasPoints = bean.points_current > 0;
            ViewTools.setViewVisible(hasPoints, layoutPointsPayment);
            ivPointsPaymentUse.setAlpha(1f);
            if (hasPoints) {
                boolean isUnavailable;
                String unavailableDesc;
                if (item.saveRewardsChecked) {
                    isUnavailable = true;
                    unavailableDesc = helper.itemView.getContext().getString(R.string.s_checkout_rewards_toast);
                } else if (bean.unavailable_alert != null && bean.unavailable_alert.status) {
                    isUnavailable = true;
                    unavailableDesc = bean.unavailable_alert.desc;
                } else {
                    isUnavailable = false;
                    unavailableDesc = null;
                }
                if (isUnavailable) {
                    layoutPointsPayment.setAlpha(0.4f);
                    ivPointsPaymentUse.setAlpha(0.6f);
                }
                ViewTools.setViewHtml(tvPointsPaymentTitle, mContext.getString(R.string.s_use_weee_points));
                ViewTools.setViewHtml(tvPointsPaymentValue,
                        mContext.getString(R.string.s_order_value_of_points_new,
                                String.valueOf(bean.points_current),
                                OrderHelper.formatUSMoney(OrderHelper.calcPointsPrice(bean.points_current))
                        ));
                item.checked = bean.points_price > 0;
                ivPointsPaymentUse.setImageResource(isUnavailable ? R.mipmap.pic_cant_checked : item.checked ? R.drawable.drawable_checkmark_circle_filled : R.drawable.drawable_checkmark_circle_empty);
                helper.setOnViewClickListener(R.id.layout_points_payment, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (isUnavailable) {
                            if (!EmptyUtils.isEmpty(unavailableDesc)) {
                                Toaster.showToast(unavailableDesc);
                            }
                            return;
                        }
                        if (item.pointsCanChecked) {
                            item.checked = !item.checked;
                            item.pointsCanChecked = false;
                            ivPointsPaymentUse.setImageResource(item.checked ? R.drawable.drawable_checkmark_circle_filled : R.drawable.drawable_checkmark_circle_empty);
                            if (callback != null) {
                                callback.updatePoints(item.checked);
                            }
                        }
                    }
                });
            }
            ivPointsPaymentUse.setSelected(bean.points_price > 0);
        }
    }

    private void fillTipList(CheckoutTipData checkoutTipData, LinearLayout layoutTips) {
        PreCheckoutTipInfoBean bean = checkoutTipData.tipInfoBean;
        if (!EmptyUtils.isEmpty(bean.options)) {
            layoutTips.setVisibility(View.VISIBLE);
            layoutTips.removeAllViews();
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, CommonTools.dp2px(43), 1);
            for (int i = 0; i < bean.options.size(); i++) {
                PreCheckoutTipInfoBean.OptionsBean item = bean.options.get(i);
                int finalI = i;
                layoutTips.addView(ViewTools.getHelperView(layoutTips, R.layout.item_checkout_tips, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper helper) {
                        ShapeTextView tvTip = helper.getView(R.id.tv_tip);
                        ShapeFrameLayout layoutTipOther = helper.getView(R.id.layout_tip_other);
                        TextView tvTipOther = helper.getView(R.id.tv_tip_other);
                        ImageView ivTipEdit = helper.getView(R.id.iv_tip_edit);
                        boolean isNotOther = finalI < bean.options.size() - 1;
                        helper.setText(R.id.tv_tip, item.getDisplayTip());
                        if (item.index == bean.selected_option_index) {
                            if (isNotOther) {
                                tvTip.setBackgroundSolidDrawable(ContextCompat.getColor(mContext, R.color.color_btn_primary_bg), CommonTools.dp2px(20));
                                tvTip.setTextColor(ContextCompat.getColor(mContext, R.color.color_btn_primary_fg_default));
                            } else {
                                tvTipOther.setText((int) item.tip + "");
                                setDollarPrefix(tvTipOther);
                                helper.setVisible(R.id.iv_tip_edit, true);
                                ivTipEdit.setImageDrawable(BitmapUtils.tint(mContext, R.mipmap.tip_edit_tint, ContextCompat.getColor(mContext, finalI == bean.options.size() - 1 ? R.color.color_fore : R.color.text_main)));
                                tvTipOther.setPadding(0, 0, CommonTools.dp2px(12), 0);
                                updateOtherTips(layoutTipOther, tvTipOther, true);
                            }
                        } else {
                            if (isNotOther) {
                                tvTip.setBackgroundStrokeDrawable(ContextCompat.getColor(mContext, R.color.color_surface_100_hairline), CommonTools.dp2px(1), CommonTools.dp2px(20));
                                tvTip.setTextColor(ContextCompat.getColor(mContext, R.color.color_surface_100_fg_minor));
                            } else {
                                updateOtherTips(layoutTipOther, tvTipOther, false);
                            }
                        }
                        helper.setVisible(R.id.tv_tip, isNotOther);
                        helper.setVisible(R.id.layout_tip_other, !isNotOther);
                        helper.setOnClickListener(R.id.tv_tip, new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                if (!checkoutTipData.tipCanChecked) {
                                    return;
                                }
                                updateTips(bean, layoutTips, finalI);
                                ShapeFrameLayout layoutTipOther = layoutTips.getChildAt(bean.options.size() - 1).findViewById(R.id.layout_tip_other);
                                TextView tvTipOther = layoutTips.getChildAt(bean.options.size() - 1).findViewById(R.id.tv_tip_other);
                                tvTipOther.setText(null);
                                tvTipOther.setHint(R.string.s_others);
                                helper.setVisible(R.id.iv_tip_edit, false);
                                tvTipOther.setPadding(0, 0, 0, 0);
                                updateOtherTips(layoutTipOther, tvTipOther, false);
                                if (callback != null) {
                                    checkoutTipData.tipCanChecked = false;
                                    callback.updateTipsData(item);
                                }
                            }
                        });

                        tvTipOther.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                            @Override
                            public void onFocusChange(View v, boolean hasFocus) {
                                if (hasFocus) {
                                    tvTipOther.setHint(null);
                                    helper.setVisible(R.id.iv_tip_edit, false);
                                    setDollarPrefix(tvTipOther);
                                    updateTips(bean, layoutTips, -1);
                                    tvTipOther.setPadding(0, 0, 0, 0);
                                    tvTipOther.setTextColor(ContextCompat.getColor(mContext, R.color.color_surface_1_fg_default_idle));
                                    layoutTipOther.setBackgroundStrokeDrawable(ContextCompat.getColor(mContext, R.color.color_btn_primary_bg), CommonTools.dp2px(1), CommonTools.dp2px(20));
                                    if (callback != null) {
                                        callback.onEditTextFocusChanged(v);
                                    }
                                } else {
                                    String s = ViewTools.getViewText(tvTipOther);
                                    if (TextUtils.isEmpty(s) || s.length() == 1) {
                                        tvTipOther.setText(null);
                                        tvTipOther.setHint(R.string.s_others);
                                        helper.setVisible(R.id.iv_tip_edit, false);
                                        tvTipOther.setPadding(0, 0, 0, 0);
                                        updateTips(bean, layoutTips, 0);
                                        updateOtherTips(layoutTipOther, tvTipOther, false);
                                    } else {
                                        updateTips(bean, layoutTips, -1);
                                        tvTipOther.setPadding(0, 0, CommonTools.dp2px(12), 0);
                                        updateOtherTips(layoutTipOther, tvTipOther, true);
                                        helper.setVisible(R.id.iv_tip_edit, true);
                                        item.tip = getRemoveDollarNumber(tvTipOther);
                                        if (callback != null) {
                                            checkoutTipData.tipCanChecked = false;
                                            callback.updateTipsData(item);
                                        }
                                    }
                                }
                                ivTipEdit.setImageDrawable(BitmapUtils.tint(mContext, R.mipmap.tip_edit_tint, ContextCompat.getColor(mContext, finalI == bean.options.size() - 1 ? R.color.color_fore : R.color.text_main)));
                            }
                        });

                        tvTipOther.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                            @Override
                            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                                if (actionId == EditorInfo.IME_ACTION_DONE) {
                                    clearFocus(tvTipOther);
                                }
                                return false;
                            }
                        });

                        tvTipOther.addTextChangedListener(new SimpleTextWatcher() {

                            @Override
                            public void afterTextChanged(Editable s) {
                                super.afterTextChanged(s);
                                if (tvTipOther.hasFocus()) {
                                    setDollarPrefix(tvTipOther);
                                }
                            }
                        });
                    }
                }), params);
            }

        } else {
            layoutTips.setVisibility(View.GONE);
        }
    }

    private void updateOtherTips(ShapeFrameLayout layoutTipOther, TextView tvTipOther, boolean isSelected) {
        if (isSelected) {
            layoutTipOther.setBackgroundSolidDrawable(ContextCompat.getColor(mContext, R.color.color_btn_primary_bg), CommonTools.dp2px(20));
            tvTipOther.setTextColor(ContextCompat.getColor(mContext, R.color.color_btn_primary_fg_default));
        } else {
            layoutTipOther.setBackgroundStrokeDrawable(ContextCompat.getColor(mContext, R.color.color_surface_100_hairline), CommonTools.dp2px(1), CommonTools.dp2px(20));
            tvTipOther.setTextColor(ContextCompat.getColor(mContext, R.color.color_surface_100_fg_minor));
        }
    }

    private void updateTips(PreCheckoutTipInfoBean bean, LinearLayout layoutTips, int index) {
        for (int j = 0; j < bean.options.size(); j++) {
            ShapeTextView tvTip = layoutTips.getChildAt(j).findViewById(R.id.tv_tip);
            tvTip.setBackgroundStrokeDrawable(ContextCompat.getColor(mContext, R.color.color_surface_100_hairline), CommonTools.dp2px(1), CommonTools.dp2px(20));
            tvTip.setTextColor(ContextCompat.getColor(mContext, R.color.color_surface_100_fg_minor));
            if (bean.options.get(j).index == index) {
                tvTip.setBackgroundSolidDrawable(ContextCompat.getColor(mContext, R.color.color_btn_primary_bg), CommonTools.dp2px(20));
                tvTip.setTextColor(ContextCompat.getColor(mContext, R.color.color_btn_primary_fg_default));
            }
        }
    }

    private void setDollarPrefix(TextView view) {
        if (view != null) {
            String dollar = "$";
            String s = ViewTools.getViewText(view);
            if (TextUtils.isEmpty(s)) {
                view.setText(dollar);
            } else {
                if (!s.startsWith(dollar) || s.lastIndexOf(dollar) != 0) {
                    String s1 = s.replaceAll(dollar, "");
                    view.setText(String.format("%1$s%2$s", dollar, s1));
                }
            }
            if (view instanceof EditText) {
                ((EditText) view).setSelection(ViewTools.getViewText(view).length());
            }
        }
    }

    public Integer getRemoveDollarNumber(TextView view) {
        String dollar = "$";
        String s = ViewTools.getViewText(view);
        if (!TextUtils.isEmpty(s)) {
            String s1 = s.replace(dollar, "");
            if (!EmptyUtils.isEmpty(s1)) {
                return DecimalTools.parseInt(s1);
            }
        }
        return 0;
    }

    private void fillOrderSummary(OrderSummaryBean bean, LinearLayout layoutSummary) {
        ViewTools.removeAllViews(layoutSummary);
        List<OrderSummaryOptionBean> options;
        options = bean.order_summary_options;
        if (EmptyUtils.isEmpty(options)) {
            ViewTools.setViewVisibilityIfChanged(layoutSummary, false);
            return;
        }

        final int itemCount = options.size();
        for (int i = 0; i < itemCount; i++) {
            OrderSummaryOptionBean item = bean.order_summary_options.get(i);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            final int position = i;
            layoutSummary.addView(ViewTools.getHelperView(layoutSummary, R.layout.item_checkout_summary, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    Context context = helper.itemView.getContext();
                    TextView tvTitle = helper.getView(R.id.tv_title);
                    TextView tvSubTitle = helper.getView(R.id.tv_sub_title);
                    TextView tvPrice = helper.getView(R.id.tv_price);
                    TextView tvBasePrice = helper.getView(R.id.tv_price_delete);

                    if (position == itemCount - 1) {
                        layoutParams.topMargin = CommonTools.dp2px(12);
                    } else if (position != 0) {
                        layoutParams.topMargin = CommonTools.dp2px(4);
                    }

                    if (item.show_top_line) {
                        View splitter = new View(helper.itemView.getContext());
                        LinearLayout.LayoutParams splitterLayoutParams;
                        splitterLayoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonTools.dp2px(1));
                        if (position != 0) {
                            splitterLayoutParams.topMargin = CommonTools.dp2px(12);
                        }
                        splitter.setLayoutParams(splitterLayoutParams);
                        splitter.setBackgroundResource(R.color.color_surface_2_bg_idle);
                        layoutSummary.addView(splitter);
                    }

                    if (!EmptyUtils.isEmpty(item.title) && !EmptyUtils.isEmpty(item.title.textColor)) {
                        ViewTools.applyTextColor(tvTitle, item.title.textColor);
                    } else {
                        if (position == 0) { // subtotal
                            ViewTools.applyTextColor(tvTitle, R.color.color_surface_100_fg_default);
                            ViewTools.applyTextStyle(tvTitle, R.style.style_body_xs_medium);
                        } else if (position == itemCount - 1) { // final payment amount
                            ViewTools.applyTextColor(tvTitle, R.color.color_surface_100_fg_default);
                            ViewTools.applyTextStyle(tvTitle, R.style.style_body_sm_medium);
                        } else {
                            ViewTools.applyTextColor(tvTitle, R.color.color_surface_100_fg_minor);
                            ViewTools.applyTextStyle(tvTitle, R.style.style_body_xs);
                        }
                    }

                    // price
                    helper.setVisible(R.id.tv_price, !EmptyUtils.isEmpty(item.amount));
                    if (!EmptyUtils.isEmpty(item.amount)) {
                        ViewTools.updatePaddings(tvPrice, 0, null, 0, null);
                        if (position == itemCount - 1) {
                            ViewTools.applyTextColor2(tvPrice, item.amount.textColor, R.color.color_surface_100_fg_default);
                            ViewTools.applyTextStyle(tvPrice, R.style.style_body_sm_medium);
                        } else {
                            ViewTools.applyTextColor2(tvPrice, item.amount.textColor, R.color.color_surface_100_fg_minor);
                            ViewTools.applyTextStyle(tvPrice, R.style.style_body_xs);
                        }
                        tvPrice.setText(item.amount.text);
                        Integer backgroundColorInt = ViewTools.getColorByString(item.amount.bgColor);
                        if (position != itemCount - 1 && backgroundColorInt != null) {
                            ViewTools.updatePaddings(tvPrice, CommonTools.dp2px(4), null, CommonTools.dp2px(4), null);
                            ShapeHelper.setBackgroundSolidDrawable(
                                    tvPrice,
                                    backgroundColorInt,
                                    CommonTools.dp2px(context, R.dimen.prop_size_radius_200, 4)
                            );
                            ViewTools.applyTextColor(tvPrice, R.color.color_surface_200_fg_default);
                            ViewTools.applyTextStyle(tvPrice, R.style.style_body_3xs_strong);
                        }
                    }

                    // base price
                    helper.setVisible(R.id.tv_price_delete, !EmptyUtils.isEmpty(item.base_amount));
                    if (!EmptyUtils.isEmpty(item.base_amount)) {
                        ViewTools.applyTextColor(tvBasePrice, item.base_amount.textColor);
                        tvBasePrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
                        tvBasePrice.setText(item.base_amount.text);
                    }

                    // additional title
                    helper.setVisible(R.id.tv_sub_title, !EmptyUtils.isEmpty(item.title_additional));
                    if (!EmptyUtils.isEmpty(item.title_additional)) {
                        ViewTools.applyTextColor(tvSubTitle, item.title_additional.textColor);
                        tvSubTitle.setText(item.title_additional.text);
                    }

                    // fee
                    View ivIcon = helper.getView(R.id.iv_icon);
                    ViewTools.setTouchDelegate(ivIcon, helper.itemView, -CommonTools.dp2px(8), -CommonTools.dp2px(8));
                    if (item.order_summary_include_fee != null && !EmptyUtils.isEmpty(item.order_summary_include_fee.included_fees)) {
                        if (!EmptyUtils.isEmpty(item.order_summary_include_fee.included_fees.get(0).amount)) {
                            TagInfoBean amountData = item.order_summary_include_fee.included_fees.get(0).amount;
                            if (!EmptyUtils.isEmpty(amountData.type) && "img".equalsIgnoreCase(amountData.type)) {
                                ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, amountData.iconUrl), R.color.color_place);
                            }
                        }
                        ViewTools.setViewVisibilityIfChanged(ivIcon, true);
                        ViewTools.setViewOnSafeClickListener(ivIcon, v -> {
                            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                                    .setMod_nm(EagleTrackEvent.ModNm.ORDER_SUMMARY)
                                    .setTargetNm(item.track_title)
                                    .setTargetPos(position)
                                    .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                                    .setClickType(EagleTrackEvent.ClickType.VIEW);
                            AppAnalytics.logClickAction(builder.build().getParams());

                            showBottomDialog(item.order_summary_include_fee);
                        });
                    } else if (!EmptyUtils.isEmpty(item.included_fees)) { // #PEN-1167 old version compat
                        if (!EmptyUtils.isEmpty(item.included_fees.get(0).amount)) {
                            TagInfoBean amountData = item.included_fees.get(0).amount;
                            if (!EmptyUtils.isEmpty(amountData.type) && "img".equalsIgnoreCase(amountData.type)) {
                                ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, amountData.iconUrl), R.color.color_place);
                            }
                        }
                        ViewTools.setViewVisibilityIfChanged(ivIcon, true);
                        ViewTools.setViewOnSafeClickListener(ivIcon, v -> {
                            EagleTrackModel.Builder builder = new EagleTrackModel.Builder()
                                    .setMod_nm(EagleTrackEvent.ModNm.ORDER_SUMMARY)
                                    .setTargetNm(item.track_title)
                                    .setTargetPos(position)
                                    .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                                    .setClickType(EagleTrackEvent.ClickType.VIEW);
                            AppAnalytics.logClickAction(builder.build().getParams());

                            OrderSummaryIncludeFeeBean fakeBean = new OrderSummaryIncludeFeeBean();
                            fakeBean.title = mContext.getString(R.string.s_checkout_fee_include);
                            fakeBean.included_fees = item.included_fees;
                            showBottomDialog(fakeBean);
                        });
                    } else {
                        ViewTools.setViewVisibilityIfChanged(ivIcon, false);
                    }

                    // title
                    if (!EmptyUtils.isEmpty(item.title)) {
                        tvTitle.setText(item.title.text);
                    }
                }
            }), layoutParams);
        }
        ViewTools.setViewVisibilityIfChanged(layoutSummary, true);
    }

    private void fillVip(PreCheckoutPointInfoBean bean, LinearLayout layoutVip) {
        layoutVip.removeAllViews();
        for (int i = 0; i < bean.order_reward_points_desc_v2.size(); i++) {
            PreCheckoutPointInfoBean.PointsDescBean item = bean.order_reward_points_desc_v2.get(i);
            int finalI = i;
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            params.topMargin = CommonTools.dp2px(i != 0 ? 4 : 0);
            layoutVip.addView(ViewTools.getHelperView(layoutVip, R.layout.item_checkout_vip, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    boolean displayIcon = !EmptyUtils.isEmpty(item.icon);
                    helper.setVisible(R.id.iv_icon, displayIcon);
                    if (displayIcon) {
                        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, item.icon), R.color.color_place);
                    }
                    TextView tvTitle = helper.getView(R.id.tv_title);
                    TextView tvPoints = helper.getView(R.id.tv_points);
                    ViewTools.applyTextStyle(tvTitle, finalI == 0 ? R.style.style_fluid_root_heading_sm : R.style.style_fluid_root_utility_sm_subdued);
                    ViewTools.applyTextStyle(tvPoints, finalI == 0 ? R.style.style_fluid_root_heading_sm : R.style.style_fluid_root_utility_sm_subdued);
                    tvTitle.setText(item.reward_type_desc);
                    tvPoints.setText(item.reward_type_points);
                }
            }), params);
        }
    }

    private void fillTermsItem(LinearLayout container, int itemIndex, String text, boolean isHtml, View.OnClickListener listener) {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        if (itemIndex == 0) {
            params.topMargin = CommonTools.dp2px(20);
        } else {
            params.topMargin = CommonTools.dp2px(8);
        }
        container.addView(ViewTools.getHelperView(mContext, R.layout.item_checkout_terms, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                if (isHtml) {
                    ViewTools.setViewHtml(helper.getView(R.id.tv_terms), text);
                } else {
                    helper.setText(R.id.tv_terms, text);
                }
                if (listener != null) {
                    helper.setOnClickListener(R.id.tv_terms, listener);
                }
            }
        }), params);
    }

    private void clearFocus(View view) {
        if (view != null) {
            view.clearFocus();
        }
    }

    private OnViewCallback callback;

    public void setImpressionTracker(EagleImpressionTrackerIml iml) {
        ISectionProvider provider = getItemProvider(CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS);
        if (provider instanceof CheckOutPointsProvider) {
            ((CheckOutPointsProvider) provider).setImpressionTracker(iml);
        }
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                List<ImpressionBean> event = getEagleImpressionEvent(getItem(start), start);
                if (!EmptyUtils.isEmpty(event)) {
                    list.addAll(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    List<ImpressionBean> event = getEagleImpressionEvent(getItem(i), i);
                    if (!EmptyUtils.isEmpty(event)) {
                        list.addAll(event);
                    }
                }
            }
        }
        return list;
    }

    private List<ImpressionBean> getEagleImpressionEvent(AdapterDataType item, int pos) {
        List<ImpressionBean> list = new ArrayList<>();
        if (item != null) {
            if (item instanceof CheckoutDeliveryData) {
                PreCheckoutAddressInfoBean addressInfo = ((CheckoutDeliveryData) item).addressInfoDTO;
                if (addressInfo != null) {
                    boolean lackOfAddress = !EmptyUtils.isEmpty(addressInfo.addr_firstname)
                            && !EmptyUtils.isEmpty(addressInfo.addr_lastname)
                            && !EmptyUtils.isEmpty(addressInfo.phone);
                    Map<String, Object> content = new TrackParams()
                            .put("address", lackOfAddress).get();
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.DELIVERY_INFO)
                            .addContent(content)
                            .build().getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, EagleTrackEvent.ModNm.DELIVERY_INFO));
                }
            } else if (item instanceof CheckoutPaymentData) {
                PreCheckoutV2Bean.PaymentInfoBean bean = ((CheckoutPaymentData) item).paymentInfoDTO;
                if (bean != null) {
                    Map<String, Object> content = new ArrayMap<>();
                    content.put("payment_nm", bean.payment_category);
                    if (((CheckoutPaymentData) item).isCheckCvv()) {
                        content.put("enter_cvc_number", true);
                    }
                    PreCheckoutPointInfoBean pointInfo = ((CheckoutPaymentData) item).pointInfoDTO;
                    if (pointInfo != null && pointInfo.points_current > 0) {
                        content.put("use_weee_points", ((CheckoutPaymentData) item).checked ? "select" : "unselect");
                    }
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.PAYMENT_METHOD)
                            .addContent(content)
                            .build().getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, EagleTrackEvent.ModNm.PAYMENT_METHOD));
                }
            } else if (item instanceof CheckoutPurchaseChannelData) {
                list.addAll(getPurchaseChannelImpressionEvent());
            } else if (item instanceof CheckoutCouponData) {
                PreCheckoutV2Bean.CouponInfoBean bean = ((CheckoutCouponData) item).couponInfoBean;
                Map<String, Object> content = null;
                if (bean != null) {
                    content = new TrackParams()
                            .put("coupon_info", bean.title).get();
                }
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(EagleTrackEvent.ModNm.APPLY_COUPON)
                        .addContent(content)
                        .build().getParams();
                list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, EagleTrackEvent.ModNm.APPLY_COUPON));

            } else if (item instanceof CheckoutOrderSummaryData) {
                PreCheckoutFeeInfoBean bean = ((CheckoutOrderSummaryData) item).feeInfoBean;
                if (bean != null) {
                    Map<String, Object> content = new ArrayMap<>();
                    content.put("subtotal", bean.sub_total_price);
                    content.put("coupon", bean.coupon_discount);
                    content.put("taxes", bean.tax);
                    content.put("delivery", bean.shipping_fee);
                    content.put("tip", bean.tip);
                    content.put("total", bean.total_price_with_activity);
                    content.put("weee_point", bean.points_price);
                    content.put("final_amount", bean.final_amount);
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.PURCHASE_SUMMARY)
                            .setMod_pos(5)
                            .setSec_pos(0)
                            .addContent(content)
                            .build().getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, EagleTrackEvent.ModNm.PURCHASE_SUMMARY));
                }
                OrderSummaryBean orderSummaryBean = ((CheckoutOrderSummaryData) item).orderSummaryBean;
                //banner imp
                if (orderSummaryBean != null && orderSummaryBean.loyalty_vip_saving != null) {
                    String key = "loyalty_vip_saving";
                    Map<String, Object> paramsBanner = new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.PURCHASE_SUMMARY)
                            .setBanner_key(key)
                            .setBanner_type(EagleTrackEvent.BannerType.MESSAGE)
                            .build().getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, paramsBanner, EagleTrackEvent.ModNm.PURCHASE_SUMMARY + key));
                }
            } else if (item instanceof CheckoutReviewOrderData) {
                PreCheckoutOrderReviewsBean bean = ((CheckoutReviewOrderData) item).orderReviewsDTO;
                if (bean != null) {
                    Map<String, Object> content = new ArrayMap<>();
                    if (!EmptyUtils.isEmpty(bean.fee_info)) {
                        content.put("sub_total", bean.fee_info.sub_total_price);
                        content.put("total_price_with_activity", bean.fee_info.total_price_with_activity);
                    }
                    if (!EmptyUtils.isEmpty(bean.shipping_info)) {
                        content.put("orignal_shipping_fee", bean.shipping_info.orignal_shipping_fee);
                        content.put("delivery_pickup_date", bean.shipping_info.delivery_pickup_date);
                        content.put("delivery_mode", bean.shipping_info.delivery_mode);
                        content.put("shipping_fee", bean.shipping_info.shipping_fee);
                    }
                    content.put("quantity", bean.quantity);
                    content.put("type", bean.type);
                    content.put("deal_id", bean.deal_id);
                    if (!EmptyUtils.isEmpty(bean.order_lines)) {
                        List<CheckOutTrackSummaryBean> trackList = new ArrayList<>();
                        for (PreCheckoutOrderReviewsBean.OrderLinesBean orderLinesBean : bean.order_lines) {
                            CheckOutTrackSummaryBean trackSummaryBean = new CheckOutTrackSummaryBean();
                            trackSummaryBean.base_price = orderLinesBean.base_price;
                            trackSummaryBean.price = orderLinesBean.price;
                            trackSummaryBean.product_id = orderLinesBean.product_id;
                            trackSummaryBean.quantity = orderLinesBean.quantity;
                            trackSummaryBean.source = orderLinesBean.source;
                            trackList.add(trackSummaryBean);
                        }
                        content.put("items", trackList);
                    }
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.CART)
                            .setMod_pos(0)
                            .setSec_nm(bean.type)
                            .setSec_pos(0)
                            .addContent(content)
                            .build()
                            .getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.CART_IMP, params, EagleTrackEvent.ModNm.CART + ((CheckoutReviewOrderData) item).orderReviewsDTO.deal_id));
                }
            } else if (item instanceof CheckOutPointsData || item instanceof DealPayMemberPlanData) {
                ISectionProvider provider = getItemProvider(item.getType());
                if (provider instanceof ImpressionProvider) {
                    List data = ((ImpressionProvider) provider).fetchImpressionData(item, pos);
                    list.addAll(data);
                }
            }
        }
        return list;
    }

    private List<ImpressionBean> getPurchaseChannelImpressionEvent() {
        List<ImpressionBean> list = new ArrayList<>();
        AdapterDataType purchaseChannelObj = CollectionUtils.firstOrNull(
                getData(),
                it -> it.getType() == CheckOutSectionAdapter.TYPE_PURCHASE_CHANNEL
        );
        CheckoutPurchaseChannelData purchaseChannelData = null;
        if (purchaseChannelObj instanceof CheckoutPurchaseChannelData) {
            purchaseChannelData = (CheckoutPurchaseChannelData) purchaseChannelObj;
        }

        AdapterDataType pointsObj = CollectionUtils.firstOrNull(
                getData(),
                it -> it.getType() == CheckOutSectionAdapter.TYPE_USE_POINTS
        );
        CheckoutUsePointsData usePointsData = null;
        if (pointsObj instanceof CheckoutUsePointsData) {
            usePointsData = (CheckoutUsePointsData) pointsObj;
        }

        if (purchaseChannelData != null) {
            PurchaseChannelExtraData extraData = purchaseChannelData.getExtraData();
            Map<String, Object> content = new ArrayMap<>();
            content.put("payment_nm", extraData.mainChannelCode);
            content.put("enter_cvc_number", extraData.isCheckCvv);
            content.put("ebt_payment", extraData.hasEbtChannel);
            if (usePointsData != null) {
                content.put("use_weee_points", usePointsData.checked ? "select" : "unselect");
            } else {
                content.put("use_weee_points", "unselect");
            }
            Map<String, Object> params = new EagleTrackModel.Builder()
                    .setMod_nm(EagleTrackEvent.ModNm.PAYMENT_METHOD)
                    .addContent(content)
                    .build()
                    .getParams();
            list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, EagleTrackEvent.ModNm.PAYMENT_METHOD));
        }
        return list;
    }

    private void showBottomDialog(@Nullable final OrderSummaryIncludeFeeBean bean) {
        if (bean != null) {
            new OrderSummaryIncludeFeeDialog(mContext).setData(bean).show();
        }
    }

    public void updateCoupon(CouponBean couponBean) {
        for (int i = 0; i < getData().size(); i++) {
            AdapterDataType adapterDataType = getData().get(i);
            if (adapterDataType instanceof CheckoutCouponData) {
                CheckoutCouponData data = (CheckoutCouponData) adapterDataType;
                data.setCouponSize(!EmptyUtils.isEmpty(couponBean.coupons_valid) ? couponBean.coupons_valid.size() : 0);
                notifyItemChanged(i, data);
                return;
            }
        }
    }

    public void quickUpdateCoupon(PreCheckoutV2Bean.CouponInfoBean couponInfoBean) {
        for (int i = 0; i < getData().size(); i++) {
            AdapterDataType adapterDataType = getData().get(i);
            if (adapterDataType instanceof CheckoutCouponData) {
                CheckoutCouponData data = (CheckoutCouponData) adapterDataType;
                data.couponInfoBean = couponInfoBean;
                notifyItemChanged(i, data);
                return;
            }
        }
    }

    public void updatePointsPosition(int position) {
        for (int i = 0; i < getData().size(); i++) {
            AdapterDataType adapterDataType = getData().get(i);
            if (adapterDataType instanceof CheckOutPointsData) {
                CheckOutPointsData data = (CheckOutPointsData) adapterDataType;
                if (data.t.get(position).selected) {
                    for (int j = 0; j < data.t.size(); j++) {
                        PreCheckoutV2Bean.MemberUpgradePlansBean item = data.t.get(j);
                        if (j != position) {
                            item.selected = false;
                        }
                    }
                }
                notifyItemChanged(i, data);
                return;
            }
        }
    }

    public void updateOtherTipStatus() {
        for (int i = 0; i < getData().size(); i++) {
            AdapterDataType item = getData().get(i);
            if (item instanceof CheckoutTipData) {
                notifyItemChanged(i, true);
                return;
            }
        }
    }

    public void setOnCheckOutPointsListener(CheckOutPointsProvider.OnCheckoutPointsListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS);
        if (provider instanceof CheckOutPointsProvider) {
            ((CheckOutPointsProvider) provider).setOnCheckoutPointsListener(listener);
        }
    }

    public void setOnDeliveryWindowActionListener(CheckoutDeliveryWindowAdapter.OnDeliveryWindowActionListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST);
        if (provider instanceof CheckoutOrderItemProvider) {
            ((CheckoutOrderItemProvider) provider).setOnDeliveryWindowActionListener(listener);
        }
    }

    public void setOnPurchaseChannelActionListener(OnPurchaseChannelActionListener listener) {
        ISectionProvider<?, ?> provider;
        provider = getItemProvider(CheckOutSectionAdapter.TYPE_PURCHASE_CHANNEL);
        if (provider instanceof CheckoutPurchaseChannelProvider) {
            ((CheckoutPurchaseChannelProvider) provider).setOnPurchaseChannelActionListener(listener);
        }
        provider = getItemProvider(CheckOutSectionAdapter.TYPE_USE_POINTS);
        if (provider instanceof CheckoutUsePointsProvider) {
            ((CheckoutUsePointsProvider) provider).setOnPurchaseChannelActionListener(listener);
        }
    }

    public interface OnViewCallback {
        void onEditTextFocusChanged(View v);

        void updateTipsData(PreCheckoutTipInfoBean.OptionsBean bean);

        void updatePoints(boolean checked);

        void onCvcEditTextNoFocus(String cvcText, boolean isCvcInvalid);
    }

    public CheckOutSectionAdapter setOnViewChangedCallback(OnViewCallback callback) {
        this.callback = callback;
        return this;
    }

    @Nullable
    public String getSelectedWindowIds() {
        List<Integer> windowIds = new ArrayList<>();
        CollectionUtils.forEach(getData(), it -> {
            if (it instanceof CheckoutReviewOrderData) {
                windowIds.addAll(((CheckoutReviewOrderData) it).getSelectedWindowIds());
            }
        });
        return CollectionUtils.join(windowIds, ",");
    }
}
