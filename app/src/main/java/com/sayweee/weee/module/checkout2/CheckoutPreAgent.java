package com.sayweee.weee.module.checkout2;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.service.impl.payment.bean.ClientPaymentInfo;
import com.sayweee.service.payment.PaymentKt;
import com.sayweee.service.payment.bean.PaymentChannel;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.checkout.bean.PreCheckoutTipInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout2.data.EbtAmountArgs;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelExtraData;
import com.sayweee.weee.module.checkout2.pm.PmHelper;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;

public final class CheckoutPreAgent {

    CheckoutPreAgent() {

    }

    private PreCheckoutV2Bean data;

    // customer properties
    String planId;
    double planPrice = 0;
    PreCheckoutTipInfoBean.OptionsBean optionsBean;
    String selectedWindowIds;
    String cvcText;
    private String ebtUserAmount;
    private String ebtBalance;
    private Boolean customerUsePoints; // store temp status when api not return

    // checkout pre properties
    String cartDomain;
    String[] cartIds;
    private boolean usePoints;
    String checkoutPreId;
    String checkoutAmount;
    String ebtAmount;
    String ebtMaxAmount;
    double totalPlanTip = 0;
    private List<PaymentChannel> purchaseChannels;
    boolean isAvailableEbt;

    // card tokenize result for pay payment
    ClientPaymentInfo cardPaymentInfo;

    void wrap(PreCheckoutV2Bean data) {
        this.data = data;
        clearPreviousProperties();
        if (data != null) {
            fillCheckoutPreProperties(data);
        }
    }

    private void clearPreviousProperties() {
        customerUsePoints = null;
        usePoints = false;
        optionsBean = null;
        purchaseChannels = null;
        totalPlanTip = 0;
        checkoutAmount = null;
        ebtAmount = null;
        ebtMaxAmount = null;
    }

    private void fillCheckoutPreProperties(@NonNull PreCheckoutV2Bean data) {
        checkoutPreId = data.checkout_pre_id;
        purchaseChannels = CollectionUtils.filter(
                data.purchase_channels,
                c -> PmHelper.isChannelCodeSupports(c.getChannelCode())
        );
        if (data.tipIsNotNull()) {
            PreCheckoutTipInfoBean tipBean = data.tip_info;
            optionsBean = tipBean.options.get(tipBean.selected_option_index);
        }
        if (data.fee_info != null) {
            checkoutAmount = data.fee_info.final_amount;
            ebtMaxAmount = data.fee_info.ebt_amount;
        }
        if (data.point_info != null) {
            usePoints = data.point_info.points_price > 0;
        }
        PaymentChannel ebtChannel = getEbtChannel();
        if (ebtChannel != null) {
            ebtAmount = ebtChannel.getChannelAmount();
        }
        isAvailableEbt = data.is_available_ebt;
    }

    @Nullable
    public PreCheckoutV2Bean getData() {
        return data;
    }

    RequestParams prepareCheckoutPreRequest() {
        return new RequestParams()
                .put("cart_domain", cartDomain)
                .putNonNull("tip_option", optionsBean)
                .putNonNull("plan_id", getPlanId())
                .putNonNull("referral_id", AccountManager.get().getReferralId()) //跟卖信息， 如果是从跟卖跳转的要带这个参数
                .putNonNull("delivery_window_id", selectedWindowIds)
                .putNonNull("ebt_amount", ebtUserAmount);
    }

    RequestParams prepareCheckoutRequest() {
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", cartDomain)
                .putNonNull("checkoutAmount", checkoutAmount)
                .putNonNull("checkout_pre_id", checkoutPreId)
                .putNonNull("tip_info", optionsBean)
                .putNonNull("referral_id", AccountManager.get().getReferralId())
                .putNonNull("plan_id", planId)
                .putNonNull("delivery_window_id", selectedWindowIds);
        if (purchaseChannels != null) {
            requestParams.putNonNull("purchase_channels", new ArrayList<>(purchaseChannels));
        }
        return requestParams;
    }

    @Nullable
    private String getPlanId() {
        double tip = optionsBean != null ? optionsBean.tip : 0;
        return tip + totalPlanTip > planPrice ? null : planId;
    }

    public void updatePlan(String planId, double planPrice) {
        this.planId = planId;
        this.planPrice = planPrice;
    }

    public void updateOptions(PreCheckoutTipInfoBean.OptionsBean bean) {
        this.optionsBean = bean;
    }

    public void updateDeliveryWindowIds(String selectedWindowIds) {
        this.selectedWindowIds = selectedWindowIds;
    }

    public boolean isPointsAllDeducted() {
        PreCheckoutV2Bean preCheckoutV2Bean = data;
        if (preCheckoutV2Bean == null) {
            return false;
        }
        return usePoints && !preCheckoutV2Bean.hasFinalAmount();
    }

    @Nullable
    public PaymentChannel getCardChannel() {
        return CollectionUtils.firstOrNull(purchaseChannels, c -> PaymentKt.isPayChannelCodeCard(c.getChannelCode()));
    }

    @Nullable
    public PaymentChannel getEbtChannel() {
        return CollectionUtils.firstOrNull(purchaseChannels, c -> PaymentKt.isPayChannelCodeEbt(c.getChannelCode()));
    }

    public ClientPaymentInfo getCardTokenizeResult() {
        return cardPaymentInfo;
    }

    public void setCardTokenizeResult(ClientPaymentInfo cardPaymentInfo) {
        this.cardPaymentInfo = cardPaymentInfo;
    }

    public PurchaseChannelExtraData getPurchaseChannelExtraData() {
        List<PaymentChannel> supportChannels = getSupportChannels();
        PurchaseChannelExtraData extraData = new PurchaseChannelExtraData();
        // flags
        updatePurchaseChannelExtraData(extraData, supportChannels);
        // error
        // 有 EBT 支付，但没有主要支付方式
        validateEmptyMainPurchaseChannel(extraData, supportChannels);
        return extraData;
    }

    public PurchaseChannelExtraData getPurchaseChannelExtraDataOnPlaceOrder() {
        List<PaymentChannel> supportChannels = getSupportChannels();
        PurchaseChannelExtraData extraData = new PurchaseChannelExtraData();
        // flags
        updatePurchaseChannelExtraData(extraData, supportChannels);
        // error
        // 支付方式为空
        validateEmptyPurchaseChannel(extraData, supportChannels);
        // 有 EBT 支付，但没有主要支付方式
        validateEmptyMainPurchaseChannel(extraData, supportChannels);
        // 有卡支付且需要检查 CVC，但检查失败
        validateCardCvv(extraData, supportChannels);
        return extraData;
    }

    List<PaymentChannel> getSupportChannels() {
        return CollectionUtils.orEmptyList(purchaseChannels);
    }

    private void updatePurchaseChannelExtraData(PurchaseChannelExtraData extraData, List<PaymentChannel> supportChannels) {
        // isPointsAllDeducted
        extraData.isPointsAllDeducted = isPointsAllDeducted();

        // hasEbtChannel
        PaymentChannel ebtChannel = CollectionUtils.firstOrNull(
                supportChannels,
                c -> PaymentKt.isPayChannelCodeEbt(c.getChannelCode())
        );
        extraData.hasEbtChannel = ebtChannel != null;

        // mainChannelCode
        PaymentChannel mainChannel = CollectionUtils.firstOrNull(
                supportChannels,
                c -> !PaymentKt.isPayChannelCodeEbt(c.getChannelCode())
        );
        extraData.mainChannelCode = mainChannel != null ? mainChannel.getChannelCode() : null;

        // isCheckCvv
        PaymentChannel cardChannel = CollectionUtils.firstOrNull(
                supportChannels,
                c -> PaymentKt.isPayChannelCodeCard(c.getChannelCode())
        );
        extraData.isCheckCvv = isCheckCvv(cardChannel);

        // isChannelSelectable
        extraData.isChannelSelectable = isPaymentMethodSelectable();
    }

    private void validateEmptyPurchaseChannel(PurchaseChannelExtraData extraData, List<PaymentChannel> supportChannels) {
        extraData.emptyPurchaseChannel = supportChannels.isEmpty();
    }

    private void validateEmptyMainPurchaseChannel(PurchaseChannelExtraData extraData, List<PaymentChannel> supportChannels) {
        PaymentChannel ebtChannel = CollectionUtils.firstOrNull(
                supportChannels,
                c -> PaymentKt.isPayChannelCodeEbt(c.getChannelCode())
        );
        if (ebtChannel != null) {
            extraData.emptyMainPurchaseChannel = supportChannels.size() == 1;
        } else {
            extraData.emptyMainPurchaseChannel = false;
        }
    }

    private void validateCardCvv(PurchaseChannelExtraData extraData, List<PaymentChannel> supportChannels) {
        PaymentChannel cardChannel = CollectionUtils.firstOrNull(
                supportChannels,
                c -> PaymentKt.isPayChannelCodeCard(c.getChannelCode())
        );
        if (isCheckCvv(cardChannel)) {
            extraData.invalidCardCvv = isInvalidCvc(cvcText);
        } else {
            extraData.invalidCardCvv = false;
        }
    }

    public List<String> getPurchaseChannelCodes() {
        return CollectionUtils.map(
                purchaseChannels,
                PaymentChannel::getChannelCode
        );
    }

    public RequestParams generateChangePaymentCategoryParams(boolean usePoints, boolean removeEbt) {
        RequestParams params = new RequestParams();
        List<String> channelCodes = new ArrayList<>();
        List<PaymentChannel> mainChannels = CollectionUtils.filter(
                purchaseChannels,
                c -> !PaymentKt.isPayChannelCodeEbt(c.getChannelCode())
        );
        for (PaymentChannel channel : mainChannels) {
            channelCodes.add(channel.getChannelCode());
            if (PaymentKt.isPayChannelCodeCard(channel.getChannelCode())) {
                params.putNonNull("profile_id", channel.getProfileId());
            }
        }
        if (!removeEbt) {
            PaymentChannel ebtChannel = CollectionUtils.firstOrNull(
                    purchaseChannels,
                    c -> PaymentKt.isPayChannelCodeEbt(c.getChannelCode())
            );
            if (ebtChannel != null) {
                channelCodes.add(ebtChannel.getChannelCode());
                params.putNonNull("ebt_profile_id", ebtChannel.getProfileId());
            }
        }
        params.putNonNull("payment_category", CollectionUtils.join(channelCodes, ","));
        params.putNonNull("points", usePoints);
        return params;
    }

    public static boolean isCheckCvv(@Nullable PaymentChannel channel) {
        if (channel == null) return false;
        return PaymentKt.isPayChannelCodeCard(channel.getChannelCode()) // 是信用卡或借记卡
                && channel.isCheckCvv() // 是否需要检查
                && DecimalTools.compare(channel.getChannelAmount(), "0") > 0 // 需要支付的金额 > 0
                ;
    }

    public static boolean isInvalidCvc(String cvc) {
        return cvc == null || cvc.length() < 3;
    }

    public void setEbtUserAmount(@Nullable String ebtUserAmount) {
        this.ebtUserAmount = ebtUserAmount;
    }

    public void setEbtBalance(@Nullable String ebtBalance) {
        this.ebtBalance = ebtBalance;
    }

    public void setCustomerUsePoints(boolean userUsePoints) {
        this.customerUsePoints = userUsePoints;
    }

    public boolean isUsePoints() {
        if (customerUsePoints != null) {
            return customerUsePoints;
        }
        return usePoints;
    }

    @NonNull
    public EbtAmountArgs getEbtAmountArgs() {
        EbtAmountArgs.Builder builder = new EbtAmountArgs.Builder()
                .userAmount(ebtUserAmount)
                .channelAmount(ebtAmount)
                .maxAmount(ebtMaxAmount)
                .balance(ebtBalance);
        return builder.build();
    }

    public boolean isAllCommissionPartner() {
        PreCheckoutV2Bean preCheckoutV2Bean = data;
        if (preCheckoutV2Bean == null) {
            return false;
        }

        // If all order reviews's "sub_order_type" are "commission_partner",
        // then payment method is unselectable, and contains seller alcohol
        return CollectionUtils.all(
                preCheckoutV2Bean.order_reviews,
                review -> Constants.OrderSubType.COMMISSION_PARTNER.equals(review.sub_order_type)
        );
    }

    public boolean isPaymentMethodSelectable() {
        return !isAllCommissionPartner();
    }

    public int getAlcoholAgreementType() {
        if (isAllCommissionPartner()) {
            return Constants.AlcoholAgreementType.SELLER;
        }
        return data != null && data.contain_alcohol
                ? Constants.AlcoholAgreementType.GROCERY
                : Constants.AlcoholAgreementType.NONE;
    }
}
