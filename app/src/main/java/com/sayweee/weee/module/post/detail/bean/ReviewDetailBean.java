package com.sayweee.weee.module.post.detail.bean;

import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.post.bean.ExternalTagsBean;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  winds
 * Date:    2021/7/4.
 * Desc:
 */
public class ReviewDetailBean implements Serializable {
    /**
     * id : 843344
     * user_id : 47081
     * user_name : Manya
     * user_avatar : null
     * like_count : 19
     * like_count_label : 19
     * is_set_like : true
     * comments_count : 0
     * featured : 1
     * product_image_url : https://cdn01.sayweee.net/2020-10/lBEAv-SfQpaSC8z5v4hong.jpg
     * product_title : 冷冻五花肉
     * pictures : ["https://cdn01.sayweee.net/2021-05/LCg2I4pzTnSQ3D6YutE0dQ.jpg","https://cdn01.sayweee.net/2021-05/D5Ha2B7YQxCZqTOkItYpWw.jpg","https://cdn01.sayweee.net/2021-05/WwDnx3VjT7-s1juN9CLiPQ.jpg","https://cdn01.sayweee.net/2021-05/RlpVabMOSVWpA88gcBY_Dg.jpg"]
     * resize_url : https://cdn01.sayweee.net/2021-05/LCg2I4pzTnSQ3D6YutE0dQ-320x239.jpg
     * size_rate : 0.75
     * product_id : 53730
     * rec_create_time : 1621321153
     * comment : null
     * comment_lang : 我全是用它做出来的！！这个五花肉是我的最爱～各种做法最出来都好吃到爆！肥瘦相间，品质非常高！！买它买它买它！！会无限回购！！
     * category_num : meat
     * is_bundle : null
     * is_translated : false
     * quantity : 26
     * privilege : false
     * product : {"activity_tag_list":[],"base_price":15.99,"bought_times":null,"brand_name":null,"category":"meat01","category_color":"#F26F6F","category_name":"肉类","discount_percentage":10,"feature":2,"history_purchase":[],"id":53730,"img":"https://cdn01.sayweee.net/2020-10/lBEAv-SfQpaSC8z5v4hong-square-160.jpg","img_urls":["https://cdn01.sayweee.net/2020-10/lBEAv-SfQpaSC8z5v4hong-square-320.jpg"],"is_hotdish":"N","vender_id":"574","is_limit_product":false,"label_list":[{"label_name":"10% Off","label_color":"#DF2C2E","label_position":"price"}],"label_color":null,"label_name":null,"label_position":null,"last_week_sold_count":1001,"last_week_sold_count_ui":"1K+","max_order_quantity":381,"member_price":14.39,"min_order_quantity":1,"name":"冷冻五花肉 3 磅","one_day_sold_count":null,"parent_category":"meat","price":14.39,"product_max_order_quantity":null,"product_sales_feature":"2","product_tags":null,"remaining_count":381,"show_member_price":false,"slug":"Pork-Belly-with-Skin--Frozen/53730","sold_count":191,"sold_status":"available","square_img_url":"https://cdn01.sayweee.net/2020-10/lBEAv-SfQpaSC8z5v4hong-square-320.jpg","sub_name":"肥瘦相间 肉质香嫩","tag_list":["meat01","meat"],"unit_price":"$4.79/磅","unit_range":"3磅","view_link":"http://tb1.sayweee.net/zh/product/Pork-Belly-with-Skin--Frozen-1/53730","is_pantry":false,"unit":null,"unit_min":null,"unit_max":null,"image_url":null,"currency":null,"show_reminder":false,"reminder_set":false,"quantity":0,"is_show_member_price":null,"parent_catalogue_num":null}
     * social_status : followed / unfollow
     */

    public int id;
    public int user_id;
    public String uid;
    public String user_name;
    public String user_avatar;
    public String status;
    public int like_count;
    public String like_count_label;
    public boolean is_set_like;
    public int comments_count;
    public int featured;
    public String product_image_url;
    public String product_title;
    public String resize_url;
    public double size_rate;
    public int product_id;
    public int rec_create_time;
    public String comment;
    public String comment_lang;
    public String title;
    public String title_lang;
    public String category_num;
    public String is_bundle;
    public boolean is_translated;
    public int quantity;
    public boolean privilege;
    public ProductBean product;
    public List<String> pictures;
    public String social_status = "Followed"; //unfollow
    public String origin_lang; //unfollow
    public String user_badge;
    public int show_translate;
    public int rating;
    public List<ExternalTagsBean> external_tags;


    public boolean isFollowing() {
        return "Followed".equalsIgnoreCase(social_status);
    }

    public boolean isPublished() {
        return "P".equals(status);
    }

    public boolean isF() {
        return "F".equals(status);
    }

    public boolean shareVisible() {
        return isPublished() || isF();
    }

    protected boolean hasToggled; //是否切换过文案展示

    public void toggleTranslateStatus() {
        this.hasToggled = !hasToggled;
    }

    public boolean useOrigin() {
        return (show_translate == 1 && hasToggled) || (show_translate == 2 && !hasToggled);
    }

    public boolean hasToggled() {
        return hasToggled;
    }

    public boolean showTranslatePortal() {
        return show_translate != 0;
    }
}
