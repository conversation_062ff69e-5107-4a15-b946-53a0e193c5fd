package com.sayweee.weee.module.category.service;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.tools.SecurityUtils;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cate.adapter.FilterListAdapter;
import com.sayweee.weee.module.cate.product.bean.RelatedBean;
import com.sayweee.weee.module.category.bean.CategoryBannerV2Bean;
import com.sayweee.weee.module.category.bean.CategoryCarouselBean;
import com.sayweee.weee.module.category.bean.CategoryCarouselData;
import com.sayweee.weee.module.category.bean.CategoryEndData;
import com.sayweee.weee.module.category.bean.CategoryFilterData;
import com.sayweee.weee.module.category.bean.CategoryPagerBean;
import com.sayweee.weee.module.category.bean.CategoryPagerBundle;
import com.sayweee.weee.module.category.bean.ProductFilterBean;
import com.sayweee.weee.module.category.bean.ProductPropertyValueBean;
import com.sayweee.weee.module.category.bean.ProductSortBean;
import com.sayweee.weee.module.category.bean.RelatedData;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import io.reactivex.Observable;

public class CategoryPagerViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public MutableLiveData<List<CategoryPagerBean.Categories>> indicatorData = new MutableLiveData<>();//副分类

    public MutableLiveData<Integer> indexByUrlData = new MutableLiveData<>();

    public MutableLiveData<CategoryFilterData> filterData = new MutableLiveData<>();//filter dialog

    public MutableLiveData<Integer> filterQtyData = new MutableLiveData<>();//filter 按钮qty

    public MutableLiveData<List<AdapterDataType>> adapterData = new MutableLiveData<>();

    public MutableLiveData<List<AdapterDataType>> adapterAppendData = new MutableLiveData<>();

    public MutableLiveData<Boolean> veilData = new MutableLiveData<>();

    public MutableLiveData<Boolean> nextPageExistData = new MutableLiveData<>();

    public MutableLiveData<RelatedData> relatedCardData = new MutableLiveData<>();

    public MutableLiveData<Boolean> errorData = new MutableLiveData<>();//getCategoryPagerData date null 主接口没有数据

    public CategoryPagerBundle categoryPagerBundle;
    Map<String, List<AdapterDataType>> dataCache = new ArrayMap<>();
    Map<String, CategoryFilterData> filterCache = new ArrayMap<>();
    Map<String, Integer> filterQtyCache = new ArrayMap<>();
    Map<String, Integer> pageCache = new ArrayMap<>();//加载更多分页缓存
    RelatedData relatedDataCache;
    public String currentCatalogueNum = ALL;
    public String sort;
    private String filters;
    private String outFilters;
    CategoryFilterData currentCategoryFilterData;
    CategoryViewModel categoryViewModel;
    static final String PRODUCT = "product";
    static final String CAROUSEL = "carousel";
    static final String COMMA = ",";//逗号，多选filter分隔符
    public static final String ALL = "all";
    public static final String KEY_CATALOGUE_NUM = "catalogue_num";
    public static final int REQUEST_TYPE_CATEGORY = 100;//切换大分类
    public static final int REQUEST_TYPE_CATALOGUE_NUM = 200;//切换副分类
    public static final int REQUEST_TYPE_FILTER = 300;//filter and sort
    public static final int REQUEST_TYPE_NOTIFIED_FILTER = 310;
    public static final int REQUEST_TYPE_REFRESH = 400;
    public static final int REQUEST_TYPE_LOAD_MORE = 500;
    private int productId;
    private String productKey;
    private Set<String> keysAppended = new HashSet<>();

    @IntDef({REQUEST_TYPE_CATEGORY, REQUEST_TYPE_CATALOGUE_NUM, REQUEST_TYPE_FILTER, REQUEST_TYPE_NOTIFIED_FILTER, REQUEST_TYPE_REFRESH, REQUEST_TYPE_LOAD_MORE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface RequestType {

    }

    public CategoryPagerViewModel(@NonNull Application application) {
        super(application);
    }

    public void injectParentFragment(Fragment parentFragment) {
        categoryViewModel = ViewModelProviders.of(parentFragment).get(CategoryViewModel.class);
    }

    public void injectCategoryPagerBundle(CategoryPagerBundle categoryPagerBundle) {
        this.categoryPagerBundle = categoryPagerBundle;
    }

    /**
     * url路由进入分类后，精确定位分类，副分类，排序，筛选
     */
    public void requestFilterByUrl(int indexByUrl, Map<String, String> filterByUrl, String sortByUrl) {
        clearCache();
        CategoryFilterData categoryFilterData = filterCache.get(ALL);
        Set<String> filterByUrlKeys = filterByUrl.keySet();
        if (categoryFilterData != null) {
            //sort
            for (ProductSortBean sortBean : categoryFilterData.sorts) {
                sortBean.selected = sortBean.sort_key.equalsIgnoreCase(sortByUrl);
            }
            //filter
            List<ProductFilterBean> filters = categoryFilterData.filters;
            for (ProductFilterBean filter : filters) {
                if (filterByUrlKeys.contains(filter.property_key)) {
                    String filterByUrlValue = filterByUrl.get(filter.property_key);
                    if ("true".equalsIgnoreCase(filterByUrlValue) || "false".equalsIgnoreCase(filterByUrlValue)) {
                        filter.property_values.get(0).selected = "true".equalsIgnoreCase(filterByUrlValue);
                    } else if (!EmptyUtils.isEmpty(filterByUrlValue)) {
                        String[] split = filterByUrlValue.split(COMMA);
                        for (String valueKeyByUrl : split) {
                            for (ProductPropertyValueBean property_value : filter.property_values) {
                                if (property_value.value_key.equalsIgnoreCase(valueKeyByUrl)) {
                                    property_value.selected = true;
                                }
                            }
                        }
                    }
                }
            }
            currentCategoryFilterData = categoryFilterData;//filter by url
            indexByUrlData.postValue(indexByUrl);
        }
    }

    /**
     * 点击副分类
     */
    public void requestCatalogueNum(String catalogueNum) {
        currentCatalogueNum = catalogueNum;
        List<AdapterDataType> list = dataCache.get(currentCatalogueNum);
        if (list != null) {
            //优先使用缓存
            adapterData.setValue(list);
            filterData.setValue(filterCache.get(currentCatalogueNum));
            filterQtyData.setValue(filterQtyCache.get(currentCatalogueNum));
        } else {
            getData(REQUEST_TYPE_CATALOGUE_NUM, catalogueNum);
        }
    }

    /**
     * 点击筛选show out indicator
     */
    public void requestFilterIndicator(CategoryFilterData categoryFilterData, ProductPropertyValueBean bean) {
        sync(categoryFilterData.outFilters, bean);
        sync(categoryFilterData.filters, bean);
        requestFilter(categoryFilterData);//点击filter show out indicator
    }

    private void sync(List<ProductFilterBean> filters, ProductPropertyValueBean bean) {
        if (CollectionUtils.isNotEmpty(filters)) {
            for (ProductFilterBean filter : filters) {
                if (filter.property_show_type.equals(FilterListAdapter.BOOLEAN)) {
                    if (filter.property_key.equals(bean.value_key) && filter.property_name.equals(bean.value_name)) {
                        filter.property_values.get(0).selected = bean.selected;
                        break;
                    }
                } else if ((filter.property_show_type.equals(FilterListAdapter.MULTIPLE) || filter.property_show_type.equals(FilterListAdapter.SINGLE))
                        && !EmptyUtils.isEmpty(filter.property_values)) {
                    if (filter.property_key.equalsIgnoreCase(bean.property_key)) {//外层key校验
                        for (ProductPropertyValueBean property_value : filter.property_values) {
                            if (property_value.value_key.equals(bean.value_key) && property_value.value_name.equals(bean.value_name)) {
                                property_value.selected = bean.selected;
                                //单选情况下，其余全部不选中
                                if (filter.property_show_type.equals(FilterListAdapter.SINGLE) && bean.selected) {
                                    for (ProductPropertyValueBean value : filter.property_values) {
                                        if (!value.value_key.equals(bean.value_key)) {
                                            value.selected = false;
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 点击筛选排序
     */
    public void requestFilter(CategoryFilterData categoryFilterData) {
        currentCategoryFilterData = categoryFilterData;//filter 用户手动
        clearCache();
        getData(REQUEST_TYPE_FILTER);//主动filter
    }

    public void requestSingleFilter(CategoryFilterData categoryFilterData, ProductFilterBean productFilterBean) {
        currentCategoryFilterData = categoryFilterData;
        syncSingleFilter(currentCategoryFilterData.outFilters, productFilterBean);
        syncSingleFilter(currentCategoryFilterData.filters, productFilterBean);
        clearCache();
        getData(REQUEST_TYPE_FILTER);//二级filter show out 模块主动filter
    }

    private void syncSingleFilter(List<ProductFilterBean> filters, ProductFilterBean productFilterBean) {
        if (CollectionUtils.isNotEmpty(filters)) {
            for (ProductFilterBean filter : filters) {
                if (filter.property_key.equals(productFilterBean.property_key)) {
                    filters.remove(filter);
                    break;
                }
            }
            filters.add(productFilterBean);
        }
    }

    /**
     * 公用sort&filter参数改变
     *
     * @param key ===>String key = categoryPagerBundle.current.key + ";" + currentCatalogueNum; !!!!!!!!
     */
    public void requestSync(String key) {
        if (categoryViewModel == null) {
            return;
        }
        if (currentCategoryFilterData != null) {
            //应用公用sort
            List<ProductSortBean> sorts = currentCategoryFilterData.sorts;
            if (!EmptyUtils.isEmpty(sorts)) {
                String sortKey = categoryViewModel.sort != null ? categoryViewModel.sort.sort_key : null;
                for (ProductSortBean sort : sorts) {
                    sort.selected = sort.sort_key.equalsIgnoreCase(sortKey);
                }
            }
            //删除当前大分类与公用filter的交集
            //接下来请求数据时会自动使用公用filter
            Set<String> shareableFilterPropertyKeys = categoryViewModel.shareablePart.keySet();
            List<ProductFilterBean> filters = currentCategoryFilterData.filters;
            Iterator<ProductFilterBean> iterator = filters.iterator();
            while (iterator.hasNext()) {
                ProductFilterBean filter = iterator.next();
                if (shareableFilterPropertyKeys.contains(filter.property_key) && filter.shareable) {
                    iterator.remove();
                }
            }
        }

        String[] split = key.split(";");
        String categoryPagerBundleCurrentKey = split[0];
        String catalogueNum = split[1];
        if (!categoryPagerBundle.current.key.equalsIgnoreCase(categoryPagerBundleCurrentKey)) {
            //不在当前分类
            clearCache();
            getData(REQUEST_TYPE_NOTIFIED_FILTER);//sync通知filter
        } else {
            //就在当前分类
            List<AdapterDataType> list = dataCache.get(catalogueNum);
            dataCache.clear();
            dataCache.put(catalogueNum, list);
            keysAppended.clear();
            if (list != null) {
                for (AdapterDataType adapterDataType : list) {
                    if (adapterDataType instanceof ProductItemData) {
                        keysAppended.add(getKey(catalogueNum, ((ProductItemData) adapterDataType).t));
                    }
                }
            }
        }
    }

    public void requestMore() {
        Integer page = pageCache.get(currentCatalogueNum);
        pageCache.put(currentCatalogueNum, page + 1);
        getData(REQUEST_TYPE_LOAD_MORE);
    }

    public void getData(@RequestType int type) {
        getData(type, null);
    }

    public void getData(@RequestType int type, String catalogueNum) {
        if (REQUEST_TYPE_REFRESH == type) {
            clearCache();
        }
        if (REQUEST_TYPE_CATALOGUE_NUM == type || REQUEST_TYPE_FILTER == type || REQUEST_TYPE_NOTIFIED_FILTER == type) {
            veilData.postValue(true);
        }
        if (REQUEST_TYPE_LOAD_MORE != type) {
            pageCache.put(currentCatalogueNum, 1);
        }
        Map<String, String> params = getRequestParams(type);
        //category页面数据
        Observable<ResponseBean<CategoryPagerBean>> getCategoryPagerData = getLoader()
                .getHttpService()
                .getCategoryPagerData(params)
                .compose(ResponseTransformer.scheduler(this));
        Map<String, String> filterMap = JsonUtils.parseObject(filters, Map.class);
        boolean noFilter;
        if (CollectionUtils.isNotEmpty(filterMap)) {
            noFilter = filterMap.containsKey(KEY_CATALOGUE_NUM) && filterMap.size() == 1;//仅有副分类
        } else {
            noFilter = true;
        }
        boolean noSort;
        if (TextUtils.isEmpty(sort)) {
            noSort = true;
        } else {
            noSort = categoryViewModel.sort == null || categoryViewModel.sort.sort_def;
        }
        boolean needRequestBanner = noFilter && noSort && REQUEST_TYPE_LOAD_MORE != type;//是否需要单独请求category top banner
        Observable<ResponseBean<?>> observable;
        if (needRequestBanner) {
            //category banner V2
            Observable<ResponseBean<CategoryBannerV2Bean>> getCategoryBannerV2 = getLoader()
                    .getHttpService()
                    .getCategoryBannerV2(getBannerRequestParams())
                    .compose(ResponseTransformer.scheduler(this));
            observable = Observable.mergeArrayDelayError(getCategoryBannerV2, getCategoryPagerData);
        } else {
            observable = Observable.mergeArrayDelayError(getCategoryPagerData);
        }
        observable.subscribe(new ResponseObserver<ResponseBean<?>>() {
            CategoryBannerV2Bean categoryBannerV2Bean;
            CategoryPagerBean data;

            @Override
            public void onResponse(ResponseBean<?> response) {
                Object responseData = response.getData();
                if (responseData instanceof CategoryBannerV2Bean) {
                    categoryBannerV2Bean = (CategoryBannerV2Bean) responseData;
                } else if (responseData instanceof CategoryPagerBean) {
                    data = (CategoryPagerBean) responseData;
                }

            }

            @Override
            public void onFinish() {
                if (data != null) {
                    switch (type) {
                        case REQUEST_TYPE_CATEGORY:
                            packIndicator(data);
                            packFilter(data);
                            filterQtyData.postValue(filterQtyCache.get(currentCatalogueNum));
                            break;
                        case REQUEST_TYPE_CATALOGUE_NUM:
                            int filterDot = 0;//1表示有筛选条件被选中,选中状态dot显示
                            if (CollectionUtils.isNotEmpty(data.sorts)) {
                                for (ProductSortBean sortBean : data.sorts) {
                                    if (sortBean.selected && !sortBean.sort_def) {
                                        filterDot = 1;
                                        break;
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(data.filters) && filterDot != 1) {
                                for (ProductFilterBean filter : data.filters) {
                                    if (filterDot == 1) {
                                        break;
                                    }
                                    if (CollectionUtils.isNotEmpty(filter.property_values)) {
                                        for (ProductPropertyValueBean propertyValue : filter.property_values) {
                                            if (propertyValue.selected) {
                                                filterDot = 1;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            filterQtyCache.put(currentCatalogueNum, filterDot);
                            filterQtyData.postValue(filterDot);
                            packFilter(data);
                            break;
                        case REQUEST_TYPE_FILTER:
                        case REQUEST_TYPE_NOTIFIED_FILTER:
                            packFilter(data);
                            break;
                        default:
                            break;
                    }
                    packData(categoryBannerV2Bean, data, type, catalogueNum);
                    WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.CATEGORY, String.valueOf(categoryViewModel.hashCode()));
                } else {
                    errorData.postValue(true);
                }
                veilData.postValue(false);
            }
        });
    }

    @NonNull
    private Map<String, String> getRequestParams(int type) {
        Map<String, String> params = new ArrayMap<>();
        params.put("date", OrderManager.get().getDeliveryPickupDate());
        params.put("filter_sub_category", categoryPagerBundle.current.key);
        filters = getFilters(type);
        if (!TextUtils.isEmpty(filters) && !"{}".equals(filters)) {
            params.put("filters", filters);
        }
        if (!TextUtils.isEmpty(outFilters) && !"{}".equals(outFilters)) {
            params.put("out_filters", outFilters);
        }
        params.put("lang", LanguageManager.get().getLanguage());
        params.put("page", String.valueOf(pageCache.get(currentCatalogueNum)));
        params.put("limit", "20");
        sort = categoryViewModel.sort != null ? categoryViewModel.sort.sort_key : null;
        if (!TextUtils.isEmpty(sort)) {
            params.put("sort", sort);
        }
        params.put("zipcode", OrderManager.get().getZipCode());
        String string = null;
        try {
            string = CommonTools.mapToQueryString(params);
        } catch (Exception e) {
            // e.printStackTrace();
        }
        String sign = null;
        try {
            sign = SecurityUtils.md5(string);
        } catch (NoSuchAlgorithmException e) {
            // e.printStackTrace();
        }
        params.put("sign", sign);
        return params;
    }


    /**
     * 生成当前sort&filter参数
     * 储存公用sort&filter参数
     * 同步公用sort&filter参数
     */
    private String getFilters(int type) {
        int filterQty = 0;
        Map<String, String> filterMap = new LinkedHashMap<>();
        //副分类
        if (!ALL.equalsIgnoreCase(currentCatalogueNum)) {
            filterMap.put(KEY_CATALOGUE_NUM, currentCatalogueNum);
        }
        //当前大分类下未生成可用的filter
        if (currentCategoryFilterData == null) {
            filterMap.putAll(categoryViewModel.shareablePart);
            if (categoryViewModel.sort != null && !categoryViewModel.sort.sort_def) {
                filterQty = filterQty + 1;
            }
            filterQty = calculateFilterMapQty(filterQty, categoryViewModel.shareablePart);
            if (type != REQUEST_TYPE_CATEGORY) {
                filterQtyData.postValue(filterQty);
            }
            filterQtyCache.put(currentCatalogueNum, filterQty);
            return JsonUtils.toJSONString(filterMap);
        }
        List<ProductSortBean> sorts = currentCategoryFilterData.sorts;
        boolean isNeedSync = false;
        //排序
        String sortOld = categoryViewModel.sort != null ? categoryViewModel.sort.sort_key : null;
        categoryViewModel.sort = null;//重置排序
        if (!EmptyUtils.isEmpty(sorts)) {
            for (ProductSortBean sortsBean : sorts) {
                if (sortsBean.selected) {
                    categoryViewModel.sort = sortsBean;
                    isNeedSync = !sortsBean.sort_key.equalsIgnoreCase(sortOld);//公用排序是否改变
                    if (!sortsBean.sort_def) {
                        filterQty = filterQty + 1;
                    }
                    break;
                }
            }
        }
        //筛选
        Map<String, String> shareablePartNew = new LinkedHashMap<>();//共享filter部分
        filterMap.putAll(categoryViewModel.shareablePart);
        List<ProductFilterBean> filters = currentCategoryFilterData.filters;
        extractFilterMap(filters, filterMap, shareablePartNew);

        //outFilters
        Map<String, String> outFilterMap = new LinkedHashMap<>();
        extractFilterMap(currentCategoryFilterData.outFilters, outFilterMap, null);
        outFilters = JsonUtils.toJSONString(outFilterMap);

        Map<String, String> temp = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : shareablePartNew.entrySet()) {
            if (!"false".equalsIgnoreCase(entry.getValue())) {
                temp.put(entry.getKey(), entry.getValue());
            }
        }
        if (!isNeedSync) {
            isNeedSync = !temp.equals(categoryViewModel.shareablePart);//公用筛选是否改变
        }
        categoryViewModel.shareablePart.putAll(shareablePartNew);
        //删除共享filter中的false value,修正出最终共享filter部分
        Iterator<Map.Entry<String, String>> iterator = categoryViewModel.shareablePart.entrySet().iterator();
        while (iterator.hasNext()) {
            if (iterator.next().getValue().equals("false")) {
                iterator.remove();
            }
        }
        //通知其他分类更新
        if (type == REQUEST_TYPE_FILTER && isNeedSync) {
            String key = categoryPagerBundle.current.key + ";" + currentCatalogueNum;
            categoryViewModel.syncData.postValue(key);
        }
        //计算filter按钮qty
        if (REQUEST_TYPE_LOAD_MORE != type) {
            filterQty = calculateFilterMapQty(filterQty, filterMap);
            filterQtyData.postValue(filterQty);
            filterQtyCache.put(currentCatalogueNum, filterQty);
        }
        return JsonUtils.toJSONString(filterMap);
    }

    private static void extractFilterMap(List<ProductFilterBean> filters, Map<String, String> filterMap, Map<String, String> shareablePartNew) {
        if (CollectionUtils.isNotEmpty(filters)) {
            for (ProductFilterBean filter : filters) {
                if (filter.property_show_type.equals(FilterListAdapter.BOOLEAN)) {
                    if (filter.property_values.get(0).selected) {
                        filterMap.put(filter.property_key, "true");
                    } else {
                        filterMap.remove(filter.property_key);
                    }
                    if (filter.shareable) {
                        CollectionUtils.put(shareablePartNew, filter.property_key, filter.property_values.get(0).selected ? "true" : "false");
                    }
                } else if (filter.property_show_type.equals(FilterListAdapter.MULTIPLE) || filter.property_show_type.equals(FilterListAdapter.SINGLE)) {
                    StringBuilder s = new StringBuilder();
                    for (ProductPropertyValueBean property_value : filter.property_values) {
                        if (property_value.selected) {
                            s.append(COMMA).append(property_value.value_key);
                        }
                    }
                    if (!TextUtils.isEmpty(s)) {
                        s.deleteCharAt(0);
                        filterMap.put(filter.property_key, s.toString());
                        if (filter.shareable) {
                            CollectionUtils.put(shareablePartNew, filter.property_key, s.toString());
                        }
                    } else {
                        filterMap.remove(filter.property_key);
                        if (filter.shareable) {
                            CollectionUtils.put(shareablePartNew, filter.property_key, "false");
                        }
                    }
                }
            }
        }
    }

    private int calculateFilterMapQty(int filterQty, Map<String, String> filterMap) {
        for (String key : filterMap.keySet()) {
            String value = filterMap.get(key);
            if (value != null && !KEY_CATALOGUE_NUM.equalsIgnoreCase(key)) {
                String[] split = value.split(COMMA);
                if (split.length == 0) {
                    filterQty = filterQty + 1;
                } else {
                    filterQty = filterQty + split.length;
                }
            }
        }
        return filterQty;
    }

    /**
     * 副分类indicator
     */
    private void packIndicator(CategoryPagerBean data) {
        CategoryPagerBean.Categories all = new CategoryPagerBean.Categories();
        all.catalogue_num = ALL;
        all.catalogue_name = LifecycleProvider.get().getTopActivity().getResources().getString(R.string.s_cate_all);
        all.selected = true;
        List<CategoryPagerBean.Categories> list = data.categories;
        if (!list.isEmpty()) {
            if (!ALL.equalsIgnoreCase(list.get(0).catalogue_num)) {
                list.add(0, all);
            }
            for (CategoryPagerBean.Categories category : list) {
                if (category.selected && !ALL.equalsIgnoreCase(category.catalogue_num)) {
                    all.selected = false;
                    break;
                }
            }
        }
        if (!EmptyUtils.isEmpty(list)) {
            indicatorData.postValue(list);
        }
    }

    /**
     * filter and sort dialog
     */
    private void packFilter(CategoryPagerBean data) {
        CategoryFilterData categoryFilterData = new CategoryFilterData(data.sorts, data.filters, data.out_filters);
        filterData.postValue(categoryFilterData);
        filterCache.put(currentCatalogueNum, categoryFilterData);//缓存各个副分类filter
    }

    private void packData(CategoryBannerV2Bean categoryBannerV2Bean, CategoryPagerBean data, int type, String catalogueNum) {
        List<AdapterDataType> list = new ArrayList<>();
        //bannerV2模块
        if (categoryBannerV2Bean != null && CollectionUtils.isNotEmpty(categoryBannerV2Bean.carouselInfoList)) {
            CategoryCarouselData categoryCarouselData = new CategoryCarouselData(categoryBannerV2Bean.carouselInfoList);
            categoryCarouselData.setBannerParams(categoryBannerV2Bean.autoplay, categoryBannerV2Bean.loop, categoryBannerV2Bean.loop_interval);
            list.add(categoryCarouselData);
        }
        int pos = !EmptyUtils.isEmpty(dataCache.get(currentCatalogueNum)) ? dataCache.get(currentCatalogueNum).size() : 0;
        String source = Constants.Source.CATEGORIES + categoryPagerBundle.current.key + "-" + currentCatalogueNum;
        if (!EmptyUtils.isEmpty(data.contents)) {
            for (CategoryPagerBean.Contents content : data.contents) {
                if (PRODUCT.equalsIgnoreCase(content.type)) {
                    ProductBean productBean = JsonUtils.parseObject(content.data, ProductBean.class);
                    if (productBean != null) {
                        String key = getKey(currentCatalogueNum, productBean);
                        if (keysAppended.add(key)) {
                            list.add(new ProductItemData(productBean)
                                    .setModNm(EagleTrackEvent.ModNm.ITEM_LIST)
                                    .setModPos(2)
                                    .setProductSource(source));//商品卡片模块
                        }
                    }
                } else if (CAROUSEL.equalsIgnoreCase(content.type)) {
                    List<CategoryCarouselBean> carouselBeanList = JsonUtils.parseArray(content.data, CategoryCarouselBean.class);
                    if (!EmptyUtils.isEmpty(carouselBeanList)) {
                        CategoryCarouselData categoryCarouselData = new CategoryCarouselData(carouselBeanList);
                        categoryCarouselData.setBannerParams(content.autoplay, content.loop, content.loop_interval);
                        list.add(categoryCarouselData);//banner模块
                    }
                }
            }
        }

        setProdPos(list, pos);//附上埋点prod pos
        if (!EmptyUtils.isEmpty(list) && ALL.equalsIgnoreCase(currentCatalogueNum) && !data.next_page_exist) {
            list.add(new CategoryEndData(categoryPagerBundle));//底部左右切换按钮
        }

        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(list);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(getProductTraceTopic(), WeeeEvent.PageView.CATEGORY));

        boolean isLoadMore = REQUEST_TYPE_LOAD_MORE == type;
        if (!isLoadMore) {
            adapterData.postValue(list);
            String key = REQUEST_TYPE_CATALOGUE_NUM == type ? catalogueNum : currentCatalogueNum;
            dataCache.put(key, list);
        } else {
            adapterAppendData.postValue(list);
        }
        nextPageExistData.postValue(data.next_page_exist);
    }

    private void setProdPos(List<AdapterDataType> list, int pos) {
        int prodPos = pos;
        for (AdapterDataType adapterDataType : list) {
            if (adapterDataType instanceof ProductItemData) {
                ((ProductItemData) adapterDataType).prodPos = prodPos;
                prodPos = prodPos + 1;
            } else if (adapterDataType instanceof CategoryCarouselData) {
                prodPos = prodPos + 1;
            }
        }
    }

    public void clearCache() {
        dataCache.clear();
        keysAppended.clear();
    }

    public void getPdpRelated(ProductItemData data, int position) {
        ProductBean bean = data.t;
        productId = bean.id;
        productKey = bean.getProductKey();
    }

    public void getBuyTogether(ProductItemData data, int position) {
    }

    public void checkRelatedCache() {
        SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(productId, productKey);
        if (relatedDataCache != null && (simpleOrderItem == null || simpleOrderItem.quantity == 0)) {
            relatedCardData.postValue(relatedDataCache);
            relatedDataCache = null;
        }
    }

    @NonNull
    private RelatedData getRelatedData(RelatedBean relatedBean, ProductItemData data, int position) {
        RelatedData relatedData = new RelatedData(relatedBean);
        relatedData.source = data.source;
        relatedData.modNm = data.modNm;
        relatedData.modPos = data.modPos;
        relatedData.position = position;
        relatedData.productId = data.t.id;
        return relatedData;
    }

    @NonNull
    private Map<String, Serializable> getBannerRequestParams() {
        RequestParams params = new RequestParams();
        params.put("key", "category");
        params.put("category", categoryPagerBundle.current.key);
        if (!ALL.equalsIgnoreCase(currentCatalogueNum)) {
            params.put("subCategory", currentCatalogueNum);
        }
        return params.get();
    }

    private @NonNull String getKey(String catalogueNum, ProductBean productBean) {
        return catalogueNum + "_" + productBean.getProductId() + "_" + productBean.getProductKey();
    }

    public Map<String, String> getCtxFilter() {
        Map<String, String> map = new HashMap<>();
        CollectionUtils.putAll(map, JsonUtils.parseObject(filters, Map.class));
        CollectionUtils.putAll(map, JsonUtils.parseObject(outFilters, Map.class));
        map.remove(KEY_CATALOGUE_NUM);//埋点filter不需要副分类
        return map;
    }

    public String getProductTraceTopic() {
        String topic = WeeeEvent.PageView.CATEGORY;
        if (categoryPagerBundle != null && categoryPagerBundle.current != null) {
            topic += "-" + categoryPagerBundle.current.key;
        }
        return topic;
    }
}
