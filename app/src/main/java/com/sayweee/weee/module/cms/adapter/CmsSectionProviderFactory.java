package com.sayweee.weee.module.cms.adapter;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.base.adapter.SimpleSectionAdapter;

public abstract class CmsSectionProviderFactory<Provider extends ISectionProvider<?, ?>>
        extends SimpleSectionAdapter.SectionProviderFactory {

    @NonNull
    @Override
    public final ISectionProvider<?, ?> getItemProvider(int viewType) {
        return get();
    }

    @NonNull
    public abstract Provider get();


}
