package com.sayweee.weee.module.home.service;

import androidx.annotation.Nullable;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.data.CmsBannerData;
import com.sayweee.weee.module.cms.iml.product.data.CmsProductLineData;
import com.sayweee.weee.module.cms.service.ComponentPool;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.module.home.provider.CmsCacheData;
import com.sayweee.weee.module.home.provider.banner.data.CmsBannerThemeData;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.utils.PreferenceUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;

/**
 * Author:  winds
 * Date:    2022/6/24.
 * Desc:
 */
public class HomeCacheHelper {

    public static final String KEY_HOME = "_home";

    public static void clearCache() {
        TaskScheduler.execute(new Runnable() {
            @Override
            public void run() {
                PreferenceUtils.getSharedPreferences("_cache")
                        .edit()
                        .remove(KEY_HOME)
                        .apply();
            }
        });
    }

    public static final String KEY_HOME_NEW = "_home_new";


    public static void clearHomeCache() {
        clearCache();
        clearNewCache();
    }

    public static void writeNewCache(Collection<ComponentData> list) {
        if (list != null && list.size() > 0) {
            TaskScheduler.execute(new Runnable() {
                @Override
                public void run() {
                    ArrayList<String> caches = new ArrayList<>();
                    for (ComponentData data : list) {
                        String cache = null;
                        if (data instanceof ICache) {
                            if (ComponentPool.Key.COMPONENT_CM_CONTENT_FEED.equals(data.componentKey) && data instanceof CmsContentFeedPacket) {
                                cache = new CmsContentFeedCacheConverter().convertToCache((CmsContentFeedPacket) data);
                            } else {
                                cache = JsonUtils.toJSONString(data);
                            }
                        }
                        if (cache != null) {
                            caches.add(cache);
                        }
                        if (caches.size() > 6) {
                            break;
                        }
                    }

                    PreferenceUtils.getSharedPreferences("_cache")
                            .edit()
                            .putString(KEY_HOME_NEW, JsonUtils.toJSONString(caches))
                            .apply();
                }
            });
        }
    }

    public static void clearNewCache() {
        TaskScheduler.execute(new Runnable() {
            @Override
            public void run() {
                PreferenceUtils.getSharedPreferences("_cache")
                        .edit()
                        .remove(KEY_HOME_NEW)
                        .apply();
            }
        });
    }


    public static Observable<List<AdapterDataType>> readNewCache() {
        return Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {

            void setTargetType(ComponentData data, int type) {
                if (data != null) {
                    data.type = type;
                }
            }

            @Override
            public void subscribe(ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                List<AdapterDataType> list = new ArrayList<>();
                String data = PreferenceUtils.getSharedPreferences("_cache").getString(KEY_HOME_NEW, null);
                if (data != null && data.trim().length() > 0) {
                    List<String> cache = JsonUtils.parseArray(data, String.class);
                    if (!EmptyUtils.isEmpty(cache)) {
                        for (String s : cache) {
                            if (s != null) {
                                CmsCacheData parsedData = JsonUtils.parseObject(s, CmsCacheData.class);
                                if (parsedData != null && parsedData.getComponentKey() != null) {
                                    String componentKey = parsedData.getComponentKey();
                                    ComponentData target = null;
                                    switch (componentKey) {
                                        case ComponentPool.Key.COMPONENT_CM_SEARCH_BAR:
                                            target = JsonUtils.parseObject(s, CmsSearchBarData.class);
                                            setTargetType(target, CmsItemType.SEARCH_BAR);
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_MAIN_BANNER:
                                            target = JsonUtils.parseObject(s, CmsBannerData.class);
                                            setTargetType(target, CmsItemType.CAROUSEL_BANNER);
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_TOP_MESSAGE:
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_CATEGORIES:
                                            target = JsonUtils.parseObject(s, CmsCategoryData.class);
                                            setTargetType(target, CmsItemType.CATEGORIES);
                                            adjustTargetType(target);
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_ITEM_LINE:
                                        case ComponentPool.Key.COMPONENT_CM_ITEM_LINE_V2:
                                            CmsProductLineData d = JsonUtils.parseObject(s, CmsProductLineData.class);
                                            // prepare title data
                                            if (d != null) {
                                                d.setData(d.t);
                                            }
                                            target = d;
                                            setTargetType(target, CmsItemType.PRODUCT_LINE);
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_BANNER_THEME:
                                            target = JsonUtils.parseObject(s, CmsBannerThemeData.class);
                                            setTargetType(target, CmsItemType.BANNER_THEME);
                                            break;
                                        case ComponentPool.Key.COMPONENT_CM_CONTENT_FEED:
                                            target = JsonUtils.parseObject(s, CmsContentFeedPacket.class);
                                            break;
                                    }
                                    if (target != null && target.isValid()) {
                                        List adapterData = target.toComponentData();
                                        if (adapterData != null && !adapterData.isEmpty()) {
                                            list.addAll(adapterData);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                emitter.onNext(list);
                emitter.onComplete();
            }

            public void adjustTargetType(@Nullable ComponentData<?, ?> data) {
                if (data instanceof CmsCategoryData) {
                    CmsCategoryData it = (CmsCategoryData) data;
                    if (it.displayCapsuleStyle()) {
                        data.type = CmsItemType.CATEGORIES_CAPSULE;
                    } else if (it.displayBarStyle()) {
                        data.type = CmsItemType.CATEGORIES_BAR;
                    }
                }
            }
        });
    }

    private interface ICacheConverter<Data extends ComponentData<?, ?>> {
        String convertToCache(Data data);
    }

    private static class CmsContentFeedCacheConverter implements ICacheConverter<CmsContentFeedPacket> {

        @Override
        public String convertToCache(CmsContentFeedPacket oldData) {
            CmsContentFeedPacket newData = new CmsContentFeedPacket();
            newData.property = oldData.property;
            newData.position = oldData.position;
            newData.pageTarget = oldData.pageTarget;
            newData.componentKey = oldData.componentKey;
            newData.traceId = oldData.traceId;
            newData.status = oldData.status;
            newData.setComponentId(oldData.getComponentId());

            CmsContentFeedListBean oldT = oldData.t;
            CmsContentFeedListBean newT = new CmsContentFeedListBean();
            newT.title = oldT.title;
            newT.module_key = oldT.module_key;
            newT.contents = new ArrayList<>();
            // limit 4 items
            for (int i = 0; i < 4; i++) {
                CmsContentFeedBean content = CollectionUtils.getOrNull(oldT.contents, i);
                if (content == null) {
                    break;
                }
                newT.contents.add(content);
            }
            newT.tabs = new ArrayList<>();
            if (oldT.tabs != null) {
                newT.tabs.addAll(oldT.tabs);
            }
            newData.t = newT;
            return JsonUtils.toJSONString(newData);
        }
    }
}
