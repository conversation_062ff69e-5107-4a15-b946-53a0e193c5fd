
package com.sayweee.weee.module.post.adapter;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.app.Activity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleMultiTypeAdapter;
import com.sayweee.weee.module.cate.product.ImagePreviewActivity;
import com.sayweee.weee.module.post.bean.ExternalTagsBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.helper.FreeBieHelper;
import com.sayweee.weee.module.post.widget.MultiImageView;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.ExpandTextView;
import com.sayweee.widget.shape.ShapeTextView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  ycy
 * more post页面RV adapter
 * Desc:
 */
public class PostMoreAdapter extends SimpleMultiTypeAdapter<AdapterDataType, AdapterViewHolder> {

    public static final int TYPE_NORMAL = 1000;

    public String mid;

    public PostMoreAdapter() {
        super(null);
    }


    @Override
    protected void registerAdapterType() {
        registerItemType(TYPE_NORMAL, R.layout.item_post_more);
    }

    public void setAdapterData(List<PostCategoryBean.ListBean> list) {
        mData.clear();
        if (list != null) {
            mData.addAll(list);
        }
        notifyDataSetChanged();
    }

    public void appendAdapterData(List<PostCategoryBean.ListBean> list) {
        if (!EmptyUtils.isEmpty(list)) {
            addData(list);
        }
    }


    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        if (TYPE_NORMAL == item.getType()) {
            PostCategoryBean.ListBean bean = (PostCategoryBean.ListBean) item;
            helper.addOnClickListener(R.id.layout_post);
            helper.addOnClickListener(R.id.iv_user);
            helper.addOnClickListener(R.id.tv_user);
            helper.addOnClickListener(R.id.iv_badge);
            helper.addOnClickListener(R.id.tv_verified_buyer);
            ExpandTextView tvTitle = helper.getView(R.id.tv_title);
            //翻译按钮
            helper.setVisibleCompat(R.id.tv_translate, bean.showTranslatePortal());
            helper.setText(R.id.tv_translate, bean.useOrigin() ? R.string.s_translate : R.string.s_show_original);

            ShapeTextView tvFreebie = helper.getView(R.id.tv_freebie);
            TextView tvVerifiedBuyer = helper.getView(R.id.tv_verified_buyer);
            ExternalTagsBean firstExternalTag = CollectionUtils.firstOrNull(bean.external_tags);
            if (firstExternalTag != null) {
                if (firstExternalTag.isFree()) {
                    FreeBieHelper.setViewStatus(mContext, tvFreebie, firstExternalTag);
                    ViewTools.setViewVisibilityIfChanged(tvFreebie, View.VISIBLE);
                    ViewTools.setViewVisibilityIfChanged(tvVerifiedBuyer, false);
                } else if (firstExternalTag.isVerifyBuyer()) {
                    tvVerifiedBuyer.setTextColor(ViewTools.parseColor(mContext, firstExternalTag.color, R.color.white));
                    tvVerifiedBuyer.setText(firstExternalTag.label);
                    ViewTools.setViewVisibilityIfChanged(tvFreebie, View.INVISIBLE);
                    ViewTools.setViewVisibilityIfChanged(tvVerifiedBuyer, true);
                } else {
                    tvFreebie.setBackgroundSolidDrawable(ViewTools.parseColor(mContext, firstExternalTag.background, R.color.white), CommonTools.dp2px(25));
                    tvFreebie.setTextColor(ViewTools.parseColor(mContext, firstExternalTag.color, R.color.white));
                    tvFreebie.setText(firstExternalTag.label);
                    ViewTools.setViewVisibilityIfChanged(tvFreebie, View.VISIBLE);
                    ViewTools.setViewVisibilityIfChanged(tvVerifiedBuyer, false);
                }
            } else {
                ViewTools.setViewVisibilityIfChanged(tvFreebie, View.INVISIBLE);
                ViewTools.setViewVisibilityIfChanged(tvVerifiedBuyer, bean.verified_buyer);
            }
            helper.setOnViewClickListener(R.id.tv_translate, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    bean.toggleTranslateStatus();
                    helper.setText(R.id.tv_translate, bean.useOrigin() ? R.string.s_translate : R.string.s_show_original);
                    helper.setText(R.id.tv_title, bean.getCurrentComment());
                }
            });

//            boolean isFirst = !EmptyUtils.isEmpty(mid) && mid.equalsIgnoreCase(String.valueOf(bean.id));
//            helper.setBackgroundColor(R.id.layout_post, isFirst ? mContext.getResources().getColor(R.color.brand_color_tone_neutral_spectrum_1) : mContext.getResources().getColor(R.color.color_surface_1_bg_idle));
//            mid = null;
            helper.setText(R.id.tv_title, bean.getCurrentComment());
            /*tvTitle.initWidth(CommonTools.getWindowWidth(mContext) - CommonTools.dp2px(32), ContextCompat.getColor(mContext, R.color.text_lesser));
            tvTitle.setMaxLines(2);
            tvTitle.initCloseEnd(ContextCompat.getColor(mContext, R.color.color_blue), mContext.getString(R.string.post_more), mContext.getString(R.string.s_show_less));
            tvTitle.setCloseText(bean.getCurrentComment());

            tvTitle.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    if (tvTitle.canExpand) {
                        tvTitle.textViewClick();
                    } else {
                        mContext.startActivity(ReviewDetailActivity.getIntent(mContext, bean.id));
                        AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_WITHIN_PDP_CLICK,
                                new TrackParams().put("post_id ", bean.id).get());
                    }
                }
            });*/

            //用户信息
            helper.loadImage(mContext, R.id.iv_user, WebpManager.get().getConvertUrl(SPEC_AVATAR, bean.user_avatar), R.mipmap.post_user_placeholder);
            //收藏信息
            helper.setText(R.id.tv_collection_qty, bean.like_count <= 0 ? mContext.getString(R.string.like) : PostCollectManager.get().getLikeCountLabel(bean.like_count));
            helper.setImageResource(R.id.iv_collect, bean.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
            helper.setOnViewClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    if (AccountManager.get().isLogin()) {
                        bean.is_set_like = !bean.is_set_like;
                        bean.like_count = bean.is_set_like ? bean.like_count + 1 : bean.like_count - 1;
                        PostCollectManager.get().toggleCollect(false, bean.id, bean.is_set_like, bean.like_count, false);
//                        helper.setVisibleCompat(R.id.tv_collection_qty, bean.like_count > 0);
                        helper.setText(R.id.tv_collection_qty, bean.like_count <= 0 ? mContext.getString(R.string.like) : PostCollectManager.get().getLikeCountLabel(bean.like_count));
                        helper.setImageResource(R.id.iv_collect, bean.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
                        int position = mData.indexOf(item);
                        trackingLike(bean, position);
                    } else {
                        mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                    }
                }
            }, R.id.cl_collect);


            if (!EmptyUtils.isEmpty(bean.user_badge)) {
                ImageLoader.load(mContext, helper.getView(R.id.iv_badge), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, bean.user_badge));
            }
            LinearLayout ll = helper.getView(R.id.ll_name);
            TextView tv = helper.getView(R.id.tv_user);
            ViewTreeObserver vt = ll.getViewTreeObserver();
            vt.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    if (ll.getMeasuredWidth() > 0) {
                        ll.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        bean.maxWidth = ll.getMeasuredWidth();
                        if (bean.maxWidth > 0) {
                            if (!EmptyUtils.isEmpty(bean.user_badge)) {
                                bean.maxWidth = bean.maxWidth - CommonTools.dp2px(24);
                            }
                            tv.setMaxWidth(bean.maxWidth);
                        }
                    }
                }
            });
            tv.setText(bean.user_name);
            if (bean.maxWidth > 0) {
                tv.setMaxWidth(bean.maxWidth);
            }
            helper.setVisibleCompat(R.id.iv_badge, !EmptyUtils.isEmpty(bean.user_badge));
            //九宫格
            MultiImageView iv_multi = helper.getView(R.id.iv_multi);
            boolean visible = !EmptyUtils.isEmpty(bean.pictures);
            iv_multi.setVisibility(visible ? View.VISIBLE : View.GONE);
            if (visible) {
                iv_multi.setList(bean.pictures);
                iv_multi.setOnItemClickListener(new MultiImageView.OnItemClickListener() {
                    @Override
                    public void onItemClick(View view, int position) {
                        toViewImage(bean.pictures, position);
                    }
                });
            }
            helper.setText(R.id.tv_time, DateUtils.formatReviewData(mContext, bean.rec_create_time));
            helper.setVisibleCompat(R.id.line, helper.getLayoutPosition() != getData().size() - 1);
        }

    }

    private void trackingLike(PostCategoryBean.ListBean bean, int position) {
        EagleTrackManger.get().trackEagleClickAction(null,
                -1,
                String.valueOf(bean.id),
                position,
                String.valueOf(bean.id),
                0,
                EagleTrackEvent.TargetType.REVIEW,
                bean.is_set_like ? EagleTrackEvent.ClickType.LIKE : EagleTrackEvent.ClickType.UNLIKE);
    }

    private void toViewImage(List<String> list, int index) {
        if (mContext != null && !EmptyUtils.isEmpty(list)) {
            ArrayList<String> thumbnailUrls = (ArrayList<String>) CollectionUtils.map(
                    list,
                    url -> WebpManager.convert(ImageSpec.SPEC_POST_AUTO, url)
            );
            mContext.startActivity(ImagePreviewActivity.getIntent(mContext, new ArrayList<>(list), thumbnailUrls, index));
            if (mContext instanceof Activity) {
                ((Activity) mContext).overridePendingTransition(0, 0);
            }
        }
    }

    public void toggleCollect(Map<String, Serializable> map) {
        int id = 0;
        Boolean status = null;
        Integer count = null;
        Serializable reviewIdObj = map.get("reviewId");
        if (reviewIdObj instanceof Integer) {
            id = (int) reviewIdObj;
        }
        Serializable statusObj = map.get("status");
        if (statusObj instanceof Boolean) {
            status = (Boolean) statusObj;
        }
        Serializable countObj = map.get("count");
        if (countObj instanceof Integer) {
            count = (Integer) countObj;
        }

        if (id != 0 && status != null && count != null) {
            for (AdapterDataType item : mData) {
                PostCategoryBean.ListBean bean = (PostCategoryBean.ListBean) item;
                if (bean.id == id) {
                    bean.is_set_like = status;
                    bean.like_count = count;
                    notifyItemChanged(mData.indexOf(item));
                    return;
                }
            }
        }
    }

}
