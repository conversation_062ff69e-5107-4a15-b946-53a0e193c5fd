package com.sayweee.weee.module.home.provider.product.data;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.config.CmsConstants;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.cms.service.IMultiDataSourceData;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.module.home.bean.LightningDealsBean;
import com.sayweee.weee.module.home.bean.LightningDealsProperty;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;

import java.util.ArrayList;
import java.util.List;

public class CmsLightingDealsData
        extends ComponentData<LightningDealsBean, LightningDealsProperty>
        implements IMultiDataSourceData, ProductTraceTask.SectionProvider {

    public static final String STATUS_BE_GOING = "begoing";
    public static final String STATUS_ON_GOING = "ongoing";

    private CmsDataSource baseDataSource;

    public CmsLightingDealsData() {
        super(CmsItemType.LIGHTNING_DEALS);
    }

    @Override
    public boolean isValid() {
        if (t != null && property != null) {
            if (filter) {
                return ProductHelper.filterReachLimitValid(t.products, 2);
            } else {
                return !EmptyUtils.isEmpty(t.products);
            }
        } else {
            return false;
        }
    }

    @Override
    public List<? extends ComponentData> toComponentData() {
        if (isValid()) {
            ArrayList<ComponentData> list = new ArrayList<>();
            list.add(this);
            list.add(new CmsBlankData());
            return list;
        }
        return null;
    }

    @Override
    public void setData(LightningDealsBean lightningDealsBean) {
        if (lightningDealsBean != null) {
            lightningDealsBean.system_timestamp = (int) (System.currentTimeMillis() / 1000);
        }
        super.setData(lightningDealsBean);
    }

    public String getIcon() {
        return (t != null && t.component_metadata != null) ? t.component_metadata.icon_url : null;
    }

    public boolean isShowLoyalty() {
        return !EmptyUtils.isEmpty(getIcon());
    }

    @Override
    public String getEventKey() {
        String eventKey = (t != null && t.component_metadata != null) ? t.component_metadata.event_key : null;
        if (!EmptyUtils.isEmpty(eventKey)) {
            return eventKey;
        }
        if (property != null && !EmptyUtils.isEmpty(property.event_key)) {
            return property.event_key;
        }
        return componentKey;
    }

    public String getTitle() {
        String title = (t != null && t.component_metadata != null) ? t.component_metadata.title : null;
        if (!EmptyUtils.isEmpty(title)) {
            return title;
        }
        title = property != null ? property.title : null;
        return title;
    }

    @Nullable
    public String getMoreLink() {
        String moreLink = (t != null && t.component_metadata != null) ? t.component_metadata.more_link : null;
        if (!EmptyUtils.isEmpty(moreLink)) {
            return moreLink;
        }
        moreLink = property != null ? property.more_link : null;
        return moreLink;
    }

    @Nullable
    public String getLinkUrl() {
        String linkUrl = (t != null && t.component_metadata != null) ? t.component_metadata.link_url : null;
        if (!EmptyUtils.isEmpty(linkUrl)) {
            return linkUrl;
        }
        linkUrl = property != null ? property.link_url : null;
        return linkUrl;
    }

    public int getTitleFontSize() {
        return property != null ? property.getTitleFontSize() : CmsConstants.TITLE_FONT_SIZE_MIDDLE;
    }

    @Override
    public void setBaseDataSource(@Nullable CmsDataSource dataSource) {
        this.baseDataSource = dataSource;
    }

    @Nullable
    @Override
    public CmsDataSource getBaseDataSource() {
        return baseDataSource;
    }

    @Override
    public void updateDataSourceResponse(@NonNull CmsDataSource dataSource, Object object, FailureBean failure) {
        LightningDealsBean newData = null;
        if (object instanceof LightningDealsBean) {
            newData = (LightningDealsBean) object;
        }
        setData(newData);
    }

    // =========================================
    // Implementation of ProductSalesTraceTask.SectionProvider interface
    // =========================================
    @Override
    public void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder) {
        if (isValid()) {
            for (ProductBean product : t.products) {
                builder.add(product.recommendation_trace_id, product.id);
            }
            builder.setModNm(getEventKey());
            builder.setUniqueKey(ProductTraceKey.generateUniqueKey(getEventKey(), null));
        }
    }
}
