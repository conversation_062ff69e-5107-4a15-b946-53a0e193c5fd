package com.sayweee.weee.module.mkpl.common;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.utils.ViewTools;

//
// Created by <PERSON><PERSON> on 12/07/2023.
// Copyright (c) 2023 Weee LLC. All rights reserved.
//
public class MiniCartAddItemProvider extends SimpleSectionProvider<MiniCartAddItemData, AdapterViewHolder> {

    @Override
    public int getItemType() {
        return R.layout.provider_mini_cart_add_item;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.provider_mini_cart_add_item;
    }

    @Override
    public void convert(AdapterViewHolder helper, MiniCartAddItemData item) {
        setFullSpan(helper);
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_mini_cart_add_item), 600);
        helper.addOnClickListener(R.id.ll_mini_cart_action_add_items);
    }
}
