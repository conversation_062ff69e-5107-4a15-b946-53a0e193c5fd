package com.sayweee.weee.module.product.provider;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_375_AUTO;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.OnProviderCallback;
import com.sayweee.weee.module.category.adapter.CategoryCarouselProvider;
import com.sayweee.weee.module.category.bean.CategoryCarouselBean;
import com.sayweee.weee.module.category.bean.CategoryCarouselData;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.widget.round.RoundImageView;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.listener.OnBannerListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PdpCarouselProvider extends CategoryCarouselProvider {

    public PdpCarouselProvider setOnProviderCallback(OnProviderCallback callback) {
        this.callback = callback;
        return this;
    }
    @Override
    public void convert(AdapterViewHolder helper, CategoryCarouselData item) {
        List<CategoryCarouselBean> list = item.t;
        CarouselBanner banner = helper.getView(R.id.banner);
        TextView tvSponsored = helper.getView(R.id.tv_sponsored);
        LinearLayout layoutIndicator = helper.getView(R.id.layout_indicator_container);
        banner.removeIndicator();
        int count = list.size();
        helper.setVisibleCompat(R.id.layout_indicator_container, count > 1);
        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal_dark_grey, R.drawable.shape_line_banner_line_normal_selected, true, count, 0);
        tvSponsored.setVisibility(list.get(0).is_sponsor ? View.VISIBLE : View.GONE);
        tvSponsored.setText(list.get(0).sponsored_text);
        item.setBannerData(list.get(0).id, 0, list.get(0).link_url);
        banner.isAutoLoop(item.isAutoplay());
        banner.setAdapter(
                        new BannerImageAdapter<CategoryCarouselBean>(list) {

                            @Override
                            public void onViewAttachedToWindow(@NonNull BannerImageHolder holder) {
                                super.onViewAttachedToWindow(holder);
                                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) holder.imageView.getLayoutParams();
                                layoutParams.leftMargin = CommonTools.dp2px(20);
                                layoutParams.rightMargin = CommonTools.dp2px(20);
                            }

                            @Override
                            public BannerImageHolder onCreateHolder(ViewGroup parent, int viewType) {
                                RoundImageView imageView = new RoundImageView(parent.getContext());
                                imageView.setRadius(CommonTools.dp2px(26));
                                //注意，必须设置为match_parent，这个是viewpager2强制要求的
                                ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                                        ViewGroup.LayoutParams.MATCH_PARENT,
                                        ViewGroup.LayoutParams.MATCH_PARENT);
                                imageView.setLayoutParams(params);
                                imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                                return new BannerImageHolder(imageView);
                            }

                            @Override
                            public void onBindView(BannerImageHolder holder, CategoryCarouselBean data, int position, int size) {
                                ImageLoader.load(context, holder.imageView, WebpManager.get().getConvertUrl(SPEC_375_AUTO, data.img_url), R.mipmap.iv_banner_placeholder_375);
                            }
                        }, item.isLoop())
                .setOnBannerListener(new OnBannerListener<CategoryCarouselBean>() {
                    @Override
                    public void OnBannerClick(CategoryCarouselBean data, int position) {
                        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(item.productId), null, null, null, item.traceId);
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(item.modNm)
                                .setMod_pos(item.modPos)
                                .setTargetNm(String.valueOf(data.id))
                                .setTargetPos(position)
                                .setSec_nm(item.secNm)
                                .setTargetType(EagleTrackEvent.BannerType.MESSAGE)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setUrl(data.url)
                                .addCtx(ctx)
                                .build().getParams());
                        context.startActivity(WebViewActivity.getIntent(context, data.link_url));
                    }
                })
                .addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
                    @Override
                    public void onPageSelected(int position, boolean isAuto) {
                        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal_dark_grey, R.drawable.shape_line_banner_line_normal_selected, false, count, position);
                        CategoryCarouselBean carouselBean = list.get(position);
                        item.setBannerData(carouselBean.id, position, carouselBean.link_url);
                        if (!isAuto) {
                            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(item.productId), null, null, null, item.traceId);
                            trackEagleBannerImp(item.modNm
                                    , item.modPos
                                    , item.secNm
                                    , -1
                                    , carouselBean.id
                                    , null
                                    , position
                                    , EagleTrackEvent.BannerType.MESSAGE
                                    , carouselBean.link_url
                                    , ctx);
                        }
                        tvSponsored.setVisibility(carouselBean.is_sponsor ? View.VISIBLE : View.GONE);
                        tvSponsored.setText(carouselBean.sponsored_text);
                    }
                });

    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CategoryCarouselData item, int position) {
        String productId = String.valueOf(item.productId);
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, productId, null, null, null, item.traceId);
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(item.modNm)
                .setMod_pos(item.modPos)
                .setSec_nm(item.secNm)
                .setBanner_id(item.bannerId)
                .setBanner_pos(item.bannerPosition)
                .setBanner_type(EagleTrackEvent.BannerType.MESSAGE)
                .setUrl(item.bannerUrl)
                .addCtx(ctx)
                .build().getParams();
        List<ImpressionBean> list = new ArrayList<>();
        list.add(new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, position + item.modNm + productId));
        return list;
    }


    private void trackEagleBannerImp(String mod_nm, int mod_pos, String sec_nm, int sec_pos, int banner_id, String banner_key, int banner_pos, String banner_type, String value, Map<String, Object> ctx) {
        AppAnalytics.logBannerImp(new EagleTrackModel.Builder()
                .setMod_nm(mod_nm)
                .setMod_pos(mod_pos)
                .setSec_nm(sec_nm)
                .setSec_pos(sec_pos)
                .setBanner_id(banner_id)
                .setBanner_key(banner_key)
                .setBanner_pos(banner_pos)
                .setBanner_type(banner_type)
                .addCtx(ctx)
                .setUrl(value)
                .build().getParams());
    }

}
