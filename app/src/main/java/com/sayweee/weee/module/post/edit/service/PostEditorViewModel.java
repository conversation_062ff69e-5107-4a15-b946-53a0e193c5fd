package com.sayweee.weee.module.post.edit.service;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.ContentResolver;
import android.content.Context;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.provider.MediaStore;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.request.FutureTarget;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.tools.SdkVersionUtils;
import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.post.bean.AtBean;
import com.sayweee.weee.module.post.bean.EditorTranslationData;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.edit.MediaHelper;
import com.sayweee.weee.module.post.edit.adapter.PostEditorAdapter;
import com.sayweee.weee.module.post.edit.bean.CommitLanguageBean;
import com.sayweee.weee.module.post.edit.bean.EditorContentData;
import com.sayweee.weee.module.post.edit.bean.EditorHashTagData;
import com.sayweee.weee.module.post.edit.bean.EditorHeaderData;
import com.sayweee.weee.module.post.edit.bean.EditorProductData;
import com.sayweee.weee.module.post.edit.bean.EditorProductItemData;
import com.sayweee.weee.module.post.edit.bean.EditorTitleData;
import com.sayweee.weee.module.post.edit.bean.LanguageBean;
import com.sayweee.weee.module.post.edit.bean.NotifyBean;
import com.sayweee.weee.module.post.edit.bean.SuggestTranslationsBean;
import com.sayweee.weee.module.post.edit.service.bean.HashTagItemBean;
import com.sayweee.weee.module.post.edit.service.bean.PostDraftData;
import com.sayweee.weee.module.post.edit.service.bean.PostEditData;
import com.sayweee.weee.module.post.edit.service.bean.PostUploadData;
import com.sayweee.weee.module.post.edit.utils.DownloadUtil;
import com.sayweee.weee.module.post.profile.ProfileIntentCreator;
import com.sayweee.weee.module.search.bean.UploadResponseBean;
import com.sayweee.weee.service.helper.PictureSelectorHelper;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.progress.ProgressInfo;
import com.sayweee.weee.utils.progress.ProgressListener;
import com.sayweee.weee.utils.progress.ProgressRequestBody;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * Author:  winds
 * Date:    2021/11/30.
 * Desc:
 */
public class PostEditorViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public MutableLiveData<List<AdapterDataType>> adapterData = new MutableLiveData<>();

    public MutableLiveData<Boolean> uploadReadyData = new MutableLiveData<>();

    public MutableLiveData<Boolean> draftReadyData = new MutableLiveData<>();

    public MutableLiveData<Boolean> editResultData = new MutableLiveData<>();

    public MutableLiveData<String> selectCoverResult = new MutableLiveData<>();

    public MutableLiveData<Bitmap> seekToBitmap = new MutableLiveData<>();

    public MutableLiveData<List<AtBean>> atData = new MutableLiveData();

    public MutableLiveData<LanguageBean> languageData = new MutableLiveData();

    public MutableLiveData<Integer> downloadStart = new MutableLiveData();

    public MutableLiveData<Integer> downloadProgress = new MutableLiveData();

    public MutableLiveData<String> downloadFinish = new MutableLiveData();

    public MutableLiveData<String> downloadError = new MutableLiveData();

    public MutableLiveData<NotifyBean> notifyData = new MutableLiveData();

    public MutableLiveData<List<SimpleProductBean>> productList = new MutableLiveData();

    public MutableLiveData<SuggestTranslationsBean> translationsBeanMutableLiveData = new MutableLiveData();

    public PostEditorViewModel(@NonNull Application application) {
        super(application);
    }

    public void initDataByAdd(int type, LocalMedia media, List<HashTagItemBean> list, List<SimpleProductBean> productBeanList, String source) {
        onSubscribeResultDisplayLoading(Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                long uploadId = generateUploadId();
                long duration = media.getDuration();
                String videoPath = PictureSelectorHelper.getMediaCompatPath(media);
                String coverPath = getCoverPathDefault(videoPath, uploadId);
                List<AdapterDataType> target = createAdapterData(new EditorHeaderData(uploadId, videoPath, coverPath, 0).setDuration(duration).setSource(source), null, null, list, productBeanList, false);
                emitter.onNext(target);
                emitter.onComplete();
            }
        }));
    }

    public void initDataByEmailAdd(int type, List<SimpleProductBean> productBeanList, String source) {
        onSubscribeResultDisplayLoading(Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                List<AdapterDataType> target = createAdapterData(new EditorHeaderData(0, null, null, 0).setSource(source), null, null, null, productBeanList, false);
                emitter.onNext(target);
                emitter.onComplete();
            }
        }));
    }

    public void initDataByDraft(int type, PostDraftData draft) {
        onSubscribeResult(Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                long uploadId = draft.getDraftId();
                long duration = draft.duration;
                String videoPath = draft.video;
                String coverPath = draft.image;
                if (coverPath == null && !EmptyUtils.isEmpty(videoPath)) {
                    coverPath = getCoverPathDefault(videoPath, uploadId);
                }
                String title = draft.title;
                String content = draft.content;
                List<HashTagItemBean> hashTagList = draft.hashTagList;
                List<SimpleProductBean> productList = draft.productList;
                List<AdapterDataType> target = createAdapterData(new EditorHeaderData(uploadId,
                        videoPath,
                        coverPath,
                        draft.thumbnail_create_time).setDraftId(draft.draftId).setDuration(duration).setSource(draft.source), title, content, hashTagList, productList, true);
                emitter.onNext(target);
                emitter.onComplete();
            }
        }));

    }

    public void initDataByEdit(int type, PostBean post, List<SimpleProductBean> list, List<HashTagItemBean> tags) {
        onSubscribeResult(Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                long uploadId = generateUploadId();
                String videoPath = post.ref_url;
                String coverPath = post.resize_url;
                if (coverPath == null) {
                    coverPath = getCoverPathDefault(videoPath, uploadId);
                }
                String title = post.hasToggled() ? post.title : post.title_lang;
                String content = post.hasToggled() ? post.comment : post.comment_lang;
                List<AdapterDataType> target = createAdapterData(new EditorHeaderData(uploadId, videoPath, coverPath, 0).setEditMode(true).setEditId(post.id), title, content, tags, list, false);
                emitter.onNext(target);
                emitter.onComplete();
            }
        }), new SimpleObserver<List<AdapterDataType>>() {

            @Override
            public void onSubscribe(Disposable d) {
//                setLoadingStatus(true);
            }

            @Override
            public void onNext(@NonNull List<AdapterDataType> response) {
                adapterData.setValue(response);
//                setLoadingStatus(false);
            }
        });
    }

    private List<AdapterDataType> createAdapterData(EditorHeaderData titleData, String title, String content, List<HashTagItemBean> hashTagList, List<SimpleProductBean> productList, boolean isDraft) {
        List<AdapterDataType> list = new ArrayList<>();
        list.add(titleData);
        list.add(new EditorTitleData(PostEditorAdapter.TYPE_TITLE, title));
        list.add(new EditorContentData(PostEditorAdapter.TYPE_CONTENT, content, titleData.id, isDraft));
        list.add(new EditorTranslationData(PostEditorAdapter.TYPE_TRANSLATION, !EmptyUtils.isEmpty(title)));
        if (VariantConfig.IS_VIEW_VISIBLE) {
            list.add(new EditorHashTagData(PostEditorAdapter.TYPE_HASH_TAG).setHashTagList(hashTagList));
        }
        list.add(new EditorProductData(PostEditorAdapter.TYPE_ADD_PRODUCT, productList));
        if (!EmptyUtils.isEmpty(productList)) {
            for (SimpleProductBean bean : productList) {
                list.add(new EditorProductItemData(PostEditorAdapter.TYPE_PRODUCT_ITEM, bean));
            }
        }
        list.add(new SimpleAdapterDataType(PostEditorAdapter.TYPE_EMPTY));
        return list;
    }

    public void onVideoPickChanged(LocalMedia data) {
        List<AdapterDataType> list = adapterData.getValue();
        if (list != null) {
            Observable<List<AdapterDataType>> observable = Observable.create(new ObservableOnSubscribe<List<AdapterDataType>>() {
                @Override
                public void subscribe(@NonNull ObservableEmitter<List<AdapterDataType>> emitter) throws Exception {
                    EditorHeaderData target = null;
                    for (AdapterDataType item : list) {
                        if (item instanceof EditorHeaderData) {
                            target = (EditorHeaderData) item;
                            break;
                        }
                    }
                    if (target != null) {
                        long duration = 0;
                        String videoPath = null;
                        String coverPath = null;
                        if (data != null) {
                            duration = data.getDuration();
                            videoPath = PictureSelectorHelper.getMediaCompatPath(data);
                            coverPath = getCoverPathDefault(videoPath, target.uploadId);
                        }
                        target.setDuration(duration).setMediaData(videoPath, coverPath);
                    }
                    emitter.onNext(list);
                    emitter.onComplete();
                }
            });
            if (data == null) {
                onSubscribeResult(observable);
            } else {
                onSubscribeResultDisplayLoading(observable);
            }
        }
    }

    public void setSelectCover(Bitmap bitmap) {
        Observable.create(new ObservableOnSubscribe<String>() {
                    @Override
                    public void subscribe(@NonNull ObservableEmitter<String> emitter) throws Exception {
                        String path = PostUploadManager.get().saveCoverBitmap(bitmap);
                        emitter.onNext(path);
                        emitter.onComplete();
                    }
                }).compose(DisposableTransformer.scheduler(this, true))
                .subscribe(new SimpleObserver<String>() {
                    @Override
                    public void onNext(String path) {

                        selectCoverResult.setValue(path);
                    }
                });
    }

    public void onProductItemRemoved(EditorProductItemData data) {
        List<AdapterDataType> list = adapterData.getValue();
        if (list != null) {
            boolean removed = list.remove(data);
            if (removed) {
                EditorProductData targetData = null;
                SimpleProductBean product = data.product;
                for (AdapterDataType item : list) {
                    if (item instanceof EditorProductData) {
                        targetData = (EditorProductData) item;
                        break;
                    }
                }
                if (targetData != null) {
                    targetData.removeItem(product);
                }

                adapterData.setValue(list);
            }
        }
    }

    public void onProductPickChanged(List<SimpleProductBean> data) {
        List<AdapterDataType> list = adapterData.getValue();
        if (list != null) {
            EditorProductData target = null;
            List<AdapterDataType> invalidData = new ArrayList<>();
            for (AdapterDataType item : list) {
                if (item instanceof EditorProductData) {
                    target = (EditorProductData) item;
                } else if (item instanceof EditorProductItemData) {
                    invalidData.add(item);
                }
            }
            if (target != null) {
                list.removeAll(invalidData);
                int index = list.indexOf(target);
                target.setProductBeanList(data);
                if (!EmptyUtils.isEmpty(data)) {
                    for (SimpleProductBean bean : data) {
                        index += 1;
                        list.add(index, new EditorProductItemData(PostEditorAdapter.TYPE_PRODUCT_ITEM, bean));
                    }
                }
            }
            adapterData.setValue(list);
        }
    }

    public void onPostCoverChanged(String cover) {

    }

    public void onTagsPickChanged(List<HashTagItemBean> data) {
        List<AdapterDataType> list = adapterData.getValue();
        if (list != null) {
            for (AdapterDataType item : list) {
                if (item instanceof EditorHashTagData) {
                    ((EditorHashTagData) item).setHashTagList(data);
                }
            }
            adapterData.setValue(list);
        }
    }

    private void onSubscribeResult(Observable<List<AdapterDataType>> observable) {
        onSubscribeResult(observable, false, 0);
    }

    private void onSubscribeResultDisplayLoading(Observable<List<AdapterDataType>> observable) {
        onSubscribeResult(observable, true, 500);
    }

    private void onSubscribeResult(Observable<List<AdapterDataType>> observable, boolean loading, long delay) {
        onSubscribeResult(observable, new SimpleObserver<List<AdapterDataType>>() {

            @Override
            public void onSubscribe(Disposable d) {
                super.onSubscribe(d);
                if (loading) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onNext(@NonNull List<AdapterDataType> list) {
                adapterData.setValue(list);
                if (loading) {
                    delayDispatchLoadingStatus(false, delay);
                }
            }
        });
    }

    private <T> void onSubscribeResult(Observable<T> observable, Observer<T> observer) {
        observable.compose(DisposableTransformer.scheduler(this, true))
                .subscribe(observer);
    }

    /**
     * 获取视频封面，此方法需要在子线程中获取
     *
     * @param videoPath
     * @param uploadId
     * @return
     */
    private String getCoverPathDefault(String videoPath, long uploadId) {
        Logger.json("cover ==>", "cover start " + videoPath);
        Bitmap bitmap = getCoverByGlide(videoPath);
        if (bitmap == null) {
            Logger.json("cover ==>", "cover load by thumbnail");
            try {
                bitmap = ThumbnailUtils.createVideoThumbnail(videoPath, MediaStore.Images.Thumbnails.MINI_KIND);
            } catch (Exception ignored) {
            }
        }
        if (bitmap == null) {
            Logger.json("cover ==>", "cover load by frame");
            bitmap = retryGetCover(videoPath);
        }
        if (bitmap != null) {
            Logger.json("cover ==>", "cover load success to save");
            return saveVideoCover(bitmap, uploadId);
        } else {
            Logger.json("cover ==>", "cover load failed");
        }
        return null;
    }

    private String saveVideoCover(Bitmap bitmap, long uploadId) {
        String dirPath = CommonTools.getCachePath(LifecycleProvider.get().getApplication(), AppConfig.PATH_POST_NAME + File.separator + uploadId);
        String path = dirPath + File.separator + System.currentTimeMillis() + ".jpeg";
        PostUploadManager.get().saveBitmap(new File(path), bitmap);
        return path;
    }

    private Bitmap retryGetCover(String videoPath) {
        MediaMetadataRetriever metadataRetriever = null;
        try {
            metadataRetriever = new MediaMetadataRetriever();
            MediaHelper.setDataSourceCompat(metadataRetriever, videoPath);
            return metadataRetriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
        } catch (Exception e) {
            return null;
        } finally {
            if (metadataRetriever != null) {
                try {
                    metadataRetriever.release();
                } catch (Exception ignored) {
                }
            }
        }
    }

    @SuppressLint("CheckResult")
    private Bitmap getCoverByGlide(String path) {
        try {
            Uri uri = null;
            if (SdkVersionUtils.checkedAndroid_Q() && path.startsWith(ContentResolver.SCHEME_CONTENT)) {
                uri = Uri.parse(path);
            }
            RequestBuilder<Bitmap> builder = Glide.with(getApplication()).asBitmap();
            if (uri != null) {
                builder.load(uri);
            } else {
                builder.load(path);
            }
            FutureTarget<Bitmap> target = builder.submit();
            if (target != null) {
                Bitmap bitmap = target.get();
                target.cancel(false);
                return bitmap;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            // e.printStackTrace();
        }
        return null;
    }

    private void onImageRetryResult(String path) {
        List<AdapterDataType> list = adapterData.getValue();
        if (!EmptyUtils.isEmpty(list)) {
            AdapterDataType item = list.get(0);
            if (item instanceof EditorHeaderData) {
                ((EditorHeaderData) item).image = path;
            }
            adapterData.postValue(list);
        }
    }

    @SuppressWarnings("squid:S2245")
    private final Random rand = new Random();

    private long generateUploadId() {
        return System.currentTimeMillis() + rand.nextInt();
    }

    /*******************************************************************************************/
    //提交
    public void generateUploadData(List<AdapterDataType> list, String attachedProductId) {
        onSubscribeResult(Observable.create(new ObservableOnSubscribe<PostUploadData>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<PostUploadData> emitter) throws Exception {
                PostUploadData result = prepareUploadData(list);
                if (result != null && result.isPrepared()) {
                    result.attachedProductId = attachedProductId;
                    PostUploadManager.get().addPostUploadData(result);
                    if (result.draftId > 0) {
                        PostDraftManager.get().removePostDraftData(result.draftId);
                    }
                    emitter.onNext(result);
                    emitter.onComplete();
                } else {
                    emitter.onError(new IllegalArgumentException("lack of params " + JsonUtils.toJSONString(result)));
                }
            }
        }), new SimpleObserver<PostUploadData>() {
            @Override
            public void onNext(@NonNull PostUploadData result) {
                PostUploadManager.get().execUploadVideo(result);
                uploadReadyData.setValue(true);
            }

            @Override
            public void onError(Throwable e) {
                Logger.error(e);
            }
        });
    }

    private PostUploadData prepareUploadData(List<AdapterDataType> list) {
        long duration = 0;
        String video = null;
        String cover = null;
        long draftId = -1;
        long uploadId = -1;
        long thumbnail_create_time = -1;
        String title = null;
        String content = null;
        String source = null;
        List<SimpleProductBean> attachedProducts = null;
        List<HashTagItemBean> attachedTags = null;
        List<CommitLanguageBean> translation = null;
        if (!EmptyUtils.isEmpty(list)) {
            for (AdapterDataType item : list) {
                if (item instanceof EditorHeaderData) {
                    EditorHeaderData temp = (EditorHeaderData) item;
                    duration = temp.duration;
                    video = temp.video;
                    cover = temp.image;
                    thumbnail_create_time = temp.thumbnailCreateTime;
                    draftId = temp.getDraftId();
                    uploadId = temp.uploadId;
                    source = temp.source;
                } else if (item instanceof EditorTitleData) {
                    title = ((EditorTitleData) item).title;
                } else if (item instanceof EditorContentData) {
                    content = ((EditorContentData) item).content;
                } else if (item instanceof EditorHashTagData) {
                    attachedTags = ((EditorHashTagData) item).hashTagList;
                } else if (item instanceof EditorProductData) {
                    attachedProducts = ((EditorProductData) item).list;
                } else if (item instanceof EditorTranslationData) {
                    translation = ((EditorTranslationData) item).translation;
                }
            }
        }
        PostUploadData data = new PostUploadData(uploadId, cover, video, title, content, attachedTags, attachedProducts, translation, source);
        data.thumbnail_create_time = thumbnail_create_time;
        if (data.isPrepared()) {
            if (duration <= 0) {
                duration = getMediaDuration(video);
            }
            data.setDuration(duration);
        }
        if (draftId > 0) {
            data.setDraftId(draftId);
        }
        return data;
    }

    private long getMediaDuration(String path) {
        MediaPlayer player = null;
        try {
            player = new MediaPlayer();
            player.setDataSource(path);
            player.prepare();
            return player.getDuration();
        } catch (Exception e) {
            // e.printStackTrace();
        } finally {
            if (player != null) {
                try {
                    player.release();
                } catch (Exception ignored) {
                    // ignored.printStackTrace();
                }
            }
        }
        return 0;
    }

    public void generateDraftData(List<AdapterDataType> list) {
        if (!EmptyUtils.isEmpty(list)) {
            onSubscribeResult(Observable.create(new ObservableOnSubscribe<Boolean>() {
                @Override
                public void subscribe(@NonNull ObservableEmitter<Boolean> emitter) throws Exception {
                    long duration = 0;
                    String video = null;
                    String cover = null;
                    long draftId = -1;
                    long uploadId = -1;
                    long thumbnail_create_time = -1;
                    String title = null;
                    String content = null;
                    String source = null;
                    SuggestTranslationsBean suggestTranslationsBean = null;
                    List<SimpleProductBean> attachedProducts = null;
                    List<HashTagItemBean> attachedTags = null;

                    for (AdapterDataType item : list) {
                        if (item instanceof EditorHeaderData) {
                            EditorHeaderData temp = (EditorHeaderData) item;
                            video = temp.video;
                            cover = temp.image;
                            duration = temp.duration;
                            thumbnail_create_time = temp.thumbnailCreateTime;
                            draftId = temp.getDraftId();
                            uploadId = temp.uploadId;
                            source = temp.source;
                        } else if (item instanceof EditorTitleData) {
                            title = ((EditorTitleData) item).title;
                        } else if (item instanceof EditorContentData) {
                            content = ((EditorContentData) item).content;
                        } else if (item instanceof EditorHashTagData) {
                            attachedTags = ((EditorHashTagData) item).hashTagList;
                        } else if (item instanceof EditorProductData) {
                            attachedProducts = ((EditorProductData) item).list;
                        } else if (item instanceof EditorTranslationData) {
                            suggestTranslationsBean = ((EditorTranslationData) item).suggestTranslationsBean;
                        }
                    }
                    if (draftId <= 0) {
                        draftId = uploadId;
                    }
                    PostDraftData result = new PostDraftData(
                            draftId,
                            duration,
                            cover,
                            video,
                            title,
                            content,
                            System.currentTimeMillis(),
                            attachedTags,
                            attachedProducts,
                            suggestTranslationsBean,
                            source
                    );
                    result.thumbnail_create_time = thumbnail_create_time;
                    PostDraftManager.get().addOrUpdatePostDraftData(result);

                    emitter.onNext(true);
                    emitter.onComplete();
                }
            }), new SimpleObserver<Boolean>() {
                @Override
                public void onNext(@NonNull Boolean result) {
                    draftReadyData.setValue(result);
                }
            });
        }
    }

    public void execPostEdit(List<AdapterDataType> list, String compressPath) {
        onSubscribeResult(Observable.create(new ObservableOnSubscribe<PostEditData>() {
            @Override
            public void subscribe(@NonNull ObservableEmitter<PostEditData> emitter) throws Exception {
                PostEditData result = prepareEditData(list, compressPath);
                if (result.isPrepared()) {
                    emitter.onNext(result);
                    emitter.onComplete();
                } else {
                    emitter.onError(new IllegalArgumentException("lack of params " + JsonUtils.toJSONString(result)));
                }
            }
        }), new SimpleObserver<PostEditData>() {
            @Override
            public void onSubscribe(Disposable d) {
                super.onSubscribe(d);
                if (EmptyUtils.isEmpty(compressPath)) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onNext(@NonNull PostEditData result) {
                if (result.id <= 0) {
                    uploadCover(result);
                } else {
                    editVideo(result);
                }
            }

            @Override
            public void onError(Throwable e) {
                Logger.error(e);
                setLoadingStatus(false);
            }
        });
    }

    private void uploadCover(PostEditData result) {
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        File coverFile = new File(result.coverPath);
        String url = CommonTools.packageUploadUrl(null, coverFile.getName());

        builder.addFormDataPart("file", coverFile.getName(), RequestBody.create(MediaType.get("image/jpeg"), coverFile));
        getLoader().getHttpService().uploadVideoResource(url, AccountManager.get().getBearerToken(), builder.build().parts())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<UploadResponseBean>() {
                    @Override
                    public void onResponse(UploadResponseBean response) {
                        List<UploadResponseBean.Body> list = response.object;
                        if (!EmptyUtils.isEmpty(list)) {
                            result.coverUploadPath = list.get(0).url;
                        }
                        editVideo(result);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        editVideo(result);
                    }
                });
    }

    private void editVideo(PostEditData data) {
        getLoader()
                .getHttpService()
                .editPost(String.valueOf(data.id),
                        new RequestParams().put("type", "video")
                                .put("title", data.title)
                                .put("description", data.content)
                                .putNonNull("tag_ids", data.getAttachHashTagIds())
                                .putNonNull("product_ids", data.getAttachProductIds())
                                .put("ref_url", data.getVideoPath())
                                .put("show_url", data.getCoverPath())
                                .putNonNull("translation", (Serializable) data.translation)
//                                .put("duration", data.duration / 1000) //编辑未修改视频 可不传此参数
                                .putNonNull("thumbnail_create_time", data.thumbnail_create_time <= 0 ? null : data.thumbnail_create_time)
                                .create())
                .compose(DisposableTransformer.scheduler(this, true))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        editResultData.setValue(true);
                        PostUploadManager.get().clearCacheAfterSuccess(data.uploadId);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    private PostEditData prepareEditData(List<AdapterDataType> list, String compressPath) {
        int id = 0;
        long uploadId = -1;
        long duration = 0;
        long thumbnail_create_time = 0;
        String video = null;
        String cover = null;
        String title = null;
        String content = null;
        List<SimpleProductBean> attachedProducts = null;
        List<HashTagItemBean> attachedTags = null;
        List<CommitLanguageBean> translation = null;
        if (!EmptyUtils.isEmpty(list)) {
            for (AdapterDataType item : list) {
                if (item instanceof EditorHeaderData) {
                    EditorHeaderData temp = (EditorHeaderData) item;
                    id = temp.id;
                    uploadId = temp.uploadId;
                    duration = temp.duration;
                    video = temp.video;
                    cover = EmptyUtils.isEmpty(compressPath) ? temp.image : compressPath;
                    thumbnail_create_time = temp.thumbnailCreateTime;
                } else if (item instanceof EditorTitleData) {
                    title = ((EditorTitleData) item).title;
                } else if (item instanceof EditorContentData) {
                    content = ((EditorContentData) item).content;
                } else if (item instanceof EditorHashTagData) {
                    attachedTags = ((EditorHashTagData) item).hashTagList;
                } else if (item instanceof EditorProductData) {
                    attachedProducts = ((EditorProductData) item).list;
                } else if (item instanceof EditorTranslationData) {
                    translation = ((EditorTranslationData) item).translation;
                }
            }
        }
        //编辑未修改视频，故不需要此参数
        /*if (duration <= 0) {
            duration = getMediaDuration(video);
        }*/
        PostEditData data = new PostEditData(id, uploadId, duration, title, content, cover, video, attachedTags, attachedProducts, translation);
        if (thumbnail_create_time > 0) {
            data.coverPath = cover;
        }
        data.thumbnail_create_time = thumbnail_create_time;
        return data;
    }

    //@用户功能
    public void getMentionList(String keyword, String author_id) {
        getLoader().getHttpService().getAtList(keyword, author_id, "post_desc")
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<List<AtBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<AtBean>> response) {
                        atData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    //@用户功能
    public void getLanguageList() {
        getLoader().getHttpService().getLanguageList()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<LanguageBean>>() {
                    @Override
                    public void onResponse(ResponseBean<LanguageBean> response) {
                        languageData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    public void downloadVideo(Context context, String url) {
        String compressPath = CommonTools.getCachePath(context, AppConfig.PATH_POST_NAME + File.separator) + File.separator + System.currentTimeMillis() + ".mp4";
        DownloadUtil.download(url, compressPath, new DownloadListener() {
            @Override
            public void onStart() {
                downloadStart.postValue(0);
            }

            @Override
            public void onProgress(int progress) {
                downloadProgress.postValue(progress);
            }

            @Override
            public void onFinish(String path) {
                downloadFinish.postValue(path);
            }

            @Override
            public void onFail(String errorInfo) {
                downloadError.postValue(errorInfo);
            }
        });

    }

    public void uploadImg(List<AdapterDataType> list, String path) {
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        File coverFile = new File(path);
        String url = CommonTools.packageUploadUrl(null, coverFile.getName());

        builder.addFormDataPart("file", coverFile.getName(), new ProgressRequestBody(coverFile, "image/jpeg", new ProgressListener() {
            @Override
            public void onProgress(ProgressInfo progressInfo) {

            }

            @Override
            public void onError(long id, Exception e) {

            }
        }));
        getLoader().getHttpService().uploadVideoResource(url, AccountManager.get().getBearerToken(), builder.build().parts())
                .compose(DisposableTransformer.scheduler())
                .subscribe(new ResponseObserver<UploadResponseBean>() {

                    @Override
                    public void onBegin() {
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(UploadResponseBean response) {
                        if (!EmptyUtils.isEmpty(response.object)) {
                            execPostEdit(list, response.object.get(0).url);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {

                    }
                });

    }

    //editPost 发布视频 进Profile显示的弹窗数据
    public void getNotify() {
        getLoader().getHttpService().getNotify()
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NotifyBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NotifyBean> response) {
                        notifyData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    //editPost 发布视频 进Profile显示的弹窗数据
    public void getProductList(String productIds) {
        getLoader().getHttpService().getPostProductList(productIds)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<List<SimpleProductBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<SimpleProductBean>> response) {
                        productList.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });

    }

    private void delayDispatchLoadingStatus(boolean status, long delay) {
        Observable.interval(delay, TimeUnit.MILLISECONDS)
                .take(1)
                .compose(ResponseTransformer.scheduler(this, false))
                .subscribe(new SimpleObserver<Long>() {
                    @Override
                    public void onNext(Long aLong) {
                        setLoadingStatus(status);
                    }
                });
    }
}
