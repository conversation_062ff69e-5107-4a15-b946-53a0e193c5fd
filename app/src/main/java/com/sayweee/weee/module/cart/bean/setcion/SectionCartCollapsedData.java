package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.adapter.SectionCartAdapter;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.IFrameUi;

import java.util.List;

public class SectionCartCollapsedData
        extends SimpleAdapterDataType
        implements IFrameUi {

    public int quantity;
    public List<SectionCartProductData> productData;
    public String cartId;
    public boolean isSave4Later;
    private CartFrameUiData uiData;

    public SectionCartCollapsedData() {
        super(CartSectionType.TYPE_PRODUCT_COLLAPSED);
        this.uiData = new CartFrameUiData(
                /* innerFrame= */-1,
                /* outerFrame= */SectionCartAdapter.BOTTOM_FRAME,
                /* hasTopDivider= */false
        );
    }

    public SectionCartCollapsedData setProductData(List<SectionCartProductData> productData) {
        this.productData = productData;
        return this;
    }

    public int getRemainingQuantity(int num) {
        int count = 0;
        if (productData.size() > num) {
            for (int i = num; i < productData.size(); i++) {
                count += productData.get(i).getData().quantity;
            }
        }
        return count;
    }

    public SectionCartCollapsedData setCartId(String cartId) {
        this.cartId = cartId;
        return this;
    }

    public SectionCartCollapsedData setIsSave4Later(boolean isSave4Later) {
        this.isSave4Later = isSave4Later;
        return this;
    }

    @Override
    public void setFrameUiData(CartFrameUiData uiData) {
        this.uiData = uiData;
    }

    @Override
    public CartFrameUiData getFrameUiData() {
        return uiData;
    }

}
