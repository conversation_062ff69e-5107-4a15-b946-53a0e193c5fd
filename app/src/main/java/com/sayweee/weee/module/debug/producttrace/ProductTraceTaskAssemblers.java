package com.sayweee.weee.module.debug.producttrace;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

class ProductTraceTaskAssemblers {

    static class NoOpImpl implements ProductTraceTaskAssembler {

        @Override
        public <T> void addAll(Iterable<T> iterable) {
            // No operation
        }

        @Override
        public <T> void add(T item) {
            // No operation
        }

        @NonNull
        @Override
        public List<ProductTraceTask> assemble(String topic, String pageKey) {
            return Collections.emptyList();
        }
    }

    static class RealImpl implements ProductTraceTaskAssembler {

        private final List<ProductTraceTask> tasks;
        private ProductTraceTask.Builder singleTaskBuilder;

        protected RealImpl() {
            tasks = new ArrayList<>();
        }

        @Override
        public <T> void addAll(Iterable<T> iterable) {
            if (iterable != null) {
                for (T item : iterable) {
                    add(item);
                }
            }
        }

        @Override
        public <T> void add(T item) {
            if (item instanceof ProductTraceTask.SectionProvider) {
                addSection((ProductTraceTask.SectionProvider) item);
            } else if (item instanceof ProductTraceTask.SingleProvider) {
                addSingle((ProductTraceTask.SingleProvider) item);
            }
        }

        private void addSection(ProductTraceTask.SectionProvider provider) {
            ProductTraceTask.Builder sectionBuilder = new ProductTraceTask.Builder();
            provider.assembleProductSalesTraceTask(sectionBuilder);
            tasks.addAll(sectionBuilder.build());
        }

        private void addSingle(ProductTraceTask.SingleProvider provider) {
            ProductTraceTask.Builder taskBuilder = new ProductTraceTask.Builder();
            provider.assembleProductSalesTraceTask(taskBuilder);
            if (singleTaskBuilder != null) {
                boolean merged = singleTaskBuilder.merge(taskBuilder);
                //noinspection StatementWithEmptyBody
                if (merged) {
                    // merged into existing single task, wait for next item
                } else {
                    flush();
                    singleTaskBuilder = taskBuilder;
                }
            } else {
                singleTaskBuilder = taskBuilder;
            }
        }

        @Override
        @NonNull
        public List<ProductTraceTask> assemble(String topic, String pageKey) {
            flush();
            List<ProductTraceTask> tasks = this.tasks;
            for (ProductTraceTask task : tasks) {
                task.setTopic(topic);
                task.setPageKey(pageKey);
            }
            return tasks;
        }

        private void flush() {
            if (singleTaskBuilder != null) {
                tasks.addAll(singleTaskBuilder.build());
                singleTaskBuilder = null;
            }
        }
    }

}
