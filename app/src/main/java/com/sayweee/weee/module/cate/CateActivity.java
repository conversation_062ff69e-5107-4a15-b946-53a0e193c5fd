package com.sayweee.weee.module.cate;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.collection.ArrayMap;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.WebFiltersManager;
import com.sayweee.weee.module.category.CategoryFragment;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewModel;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.CategoryConfigBean;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * Author:  ycy
 * push出来的category页面，eg:族裔分类页面
 */
public class CateActivity extends WrapperMvvmActivity<WebViewModel> {

    int type;

    public static Intent getIntent(Context context, int type, String key, HashMap<String, String> params) {
        return new Intent(context, CateActivity.class)
                .putExtra("type", type)
                .putExtra("key", key)
                .putExtra("params", params);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_cuisine;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        SharedViewModel.get().clearFilters(false);
        Intent intent = getIntent();
        type = intent.getIntExtra("type",-1);
        String key = intent.getStringExtra("key");
        FragmentManager manager = getSupportFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        //url携带参数情况
        Fragment categoryFragment = CategoryFragment.newInstance();
        Serializable serializable = intent.getSerializableExtra("params");
        if (serializable instanceof HashMap) {
            HashMap<String, String> params = (HashMap<String, String>) serializable;
            if (!EmptyUtils.isEmpty(params)) {
                String filter_sub_category = params.get("filter_sub_category");
                if (EmptyUtils.isEmpty(filter_sub_category)) {
                    filter_sub_category = params.get("category");//https://www.sayweee.com/shopping_list?category=instant
                }
                Map<String, Object> map = new ArrayMap<>();
                if (!EmptyUtils.isEmpty(filter_sub_category)) {
                    map.put(filter_sub_category, params);
                    ((CategoryFragment) categoryFragment).onPageSelected(map);
                }
            }
        }

        CategoryConfigBean dynamicConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.CATEGORY);
        boolean isOriginal = dynamicConfig != null && dynamicConfig.display_original_style;
        transaction.add(R.id.layout_content, (isOriginal || Constants.CategoryStyleType.ACTIVITY_CUISINE == type) ? CateFragment.newInstance(type, key) : categoryFragment);
        transaction.commitAllowingStateLoss();
    }

    @Override
    public void loadData() {

    }

    @Override
    public void attachModel() {

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Constants.CategoryStyleType.ACTIVITY_CUISINE == type) {
            AppAnalytics.logPageView(WeeeEvent.PageView.CUISINE, this);
        } else {
            AppAnalytics.logPageView(WeeeEvent.PageView.CATEGORY, this);
        }
    }

    @Override
    protected void onDestroy() {
        SharedViewModel.get().clearFilters(true);
        WebFiltersManager.get().clear();
        super.onDestroy();
    }
}
