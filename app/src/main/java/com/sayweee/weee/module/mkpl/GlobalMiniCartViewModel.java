package com.sayweee.weee.module.mkpl;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.cms.iml.title.data.SimpleTitleData;
import com.sayweee.weee.module.mkpl.bean.GlobalCartBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListRequest;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.mkpl.bean.GlobalCartRecommendRequest;
import com.sayweee.weee.module.mkpl.bean.GlobalCartRecommendResponse;
import com.sayweee.weee.module.mkpl.bean.GlobalMiniCartAction;
import com.sayweee.weee.module.mkpl.common.MiniCartActionData;
import com.sayweee.weee.module.mkpl.common.MiniCartAddItemData;
import com.sayweee.weee.module.mkpl.common.MiniCartItemData;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.live.UnPeekLiveData;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.function.Transform;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.reactivex.Observer;

public class GlobalMiniCartViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    private final MutableLiveData<GlobalCartListResponse> globalCartListResponseLiveData;
    private final MutableLiveData<List<AdapterDataType>> recommendItemsLiveData;
    private final MutableLiveData<GlobalMiniCartAction.PageState> globalMiniCartFragmentStateLiveData;
    private final MutableLiveData<GlobalCartRecommendResponse> globalCartRecommendResponseLiveData;
    private final MutableLiveData<SellerGroupStatusBean> sellerGroupStatusLiveData;

    private GlobalCartBean cartCachedBean;
    private GlobalCartListBean cartListCachedBean;
    private String selectedSellerId;

    public GlobalMiniCartViewModel(@NonNull Application application) {
        super(application);
        globalCartListResponseLiveData = new UnPeekLiveData<>();
        recommendItemsLiveData = new UnPeekLiveData<>();
        globalMiniCartFragmentStateLiveData = new MutableLiveData<>();
        globalCartRecommendResponseLiveData = new UnPeekLiveData<>();
        sellerGroupStatusLiveData = new MutableLiveData<>();
    }

    public String getSelectedSellerId() {
        return selectedSellerId;
    }

    public void setSelectedSellerId(String selectedSellerId) {
        this.selectedSellerId = selectedSellerId;
    }

    public LiveData<GlobalCartListResponse> getGlobalCartListResponseLiveData() {
        return globalCartListResponseLiveData;
    }

    public void notifyGlobalCartListResponseLiveDataChange(@Nullable GlobalCartListResponse response) {
        globalCartListResponseLiveData.postValue(response);
    }

    public LiveData<List<AdapterDataType>> getRecommendItemsLiveData() {
        return recommendItemsLiveData;
    }

    public LiveData<GlobalMiniCartAction.PageState> getGlobalMiniCartPageStateLiveData() {
        return globalMiniCartFragmentStateLiveData;
    }

    public void notifyGlobalMiniCartPageStateLiveDataChange(@NonNull GlobalMiniCartAction.PageState pageState) {
        GlobalMiniCartAction.PageState oldValue = globalMiniCartFragmentStateLiveData.getValue();
        if (oldValue == null || !oldValue.equals(pageState)) {
            globalMiniCartFragmentStateLiveData.setValue(pageState);
        }
    }

    public LiveData<SellerGroupStatusBean> getSellerGroupStatusLiveData() {
        return sellerGroupStatusLiveData;
    }

    public void resetSellerGroupStatusLiveData() {
        sellerGroupStatusLiveData.setValue(null);
    }

    @Nullable
    public GlobalCartBean getCartCachedBean() {
        return cartCachedBean;
    }

    public void updateCartCachedBean(GlobalCartBean cachedCart) {
        this.cartCachedBean = cachedCart;
    }

    public GlobalCartListBean getCartListCachedBean() {
        return cartListCachedBean;
    }

    public void updateCartListCachedBean(GlobalCartListBean cartListCachedBean) {
        this.cartListCachedBean = cartListCachedBean;
    }

    public void getSellerCartFloat(@Nullable String sellerId) {
        getSellerCartFloat(null, sellerId);
    }

    public void getSellerCartFloat(@Nullable String token, @Nullable String sellerId) {
        final GlobalCartListRequest request = new GlobalCartListRequest();
        request.setToken(token);
        if (!EmptyUtils.isEmpty(sellerId)) {
            request.setVendorId(sellerId);
            request.setShowProductDetail(true);
        }
        getLoader()
                .getHttpService()
                .getSellerCartFloatList(request.asQueryMap())
                .compose(ResponseTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<GlobalCartListBean>>(/* useStrictMode= */false) {

                    public void onResponse(ResponseBean<GlobalCartListBean> response) {
                        globalCartListResponseLiveData.postValue(
                                new GlobalCartListResponse(request, response, null)
                        );
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        globalCartListResponseLiveData.postValue(
                                new GlobalCartListResponse(request, null, failure)
                        );
                    }
                });
    }

    public void clearRecommendItems() {
        recommendItemsLiveData.setValue(null);
        globalCartRecommendResponseLiveData.setValue(null);
    }

    public LiveData<GlobalCartRecommendResponse> getGlobalCartRecommendResponseLiveData() {
        return globalCartRecommendResponseLiveData;
    }

    public void getSellerCartRecommend(@NonNull String sellerId) {
        final GlobalCartRecommendRequest request = new GlobalCartRecommendRequest();
        request.setSellerId(sellerId);
        getLoader().getHttpService()
                .getSellerCartRecommend(request.getSellerId())
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<PdpModulesBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PdpModulesBean> response) {
                        globalCartRecommendResponseLiveData.postValue(
                                new GlobalCartRecommendResponse(request, response, null)
                        );
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        globalCartRecommendResponseLiveData.postValue(
                                new GlobalCartRecommendResponse(request, null, failure)
                        );
                    }
                });
    }

    public List<AdapterDataType> handleGlobalCartData(
            @NonNull Context context,
            @NonNull GlobalCartBean cart,
            @Nullable String pageTarget
    ) {
        List<AdapterDataType> items = new ArrayList<>();
        if (EmptyUtils.isEmpty(cart.items)) return items;
        // top padding
        items.add(new CmsBlankData());
        // add cart items
        int productPosition = 0;
        for (NewItemBean it : cart.items) {
            MiniCartItemData item = new MiniCartItemData(R.layout.item_mkpl_cart_product, it);
            item.modNm = EagleTrackEvent.ModNm.MKPL_CART_ITEM;
            item.pageTarget = pageTarget;
            item.prodPos = productPosition++;
            items.add(item);
        }
        if (cart.group_order_button != null && cart.group_order_button.show) {
            List<MiniCartActionData.Action> actions;
            actions = CollectionUtils.arrayListOf(
                    new MiniCartActionData.Action(
                            /* title= */context.getString(R.string.s_mkpl_add_more_items),
                            /* tips= */null
                    ),
                    new MiniCartActionData.Action(
                            /* title= */cart.group_order_button.desc,
                            /* tips= */cart.group_order_button.tip
                    )
            );
            items.add(new MiniCartActionData(actions));
        } else {
            items.add(new MiniCartAddItemData());
        }

        // add recommend items
        List<AdapterDataType> recommendDataItems = getRecommendItemsLiveData().getValue();
        if (!EmptyUtils.isEmpty(recommendDataItems)) {
            items.addAll(recommendDataItems);
        }
        return items;
    }

    public List<AdapterDataType> handleSellerCartRecommendData(String sellerId, @NonNull PdpModulesBean bean) {
        if (EmptyUtils.isEmpty(bean.modules)) return CollectionUtils.emptyList();
        List<AdapterDataType> items = new ArrayList<>();
        int modPos = 1;
        for (PdpProductsBean module : bean.modules) {
            List<ProductBean> productList = module.product_list;
            if (EmptyUtils.isEmpty(productList)) continue;

            SimpleTitleData titleData = new SimpleTitleData();
            titleData.setTitle(module.title);
            items.add(titleData);
            items.add(new CmsBlankData());
            for (int prodPos = 0; prodPos < productList.size(); prodPos++) {
                ProductItemData item = new ProductItemData(productList.get(prodPos));
                item.type = CmsItemType.PRODUCT_ITEM;
                item.setModNm(EagleTrackEvent.ModNm.MKPL_CART_RECOMMEND);
                item.setModPos(modPos);
                item.setProdPos(prodPos);
                item.source = ProductHelper.getAddCartSource(item.getEventKey(), sellerId);
                items.add(item);
            }
            modPos++;
        }
        recommendItemsLiveData.setValue(items);
        return items;
    }

    @Nullable
    public Map<String, Object> getCartImpressionContent(@Nullable GlobalCartBean cart) {
        if (cart == null) return null;

        Map<String, Object> map = new ArrayMap<>();
        map.put("type", "seller");
        map.put("delivery_pickup_date", cart.shipping_info != null ? cart.shipping_info.delivery_pickup_date : null);
        map.put("delivery_mode", cart.shipping_info != null ? cart.shipping_info.delivery_mode : null);
        map.put("deal_id", null);
        map.put("vendor_id", cart.vendor_info != null ? cart.vendor_info.vendor_id : null);
        map.put("sub_total_price", cart.fee_info != null ? cart.fee_info.sub_total_price : null);
        map.put("total_price_with_activity", cart.fee_info != null ? cart.fee_info.total_price_with_activity : null);
        map.put("total_price_with_activity_and_coupon", cart.fee_info != null ?
                cart.fee_info.total_price_with_activity_and_coupon : null);
        map.put("shipping_fee", cart.shipping_info != null ? cart.shipping_info.shipping_fee : 0);
        map.put("shipping_desc", cart.shipping_info != null ? cart.shipping_info.shipping_desc : null);
        map.put("quantity", cart.quantity);
        map.put("orignal_shipping_fee", cart.shipping_info != null ? cart.shipping_info.orignal_shipping_fee : null);
        map.put("hotdish_wave", null);

        // items
        List<Map<String, Object>> itemsMaps = new ArrayList<>();
        List<NewItemBean> items = cart.items != null ? cart.items : CollectionUtils.emptyList();
        Transform<NewItemBean, Map<String, Object>> mapper = item -> {
            Map<String, Object> m = new ArrayMap<>();
            m.put("product_id", item.product_id);
            m.put("quantity", item.quantity);
            m.put("source", item.source);
            m.put("price", item.price);
            m.put("base_price", item.base_price > 0 ? item.base_price : null);
            m.put("reason_type", item.reason_type);
            m.put("status", item.status);
            m.put("volume_price_support", item.volume_price_support);
            return m;
        };
        for (NewItemBean item : items) {
            itemsMaps.add(mapper.transform(item));
            List<NewItemBean> activityItems = item.getActivityItems();
            for (NewItemBean activityItem : activityItems) {
                itemsMaps.add(mapper.transform(activityItem));
            }
        }
        if (!EmptyUtils.isEmpty(itemsMaps)) {
            map.put("items", itemsMaps);
        } else {
            map.put("items", null);
        }
        // trade_in_items
        map.put("trade_in_items", null);
        return map;
    }

    public void checkGroupOrderExists() {
        Observer<ResponseBean<SellerGroupStatusBean>> observer;
        observer = new ResponseObserver<ResponseBean<SellerGroupStatusBean>>() {

            @Override
            public void onBegin() {
                super.onBegin();
                setLoadingStatus(true);
            }

            @Override
            public void onResponse(ResponseBean<SellerGroupStatusBean> response) {
                sellerGroupStatusLiveData.postValue(response.getData());
            }

            @Override
            public void onFinish() {
                super.onFinish();
                setLoadingStatus(false);
            }
        };

        getLoader().getHttpService()
                .getSellerGroupStatus()
                .compose(ResponseTransformer.scheduler())
                .subscribe(observer);
    }
}
