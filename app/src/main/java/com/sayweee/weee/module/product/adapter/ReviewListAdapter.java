package com.sayweee.weee.module.product.adapter;

import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class ReviewListAdapter extends BaseQuickAdapter<PostCategoryBean.ListBean, AdapterViewHolder> implements ImpressionChild, EagleImpressionAdapter {
    private RecyclerView attachView;
    private int modPos;

    public ReviewListAdapter() {
        super(R.layout.item_pdp_review_list);
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setLayoutParamsMargin(holder, CommonTools.dp2px(20));
    }

    protected void setLayoutParamsMargin(AdapterViewHolder holder, int margin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) holder.itemView.getLayoutParams();
        layoutParams.leftMargin = margin;
        layoutParams.rightMargin = margin;
        layoutParams.topMargin = holder.getLayoutPosition() == 0 ? 0 : CommonTools.dp2px(12);
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, PostCategoryBean.ListBean bean) {
        helper.loadImageDefault(mContext, R.id.iv_icon, WebpManager.convert(ImageSpec.SPEC_PRODUCT, EmptyUtils.isEmpty(bean.pictures) ? bean.product_image_url : bean.pictures.get(0)));
        helper.setText(R.id.tv_name, bean.user_name);
        helper.setText(R.id.tv_content, bean.comment_lang);
        helper.setText(R.id.tv_date, DateUtils.formatReviewData(mContext, bean.rec_create_time));

        helper.setVisibleCompat(R.id.tv_rating, helper.getLayoutPosition() != getData().size() - 1);
    }

    @Override
    public void setAttachView(RecyclerView attachView) {
        this.attachView = attachView;

    }

    @Override
    public RecyclerView getAttachView() {
        return attachView;
    }

    public ReviewListAdapter setModPos(int modPos) {
        this.modPos = modPos;
        return this;
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && start <= end) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start), start);
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i), i);
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    private ImpressionBean getEagleImpressionEvent(Object item, int position) {
        if (!(item instanceof PostCategoryBean.ListBean)) return null;
        PostCategoryBean.ListBean bean = (PostCategoryBean.ListBean) item;
        String eventKey = "reviews_carousel" + "_" + position + "_" + bean.id;

        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm("reviews_carousel")
                .setMod_pos(modPos)
                .setBanner_id(bean.id)
                .setUrl(bean.link)
                .setBanner_pos(position)
                .setBanner_type(bean.type)
                .build()
                .getParams();
        return new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, eventKey);
    }
}
