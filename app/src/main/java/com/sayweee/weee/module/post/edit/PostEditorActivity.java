package com.sayweee.weee.module.post.edit;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.danikula.videocache.HttpProxyCacheServer;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.EditPostManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.launch.service.ExperimentManager;
import com.sayweee.weee.module.post.adapter.CommentEtAdapter;
import com.sayweee.weee.module.post.bean.AtBean;
import com.sayweee.weee.module.post.bean.EditorTranslationData;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.edit.adapter.PostEditorAdapter;
import com.sayweee.weee.module.post.edit.bean.CommitLanguageBean;
import com.sayweee.weee.module.post.edit.bean.EditorContentData;
import com.sayweee.weee.module.post.edit.bean.EditorHashTagData;
import com.sayweee.weee.module.post.edit.bean.EditorHeaderData;
import com.sayweee.weee.module.post.edit.bean.EditorProductData;
import com.sayweee.weee.module.post.edit.bean.EditorProductItemData;
import com.sayweee.weee.module.post.edit.bean.EditorTitleData;
import com.sayweee.weee.module.post.edit.bean.LanguageBean;
import com.sayweee.weee.module.post.edit.bean.NotifyBean;
import com.sayweee.weee.module.post.edit.bean.SuggestTranslationsBean;
import com.sayweee.weee.module.post.edit.service.PostEditorViewModel;
import com.sayweee.weee.module.post.edit.service.bean.HashTagItemBean;
import com.sayweee.weee.module.post.edit.service.bean.PostDraftData;
import com.sayweee.weee.module.post.product.PostAttachActivity;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.player.AppVideoManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.guide.GuideHelper;
import com.sayweee.weee.service.helper.PictureSelectorHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DefaultTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.FileUtils;
import com.sayweee.weee.utils.ScrollTopScroller;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.CircularProgressView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.http.support.Utils;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.KeyboardUtils;
import com.sayweee.widget.toaster.Toaster;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * Author:  winds
 * Date:    2021/11/30.
 * Desc:
 */
public class PostEditorActivity extends WrapperMvvmActivity<PostEditorViewModel> {

    private static final int REQUEST_ADD_ITEMS = 888;
    private static final int REQUEST_PREVIEW = 999;
    private static final int REQUEST_HASH_TAG = 777;
    private static final int REQUEST_TRANSLATE = 666;
    private static final int SELECT_COVER = 99;
    public static final int TYPE_ADD = 100;
    public static final int TYPE_PDP_ADD = 101;
    public static final int TYPE_EMAIL_ADD = 102;
    public static final int TYPE_EDIT = 200;
    public static final int TYPE_DRAFT = 300;

    public static final String FROM_TYPE = "fromType";
    public static final String MEDIA_DATA = "mediaData";
    public static final String FROM_SOURCE = "source";
    public static final String DRAFT_DATA = "draftData";
    public static final String EDIT_DATA = "editData";
    public static final String ATTACHED_PRODUCT = "attachedProduct";
    public static final String ATTACHED_HASH_TAG = "attachHashTag";
    public static final String PRODUCT_IDS = "product_ids";
    public static final String STATUS_DRAFT = "D";
    public static final String STATUS_PUBLISHED = "P";
    public static final String STATUS_REVIEW = "A";
    public static final String TYPE_VIDEO = "video";

    int type;
    TextView btnCommit;
    RecyclerView mRecyclerView;
    PostEditorAdapter adapter;
    private View llAt;
    private ImageView ivAt;
    private View vLine;
    private KeyboardChangeHelper helper;
    private RecyclerView mRxAt;
    private CommentEtAdapter mentionAdapter;
    private LinearLayout llDownload;
    private FrameLayout layoutProgress;
    private CircularProgressView cpvProgress;
    private TextView tvProgress;
    private TextView tvProgressStatus;
    private boolean isDownload;
    private String compressPath;
    private NotifyBean notifyBean;
    private SuggestTranslationsBean suggestTranslationsBean;
    private String attachedProductId;

    public static Intent getIntentOnAdd(Context context, LocalMedia media, List<HashTagItemBean> tags, String source) {
        return new Intent(context, PostEditorActivity.class)
                .putExtra(FROM_TYPE, TYPE_ADD)
                .putExtra(MEDIA_DATA, media)
                .putExtra(FROM_SOURCE, source)
                .putExtra(ATTACHED_HASH_TAG, !EmptyUtils.isEmpty(tags) ? new ArrayList<>(tags) : null);
    }

    public static Intent getIntentOnPdpAdd(Context context, LocalMedia media, List<ProductBean> products, String source) {
        Intent a = new Intent(context, PostEditorActivity.class);
        a.putExtra(FROM_TYPE, TYPE_PDP_ADD);
        a.putExtra(FROM_SOURCE, source);
        a.putExtra(MEDIA_DATA, media);
        if (!EmptyUtils.isEmpty(products)) {
            a.putExtra(ATTACHED_PRODUCT, new ArrayList<>(ProductHelper.simplifyList(products)));
        }
        return a;
    }

    public static Intent getIntentOnEmailAdd(Context context, String productIds, String source) {
        return new Intent(context, PostEditorActivity.class)
                .putExtra(FROM_TYPE, TYPE_EMAIL_ADD)
                .putExtra(PRODUCT_IDS, productIds)
                .putExtra(FROM_SOURCE, source);
    }

    public static Intent getIntentOnDraft(Context context, PostDraftData data) {
        return new Intent(context, PostEditorActivity.class).putExtra(FROM_TYPE, TYPE_DRAFT).putExtra(DRAFT_DATA, data);
    }

    public static Intent getIntentOnEdit(Context context, PostBean post, List<ProductBean> products, List<HashTagItemBean> tags) {
        Intent a = new Intent(context, PostEditorActivity.class);
        a.putExtra(FROM_TYPE, TYPE_EDIT);
        a.putExtra(EDIT_DATA, post);
        if (!EmptyUtils.isEmpty(products)) {
            a.putExtra(ATTACHED_PRODUCT, new ArrayList<>(ProductHelper.simplifyList(products)));
        }
        if (!EmptyUtils.isEmpty(tags)) {
            a.putExtra(ATTACHED_HASH_TAG, new ArrayList<>(tags));
        }
        return a;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_post_editor;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        setWrapperDivider(null);
        getContentView().setBackgroundColor(Color.WHITE);
        setWrapperTitle(R.string.edit_post);
        btnCommit = findViewById(R.id.btn_commit);
        mRecyclerView = findViewById(R.id.mRecyclerView);
        mRxAt = findViewById(R.id.rv_at);
        llAt = findViewById(R.id.ll_at);
        ivAt = findViewById(R.id.iv_at);
        vLine = findViewById(R.id.v_line);
        llDownload = findViewById(R.id.ll_download);
        layoutProgress = findViewById(R.id.layout_progress);
        cpvProgress = findViewById(R.id.cpv_progress);
        tvProgress = findViewById(R.id.tv_progress);
        tvProgressStatus = findViewById(R.id.tv_progress_status);
        suggestTranslationsBean = new SuggestTranslationsBean();
        findViewById(R.id.iv_close_keyword).setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                keyboardHide();
            }
        });
        ivAt.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                setContentAt();
            }
        });
        btnCommit.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (!adapter.hasTitle()) {
                    Toaster.showToast(getString(R.string.s_please_enter_title));
                    return;
                }
                if (!adapter.canPost()) {
                    return;
                }
                if (AccountManager.get().isNotifyGuided()) {
                    execCommit();
                } else {
                    viewModel.getNotify();
                }
            }
        });
        adapter = new PostEditorAdapter();
        RecyclerView.LayoutManager layoutManager;
        layoutManager = new SafeStaggeredGridLayoutManager(1, StaggeredGridLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(adapter);
        mRecyclerView.setItemAnimator(null);
        View footerView = new Space(view.getContext());
        footerView.setLayoutParams(
                new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        CommonTools.dp2px(100)
                )
        );
        adapter.setFooterView(footerView);

        mRxAt.setLayoutManager(new LinearLayoutManager(activity));
        mentionAdapter = new CommentEtAdapter();
        mRxAt.setAdapter(mentionAdapter);
        View empty = ViewTools.getHelperView(activity, R.layout.common_empty_mention_list, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                helper.getView(R.id.iv_empty).setVisibility(View.GONE);
            }
        });
        mentionAdapter.setEmptyView(empty);
        mentionAdapter.isUseEmpty(false);
        setKeyboardObserver();
        mentionAdapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                AdapterDataType item = mentionAdapter.getItem(position);
                if (item instanceof AtBean) {
                    AtBean bean = (AtBean) item;
                    adapter.setEtContent(String.valueOf(bean.id), bean.alias);
                    showMentionList(false);
                }
            }
        });

        adapter.setOnPostEditorListener(new PostEditorAdapter.OnPostEditorListener() {
            @Override
            public void onVideoPreview(EditorHeaderData data) {
                startActivityForResult(PreviewVideoActivity.getIntent(activity, data.image, data.video, data.canDelete()), REQUEST_PREVIEW);
            }

            @Override
            public void onVideoPickClick() {
                execVideoPicker();
            }

            @Override
            public void onProductSectionClick(EditorProductData data) {
                startActivityForResult(PostAttachActivity.getIntent(activity, PostAttachActivity.TYPE_PRODUCT, false, data.list), REQUEST_ADD_ITEMS);
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        keyboardHide();
                        adapter.clearFocus();
                    }
                }, 500);
            }

            @Override
            public void onProductItemRemove(EditorProductItemData item, int position) {
                viewModel.onProductItemRemoved(item);
                adapter.notifyItemRangeRemoved(position, 1);
            }

            @Override
            public void onTagsSectionClick(EditorHashTagData data) {
                startActivityForResult(AddHashtagsActivity.getIntent(activity, data.hashTagList), REQUEST_HASH_TAG);
                mRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        keyboardHide();
                        adapter.clearFocus();
                    }
                }, 500);

            }

            @Override
            public void onStepChanged(boolean enable) {
                setCommitEnable(enable);
            }

            @Override
            public void onGoToCover(String path) {
                if (type != TYPE_EDIT) {
                    startActivityForResult(SelectCoverActivity.getIntent(activity, EmptyUtils.isEmpty(compressPath) ? path : compressPath), SELECT_COVER);
                } else {
                    HttpProxyCacheServer proxy = AppVideoManager.getCacheProxy(activity, null);
                    if (proxy.isCached(path)) {
                        String cachedPath = proxy.getProxyUrl(path);
                        startActivityForResult(SelectCoverActivity.getIntent(activity, cachedPath), SELECT_COVER);
                    } else {
                        if (Utils.isNetworkConnected(activity)) {
                            String proxyUrl = proxy.getProxyUrl(path);
                            viewModel.downloadVideo(activity, proxyUrl);
                            isDownload = true;
                        } else {
                            downloadError();
                        }
                    }
                }
            }

            @Override
            public void onContentKeyWordVisible(boolean hasFocus) {
//                hasFocus = llAt.getVisibility() != View.VISIBLE && hasFocus;
                contentKeyWordChange(hasFocus);
            }

            @Override
            public void onTitleKeyWordVisible(boolean hasFocus) {
                hasFocus = llAt.getVisibility() != View.VISIBLE && hasFocus;
                titleKeyWordChange(hasFocus);
            }

            @Override
            public void OnAtCharacterInput(String content) {
                viewModel.getMentionList(content, AccountManager.get().getUID());
            }

            @Override
            public void OnAtCharacterInputStop() {
                showMentionList(false);
            }

            @Override
            public void onContentInputChange(String text) {
                if (EmptyUtils.isEmpty(text) || !text.contains("@")) {
                    showMentionList(false);
                }
            }

            @Override
            public void onTranslationClick(boolean canClick) {
                if (canClick) {
                    if ((!EmptyUtils.isEmpty(suggestTranslationsBean.title) && !suggestTranslationsBean.title.equalsIgnoreCase(adapter.getPostTitle())) || (!EmptyUtils.isEmpty(suggestTranslationsBean.desc) && !suggestTranslationsBean.desc.equalsIgnoreCase(adapter.getPostContent()))) {
                        clearTranslation();
                    }
                    suggestTranslationsBean.title = adapter.getPostTitle();
                    suggestTranslationsBean.desc = adapter.getPostContent();
                    startActivityForResult(SuggestTranslationsActivity.getIntent(activity, suggestTranslationsBean), REQUEST_TRANSLATE);
                    mRecyclerView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            keyboardHide();
                            adapter.clearFocus();
                        }
                    }, 500);
                } else {
                    Toaster.showToast(getString(R.string.s_please_enter_title));
                }
            }
        });

    }


    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true, true);
    }

    @Override
    public void loadData() {
        ConfigService.get().fetchExperiment(ExperimentManager.ID_SEARCH_RESULTS_GRID);

        Intent intent = getIntent();
        type = intent.getIntExtra(FROM_TYPE, -1);
        if (type == TYPE_ADD || type == TYPE_PDP_ADD) {
            viewModel.getLanguageList();
            Parcelable parcelable = intent.getParcelableExtra(MEDIA_DATA);
            LocalMedia mediaData = null;
            if (parcelable instanceof LocalMedia) {
                mediaData = (LocalMedia) parcelable;
            }
            if (mediaData == null) {
                finish();
                return;
            }
            Serializable serializableProducts = intent.getSerializableExtra(ATTACHED_PRODUCT);
            List<SimpleProductBean> products = null;
            if (serializableProducts instanceof List) {
                products = (List<SimpleProductBean>) serializableProducts;
                if (type == TYPE_PDP_ADD && !EmptyUtils.isEmpty(products)) {
                    attachedProductId = String.valueOf(products.get(0).id);
                }
            }
            Serializable serializableTags = intent.getSerializableExtra(ATTACHED_HASH_TAG);
            List<HashTagItemBean> tags = null;
            String source = intent.getStringExtra(FROM_SOURCE);
            if (serializableTags instanceof List) {
                tags = (List<HashTagItemBean>) serializableTags;
            }
            viewModel.initDataByAdd(type, mediaData, tags, products, source);
        } else if (type == TYPE_EMAIL_ADD) {
            String productIds = intent.getStringExtra(PRODUCT_IDS);
            viewModel.getLanguageList();
            viewModel.getProductList(productIds);
        } else if (type == TYPE_EDIT) {
            viewModel.getLanguageList();
            Serializable serializable = intent.getSerializableExtra(EDIT_DATA);
            PostBean editData = null;
            if (serializable instanceof PostBean) {
                editData = (PostBean) serializable;
            }
            if (editData == null) {
                finish();
                return;
            }
            Serializable serializableProducts = intent.getSerializableExtra(ATTACHED_PRODUCT);
            List<SimpleProductBean> products = null;
            if (serializableProducts instanceof List) {
                products = (List<SimpleProductBean>) serializableProducts;
            }
            Serializable serializableTags = intent.getSerializableExtra(ATTACHED_HASH_TAG);
            List<HashTagItemBean> tags = null;
            if (serializableTags instanceof List) {
                tags = (List<HashTagItemBean>) serializableTags;
            }
            viewModel.initDataByEdit(type, editData, products, tags);
        } else if (type == TYPE_DRAFT) {

            Serializable serializable = intent.getSerializableExtra(DRAFT_DATA);
            PostDraftData draftData = null;
            if (serializable instanceof PostDraftData) {
                draftData = (PostDraftData) serializable;
            }
            if (draftData == null) {
                finish();
                return;
            }
            suggestTranslationsBean = ((PostDraftData) serializable).suggestTranslationsBean;
            viewModel.initDataByDraft(type, draftData);
            viewModel.getLanguageList();
        } else {
            finish();
        }


    }

    @Override
    public void attachModel() {
        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                toSelectCoverGuided(list);
                adapter.setAdapterData(list);
            }
        });

        viewModel.uploadReadyData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean ready) {
                if (ready) {
                    startActivity(ProfileActivity.getIntent(activity)
                            .putExtra("status", STATUS_PUBLISHED)
                            .putExtra("notifyBean", notifyBean)
//                                .putExtra("type",TYPE_VIDEO)
                            .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP));

                    finish();
                }
            }
        });

        viewModel.draftReadyData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean ready) {
                if (ready) {
                    Intent a = ProfileActivity.getIntent(activity, STATUS_DRAFT);
                    a.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    startActivity(a);
                    finish();
                }
            }
        });

        viewModel.editResultData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                if (type == TYPE_EDIT) {
//                    setResult(RESULT_OK, new Intent().putExtra("status", STATUS_PUBLISHED).putExtra("type", TYPE_VIDEO).putExtra("notifyBean", notifyBean));
                    startActivity(ProfileActivity.getIntent(activity)
                            .putExtra("status", STATUS_PUBLISHED)
                            .putExtra("notifyBean", notifyBean)
//                                .putExtra("type",TYPE_VIDEO)
                            .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP));
                }
                finish();
            }
        });

        viewModel.atData.observe(this, new Observer<List<AtBean>>() {
            @Override
            public void onChanged(List<AtBean> atBeans) {
                if (EmptyUtils.isEmpty(adapter.getPostText()) || !adapter.getPostText().contains("@")) {
                    return;
                }
                showMentionList(true);
                mentionAdapter.isUseEmpty(EmptyUtils.isEmpty(atBeans));
                mentionAdapter.setAdapterData(atBeans);
                if (!EmptyUtils.isEmpty(atBeans)) {
                    mRxAt.scrollToPosition(0);
                }
            }
        });

        viewModel.downloadStart.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                downLoadChange(true);
                tvProgressStatus.setText(getString(R.string.video_loading));
            }
        });

        viewModel.downloadProgress.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                tvProgress.setText(integer + "%");
                cpvProgress.setProgress(integer);
            }
        });

        viewModel.downloadFinish.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                tvProgressStatus.setText(getString(R.string.loading_complete));
                downLoadChange(false);
                compressPath = s;
                startActivityForResult(SelectCoverActivity.getIntent(activity, compressPath), SELECT_COVER);
            }
        });

        viewModel.downloadError.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                downloadError();
            }
        });

        viewModel.notifyData.observe(this, new Observer<NotifyBean>() {
            @Override
            public void onChanged(NotifyBean result) {
                notifyBean = result;
                execCommit();
            }
        });

        viewModel.languageData.observe(this, new Observer<LanguageBean>() {
            @Override
            public void onChanged(LanguageBean languageBean) {
                if (EmptyUtils.isEmpty(languageBean) || EmptyUtils.isEmpty(languageBean.language) || suggestTranslationsBean == null) {
                    return;
                }
                if (EmptyUtils.isEmpty(suggestTranslationsBean.i18n_info)) {
                    suggestTranslationsBean.i18n_info = new ArrayList<>();
                    for (int i = 0; i < languageBean.language.size(); i++) {
                        LanguageBean.LanguageList item = languageBean.language.get(i);
                        if (item == null) {
                            break;
                        }
                        SuggestTranslationsBean.Language language = new SuggestTranslationsBean.Language();
                        language.label = item.label;
                        language.lang = item.key;
                        if (i == 0) {
                            language.checked = true;
                        }
                        suggestTranslationsBean.i18n_info.add(language);
                    }
                    int i = -1;
                    List<SuggestTranslationsBean.Language> itemList = EditPostManager.getTranslationData();
                    if (!EmptyUtils.isEmpty(itemList)) {
                        suggestTranslationsBean.i18n_info.get(0).checked = false;
                        for (SuggestTranslationsBean.Language item : suggestTranslationsBean.i18n_info) {
                            i++;
                            if (item.lang.equalsIgnoreCase(itemList.get(0).lang)) {
                                suggestTranslationsBean.i18n_info.add(0, suggestTranslationsBean.i18n_info.remove(i));
//                            Collections.swap(suggestTranslationsBean.i18n_info, 0, i);
                                suggestTranslationsBean.i18n_info.get(0).checked = true;
                                return;
                            }
                        }
                        suggestTranslationsBean.i18n_info.get(0).checked = true;
                    }

                } else {
                    List<SuggestTranslationsBean.Language> oldList = new ArrayList<>();
                    oldList.addAll(suggestTranslationsBean.i18n_info);
                    suggestTranslationsBean.i18n_info.clear();
                    for (int i = 0; i < languageBean.language.size(); i++) {
                        LanguageBean.LanguageList item = languageBean.language.get(i);
                        if (item == null) {
                            break;
                        }
                        SuggestTranslationsBean.Language language = new SuggestTranslationsBean.Language();
                        language.label = item.label;
                        language.lang = item.key;
                        suggestTranslationsBean.i18n_info.add(language);
                    }
                    List<SuggestTranslationsBean.Language> itemList = EditPostManager.getTranslationData();
                    if (!EmptyUtils.isEmpty(itemList)) {
                        for (SuggestTranslationsBean.Language item : suggestTranslationsBean.i18n_info) {
                            for (SuggestTranslationsBean.Language oldItem : oldList) {
                                if (item.lang.equalsIgnoreCase(oldItem.lang)) {
                                    item.title = oldItem.title;
                                    item.description = oldItem.description;
                                    item.isFirst = oldItem.isFirst;
                                }
                            }
                        }
                        int i = -1;
                        suggestTranslationsBean.i18n_info.get(0).checked = false;
                        for (SuggestTranslationsBean.Language item : suggestTranslationsBean.i18n_info) {
                            i++;
                            if (item.lang.equalsIgnoreCase(itemList.get(0).lang)) {
                                suggestTranslationsBean.i18n_info.add(0, suggestTranslationsBean.i18n_info.remove(i));
//                            Collections.swap(suggestTranslationsBean.i18n_info, 0, i);
                                suggestTranslationsBean.i18n_info.get(0).checked = true;
                                return;
                            }
                        }
                        suggestTranslationsBean.i18n_info.get(0).checked = true;
                    }
                }
            }
        });

        viewModel.productList.observe(this, new Observer<List<SimpleProductBean>>() {
            @Override
            public void onChanged(List<SimpleProductBean> productBeans) {
                Intent intent = getIntent();
                String source = intent.getStringExtra(FROM_SOURCE);
                viewModel.initDataByEmailAdd(type, productBeans, source);
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case REQUEST_PREVIEW:
                    viewModel.onVideoPickChanged(null);
                    break;
                case REQUEST_ADD_ITEMS:
                    Serializable serializable = data.getSerializableExtra("simple_products");
                    if (serializable instanceof List) {
                        viewModel.onProductPickChanged((List<SimpleProductBean>) serializable);
                    }
                    break;
                case REQUEST_HASH_TAG:
                    Serializable serializableExtra = data.getSerializableExtra(AddHashtagsActivity.HASHTAGS_KEY);
                    if (serializableExtra instanceof List) {
                        viewModel.onTagsPickChanged((List<HashTagItemBean>) serializableExtra);
                    }
                    break;

                case SELECT_COVER:
                    String path = data.getStringExtra("path");
                    if (type == TYPE_EDIT) {
                        compressPath = path;
                    }
                    long cursor = data.getLongExtra("cursor", 0);
                    adapter.updatePhoto(path, cursor);
                    break;

                case REQUEST_TRANSLATE:
                    suggestTranslationsBean = (SuggestTranslationsBean) data.getSerializableExtra("bean");
                    int position = data.getIntExtra("position", 0);
                    int lastSavePosition = data.getIntExtra("lastSavePosition", 0);
                    boolean toToast = data.getBooleanExtra("toToast", false);
                    boolean isSaved = data.getBooleanExtra("isSaved", false);
                    if (EmptyUtils.isEmpty(suggestTranslationsBean) || EmptyUtils.isEmpty(suggestTranslationsBean.i18n_info) || suggestTranslationsBean.i18n_info.size() <= 0) {
                        return;
                    }
                    if (toToast) {
                        Toaster.showToast(getString(R.string.translation_saved));
                    }
                    if (lastSavePosition <= suggestTranslationsBean.i18n_info.size() - 1) {
                        suggestTranslationsBean.i18n_info.add(0, suggestTranslationsBean.i18n_info.remove(lastSavePosition));
                    }
                    List<CommitLanguageBean> languageBeans = new ArrayList<>();
                    for (SuggestTranslationsBean.Language item : suggestTranslationsBean.i18n_info) {
                        item.isFirst = false;
                        item.checked = false;
                        if (!EmptyUtils.isEmpty(item.title)) {
                            CommitLanguageBean bean = new CommitLanguageBean();
                            bean.title = item.title;
                            bean.description = item.description;
                            bean.lang = item.lang;
                            languageBeans.add(bean);
                        }
                    }
                    suggestTranslationsBean.i18n_info.get(0).isFirst = true;
                    suggestTranslationsBean.i18n_info.get(0).checked = true;
                    EditPostManager.setTranslationData(suggestTranslationsBean.i18n_info);
                    if (languageBeans.size() > 0) {
                        int i = -1;
                        for (AdapterDataType item : adapter.getData()) {
                            i++;
                            if (item instanceof EditorTranslationData) {
                                ((EditorTranslationData) item).setTranslation(languageBeans);
                                if (isSaved) {
                                    ((EditorTranslationData) item).setUpdateVisible(false);
                                    adapter.setUpdateVisible(true);
                                }
                                adapter.notifyItemChanged(i);
                            }
                        }
                    }
                    break;
            }
        }
    }

    @Override
    public boolean isShouldHideInput(View v, MotionEvent event) {
        if (DefaultTools.isTouchTarget(event, ivAt, mRxAt, adapter.getTag(), adapter.getLayoutProduct(), adapter.getLayoutTranslation())) {
            return false;
        }
        return super.isShouldHideInput(v, event);
    }

    @Override
    public void onBackPressed() {
        showConfirmDialog();
    }

    @Override
    protected void onDestroy() {
        if (!EmptyUtils.isEmpty(compressPath)) {
            FileUtils.deleteFile(new File(compressPath));
        }
        if (helper != null) {
            helper.endObserve();
        }

        adapter.removeTextWatcher();
        super.onDestroy();
    }

    private void closeKeyWord() {
        View view = getWindow().peekDecorView();
        KeyboardUtils.setKeyboardVisible(this, view, false);
        getWrapperTitle().setVisible(R.id.layout_wrapper_title, true);
        contentToTop(0);
//        adapter.clearFocus();
    }

    private void showMentionList(boolean visible) {
        mRxAt.setVisibility(visible ? View.VISIBLE : View.GONE);
        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType item = adapter.getData().get(i);
            if (item instanceof EditorContentData) {
                contentToTop(i);
                return;
            }
        }
    }

    private void setContentAt() {
        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType item = adapter.getData().get(i);
            if (item instanceof EditorContentData) {
                adapter.setAt();
                contentToTop(i);
                return;
            }
        }
    }

    private void setCommitEnable(boolean enable) {
        if (btnCommit != null) {
            btnCommit.setBackgroundResource(enable ? R.drawable.selector_btn_status_corner : R.drawable.bg_check_out_btn_grey);
        }
    }

    private void toSelectCoverGuided(List<AdapterDataType> list) {
        if (!AccountManager.get().isSelectCoverGuided()) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) instanceof EditorHeaderData) {
                    EditorHeaderData item = (EditorHeaderData) list.get(i);
                    if (!EmptyUtils.isEmpty(item.video)) {
                        GuideHelper.showSelectCoverGuide(activity, item.image);
                    }
                    break;
                }
            }
        }
    }

    private void execVideoPicker() {
        PictureSelectorHelper.showVideoSelector(activity, new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(List<LocalMedia> list) {
                if (!EmptyUtils.isEmpty(list) && viewModel != null) {
                    viewModel.onVideoPickChanged(list.get(0));
                }
            }

            @Override
            public void onCancel() {

            }
        });
    }

    protected void setKeyboardObserver() {
        helper = new KeyboardChangeHelper(getContentView());
        helper.startObserve().disableResize(true).setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
//                getWrapperTitle().setVisible(R.id.layout_wrapper_title, true);
//                adapter.clearFocus();
                llAt.setVisibility(View.GONE);
                btnCommit.setVisibility(View.VISIBLE);
            }

            @Override
            public void onKeyboardShow() {
                llAt.setVisibility(View.VISIBLE);
                btnCommit.setVisibility(View.GONE);
            }
        });
    }

    private void keyboardShow() {
        getWrapperTitle().setVisible(R.id.layout_wrapper_title, false);
        int i = -1;
        for (AdapterDataType item : adapter.getData()) {
            i++;
            if (item instanceof EditorHeaderData) {
                adapter.setPhotoVisible(false, i);
            } else if (item instanceof EditorTitleData) {
                adapter.setTitleVisible(false, i);
            }
        }
        contentToTop(0);
        mRecyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                View view = getWindow().peekDecorView();
                KeyboardUtils.setKeyboardVisible(activity, view, true);
            }
        }, 100L);
    }

    private void keyboardHide() {
        getWrapperTitle().setVisible(R.id.layout_wrapper_title, true);
        int i = -1;
        for (AdapterDataType item : adapter.getData()) {
            i++;
            if (item instanceof EditorHeaderData) {
                adapter.setPhotoVisible(true, i);
            } else if (item instanceof EditorTitleData) {
                adapter.setTitleVisible(true, i);
            }
        }
        contentToTop(0);
        mRecyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                View view = getWindow().peekDecorView();
                KeyboardUtils.setKeyboardVisible(activity, view, false);
            }
        }, 100L);
    }

    private void contentKeyWordChange(boolean hasFocus) {
        if (hasFocus) {
            keyboardShow();
        } else {
            keyboardHide();
        }

    }

    private void titleKeyWordChange(boolean hasFocus) {
        if (hasFocus) {
            adapter.setPhotoVisible(true, 0);
            adapter.setTitleVisible(true, 1);
            vLine.setVisibility(View.GONE);
            ivAt.setVisibility(View.GONE);
            llAt.setVisibility(View.VISIBLE);
            mRxAt.setVisibility(View.GONE);
        }
    }

    private void contentToTop(int i) {
        RecyclerView.LayoutManager layoutManager = mRecyclerView != null ? mRecyclerView.getLayoutManager() : null;
        if (layoutManager == null) return;

        RecyclerView.SmoothScroller scroller = new ScrollTopScroller(activity);
        scroller.setTargetPosition(i);
        layoutManager.startSmoothScroll(scroller);
        vLine.setVisibility(View.VISIBLE);
        ivAt.setVisibility(!VariantConfig.IS_VIEW_VISIBLE ? View.GONE : View.VISIBLE);
    }

    private void execCommit() {
        List<AdapterDataType> list = adapter.getData();
        if (!EmptyUtils.isEmpty(list) && viewModel != null) {
            for (int i = 0; i < adapter.getData().size(); i++) {
                AdapterDataType item = adapter.getData().get(i);
                if (item instanceof EditorContentData) {
                    ((EditorContentData) item).content = adapter.getPostContent();
                } else if (item instanceof EditorTranslationData) {
                    ((EditorTranslationData) item).setUpdateVisible(false);
                }
            }
            if (type == TYPE_EDIT) {
                if (EmptyUtils.isEmpty(compressPath)) {
                    viewModel.execPostEdit(list, compressPath);
                } else {
                    viewModel.uploadImg(list, compressPath);
                }
            } else {
                int hashtagCount = 0;
                int itemCount = 0;
                for (int i = 0; i < adapter.getData().size(); i++) {
                    AdapterDataType item = adapter.getData().get(i);
                    if (item instanceof EditorHashTagData) {
                        EditorHashTagData bean = ((EditorHashTagData) item);
                        hashtagCount = EmptyUtils.isEmpty(bean.hashTagList) ? 0 : bean.hashTagList.size();
                    } else if (item instanceof EditorProductItemData) {
                        itemCount++;
                    }
                }
                Map<String, Object> content = new TrackParams()
                        .put("hashtag_count", hashtagCount)
                        .put("itemCount", itemCount)
                        .get();
                AppAnalytics.logPostVideoAction(new EagleTrackModel.Builder()
                        .setClickType("submit")
                        .addContent(content)
                        .build().getParams());
                viewModel.generateUploadData(list, attachedProductId);
            }
        }
    }

    private void showConfirmDialog() {
        if (type == TYPE_EDIT) {
            new CompatDialog(activity, CompatDialog.STYLE_VERTICAL).setUp(
                    new OnDialogClickListener() {
                        @Override
                        public void onClick(WrapperDialog dialog, View view) {
                            dialog.dismiss();
                            finish();
                        }
                    },
                    getString(R.string.are_you_sure_you_want_to_discard_your_post_edit),
                    getString(R.string.yes_discard),
                    getString(R.string.cancel)
            ).show();
        } else {
            List<Integer> name = new ArrayList<>();
            name.add(R.string.save_draft);
            name.add(R.string.discard);
            List<Drawable> drawables = new ArrayList<>();
            drawables.add(ContextCompat.getDrawable(activity, R.mipmap.pic_save_draft));
            drawables.add(ContextCompat.getDrawable(activity, R.mipmap.pic_discard));
            new BottomDialog(activity).setBottomData(name, drawables).setOnClickListener(new BottomDialog.OnClickListener() {
                @Override
                public void onReplyClick(BottomDialog dialog) {
                    dialog.dismiss();
                    for (int i = 0; i < adapter.getData().size(); i++) {
                        AdapterDataType item = adapter.getData().get(i);
                        if (item instanceof EditorContentData) {
                            ((EditorContentData) item).content = adapter.getPostContent();
                        } else if (item instanceof EditorTranslationData) {
                            ((EditorTranslationData) item).setSuggestTranslationsBean(suggestTranslationsBean);
                        }
                    }
                    viewModel.generateDraftData(adapter.getData());
                }

                @Override
                public void onDeleteClick(BottomDialog dialog) {
                    dialog.dismiss();
                    finish();
                }
            }).show();
        }
    }


    private void downLoadChange(boolean visible) {
        llDownload.setVisibility(visible ? View.VISIBLE : View.GONE);
        layoutProgress.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    private void downloadError() {
        layoutProgress.setVisibility(View.GONE);
        llDownload.setVisibility(View.VISIBLE);
        tvProgressStatus.setText(getString(R.string.loading_failed));
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                downLoadChange(false);
                tvProgressStatus.setText(getString(R.string.video_loading));
            }
        }, 3000);
        isDownload = false;
    }

    private void clearTranslation() {
        if (!EmptyUtils.isEmpty(suggestTranslationsBean) && !EmptyUtils.isEmpty(suggestTranslationsBean.i18n_info)) {
            for (SuggestTranslationsBean.Language item : suggestTranslationsBean.i18n_info) {
                item.title = null;
                item.description = null;
                item.isUpdated = false;
            }
        }
    }
}
