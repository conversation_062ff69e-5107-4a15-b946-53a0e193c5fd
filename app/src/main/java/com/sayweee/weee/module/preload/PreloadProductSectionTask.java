package com.sayweee.weee.module.preload;

import androidx.annotation.NonNull;

import com.sayweee.logger.Logger;
import com.sayweee.preload.PreloadInGroup;
import com.sayweee.preload.PreloadTask;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.util.Map;

//
// Created by <PERSON><PERSON> on 9/4/24.
// Copyright (c) 2024 Weee LLC. All rights reserved.
//
public class PreloadProductSectionTask implements PreloadTask<PdpSectionBean>, PreloadInGroup {

    public static final String KEY_PDP_SECTION = "pdp_section";

    ProductPageParams productParams;

    public PreloadProductSectionTask(ProductPageParams params) {
        this.productParams = params;
    }

    @Override
    public PdpSectionBean loadData() {
        try {
            int productId = productParams.productId;
            if (productId < 1 && productParams.product != null) {
                productId = productParams.product.id;
            }
            //13827
            Map<String, Serializable> params = new RequestParams().put("slot", "pdpSection").putNonNull("searchTerm", productId).putNonNull("fromPage", "pdp").get();
            return RetrofitIml.get().getHttpService(PreloadApi.class)
                    .getPdpSectionInfo(params).execute().body().getData();
        } catch (Exception e) {
            Logger.e("pdp_preload", KEY_PDP_SECTION + e.getMessage());
            return null;
        }
    }

    @NonNull
    @Override
    public String keyInGroup() {
        return KEY_PDP_SECTION;
    }
}
