package com.sayweee.weee.module.checkout.service;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.bean.UpsellMoreData;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.checkout.bean.UpSellListData;
import com.sayweee.weee.module.checkout.bean.UpsellMoreBean;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;

import java.util.ArrayList;
import java.util.List;

public class SectionUpsellViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public MutableLiveData<List<UpSellListData>> adapterData = new MutableLiveData<>();
    public MutableLiveData<UpsellMoreData> appendData = new MutableLiveData<>();

    public SectionUpsellViewModel(@NonNull Application application) {
        super(application);
    }

    public void loadData(@NonNull UpSellBean upSellBean) {
        List<UpSellListData> list = new ArrayList<>();
        if (upSellBean.upsell_list != null) {
            for (UpSellBean.UpSellListBean it : upSellBean.upsell_list) {
                UpSellListData item = new UpSellListData(it);
                if (item.isValid()) {
                    list.add(item);
                }
            }
        }
        adapterData.postValue(list);
        assembleProductTrace(list);
    }

    public void loadMore(String type, String viewMoreLink, int offset, int index) {
        getLoader().getHttpService()
                .getMoreUpsell(viewMoreLink, offset)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<UpsellMoreBean>>() {
                    @Override
                    public void onResponse(ResponseBean<UpsellMoreBean> response) {
                        UpsellMoreData data = new UpsellMoreData();
                        data.products = response.getData().products;
                        data.index = index;
                        ProductHelper.filterReachLimitValid(data.products, 0);
                        appendData.postValue(data);

                        assembleProductTrace(type, data);
                    }
                });
    }

    private void assembleProductTrace(String type, UpsellMoreData data) {
        // Create a temporary UpSellListBean to hold the products and type
        UpSellBean.UpSellListBean listBean = new UpSellBean.UpSellListBean();
        listBean.type = type;
        listBean.items = data.products;
        assembleProductTrace(CollectionUtils.arrayListOf(new UpSellListData(listBean)));
    }

    private void assembleProductTrace(List<UpSellListData> items) {
        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(items);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(WeeeEvent.PageView.BEFORE_YOU_CHECKOUT, WeeeEvent.PageView.BEFORE_YOU_CHECKOUT));
    }


}
