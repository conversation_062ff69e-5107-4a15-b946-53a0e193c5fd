package com.sayweee.weee.module.product.service;


import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cate.product.bean.PromotionListBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.post.bean.PdpVideoListBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.PdpSummaryBean;
import com.sayweee.weee.module.product.data.GiftCardPriceRefreshBean;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.List;

//
// Created by <PERSON><PERSON> on 21/03/2025.
//
public class PdpCombineData {

    public ProductDetailBean detail;
    public PromotionListBean promotion;
    public PostCategoryBean review;
    public PdpVideoListBean post;
    public PdpModulesBean modules;
    public PdpProductsBean recently;
    public CmsContentFeedListBean contentFeedListBean;
    public String referralDesc;
    public String inviteFriendsGetAmount;
    public PdpSummaryBean summaryBean;

    // local set
    public ProductBean transferProduct;
    public String traceId;
    public String cartSource;
    public boolean disableOtherPage;
    public GiftCardPriceRefreshBean refreshBean;
    public List<String> aiQuestionBean;
    public boolean changeGroupFilter;
    public PdpMiddleBannerBean middleBarInfoBean;
    public List<ProductBean> adsProductList;
    public PdpSectionBean.BannerInfoBean topBarInfoBean;


    public boolean canShowSharePopup() {
        return !EmptyUtils.isEmpty(detail) && !EmptyUtils.isEmpty(detail.product) && !detail.product.affiliate_show && !detail.product.isSeller() && !"seller".equalsIgnoreCase(detail.product.biz_type);
    }

}
