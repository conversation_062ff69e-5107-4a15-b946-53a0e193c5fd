package com.sayweee.weee.module.home.theme;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;

import com.sayweee.weee.utils.ColorTools;

public class BannerThemeDrawable {

    private static final int GRADIENT_COLOR_START = 0x00FFFFFF; // white, transparent;
    private static final int GRADIENT_COLOR_END = Color.WHITE;
    private static final int DEFAULT_SOLID_COLOR = Color.WHITE;
    private static final int[] GRADIENT_COLOR = new int[]{GRADIENT_COLOR_START, GRADIENT_COLOR_START, GRADIENT_COLOR_END};
    private static final float[] GRADIENT_POSITIONS = new float[]{0f, .6f, 1f};

    private final LayerDrawable drawable;

    private final int initialSolidColor;
    private final GradientDrawable solidDrawable;
    private final GradientDrawable gradientDrawable;

    private int solidColor;
    private final int[] gradientColor = new int[]{GRADIENT_COLOR_START, GRADIENT_COLOR_END};

    public BannerThemeDrawable() {
        this(DEFAULT_SOLID_COLOR);
    }

    public BannerThemeDrawable(@ColorInt int solidColor) {
        this.initialSolidColor = solidColor;
        this.solidColor = solidColor;

        solidDrawable = new GradientDrawable();
        solidDrawable.setShape(GradientDrawable.RECTANGLE);
        solidDrawable.setColor(solidColor);

        gradientDrawable = new GradientDrawable();
        gradientDrawable.setShape(GradientDrawable.RECTANGLE);
        gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        gradientDrawable.setOrientation(GradientDrawable.Orientation.TOP_BOTTOM);
        gradientDrawable.setColors(gradientColor);

        drawable = new LayerDrawable(new Drawable[]{solidDrawable, gradientDrawable});
    }

    @NonNull
    public Drawable getDrawable() {
        return drawable;
    }

    @ColorInt
    public int getSolidColor() {
        return solidColor;
    }

    public void setSolidColor(@ColorInt int colorInt) {
        this.solidColor = colorInt;
        solidDrawable.setColor(colorInt);
        drawable.invalidateSelf();
    }

    public void setGradientFractions(float fractionStart, float fractionEnd) {
        int colorStart, colorEnd;
        colorStart = ColorTools.getGradientColorAtPosition(GRADIENT_COLOR, GRADIENT_POSITIONS, fractionStart);
        colorEnd = ColorTools.getGradientColorAtPosition(GRADIENT_COLOR, GRADIENT_POSITIONS, fractionEnd);

        gradientColor[0] = colorStart;
        gradientColor[1] = colorEnd;
        gradientDrawable.setColors(gradientColor);
        drawable.invalidateSelf();
    }

    public void reset() {
        this.solidColor = initialSolidColor;
        solidDrawable.setColor(initialSolidColor);
        gradientDrawable.setColors(gradientColor);
        drawable.invalidateSelf();
    }

}
