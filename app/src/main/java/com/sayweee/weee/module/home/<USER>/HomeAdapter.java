package com.sayweee.weee.module.home.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsAdapter;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.adapter.payload.CmsMultiDataSourceUpdate;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.CarouselBannerProvider;
import com.sayweee.weee.module.cms.iml.banner.CarouselBannerProviderFactory;
import com.sayweee.weee.module.cms.iml.bannerarray.CmsBannerArrayProvider;
import com.sayweee.weee.module.cms.iml.bannerarray.CmsBannerArrayThreeProvider;
import com.sayweee.weee.module.cms.iml.navline.CmsNavLineProvider;
import com.sayweee.weee.module.cms.iml.navline.CmsNavLineV2Provider;
import com.sayweee.weee.module.cms.iml.product.BigProductLineProvider;
import com.sayweee.weee.module.cms.iml.product.ProductItemProvider;
import com.sayweee.weee.module.cms.iml.product.ProductLineProvider;
import com.sayweee.weee.module.cms.iml.product.ProductLineTabProvider;
import com.sayweee.weee.module.cms.iml.product.data.CmsProductLineData;
import com.sayweee.weee.module.home.HomeFragment;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.bean.TrendingPostBean;
import com.sayweee.weee.module.home.provider.banner.BannerLineProvider;
import com.sayweee.weee.module.home.provider.banner.BannerThemeProvider;
import com.sayweee.weee.module.home.provider.banner.CmsLayoutLarProvider;
import com.sayweee.weee.module.home.provider.banner.NoticeBannerProvider;
import com.sayweee.weee.module.home.provider.banner.PlayerPayload;
import com.sayweee.weee.module.home.provider.bar.SearchBarProvider;
import com.sayweee.weee.module.home.provider.brand.CardLineProvider;
import com.sayweee.weee.module.home.provider.category.CategoryBarProvider;
import com.sayweee.weee.module.home.provider.category.CategoryCapsuleProvider;
import com.sayweee.weee.module.home.provider.category.CategoryProvider;
import com.sayweee.weee.module.home.provider.community.TrendingPostProvider;
import com.sayweee.weee.module.home.provider.community.data.CmsTrendingPostData;
import com.sayweee.weee.module.home.provider.coupon.PersonalizedCouponProvider;
import com.sayweee.weee.module.home.provider.coupon.data.CmsPersonalizedCouponData;
import com.sayweee.weee.module.home.provider.message.HighlightedSkuProvider;
import com.sayweee.weee.module.home.provider.message.NewTopMessageProvider;
import com.sayweee.weee.module.home.provider.message.TopMessageV2Provider;
import com.sayweee.weee.module.home.provider.message.TopMessageV3Provider;
import com.sayweee.weee.module.home.provider.product.CollectionItemProvider;
import com.sayweee.weee.module.home.provider.product.FeatureThisWeekProvider;
import com.sayweee.weee.module.home.provider.product.LightingDealsProvider;
import com.sayweee.weee.module.home.provider.product.RecommendProvider;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.home.provider.thematic.ThematicProvider;
import com.sayweee.weee.module.mkpl.LabelScrollAdapter;
import com.sayweee.weee.module.mkpl.feed.CmsContentFeedProvider;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.widget.banner.ex.PlayerHandler;
import com.sayweee.weee.widget.banner.ex.PlayerTriggerAdapter;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.timer.OnTimerListener;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/5/16.
 * Desc:
 */
public class HomeAdapter extends CmsAdapter implements PlayerTriggerAdapter, LabelScrollAdapter {

    int searchBarIndex;
    int trendingPostIndex;
    int recommendIndex;
    List<Integer> videoIndexes;
    private boolean shouldDisplayStickyRecommend;

    private RecyclerViewScrollStatePersist scrollStatePersist;

    public boolean displayStickyRecommend() {
        return shouldDisplayStickyRecommend;
    }

    public void setScrollStatePersist(@Nullable RecyclerViewScrollStatePersist scrollStatePersist) {
        this.scrollStatePersist = scrollStatePersist;
    }

    @Override
    public void setNewData(@Nullable List<AdapterDataType> data) {
        super.setNewData(data);
        findDataIndex();
    }

    @Override
    public void addData(@NonNull Collection<? extends AdapterDataType> newData) {
        super.addData(newData);
        findDataIndex();
    }

    @Override
    public void onPageResume(RecyclerView view) {
        super.onPageResume(view);
        ProductSyncHelper.onPageResume(this);
        PlayerHandler.notifyLifecycleStatusChanged(view, /* isResume= */true);
    }

    @Override
    public void onPagePause(RecyclerView view) {
        super.onPagePause(view);
        PlayerHandler.notifyLifecycleStatusChanged(view, /* isResume= */false);
    }

    @Override
    protected void addAdapterProvider() {
        super.addAdapterProvider();
        addItemProvider(new BannerThemeProvider());//banner theme
        addItemProvider(new SearchBarProvider());
        addItemProvider(new CarouselBannerProviderFactory().get());
        addItemProvider(new NewTopMessageProvider());
        addItemProvider(new TopMessageV2Provider());//top msg V2
        addItemProvider(new TopMessageV3Provider());//Announcement message on homepage
        addItemProvider(new CategoryProvider());
        addItemProvider(new ThematicProvider()); // horizontal thematic
        addItemProvider(new ProductItemProvider()); // single product
        addItemProvider(new ProductLineProvider().setProductDisplayStyle(ProductView.STYLE_ITEM_MINI)); //  horizontal products
        addItemProvider(new CmsBannerArrayProvider());
        addItemProvider(new CmsBannerArrayThreeProvider());
        addItemProvider(new CollectionItemProvider().setProductDisplayStyle(ProductView.STYLE_ITEM_MINI)); // horizontal products
        addItemProvider(new FeatureThisWeekProvider()); // single product
        addItemProvider(new LightingDealsProvider().setProductDisplayStyle(ProductView.STYLE_ITEM_MINI)); // horizontal products
        addItemProvider(new TrendingPostProvider()); // horizontal video posts
        addItemProvider(new NoticeBannerProvider());
        addItemProvider(new BannerLineProvider());
        addItemProvider(new RecommendProvider());
        addItemProvider(new CardLineProvider());
        addItemProvider(new CmsContentFeedProvider(OnIndicatorClickListener.EMPTY, getSupportManager()));
        addItemProvider(new BigProductLineProvider()); // horizontal products
        addItemProvider(new CmsNavLineProvider());
        addItemProvider(new CmsNavLineV2Provider());
        addItemProvider(new CmsLayoutLarProvider());
        addItemProvider(new PersonalizedCouponProvider());
        addItemProvider(new HighlightedSkuProvider());
    }

    @Nullable
    @Override
    protected SectionProviderFactory getSectionProviderFactory() {
        return new SectionProviderFactory(super.getSectionProviderFactory()) {
            @Nullable
            @Override
            public ISectionProvider<?, ?> getItemProvider(int viewType) {
                ISectionProvider<?, ?> provider;
                if (viewType == CmsItemType.PRODUCT_LINE_TAB) {
                    // horizontal products
                    provider = new ProductLineTabProvider()
                            .setProductDisplayStyle(ProductView.STYLE_ITEM_MINI)
                            .setScrollStatePersist(scrollStatePersist)
                            .setCmsMultiDataSourceListener(cmsMultiDataSourceListener);
                } else if (viewType == CmsItemType.CATEGORIES_CAPSULE) {
                    provider = new CategoryCapsuleProvider(scrollStatePersist);
                } else if (viewType == CmsItemType.CATEGORIES_BAR) {
                    provider = new CategoryBarProvider(scrollStatePersist);
                } else {
                    provider = parentFactory != null ? parentFactory.getItemProvider(viewType) : null;
                }
                return provider;
            }
        };
    }

    private FragmentManager getSupportManager() {
        FragmentManager manager = null;
        if (mContext instanceof FragmentActivity) {
            Fragment fragment = ((FragmentActivity) mContext).getSupportFragmentManager()
                    .findFragmentByTag(HomeFragment.class.getName());
            if (fragment != null) {
                manager = fragment.getChildFragmentManager();
            }
        }
        return manager;
    }

    public int getTrendingPostIndex() {
        return trendingPostIndex;
    }

    public int getSearchBarIndex() {
        return searchBarIndex;
    }

    public int getRecommendIndex() {
        return recommendIndex;
    }

    protected void findDataIndex() {
        shouldDisplayStickyRecommend = true;
        searchBarIndex = -1;
        trendingPostIndex = -1;
        recommendIndex = -1;
        if (videoIndexes != null) {
            videoIndexes.clear();
        }
        List<AdapterDataType> list = getData();
        for (int i = 0; i < list.size(); i++) {
            int type = list.get(i).getType();
            switch (type) {
                case CmsItemType.SEARCH_BAR:
                    searchBarIndex = i;
                    break;
                case CmsItemType.TRENDING_POST:
                    trendingPostIndex = i;
                    break;
                case CmsItemType.RECOMMEND:
                    shouldDisplayStickyRecommend = true;
                    recommendIndex = i;
                    break;
                case CmsItemType.CONTENT_FEED:
                    shouldDisplayStickyRecommend = false;
                    recommendIndex = i;
                    break;
                case CmsItemType.CAROUSEL_BANNER:
                case CmsItemType.LAYOUT_LAR:
                    if (videoIndexes == null) {
                        videoIndexes = new ArrayList<>();
                    }
                    videoIndexes.add(i);
                    break;
                default:
                    break;
            }
        }
    }

    public AdapterDataType getTargetData(int type) {
        int size = mData.size();
        for (int i = 0; i < size; i++) {
            AdapterDataType item = mData.get(i);
            if (item.getType() == type) {
                return item;
            }
        }
        return null;
    }

    public int getPagerIndex() {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.RECOMMEND);
        if (provider instanceof RecommendProvider) {
            return ((RecommendProvider) provider).getPagerIndex();
        }
        return 0;
    }

    public void setPageTarget(Fragment fragment) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.RECOMMEND);
        if (provider instanceof RecommendProvider) {
            ((RecommendProvider) provider).setAttachedContext(fragment);
        }

        ISectionProvider<?, ?> temp = getItemProvider(CmsItemType.CONTENT_FEED);
        if (temp instanceof CmsContentFeedProvider) {
            ((CmsContentFeedProvider) temp).setAttachedManager(fragment.getChildFragmentManager());
        }
    }

    /**
     * 刷新限时秒杀数据
     */
    public void refreshOnlyLightDealsData(int id) {
        for (int index = 0, itemCount = CollectionUtils.size(mData); index < itemCount; index++) {
            AdapterDataType data = CollectionUtils.getOrNull(mData, index);
            if (data instanceof CmsLightingDealsData) {
                CmsLightingDealsData item = (CmsLightingDealsData) data;
                for (int i = 0, size = item.t.products.size(); i < size; i++) {
                    LightningDealsProductBean bean = item.t.products.get(i);
                    if (bean.id == id) {
                        notifyItemChanged(index, i);
                    }
                }
            }
        }
    }

    public void setOnLightingDealTimerListener(OnTimerListener listener) {
        ISectionProvider<?, ?> provider;
        provider = getItemProvider(CmsItemType.LIGHTNING_DEALS);
        if (provider instanceof LightingDealsProvider) {
            ((LightingDealsProvider) provider).setOnTimerListener(listener);
        }
    }

    public void setOnTopMessageTimerListener(OnTimerListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.NEW_TOP_MESSAGE);
        if (provider instanceof NewTopMessageProvider) {
            ((NewTopMessageProvider) provider).setOnTimerListener(listener);
        }
    }

    public void setOnRemindListener(OnRemindListener listener) {
        ISectionProvider<?, ?> provider;
        provider = getItemProvider(CmsItemType.LIGHTNING_DEALS);
        if (provider instanceof LightingDealsProvider) {
            ((LightingDealsProvider) provider).setOnRemindListener(listener);
        }
    }

    public void setOnPageChangeListener(ViewPager.OnPageChangeListener pageChangeListener) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.RECOMMEND);
        if (provider instanceof RecommendProvider) {
            ((RecommendProvider) provider).setOnPageChangeListener(pageChangeListener);
        }
    }

    public void setOnPostPopVisibleListener(TrendingPostProvider.OnPostPopVisibleListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.TRENDING_POST);
        if (provider instanceof TrendingPostProvider) {
            ((TrendingPostProvider) provider).setOnPostPopVisibleListener(listener);
        }
    }

    public void setOnBannerPageChangeListener(CarouselBannerProvider.OnBannerPageChangeListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.CAROUSEL_BANNER);
        if (provider instanceof CarouselBannerProvider) {
            ((CarouselBannerProvider) provider).setOnBannerPageChangeListener(listener);
        }
    }

    public void toggleCollect(Map<String, Serializable> map) {
        int id = 0;
        Boolean status = null;
        Integer count = null;
        Serializable reviewIdObj = map.get("reviewId");
        if (reviewIdObj instanceof Integer) {
            id = (int) reviewIdObj;
        }
        Serializable statusObj = map.get("status");
        if (statusObj instanceof Boolean) {
            status = (Boolean) statusObj;
        }
        Serializable countObj = map.get("count");
        if (countObj instanceof Integer) {
            count = (Integer) countObj;
        }

        if (id != 0 && status != null && count != null) {
            for (AdapterDataType item : mData) {
                if (item instanceof CmsTrendingPostData) {
                    CmsTrendingPostData data = (CmsTrendingPostData) item;
                    List<TrendingPostBean.TrendingPostResponsesBean> tabs = data.t.valid;
                    for (int j = 0; j < tabs.size(); j++) {
                        List<PostBean> postList = tabs.get(j).posts;
                        for (int z = 0; z < postList.size(); z++) {
                            PostBean bean = postList.get(z);
                            if (bean.id == id) {
                                bean.is_set_like = status;
                                bean.like_count = count;
                                notifyItemChanged(mData.indexOf(item));
                                return;
                            }
                        }
                    }
                }
            }
        }
    }

    public void revokeRemind(int id) {
        if (CollectManager.get().isProductCollect(id)) {
            CollectManager.get().toggleProductCollect(id);
            if (hasId(id)) {
                refreshOnlyLightDealsData(id);
            }
        }
    }

    private boolean hasId(int id) {
        for (AdapterDataType item : mData) {
            if (item instanceof CmsLightingDealsData) {
                for (LightningDealsProductBean productBean : ((CmsLightingDealsData) item).t.products) {
                    if (id == productBean.id) {
                        productBean.remind_set = false;
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void notifyItemPlayByPosition(int start, int end, int status) {
        if (CollectionUtils.isNotEmpty(videoIndexes)) {
            for (Integer index : videoIndexes) {
                if (index >= start && index <= end) {
                    notifyItemChanged(index, new PlayerPayload(status));
                }
            }
        }
    }

    public void notifyMultiDataSourceUpdate(String componentId) {
        if (componentId == null || componentId.isEmpty()) return;
        Pair<Integer, AdapterDataType> pair = CollectionUtils.firstOrNullWithIndex(
                getData(),
                item -> {
                    if (item instanceof ComponentData) {
                        ComponentData<?, ?> data = (ComponentData<?, ?>) item;
                        return componentId.equals(data.getComponentId());
                    }
                    return false;
                }
        );
        if (pair != null) {
            notifyItemChanged(pair.first, new CmsMultiDataSourceUpdate(componentId));
        }
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        for (int i = start; i <= end; i++) {
            Object item = getItem(i);
            if (item instanceof CmsProductLineData || item instanceof CmsPersonalizedCouponData) {
                notifyItemChanged(i, item);
            }
        }
    }
}
