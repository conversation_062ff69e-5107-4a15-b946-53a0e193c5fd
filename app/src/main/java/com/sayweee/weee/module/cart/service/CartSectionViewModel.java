package com.sayweee.weee.module.cart.service;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;

import com.alibaba.fastjson.JSON;
import com.sayweee.core.order.OrderProvider;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.CartStatusManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.AdapterPanelData;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.AddOnDetailBean;
import com.sayweee.weee.module.cart.bean.BoughtListBean;
import com.sayweee.weee.module.cart.bean.CartAddOnDetailData;
import com.sayweee.weee.module.cart.bean.CartSellerDetailData;
import com.sayweee.weee.module.cart.bean.CartTopMessageData;
import com.sayweee.weee.module.cart.bean.GroupBuySellerDetailBean;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.NextUrlBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductListBean;
import com.sayweee.weee.module.cart.bean.ProductUpdateBean;
import com.sayweee.weee.module.cart.bean.SaveForLaterResponseBean;
import com.sayweee.weee.module.cart.bean.TopInfoBean;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.bean.WrapperUpSellBean;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartSectionData;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartTopPromotionData;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartTopSectionData;
import com.sayweee.weee.module.cart.bean.setcion.CartSectionType;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartActivityData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartDealData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartProductData;
import com.sayweee.weee.module.cate.bean.VendorIntroductionBean;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.module.home.bean.NewTopMessageBean;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.Observer;
import okhttp3.MediaType;
import okhttp3.RequestBody;

public class CartSectionViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    private static final String CART_DOMAIN_KEY = "cart_domain";
    private static final String SAVE_4_LATER_KEY = "product_keys";
    private static final String CODE_SAVE_4_LATER_LIMIT = "SO90111"; // 稍后购买列表满提示

    public static final String FAILURE_SECTION_CHECKOUT = "section_checkout_failure";

    public String domainValue;
    private SaveForLaterResponseBean saveForLaterResponseBean;//购物车save 4 later数据

    public MutableLiveData<FailureBean> failureData = new MutableLiveData<>();
    public MutableLiveData<NewPreOrderBean> cartData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterCartSectionData>> adapterCartData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterCartSectionData>> adapterCartDataSilent = new MutableLiveData<>();
    public MutableLiveData<AdapterCartTopSectionData> adapterCartTopSectionData = new MutableLiveData<>(new AdapterCartTopSectionData());

    //购物车顶部倒计时信息
    public MutableLiveData<TopInfoBean> topBannerData = new MutableLiveData<>();
    public MutableLiveData<WrapperUpSellBean> upSellData = new MutableLiveData<>();
    public MutableLiveData<VendorIntroductionBean> vendorIntroductionData = new MutableLiveData<>();
    public MutableLiveData<SectionCartDealData> sectionCartDealChangeData = new MutableLiveData<>();
    public MutableLiveData<NewSectionBean> sectionCheckoutData = new MutableLiveData<>();

    // Group order
    public final MutableLiveData<SellerGroupStatusBean> sellerGroupStatusLiveData = new MutableLiveData<>();

    public CartSectionViewModel(@NonNull Application application) {
        super(application);
    }

    public void refreshCartData() {
        domainValue = Constants.CartDomain.DOMAIN_GROCERY;
        refreshCartData(domainValue);
        refreshCartTopSectionData();
    }

    private void refreshCartTopSectionData() {
        Observer<ResponseBean<?>> observer = new ResponseObserver<ResponseBean<?>>() {

            private final AdapterCartTopSectionData data = new AdapterCartTopSectionData();

            @Override
            public void onResponse(ResponseBean<?> response) {
                Object responseData = response.getData();
                if (responseData instanceof AddOnDetailBean) {
                    data.addOnDetailData = new CartAddOnDetailData(((AddOnDetailBean) responseData));
                } else if (responseData instanceof GroupBuySellerDetailBean) {
                    data.sellerDetailData = new CartSellerDetailData(((GroupBuySellerDetailBean) responseData));
                } else if (responseData instanceof List) {
                    data.topMessageDataList = CollectionUtils.map(((List<NewTopMessageBean>) responseData), CartTopMessageData::new);
                }
            }

            @Override
            public void onFinish() {
                super.onFinish();
                adapterCartTopSectionData.postValue(data);
            }
        };

        OrderApi api = getLoader().getHttpService();
        ObservableSource<ResponseBean<?>> groupBuyReq = api
                .getGroupBuySellerDetail()
                .compose(ResponseTransformer.scheduler(this, false));
        ObservableSource<ResponseBean<?>> addOnReq = api
                .getAddOnDetail()
                .compose(ResponseTransformer.scheduler(this, false));
        ObservableSource<ResponseBean<?>> topMessageBuyReq = api
                .getCartTopMessage(new RequestParams().put("freeTrialGold", 1).put("version", "v2").get())
                .compose(ResponseTransformer.scheduler(this, false));
        Observable.mergeDelayError(groupBuyReq, addOnReq, topMessageBuyReq).subscribe(observer);
    }

    private void refreshCartData(String domain) {
        Map<String, Serializable> params = new RequestParams().put(CART_DOMAIN_KEY, domain).get();
        Observable<ResponseBean<NewPreOrderBean>> getPreOrderV5 = getLoader().getHttpService().getPreOrderV5(params).compose(DisposableTransformer.scheduler(this, false));
        Observable<ResponseBean<?>> observable;
        boolean isLogin = AccountManager.get().isLogin();
        if (!isLogin) {//未登录状态不请求Save4later
            observable = Observable.mergeArrayDelayError(getPreOrderV5);
        } else {
            Observable<ResponseBean<SaveForLaterResponseBean>> getSave4later = getLoader().getHttpService().getSave4later(0, 10).compose(DisposableTransformer.scheduler(this, false));
            observable = Observable.mergeArrayDelayError(getPreOrderV5, getSave4later);
        }
        observable.subscribe(new ResponseObserver<ResponseBean<?>>() {
            NewPreOrderBean newPreOrderBean;

            @Override
            public void onResponse(ResponseBean<?> response) {
                Object data = response.getData();
                if (data instanceof SaveForLaterResponseBean) {
                    saveForLaterResponseBean = (SaveForLaterResponseBean) data;//首次进入购物车或者刷新购物车页面,调用/save4later/v2查询接口查询
                } else if (data instanceof NewPreOrderBean) {
                    newPreOrderBean = (NewPreOrderBean) data;
                }
            }

            @Override
            public void onFinish() {
                if (newPreOrderBean != null) {
                    handleResponse(newPreOrderBean);
                    WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_LOAD,
                            WeeeEvent.PageView.CART, String.valueOf(CartSectionViewModel.this.hashCode()));
                }
            }

            @Override
            public void onError(FailureBean failure) {
                if (DevConfig.isDebug()) {
                    Toaster.showToast(failure.getFailureMessage());
                }
            }
        });
    }

    private void handleResponse(NewPreOrderBean data) {
        handleResponse(data, false);
    }

    private void handleResponse(NewPreOrderBean data, boolean isSilent) {
        updateSimplePreOrder(data);
        parseCartData(data, isSilent);
    }

    protected void refreshOnError() {
        refreshCartData(domainValue);
    }

    private boolean isGroceryCartData() {
        return Constants.CartDomain.DOMAIN_GROCERY.equalsIgnoreCase(domainValue);
    }

    protected void updateSimplePreOrder(NewPreOrderBean data) {
        //如果is_refresh_porder，通过refreshSimplePreOrderDelay来updateSimplePreOrder,否则主动updateSimplePreOrder
        if (data.is_refresh_porder) {
            //购物车接口添加字段告知前端是否需要刷新simple，若需要则刷新simple，更新本地的zipcode和date信息
            OrderProvider.get().refreshSimplePreOrderDelay(0, true);
        } else {
            SimplePreOrderBean bean = OrderHelper.toSimplePreOrderV5(data);
            OrderProvider.get().putSimpleOrderData(bean, false);
        }
    }

    private void parseCartData(NewPreOrderBean data, boolean isSilent) {
        Map<String, Boolean> cartCollapseInfo = CartStatusManager.get().getCartCollapseInfo();
        boolean displaySaveFLater = data.display_save_for_later;
        List<AdapterCartSectionData> list = new ArrayList<>();
        //新版赠品活动
        boolean hasTopPromotions = false;
        if (CollectionUtils.isNotEmpty(data.top_promotions)) {
            hasTopPromotions = true;
            int i = -1;
            for (NewSectionBean.ActivityInfo topPromotion : data.top_promotions) {
                i++;
                AdapterCartTopPromotionData promotionData = new AdapterCartTopPromotionData().setPosition(i);
                promotionData.setActivityInfo(topPromotion);
                list.add(promotionData);
            }
        }
        if (data.sections != null && !data.sections.isEmpty()) {
            for (NewSectionBean section : data.sections) {
                int index = data.sections.indexOf(section);
                if (isGroceryCartData()) {
                    boolean isCollapsed = Boolean.TRUE.equals(cartCollapseInfo.get(section.getCartId()));//是否已折叠购物车商品
                    AdapterCartSectionData adapterCartSectionData = new AdapterCartSectionData(section, isCollapsed, displaySaveFLater, index);
                    NextUrlBean nextUrlBean = adapterCartSectionData.nextUrlBean;
                    if (nextUrlBean != null && nextUrlBean.isValid()) {
                        loadNextUrl(nextUrlBean, adapterCartSectionData.targetData.getCartId());
                    }
                    list.add(adapterCartSectionData);
                }
            }
        } else {
            if (!hasTopPromotions) {
                list.add(new AdapterCartSectionData(AdapterCartSectionData.TYPE_EMPTY, null, 0));
            }
        }

        if (displaySaveFLater && saveForLaterResponseBean != null && !EmptyUtils.isEmpty(saveForLaterResponseBean.items)) {
            boolean isCollapsed = Boolean.TRUE.equals(cartCollapseInfo.get(saveForLaterResponseBean.getCartId()));
            int index = data.sections.size() + 1;
            list.add(new AdapterCartSectionData(saveForLaterResponseBean, isCollapsed, index));
        }

        if (isSilent) {
            adapterCartDataSilent.postValue(list);
        } else {
            adapterCartData.postValue(list);
        }
        cartData.postValue(data);
    }

    public void collapseSection(String cartId, boolean isCollapsed) {
        CartStatusManager.get().put(cartId, isCollapsed);
        NewPreOrderBean data = cartData.getValue();
        if (data != null) {
            parseCartData(data, false);
        }
    }

    public void editProductData(SectionCartProductData data, int num) {
        if (data != null) {
            if (data.isActivityTradeIn() || data.isInvalidProduct()) { //换购商品/失效商品不能编辑
                return;
            }
            OrderManager.get().putSimpleOrderItemChanged(data.t.product_id, num, data.t.refer_type, data.t.refer_value, data.t.product_key, data.t.options);
            data.t.quantity = num;
            notifyServerProductChanged(data, false);
        } else {
            refreshOnError();
        }
    }

    public void editProductData(SectionCartProductData data, boolean isAdd) {
        if (data != null) {
            preIntoSimpleOrder(data, isAdd);
            execEditCartProductData(data, isAdd);
        } else {
            refreshOnError();
        }
    }

    public void editProductData(AdapterProductData data, boolean isAdd) {
        if (data != null) {
            preIntoSimpleOrder(data, isAdd);
            execEditAdapterProductData(data);
        } else {
            refreshOnError();
        }
    }

    public void removeCartProductData(SectionCartProductData data) {
        if (data != null) {
            if (data.isGiftProduct()) {
                deleteActivityGift(data);
            } else {
                preIntoSimpleOrderOnRemoved(data);
                execRemoveCartProductData(data);
            }
        } else {
            refreshOnError();
        }
    }

    private void execEditCartProductData(SectionCartProductData data, boolean isAdd) {
        if (data.isActivityTradeIn() || data.isInvalidProduct()) { //换购商品/失效商品不能编辑
            return;
        }
        NewItemBean bean = data.t;
        int num = OrderHelper.editNum(isAdd, bean.quantity, bean.min_order_quantity, bean.getOrderMaxQuantity());
        if (bean.quantity != num) {
            //数目变化
            bean.quantity = num;
            notifyServerProductChanged(data, false);
        }
    }

    /**
     * 从推荐或曾经购买列表中编辑商品
     */
    private void execEditAdapterProductData(AdapterProductData data) {
        String deliveryDate = OrderManager.get().getDeliveryPickupDate();
        NewItemBean bean = toCartItem(data.t, data.source, data.newSource, deliveryDate);
        //数目变化
        bean.quantity = data.getProductQuantity();
        notifyServerProductChanged(new SectionCartProductData(0, bean), true);//编辑购物车底部推荐商品
    }

    private void execRemoveCartProductData(SectionCartProductData data) {
        data.t.quantity = 0;
        notifyServerProductChanged(data, false);
    }

    private void notifyServerProductChanged(SectionCartProductData data, boolean isSilent) {
        ProductUpdateBean updateBean = toProductUpdateBean(data.t);
        ArrayList<ProductUpdateBean> list = new ArrayList<>();
        list.add(updateBean);
        updateCartData(list, isSilent);
    }

    public void updateCartData(List<ProductUpdateBean> list, boolean isSilent) {
        createUpdateCartObservable(list)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        if (response != null) {
                            handleResponse(response.getData(), isSilent);
                            //刷新New user homepage cart progress bar
                            if (TimerBannerManager.get().isProgressStyle()) {
                                TimerBannerManager.get().refreshTimerBanner(false);
                            }
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        OrderProvider.get().onEditCartError(list, failure);
                        showDevelopErrorMessage(failure);
                    }
                });
    }

    public void deleteActivityGift(SectionCartProductData data) {
        List<Map<String, Serializable>> requestParams = new ArrayList<>();
        requestParams.add(new RequestParams().put(CART_DOMAIN_KEY, domainValue)
                .put("vendor_id", data.vendorId)
                .put("gifts", (Serializable) data.giftItems).get());
        RequestBody requestBody = new RequestParams()
                .create(JSON.toJSONString(requestParams), MediaType.parse("application/json; charset=utf-8"));
        getLoader()
                .getHttpService()
                .deleteActivityGiftV2(requestBody)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        handleResponse(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });
    }

    /**
     * 生成同步购物车变化的Observe对象，若当前无变化，则生成空的Observe
     *
     * @param list
     * @return
     */
    private Observable<ResponseBean<NewPreOrderBean>> createUpdateCartObservable(List<ProductUpdateBean> list) {
        if (list == null || list.isEmpty()) {
            return Observable.create(new ObservableOnSubscribe<ResponseBean<NewPreOrderBean>>() {
                @Override
                public void subscribe(@NotNull ObservableEmitter<ResponseBean<NewPreOrderBean>> emitter) throws Exception {
                    emitter.onComplete();
                }
            });
        }
        return getLoader().getHttpService()
                .updateCartV3(new RequestParams().create(JsonUtils.toJSONString(list), MediaType.parse("application/json; charset=utf-8")));
    }

    private void showDevelopErrorMessage(FailureBean bean) {
        if (DevConfig.isDevelop()) {
            if (bean != null && !TextUtils.isEmpty(bean.getMessage())) {
                //通用error处理
                Toaster.showToast(bean.getMessage());
            }
        }
    }

    private ProductUpdateBean toProductUpdateBean(NewItemBean item) {
        ProductUpdateBean bean = new ProductUpdateBean();
        bean.product_id = item.product_id;
        bean.quantity = item.quantity;
        bean.refer_type = item.refer_type;
        bean.refer_value = item.refer_value;
        bean.source = item.source;
        bean.delivery_date = !EmptyUtils.isEmpty(item.delivery_date) ? item.delivery_date : OrderManager.get().getDeliveryPickupDate();//新加购接口对delivery_date有判空的强校验
        bean.source_store = StoreManager.get().getStoreKey();
        bean.product_key = item.product_key;
        bean.options = item.options;
        bean.new_source = item.new_source;
        return bean;
    }

    private NewItemBean toCartItem(ProductBean bean, String source, String newSource, String deliveryDate) {
        NewItemBean item = new NewItemBean();
        item.quantity = 0;
        item.product_id = bean.id;
        item.title = bean.name;
        item.img = bean.img;
        item.source = source;
        item.new_source = newSource;
        boolean showMemberPrice = OrderHelper.isVip() && bean.show_member_price; //根据当前用户状态判断是否显示会员价
        item.price_type = showMemberPrice ? Constants.PriceType.MEMBER : Constants.PriceType.NORMAL;
        item.price = showMemberPrice ? bean.member_price : bean.price;
        item.base_price = bean.base_price;
        item.refer_type = bean.getProductType();
//        bean.refer_value = item.is_hotdish
        item.max_order_quantity = bean.max_order_quantity;
        item.min_order_quantity = bean.min_order_quantity;
        item.catalogue_num = bean.category_name;
        item.delivery_date = deliveryDate;
        item.is_colding_package = bean.is_colding_package;
        return item;
    }

    private void preIntoSimpleOrder(SectionCartProductData data, boolean isAdd) {
        OrderManager.get().putSimpleOrderItemChanged(data.t.product_id, data.t.min_order_quantity, data.t.getOrderMaxQuantity(), isAdd, data.t.refer_type, data.t.refer_value, data.t.product_key, data.t.options);
    }

    private void preIntoSimpleOrder(AdapterProductData data, boolean isAdd) {
        String referType = data.t.getProductType();
        //OrderManager.get().putSimpleOrderItemChanged(0,0 ,0 , isAdd, referType, null, data.t.product_key, null);
        int productId = data.t.id;
        String productKey = data.t.product_key;
        SimplePreOrderBean.ItemsBean item = OrderProvider.get().getSimpleOrderItem(productId, productKey);
        if (item == null) {
            item = OrderProvider.get().buildCartItemData(productId
                    , 0
                    , referType
                    , null
                    , productKey
                    , null
            );
        }
        int lastNum = item.quantity;
        item.quantity = OrderHelper.editNum(isAdd, lastNum, data.t.min_order_quantity, data.t.getOrderMaxQuantity(), data.t.getVolumeThreshold());
        OrderProvider.get().putSimpleOrderItemChanged(item, lastNum, false);
    }

    private void preIntoSimpleOrderOnRemoved(SectionCartProductData data) {
        NewItemBean bean = data.t;
        SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(bean.product_id, bean.product_key);
        if (item == null) {
            item = OrderProvider.get().buildCartItemData(bean.product_id
                    , 0
                    , bean.refer_type
                    , bean.refer_value
                    , bean.product_key
                    , bean.options
            );
        }
        int lastNum = item.quantity;
        item.quantity = 0;
        OrderProvider.get().putSimpleOrderItemChanged(item, lastNum, false);
    }

    /**
     * cart商品===>save4Later
     */
    public void cartToSave4Later(String productKey) {
        ArrayList<String> list = new ArrayList<>();
        list.add(productKey);
        Map<String, Serializable> requestParams = new RequestParams().put(SAVE_4_LATER_KEY, list).get();
        getLoader()
                .getHttpService()
                .cartToSave4Later(requestParams)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        saveForLaterResponseBean = response.getData().save_for_later_response;//用户通过购物车加购移动商品到稍后再买,返回10条稍后再买数据
                        handleResponse(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (failure != null
                                && CODE_SAVE_4_LATER_LIMIT.equals(failure.getMessageId())
                                && !EmptyUtils.isEmpty(failure.getMessage())
                        ) {
                            Toaster.showToast(failure.getMessage());
                        }
                    }
                });
    }

    /**
     * save4Later商品===>cart
     */
    public void save4LaterToCart(String productKey) {
        removeSave4LaterProduct(productKey);
        ArrayList<String> list = new ArrayList<>();
        list.add(productKey);
        Map<String, Serializable> requestParams = new RequestParams().put(SAVE_4_LATER_KEY, list).get();
        getLoader()
                .getHttpService()
                .save4LaterToCart(requestParams)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        handleResponse(response.getData(), true);//只刷新save4later以上区域数据
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });
    }

    /**
     * 删除save4Later商品
     */
    public void deleteSave4Later(String productKey) {
        removeSave4LaterProduct(productKey);
        ArrayList<String> list = new ArrayList<>();
        list.add(productKey);
        Map<String, Serializable> requestParams = new RequestParams().put(SAVE_4_LATER_KEY, list).get();
        getLoader()
                .getHttpService()
                .deleteSave4Later(requestParams)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                    }
                });
    }

    private void removeSave4LaterProduct(String productKey) {
        if (productKey != null && saveForLaterResponseBean != null && !EmptyUtils.isEmpty(saveForLaterResponseBean.items)) {
            Iterator<NewItemBean> iterator = saveForLaterResponseBean.items.iterator();
            while (iterator.hasNext()) {
                if (productKey.equalsIgnoreCase(iterator.next().product_key)) {
                    iterator.remove();
                    saveForLaterResponseBean.total_count = saveForLaterResponseBean.total_count - 1;
                    return;
                }
            }
        }
    }

    public void getTopBanner() {
        if (AccountManager.get().isLogin()) {
            getLoader().getHttpService().getTopInfo("cart")
                    .compose(DisposableTransformer.scheduler(this, false))
                    .subscribe(new ResponseObserver<ResponseBean<TopInfoBean>>() {
                        @Override
                        public void onResponse(ResponseBean<TopInfoBean> response) {
                            TopInfoBean data = response.getData();
                            topBannerData.postValue(data);
                        }
                    });
        } else {
            topBannerData.postValue(null);
        }
    }

    public void upSell() {
        getLoader()
                .getHttpService()
                .upSellV2(domainValue)
                .timeout(DevConfig.isTb1() ? 6000 : 1800, TimeUnit.MILLISECONDS)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<UpSellBean>>() {
                    @Override
                    public void onResponse(ResponseBean<UpSellBean> response) {
                        upSellData.postValue(new WrapperUpSellBean(response.getData()));
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        upSellData.postValue(new WrapperUpSellBean());
                    }
                });
    }

    public void getVendorIntroduction(String vendorId) {
        getLoader()
                .getHttpService()
                .getVendorIntroduction(vendorId)
                .compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<VendorIntroductionBean>>() {
                    @Override
                    public void onResponse(ResponseBean<VendorIntroductionBean> response) {
                        if (!EmptyUtils.isEmpty(response.getData().introduction)) {
                            ArrayMap<String, String> params = new ArrayMap<>();
                            params.put("vendor_id", vendorId);
                            response.getData().trackParams = params;
                            vendorIntroductionData.postValue(response.getData());
                        }
                    }
                });
    }

    public void loadMoreSave4LaterV2(int pageSize) {
        int offset = 0;
        if (saveForLaterResponseBean != null && !EmptyUtils.isEmpty(saveForLaterResponseBean.items)) {
            offset = saveForLaterResponseBean.items.size();
        }
        getLoader()
                .getHttpService()
                .getSave4later(offset, pageSize)
                .compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<SaveForLaterResponseBean>>() {
                    @Override
                    public void onResponse(ResponseBean<SaveForLaterResponseBean> response) {
                        SaveForLaterResponseBean moreData = response.getData();
                        NewPreOrderBean preOrderBean = cartData.getValue();
                        if (!EmptyUtils.isEmpty(moreData.items) && preOrderBean != null) {
                            saveForLaterResponseBean.page_no = moreData.page_no;
                            saveForLaterResponseBean.items.addAll(moreData.items);
                            parseCartData(preOrderBean, false);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }
                });
    }

    /**
     * 底部推荐/曾经购买panel。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。
     */
    int limit = 20;
    int boughtOffset = 0;
    int recommendOffset = 0;
    final int MAX_OFFSET = 100;
    public boolean isBoughtDataLoadEnd;        //曾经购买数据是否全部加载完毕
    public boolean isRecommendDataLoadEnd;     //为你推荐数据是否全部加载完毕
    public boolean isMultiData;
    public MutableLiveData<AdapterPanelData> adapterPanelData = new MutableLiveData<>();

    public void getBoughtData() {
        createBoughtObservable()
                .subscribe(new ResponseObserver<ResponseBean<BoughtListBean>>() {
                    @Override
                    public void onResponse(ResponseBean<BoughtListBean> response) {
                        AdapterPanelData panelData = new AdapterPanelData();
                        processBoughtData(panelData, response.getData());
                        setPanelDataChanged(panelData);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        AdapterPanelData panelData = new AdapterPanelData();
                        setPanelDataChanged(panelData);
                    }
                });
    }

    public void getPreferenceData() {
        createPreferenceObservable()
                .subscribe(new ResponseObserver<ResponseBean<ProductListBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ProductListBean> response) {
                        AdapterPanelData panelData = new AdapterPanelData();
                        processRecommendData(panelData, response.getData());
                        setPanelDataChanged(panelData);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        AdapterPanelData panelData = new AdapterPanelData();
                        setPanelDataChanged(panelData);
                    }
                });
    }

    private Observable<ResponseBean<BoughtListBean>> createBoughtObservable() {
        return getLoader()
                .getHttpService()
                .boughtOnce(limit, boughtOffset)
                .compose(DisposableTransformer.scheduler(this, false));
    }

    private Observable<ResponseBean<ProductListBean>> createPreferenceObservable() {
        return getLoader()
                .getHttpService()
                .cartPreference(limit, recommendOffset)
                .compose(DisposableTransformer.scheduler(this, false));
    }

    public void getPanelData() {
        boughtOffset = 0;
        recommendOffset = 0;
        isBoughtDataLoadEnd = false;
        isRecommendDataLoadEnd = false;
        ArrayList<Object> list = new ArrayList<>();
        Observable.mergeDelayError(createPreferenceObservable(), createBoughtObservable())
                .subscribe(new ResponseObserver<ResponseBean<?>>() {
                    @Override
                    public void onResponse(ResponseBean<?> response) {
                        Object data = response.getData();
                        list.add(data);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showDevelopErrorMessage(failure);
                        list.add(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        if (list.size() >= 2) {

                            boughtOffset = 0;
                            recommendOffset = 0;

                            Object o1 = list.get(0);
                            Object o2 = list.get(1);
                            AdapterPanelData panelData = new AdapterPanelData();
                            panelData.isFirstLoad = true;
                            processPanelData(panelData, o1);
                            processPanelData(panelData, o2);
                            setPanelDataChanged(panelData);
                            isMultiData = CollectionUtils.isNotEmpty(panelData.boughtData) && CollectionUtils.isNotEmpty(panelData.recommendData);
                        }
                    }
                });

    }

    private void processPanelData(AdapterPanelData panelData, Object obj) {
        if (obj instanceof ProductListBean) {
            ProductListBean productListBean = (ProductListBean) obj;
            processRecommendData(panelData, productListBean);
        } else if (obj instanceof BoughtListBean) {
            BoughtListBean boughtListBean = (BoughtListBean) obj;
            processBoughtData(panelData, boughtListBean);
        }
    }

    private void processRecommendData(AdapterPanelData panelData, ProductListBean bean) {
        // No paging limit
        // https://sayweee.atlassian.net/browse/PCORE-7469
        // int maxOffset = Math.min(bean.total_count, MAX_OFFSET);
        int maxOffset = bean.total_count;
        List<ProductBean> list = bean.products;
        List<AdapterProductData> items = new ArrayList<>();
        recommendOffset += limit;
        if (list != null && !list.isEmpty()) {
            for (ProductBean item : list) {
                if (isProductValid(item)) {
                    ProductItemData p = new ProductItemData(CartSectionType.CART_SECTION_PANEL_PRODUCT, item);
                    p.setProductSource(Constants.Source.CART_PREFERENCE);
                    p.setModNm(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST).setModPos(2);
                    p.setSecNm(EagleTrackEvent.SecNm.PREFERENCE);
                    items.add(p);
                }
            }
            panelData.loadRecommendDataEnd = recommendOffset >= maxOffset;
        } else {
            panelData.loadRecommendDataEnd = true;
        }
        panelData.recommendData = items;
        isRecommendDataLoadEnd = panelData.loadRecommendDataEnd;
    }

    private void processBoughtData(AdapterPanelData panelData, BoughtListBean bean) {
        // No paging limit
        // https://sayweee.atlassian.net/browse/PCORE-7469
        // int maxOffset = Math.min(bean.total_count, MAX_OFFSET);
        int maxOffset = bean.total_count;
        List<ProductBean> list = bean.products;
        List<AdapterProductData> items = new ArrayList<>();
        boughtOffset += limit;
        if (list != null && !list.isEmpty()) {
            for (ProductBean item : list) {
                if (isProductValid(item)) {
                    ProductItemData p = new ProductItemData(CartSectionType.CART_SECTION_PANEL_PRODUCT, item);
                    p.setProductSource(Constants.Source.CART_BOUGHT);
                    p.setModNm(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST).setModPos(2);
                    p.setSecNm(EagleTrackEvent.SecNm.BOUGHT_BEFORE);
                    items.add(p);
                }
            }
            panelData.loadBoughtDataEnd = boughtOffset >= maxOffset;
        } else {
            panelData.loadBoughtDataEnd = true;
        }
        panelData.boughtData = items;
        isBoughtDataLoadEnd = panelData.loadBoughtDataEnd;
    }

    private void setPanelDataChanged(AdapterPanelData data) {
        adapterPanelData.postValue(data);

        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(data.recommendData);
        taskAssembler.addAll(data.boughtData);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(WeeeEvent.PageView.CART, WeeeEvent.PageView.CART));
    }

    private boolean isProductValid(ProductBean item) {
        return item != null
                && !OrderManager.REACH_LIMIT.equalsIgnoreCase(item.sold_status)
                && !OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(item.sold_status)
                && !OrderManager.SOLD_OUT.equalsIgnoreCase(item.sold_status)
                && !OrderManager.get().isReachLimit(item);
    }

    public void getMorePanelData(int index) {
        if (boughtOffset == 0 && recommendOffset == 0) {
            //第一次加载还未完成
            return;
        }
        if (isMultiData) {
            //兩種数据下加载更多
            if (index == 0) {
                getPreferenceData();
            } else {
                getBoughtData();
            }
        } else {
            //单个数据下加载更多
            if (!isBoughtDataLoadEnd) {
                getBoughtData();
            } else if (!isRecommendDataLoadEnd) {
                getPreferenceData();
            }
        }
    }

    private void loadNextUrl(NextUrlBean nextUrlBean, String cartId) {
        getLoader()
                .getHttpService()
                .getCartRecommend(nextUrlBean.nextUrl, OrderProvider.get().getZipCode(), OrderProvider.get().getDeliveryPickupDate())
                .compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<List<ProductBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<ProductBean>> response) {
                        List<NewItemBean> list = new ArrayList<>();
                        SectionCartActivityData activityData = nextUrlBean.activityData;
                        List<ProductBean> data = response.getData();
                        if (CollectionUtils.isNotEmpty(data)) {
                            for (int i = 0; i < data.size(); i++) {
                                ProductBean productBean = data.get(i);
                                boolean reachLimit = OrderManager.get().isReachLimit(productBean);
                                if (!reachLimit) {
                                    NewItemBean itemBean = OrderHelper.toItemBean(productBean);
                                    itemBean.source = activityData.targetData.isTradeInOriginalType() ? Constants.Source.TRADE_IN_CART : Constants.Source.ADD_ON_CART;
                                    itemBean.can_add = activityData.targetData.recommend.item_can_add;
                                    productBean.prod_pos = i;
                                    itemBean.new_source = OpHelper.buildNewSource(activityData.element, productBean);//换购手动拼接
                                    list.add(itemBean);
                                }
                            }
                        }
                        activityData.targetData.recommend.items = list;
                        SectionCartDealData sectionCartDealData = new SectionCartDealData();//新版活动模块
                        sectionCartDealData
                                .setActivityInfo(activityData.targetData)
                                .setTrackingInfo(activityData.element, activityData.ctx)
                                .setCartId(cartId);
                        sectionCartDealChangeData.postValue(sectionCartDealData);
                    }
                });
    }

    // Group order
    public void checkGroupOrderExists(String currentVendorId) {
        getLoader().getHttpService()
                .getSellerGroupStatus()
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<SellerGroupStatusBean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(ResponseBean<SellerGroupStatusBean> response) {
                        SellerGroupStatusBean data = response != null ? response.getData() : null;
                        if (data != null) {
                            data.currentVendorId = currentVendorId;
                        }
                        sellerGroupStatusLiveData.postValue(data);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    public void requestIndividualSectionCheckout(NewSectionBean section) {
        String cartDomain = Constants.CartDomain.DOMAIN_GROCERY;
        ArrayList<String> selectedKeys = new ArrayList<>();
        CollectionUtils.mapNotNullTo(selectedKeys, section.getCartItems(), it -> it.product_key);
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", cartDomain)
                .put("selected_product_keys", selectedKeys);
        getLoader().getHttpService().selectCartItems(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        sectionCheckoutData.postValue(section);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        failure.setObject(FAILURE_SECTION_CHECKOUT);
                    }
                });
    }
}
