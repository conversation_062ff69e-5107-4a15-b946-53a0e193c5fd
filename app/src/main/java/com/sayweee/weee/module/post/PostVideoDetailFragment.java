package com.sayweee.weee.module.post;

import static android.app.Activity.RESULT_OK;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSONObject;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.logger.Logger;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentPostVideoDetailBinding;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.post.adapter.PostBottomProductAdapter;
import com.sayweee.weee.module.post.adapter.PostVideoDetailAdapter;
import com.sayweee.weee.module.post.base.CmtBaseFragment;
import com.sayweee.weee.module.post.bean.PinVideoBean;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.bean.ProductNewBean;
import com.sayweee.weee.module.post.bean.RateTranslationBean;
import com.sayweee.weee.module.post.bean.VideoDealRightBean;
import com.sayweee.weee.module.post.edit.PostEditorActivity;
import com.sayweee.weee.module.post.edit.service.bean.HashTagItemBean;
import com.sayweee.weee.module.post.helper.CommentHelper;
import com.sayweee.weee.module.post.helper.SocialStatusHelper;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.module.post.profile.ProfileIntentCreator;
import com.sayweee.weee.module.post.service.IPostStatus;
import com.sayweee.weee.module.post.shared.CmtFailureData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.module.post.widget.VideoDealRightDialog;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.player.SimpleProgressVideoPlayer;
import com.sayweee.weee.player.SimpleVideoPlayer;
import com.sayweee.weee.player.cache.VideoCacheModel;
import com.sayweee.weee.player.cache.VideoProxyHelper;
import com.sayweee.weee.player.listener.PlayerProgressListener;
import com.sayweee.weee.player.listener.PlayerStateUiListener;
import com.sayweee.weee.player.view.PlayerVideoView;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.CommunityConfigBean;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.BoldTextView;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.DefaultClickableSpan;
import com.sayweee.weee.widget.PreventClickableSpan;
import com.sayweee.weee.widget.VerticalScrollTextView;
import com.sayweee.weee.widget.likebutton.DYLikeView;
import com.sayweee.weee.widget.likebutton.OnLikeListener;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.span.TagClickSpan;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.http.support.Utils;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.utils.Spanny;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

/**
 * Author:  winds
 * Date:    2021/7/19.
 * Desc:    视频晒单详情
 */
public class PostVideoDetailFragment extends CmtBaseFragment<PostVideoDetailViewModel> {

    public static final String TAG = "PostVideoDetailFragment";
    public static final int ANIMATION_DURATION = 500;
    public static final int ANIMATION_TIMER_START = 1000;
    public static final int ANIMATION_DISAPPEAR_DELAY = 5000;
    public static boolean isBubbleShowed;
    ImageView ivAvatar, ivFollowToggle;
    SimpleProgressVideoPlayer player;
    TextView tvCollect;
    TextView tvPost;
    TextView tvShare;
    TextView tvBottomProductCount;
    TextView tvTitle;
    TextView tvComment;
    TextView tvHide;
    TextView tvTranslate;
    TextView tvProduct;
    TextView tvRateTranslate;
    VerticalScrollTextView tvContentExpend;
    TextView layoutHashtags;
    private ImageView ivTagExpand;
    private View llExpend, layoutContentContainer, llPinVideoBubble, llTagExpand;

    private boolean isSmsSharing;
    private static final int POST_EDIT_RESULT = 999;
    private static final String STATUS_REVIEW = "A";
    protected PostBean bean;
    protected KeyboardChangeHelper helper;
    protected PostInputFragment inputFragment;
    protected int commentCount;
    private String titleText, commentText;
    private int forward_flag = 0;//快进
    private int rewind_flag = 0;//快退
    private int pause_flag = 0;//暂停
    private long videoCurrentPosition;
    private int currentState = -1000;
    private final List<String> segment_play = new ArrayList();
    private long beginTime;
    private String beginSegment;
    private boolean isFromPdp;//page source
    private boolean isFromHashTag;//page source
    private String tagId;
    private final List<ProductBean> productList = new ArrayList<>();
    private final List<ProductNewBean> productNewList = new ArrayList<>();
    private int error_code = 0;//多端统一定义错误码
    private long enterTime = 0;
    private long startPlayTime = 0;
    private Disposable mBubbleDisposable;

    RecyclerView rvList;
    PostBottomProductAdapter adapter;
    private TextView tvCommentInvisible;
    EagleImpressionTrackerIml eagleImpressionTracker;
    private DYLikeView tvLikeView;
    private boolean isLikeTextChecked, isViewVisible;

    private WeakReference<PostVideoDetailFragment> weakRef;

    private FragmentPostVideoDetailBinding binding;

    public static Fragment newInstance(int position, boolean isFromPdp, String tagId) {
        PostVideoDetailFragment fragment = new PostVideoDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("position", position);
        bundle.putBoolean("isFromPdp", isFromPdp);
        bundle.putBoolean("isFromHashTag", !EmptyUtils.isEmpty(tagId));
        bundle.putString("tagId", tagId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_post_video_detail;
    }

    public void initView(View view, Bundle savedInstanceState) {
        weakRef = new WeakReference<>(this);
        isViewVisible = VariantConfig.IS_VIEW_VISIBLE;
        binding = FragmentPostVideoDetailBinding.bind(contentView);
        eagleImpressionTracker = new EagleImpressionTrackerIml();
        enterTime = System.currentTimeMillis();
        ivAvatar = view.findViewById(R.id.iv_avatar);
        ivFollowToggle = view.findViewById(R.id.iv_follow_toggle);
        tvCollect = view.findViewById(R.id.tv_collect);
        tvLikeView = view.findViewById(R.id.tv_like_view);
        tvPost = view.findViewById(R.id.tv_post);
        tvShare = view.findViewById(R.id.tv_share);
        tvBottomProductCount = view.findViewById(R.id.tv_bottom_product_count);
        player = view.findViewById(R.id.player);
        tvTitle = view.findViewById(R.id.tv_title);
        tvTitle.setMovementMethod(LinkMovementMethod.getInstance());
        tvComment = view.findViewById(R.id.tv_comment);
        tvComment.setMovementMethod(LinkMovementMethod.getInstance());
        tvCommentInvisible = view.findViewById(R.id.tv_comment_invisible);
        addCommentAttachStateChangeListener();

        tvContentExpend = view.findViewById(R.id.tv_content_expend);
        tvContentExpend.setMovementMethod(LinkMovementMethod.getInstance());
        llExpend = view.findViewById(R.id.ll_expend);
        rvList = view.findViewById(R.id.rv_list);
        layoutHashtags = view.findViewById(R.id.layout_hashtags);
        tvHide = view.findViewById(R.id.tv_hide);
        tvTranslate = view.findViewById(R.id.tv_translate);
        tvRateTranslate = view.findViewById(R.id.tv_rate_translate);
        layoutContentContainer = view.findViewById(R.id.layout_content_container);
        llPinVideoBubble = view.findViewById(R.id.ll_pin_video_bubble);
        ivTagExpand = view.findViewById(R.id.iv_tag_expand);
        llTagExpand = view.findViewById(R.id.ll_tag_expand);
        tvProduct = view.findViewById(R.id.tv_product);

        loadBundleData();
        setClickEvent(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                handleClickEvent(v);
            }
        });
        setKeyboardObserver();
        setPlayerSlideConfig();
        setViewVisible();
        //消息中心跳转具体功能模块
        if (activity instanceof PostVideoDetailActivity) {
            String msgId = ((PostVideoDetailActivity) activity).msgId;
            String msgType = ((PostVideoDetailActivity) activity).msgType;
            if (Constants.MessageParams.TypeParams.COMMENT.equals(msgType) || Constants.MessageParams.TypeParams.LIKE.equals(msgType)) {
                onCommentPageTrigger(msgId, msgType);
            }
        }
        fillBottomData();
        tvLikeView.setOnLikeListener(new OnLikeListener() {
            @Override
            public void liked(DYLikeView likeView) {
                if (AccountManager.get().isLogin()) {
                    if (!isLikeTextChecked) {
                        bean.is_set_like = false;
                        onPriseTrigger();
                    }
                    isLikeTextChecked = false;
                } else {
                    tvLikeView.setLiked(false);
                    toLoginPage();
                }

            }

            @Override
            public void unLiked(DYLikeView likeView) {
                if (AccountManager.get().isLogin()) {
                    if (!isLikeTextChecked) {
                        bean.is_set_like = true;
                        onPriseTrigger();
                    }
                    isLikeTextChecked = false;
                } else {
                    tvLikeView.setLiked(false);
                    toLoginPage();
                }
            }
        });
    }

    private void setViewVisible() {
        ViewTools.setViewVisible(layoutHashtags,isViewVisible);
        ViewTools.setViewVisible(findViewById(R.id.tv_go_to_community), isViewVisible);
        ViewTools.setViewVisible(findViewById(R.id.tv_title_go_to_community), isViewVisible);
    }

    private void loadBundleData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            int position = arguments.getInt("position", 0);
            isFromPdp = arguments.getBoolean("isFromPdp", false);
            isFromHashTag = arguments.getBoolean("isFromHashTag", false);
            tagId = arguments.getString("tagId", "");

            // 从 Activity 的 adapter 获取数据
            if (getActivity() instanceof PostVideoDetailActivity) {
                PostVideoDetailActivity activity = (PostVideoDetailActivity) getActivity();
                PostVideoDetailAdapter adapter = activity.getAdapter();
                if (adapter != null && CollectionUtils.isNotEmpty(adapter.getPostList())) {
                    int index = Math.min(adapter.getItemCount() - 1, position);
                    bean = adapter.getItemData(index);
                    if (bean != null) {
                        ViewTools.setViewVisible(bean.isInvalid(), findViewById(R.id.tv_error));
                        ViewTools.setViewVisible(!bean.isInvalid(), player, layoutContentContainer);
                        if (!bean.isInvalid()) {
                            setVideoPlayUi();
                        }
                    }
                }
            }
        }
        if (bean == null && getActivity() instanceof PostVideoDetailActivity) {
            getActivity().finish();
        }
    }

    @Override
    public void loadData() {
        if (bean != null) {
            viewModel.getPostNewProducts(String.valueOf(bean.id));
        }
    }

    @Override
    public void attachModel() {
        viewModel.shareData.observe(this, new Observer<ShareBean>() {
            @Override
            public void onChanged(ShareBean bean) {
                showShareDialog(bean);
            }
        });

        viewModel.postCommentData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                setCommentCount(++commentCount);
            }
        });

        viewModel.deleteData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isDeleted) {
                if (Boolean.TRUE.equals(isDeleted)) {
                    SharedViewModel.get().postDeleteData.postValue(true);
                    activity.finish();
                }
            }
        });

        SharedViewModel.get().followChangeData.observe(this, new Observer<ArrayMap<String, Integer>>() {
            @Override
            public void onChanged(ArrayMap<String, Integer> data) {
                Integer status = data.get(bean.uid);
                if (status == null) {
                    return;
                }
                if (status != IPostStatus.STATUS_UNDEFINED) {
                    if (status == IPostStatus.STATUS_BLOCKED) {
                        bean.social_status = IPostStatus.BLOCKED;
                    }
                    bean.setFollowStatus(status == IPostStatus.STATUS_FOLLOWED);
                    setFollowStatus();
                }
            }
        });

        viewModel.pinVideoStatus.observe(this, new Observer<PinVideoBean>() {
            @Override
            public void onChanged(PinVideoBean pinVideoBean) {
                handlePinStatusChange(pinVideoBean);
            }
        });

        viewModel.postProductsNewBean.observe(this, new Observer<List<ProductNewBean>>() {
            @Override
            public void onChanged(List<ProductNewBean> productNewBeans) {
                productNewList.clear();
                productNewList.addAll(productNewBeans);
                if (!EmptyUtils.isEmpty(productNewBeans)) {
                    productList.clear();
                    List<AdapterProductData> adapterData = new ArrayList<>();
                    for (ProductNewBean bean : productNewBeans) {
                        productList.addAll(bean.product_list);
                    }
                    if (null != bean && !EmptyUtils.isEmpty(bean.valid_product_ids)) {
                        String[] validIds = bean.valid_product_ids.split(",");
                        List<ProductBean> validSortList = new ArrayList<>();
                        for (String a : validIds) {
                            for (ProductBean bean : productList) {
                                if (!EmptyUtils.isEmpty(bean.relate_product)) {
                                    bean.relate_product.get(0).setSimilar(true);
                                }
                                if (a.equalsIgnoreCase(!EmptyUtils.isEmpty(bean.relate_product) ? String.valueOf(bean.relate_product.get(0).id) : String.valueOf(bean.id))) {
                                    validSortList.add(!EmptyUtils.isEmpty(bean.relate_product) ? bean.relate_product.get(0) : bean);
                                }
                            }
                        }
                        for (ProductBean item : validSortList) {
                            if (!EmptyUtils.isEmpty(item) || !EmptyUtils.isEmpty(item.relate_product)) {
                                adapterData.add(new AdapterProductData(PostBottomProductAdapter.TYPE_PRODUCT, !EmptyUtils.isEmpty(item.relate_product) ? item.relate_product.get(0) : item));
                            }
                        }
                        adapter.setNewData(adapterData);
                    }
                }
                tvProduct.setText(String.valueOf(productList.size()));
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (!EmptyUtils.isEmpty(productNewList)) {
                    viewModel.getPostNewProducts(String.valueOf(bean.id));
                }
            }
        });

        CmtSharedViewModel.get().observeSpamFailureData(getViewLifecycleOwner(), new Observer<CmtFailureData>() {
            @Override
            public void onChanged(CmtFailureData data) {
                if (data == null) return;
//                if (data.getType() == CmtFailureData.TYPE_SPAM_PRAISE) {
//                    resetPrise();
//                }
                if (data.getType() == CmtFailureData.TYPE_SPAM_FOLLOW) {
                    resetFollow();
                }
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        if (bean != null && !bean.isInvalid()) {
            setTitleComment();
            player.onVideoResumeCompat();
        }
        syncFollowStatus();
        ProductSyncHelper.onPageResume(adapter);
        showBubbleByAnim();
        if (bean != null && !bean.isInvalid()) {
            if (EmptyUtils.isEmpty(bean.ref_product_ids)) {
                tvProduct.setText(String.valueOf(0));
            }
        }
        if (isSmsSharing) {
            isSmsSharing = false;
            Toaster.showToast(getString(R.string.s_share_success));
        }
        AppAnalytics.logPageView(WeeeEvent.PageView.COMM_VIDEO, this);
        eagleImpressionTracker.onPageResume(rvList);
    }

    @Override
    public void onPause() {
        super.onPause();
        player.onVideoPause();
        hideBubble(false);
        eagleImpressionTracker.onPagePause(rvList);
    }

    @Override
    public void onDestroyView() {
        if (helper != null) {
            helper.endObserve();
        }
        if (player != null) {
            player.release();
            player.setOnSeekBarDragCallback(null);
            player.setOnSlideProgressChangedListener(null);
            player.setGSYStateUiListener(null);
            player.setGSYVideoProgressListener(null);
            player = null;
        }
        if (tvLikeView != null) {
            tvLikeView.setOnLikeListener(null);
        }
        if (inputFragment != null) {
            inputFragment.setonInputCallback(null);
        }
        if (rvList != null && onScrollListener != null) {
            rvList.removeOnScrollListener(onScrollListener);
            onScrollListener = null;
        }
        if (tvCommentInvisible != null) {
            tvCommentInvisible.removeOnAttachStateChangeListener(commentAttachStateChangeListener);
            commentAttachStateChangeListener = null;
        }
        removeCommentGlobalLayoutListener();
        setClickEvent(null);
        weakRef.clear();
        weakRef = null;
        super.onDestroyView();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case POST_EDIT_RESULT:
                    startActivity(ProfileActivity.getIntent(activity)
                            .putExtra("status", STATUS_REVIEW)
                            .putExtra("type", "video")
                            .setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP));
                    activity.finish();
                    break;
            }
        }
    }

    private void setVideoPlayUi() {
        ImageLoader.load(activity, ivAvatar, WebpManager.get().getConvertUrl(ImageSpec.SPEC_AVATAR, bean.user_avatar), R.mipmap.post_user_placeholder);
        ivFollowToggle.setVisibility(String.valueOf(bean.user_id).equals(AccountManager.get().getUserId()) || !isViewVisible ? View.GONE : View.VISIBLE);
        setFollowStatus(bean.isFollowed());
        setPrise();
        setCommentCount(bean.comments_count);
        tvBottomProductCount.setText(String.valueOf(EmptyUtils.isEmpty(bean.ref_product_ids) ? 0 : (bean.ref_product_ids.split(",")).length));
        //video setting
        player.setLooping(true);
        player.setPlayerCover(WebpManager.get().getConvertUrl(ImageSpec.SPEC_375_AUTO, bean.resize_url));
        if (player instanceof SimpleProgressVideoPlayer) {
            ((SimpleProgressVideoPlayer) player).setDisplayRate(bean.size_rate);
        }
        player.setPlayTag(TAG + ":" + bean.id + ":" + bean.ref_url);

        VideoCacheModel cacheModel = new VideoCacheModel(bean.ref_url);
        String proxyUrl = VideoProxyHelper.getInstance().getProxyUrl(
                binding.getRoot().getContext(), cacheModel);
        player.setUp(proxyUrl, true, "");
        //player回调
        //快进快退
        player.setOnSeekBarDragCallback(new SimpleVideoPlayer.OnSeekBarDragCallback() {
            @Override
            public void onDrag(boolean isForward) {
                if (isForward) {
                    forward_flag = 1;
                } else {
                    rewind_flag = 1;
                }
            }
        });
        player.setGSYVideoProgressListener(new PlayerProgressListener() {

            @Override
            public void onProgress(long progress, long secProgress, long currentPosition, long duration) {
                //当前播放位置（暂停后再播放可能会有跳动）
                videoCurrentPosition = TimeUnit.MILLISECONDS.toSeconds(currentPosition);
                long totalDuration = TimeUnit.MILLISECONDS.toSeconds(duration);
                if ((progress > 95 || totalDuration - videoCurrentPosition < 0.5f) && shouldShowReachEndTip()) {
                    player.getGSYVideoManager().pause();
                    showReachEndTip(true);
                }
            }
        });
        player.setGSYStateUiListener(new PlayerStateUiListener() {
            @Override
            public void onStateChanged(int state) {
                switch (state) {
                    case PlayerVideoView.CURRENT_STATE_PLAYING:
                        if (startPlayTime == 0) {
                            startPlayTime = System.currentTimeMillis();
                        }
                        Logger.json("onStateChanged", "  CURRENT_STATE_PLAYING");
                        if (currentState != state) {
                            //开始播放
                            beginTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
                            beginSegment = beginTime + "-" + videoCurrentPosition + "-";
                        }
                        break;
                    case PlayerVideoView.CURRENT_STATE_PAUSE:
                        Logger.json("onStateChanged", "  CURRENT_STATE_PAUSE");
                        pause_flag = 1;
                        //暂停播放
                        addSegment();
                        break;
                    case PlayerVideoView.CURRENT_STATE_PLAYING_BUFFERING_START:
                        Logger.json("onStateChanged", "  CURRENT_STATE_PLAYING_BUFFERING_START");
                        //播放完成，开始第二次播放
                        addSegment();
                        break;
                    case PlayerVideoView.CURRENT_STATE_ERROR:
                        error_code = 10002;//播放器加载超时
                        break;
                }
                currentState = state;
            }
        });
        //hashtag
        setHashTags();
        tvTranslate.setText(getString(bean.useOrigin() ? R.string.detail_translate : R.string.detail_show_original));
        setUserInfo();
        setTitleComment();
    }

    private void setUserInfo() {
        if (EmptyUtils.isEmpty(bean.user_name)) return;
        StringBuilder builderName = new StringBuilder("@");
        builderName.append(bean.user_name);
        binding.tvUser.setText(builderName);

        if (bean.verified_seller) {
            binding.ivVerified.setVisibility(View.VISIBLE);
        } else {
            binding.ivVerified.setVisibility(View.GONE);
        }
    }

    private void setTitleComment() {
        titleText = bean.hasToggled() ? bean.title : bean.title_lang;
        commentText = bean.hasToggled() ? bean.comment : bean.comment_lang;
        if (EmptyUtils.isEmpty(commentText)) {
            commentText = "";
        }
        if (!EmptyUtils.isEmpty(commentText)) {
            commentText = commentText.trim();
        }
        //标题+内容
        setContent(/* isResetTranslateBtn = */true);
    }

    protected void setClickEvent(OnSafeClickListener clickListener) {
        //各个模块点击事件
        setOnClickListener(
                clickListener
                , R.id.iv_avatar, R.id.iv_follow_toggle, R.id.tv_collect, R.id.tv_post, R.id.tv_share, R.id.tv_product, R.id.tv_comment_input
                , R.id.tv_hide, R.id.tv_translate, R.id.tv_go_to_community
                , R.id.tv_go_back, R.id.iv_replay, R.id.ll_tag_expand, R.id.layout_bottom_product, R.id.tv_rate_translate
                , R.id.tv_comment, R.id.tv_content_expend);
    }

    @SuppressLint("NonConstantResourceId")
    private void handleClickEvent(@Nullable View view) {
        if (view == null) return;
        final PostBean bean = this.bean;
        if (bean == null) return;

        switch (view.getId()) {
            case R.id.iv_avatar:
                if (!isViewVisible && AccountManager.get().getUserIdInt() != bean.user_id) return;
                if (AccountManager.get().getUserIdInt() == bean.user_id) {
                    startActivity(ProfileActivity.getIntent(activity, "post-" + bean.id + "-v", bean.id, "detail", bean.uid));
                } else {
                    onUserProfileTrigger();
                }
                trackClick("user_icon");//访问制作者首页
                trackConsumption("user_icon");
                Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(bean.id), null, null);
                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.COMM_PROFILE,
                        -1,
                        null,
                        -1,
                        String.valueOf(bean.user_id),
                        -1,
                        EagleTrackEvent.TargetType.PROFILE,
                        EagleTrackEvent.ClickType.VIEW,
                        ctx);
                break;
            case R.id.iv_follow_toggle:
                if (bean.isBlocked() || bean.isFollowed()) {
                    // blocked (show) and followed (hide) return
                    return;
                }
                if (AccountManager.get().isLogin()) {
                    boolean isFollow = !bean.isFollowed();
                    SocialStatusHelper.OnFollowListener onFollowListener;
                    onFollowListener = (userId, followStatus) -> {
                        bean.setFollowStatus(followStatus);
//                        setFollowStatus();
                        animateFollowStatus();
                    };
                    SocialStatusHelper.setFollowStatus(bean.uid, isFollow, onFollowListener);
                    if (isFollow) {
                        trackClick("user_follow");//成为粉丝
                    }
                    Map<String, Object> ctx1 = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(bean.id), null, null);
                    EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.COMM_PROFILE,
                            -1,
                            null,
                            -1,
                            String.valueOf(bean.user_id),
                            -1,
                            EagleTrackEvent.TargetType.PROFILE,
                            isFollow ? EagleTrackEvent.ClickType.FOLLOW : EagleTrackEvent.ClickType.UNFOLLOW,
                            ctx1);
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
                break;
            case R.id.tv_collect:
                //视频点赞取消
                isLikeTextChecked = true;
                if (AccountManager.get().isLogin()) {
                    tvLikeView.performClick();
                } else {
                    toLoginPage();
                }

                onPriseTrigger();
                break;
            case R.id.tv_post:
                //查看评论
                onCommentPageTrigger(null, Constants.MessageParams.TypeParams.COMMENT);
                trackClick("post_detail_check_comments");//Check Existing Comments
                Map<String, Object> ctx1 = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(bean.id), null, null);
                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.VIDEO_DETAIL,
                        -1,
                        null,
                        -1,
                        String.valueOf(bean.id),
                        -1,
                        EagleTrackEvent.TargetType.POST,
                        EagleTrackEvent.ClickType.COMMENT,
                        ctx1);
                break;
            case R.id.tv_comment_input:
                if (AccountManager.get().isLogin()) {
                    onCommentTrigger();
                    trackClick("post_detail_comment");//添加评论
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
                break;
            case R.id.tv_share:
                toShare();
                trackClick("post_share");//share
                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.VIDEO_DETAIL,
                        -1,
                        null,
                        -1,
                        String.valueOf(bean.id),
                        -1,
                        EagleTrackEvent.TargetType.POST,
                        EagleTrackEvent.ClickType.SHARE);
                break;
            case R.id.tv_product:
            case R.id.layout_bottom_product:
                onPostProductTrigger();
                trackClick("post_item_list");//Items
                break;
            case R.id.tv_hide:
                ViewTools.setViewVisible(View.GONE, llExpend);
                ViewTools.setViewVisible(View.VISIBLE, tvComment);
                setTranslateBtn(true);
                break;
            case R.id.tv_content_expend:
                //收起文字
                if (tvContentExpend.canClick()) {
                    ViewTools.setViewVisible(View.GONE, llExpend);
                    ViewTools.setViewVisible(View.VISIBLE, tvComment);
                    setTranslateBtn(true);
                }
                break;
            case R.id.tv_translate:
                bean.toggleTranslateStatus();
                //翻译按钮
                tvTranslate.setText(getString(bean.useOrigin() ? R.string.detail_translate : R.string.detail_show_original));
                titleText = bean.hasToggled() ? bean.title : bean.title_lang;
                commentText = bean.hasToggled() ? bean.comment : bean.comment_lang;
                if (EmptyUtils.isEmpty(commentText)) {
                    commentText = "";
                }
                commentText = commentText.trim();
                setContent(/* isResetTranslateBtn = */false);
                if (llExpend.getVisibility() == View.VISIBLE) {
                    setExpandContent();
                }
                break;
            case R.id.tv_go_to_community:
                if (isFromPdp) {
                    SharedViewModel.get().toPost();
                } else {
                    toWeb(Constants.Url.HASH_TAGS);
                }
                break;
            case R.id.tv_go_back:
                FragmentActivity activity = getActivity();
                if (activity != null) {
                    activity.finish();
                }
                break;
            case R.id.iv_replay:
                showReachEndTip(false);
                player.getGSYVideoManager().seekTo(0);
                player.getGSYVideoManager().start();
                break;
            case R.id.ll_tag_expand:
                tagExpand = !tagExpand;
                if (tagExpand) {
                    expandTag();
                } else {
                    hideTag();
                }
                ivTagExpand.setImageResource(tagExpand ? R.mipmap.pic_video_up : R.mipmap.pic_video_down);
                break;
            case R.id.tv_rate_translate:
                onRateTranslationTrigger();
                break;
            case R.id.tv_comment:
                if (tvCommentInvisible.getLineCount() > 1) {
                    expandContent();
                }
                break;
        }
    }

    protected void setPlayerSlideConfig() {
        if (player != null) {
            player.setOnSlideProgressChangedListener(new SimpleProgressVideoPlayer.OnSlideProgressChangedListener() {
                @Override
                public void onSlideChanged(boolean visible, long current, long total) {
                    if (layoutContentContainer != null) {
                        layoutContentContainer.setVisibility(visible ? View.INVISIBLE : View.VISIBLE);
                    }
                }
            });
        }
    }

    protected void setKeyboardObserver() {
        final View content = getContentView();
        content.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(@NonNull View view) {
                helper = new KeyboardChangeHelper(content);
                helper.startObserve().setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

                    @Override
                    public void onKeyboardHide() {
                        //dismissInputPanel(false);
                    }
                });
            }

            @Override
            public void onViewDetachedFromWindow(@NonNull View view) {
                if (helper != null) {
                    helper.endObserve();
                }
            }
        });

    }

    private void onPostProductTrigger() {
        CommunityConfigBean communityConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.COMMUNITY);
        boolean enable = communityConfig != null && communityConfig.video_pop_enable;
        if (!EmptyUtils.isEmpty(productNewList)) {
            String tag = PostProductNewFragment.class.getSimpleName() + bean.id;
            Fragment fragment = getChildFragmentManager().findFragmentByTag(tag);
            if (fragment instanceof PostProductNewFragment) {
                ((PostProductNewFragment) fragment).show(getChildFragmentManager(), tag);
                ((PostProductNewFragment) fragment).setOnPostAddItemListener(new PostProductNewFragment.updateListener() {
                    @Override
                    public void updateData() {
//                        viewModel.getPostNewProducts(String.valueOf(bean.id));
                        ProductSyncHelper.onPageResume(adapter);
                    }

                    @Override
                    public void changedState(View bottomSheet, int state) {
                        startChangedState(bottomSheet, state, enable);
                    }

                    @Override
                    public void changedOffset(View bottomSheet, float slideOffset) {
                        startAnimator(bottomSheet, enable);
                    }
                });
            } else {
                PostProductNewFragment postProductNewFragment = (PostProductNewFragment) PostProductNewFragment.newInstance(
                        productNewList, bean.id, String.format(Constants.Source.PORTAL_POST_DETAIL_PDP, "v", bean.id),
                        productList.size(), true, null);
                postProductNewFragment.show(getChildFragmentManager(), tag);
                postProductNewFragment.setOnPostAddItemListener(new PostProductNewFragment.updateListener() {
                    @Override
                    public void updateData() {
//                        viewModel.getPostNewProducts(String.valueOf(bean.id));
                        ProductSyncHelper.onPageResume(adapter);
                    }

                    @Override
                    public void changedState(View bottomSheet, int state) {
                        startChangedState(bottomSheet, state, enable);
                    }

                    @Override
                    public void changedOffset(View bottomSheet, float slideOffset) {
                        startAnimator(bottomSheet, enable);
                    }
                });
            }
        }
    }

    private void onUserProfileTrigger() {
        CommunityConfigBean communityConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.COMMUNITY);
        boolean enable = communityConfig != null && communityConfig.video_pop_enable;
        if (!EmptyUtils.isEmpty(bean)) {
            String tag = PostProfileFragment.class.getSimpleName() + bean.id;
            PostProfileFragment postProfileFragment = PostProfileFragment.newInstance(bean.id, bean.uid);
            postProfileFragment.show(getChildFragmentManager(), tag);
            postProfileFragment.setOnProfileMoveListener(new PostProfileFragment.onProfileMoveListener() {
                @Override
                public void changedState(View bottomSheet, int state) {
                    startChangedState(bottomSheet, state, enable);
                }

                @Override
                public void changedOffset(View bottomSheet, float slideOffset) {
                    startAnimator(bottomSheet, enable);
                }

                @Override
                public void dialogDismiss(String status) {
                    if (ivFollowToggle.getVisibility() == View.VISIBLE) {
                        bean.social_status = status;
                        SharedViewModel.get().saveFollowStatusData(bean.uid, bean.isBlocked() ? IPostStatus.STATUS_BLOCKED : bean.isFollowed() ? IPostStatus.STATUS_FOLLOWED : IPostStatus.STATUS_UNDEFINED);
                        ivFollowToggle.setImageResource(bean.isBlocked() ? R.mipmap.pic_post_blocked : bean.isFollowed() ? R.mipmap.iv_follow_checked : R.mipmap.iv_follow_plus);
                    }
                }
            });
        }
    }

    public void hideProfileFragment() {
        if (getActivity() != null) {
            Fragment fragment = getChildFragmentManager()
                    .findFragmentByTag(PostProfileFragment.class.getSimpleName() + bean.id);
            if (fragment instanceof PostProfileFragment) {
                ((PostProfileFragment) fragment).dismiss();
            }
        }
    }

    private void onRateTranslationTrigger() {
        if (AccountManager.get().isLogin()) {
            if (bean != null) {
                String[] questions = {getString(R.string.rate_translation_first_question)
                        , getString(R.string.rate_translation_second_question)
                        , getString(R.string.rate_translation_third_question)
                        , getString(R.string.rate_translation_fourth_question)};
                List<RateTranslationBean> list = new ArrayList<>();
                for (int i = 0; i < questions.length; i++) {
                    RateTranslationBean bean = new RateTranslationBean();
                    bean.pos = i;
                    bean.name = questions[i];
                    list.add(bean);
                }
                String tag = PostRateTranslationFragment.class.getSimpleName() + bean.id + "rate";
                PostRateTranslationFragment.newInstance("translation", "post", String.valueOf(bean.id), list, bean.origin_lang)
                        .show(getChildFragmentManager(), tag);
            }
        } else {
            startActivity(AccountIntentCreator.getIntent(activity));
        }

    }

    private void onReportTrigger() {
        if (bean != null) {
            String[] questions = {getString(R.string.its_unoriginal)
                    , getString(R.string.it_has_incorrectly_tagged_items)
                    , getString(R.string.its_inappropriate)
                    , getString(R.string.its_spam)
                    , getString(R.string.rate_translation_fourth_question)
            };
            List<RateTranslationBean> list = new ArrayList<>();
            for (int i = 0; i < questions.length; i++) {
                RateTranslationBean bean = new RateTranslationBean();
                bean.pos = i;
                bean.name = questions[i];
                list.add(bean);
            }
            String tag = PostRateTranslationFragment.class.getSimpleName() + bean.id + "report";
            PostRateTranslationFragment.newInstance("report", "post", String.valueOf(bean.id), list, getString(R.string.is_there_a_problem_with_the_video), getString(R.string.report_video), false)
                    .show(getChildFragmentManager(), tag);
        }

    }

    protected void onCommentPageTrigger(String msgId, String msgType) {
        final PostBean bean = this.bean;
        if (bean == null) return;
        CommunityConfigBean communityConfig = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.COMMUNITY);
        boolean enable = communityConfig != null && communityConfig.video_pop_enable;
        String tag = PostCommentLikeFragment.class.getSimpleName() + bean.id;
//            videoWidth = player.getGSYVideoManager().getVideoWidth();
//            videoHeight = player.getGSYVideoManager().getVideoHeight();
        PostCommentLikeFragment.newInstance(true, bean.id, bean.uid, bean.like_count, bean.comments_count, null, msgId, msgType)
                .setOnCommentChangedCallback(new PostCommentLikeFragment.OnCommentChangedCallback() {
                    @Override
                    public void onCommentChanged(int num) {
                        setCommentCount(num);
                    }

                    @Override
                    public void changedState(View bottomSheet, int state) {
                        startChangedState(bottomSheet, state, enable);
                    }

                    @Override
                    public void changedOffset(View bottomSheet, float slideOffset) {
                        startAnimator(bottomSheet, enable);
                    }
                })
                .show(getChildFragmentManager(), tag);
    }

    private void startChangedState(View bottomSheet, int state, boolean enable) {
        if (!enable) {
            return;
        }
        float width = ScreenUtils.getScreenWidth(activity);
        int height = ScreenUtils.getDisplayWithoutNavigationBar(activity)[1];
        if (state == BottomSheetBehavior.STATE_EXPANDED) {
            float x = width / 2f;
            //剩余的高度
            float scale = height - height * 0.7f + CommonTools.dp2px(47);
            //显示区域占据的比例
            float scales = scale / (height);
            if (bean.size_rate > 1) {
                bottomSheet.post(() -> {
                    player.animate()
                            .scaleX(scales)
//                                                .translationY(-scale)
                            .scaleY(scales).setInterpolator(new LinearInterpolator())
                            .setDuration(220).start();
                });
                bl = scales;
            } else {
                float heightScale = -(height - height * 0.77f + CommonTools.dp2px(47));
                float sa = 1f;
                if (bean.size_rate > 0.75) {
                    sa = 0.6f;
                    heightScale = heightScale * (sa - 0.2f);
                }
                float finalSa = sa;
                float finalHeightScale = heightScale;
                bottomSheet.post(() -> {
                    player.animate()
                            .scaleY(finalSa)
                            .scaleX(finalSa)
                            .translationY(finalHeightScale).setInterpolator(new LinearInterpolator())
                            .setDuration(220).start();
                    bl = finalHeightScale;
                    bYl = finalSa;
                });
            }
            player.setPivotX(x);
            player.setPivotY(0);
            player.setProgressCanVisible(false);
            player.setPlayBtnVisible(false);
        } else if (state == BottomSheetBehavior.STATE_COLLAPSED) {
            if (bean.size_rate > 1) {
                player.animate()
                        .scaleX(1f)
                        .scaleY(1f).setInterpolator(new LinearInterpolator())
                        .setDuration(220).start();
            } else {
                player.animate()
                        .scaleY(1f)
                        .scaleX(1f)
                        .translationY(0).setInterpolator(new LinearInterpolator())
                        .setDuration(220).start();
            }
            player.setPivotX(width / 2f);
            player.setPivotY(0);
            player.setProgressCanVisible(true);
            player.setPlayBtnVisible(true);
        }
    }

    private float bl = 0;
    private float bYl = 0;

    private void startAnimator(View parent, boolean enable) {
        if (!enable) {
            return;
        }
        float width = ScreenUtils.getScreenWidth(activity);
        float statusAndNavHeight = ScreenUtils.getStatusBarHeight(activity);
        float height = ScreenUtils.getScreenHeight(activity);
        float x = width / 2f;
        float scale = height - height * 0.75f;
        if (bean.size_rate <= 1) {
            float minBl = bl;
            float widthScale = (height + statusAndNavHeight) - (CommonTools.dp2px(550));
            float widthPy = (parent.getY() + statusAndNavHeight) / (height + statusAndNavHeight);
            float widthScaleY = widthPy > 1 ? 1 : widthPy;
            float max = Math.max((minBl + widthPy / 3f), minBl);
            float v = (minBl + widthPy / 3f) > 1 ? 1f : max;
            float b = bYl + ((parent.getY() / height) / (bYl >= 1 ? 2.2f : 1.5f));
            float scaleY = bYl >= 1 ? 1f : Math.min(b, 1f);
            player.setTranslationY(Math.min(bl + (parent.getY() / 2.2f), 0));
            player.setScaleX(scaleY);
            player.setScaleY(scaleY);
            player.setProgressCanVisible(scaleY >= 1f);
        } else {
            float py = bl + (parent.getY() / height);
            //最小的宽高比例
            float scaleY = py > 1 ? 1 : py;
            player.setScaleX(scaleY);
            player.setScaleY(scaleY);
            player.setProgressCanVisible(scaleY >= 1f);
        }

        player.setPivotX(x);
        player.setPivotY(0);
    }

    protected void onCommentTrigger() {
        if (inputFragment == null) {
            inputFragment = PostInputFragment.newInstance().setUid(bean.uid).setonInputCallback(new PostInputFragment.OnInputCallback() {
                @Override
                public void onInputTrigger(String content) {
                    if (TextUtils.isEmpty(content)) {
                        return;
                    }
                    if (viewModel != null && bean != null) {
                        viewModel.postComment(bean.id, content);
                    }
                    dismissInputPanel(true);
                }
            });
        }
        showInputPanel(getString(R.string.s_say_something));
    }

    protected void showInputPanel(String hint) {
        if (bean != null && inputFragment != null) {
            String tag = PostInputFragment.class.getSimpleName() + bean.id;
            inputFragment.setInputHint(hint).show(getChildFragmentManager(), tag);
        }
    }

    protected void dismissInputPanel(boolean result) {
        if (inputFragment != null) {
            if (result) {
                inputFragment.setInputText(null);
            }
            inputFragment.dismissAllowingStateLoss();
        }
    }

    private void onPriseTrigger() {
        if (AccountManager.get().isLogin()) {
            bean.is_set_like = !bean.is_set_like;
            bean.like_count = bean.is_set_like ? bean.like_count + 1 : bean.like_count - 1;
            PostCollectManager.get().toggleCollect(true, bean.id, bean.is_set_like, bean.like_count, true);
            setPrise();
            if (bean.is_set_like) {
                trackClick("post_detail_like_post");//点赞 track
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(bean.id), null, null);
            EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.VIDEO_DETAIL,
                    -1,
                    null,
                    -1,
                    String.valueOf(bean.id),
                    -1,
                    EagleTrackEvent.TargetType.POST,
                    bean.is_set_like ? EagleTrackEvent.ClickType.LIKE : EagleTrackEvent.ClickType.UNLIKE,
                    ctx);
        } else {
            toLoginPage();
        }
    }

    private void toShare() {
//        if (AccountManager.get().isLogin()) {
        ShareBean shareBean = viewModel.shareData.getValue();
        if (shareBean == null) {
            if (bean != null) {
                viewModel.getVideoShareData(bean.id);
            }
        } else {
            showShareDialog(shareBean);
        }
//        } else {
//            toLoginPage();
//        }
    }

    private void showShareDialog(ShareBean shareBean) {
        if (shareBean != null) {
            new ShareDialog(activity).setShareData(shareBean)
                    .setAttachedData(bean.id, "post")
                    .setResultTip(getString(R.string.s_share_success))
                    .setOnShareChannelListener(new ShareDialog.OnShareListener() {
                        @Override
                        public void share(ShareDialog dialog, String type) {
                            if (Constants.ShareChannel.SMS.equalsIgnoreCase(type)) {
                                isSmsSharing = true;
                            }
                        }
                    }).show();
        }
    }

    private void toLoginPage() {
        startActivity(AccountIntentCreator.getIntent(activity));
    }

    private void setPrise() {
//        ((DrawableTextView) tvCollect).setTopDrawable(ContextCompat.getDrawable(activity, bean.is_set_like ? R.mipmap.heart_post : R.mipmap.heart_post_normal)
//                , CommonTools.dp2px(33)
//                , CommonTools.dp2px(36));
        tvLikeView.setLiked(bean.is_set_like);
        tvCollect.setText(bean.like_count <= 0 ? getString(R.string.post_like) : PostCollectManager.get().getLikeCountLabel(bean.like_count));
    }

    private void resetPrise() {
        bean.is_set_like = !bean.is_set_like;
        bean.like_count = bean.is_set_like ? bean.like_count + 1 : bean.like_count - 1;
        setPrise();
    }

    private void resetFollow() {
        bean.setFollowStatus(!bean.isFollowed());
        setFollowStatus();
    }

    private void setCommentCount(int count) {
        this.commentCount = count;
        if (bean != null) {
            bean.comments_count = this.commentCount;
        }
        if (tvPost != null) {
            tvPost.setText(count <= 0 ? getString(R.string.post_cmt) : String.valueOf(count));
        }
    }

    public void showPrivilegeDialog() {
        hideBubble(true);
        if (bean == null || !Utils.isNetworkConnected(activity) || !AccountManager.get().isLogin()) {
            return;
        }
        new VideoDealRightDialog(activity)
                .setData(new VideoDealRightBean(bean))
                .setOnClickListener(new VideoDealRightDialog.OnDialogClickListener() {
                    @Override
                    public void onPinToTopClick(VideoDealRightBean videoDealRightBean) {
                        viewModel.getPinVideoStatus(String.valueOf(bean.id), videoDealRightBean.getRequestPinStatus(), activity.getString(R.string.s_network_error));
                    }

                    @Override
                    public void viewInsight(String inSightUrl) {
                        if (!TextUtils.isEmpty(inSightUrl)) {
                            activity.startActivity(WebViewActivity.getIntent(activity, inSightUrl));
                        }
                    }

                    @Override
                    public void onEditClick() {
                        startActivityForResult(PostEditorActivity.getIntentOnEdit(activity, bean, productList, bean.hash_tags), POST_EDIT_RESULT);
                    }

                    @Override
                    public void onDeleteClick() {
                        deleteVideo(bean);
                    }

                    @Override
                    public void onReportClick() {
                        onReportTrigger();
                    }
                }).show();
    }

    private void handlePinStatusChange(PinVideoBean pinVideoBean) {

        if (pinVideoBean == null || bean == null) {
            return;
        }
        boolean isDialogType = false;
        if (VideoDealRightBean.STATUS_PINNED.equals(bean.pinning_status) || VideoDealRightBean.STATUS_PINNED_AS.equals(bean.pinning_status)) {
            bean.pinning_status = VideoDealRightBean.STATUS_NO_PIN;
            isDialogType = false;
            handlePostListData(pinVideoBean);
        } else if (VideoDealRightBean.STATUS_NO_PIN.equals(bean.pinning_status)) {
            bean.pinning_status = VideoDealRightBean.STATUS_PINNED;
            isDialogType = true;
            handlePostListData(pinVideoBean);
        } else if (VideoDealRightBean.STATUS_PIN_TO_LIMIT.equals(bean.pinning_status)) {
            isDialogType = true;
        }
        if (isDialogType) {
            new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                    .setTitleUp((dialog, view) -> dialog.dismiss()
                            , pinVideoBean.title
                            , pinVideoBean.description
                            , pinVideoBean.btn
                    ).addHelperCallback((dialog, helper) -> {
                        helper.getItemView().setMinimumHeight(CommonTools.dp2px(180f));
                        helper.getView(R.id.tv_content).setMinimumHeight(CommonTools.dp2px(40f));

                    }).show();
        } else {
            Toaster.showToast(pinVideoBean.title);
        }
    }

    private void handlePostListData(PinVideoBean pinVideoBean) {
        if (activity instanceof PostVideoDetailActivity) {
            ((PostVideoDetailActivity) activity).handlePostListData(pinVideoBean.reached_limit, bean.mainKey());
        }
    }


    public void showBubbleByAnim() {
        if (isBubbleShowed || bean == null || !bean.show_pinning) {
            return;
        }
        Observable.timer(ANIMATION_TIMER_START, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnNext(aLong -> {
                    llPinVideoBubble.setPivotX(llPinVideoBubble.getWidth());
                    llPinVideoBubble.setPivotY(-llPinVideoBubble.getHeight() >> 1);
                    llPinVideoBubble.animate().scaleX(1.0f).scaleY(1.0f).alpha(1.0f).setDuration(ANIMATION_DURATION).start();
                })
                .delay((long) ANIMATION_DISAPPEAR_DELAY + ANIMATION_DURATION, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new io.reactivex.Observer<Long>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        mBubbleDisposable = d;
                        llPinVideoBubble.setVisibility(View.VISIBLE);
                        llPinVideoBubble.setScaleX(0);
                        llPinVideoBubble.setScaleY(0);
                        llPinVideoBubble.setAlpha(0);
                    }

                    @Override
                    public void onNext(@NonNull Long aLong) {

                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        llPinVideoBubble.setVisibility(View.GONE);
                    }

                    @Override
                    public void onComplete() {
                        hideBubble(true);
                    }
                });
    }


    private void hideBubble(boolean isAnim) {
        if (isBubbleShowed) {
            return;
        }
        if (mBubbleDisposable != null && !mBubbleDisposable.isDisposed()) {
            mBubbleDisposable.dispose();
        }
        if (bean != null && bean.show_pinning) {
            isBubbleShowed = true;
        }
        if (llPinVideoBubble.getVisibility() == View.GONE) {
            return;
        }
        llPinVideoBubble.clearAnimation();
        if (isAnim) {
            llPinVideoBubble.animate().scaleX(0f).scaleY(0f).alpha(0f).setDuration(ANIMATION_DURATION)
                    .setListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            super.onAnimationEnd(animation);
                            llPinVideoBubble.setVisibility(View.GONE);
                        }
                    }).start();
        } else {
            llPinVideoBubble.setVisibility(View.GONE);
        }
    }

    private void deleteVideo(PostBean bean) {
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setUp(new OnDialogClickListener() {
                           @Override
                           public void onClick(WrapperDialog dialog, View view) {
                               dialog.dismiss();
                               viewModel.deletePost(bean.id);
                           }
                       }, activity.getString(R.string.delete_this_video)
                        , activity.getString(R.string.yes_delete)
                        , activity.getString(R.string.cancel))
                .addHelperCallback(new WrapperDialog.HelperCallback() {
                    @Override
                    public void help(Dialog dialog, ViewHelper helper) {
                        TextView content = helper.getView(R.id.tv_content);
                        if (content instanceof BoldTextView) {
                            content.setTextSize(20);
                            ((BoldTextView) content).setTextBoldStyle();
                        }
                    }
                })
                .show();
    }

    private ViewTreeObserver.OnGlobalLayoutListener commentGlobalLayoutListener;

    private void removeCommentGlobalLayoutListener() {
        if (tvCommentInvisible != null && commentGlobalLayoutListener != null) {
            ViewTreeObserver obs = tvCommentInvisible.getViewTreeObserver();
            obs.removeOnGlobalLayoutListener(commentGlobalLayoutListener);
            commentGlobalLayoutListener = null;
        }
    }

    private void addCommentGlobalLayoutListener() {
        commentGlobalLayoutListener = null;
        commentGlobalLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (weakRef != null && weakRef.get() != null) {
                    weakRef.get().onCommentGlobalLayout();
                }
            }
        };
        ViewTreeObserver obs = tvCommentInvisible.getViewTreeObserver();
        obs.addOnGlobalLayoutListener(commentGlobalLayoutListener);
    }

    private void onCommentGlobalLayout() {
        if (!isAdded()) {
            return;
        }
        final Context context = getContext();
        if (context == null) {
            return;
        }
//        if (tvCommentInvisible.getMeasuredWidth() > 0) {
//            ViewTreeObserver obs = tvCommentInvisible.getViewTreeObserver();
//            obs.removeOnGlobalLayoutListener(this);
//        }
        final String finalCommentText = tvCommentInvisible.getText().toString();
        final Layout layout = tvCommentInvisible.getLayout();
        if (layout.getLineCount() > 1) {
            int lineEndIndex = layout.getLineEnd(0);
            final String see_more = "..." + getResources().getString(R.string.s_see_more_cap);
            int seeMoreLength = see_more.length() + getResources().getInteger(R.integer.config_see_more_extra_length);
            Spanny content = new Spanny();
            int temp = Math.min(lineEndIndex - seeMoreLength, lineEndIndex);
            if (temp > 0) {
                content.append(finalCommentText.subSequence(0, temp));
            }
            content.append(see_more, new TextAppearanceSpan(context, R.style.style_fluid_root_body_base_bold));
            DefaultClickableSpan clickableSpan = new DefaultClickableSpan() {
                @Override
                public void onClick(View widget) {
                    expandContent();
                    trackClick("post_video_see_more"); // See More
                }

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    // Remove the underline
                    ds.setUnderlineText(false);
                }
            };
            int start = Math.max(content.length() - see_more.length(), 0);
            content.setSpan(new PreventClickableSpan(clickableSpan), start, content.length(),
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            content.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context,
                    R.color.root_color_white_static)), start, content.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            tvComment.setText(content);
        } else {
            tvComment.setText(finalCommentText);
        }
    }

    private View.OnAttachStateChangeListener commentAttachStateChangeListener;

    private void addCommentAttachStateChangeListener() {
        commentAttachStateChangeListener = new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(@NonNull View view) {
                if (weakRef != null && weakRef.get() != null) {
                    weakRef.get().addCommentGlobalLayoutListener();
                }
            }

            @Override
            public void onViewDetachedFromWindow(@NonNull View view) {
                if (weakRef != null && weakRef.get() != null) {
                    weakRef.get().removeCommentGlobalLayoutListener();
                }
            }
        };
        tvCommentInvisible.addOnAttachStateChangeListener(commentAttachStateChangeListener);
    }


    private void setContent(boolean isResetTranslateBtn) {
        Spanny titleSpanny = new Spanny();
        if (bean.isFeatured()) {
            Drawable drawable = ContextCompat.getDrawable(activity, R.mipmap.post_star);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(18), CommonTools.dp2px(18));
            }
            titleSpanny.append("", new CenterImageSpan(drawable)).append(" ");
        }
        titleSpanny.append(titleText);
        tvTitle.setText(titleSpanny);

        if (EmptyUtils.isEmpty(commentText)) {
            ViewTools.setViewVisible(View.GONE, tvComment);
            ViewTools.setViewVisible(View.GONE, llExpend);
        }

        final Spanny finalCommentText = CommentHelper.resolveStyle(commentText, Color.WHITE);
        tvCommentInvisible.setText(finalCommentText);

        if (isResetTranslateBtn) {
            setTranslateBtn(true);
        }
    }

    private void expandContent() {
        setTranslateBtn(true);
        setExpandContent();
    }

    private void setExpandContent() {
        Spanny titleSpanny = new Spanny();
        if (bean.isFeatured()) {
            Drawable drawable = ContextCompat.getDrawable(activity, R.mipmap.post_star);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(18), CommonTools.dp2px(18));
            }
            titleSpanny.append("", new CenterImageSpan(drawable)).append(" ");
        }
        titleSpanny.append(CommentHelper.resolveStyle(titleText, Color.WHITE));
        tvTitle.setText(titleSpanny);

        if (EmptyUtils.isEmpty(commentText)) {
            ViewTools.setViewVisible(View.GONE, tvComment);
            ViewTools.setViewVisible(View.GONE, llExpend);
            return;
        }
        tvContentExpend.setText(CommentHelper.resolveStyle(commentText, Color.WHITE));
        tvContentExpend.postDelayed(new Runnable() {
            @Override
            public void run() {
                ViewTools.setViewVisible(View.GONE, tvComment);
                ViewTools.setViewVisible(View.VISIBLE, llExpend);
                tvContentExpend.setScrollY(0);
            }
        }, 100L);
        // 动态行间距
        // setLineSpacing(tvContentExpend, content.toString());
    }

    private boolean tagExpand = false;

    private void setHashTags() {
        if (bean != null && !EmptyUtils.isEmpty(bean.hash_tags)) {
            layoutHashtags.setVisibility(View.VISIBLE);
            SpannableStringBuilder tags = new SpannableStringBuilder();
            String label;
            for (HashTagItemBean item : bean.hash_tags) {
                if (item == null) continue;
                label = "#" + item.label + " ";
                tags.append(label);
                TagClickSpan clickableSpan = new TagClickSpan(activity, item.tag_id, bean.id);
                clickableSpan.setUrl(item.url);
                tags.setSpan(clickableSpan, tags.length() - label.length(), tags.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }

            layoutHashtags.setText(tags);
            layoutHashtags.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    ViewTreeObserver obs = layoutHashtags.getViewTreeObserver();
                    obs.removeOnGlobalLayoutListener(this);
                    if (layoutHashtags.getLineCount() > 1) {
                        int lineEndIndex = layoutHashtags.getLayout().getLineEnd(0);
                        //计算"查看宽度"所占比率
                        Paint paint = new Paint();
                        paint.setTextSize(CommonTools.sp2px(14));
                        paint.setTypeface(Typeface.DEFAULT_BOLD);
                        SpannableStringBuilder content = new SpannableStringBuilder();
                        content.append(tags.subSequence(0, lineEndIndex));
                        layoutHashtags.setText(content);
                        llTagExpand.setVisibility(View.VISIBLE);
                    }
                }
            });
            layoutHashtags.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }

    private void hideTag() {
        SpannableStringBuilder tags = new SpannableStringBuilder();
        String label;
        if (bean != null && bean.hash_tags != null) {
            for (HashTagItemBean item : bean.hash_tags) {
                if (item == null) continue;
                label = "#" + item.label + " ";
                tags.append(label);
                TagClickSpan clickableSpan = new TagClickSpan(activity, item.tag_id, bean.id);
                clickableSpan.setUrl(item.url);
                tags.setSpan(clickableSpan, tags.length() - label.length(), tags.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
        if (layoutHashtags.getLineCount() > 1) {
            int lineEndIndex = layoutHashtags.getLayout().getLineEnd(0);
            //计算"查看宽度"所占比率
            Paint paint = new Paint();
            paint.setTextSize(CommonTools.sp2px(14));
            paint.setTypeface(Typeface.DEFAULT_BOLD);
            SpannableStringBuilder content = new SpannableStringBuilder();
            content.append(tags.subSequence(0, lineEndIndex));
            layoutHashtags.setText(content);
            llTagExpand.setVisibility(View.VISIBLE);
        }
    }

    private void expandTag() {
        if (bean != null && !EmptyUtils.isEmpty(bean.hash_tags)) {
            layoutHashtags.setVisibility(View.VISIBLE);
            SpannableStringBuilder tags = new SpannableStringBuilder();
            String label;
            for (HashTagItemBean item : bean.hash_tags) {
                if (item == null) continue;
                label = "#" + item.label + " ";
                tags.append(label);
                TagClickSpan clickableSpan = new TagClickSpan(activity, item.tag_id, bean.id);
                clickableSpan.setUrl(item.url);
                tags.setSpan(clickableSpan, tags.length() - label.length(), tags.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            layoutHashtags.setText(tags);
        }
    }

    public void updateTarget(PostBean data) {
        if (data != null) {
            this.bean = data;
            setPrise();
            setCommentCount(bean.comments_count);
        }
    }

    private void setTranslateBtn(boolean b) {
        tvTranslate.setVisibility(bean.showTranslatePortal() ? View.VISIBLE : View.GONE);
        findViewById(R.id.ll_rate_translate).setVisibility(bean.showTranslatePortal() ? View.VISIBLE : View.GONE);
    }

    private void setFollowStatus() {
        if (isAdded()) {
            if (activity instanceof IPostStatus) {
                ((IPostStatus) activity).notifyFollowChanged(bean.user_id, bean.isBlocked() ? IPostStatus.STATUS_BLOCKED : bean.isFollowed() ? IPostStatus.STATUS_FOLLOWED : IPostStatus.STATUS_UNFOLLOWED);
            }
        }
        setFollowStatus(bean.isFollowed());
    }

    private void animateFollowStatus() {

        AnimatorSet animatorSet = new AnimatorSet();

        animatorSet.playTogether(getExpandAnimator(), getDismissAnimator());

        ivFollowToggle.setAlpha(0f);
        ivFollowToggle.clearAnimation();
        animatorSet.start();
    }

    private AnimatorSet getExpandAnimator() {
        AnimatorSet expandAnimator = new AnimatorSet();
        ObjectAnimator scaleAnimX = ObjectAnimator.ofFloat(ivFollowToggle,
                View.SCALE_X, 0.4f, 1f);
        ObjectAnimator scaleAnimY = ObjectAnimator.ofFloat(ivFollowToggle,
                View.SCALE_Y, 0.4f, 1f);
        ObjectAnimator alphaAnim = ObjectAnimator.ofFloat(ivFollowToggle,
                View.ALPHA, 0.7f, 1f);

        expandAnimator.playTogether(scaleAnimX, scaleAnimY, alphaAnim);
        expandAnimator.setDuration(300);

        return expandAnimator;
    }

    private AnimatorSet getDismissAnimator() {
        AnimatorSet animator = new AnimatorSet();
        ObjectAnimator scaleAnimX = ObjectAnimator.ofFloat(ivFollowToggle,
                View.SCALE_X, 1f, 0f, 1f);
        ObjectAnimator scaleAnimY = ObjectAnimator.ofFloat(ivFollowToggle,
                View.SCALE_Y, 1f, 0f, 1f);
        ObjectAnimator alphaAnim = ObjectAnimator.ofFloat(ivFollowToggle,
                View.ALPHA, 1f, 0f);

        animator.playTogether(scaleAnimX, scaleAnimY, alphaAnim);
        animator.setDuration(300);
        animator.setStartDelay(1300);

        return animator;
    }

    private void setFollowStatus(boolean isFollowed) {
        if (ivFollowToggle.getVisibility() == View.VISIBLE) {
            ivFollowToggle.setImageResource(bean.isBlocked() ? R.mipmap.pic_post_blocked : isFollowed ? R.mipmap.iv_follow_checked : R.mipmap.iv_follow_plus);
        }
        // hide icon when followed
        ivFollowToggle.setAlpha(isFollowed ? 0f : 1f);
    }

    private void syncFollowStatus() {
        if (isAdded()) {
            if (activity instanceof IPostStatus && bean != null) {
                int followStatus = ((IPostStatus) activity).getFollowStatus(bean.user_id);
                if (followStatus != IPostStatus.STATUS_UNDEFINED) {
                    bean.setFollowStatus(followStatus == IPostStatus.STATUS_FOLLOWED);
                    setFollowStatus(followStatus == IPostStatus.STATUS_FOLLOWED);
                }
            }
        }
    }

    private void trackClick(String click_type) {
        AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                new TrackParams().put("post_id", bean == null ? 0 : bean.id).put("click_type", click_type).put("content_type", "v").get());
    }

    private void addSegment() {
        if (currentState == PlayerVideoView.CURRENT_STATE_PLAYING) {
            long duration = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()) - beginTime;
            if (duration > 0) {
                String segment = beginSegment + duration;
                segment_play.add(segment);
            }
        }
    }

    private boolean shouldShowReachEndTip() {
        if (activity instanceof PostVideoDetailActivity) {
            return ((PostVideoDetailActivity) activity).pagerScroll2TheLast() && (isFromHashTag || isFromPdp) && "video".equalsIgnoreCase(bean.type);
        }
        return false;
    }

    private void showReachEndTip(boolean isVisible) {
        findViewById(R.id.layout_community_container).setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    //播放记录track
    public void trackConsumption(String quit_type) {
        String source = null;
        if (activity instanceof PostVideoDetailActivity) {
            source = ((PostVideoDetailActivity) activity).trackSource;
        }
        //首帧耗时
        long first_frame_time = startPlayTime - enterTime;
        if (startPlayTime == 0) {
            error_code = 10000;//启播之前主动停止播放(用户跳过该视频）
        } else if (EmptyUtils.isEmpty(bean.ref_url)) {
            error_code = 10001;//没有播放地址
        }
        addSegment();

        int total_play_time = 0;
        for (String segment : segment_play) {
            String[] split = segment.split("-");
            if (!EmptyUtils.isEmpty(split) && split.length >= 3) {
                String time = split[2];
                total_play_time = total_play_time + DecimalTools.parseInt(time);
            }
        }

        //app tracker
        AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_VIDEO_CONSUMPTION,
                new TrackParams().put("post_id", bean.id)
                        .put("quit_type", quit_type)
                        .put("forward_flag", forward_flag)
                        .put("rewind_flag", rewind_flag)
                        .put("pause_flag", pause_flag)
                        .put("rec_id", bean.rec_id)
                        .put("total_play_time", total_play_time)
                        .putNonNull("source", source)
                        .put("segment_play", JSONObject.parseArray(JsonUtils.toJSONString(segment_play)))
                        .put("first_frame_time", first_frame_time > 0 ? first_frame_time : 0)
                        .put("error_code", error_code)
                        .get());
        //upload server
        Logger.d("track===>" + total_play_time);
        if (viewModel != null) {
            viewModel.track(bean.id, videoCurrentPosition, total_play_time);
        }

        forward_flag = 0;
        rewind_flag = 0;
        pause_flag = 0;
        currentState = -1000;
        segment_play.clear();
    }

    public void toWeb(String url) {
        if (activity != null) {
            startActivity(WebViewActivity.getIntent(activity, url));
        }
    }

    private RecyclerView.OnScrollListener onScrollListener;

    private void fillBottomData() {
        if (bean != null) {
            boolean showInputPanel = EmptyUtils.isEmpty(bean.valid_product_ids);
            ViewTools.setViewVisible(findViewById(R.id.layout_comment), showInputPanel);
            ViewTools.setViewVisible(findViewById(R.id.tv_product), showInputPanel);
            ViewTools.setViewVisible(findViewById(R.id.rv_list), !showInputPanel);
            ViewTools.setViewVisible(findViewById(R.id.layout_bottom_product_container), !showInputPanel);

            adapter = new PostBottomProductAdapter();
            adapter.setAttachedPost(bean.id);
            rvList.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false));
            rvList.setAdapter(adapter);
            onScrollListener = new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        reportEagleImpressionEvent();
                    }
                    OpActionHelper.notifyScrollStateChanged(newState);
                }
            };
            rvList.addOnScrollListener(onScrollListener);
        }
    }

    private void reportEagleImpressionEvent() {
        if (rvList != null) {
            rvList.postDelayed((new Runnable() {
                @Override
                public void run() {
                    if (isAdded() && isVisible() && eagleImpressionTracker != null) {
                        eagleImpressionTracker.trackImpression(rvList);
                    }
                }
            }), 0);
        }
    }
}
