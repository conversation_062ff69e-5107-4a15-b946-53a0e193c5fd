package com.sayweee.weee.module.product.provider;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cate.product.ProductDetailPolicyDialog;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.data.PdpTagData;
import com.sayweee.weee.module.search.widget.TagAdapter;
import com.sayweee.weee.module.search.widget.TagFlowLayout;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.helper.PromoHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.NewCenterImageSpan;
import com.sayweee.widget.shape.ShapeLinearLayout;
import com.sayweee.wrapper.utils.Spanny;
import com.zhy.view.flowlayout.FlowLayout;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class PdpTagProvider extends SimpleSectionProvider<PdpTagData, AdapterViewHolder> implements ImpressionProvider<PdpTagData> {


    @Override
    public int getItemType() {
        return PdpItemType.PDP_TAG;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_pdp_tag;
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        setLayoutParamsMargin(holder, CommonTools.dp2px(20));
        super.onViewAttachedToWindow(holder);
    }

    protected void setLayoutParamsMargin(AdapterViewHolder holder, int margin) {
        if (holder != null) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof RecyclerView.LayoutParams) {
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                params.leftMargin = margin;
                params.rightMargin = margin;
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpTagData item) {
        TagFlowLayout flContent = helper.getView(R.id.layout_flow);
        flContent.removeAllViews();
        ProductBean product = item.t;
        ProductDetailBean.ProductFeatureBean productFeatureBean = null;
        if (item.t instanceof ProductDetailBean.ProductFeatureBean) {
            productFeatureBean = (ProductDetailBean.ProductFeatureBean) item.t;
        }
        ProductDetailBean.ProductFeatureBean finalProductFeatureBean = productFeatureBean;
        List<ProductBean.LabelListBean> filteredList = new ArrayList<>();
        for (ProductBean.LabelListBean label : item.t.label_list) {
            // 判断是否需要排除off
            if (!label.isOffLabelKey()) {
                if (label.isRewardLabelKey()) {
                    int level = AccountManager.get().getLoyaltyVipLevel();
                    if (level > 1) {
                        filteredList.add(label);
                    }
                } else {
                    filteredList.add(label);
                }
            }
        }
        TagAdapter<ProductBean.LabelListBean> tagAdapter = new TagAdapter<ProductBean.LabelListBean>(filteredList) {
            @Override
            public View getView(FlowLayout parent, int position, ProductBean.LabelListBean label) {
                View tagView = LayoutInflater.from(context).inflate(R.layout.tag_pdp_flow, flContent, false);
                // 设置子项视图的 Margin
                ViewGroup.MarginLayoutParams params = new ViewGroup.MarginLayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        CommonTools.dp2px(18)
                );
                params.rightMargin = CommonTools.dp2px(8);
                params.topMargin = CommonTools.dp2px(8);
                tagView.setLayoutParams(params);
                boolean hasReward = label.isRewardLabelKey();
                ImageView ivIcon = tagView.findViewById(R.id.iv_icon);
                helper.setVisibleCompat(ivIcon, false);
                int level = AccountManager.get().getLoyaltyVipLevel();
                TextView tvMarker = tagView.findViewById(R.id.tv_title);

                if (hasReward) {
                    if (level > 1) {
                        helper.setVisibleCompat(ivIcon, true);
                        ivIcon.setImageResource(level > 2 ? R.mipmap.pic_level3_new_icon : R.mipmap.pic_level2_new_icon);
                    }
                }
                Spanny spanny = new Spanny();

                if (label.isDeliveryKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_delivery_icon,
                            CommonTools.dp2px(12),
                            CommonTools.dp2px(12)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable)).append(" ");
                }

                if (label.isPantryKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_pantry_icon,
                            CommonTools.dp2px(12),
                            CommonTools.dp2px(12)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable)).append(" ");
                }

                if (label.isAlcoholKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_alcohol_icon,
                            CommonTools.dp2px(12),
                            CommonTools.dp2px(12)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable)).append(" ");
                }

                if (label.isColdPackKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_cold_pack_new_icon,
                            CommonTools.dp2px(12),
                            CommonTools.dp2px(12)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable)).append(" ");
                }

                spanny.append(label.label_name.toUpperCase());

                final long[] clickTime = {0};
                if (label.isSnapLabelKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_green_invaild,
                            CommonTools.dp2px(12),
                            CommonTools.dp2px(12)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable));
                }

                if (label.isColdPackKey() || label.isAlcoholKey() || label.isPantryKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_pantry_info,
                            CommonTools.dp2px(10),
                            CommonTools.dp2px(10)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable));
                }

                if (label.isFreshnessKey()) {
                    Drawable drawable = ViewTools.getDrawableWithBounds(
                            context,
                            R.mipmap.pic_freshness_info,
                            CommonTools.dp2px(10),
                            CommonTools.dp2px(10)
                    );
                    spanny.append("  ").append("", new NewCenterImageSpan(drawable));
                }
                tvMarker.setText(spanny);
                if (!EmptyUtils.isEmpty(label.label_font_color)) {
                    ViewTools.applyTextColor(tvMarker, label.label_font_color);
                }

                if (!EmptyUtils.isEmpty(label.label_color)) {
                    ShapeLinearLayout layout = tagView.findViewById(R.id.layout_tag);
                    layout.setBackgroundSolidDrawable(ViewTools.parseColor(label.label_color, Color.WHITE), CommonTools.dp2px(4));
                }

                tagView.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if(label.isSnapLabelKey()){
                            long currentTimeMillis = System.currentTimeMillis();
                            if (currentTimeMillis - clickTime[0] < 2000) {
                                return;
                            }
                            clickTime[0] = currentTimeMillis;
                            clickTrack(item.t.id, item.modPos, item.traceId, label.label_key);
                            Spanny spanny1 = new Spanny();
                            Drawable drawable = ViewTools.getDrawableWithBounds(
                                    context,
                                    R.mipmap.pic_ebt,
                                    CommonTools.dp2px(29),
                                    CommonTools.dp2px(32)
                            );
                            if (drawable != null) {
                                spanny1.append("", new NewCenterImageSpan(drawable)).append(" ");
                            }
                            spanny1.append(context.getString(R.string.ebt_pop_title));
                            new ProductDetailPolicyDialog(context, spanny1, context.getString(R.string.ebt_pop_sub_title)).show();
                        } else if(label.isColdPackKey()){
                            long currentTimeMillis = System.currentTimeMillis();
                            if (currentTimeMillis - clickTime[0] < 2000) {
                                return;
                            }
                            clickTime[0] = currentTimeMillis;
                            clickTrack(item.t.id, item.modPos, item.traceId, label.label_key);
                            PromoHelper.showPdpColdPackageTips();
                        } else if(label.isAlcoholKey()){
                            long currentTimeMillis = System.currentTimeMillis();
                            if (currentTimeMillis - clickTime[0] < 2000) {
                                return;
                            }
                            clickTime[0] = currentTimeMillis;
                            String url = AlcoholHelper.getAlcoholFaqUrl(
                                    finalProductFeatureBean.isSeller(),
                                    finalProductFeatureBean.vender_info_view != null ? finalProductFeatureBean.vender_info_view.vender_id : null
                            );
                            context.startActivity(WebViewActivity.getIntent(context, url));
                        } else if(label.isPantryKey()){
                            long currentTimeMillis = System.currentTimeMillis();
                            if (currentTimeMillis - clickTime[0] < 2000) {
                                return;
                            }
                            clickTime[0] = currentTimeMillis;
                            clickTrack(item.t.id, item.modPos, item.traceId, label.label_key);
                            PantryHelper.toPantryTerms();
                        } else if(label.isFreshnessKey()){
                            long currentTimeMillis = System.currentTimeMillis();
                            if (currentTimeMillis - clickTime[0] < 2000) {
                                return;
                            }
                            clickTime[0] = currentTimeMillis;
                            if (onTagListener != null) {
                                clickTrack(item.t.id, item.modPos, item.traceId, label.label_key);
                                onTagListener.onFreshnessClick(finalProductFeatureBean.policy_pop_config_key);
                            }
                        }
                    }
                });
                return tagView;
            }

        };
        flContent.setAdapter(tagAdapter);
    }


    public interface OnTagClick {
        void onFreshnessClick(String policy_pop_config_key);
    }

    private OnTagClick onTagListener;

    public void setOnTagListener(OnTagClick onTagListener) {
        this.onTagListener = onTagListener;
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(PdpTagData item, int position) {
        if (EmptyUtils.isEmpty(item.t.label_list)) {
            return Collections.emptyList();
        }
        String productId = String.valueOf(item.t.id);
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, productId, null, null, null, item.traceId);
        List<ImpressionBean> list = new ArrayList<>();
        for (int i = 0; i < item.t.label_list.size(); i++) {
            ProductBean.LabelListBean bean = item.t.label_list.get(i);
            if (bean.isPantryKey() || bean.isAlcoholKey() || bean.isColdPackKey() || bean.isFreshnessKey() || bean.isSnapLabelKey()) {
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(EagleTrackEvent.ModNm.PRODUCT_DETAIL)
                        .setMod_pos(item.modPos)
                        .setSec_nm(item.t.label_list.get(i).label_key)
                        .addCtx(ctx)
                        .build().getParams();
                list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, i + bean.label_key + productId));
            }
        }
        return list;
    }

    private void clickTrack(int productId, int modPos, String traceId, String secNm) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(productId), null, null, null, traceId);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.PRODUCT_DETAIL)
                .setMod_pos(modPos)
                .setSec_nm(secNm)
                .setTargetNm(secNm)
                .setTargetType(EagleTrackEvent.TargetType.MESSAGE)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

}
