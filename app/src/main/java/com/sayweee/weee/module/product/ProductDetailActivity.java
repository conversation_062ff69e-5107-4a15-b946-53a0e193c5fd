package com.sayweee.weee.module.product;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;
import static com.sayweee.weee.service.webp.ImageSpec.SPEC_BANNER_LINE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.util.Base64;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.request.RequestOptions;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.preload.PreloadManager;
import com.sayweee.preload.PreloadObserver;
import com.sayweee.preload.PreloadTask;
import com.sayweee.track.model.ExtendEvent;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ActivityNewProductDetailBinding;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.CartAdapter;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cart.service.MkplHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cate.bean.VendorIntroductionBean;
import com.sayweee.weee.module.cate.product.ProductDetailPolicyDialog;
import com.sayweee.weee.module.cate.product.ProductGroupHelper;
import com.sayweee.weee.module.cate.product.ProductGroupingFragment;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.cate.product.bean.AdapterProductGroupData;
import com.sayweee.weee.module.cate.product.bean.GroupProduct;
import com.sayweee.weee.module.cate.product.bean.GroupProperty;
import com.sayweee.weee.module.cate.product.bean.TitleAnchorBean;
import com.sayweee.weee.module.category.FirstAddItemDialog;
import com.sayweee.weee.module.category.bean.CategoryCarouselBean;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.dialog.ScreenshotShareDialog;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.home.theme.IndicatorHelper;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedModelProvider;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.data.CmsCategoryFeedData;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.preload.PreloadProductAiQuestionTask;
import com.sayweee.weee.module.preload.PreloadProductDetailTask;
import com.sayweee.weee.module.preload.PreloadProductMiddleBannerTask;
import com.sayweee.weee.module.preload.PreloadProductModulesTask;
import com.sayweee.weee.module.preload.PreloadProductPostTask;
import com.sayweee.weee.module.preload.PreloadProductPromotionTask;
import com.sayweee.weee.module.preload.PreloadProductReferralTask;
import com.sayweee.weee.module.preload.PreloadProductReviewTask;
import com.sayweee.weee.module.preload.PreloadProductSectionTask;
import com.sayweee.weee.module.preload.PreloadProductSummaryTask;
import com.sayweee.weee.module.product.adapter.OnAiQuestionListener;
import com.sayweee.weee.module.product.adapter.PdpAiQuestionAdapter;
import com.sayweee.weee.module.product.bean.PdpContactButtonTextBean;
import com.sayweee.weee.module.product.bean.PdpItemBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.weee.module.product.bottom.ProductGiftFragment;
import com.sayweee.weee.module.product.data.PdpAiQuestionData;
import com.sayweee.weee.module.product.data.PdpBarInfoData;
import com.sayweee.weee.module.product.data.PdpBnplData;
import com.sayweee.weee.module.product.data.PdpFreshnessData;
import com.sayweee.weee.module.product.data.PdpGlobalData;
import com.sayweee.weee.module.product.data.PdpItemData;
import com.sayweee.weee.module.product.data.PdpPostData;
import com.sayweee.weee.module.product.data.PdpProductDetailData;
import com.sayweee.weee.module.product.data.PdpProductGroupData;
import com.sayweee.weee.module.product.data.PdpProductTitleData;
import com.sayweee.weee.module.product.data.PdpReviewData;
import com.sayweee.weee.module.product.manager.ProductAffiliateManager;
import com.sayweee.weee.module.product.manager.ProductCouponManager;
import com.sayweee.weee.module.product.manager.ProductGiftCardManager;
import com.sayweee.weee.module.product.manager.ProductOperationManager;
import com.sayweee.weee.module.product.provider.PdpBannerProvider;
import com.sayweee.weee.module.product.provider.PdpItemAdapter;
import com.sayweee.weee.module.product.provider.PdpNewProductGroupProvider;
import com.sayweee.weee.module.product.provider.PdpQuickLinksProvider;
import com.sayweee.weee.module.product.provider.PdpSummaryProvider;
import com.sayweee.weee.module.product.provider.PdpTagProvider;
import com.sayweee.weee.module.product.service.PdpWebViewPool;
import com.sayweee.weee.module.product.service.ProductDetailViewModel;
import com.sayweee.weee.module.product.service.ProductOpHelper;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.seller.SellerPageParams;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebRouter;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.handler.OnPageRefreshCallback;
import com.sayweee.weee.player.mute.PostCoverVideoManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.helper.ProgressBarManager;
import com.sayweee.weee.service.helper.PromoHelper;
import com.sayweee.weee.service.share.ShareHelper;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.service.timer.service.TimerChangedListener;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BackgroundColorTransformation;
import com.sayweee.weee.utils.BitmapOptimizer;
import com.sayweee.weee.utils.BitmapUtils;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.LinearTopSmoothScroller;
import com.sayweee.weee.widget.op.BottomOpLayout;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.recycler.SafeLinearLayoutManager;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.toaster.IToaster;
import com.sayweee.widget.toaster.IToasterController;
import com.sayweee.widget.toaster.IToasterOptions;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.listener.OnAdapterChildClickListener;
import com.sayweee.wrapper.utils.Spanny;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/5/9.
 * Desc:
 */
public class ProductDetailActivity extends WrapperMvvmActivity<ProductDetailViewModel>
        implements IToasterController, IContentFeedSharedModelProvider, OnPageRefreshCallback {

    private static final String BUNDLE_PAGE_PARAMS = "productPageParams";

    private ActivityNewProductDetailBinding binding;

    private FirstAddItemDialog firstAddItemDialog;

    protected PdpItemAdapter adapter;

    private ProductPageParams pageParams;

    private List<TitleAnchorBean> titleIndicatorList;
    private final Map<String, GroupProduct> productMap = new LinkedHashMap<>();

    String lastTarget;
    private boolean isPdpTracked;
    private int bannerHeight;
    private int top = 0;

    private boolean isFirstCLickCart = true;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;
    private KeyboardChangeHelper helper;
    private boolean isOpVisible; // 记录当前操作栏的可见状态

    private Observer<Map<String, Serializable>> postCollectsObserver;

    private ProductOperationManager productOpManager;

    private ProductGiftCardManager giftCardManager;

    private ProductAffiliateManager affiliateManager;

    private ProductCouponManager couponManager;
    private ScreenshotManager screenshotManager;
    private ScreenshotShareDialog screenshotShareDialog;
    private PdpAiQuestionAdapter pdpAiQuestionAdapter;
    private Handler handler = new Handler(Looper.getMainLooper());
    private Runnable scrollCheckRunnable;
    private static final long SCROLL_STOP_DELAY = 50L;

    private boolean isScrolling = false;
    private int lastOffset = Integer.MIN_VALUE; // 初始化为不可能的值
    private String topBannerColor;

    public static Intent getIntent(Context context, ProductPageParams productPageParams) {
        if (productPageParams != null) {
            List<PreloadTask<?>> tasks = new ArrayList<>();
            tasks.add(new PreloadProductDetailTask(productPageParams));
            tasks.add(new PreloadProductPromotionTask(productPageParams));
            tasks.add(new PreloadProductReviewTask(productPageParams));
            tasks.add(new PreloadProductPostTask(productPageParams));
            tasks.add(new PreloadProductModulesTask(productPageParams));
            tasks.add(new PreloadProductSummaryTask(productPageParams));
            if (AccountManager.get().isLogin()) {
                tasks.add(new PreloadProductAiQuestionTask(productPageParams));
            }
            tasks.add(new PreloadProductMiddleBannerTask(productPageParams));
            tasks.add(new PreloadProductSectionTask(productPageParams));
            if (!AccountManager.get().isNewUser() && !ProductDetailViewModel.hasShownShareTempter()) {
                tasks.add(new PreloadProductReferralTask(productPageParams));
            }
            productPageParams.preloadId = PreloadManager.preloadMerge(tasks);
        }
        return new Intent(context, ProductDetailActivity.class)
                .putExtra(BUNDLE_PAGE_PARAMS, productPageParams);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_new_product_detail;
    }

    @Override
    protected void beforeCreate() {
        super.beforeCreate();
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.PRODUCT,
                String.valueOf(hashCode()));
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        binding = ActivityNewProductDetailBinding.bind(contentView);
        lastTarget = AppTracker.get().getLastTarget();
        Serializable productSerializable = getIntent().getSerializableExtra(BUNDLE_PAGE_PARAMS);
        if (productSerializable instanceof ProductPageParams) {
            pageParams = (ProductPageParams) productSerializable;
            lastTarget = AppTracker.get().getLastTarget();
            if (!EmptyUtils.isEmpty(pageParams.product)) {
                pageParams.productId = pageParams.product.id;
            }
        } else {
            finish();
            return;
        }
        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);
        setupRecyclerview(binding.rvList);

        // 如果含产品数据过来显示加购，从url过来的不显示加购，获取数据后在显示
        setPdpBannerListener();
        setPdpInfoListener();
        setPdpTagListener();
        setPdpSummaryListener();
        setPdpQuickLinksListener();
        setEditTextOnFocusChangeListener();
        setPdpAiQuestionListener();
        setPdpProductGroupListener();
        setPageClickListener();
        findViewById(R.id.layout_progress).setTranslationY(0);
        binding.layoutSnackBar.layoutRoot.setVisibility(View.GONE);
        setScreenShotListener();
        binding.vStatus.setBackgroundColor(ViewTools.parseColor("#ffffff", Color.WHITE));
        binding.layoutCurrent.setBackgroundColor(ViewTools.parseColor("#ffffff", Color.WHITE));

        new ProductTraceObserver(this) {
            @Override
            protected void handleProductSalesTraceChange() {
                ProductTraceViewHelper.notify(binding.rvList);
            }
        }.setExtraTopic(WeeeEvent.PageView.PRODUCT).start();
    }

    private void initAppBarListener(boolean isSku) {
        final AppBarLayout appBarLayout = findViewById(R.id.abl_event);
        ViewTools.setViewVisible(binding.layoutTopSku, isSku);
        ViewTools.setViewVisible(binding.ivTopBanner, !isSku);
        ViewTools.setViewVisible(binding.ablEvent, true);
        binding.ablEvent.setExpanded(true, true);
        appBarLayout.addOnOffsetChangedListener(offSetChangedListener);
    }

    private void initActOperationManager() {
        // 初始化产品操作管理器
        productOpManager = new ProductOperationManager(this, binding.layoutOp, pageParams);
        productOpManager.getCartNotifyLiveData().observe(this, status -> {
            if (ProductOperationManager.NOTIFY_SHOW_COLLECT_TIPS.equalsIgnoreCase(status)) {
                notifyShowCollectTips();
            } else {
                adapter.notifyCartButtonData(status);
            }
        });

        productOpManager.getCartOftenNotifyLiveData().observe(this, status -> {
            adapter.notifyOftenPairedWith();
        });

        setProductOpConfig(pageParams.product);
    }

    public void initGiftCardManager() {
        // 初始化Gift Card操作管理
        giftCardManager = new ProductGiftCardManager(this, adapter, binding.bottomView,
                binding.tvGiftCardNewUser, status -> {
            if (ProductGiftCardManager.NOTIFY_GIFT_RELOAD.equalsIgnoreCase(status)) {
                reload(false);
            }
        });
    }

    private void initAffiliateManager() {
        // 初始化 Product Affiliate 管理
        affiliateManager = new ProductAffiliateManager(
                this,
                viewModel.getAffiliateRepository(),
                binding.ivAffiliateBtn,
                binding.ivToast,
                binding.tvToast,
                binding.layoutToast,
                pageParams
        );
        affiliateManager.getAffiliateNotifyLiveData().observe(this, status -> {
            if (status.startsWith(ProductAffiliateManager.NOTIFY_SHOW_AFFILIATE_TOAST)) {
                String toastLink = status.substring(ProductAffiliateManager.NOTIFY_SHOW_AFFILIATE_TOAST.length() + 1);
                toWebPage(toastLink);
            }
            if (ProductAffiliateManager.NOTIFY_REFRESH_PRODUCT_DATA.equalsIgnoreCase(status)) {
                refreshAffiliate();
            }
        });
    }

    private void refreshAffiliate() {
        viewModel.refreshProductData(pageParams.productId);
    }

    private void initCouponManager() {
        // 初始化Coupon管理
        couponManager = new ProductCouponManager(this, adapter,
                viewModel.getCouponRepository(), pageParams);
    }


    private void setupRecyclerview(RecyclerView recyclerView) {
        recyclerView.setLayoutManager(new SafeLinearLayoutManager(activity));
        recyclerView.addItemDecoration(new SimpleSectionItemDecoration());
        recyclerView.setHasFixedSize(true);
        recyclerView.setItemViewCacheSize(10);
        recyclerView.getRecycledViewPool().setMaxRecycledViews(CartAdapter.TYPE_PANEL, 5);
        adapter = new PdpItemAdapter(String.valueOf(hashCode()), scrollStatePersist);
        adapter.onCtxAdded(null, null, null, null, String.valueOf(pageParams.productId), null, null);
        recyclerView.setAdapter(adapter);

        recyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (pageParams.product != null && pageParams.product.isGiftCard()) return;
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    adapter.onPageScrollStateChanged(recyclerView, newState);
                    if (isAiScroll) {
                        adapter.notifyAiQuestion();
                        isAiScroll = false;
                    }
                    LabelScrollHandler.notifyScrollStateChanged(recyclerView);
                    isTabInit = false;
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (pageParams.product != null && pageParams.product.isGiftCard()) return;
                RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
                if (manager instanceof LinearLayoutManager) {
                    int position = ((LinearLayoutManager) manager).findFirstVisibleItemPosition();
                    int lastPosition = ((LinearLayoutManager) manager).findLastVisibleItemPosition();
                    int screenHeight = recyclerView.getHeight();
                    setAtTop(waterFallPosition > 0 && waterFallPosition + 1 >= position && waterFallPosition <= lastPosition);
                    setAiChange((LinearLayoutManager) manager);
                    for (int i = position; i <= lastPosition; i++) {
                        View view = manager.findViewByPosition(i);
                        if (view != null) {
                            int sTop = view.getTop();
                            int bottom = view.getBottom();
                            if (sTop < screenHeight / 2 && bottom > screenHeight / 2) {
                                if (position == 0) {
                                    View v = manager.findViewByPosition(position);
                                    if (v != null) {
                                        top = Math.abs(v.getTop());
                                        bannerHeight = v.getHeight();
                                    } else {
                                        top = 0;
                                        bannerHeight = 0;
                                    }
                                }
                                boolean visible = bannerHeight > 0;
                                if (position > 0 || top > bannerHeight) {
                                    top = bannerHeight;
                                }

                                // 关键修改1：添加精确条件判断
                                boolean shouldShowOp = position > 0 && !isOpVisible;
                                boolean shouldHideOp = position <= 0 && isOpVisible;

                                if (shouldShowOp) {
                                    showOperationBar(manager.findViewByPosition(position));
                                } else if (shouldHideOp) {
                                    hideOperationBar();
                                }
                                refreshViewStatus(position);
                                setTabPosition(i, dy);
                            }
                        }
                    }
                }
            }
        });
    }

    @Override
    public void loadData() {
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.PRODUCT,
                String.valueOf(viewModel.hashCode()));
        viewModel.setSourcePage(pageParams.sourcePage);
        viewModel.parseTransferData(pageParams.product, pageParams.traceId);
        refreshPageByType(null);
        if (pageParams.preloadId > 0) {
            PreloadManager.subscribeMerge(pageParams.preloadId, new PreloadObserver<Map<String, Object>>() {

                @Override
                public void onResponse(Map<String, Object> data) {
                    if (data != null && data.get(PreloadProductDetailTask
                            .KEY_PDP_DETAIL) instanceof ProductDetailBean) {
                        viewModel.handlePreloadData(data, pageParams.product, pageParams.traceId, pageParams.cartSource,
                                giftCardManager.getRefreshBean(), pageParams.disableOtherPage);
                    } else {
                        reload(true);
                    }
                }

                @Override
                public void onError(Throwable e) {
                    reload(true);
                }
            });
        } else {
            reload(true);
        }
    }

    private void showInviteFriendsTip() {
        if (viewModel.combineData == null || TextUtils.isEmpty(viewModel.combineData.inviteFriendsGetAmount)
                || (pageParams.product != null && pageParams.product.isGiftCard())) {
            return;
        }

        binding.tvShareInviteTip.setText("$" + viewModel.combineData.inviteFriendsGetAmount);

        InviteFriendsViewAnimation viewAnimation = new InviteFriendsViewAnimation();
        viewAnimation.showAndFadeOut(binding.tvShareInviteTip);
    }

    @Override
    public void attachModel() {
        initActOperationManager();
        initGiftCardManager();
        initAffiliateManager();
        initCouponManager();

        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                ViewTools.setViewVisible(findViewById(R.id.layout_empty), false);
                adapter.setNewData(list);
                adapter.notifyPageDataSetChanged(binding.rvList);
                execTitleAnchor();
            }
        });

        viewModel.adapterAppendData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> adapterDataTypes) {
                adapter.addData(adapterDataTypes);
                adapter.loadMoreComplete();
            }
        });

        viewModel.updateTabData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                updateWaterfallData();
            }
        });

        viewModel.loadMoreFinished.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                adapter.loadMoreEnd(true);
            }
        });

        viewModel.productDetailData.observe(this, new Observer<ProductDetailBean>() {
            @Override
            public void onChanged(ProductDetailBean productDetailBean) {
                ProductBean tmpProduct = productDetailBean.product;
                if (tmpProduct != null) {
                    pageParams.product = tmpProduct;
                    pageParams.productId = tmpProduct.id;
                }
                setCollect();
                trackProductImp(pageParams.product);
                refreshPageByType(productDetailBean);
                showInviteFriendsTip();
                setProductOpConfig(pageParams.product);
                if (pageParams.product instanceof ProductDetailBean.ProductFeatureBean) {
                    ProductDetailBean.ProductFeatureBean productFeatureBean = (ProductDetailBean.ProductFeatureBean) pageParams.product;
                    String descHtml = productFeatureBean.description_html;
                    if (descHtml != null) {
                        PdpWebViewPool.getInstance()
                                .preload(ProductDetailActivity.this, descHtml);
                    }
                    if (productFeatureBean.hasGroup()) {
                        String attachIds = "0";
                        for (GroupProduct groupProduct : productFeatureBean.group.groupProductList) {
                            if (pageParams.productId == groupProduct.product_id) {
                                attachIds = groupProduct.property_value_ids;
                                break;
                            }
                        }
                        List<AdapterProductGroupData> groupData = ProductGroupHelper.convert(productFeatureBean.group.propertyList, productFeatureBean.group.groupProductList, attachIds);
                        productOpManager.setGroupFilter(ProductGroupHelper.groupFilterName(groupData));
                        for (GroupProduct groupProduct : productFeatureBean.group.groupProductList) {
                            productMap.put(groupProduct.property_value_ids, groupProduct);
                        }
                    }
                }
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                viewModel.refreshProductData(false, null, pageParams.productId, pageParams.traceId, pageParams.cartSource, pageParams.disableOtherPage);
            }
        });

        viewModel.shareData.observe(this, new Observer<ShareBean>() {
            @Override
            public void onChanged(ShareBean productShareBean) {
                if (productShareBean != null) {
                    showShareDialog(productShareBean);
                }
            }
        });

        viewModel.policyData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String description) {
                if (!TextUtils.isEmpty(description) && pageParams.product instanceof ProductDetailBean.ProductFeatureBean) {
                    new ProductDetailPolicyDialog(activity, ((ProductDetailBean.ProductFeatureBean) pageParams.product).policy_title, description).show();
                }
            }
        });

        viewModel.alcoholData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                adapter.notifyAlcoholDataChanged(s);
            }
        });

        viewModel.vendorIntroductionData.observe(this, new Observer<VendorIntroductionBean>() {
            @Override
            public void onChanged(VendorIntroductionBean vendorIntroductionBean) {
                MkplHelper.showVendorIntroduction(activity, vendorIntroductionBean);
            }
        });


        viewModel.getReviewListLiveData().observe(
                this,
                this::notifyPdpReviewDataChanged
        );

        SharedViewModel.get().collectsData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                adapter.notifyCollectsChange();
                setCollect();//内层收藏通知外层
            }
        });

        postCollectsObserver = new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                if (adapter != null) {
                    adapter.toggleCollect(map);
                }
            }
        };
        SharedViewModel.get().postCollectsData.observeForever(postCollectsObserver);
        SharedViewModel.get().followChangeData.observe(this, new Observer<ArrayMap<String, Integer>>() {
            @Override
            public void onChanged(ArrayMap<String, Integer> data) {
                adapter.trendingPostDataUpdated(data);
            }
        });

        SharedOrderViewModel.get().cartNumData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (productOpManager.getNum() < 0) {
                    return;//当前底部按钮为不可售卖状态
                }
                if (!EmptyUtils.isEmpty(pageParams.product) && pageParams.product.isNormalProduct()) {
                    int num = productOpManager.getProductNum(pageParams.productId);
                    productOpManager.setNum(num);
                    binding.layoutOp.setOpStyle(num, productOpManager.getMin(), productOpManager.getMax());
                    adapter.notifyCartButtonNum(num);
                }
            }
        });

        viewModel.errorStatusData.observe(this, new Observer<Boolean>() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onChanged(Boolean bool) {
                if (bool) {
                    adapter.setNewData(null);
                    ViewTools.setViewVisible(findViewById(R.id.layout_empty), true);
                }
            }
        });

        viewModel.itemUpdateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                AdapterDataType item = adapter.getItem(integer);
                if (item instanceof PdpBnplData) {
                    adapter.notifyItemChanged(integer, item);
                }
            }
        });

        SharedViewModel.get().updateAffiliate.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                affiliateManager.getAffiliateNewLists();
            }
        });

        viewModel.buttonTextData.observe(this, new Observer<PdpContactButtonTextBean>() {
            @Override
            public void onChanged(PdpContactButtonTextBean pdpContactButtonTextBean) {
                adapter.notifyButtonText(pdpContactButtonTextBean.text);
            }
        });

        viewModel.aiData.observe(this, new Observer<List<String>>() {
            @Override
            public void onChanged(List<String> strings) {
                setAiData(strings);
            }
        });

        viewModel.adsProductData.observe(this, new Observer<ProductBean>() {
            @Override
            public void onChanged(ProductBean productBean) {
                initAppBarListener(true);
                setAdsProduct(productBean);
            }
        });

        viewModel.adsProductEmptyData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                removeAppBar();
            }
        });

        viewModel.adsTopBannerData.observe(this, new Observer<PdpSectionBean.BannerInfoBean>() {
            @Override
            public void onChanged(PdpSectionBean.BannerInfoBean bannerInfoBean) {
                initAppBarListener(false);
                setTopBannerData(bannerInfoBean);
            }
        });

        viewModel.refreshProductData.observe(this, new Observer<ProductDetailBean>() {
            @Override
            public void onChanged(ProductDetailBean productDetailBean) {
                if (pageParams != null && productDetailBean.product != null) {
                    pageParams.product = productDetailBean.product;
                    affiliateManager.setAffiliateConfig(pageParams);
                }
            }
        });
    }

    private void setTopBannerData(PdpSectionBean.BannerInfoBean bannerInfoBean) {
        List<CategoryCarouselBean> carouselList = bannerInfoBean.carousel;
        if (!EmptyUtils.isEmpty(carouselList)) {
            CategoryCarouselBean carouselBean = carouselList.get(0);
            ImageLoader.load(activity, binding.ivTopBanner, WebpManager.convert(SPEC_BANNER_LINE, carouselBean.img_url));
            topBannerColor = carouselBean.color;
            binding.ivTopBanner.setBackgroundColor(ViewTools.parseColor(topBannerColor, Color.WHITE));
            binding.vStatus.setBackgroundColor(ViewTools.parseColor(topBannerColor, Color.WHITE));
            binding.layoutCurrent.setBackgroundColor(ViewTools.parseColor(topBannerColor, Color.WHITE));
            binding.ivTopBanner.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    clickTrack("pdpSection_banner", 0);
                    startActivity(WebViewActivity.getIntent(activity, carouselBean.link_url));
                }
            });
        }

    }

    private void setAdsProduct(ProductBean productBean) {
        binding.vStatus.setBackgroundColor(ViewTools.parseColor("#EEF2FB", Color.WHITE));
        binding.layoutCurrent.setBackgroundColor(ViewTools.parseColor("#EEF2FB", Color.WHITE));
        ImageLoader.load(activity, binding.ivAdIcon, WebpManager.get().getConvertUrl(SPEC_64, productBean.img));
        binding.tvAdTitle.setText(productBean.name);
        Spanny spanny = new Spanny();
        if (productBean.price > 0) {
            spanny.append(OrderHelper.formatUSMoney(productBean.price), new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.color_pricing_txt)), new TextAppearanceSpan(activity, R.style.style_body_sm_medium));
        }
        if (productBean.showVolumePrice()) {
            spanny.append(getString(R.string.s_volume_threshold_simple,
                            productBean.volume_threshold),
                    new ForegroundColorSpan(ContextCompat.getColor(activity,
                            R.color.color_pricing_txt)), new TextAppearanceSpan(activity,
                            R.style.style_body_sm_medium));
        } else {
            if (productBean.base_price > 0) {
                spanny.append(" ");
                spanny.append(OrderHelper.formatUSMoney(productBean.base_price), new StrikethroughSpan(), new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.color_surface_100_fg_minor)), new TextAppearanceSpan(activity, R.style.style_body_3xs));
            }
            ProductBean.LabelListBean label = CollectionUtils.firstOrNull(productBean.label_list);
            ViewTools.setViewVisible(binding.tvProductMark, !EmptyUtils.isEmpty(label));
            if (label != null) {
                try {
                    binding.tvProductMark.setText(label.label_name);
                    binding.tvProductMark.setTextColor(ViewTools.parseColor(label.label_font_color, Color.WHITE));
                    ShapeHelper.setBackgroundDrawable(binding.tvProductMark, Color.parseColor(label.label_color), CommonTools.dp2px(2), Color.WHITE, 0);
                } catch (Exception ignored) {
                    // do nothing
                }
            }
        }
        binding.tvAdSubtitle.setText(spanny);

        binding.layoutTopSku.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                onProductClick(productBean);
            }
        });

    }

    public void onProductClick(ProductBean product) {
        clickTrack("pdpSection_sku", 0);
        if (isSpecialProduct(product)) {
            startActivity(WebViewActivity.getIntent(activity, product.view_link));
        } else {
            startActivity(ProductIntentCreator.getIntent(activity, product));
        }
    }

    protected static boolean isSpecialProduct(ProductBean product) {
        return product != null &&
                (Constants.ProductType.BUNDLE.equals(product.category)
                        || Constants.ProductType.VALUE_HOT_DISH.equals(product.is_hotdish)
                        || Constants.ProductStatus.PRE_SELL.equalsIgnoreCase(product.sold_status)
                );
    }

    private void refreshPageByType(ProductDetailBean detailBean) {
        ViewTools.setViewVisible(binding.vCart, pageParams.product != null);
        ViewTools.setViewVisible(binding.ivCollectBtn, pageParams.product != null);
        ViewTools.setViewVisible(binding.ivShareInviteBtn, pageParams.product != null);
        ViewTools.setViewVisible(binding.ivAffiliateBtn, false);
        if (pageParams.product == null) return;
        ProductBean product = pageParams.product;

        boolean isGiftCard = product.isGiftCard();
        if (isGiftCard) {
            screenshotManager.stopListening();//礼品卡页面禁止截图分享
        }

        binding.tvTitle.setText(product.name);
        binding.tvDescription.setText(product.sub_name);
        refreshTitleView(false, product);
        ViewTools.setViewVisible(binding.tvDescription, !TextUtils.isEmpty(product.sub_name));
        ViewTools.setViewVisible(binding.tvTitle, !isGiftCard);
        ViewTools.setViewVisible(binding.vCart, !isGiftCard);
        ViewTools.setViewVisible(binding.ivCollectBtn, !isGiftCard);
        ViewTools.setViewVisible(binding.ivShareInviteBtn, !isGiftCard);
        ViewTools.setViewVisible(binding.tvRedeem, isGiftCard);

        // affiliate
        affiliateManager.setAffiliateConfig(pageParams);

        // coupon
        couponManager.setCouponConfig(pageParams);

        // gift card
        giftCardManager.setGiftCardConfig(pageParams);


        if (!isGiftCard) {
            ProgressBarManager.get().registerProgressChangedListener(progressChangedListener);
        } else {
            binding.rvList.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        }

        giftCardManager.refreshPage(detailBean);
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (!isAiShow()) {
            logPageView();
            adapter.onPageResume(binding.rvList);
            trackProductImp(pageParams.product);
        }
        CollectManager.get().syncProductCollect();
        ProductSyncHelper.onPageResume(adapter);
        if (productOpManager.getNum() >= 0 && pageParams.product != null) {
            ProductSyncHelper.onPageResume(binding.layoutOp, pageParams.productId,
                    pageParams.product.product_key, productOpManager.getMin(), productOpManager.getMax());
        }
        if (pageParams.product != null && !pageParams.product.isGiftCard()) {
            ProgressBarManager.get().registerProgressChangedListener(progressChangedListener);
            TimerBannerManager.get().registerAndLog(timerChangedListener, findViewById(R.id.layout_timer_banner));
        }
//        refreshReviewListIfNeeded();
        couponManager.refreshCouponListIfNeeded();
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT,
                WeeeEvent.PageView.PRODUCT, String.valueOf(hashCode()));
        screenshotManager.startListening();
    }

    private void logPageView() {
        AppAnalytics.logPageView(WeeeEvent.PageView.PRODUCT, this, null, AppAnalytics.putPvPageTarget(null, String.valueOf(pageParams.productId)));
    }

    @Override
    protected void onPause() {
        super.onPause();
        adapter.onPagePause(binding.rvList);
        ProgressBarManager.get().unregisterProgressChangedListener(progressChangedListener);
        TimerBannerManager.get().unregisterTimerChangedListener(timerChangedListener);
        isPdpTracked = false;
        screenshotManager.stopListening();
    }

    ProgressBarManager.ProgressChangedListener progressChangedListener = new ProgressBarManager.ProgressChangedListener() {

        @Override
        public void onProgressChange(int productId, @Nullable String tagType, @Nullable UpdateResultBean tagResultBean) {
            boolean isAtc = !EmptyUtils.isEmpty(tagResultBean.sellerTagInfo) || !EmptyUtils.isEmpty(tagResultBean.firstAddItemTagInfo);
            boolean isAddToCart = productOpManager.isAddToCart(productId);
            if (isAtc && !EmptyUtils.isEmpty(tagResultBean) && "seller_pdp_act".equalsIgnoreCase(tagType) && isAddToCart) {
                addToCartForAtc(tagType, tagResultBean);
            } else {
                ProgressBarManager.get().setProgressBar(WeeeEvent.PageView.PRODUCT, findViewById(R.id.layout_progress), tagResultBean);
            }
        }
    };

    TimerChangedListener timerChangedListener = new TimerChangedListener() {
        @Override
        public void onRegister() {
            TimerBannerManager.get().setTimerPage(findViewById(R.id.layout_timer_banner), ExtendEvent.EVENT_PAGE_PRODUCT_DETAILS);
        }

        @Override
        public void onChanged(boolean display, int hour, int min, int sec) {
            TimerBannerManager.get().setTimerInfo(findViewById(R.id.layout_timer_banner), display, hour, min, sec);

        }
    };

    private void setPageClickListener() {
        OnSafeClickListener listener = new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                switch (v.getId()) {
                    case R.id.iv_collect_btn:
                    case R.id.iv_collect: {
                        clickCollect();
                        break;
                    }
                    case R.id.iv_share_invite_btn:
                    case R.id.iv_share: {
                        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                                String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm("product_detail")
                                .setTargetNm(String.valueOf(pageParams.productId))
                                .setTargetType(EagleTrackEvent.TargetType.PRODUCT)
                                .addCtx(ctx)
                                .setClickType(EagleTrackEvent.ClickType.SHARE)
                                .build().getParams());
                        toShare("right-icon-share");
                        break;
                    }
                    case R.id.iv_back:
                        finish();
                        break;
                    case R.id.btn_to_shopping:
                        SharedViewModel.get().toHome();
                        finish();
                        break;
                    case R.id.layout_top:
                        if (!EmptyUtils.isEmpty(adapter)) {
                            binding.rvList.smoothScrollToPosition(0);
                            setPageIndicatorSelected(0);
                            binding.ablEvent.setExpanded(true, true);
                        }
                        break;
                    case R.id.tv_redeem: {
                        WebRouter.toPage(ProductDetailActivity.this,
                                AppConfig.HOST_WEB + Constants.Url.GIFT_CARD_REDEEM);
                        break;
                    }
                    case R.id.tv_title:
                    case R.id.tv_description: {
                        gotoTabIndicator(TitleAnchorBean.DETAIL);
                        break;
                    }
                    case R.id.iv_question:
                        aiClickTrack();
                        showAi(null);
                        break;
                    default:
                        break;
                }
            }
        };
        setOnClickListener(listener, R.id.iv_collect, R.id.iv_collect_btn, R.id.iv_share, R.id.iv_share_invite_btn,
                R.id.iv_back, R.id.btn_to_shopping, R.id.layout_top,
                R.id.tv_redeem, R.id.tv_title, R.id.tv_description, R.id.iv_question);
    }

    private boolean toWebPage(String url) {
        if (pageParams.disableOtherPage) {
            return false;
        }
        startActivity(WebViewActivity.getIntent(activity, url));
        return true;
    }

    private void notifyShowCollectTips() {
        boolean isCollected = CollectManager.get().isProductCollect(pageParams.productId);
        if (isCollected) {
            //version 12.9 售罄 不允许取消收藏
            return;
        }
        boolean isCollect = toggleCollect(pageParams.productId);
        if (isCollect) {
            //已收藏
            showCollectTips();
            adapter.notifyCartButtonData(ProductOpHelper.SHOW_COLLECTED_TIPS);
        }
    }

    public void showCollectTips() {
        binding.layoutOp.showAutoTips(getString(R.string.s_added_to_my_list), pageParams.disableOtherPage ? null : getString(R.string.s_view_up_case), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去收藏
                boolean toNext = toWebPage(AppConfig.HOST_WEB + Constants.Url.COLLECT);
                if (toNext) {
                    binding.layoutOp.dismissAutoTips();
                }
            }
        });
    }

    private void setPdpBannerListener() {
        adapter.setBannerTracker();
        adapter.setOnPdpBannerContentListener(new PdpBannerProvider.OnPdpBannerClickListener() {
            @Override
            public void clickContent() {
                showCollectTips();
            }

            @Override
            public void isCollectListener(boolean isCollect) {
                ProductViewHelper.setProductCollect(binding.ivCollect, isCollect);
            }

            @Override
            public void toShareListener() {
                toShare("right-icon-share");
            }

            @Override
            public void refreshListener() {
                viewModel.refreshProductData(false, null, pageParams.productId, pageParams.traceId, pageParams.cartSource, pageParams.disableOtherPage);
            }

            @Override
            public void updateCartToastListener(String status) {
                if ("often_paired".equalsIgnoreCase(status)) {
                    adapter.notifyOftenPairedWith();
                    return;
                }
                ProductOpHelper.updateCartButton(binding.layoutOp, activity, status, pageParams.product,
                        productOpManager.getProductNum(pageParams.productId), pageParams.disableOtherPage);
            }

            @Override
            public void updateLastNumListener(int num) {
                if (productOpManager != null) {
                    productOpManager.setLastQuantity(num);
                }
            }
        });
    }

    private void setPdpInfoListener() {
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                AdapterDataType item = (AdapterDataType) adapter.getItem(position);
                if (item instanceof PdpFreshnessData) {
                    viewModel.getProductPolicy(((PdpFreshnessData) item).t.policy_pop_config_key);
                    clickTrack(PdpFreshnessData.MOD_NM, ((PdpFreshnessData) item).modPos);
                } else if (item instanceof PdpGlobalData) {
                    viewModel.getVendorIntroduction(String.valueOf(((PdpGlobalData) item).t.vender_info_view.vender_id), String.valueOf(pageParams.productId));
                    clickTrack(PdpFreshnessData.MOD_NM, ((PdpGlobalData) item).modPos);
                } else if (item instanceof PdpProductGroupData) {
                    PdpProductGroupData bean = (PdpProductGroupData) item;
                    if (!EmptyUtils.isEmpty(bean.t.group.propertyList) && !EmptyUtils.isEmpty(bean.t.group.propertyList.get(0).property_value_list)) {
                        clickGroupTrack(bean.t.group.propertyList.get(0).property_value_list.get(0).property_value_id);
                    }
                    showGrouping(bean.t, bean.tag);
                    clickTrack(PdpProductGroupData.modNm, ((PdpProductGroupData) item).modPos);
                } else if (item instanceof PdpItemData) {
                    PdpItemBean itemBean = ((PdpItemData) item).t;
                    clickTrack(itemBean.modNm, itemBean.modPos);
                    if (!EmptyUtils.isEmpty(itemBean.policy_pop_config_key)) {
                        viewModel.getProductPolicy(itemBean.policy_pop_config_key);
                    } else {
                        switch (itemBean.status) {
                            case PdpItemBean.PANTRY:
                                PantryHelper.toPantryTerms();
                                break;
                            case PdpItemBean.COLD_PACK:
                                PromoHelper.showPdpColdPackageTips();
                                break;
                            case PdpItemBean.FREE_GIFT:
                                showFreeGiftDialog(itemBean.giftInfo);
                                break;

                        }
                    }
                } else if (item instanceof PdpBarInfoData) {
                    if (pageParams != null && pageParams.product != null && pageParams.product instanceof ProductDetailBean.ProductFeatureBean) {
                        ProductDetailBean.ProductFeatureBean detail = (ProductDetailBean.ProductFeatureBean) pageParams.product;
                        showFreeGiftDialog(detail.giftInfo);
                    }

                }
            }
        });
    }

    private void setPdpTagListener() {
        adapter.setOnTagListener(new PdpTagProvider.OnTagClick() {
            @Override
            public void onFreshnessClick(String policy_pop_config_key) {
                viewModel.getProductPolicy(policy_pop_config_key);
            }
        });
    }

    private void setPdpSummaryListener() {
        adapter.setOnProductSummaryListener(new PdpSummaryProvider.OnSummaryClickListener() {
            @Override
            public void onSummaryInfoTipClick() {
                // goto review indicator
                gotoTabIndicator(TitleAnchorBean.REVIEW);
            }
        });
    }

    private boolean isAiScroll;
    private boolean isFirstAiScroll = true;

    private void gotoTabIndicator(String review) {
        int i = -1;
        for (TitleAnchorBean titleAnchorBean : titleIndicatorList) {
            i++;
            if (titleAnchorBean.flag.equals(review)) {
                if (review.equalsIgnoreCase(TitleAnchorBean.AI) && isFirstAiScroll) {
                    isAiScroll = true;
                    isFirstAiScroll = false;
                }
                setPageIndicatorSelected(i);
                setScrollOffset(titleAnchorBean.top, true, false);
                break;
            }
        }
    }

    private void setPdpQuickLinksListener() {
        adapter.setOnQuickLinksListener(new PdpQuickLinksProvider.OnTabClickListener() {
            @Override
            public void onTabClick(String flag) {
                gotoTabIndicator(flag);
            }
        });
    }

    private void setPdpProductGroupListener() {
        adapter.setOnProductGroupListener(new PdpNewProductGroupProvider.OnProductGroupListener() {
            @Override
            public void onProductGroupClick(String valueId, int position) {
                if (pageParams == null || pageParams.product == null) {
                    return;
                }
                if (pageParams.product instanceof ProductDetailBean.ProductFeatureBean) {
                    ProductDetailBean.ProductFeatureBean productFeatureBean = (ProductDetailBean.ProductFeatureBean) pageParams.product;
                    if (!productFeatureBean.hasGroup()) {
                        return;
                    }
                    for (GroupProduct groupProduct : productFeatureBean.group.groupProductList) {
                        if (pageParams.productId == groupProduct.product_id) {
                            String attachIds = groupProduct.property_value_ids;
                            String newAttachIds = ProductGroupHelper.selectGroup(valueId, position, attachIds, productFeatureBean.group.groupProductList);
                            List<AdapterProductGroupData> groupData = ProductGroupHelper.convert(productFeatureBean.group.propertyList, productFeatureBean.group.groupProductList, newAttachIds);
                            adapter.notifyProductGroup(groupData);
                            if (!EmptyUtils.isEmpty(productMap.get(newAttachIds))) {
                                viewModel.preloadDrawable = null;
                                pageParams.productId = productMap.get(newAttachIds).product_id;
                                viewModel.changeGroupFilter = true;
                                productOpManager.setChangeGroupFilter();
                                adapter.resetBanner();
                                reload(false, false);
                                binding.ablEvent.setExpanded(false, false);
                            }
                            break;
                        }
                    }
                }
            }
        });
    }

    private void setProductOpConfig(ProductBean product) {
        if (product == null) return;
        productOpManager.setProductKey(product.product_key);
        productOpManager.setProductOpConfig(product, tips -> adapter.notifyCartButtonData(tips));
    }

    protected boolean toggleCollect(int productId) {
        return fillCollect(productId, true);
    }

    protected boolean fillCollect(int productId, boolean isToggle) {
        if (isToggle) {
            CollectManager.get().toggleProductCollect(productId);
        }
        boolean isCollect = CollectManager.get().isProductCollect(productId);
        setCollect();
        if (OrderManager.SOLD_OUT.equalsIgnoreCase(pageParams.product.sold_status)) {
            if (binding.ivCollect.getVisibility() == View.VISIBLE) {
                binding.ivCollect.setVisibility(View.GONE);
            }
            if (binding.ivCollectBtn.getVisibility() == View.VISIBLE) {
                binding.ivCollectBtn.setVisibility(View.GONE);
            }
            adapter.notifyCartButtonData(ProductOpHelper.COLLECT_UPDATE);
            binding.layoutOp.setOpStyle(isCollect ? BottomOpLayout.TYPE_REMINDED_YET : BottomOpLayout.TYPE_REMINDED);
        } else {
            if (binding.ivCollect.getVisibility() == View.VISIBLE) {
                binding.ivCollect.setVisibility(View.VISIBLE);
            }
            if (binding.ivCollectBtn.getVisibility() == View.VISIBLE) {
                binding.ivCollectBtn.setVisibility(View.VISIBLE);
            }
        }
        return isCollect;
    }

    private void toLoginPage() {
        startActivity(AccountIntentCreator.getIntent(activity));
    }

    private void reload(boolean displayReferral) {
        if (displayReferral) {
            binding.rvList.scrollToPosition(0);
        }
        viewModel.refreshProductData(displayReferral, pageParams.product, pageParams.productId, pageParams.traceId,
                pageParams.cartSource, giftCardManager.getRefreshBean());
    }


    private void reload(boolean displayReferral, boolean isSilent) {
        if (displayReferral) {
            binding.rvList.scrollToPosition(0);
        }
        viewModel.refreshProductData(displayReferral, pageParams.product, pageParams.productId, pageParams.traceId,
                pageParams.cartSource, giftCardManager.getRefreshBean(), isSilent);
    }


    private void removeAppBar() {
        binding.vStatus.setBackgroundColor(ViewTools.parseColor("#ffffff", Color.WHITE));
        binding.layoutCurrent.setBackgroundColor(ViewTools.parseColor("#ffffff", Color.WHITE));
        binding.ablEvent.removeOnOffsetChangedListener(offSetChangedListener);
        ViewTools.setViewVisible(binding.layoutTopSku, false);
        ViewTools.setViewVisible(binding.ivTopBanner, false);
        ViewTools.setViewVisible(binding.ablEvent, false);
        binding.ablEvent.setExpanded(false, true);
        binding.coordinator.requestLayout();
    }

    private void showGrouping(ProductDetailBean.ProductFeatureBean featureBean, String tag) {
        ProductGroupingFragment productGroupingFragment = ProductGroupingFragment
                .newInstance(pageParams.productId, featureBean, pageParams.cartSource)
                .setOnGroupedCallback(new ProductGroupingFragment.OnGroupedCallback() {
                    @Override
                    public void onGrouped(int newProductId) {
                        viewModel.preloadDrawable = null;
                        pageParams.productId = newProductId;
                        reload(false);
                    }

                    @Override
                    public void onDismissed(GroupProduct groupProduct, int num) {
                        if (num >= 0) {
                            ProductSyncHelper.onPageResume(binding.layoutOp, groupProduct.product_id, null, groupProduct.min_order_quantity, groupProduct.max_order_quantity);
                        }
                    }

                    @Override
                    public void onProductStatusChanged() {
                        boolean isCollect = CollectManager.get().isProductCollect(pageParams.productId);
                        binding.layoutOp.setOpStyle(isCollect ? BottomOpLayout.TYPE_REMINDED_YET : BottomOpLayout.TYPE_REMINDED);
                    }
                });
        productGroupingFragment.show(getSupportFragmentManager(), tag);
        //tracking page sec imp
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
        if (featureBean != null && featureBean.group != null && featureBean.group.propertyList != null) {
            for (GroupProperty property : featureBean.group.propertyList) {
                Map<String, Object> element = EagleTrackManger.get().getElement("group_feature", 1, property.property_id,
                        featureBean.group.propertyList.indexOf(property));
                AppAnalytics.logPageSecImp(new EagleTrackModel.Builder()
                        .addElement(element)
                        .addContent(new TrackParams().put("default_nm", property.property_id_chosen).put("qty", property.property_value_list.size()).get())
                        .addCtx(ctx)
                        .build().getParams());
            }
        }
    }

    private void toShare(String eventParaLocation) {
        setShareTempterClicked();
        if (AccountManager.get().isLogin()) {
            viewModel.getProductShare(pageParams.productId);
        } else {
            toLoginPage();
        }
    }

    private void setShareTempterClicked() {
        int today = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        viewModel.getPreferences().encode("share_tempter_clicked_day", today);
    }

    private void showShareDialog(final ShareBean productShareBean) {
        new ShareDialog(activity)
                .setScreenName(ExtendEvent.EVENT_PAGE_PRODUCT_DETAILS)
                .setShareData(productShareBean)
                .show();
    }

    private int tabPosition;
    private String alsoTitle = "";
    private int waterFallPosition;
    private boolean hasAi;

    private void execTitleAnchor() {
        //review //layout_post
        //post  //layout_posts_video
        //recommend //layout_recommend
        //detail    //layout_detail
        titleIndicatorList = new ArrayList<>();
        int i = -1;
        for (AdapterDataType item : adapter.getData()) {
            i++;
            if (item instanceof PdpReviewData) {
                titleIndicatorList.add(new TitleAnchorBean(TitleAnchorBean.REVIEW, i, getString(R.string.reviews)));
            }
            if (item instanceof PdpPostData) {
                titleIndicatorList.add(new TitleAnchorBean(TitleAnchorBean.POST, i, getString(R.string.videos)));
            }
            if (item instanceof PdpProductDetailData) {
                titleIndicatorList.add(new TitleAnchorBean(TitleAnchorBean.DETAIL, i, getString(R.string.s_title_details)));
            }

            if (item instanceof PdpAiQuestionData) {
                hasAi = true;
                titleIndicatorList.add(new TitleAnchorBean(TitleAnchorBean.AI, i, getString(R.string.s_question)));
            }
        }
//        titleIndicatorList.add(new TitleAnchorBean(TitleAnchorBean.ALSO_LIKE, i, alsoTitle));
        fillIndicator();
    }

    private void updateWaterfallData() {
        int i = -1;
        int position = -1;
        String key = null;
        for (AdapterDataType item : adapter.getData()) {
            i++;
            if (item instanceof PdpProductTitleData) {
                PdpProductTitleData data = ((PdpProductTitleData) item);
                if (!EmptyUtils.isEmpty(data.status)) {
                    alsoTitle = data.t;
                    waterFallPosition = i;
                }
            }
            if (item instanceof CmsCategoryFeedData) {
                position = i;
                key = ((CmsCategoryFeedData) item).componentKey;
            }
        }
        for (TitleAnchorBean data : titleIndicatorList) {
            if (TitleAnchorBean.ALSO_LIKE.equalsIgnoreCase(data.flag)) {
                data.title = alsoTitle;
                if (position >= 0) {
                    data.top = position;
                }
                data.setKey(key);
            }
        }
        fillIndicator();
    }

    private void fillIndicator() {
        IndicatorHelper.fillProductDetailTitleNewIndicator(activity, binding.indicatorTitle, titleIndicatorList, new OnAdapterChildClickListener() {
            @Override
            public void onAdapterChildClick(View view, int position) {
                if (tabPosition == position) {
                    return;
                }
                TitleAnchorBean bean = titleIndicatorList.get(position);
                boolean isWaterfall = alsoTitle.equalsIgnoreCase(bean.title);
                setScrollOffset(bean.top, false, isWaterfall);
                String targetNm = null;
                if (getString(R.string.s_title_reviews).equalsIgnoreCase(bean.title)) {
                    targetNm = "Reviews";
                } else if (getString(R.string.s_title_posts).equalsIgnoreCase(bean.title)) {
                    targetNm = "Posts";
                } else if (getString(R.string.s_title_recommendations).equalsIgnoreCase(bean.title)) {
                    targetNm = "ProductRecommendations";
                } else if (getString(R.string.s_title_details).equalsIgnoreCase(bean.title)) {
                    targetNm = "Details";
                } else if (getString(R.string.s_question).equalsIgnoreCase(bean.title)) {
                    targetNm = "ai_bot";
                } else if (isWaterfall) {
                    targetNm = bean.key;
                }
                Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                        String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setTargetNm(targetNm)
                        .setTargetPos(position)
                        .setTargetType(EagleTrackEvent.TargetType.FILTER_BUTTON)
                        .addCtx(ctx)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .build().getParams());
                isFirstTabToChange = true;
                setPageIndicatorSelected(position);
                if (hasAi && pdpAiQuestionPosition != -1 && pdpAiQuestionPosition < bean.top) {
                    binding.layoutQuestion.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            setAiQuestionVisible(true);
                        }
                    }, 100);
                }
            }
        });
    }

    private void setScrollOffset(int offset, boolean isInitScroll, boolean isWaterfall) {
        int titleHeight = CommonTools.getStatusBarHeight(activity);
        if (isWaterfall) {
            titleHeight = titleHeight + CommonTools.dp2px(40);
            adapter.scrollToTop();
        }

        RecyclerView.LayoutManager manager = binding.rvList.getLayoutManager();
        int position = ((LinearLayoutManager) manager).findFirstVisibleItemPosition();
        if (hasAi) {
            if (pdpAiQuestionPosition < offset) {
                titleHeight = titleHeight + CommonTools.dp2px(60);
            } else if (pdpAiQuestionPosition == offset) {
                titleHeight = titleHeight + CommonTools.dp2px(20);
            }
        }
        if (manager instanceof LinearLayoutManager) {
            if (isInitScroll) {
                tabClickTime = System.currentTimeMillis();
                isTabInit = true;
                LinearTopSmoothScroller smoothScroller = new LinearTopSmoothScroller(activity, false, titleHeight);
                smoothScroller.setTargetPosition(offset);
                manager.startSmoothScroll(smoothScroller);
            } else {
                ((LinearLayoutManager) manager).scrollToPositionWithOffset(offset, titleHeight);
            }
            refreshViewStatus(position);
            isFirstTabToChange = true;
        }

    }

    private void setScrollOffset(int offset, boolean isInitScroll) {
        setScrollOffset(offset, isInitScroll, false);
    }

    private void clickCollect() {
        if (AccountManager.get().isLogin()) {
            ProductBean product = pageParams.product;
            boolean isCollect = toggleCollect(pageParams.productId);
            if (isCollect) {
                //已收藏
                showCollectTips();
                adapter.notifyCartButtonData(ProductOpHelper.SHOW_COLLECTED_TIPS);
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                    String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
            ctx.put("volume_price_support", product != null && product.volume_price_support);
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm("product_detail")
                    .setTargetNm(String.valueOf(pageParams.productId))
                    .setTargetType(EagleTrackEvent.TargetType.PRODUCT)
                    .setIsMkpl(product != null ? product.isSeller() : null)
                    .addCtx(ctx)
                    .setClickType(isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE)
                    .build().getParams());
        } else {
            toLoginPage();
        }
    }

    private boolean isTabClick;
    private long tabClickTime = 0;

    private void setPageIndicatorSelected(int position) {
        if (binding.indicatorTitle != null && !isDestroyed() && !isFinishing() && binding.indicatorTitle.getIndicatorHelper().getSelectedIndex() != position) {
            tabClickTime = System.currentTimeMillis();
            binding.indicatorTitle.postDelayed(new Runnable() {
                @Override
                public void run() {
                    isTabClick = true;
                    binding.indicatorTitle.getIndicatorHelper().handlePageSelected(position);
                    tabPosition = position;
                }
            }, 200);
        }
    }

    private void trackProductImp(ProductBean product) {
        if (!isPdpTracked && product instanceof ProductDetailBean.ProductFeatureBean) {
            isPdpTracked = true;
            Map<String, Object> element = EagleTrackManger.get().getElement("product_detail", 0, null, -1);
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                    String.valueOf(product.id), null, null, null, pageParams.traceId);
            Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_PDP).setTarget(product, -1).setElement(element).setContext(ctx).get();
            AppAnalytics.logProdImp(params);
        }
    }


    private void clickTrack(String modNum, int modPos) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(modNum)
                .setMod_pos(modPos)
                .setTargetNm(modNum)
                .setTargetType(EagleTrackEvent.TargetType.MESSAGE)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private void clickGroupTrack(String targetNm) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(pageParams.productId), null, null, null, pageParams.traceId);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm("group_feature")
                .setMod_pos(0)
                .setTargetNm(targetNm)
                .setTargetPos(0)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private void setEditTextOnFocusChangeListener() {
        setKeyboardObserver();
    }

    private void setPdpAiQuestionListener() {
        adapter.setOnAiQuestionListener(aiQuestionListener);
    }

    protected void setKeyboardObserver() {
        helper = new KeyboardChangeHelper(getContentView());
        helper.startObserve().disableResize(true).setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
//                getWrapperTitle().setVisible(R.id.layout_wrapper_title, true);
//                adapter.clearFocus();
            }

            @Override
            public void onKeyboardShow() {
                EditText editText = adapter.getGiftEditText();
                if (adapter == null || adapter.getGiftEditText() == null) {
                    return;
                }
                Rect r = new Rect();
                editText.getWindowVisibleDisplayFrame(r);
                int screenHeight = editText.getRootView().getHeight();
                int keypadHeight = screenHeight - r.bottom;

                int[] location = new int[2];
                editText.getLocationOnScreen(location);
                int editTextBottom = location[1] + editText.getHeight();

                // 计算 EditText 底部距离屏幕底部的距离
                int distanceToBottom = screenHeight - editTextBottom;
                // 如果软键盘弹出且遮挡到 EditText
                if (distanceToBottom < keypadHeight) {
                    int scrollDistance = Math.max(0, editTextBottom - (screenHeight - keypadHeight));
                    editText.post(new Runnable() {
                        @Override
                        public void run() {
                            binding.rvList.smoothScrollBy(0, scrollDistance);
                        }
                    });
                }

            }
        });
    }

    private void notifyPdpReviewDataChanged(@Nullable PostCategoryBean data) {
        PdpItemAdapter adapter = this.adapter;
        if (adapter == null || EmptyUtils.isEmpty(adapter.getData())) return;
        Pair<Integer, AdapterDataType> itemData = CollectionUtils.firstOrNullWithIndex(
                adapter.getData(),
                item -> item.getType() == PdpItemType.PDP_PRODUCT_REVIEW
        );
        if (itemData != null && itemData.second instanceof PdpReviewData) {
            ((PdpReviewData) itemData.second).t = data;
            adapter.notifyItemChanged(itemData.first, null);
        }
    }

    private void refreshViewStatus(int position) {
        if (bannerHeight <= 0) {
            return;
        }
        ProductBean product = pageParams.product;
        if (product == null) {
            return;
        }
        boolean visible = position > 0;
        boolean praiseVisible = !EmptyUtils.isEmpty(product) && !product.isSoldOut();
//        float divide = (float) top / bannerHeight;

        // set the title view style
        refreshTitleView(visible, product);

        ViewTools.setViewVisible(binding.ivShareInviteBtn, !visible && !product.isGiftCard());
        boolean isShowInvite = viewModel.combineData != null && !TextUtils.isEmpty(viewModel.combineData.inviteFriendsGetAmount);
        ViewTools.setViewVisible(binding.tvShareInviteTip, !visible && !product.isGiftCard() && isShowInvite);
        ViewTools.setViewVisible(binding.ivCollectBtn, !visible && praiseVisible && !product.isGiftCard());
        ViewTools.setViewVisible(binding.ivCollect, visible && praiseVisible && !product.isGiftCard());
        if (product instanceof ProductDetailBean.ProductFeatureBean) {
            ViewTools.setViewVisible(binding.ivAffiliateBtn, !visible &&
                    ((ProductDetailBean.ProductFeatureBean) product).affiliate_show && !product.isGiftCard());
        }
        ViewTools.setViewVisible(findViewById(R.id.v_shadow), visible);
        ViewTools.setViewVisible(binding.layoutTopTitle, visible);
        if (!visible) {
            isFirstTabToChange = false;
        }
        ViewTools.setViewVisible(binding.indicatorTitle, visible && !titleIndicatorList.isEmpty());
        ViewTools.setViewVisible(binding.ivShare, visible && !product.isGiftCard());
    }

    private void refreshTitleView(boolean btnVisible, ProductBean product) {
        boolean isSubNameEmpty = TextUtils.isEmpty(product.sub_name);

        int titleMaxLines;
        int titleGravity;

        if (btnVisible) {
            titleMaxLines = 1;
            if (isSubNameEmpty) {
                titleGravity = Gravity.START | Gravity.CENTER_VERTICAL; // sub_name 为空，垂直居中对齐
            } else {
                titleGravity = Gravity.START | Gravity.BOTTOM; // sub_name 不为空，底部对齐
            }
        } else {
            if (isSubNameEmpty) {
                titleMaxLines = 2; // sub_name 为空，tvTitle 显示两行
                titleGravity = Gravity.CENTER;
            } else {
                titleMaxLines = 1; // sub_name 不为空，tvTitle 显示一行
                titleGravity = Gravity.CENTER | Gravity.BOTTOM;
            }
        }

        int descGravity = btnVisible ? (Gravity.START | Gravity.TOP) : (Gravity.CENTER | Gravity.TOP);

        int titleTextStyle = btnVisible ? R.style.style_body_sm_medium : R.style.style_body_base_medium;
        int titleTextColor = btnVisible ? R.color.color_surface_200_fg_minor : R.color.color_root_energy_blue_dark_6;
        int descTextStyle = btnVisible ? R.style.style_body_3xs : R.style.style_body_sm;
        int descTextColor = btnVisible ? R.color.color_surface_600_fg_minor : R.color.color_surface_400_fg_minor;

        binding.tvTitle.setMaxLines(titleMaxLines);
        binding.tvTitle.updateBaseGravity(titleGravity);
        binding.tvDescription.updateBaseGravity(descGravity);

        ViewTools.applyTextStyle(binding.tvTitle, titleTextStyle);
        ViewTools.applyTextColor(binding.tvTitle, titleTextColor);
        ViewTools.applyTextStyle(binding.tvDescription, descTextStyle);
        ViewTools.applyTextColor(binding.tvDescription, descTextColor);
    }

    private boolean isTabInit = false;
    private boolean isFirstTabToChange;

    private void setTabPosition(int position, int dy) {
        if (!EmptyUtils.isEmpty(titleIndicatorList) && dy != 0) {
            int index = -1; // 初始化为无效索引
            isTabClick = System.currentTimeMillis() - tabClickTime < 1000;
            // 遍历查找匹配的位置
            for (int i = titleIndicatorList.size() - 1; i >= 0; i--) {
                TitleAnchorBean bean = titleIndicatorList.get(i);
                if (bean.top != 0 && position == bean.top && !isTabClick) {
                    index = i;
                    break; // 找到后立即退出循环
                }
            }

            // 根据是否找到有效索引处理指示器
            if (index != -1) {
                if (isTabInit) {
                    return; // 初始化时不更新指示器
                }
                binding.indicatorTitle.getIndicatorHelper().handlePageSelected(index);
                tabPosition = index;
                isFirstTabToChange = true;
            } else {
                if (!isFirstTabToChange && !isTabClick) {
                    binding.indicatorTitle.getIndicatorHelper().handlePageSelected(999);
                    tabPosition = 999;
                }
            }
        }
    }

    private void showFreeGiftDialog(ProductDetailBean.GiftInfo data) {
        String tag = ProductGiftFragment.class.getName();
        ProductGiftFragment productViewFragment =
                (ProductGiftFragment) ProductGiftFragment.newInstance(pageParams.productId, pageParams.traceId, data);
        productViewFragment.show(getSupportFragmentManager(), tag);
    }

    private void setAtTop(boolean visible) {
        binding.layoutTop.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void finish() {
        if (pageParams != null && pageParams.isSearchV2) {
            Intent resultIntent = new Intent();
            resultIntent.putExtra(SearchResultsFragmentV2.PARAM_PRODUCT, pageParams.product);
            setResult(RESULT_CANCELED, resultIntent);
        }
        super.finish();
    }

    @Override
    protected void onStop() {
        super.onStop();
        // reduce memory
        PostCoverVideoManager.clearVideo(String.valueOf(hashCode()));
    }

    @Override
    protected void onDestroy() {
        AdsManager.get().flushAllEvents();

        super.onDestroy();

        if (helper != null) {
            helper.endObserve();
        }
        PreloadManager.destroy(pageParams.preloadId);
        if (adapter != null) {
            adapter.destroyBanner();
        }
        PdpWebViewPool.getInstance().recycle(this);

        binding.tvShareInviteTip.clearAnimation();

        if (affiliateManager != null) {
            affiliateManager.onDestroy();
        }
        progressChangedListener = null;
        if (hideRunnable != null) {
            binding.layoutSnackBar.layoutRoot.removeCallbacks(hideRunnable);
        }
        SharedViewModel.get().postCollectsData.removeObserver(postCollectsObserver);
        binding.ablEvent.removeOnOffsetChangedListener(offSetChangedListener);
        handler.removeCallbacksAndMessages(null);
    }

    private void setCollect() {
        boolean isCollect = CollectManager.get().isProductCollect(pageParams.productId);
        ProductViewHelper.setProductCollect(binding.ivCollect, isCollect);
        ProductViewHelper.setProductCollect(binding.ivCollectBtn, isCollect);
    }


    // 显示操作栏（带动画）
    private void showOperationBar(View anchorView) {
        if (binding.layoutOp == null || anchorView == null) return;
        // 关键修改2：确保初始状态
        binding.layoutOp.setAlpha(0f);
        binding.layoutOp.setTranslationY(anchorView.getHeight());
        binding.layoutOp.setVisibility(View.VISIBLE);
        setProgressBannerPadding();
        binding.layoutOp.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(100)
                .setInterpolator(new DecelerateInterpolator())
                .withStartAction(() -> {
                    isOpVisible = true;
                })
                .start();
    }

    // 隐藏操作栏
    private void hideOperationBar() {
        if (binding.layoutOp == null) return;
        setProgressBannerPadding();
        binding.layoutOp.setVisibility(View.GONE);
        binding.layoutOp.setAlpha(0f);
        binding.layoutOp.animate()
                .alpha(0f)
                .translationY(binding.layoutOp.getHeight())
                .setDuration(100)
                .setInterpolator(new AccelerateInterpolator())
                .withEndAction(() -> {
                    isOpVisible = false;
                })
                .start();
    }

    private void setProgressBannerPadding() {
        findViewById(R.id.layout_timer_banner).setPadding(0, 0, 0, CommonTools.dp2px(binding.layoutOp.getVisibility() == View.VISIBLE ? 74 : 0));
        findViewById(R.id.layout_progress).setPadding(0, 0, 0, CommonTools.dp2px(binding.layoutOp.getVisibility() == View.VISIBLE ? 74 : 0));
    }

    private void addToCartForAtc(String tagType, UpdateResultBean tag) {
        if (isFirstCLickCart && !EmptyUtils.isEmpty(tag.firstAddItemTagInfo)) {
            isFirstCLickCart = false;
            if (firstAddItemDialog == null) {
                firstAddItemDialog = new FirstAddItemDialog(activity);
            }
            firstAddItemDialog.setData(pageParams.productId, "first_add_seller_item_new", tag.firstAddItemTagInfo).show();
        } else if (!EmptyUtils.isEmpty(tag.sellerTagInfo) && firstAddItemDialog != null && !firstAddItemDialog.isShowing()) {
            showAtcToast("seller_item_exist_new", tag.sellerTagInfo);
        }
    }

    private void showAtcToast(String tagType, UpdateResultBean.TagInfoBean tagInfo) {
        showSellerItemExist(tagType, tagInfo);
    }

    // 用于控制延迟隐藏的 Runnable
    private Runnable hideRunnable;
    // 是否正在显示中
    private boolean isSnackBarShowing = false;

    private void showSellerItemExist(String tagType, UpdateResultBean.TagInfoBean tagInfo) {
        String iconUrl = WebpManager.convert(ImageSpec.SPEC_PRODUCT, tagInfo.product_image_url);
        String title = String.format(ContextCompat.getString(activity, R.string.s_added_to), tagInfo.vendor_name);
        boolean goVendor = tagInfo.shipping_free_progress != null && tagInfo.shipping_free_progress < 100;
        String actionTitle = goVendor ? ContextCompat.getString(activity, R.string.s_mkpl_store) : ContextCompat.getString(activity, R.string.s_cart);
        if (!EmptyUtils.isEmpty(iconUrl)) {
            ImageLoader.load(activity, binding.layoutSnackBar.ivIcon, iconUrl);
            binding.layoutSnackBar.ivIcon.setVisibility(View.VISIBLE);
        } else {
            binding.layoutSnackBar.ivIcon.setImageDrawable(null);
            binding.layoutSnackBar.ivIcon.setVisibility(View.GONE);
        }

        binding.layoutSnackBar.tvTitle.setText(title);
        binding.layoutSnackBar.tvSubtitle.setText(ViewTools.fromHtml(tagInfo.tag));
        binding.layoutSnackBar.tvSubtitle.setVisibility(!EmptyUtils.isEmpty(ViewTools.fromHtml(tagInfo.tag)) ? View.VISIBLE : View.GONE);
        binding.layoutSnackBar.tvAction.setText(actionTitle);
        binding.layoutSnackBar.tvAction.setVisibility(View.GONE);
        binding.layoutSnackBar.ivAction.setVisibility(View.GONE);
        ViewTools.setViewVisible(binding.layoutSnackBar.tvAction, true);
        ViewTools.setViewVisible(binding.layoutSnackBar.ivAction, true);
        if (tagInfo.shipping_free_progress != null && tagInfo.shipping_free_progress == 100) {
            ViewTools.setViewVisible(binding.layoutSnackBar.ivAction, false);
            TextView tvAction = binding.layoutSnackBar.tvAction;
            //背景
            GradientDrawable drawable = new GradientDrawable();
            drawable.setShape(GradientDrawable.RECTANGLE);
            drawable.setCornerRadius(CommonTools.dp2px(20));
            drawable.setColor(ContextCompat.getColor(activity, R.color.color_primary_flow_teal));
            tvAction.setBackground(drawable);
            tvAction.setGravity(Gravity.CENTER_VERTICAL);
            tvAction.setPadding(CommonTools.dp2px(12), 0, CommonTools.dp2px(4), 0);
            tvAction.getLayoutParams().height = CommonTools.dp2px(28);
            //箭头
            Drawable end = BitmapUtils.tint(activity, R.mipmap.iccmpt_carat_right_20x20, Color.WHITE);
            end.setBounds(0, 0, CommonTools.dp2px(20), CommonTools.dp2px(20));
            tvAction.setCompoundDrawables(null, null, end, null);
            tvAction.setCompoundDrawablePadding(CommonTools.dp2px(4));
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) tvAction.getLayoutParams();
            layoutParams.rightMargin = CommonTools.dp2px(10);
            tvAction.setLayoutParams(layoutParams);
        }
        SellerPageParams pageParamsCart = new SellerPageParams();
        pageParamsCart.openCart = 1;
        String modNm = "mkpl_shipping_tip_popup";
        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(String.valueOf(pageParams.productId))
                .asMap();
        ctx.put("popup_nm", tagType);
        ViewTools.setViewOnSafeClickListener(binding.layoutSnackBar.layoutRoot, v -> {
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm(modNm)
                    .setTargetPos(0)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .setTargetNm(String.valueOf(tagInfo.vendor_id))
                    .setTargetType(goVendor ? "mkpl_seller" : "cart")
                    .addCtx(ctx)
                    .build().getParams());
            startActivity(SellerActivity.getIntentByUrl(activity, String.valueOf(tagInfo.vendor_id), goVendor ? null : pageParamsCart, null));
        });

        if (isSnackBarShowing) {
            binding.layoutSnackBar.layoutRoot.removeCallbacks(hideRunnable);
            binding.layoutSnackBar.layoutRoot.postDelayed(hideRunnable, 3000);
        } else {
            showCustomSnackBar();
        }
        //t2_popup
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("product_id", pageParams.productId);
        map.put("shipping_free_progress", tagInfo.shipping_free_progress);
        AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .addContent(new TrackParams()
                        .put("action", "view")
                        .put("id", tagInfo.vendor_id)
                        .put("name", tagType)
                        .put("other_parameter", map)
                        .get())
                .build()
                .getParams());
    }

    /**
     * 显示自定义 SnackBar 布局，3 秒后自动消失
     */
    private void showCustomSnackBar() {
        // 显示布局（带渐入动画）
        binding.layoutSnackBar.layoutRoot.setVisibility(View.VISIBLE);
        binding.layoutSnackBar.layoutRoot.setAlpha(0f);
        binding.layoutSnackBar.layoutRoot.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

        // 启动 3 秒后隐藏任务
        hideRunnable = () -> hideCustomSnackBar();
        binding.layoutSnackBar.layoutRoot.postDelayed(hideRunnable, 3000);
        isSnackBarShowing = true;
    }

    /**
     * 隐藏自定义 SnackBar（带渐出动画）
     */
    private void hideCustomSnackBar() {
        if (binding.layoutSnackBar.layoutRoot.getVisibility() == View.VISIBLE) {
            binding.layoutSnackBar.layoutRoot.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction(() -> {
                        binding.layoutSnackBar.layoutRoot.setVisibility(View.GONE);
                        isSnackBarShowing = false;
                    })
                    .start();
        }
    }

    @Override
    public void onApplyToasterOptions(@NonNull IToaster<? extends IToasterOptions> toaster, @NonNull IToasterOptions options) {
        if (toaster.getType() == Toaster.TYPE_SNACK_BAR) {
            options.setContentMarginBottom(CommonTools.dp2px(72));
        }
    }

    private int pdpAiQuestionPosition = -1;

    private int findPdpAiQuestionPosition() {
        pdpAiQuestionPosition = -1;
        for (int i = 0; i < adapter.getData().size(); i++) {
            if (adapter.getItem(i) instanceof PdpAiQuestionData) {
                pdpAiQuestionPosition = i;
                return pdpAiQuestionPosition;
            }
        }
        return -1;
    }

    private void setAiChange(LinearLayoutManager manager) {
        if (findPdpAiQuestionPosition() == -1) return;
        // 判断 pdpAiQuestionPosition 是否在当前可见范围内

        // 获取 indicator_title 的底部坐标
        View indicatorView = binding.indicatorTitle;
        if (indicatorView == null || indicatorView.getVisibility() != View.VISIBLE)
            return;

        // 获取 PdpAiQuestionData 的 item 的 view
        View questionItemView = manager.findViewByPosition(pdpAiQuestionPosition);
        if (questionItemView == null) return;
        int questionTop = questionItemView.getTop() + CommonTools.dp2px(90);
        int indicatorBottom = binding.indicatorTitle.getBottom();
        // 判断是否下滑到 indicator_title 下方
        boolean isBelowIndicator = questionTop <= indicatorBottom;
        if (isBelowIndicator) {
            if (binding.layoutQuestion.getVisibility() == View.GONE) {
                AppAnalytics.logPageSecImp(new EagleTrackModel.Builder()
                        .setMod_nm("ai_bot")
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .build().getParams());
            }
            setAiQuestionVisible(true);
        } else {
            setAiQuestionVisible(false);
        }
    }

    private void setAiQuestionVisible(boolean visible) {
        if (visible) {
            binding.layoutQuestion.setVisibility(View.VISIBLE);
            binding.rvQuestion.setVisibility(View.VISIBLE);
            binding.ivQuestion.setVisibility(View.VISIBLE);
        } else {
            binding.layoutQuestion.setVisibility(View.GONE);
            binding.rvQuestion.setVisibility(View.GONE);
            binding.ivQuestion.setVisibility(View.GONE);
        }
    }

    private void setAiData(List<String> strings) {
        if (!EmptyUtils.isEmpty(strings)) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false);
            pdpAiQuestionAdapter = new PdpAiQuestionAdapter();
            pdpAiQuestionAdapter.setMod_nm("ai_bot", pageParams != null ? pageParams.productId : 0, -1);
            pdpAiQuestionAdapter.setNewData(strings);
            pdpAiQuestionAdapter.setOnAiQuestionListener(aiQuestionListener);
            binding.rvQuestion.setLayoutManager(layoutManager);
            if (binding.rvQuestion.getItemDecorationCount() == 0) {
                binding.rvQuestion.addItemDecoration(new RecyclerView.ItemDecoration() {
                    @Override
                    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                        super.getItemOffsets(outRect, view, parent, state);
                        RecyclerView.Adapter<?> adapter = parent.getAdapter();
                        if (adapter == null) {
                            return;
                        }
                        int layoutPosition = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
                        outRect.left = layoutPosition == 0 ? CommonTools.dp2px(30) : CommonTools.dp2px(8);
                        int itemCount = adapter.getItemCount();
                        if (itemCount > 1) {
                            outRect.right = layoutPosition == itemCount - 1 ? CommonTools.dp2px(20) : 0;
                        }
                    }
                });
            }
            binding.rvQuestion.setAdapter(pdpAiQuestionAdapter);
        }
    }

    OnAiQuestionListener aiQuestionListener = new OnAiQuestionListener() {
        @Override
        public void clickListener(String question) {
            showAi(question);
        }
    };

    @Override
    public IContentFeedSharedViewModel getContentFeedSharedViewModel() {
        return viewModel;
    }

    private void setScreenShotListener() {
        screenshotManager = new ScreenshotManager(this);
        screenshotManager.setListener((bitmap, filePath) -> {
            if (screenshotShareDialog == null) {
                screenshotShareDialog = new ScreenshotShareDialog(activity);
            }
            if (screenshotShareDialog.isShowing()) {
                return;
            }
            ShareBean bean = ShareHelper.getShareBean();
            bean.share_type = ShareBean.SHARE_TYPE_IMAGE;
            bean.share_data = new ShareBean.ShareDataBean();
            bean.share_data.image_data = BitmapOptimizer.bitmap2StringByBase64(bitmap, Base64.DEFAULT);
            bean.view_link = AppConfig.HOST_WEB + "/product/view/" + pageParams.productId;
            screenshotShareDialog.setScreenShotShareData(bean, bitmap).show();
            AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                    .addContent(new TrackParams()
                            .put("action", "view")
                            .put("name", "screenshot_popup")
                            .get())
                    .build()
                    .getParams());
        });
    }

    private String getProductId() {
        return String.valueOf(pageParams.productId);
    }

    @Nullable
    private PdpAiFragment getPdpAiFragment() {
        String fragmentTag = PdpAiFragment.getFragmentTag(getProductId());
        return (PdpAiFragment) getSupportFragmentManager().findFragmentByTag(fragmentTag);
    }

    private void showAi(String question) {
        changeAiBackground(true);
        String fragmentTag = PdpAiFragment.getFragmentTag(getProductId());
        PdpAiFragment fragment = getPdpAiFragment();
        if (fragment == null) {
            List<String> questions = new ArrayList<>();
            if (question == null) {
                if (viewModel.aiData != null && CollectionUtils.isNotEmpty(viewModel.aiData.getValue())) {
                    questions.addAll(viewModel.aiData.getValue());
                }
            } else {
                questions.add(question);
            }
            fragment = PdpAiFragment.getFragment(pageParams.product, questions);
            fragment.setOnCloseListener(() -> {
                changeAiBackground(false);
                logPageView();
            });
            getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(PdpAiFragment.getEnterAnim(), PdpAiFragment.getExitAnim())
                    .add(R.id.fragment_container, fragment, fragmentTag)
                    .commitAllowingStateLoss();
        } else {
            fragment.show(question, pageParams.product);
        }
    }

    private void changeAiBackground(boolean show) {
        View vBackground = binding.vBackground;
        vBackground.setClickable(true);
        if (show) {
            ViewTools.setViewVisibilityIfChanged(vBackground, true);
        }
        vBackground.animate().cancel();
        vBackground.animate()
                .alpha(show ? 0.3f : 0f)
                .setDuration(200)
                .setInterpolator(new DecelerateInterpolator())
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        if (!show)
                            ViewTools.setViewVisibilityIfChanged(vBackground, false);
                    }
                })
                .start();
    }

    boolean isCurrentVisible = false;
    AppBarLayout.OnOffsetChangedListener offSetChangedListener = new AppBarLayout.OnOffsetChangedListener() {
        @Override
        public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
            int absOffset = Math.abs(verticalOffset);
            boolean currentVisible = absOffset >= (binding.ablEvent.getHeight());
            if (isCurrentVisible != currentVisible) {
                boolean isSku = binding.layoutTopSku.getVisibility() == View.VISIBLE;
                String color = isSku ? "#EEF2FB" : !EmptyUtils.isEmpty(topBannerColor) ? topBannerColor : "#ffffff";
                isCurrentVisible = currentVisible;
                binding.vStatus.setBackgroundColor(ViewTools.parseColor(currentVisible ? "#ffffff" : color, Color.WHITE));
                binding.layoutCurrent.setBackgroundColor(ViewTools.parseColor(currentVisible ? "#ffffff" : color, Color.WHITE));
            }
            // 检查偏移量是否真的发生了变化
            boolean offsetChanged = (lastOffset != verticalOffset);
            lastOffset = verticalOffset;
            // 如果没有变化，直接返回
            if (!offsetChanged) {
                return;
            }
            // 标记为正在滚动
            isScrolling = true;
            // 移除之前的停止检测
            if (scrollCheckRunnable != null) {
                handler.removeCallbacks(scrollCheckRunnable);
            }
            // 设置新的停止检测
            scrollCheckRunnable = new Runnable() {
                @Override
                public void run() {
                    // 只有当偏移量发生变化或者还没有报告过停止时，才报告停止
                    if (isScrolling) {
                        isScrolling = false;
                        onAppBarScrollStopped(appBarLayout, verticalOffset);
                    }
                }
            };
            handler.postDelayed(scrollCheckRunnable, SCROLL_STOP_DELAY);
        }
    };

    private void onAppBarScrollStopped(AppBarLayout appBarLayout, int verticalOffset) {
        int totalScrollRange = appBarLayout.getTotalScrollRange();
        float percentage = Math.abs(verticalOffset) / (float) totalScrollRange;

        //停止状态: 完全展开
        if (percentage == 0f) {
            topSectionImp();
        }
        //停止状态: 完全折叠
        else if (percentage == 1f) {
        }
        //停止状态: 中间位置
        else {
            topSectionImp();
        }
    }

    private void topSectionImp() {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, getProductId(), null, null, null, pageParams.traceId);
        String modNm = binding.ivTopBanner.getVisibility() == View.VISIBLE ? "pdpSection_banner" : "pdpSection_sku";
        AppAnalytics.logEvent(EagleTrackEvent.EventType.PROD_IMP, new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(0)
                .addCtx(ctx)
                .build()
                .getParams());
    }

    private boolean isAiShow() {
        return getPdpAiFragment() != null && ViewTools.isViewVisible(binding.vBackground);
    }

    private void aiClickTrack() {
        Map<String, Object> ctx = new ArrayMap<>();
        ctx.put(EagleTrackEvent.Ctx.PAGE_TARGET, getProductId());
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm("ai_bot")
                .setTargetNm("ask_question")
                .setTargetPos(0)
                .addCtx(ctx)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    @Override
    public void onPageRefresh() {
        reload(false);
    }
}
