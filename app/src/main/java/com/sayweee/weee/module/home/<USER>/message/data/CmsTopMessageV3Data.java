package com.sayweee.weee.module.home.provider.message.data;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.module.home.bean.TopMessageV3Bean;

import java.util.ArrayList;
import java.util.List;

@Deprecated
public class CmsTopMessageV3Data extends ComponentData<TopMessageV3Bean, CmsProperty> implements ICache {

    public CmsTopMessageV3Data() {
        super(CmsItemType.TOP_MESSAGE_V3);
    }

    @Override
    public boolean isValid() {
        return t != null && t.content != null;
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        if (isValid()) {
            ArrayList<AdapterDataType> list = new ArrayList<>();
            list.add(this);
            list.add(new CmsBlankData());
            return list;
        }
        return null;
    }
}
