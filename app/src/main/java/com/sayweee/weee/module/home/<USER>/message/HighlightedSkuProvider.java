package com.sayweee.weee.module.home.provider.message;

import android.text.style.StrikethroughSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.ads.bean.AdsCreativeBean;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.provider.message.data.CmsHighlightedSkuData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OnShowTipsListener;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HighlightedSkuProvider extends SimpleSectionProvider<CmsHighlightedSkuData, AdapterViewHolder> implements ImpressionProvider<CmsHighlightedSkuData> {

    @Override
    public int getItemType() {
        return CmsItemType.NEW_TOP_MESSAGE_SKU;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_new_top_message_sku;
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsHighlightedSkuData item) {
        ProductBean bean = item.getProduct();
        ImageView imageView = helper.getView(R.id.iv_icon);
        ImageLoader.load(
                context,
                imageView,
                WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.getHeadImageUrl()),
                R.mipmap.iv_product_placeholder);
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        tvProductName.setText(bean.name);
        //price
        TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
        TextView tvPrice = helper.getView(R.id.tv_price);
        TextView tvBasePrice = helper.getView(R.id.tv_price_delete);
        View llTvProductMark = helper.getView(R.id.ll_tv_product_mark);
        ViewTools.setViewVisible(false, tvPriceVolume, tvPrice, tvBasePrice, llTvProductMark);
        if (bean.showVolumePrice()) {
            ViewTools.setViewVisibilityIfChanged(tvPriceVolume, true);
            Spanny s = new Spanny()
                    .append(OrderHelper.formatUSMoney(bean.price))
                    .append(context.getString(R.string.s_volume_threshold_simple, bean.volume_threshold));
            tvPriceVolume.setText(s);
        } else {
            boolean showBasePrice = bean.base_price > 0;
            ViewTools.setViewVisibilityIfChanged(tvPrice, true);
            ViewTools.setViewVisibilityIfChanged(tvBasePrice, showBasePrice);
            tvPrice.setText(OrderHelper.formatUSMoney(bean.price));
            if (showBasePrice) {
                tvBasePrice.setText(new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()));
            }
            ProductBean.LabelListBean label = CollectionUtils.firstOrNull(bean.label_list, ProductBean.LabelListBean::isOffLabelKey);
            if (label != null) {
                ViewTools.setViewVisibilityIfChanged(llTvProductMark, true);
                ShapeTextView tvMarker = helper.getView(R.id.tv_product_mark);
                tvMarker.setText(label.label_name);
            }
        }
        String modNm = item.getEventKey();
        int modPos = item.position;
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .build()
                .getElement();
        //加购action
        CartOpLayout layoutOp = helper.getView(R.id.layout_op);
        if (ProductView.isPreSell(bean.sold_status)) {
            layoutOp.setDisableStyle();
        } else {
            String source = ProductHelper.getAddCartSource(modNm);
            OpHelper.helperOp(layoutOp, bean, item, source, element);
            layoutOp.setOnShowTipsListener(view -> {
            });
        }
        //click
        AdsCreativeBean adsCreative = bean.ads_creative;
        EagleContext ctx = new EagleContext();
        ctx.setVolumePriceSupport(bean.volume_price_support);
        helper.getView(R.id.ll_click).setOnClickListener(v -> {
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm(modNm)
                    .setMod_pos(modPos)
                    .setTargetNm(String.valueOf(bean.id))
                    .setTargetPos(0)
                    .setTargetType("product")
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .addCtx(ctx.asMap())
                    .build().getParams());
            context.startActivity(WebViewActivity.getIntent(context, bean.view_link));
            if (adsCreative != null) {
                AdsManager.addAdsTrackPendingInfo(bean);
                AdsManager.get().trackClick(
                        /* productBean= */bean,
                        /* position= */0,
                        /* dbgInfo= */null
                );
            }
        });
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CmsHighlightedSkuData item, int position) {
        List<ImpressionBean> list = new ArrayList<>();
        ProductBean product = item.getProduct();
        String productId = String.valueOf(product.id);
        String key = position + "_" + productId;
        EagleContext ctx = new EagleContext().setVolumePriceSupport(product.volume_price_support);
        Map<String, Object> element = EagleTrackManger.get().getElement(item.getEventKey(), item.position, null, -1);
        Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_LIST).setTarget(product, 0).setElement(element).setContext(ctx.asMap()).get();
        list.add(new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key));
        //ads_creative
        if (product.ads_creative != null && !product.ads_creative.isImpressionTracked) {
            product.ads_creative.isImpressionTracked = true;
            AdsManager.get().trackImpression(
                    /* productBean= */product,
                    /* position= */0,
                    /* dbgInfo= */null
            );
        }
        return list;
    }
}
