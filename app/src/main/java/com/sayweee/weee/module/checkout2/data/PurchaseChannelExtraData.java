package com.sayweee.weee.module.checkout2.data;

public final class PurchaseChannelExtraData {

    // errors
    public boolean emptyPurchaseChannel;
    public boolean emptyMainPurchaseChannel;
    public boolean invalidCardCvv;

    // flags
    public String mainChannelCode;
    public boolean isCheckCvv;
    public boolean hasEbtChannel;
    public boolean isPointsAllDeducted;
    public boolean isChannelSelectable;

    public boolean hasError() {
        return emptyPurchaseChannel || emptyMainPurchaseChannel || invalidCardCvv;
    }
}
