package com.sayweee.weee.module.cms.service2;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.logger.Logger;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.service.CmsApi;
import com.sayweee.weee.module.cms.service.ICmsParser;
import com.sayweee.weee.module.cms.service.IMultiDataSourceData;
import com.sayweee.weee.module.cms.service2.bean.CmsPageData;
import com.sayweee.weee.module.cms.service2.loader.CmsPageLoader;
import com.sayweee.weee.module.cms.service2.loader.CmsPagePagingLoader;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.Observer;
import io.reactivex.functions.Function;

public abstract class CmsViewModel<M extends BaseLoaderModel<OrderApi>>
        extends BaseViewModel<M>
        implements CmsPageLoader.Callback {

    protected static final String TAG = "CmsViewModel";

    // Internal usages
    protected final CmsComponentParsers parsers = new CmsComponentParsers();
    protected CmsPageData pageData;
    private CmsPageLoader pageLoader;

    // Interact with UI
    public MutableLiveData<CmsPageParam> pageParamData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterDataType>> adapterData = new MutableLiveData<>();
    public MutableLiveData<List<AdapterDataType>> adapterAppendData = new MutableLiveData<>();
    public MutableLiveData<String> componentDataUpdateLiveData = new MutableLiveData<>();
    protected MutableLiveData<Boolean> loadMoreEndSignal = new MutableLiveData<>();
    protected MutableLiveData<Boolean> componentConsumedSignal = new MutableLiveData<>();

    protected CmsViewModel(@NonNull Application application) {
        super(application);
    }

    public LiveData<Boolean> getLoadMoreEndSignal() {
        return loadMoreEndSignal;
    }

    public LiveData<Boolean> getComponentConsumedSignal() {
        return componentConsumedSignal;
    }

    protected void beforeFetchCmsData() {
        pageData = new CmsPageData();
        pageLoader = null;
    }

    public void fetchCmsData(Map<String, Serializable> params) {
        beforeFetchCmsData();
        getLoader()
                .createHttpService(CmsApi.class)
                .getDataByCMS(params)
                .compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<CmsBean>>() {

                    @Override
                    public void onResponse(ResponseBean<CmsBean> response) {
                        CmsPageData pageData = parseCmsResponse(response.getData());
                        parseCmsPageData(pageData);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        onCmsError(failure);
                    }
                });
    }

    @NonNull
    protected CmsPageData parseCmsResponse(@NonNull CmsBean cmsBean) {
        return CmsResponseParser.of(parsers)
                .setCmsDataSourceModifier(this::modifyComponentDataSource)
                .parse(cmsBean);
    }

    protected void parseCmsPageData(@NonNull CmsPageData pageData) {
        this.pageData = pageData;
        loadData();
    }

    protected void onCmsError(@NonNull FailureBean failure) {

    }

    public void loadData() {
        Logger.i(TAG, "loadData()");
        getPageLoader().loadData();
    }

    public void loadMoreData() {
        Logger.i(TAG, "loadMoreData()");
        getPageLoader().loadData();
    }

    @NonNull
    protected CmsPageLoader getPageLoader() {
        if (pageLoader != null) {
            return pageLoader;
        }

        CmsPageLoader loader = createPageLoader();
        loader.setConsumer(this);
        loader.setCallback(this);
        this.pageLoader = loader;
        return pageLoader;
    }

    @NonNull
    protected CmsPageLoader createPageLoader() {
        return new CmsPagePagingLoader(pageData);
    }

    protected void modifyComponentDataSource(ComponentData<?, ?> data, CmsDataSource dataSource) {

    }

    @Override
    public final void onPageLoaderDataLoaded(int pageNum, @NonNull List<ComponentData<?, ?>> newDataList) {
        Logger.i(TAG, "onPageLoaderDataLoaded() pageNum=" + pageNum + ", newDataList=" + newDataList.size());
        dispatchDataChanged(pageNum, newDataList);
    }

    @Override
    public final void onPageLoaderAppendAdapterDataLoaded(int pageNum, @NonNull List<AdapterDataType> newAdapterDataList) {
        Logger.i(TAG, "onPageLoaderAppendAdapterDataLoaded() pageNum=" + pageNum + ", newDataList=" + newAdapterDataList.size());

        List<AdapterDataType> list = beforeDispatchAdapterData(pageNum, newAdapterDataList);
        execDispatchAdapterData(pageNum, list);
    }

    @Override
    public final void onPageLoaderDataLoadFinished(int pageNum) {
        Logger.i(TAG, "onPageLoaderDataLoadFinished() pageNum=" + pageNum);
        loadMoreEndSignal.postValue(true);
    }

    @Override
    public void onPageLoaderAllComponentsConsumed() {
        Logger.i(TAG, "onPageLoaderAllComponentsConsumed()");
        componentConsumedSignal.postValue(true);
    }

    protected final void dispatchDataChanged(int pageNum, List<ComponentData<?, ?>> newDataList) {
        processComponentDataList();
        List<AdapterDataType> list = createAdapterData(pageNum, newDataList);
        list = beforeDispatchAdapterData(pageNum, list);
        execDispatchAdapterData(pageNum, list);
    }

    protected void processComponentDataList() {
        int pos = 0;
        for (ComponentData<?, ?> data : pageData.getComponentDataList()) {
            if (data != null && data.isValid()) {
                data.position = pos;
                pos++;
            }
        }
    }

    @NonNull
    protected List<AdapterDataType> createAdapterData(int pageNum, List<ComponentData<?, ?>> newDataList) {
        List<AdapterDataType> list = new ArrayList<>();
        for (ComponentData<?, ?> component : newDataList) {
            if (component != null && component.isValid()) {
                List<? extends AdapterDataType> temp = component.toComponentData();
                if (temp != null && !temp.isEmpty()) {
                    list.addAll(temp);
                }
            }
        }
        return list;
    }

    protected List<AdapterDataType> beforeDispatchAdapterData(int pageNum, @NonNull List<AdapterDataType> list) {
        return list;
    }

    protected void execDispatchAdapterData(int pageNum, @NonNull List<AdapterDataType> list) {
        if (pageNum == 0) {
            adapterData.postValue(list);
        } else {
            adapterAppendData.postValue(list);
        }
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        clearCmsParser();
    }

    protected void registerCmsParser(String componentKey, ICmsParser parser) {
        parsers.register(componentKey, parser);
    }

    protected void clearCmsParser() {
        parsers.clear();
    }

    public void requestMultiDataSource(@NonNull CmsDataSource dataSource) {
        requestMultiDataSource(dataSource, 0L);
    }

    public void requestMultiDataSource(@NonNull CmsDataSource dataSource, long delayMillis) {
        Observer<ResponseBean<String>> observer = new ResponseObserver<ResponseBean<String>>() {

            @Override
            public void onResponse(ResponseBean<String> response) {
                updateComponentData(response.getData(), null);
            }

            @Override
            public void onError(FailureBean failure) {
                super.onError(failure);
                updateComponentData(null, failure);
            }

            private void updateComponentData(String data, FailureBean failureBean) {
                ComponentData<?, ?> componentData = pageData.getComponentById(dataSource.getComponentId());
                if (!(componentData instanceof IMultiDataSourceData)) {
                    return;
                }

                IMultiDataSourceData target = (IMultiDataSourceData) componentData;
                if (data != null) {
                    if (data.startsWith("[")) {
                        List<?> list = JsonUtils.parseArray(data, dataSource.getTargetClazz());
                        target.updateDataSourceResponse(dataSource, list, null);
                    } else {
                        Object object = JsonUtils.parseObject(data, dataSource.getTargetClazz());
                        target.updateDataSourceResponse(dataSource, object, null);
                    }
                } else {
                    target.updateDataSourceResponse(dataSource, null, failureBean);
                }
                componentDataUpdateLiveData.postValue(dataSource.getComponentId());
            }
        };

        Observable<ResponseBean<String>> observable;
        final Observable<ResponseBean<String>> rxGetCmsChildDataByUrl;
        rxGetCmsChildDataByUrl = getLoader().createHttpService(CmsApi.class)
                .getCmsChildDataByUrl(dataSource.getUrl(), dataSource.getQueryParams());
        if (delayMillis > 0L) {
            observable = Observable.timer(delayMillis, TimeUnit.MILLISECONDS).flatMap(new Function<Long, ObservableSource<ResponseBean<String>>>() {
                @Override
                public ObservableSource<ResponseBean<String>> apply(Long aLong) throws Exception {
                    return rxGetCmsChildDataByUrl;
                }
            });
        } else {
            observable = rxGetCmsChildDataByUrl;
        }
        observable = observable.compose(DisposableTransformer.scheduler(this, false));
        observable.subscribe(observer);
    }

    @Nullable
    public CmsDataSource getDataSource(@NonNull String componentId) {
        ComponentData<?, ?> componentData = pageData.getComponentById(componentId);
        if (componentData instanceof IMultiDataSourceData) {
            return ((IMultiDataSourceData) componentData).getBaseDataSource();
        }
        return null;
    }

    public void updateComponentData(@NonNull CmsDataSource dataSource, Object data, FailureBean failureBean) {
        ComponentData<?, ?> componentData = pageData.getComponentById(dataSource.getComponentId());
        if (componentData instanceof IMultiDataSourceData) {
            ((IMultiDataSourceData) componentData).updateDataSourceResponse(dataSource, data, failureBean);
            componentDataUpdateLiveData.postValue(dataSource.getComponentId());
        }
    }

}
