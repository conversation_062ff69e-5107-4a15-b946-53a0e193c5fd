package com.sayweee.weee.module.cart.widget;

import android.content.Context;
import android.text.style.StrikethroughSpan;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.module.cart.bean.CouponReminder;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.checkout.CheckOutSectionActivity;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.checkout2.DealPayActivity;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.utils.Spanny;

public class CheckOutBottomView extends LinearLayout {

    protected ViewHelper helper;

    String finalAmount, baseFinalAmount;
    CouponReminder couponReminder;
    String reminder;
    String topContent;

    public CheckOutBottomView(Context context) {
        this(context, null);
    }

    public CheckOutBottomView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CheckOutBottomView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        int layoutRes = R.layout.layout_check_out_btn;
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
    }

    public CheckOutBottomView setAmount(String finalAmount, String baseFinalAmount) {
        this.finalAmount = finalAmount;
        this.baseFinalAmount = baseFinalAmount;
        return this;
    }

    public CheckOutBottomView setCouponReminder(CouponReminder couponReminder) {
        this.couponReminder = couponReminder;
        return this;
    }

    public CheckOutBottomView setCouponReminder(String reminder) {
        this.reminder = reminder;
        return this;
    }

    public CheckOutBottomView setAttachedData() {
        Context context = getContext();
        helper.setVisible(R.id.loading_progressbar, false);
        TextView tvTotal = helper.getView(R.id.tv_total);
        TextView tvFinalAmount = helper.getView(R.id.tv_final_amount);
        TextView tvBaseFinalAmount = helper.getView(R.id.tv_base_final_amount);
        HtmlTextView tvCouponReminder = helper.getView(R.id.tv_coupon_reminder);
        HtmlTextView tvTopContent = helper.getView(R.id.tv_top_content);
        ViewTools.setViewVisible(tvBaseFinalAmount, isShowBaseFinalAmount());
        ViewTools.setViewVisible(tvCouponReminder, isShowCouponReminder() || !EmptyUtils.isEmpty(reminder));
        boolean hasTopContent = !EmptyUtils.isEmpty(topContent);
        ViewTools.setViewVisible(tvTopContent, hasTopContent);
        if (hasTopContent) {
            tvTopContent.setHtmlText(topContent);
        }
        // Checkout || DealPay shows total, others show subtotal
        // Jira: https://sayweee.atlassian.net/browse/PCORE-8545
        if (context instanceof CheckOutSectionActivity
                || context instanceof CheckoutSectionActivity
                || context instanceof DealPayActivity
        ) {
            tvTotal.setText(String.format("%s:", context.getString(R.string.s_total)));
        } else {
            tvTotal.setText(String.format("%s:", context.getString(R.string.s_subtotal_section)));
        }
        tvFinalAmount.setText(OrderHelper.formatUSMoney(DecimalTools.parseDouble(finalAmount)));
        if (isShowBaseFinalAmount()) {
            tvBaseFinalAmount.setText(new Spanny(OrderHelper.formatUSMoney(DecimalTools.parseDouble(baseFinalAmount)), new StrikethroughSpan()));
        }
        if (isShowCouponReminder()) {
            ViewTools.setViewHtml(tvCouponReminder, couponReminder.tag_text);
        } else if (!EmptyUtils.isEmpty(reminder)) {
            ViewTools.setViewHtml(tvCouponReminder, reminder);
        }
        return this;
    }

    public CheckOutBottomView setOnViewClickListener(int viewId, OnClickListener listener) {
        helper.setOnClickListener(viewId, listener);
        return this;
    }

    public boolean isShowBaseFinalAmount() {
        return !EmptyUtils.isEmpty(baseFinalAmount);
    }

    public boolean isShowCouponReminder() {
        return couponReminder != null && !EmptyUtils.isEmpty(couponReminder.tag_text);
    }

    public void loading() {
        ProgressBar progressBar = helper.getView(R.id.loading_progressbar);
        progressBar.setVisibility(VISIBLE);
        TextView tvCheckout = helper.getView(R.id.tv_checkout);
        tvCheckout.setEnabled(false);
        tvCheckout.setText("");
        TextView tvCheckoutUnable = helper.getView(R.id.tv_checkout_unable);
        tvCheckoutUnable.setText("");
    }

    public boolean isLoading() {
        ProgressBar progressBar = helper.getView(R.id.loading_progressbar);
        return progressBar.getVisibility() == View.VISIBLE;
    }

    public void setCanCheckOut(boolean enable) {
        setCanCheckOut(enable, R.string.s_cart_checkout);
    }

    public void setCanCheckOut(boolean enable, int textId) {
        String text = helper.itemView.getContext().getString(textId);
        setCanCheckOut(enable, text);
    }

    public void setCanCheckOut(boolean enable, String text) {
        helper.getView(R.id.loading_progressbar).setVisibility(GONE);
        TextView tvCheckout = helper.getView(R.id.tv_checkout);
        tvCheckout.setText(text);
        tvCheckout.setEnabled(enable);
        helper.setVisible(R.id.tv_checkout, enable);
        TextView tvCheckoutUnable = helper.getView(R.id.tv_checkout_unable);
        tvCheckoutUnable.setText(text);
    }

    public boolean isCanCheckOut() {
        TextView tvCheckout = helper.getView(R.id.tv_checkout);
        return tvCheckout != null && tvCheckout.isEnabled();
    }

    /**
     * Check if the view is ready for user interaction.
     * This means it is not loading and the checkout button is enabled.
     *
     * @return true if the view is interactive, false otherwise.
     */
    public boolean isInteractivity() {
        return !isLoading() && isCanCheckOut();
    }

}
