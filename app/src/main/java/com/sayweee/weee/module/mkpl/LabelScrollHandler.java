package com.sayweee.weee.module.mkpl;

import androidx.core.util.Pair;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.widget.recycler.RecyclerViewTools;

public final class LabelScrollHandler {

    private LabelScrollHandler() {

    }

    /**
     * 通知数据被改动
     *
     * @param view recyclerview
     */
    public static void notifyAdapterDataChanged(RecyclerView view) {
        notifyAdapterDataChanged(view, 200L);
    }

    public static void notifyAdapterDataChanged(RecyclerView view, long delayMillis) {
        if (view != null && view.isAttachedToWindow()) {
            view.postDelayed(new Runnable() {
                @Override
                public void run() {
                    findVisibleItem(view);
                }
            }, delayMillis);
        }
    }

    /**
     * 滚动停止后触发
     *
     * @param view recyclerview
     */
    public static void notifyScrollStateChanged(RecyclerView view) {
        if (view != null) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    findVisibleItem(view);
                }
            });
        }
    }

    /**
     * 找到可见的item
     *
     * @param recyclerView recyclerview
     */
    private static void findVisibleItem(RecyclerView recyclerView) {
        if (recyclerView == null || !recyclerView.isAttachedToWindow()) {
            return;
        }
        RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        if (!(adapter instanceof LabelScrollAdapter)) {
            return;
        }
        Pair<Integer, Integer> range = RecyclerViewTools.findVisibleItemPositionRange(recyclerView, false);
        if (range != null) {
            if (recyclerView.isComputingLayout()) {
                recyclerView.post(() -> ((LabelScrollAdapter) adapter).notifyItemScrollByPosition(range.first, range.second));
            } else {
                ((LabelScrollAdapter) adapter).notifyItemScrollByPosition(range.first, range.second);
            }
        }
    }

}
