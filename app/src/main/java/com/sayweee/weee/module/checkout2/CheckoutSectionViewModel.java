package com.sayweee.weee.module.checkout2;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelKt;

import com.sayweee.core.order.OrderProvider;
import com.sayweee.service.PaymentService;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.bean.CheckOutPointsData;
import com.sayweee.weee.module.checkout.bean.CheckOutPointsTitleData;
import com.sayweee.weee.module.checkout.bean.CheckoutCouponData;
import com.sayweee.weee.module.checkout.bean.CheckoutCurrentBenefitsData;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutOrderSummaryData;
import com.sayweee.weee.module.checkout.bean.CheckoutRemindTopData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CheckoutTermsData;
import com.sayweee.weee.module.checkout.bean.CheckoutTipData;
import com.sayweee.weee.module.checkout.bean.CheckoutVipData;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout2.bean.CheckoutV4Bean;
import com.sayweee.weee.module.checkout2.data.CheckoutPurchaseChannelData;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.Observer;

public class CheckoutSectionViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    static final String ORDER_EMPTY = "SO10012"; // 您的购物车暂时为空哦
    static final String ORDER_FORCE_REFRESH = "SO10001"; // 结算数据异常，需要强制刷新
    static final String ORDER_FORCE_REFRESH_2 = "SO10005"; // 结算数据异常，需要强制刷新，同上
    static final String ORDER_ADDRESS_EMPTY = "SO90010"; // 地址信息为空
    static final String ORDER_COUPON_ERROR = "SO90001"; // 优惠券信息异常
    static final String ORDER_ADDRESS_ERROR = "SO90002"; // 地址信息错误
    static final String ORDER_PHONE_ERROR = "SO90003"; // 联系方式错误
    static final String ORDER_SHOW_ERROR = "SO90004"; // 接口通知前端显示错误信息并刷新数据
    static final String ORDER_NOT_MAIL = "SO90113"; // 当前地址不在配送范围
    static final String TAG_FORCE_REFRESH = "force"; //

    final MutableLiveData<List<AdapterDataType>> adapterData = new MutableLiveData<>();
    final MutableLiveData<PreCheckoutV2Bean> checkoutPreLiveData = new MutableLiveData<>();
    final MutableLiveData<CheckoutV4Bean> checkoutLiveData = new MutableLiveData<>();
    final MutableLiveData<FailureBean> failureData = new MutableLiveData<>();
    final MutableLiveData<Boolean> refreshData = new MutableLiveData<>();
    final MutableLiveData<CouponBean> couponLiveData = new MutableLiveData<>();
    final MutableLiveData<Integer> toastLiveData = new MutableLiveData<>();
    int couponSize;

    private static final String PAYMENT_ROUTING = "test";

    private final CheckoutPreAgent checkoutPreAgent = new CheckoutPreAgent();

    public CheckoutSectionViewModel(@NonNull Application application) {
        super(application);
    }

    public LiveData<Boolean> getLoadPreCheckoutDataSignal() {
        return refreshData;
    }

    public void refreshPreCheckoutData() {
        refreshPreCheckoutData(false);
    }

    public void refreshPreCheckoutData(boolean showLoading) {
        refreshData.postValue(showLoading);
    }

    public void setCartDomain(String cartDomain) {
        this.checkoutPreAgent.cartDomain = cartDomain;
    }

    public void setCartId(String cartId) {
        String[] cartIdsArr = null;
        if (cartId != null && !cartId.isEmpty()) {
            cartIdsArr = cartId.split(",");
        }
        this.checkoutPreAgent.cartIds = cartIdsArr;
    }

    /**
     * 准备结算V2
     */
    public void preCheckoutV2(boolean isSilent) {
        rxCheckoutPre().subscribe(getPreCheckoutObserver(isSilent));
    }

    private Observable<ResponseBean<PreCheckoutV2Bean>> rxCheckoutPre() {
        RequestParams requestParams = checkoutPreAgent.prepareCheckoutPreRequest();
        String preV2 = "/ec/so/order/checkout/pre/v2";
        String preCart = "/ec/so/order/checkout/pre/cart";//可以根据cart_id结算对应的购物车
        boolean usePreCart = checkoutPreAgent.cartIds != null && checkoutPreAgent.cartIds.length > 0;
        String api = usePreCart ? preCart : preV2;
        if (usePreCart) {
            requestParams.put("cart_ids", checkoutPreAgent.cartIds);
        }
        requestParams.put("payment_routing", PAYMENT_ROUTING);
        return getLoader().getHttpService()
                .preCheckoutV2(api, requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false));
    }

    private Observer<ResponseBean<PreCheckoutV2Bean>> getPreCheckoutObserver(boolean isSilent) {
        return new ResponseObserver<ResponseBean<PreCheckoutV2Bean>>() {

            @Override
            public void onBegin() {
                if (!isSilent) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onResponse(ResponseBean<PreCheckoutV2Bean> response) {
                PreCheckoutV2Bean data = response.getData();
                checkoutPreLiveData.postValue(data);
            }

            @Override
            public void onError(FailureBean failure) {
                super.onError(failure);
                failureData.postValue(failure);
            }

            @Override
            public void onFinish() {
                if (!isSilent) {
                    setLoadingStatus(false);
                }
            }
        };
    }

    public void renderAdapterData(@NonNull PreCheckoutV2Bean data) {
        List<AdapterDataType> list = new ArrayList<>();
        // Top banner
        if (!EmptyUtils.isEmpty(data.reminder_content_top)) {
            list.add(new CheckoutRemindTopData(data.reminder_content_top, CheckOutSectionAdapter.TYPE_REMIND_TOP));
        }

        // Address
        list.add(new CheckoutDeliveryData(data.address_info, CheckOutSectionAdapter.TYPE_DELIVERY_INFORMATION));

        // Pay with
        boolean saveRewardsChecked = false;
        boolean hasSaveRewards = !EmptyUtils.isEmpty(data.member_upgrade_plans);
        if (hasSaveRewards) {
            for (PreCheckoutV2Bean.MemberUpgradePlansBean bean : data.member_upgrade_plans) {
                if (bean.selected) {
                    saveRewardsChecked = true;
                    break;
                }
            }
        }
        CheckoutPurchaseChannelData purchaseChannelData = new CheckoutPurchaseChannelData(
                /* purchaseChannels= */checkoutPreAgent.getSupportChannels(),
                /* cvcText= */checkoutPreAgent.cvcText,
                /* firstPurchasedAmount= */null,
                /* extraData= */checkoutPreAgent.getPurchaseChannelExtraData()
        );
        list.add(purchaseChannelData);

        // Points
        boolean hasPoints = data.point_info != null && data.point_info.points_current > 0;
        if (hasPoints) {
            boolean isAllCommissionPartner = checkoutPreAgent.isAllCommissionPartner();
            CheckoutUsePointsData pointsData = new CheckoutUsePointsData(
                    /* pointInfo= */data.point_info,
                    /* saveRewardsChecked= */saveRewardsChecked,
                    /* isAllCommissionPartner= */isAllCommissionPartner
            );
            list.add(pointsData);
        }

        // Review order
        if (!EmptyUtils.isEmpty(data.order_reviews)) {
            list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_REVIEW_ORDER));
            for (PreCheckoutOrderReviewsBean bean : data.order_reviews) {
                list.add(new CheckoutReviewOrderData(bean, CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST));
            }
        }

        // Coupons
        if (EmptyUtils.isEmpty(data.coupon_info)) {
            getCoupons(checkoutPreAgent.cartDomain);
        }
        list.add(new CheckoutCouponData(data.coupon_info, CheckOutSectionAdapter.TYPE_APPLY_COUPON).setCouponSize(couponSize));

        // Tip
        boolean hasTips = data.tip_info != null && data.tip_info.show && !EmptyUtils.isEmpty(data.tip_info.options);
        if (hasTips) {
            list.add(new CheckoutTipData(data.tip_info, CheckOutSectionAdapter.TYPE_DELIVERY_TIP));
        }

        // Summary
        if (!EmptyUtils.isEmpty(data.order_summary)) {
            list.add(new CheckoutOrderSummaryData(data.order_summary, CheckOutSectionAdapter.TYPE_ORDER_SUMMARY).setFeeInfo(data.fee_info));
        }
        if (!EmptyUtils.isEmpty(data.current_benefits)) {
            list.add(new CheckoutCurrentBenefitsData(data.current_benefits, CheckOutSectionAdapter.TYPE_CURRENT_BENEFITS));
        }

        // Member plans
        if (hasSaveRewards) {
            list.add(new CheckOutPointsTitleData(
                    CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS_TITLE,
                    data.member_upgrade_plan_title,
                    data.member_upgrade_plan_sub_title
            ));
            list.add(new CheckOutPointsData(
                    CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS,
                    data.member_upgrade_plans
            ));
        }

        if (!EmptyUtils.isEmpty(data.point_info) && !EmptyUtils.isEmpty(data.point_info.order_reward_points_desc_v2)) {
            list.add(new CheckoutVipData(data.point_info, CheckOutSectionAdapter.TYPE_VIP_MEMBER));
        }

        // Always fill terms layout.
        // Jira: https://sayweee.atlassian.net/browse/PEP-4048
        // Jira: https://sayweee.atlassian.net/browse/PCORE-2487
        list.add(new CheckoutTermsData(
                /* type= */CheckOutSectionAdapter.TYPE_TERMS,
                /* containFrozen= */data.contain_forzen,
                /* alcoholAgreementType= */checkoutPreAgent.getAlcoholAgreementType()
        ));

        list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_PLACE));

        adapterData.postValue(list);
    }

    /**
     * 结算
     */
    public void checkout() {
        final RequestParams requestParams = checkoutPreAgent.prepareCheckoutRequest();
        getLoader().getHttpService()
                .checkoutV4(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<CheckoutV4Bean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(ResponseBean<CheckoutV4Bean> response) {
                        checkoutLiveData.postValue(response.getData());
                        OrderProvider.get().refreshSimplePreOrder();
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        failure.setObject(TAG_FORCE_REFRESH);
                        failureData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    public void updatePaymentCategory(boolean usePoints, boolean removeEbt) {
        RequestParams requestParams = getCheckoutPreAgent()
                .generateChangePaymentCategoryParams(usePoints, removeEbt);
        PaymentService.get().changePaymentMethods(
                ViewModelKt.getViewModelScope(this),
                requestParams.get(),
                (result, failure) -> {
                    if (result != null && result) {
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", "checkout");
                        EagleTrackManger.get().trackEagleInfoUpdate(
                                EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                true,
                                "weee_points",
                                map
                        );
                        toastLiveData.postValue(R.string.s_checkout_payment_updated);
                    } else {
                        if (failure != null) {
                            showToast(failure.getMessage());
                        }
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", "checkout");
                        EagleTrackManger.get().trackEagleInfoUpdate(
                                EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                false,
                                "weee_points",
                                map
                        );
                    }
                    refreshPreCheckoutData();
                });
    }

    public void getCoupons(String cartDomain) {
        getLoader()
                .getHttpService()
                .getCoupons(cartDomain)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<CouponBean>>() {

                    @Override
                    public void onResponse(ResponseBean<CouponBean> response) {
                        couponLiveData.postValue(response.getData());
                    }
                });
    }

    public static void showToast(String msg) {
        String toastMsg = !EmptyUtils.isEmpty(msg) ? msg : "Sorry, something went wrong, please try again later.";
        Toaster.showToast(toastMsg);
    }

    public boolean ensureDeliveryInfo(@NonNull PreCheckoutV2Bean preCheckoutV2Bean) {
        boolean hasAddress = preCheckoutV2Bean.address_info != null
                && !EmptyUtils.isEmpty(preCheckoutV2Bean.address_info.address);
        if (!hasAddress) {
            boolean requireAddress = CollectionUtils.any(preCheckoutV2Bean.order_reviews, orderReview -> {
                PreCheckoutOrderReviewsBean.ShippingInfoBean shippingInfo;
                shippingInfo = orderReview != null ? orderReview.shipping_info : null;
                String deliveryMode = shippingInfo != null ? shippingInfo.delivery_mode : null;
                return "delivery".equalsIgnoreCase(deliveryMode) || "shipping".equalsIgnoreCase(deliveryMode);
            });
            return !requireAddress;
        }
        return true;
    }

    public void setCvcText(String cvcText) {
        getCheckoutPreAgent().cvcText = cvcText;
    }

    CheckoutPreAgent getCheckoutPreAgent() {
        return checkoutPreAgent;
    }

    public void setCheckoutPreData(PreCheckoutV2Bean preCheckoutV2Bean) {
        checkoutPreAgent.wrap(preCheckoutV2Bean);
    }
}
