package com.sayweee.weee.module.home.provider.category;

import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.service.ICmsParser;
import com.sayweee.weee.module.home.bean.CategoriesBean;
import com.sayweee.weee.module.home.bean.CategoriesProperty;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;

/**
 * Author:  winds
 * Date:    2023/5/29.
 * Desc:
 */
public class CategoryParser implements ICmsParser {

    @Override
    public CmsCategoryData packetData(
            String componentId,
            CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean component,
            CmsBean.DataSourceBean source,
            CmsPageParam pageParam
    ) {
        CategoriesProperty property = new CategoriesProperty();
        if (component.properties != null) {
            property.parseProperty(component.properties);
        }
        CmsCategoryData data;
        if (property.displayCapsuleStyle()) {
            data = new CmsCategoryData(CmsItemType.CATEGORIES_CAPSULE);
        } else if (property.displayBarStyle()) {
            data = new CmsCategoryData(CmsItemType.CATEGORIES_BAR);
        } else {
            data = new CmsCategoryData();
        }
        data.setProperty(property);
        return data;
    }

    @Override
    public Class<?> getTargetClazz() {
        return CategoriesBean.class;
    }
}
