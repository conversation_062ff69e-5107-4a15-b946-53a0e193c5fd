package com.sayweee.weee.module.order.provider;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.os.Build;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.helper.widget.Flow;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ProviderOrderButtonBinding;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.order.data.OrderButtonData;
import com.sayweee.weee.module.order.list.OrderListFragment;
import com.sayweee.weee.module.web.WebRouter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.skydoves.balloon.Balloon;
import com.skydoves.balloon.BalloonSizeSpec;

import java.util.HashMap;
import java.util.Map;


//
// Created by Thomsen on 02/04/2024.
// Copyright (c) 2024 Weee LLC. All rights reserved.
//
public class OrderButtonProvider extends SimpleSectionProvider<OrderButtonData, AdapterViewHolder> {

    private OnButtonClickListener onButtonClickListener;

    public void setOnButtonClickListener(OnButtonClickListener onButtonClickListener) {
        this.onButtonClickListener = onButtonClickListener;
    }

    @Override
    public int getItemType() {
        return R.layout.provider_order_button;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.provider_order_button;
    }

    private ArrayMap<String, TextView> tipButtons = new ArrayMap<>();
    private ArrayMap<String, Balloon> tipBalloons = new ArrayMap<>(); // <key, balloon>

    @Override
    public void convert(AdapterViewHolder helper, OrderButtonData item) {
        if (item == null || item.items == null) return;

        ProviderOrderButtonBinding binding = ProviderOrderButtonBinding.bind(helper.itemView);

        binding.getRoot().removeAllViews();

        Flow flow = getFlow(binding);
        binding.getRoot().addView(flow);

        int[] refIds = new int[item.items.size()];

        TextView btnView = null;
        for (OrderButtonData.ButtonItem buttonItem : item.items) {
            btnView = getButton(binding, buttonItem);
            int id = View.generateViewId();
            btnView.setId(id);
            refIds[item.items.indexOf(buttonItem)] = id;
            binding.getRoot().addView(btnView);
            // init post tip
//            initTipView(binding, btnView, buttonItem.tips);
            setBalloonTip(btnView, buttonItem);
        }
        flow.setReferencedIds(refIds);
    }

    private static Flow getFlow(ProviderOrderButtonBinding binding) {
        Flow flow = new Flow(binding.getRoot().getContext());
        ConstraintLayout.LayoutParams flowParams = new ConstraintLayout.LayoutParams(
                0, ConstraintLayout.LayoutParams.WRAP_CONTENT
        );
        flowParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
        flowParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
        flowParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
        flow.setLayoutParams(flowParams);

        flow.setVerticalGap(CommonTools.dp2px(10));
        flow.setHorizontalGap(CommonTools.dp2px(8));
        flow.setHorizontalBias(1.0f);
        flow.setHorizontalStyle(Flow.CHAIN_PACKED);
        flow.setWrapMode(Flow.WRAP_CHAIN);

        return flow;
    }

    private TextView getButton(ProviderOrderButtonBinding binding, OrderButtonData.ButtonItem buttonItem) {
        Context context = binding.getRoot().getContext();
        int tbSize = CommonTools.dp2px(5);
        int lrSize = CommonTools.dp2px(16);
        TextView btnView = new TextView(context);
        ConstraintLayout.LayoutParams params = new ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                CommonTools.dp2px(36));
        btnView.setLayoutParams(params);
        btnView.setPadding(lrSize, tbSize, lrSize, tbSize);
        btnView.setText(getValueByResName(context, buttonItem.resName));
        btnView.setTextSize(11f);
        btnView.setGravity(Gravity.CENTER);
        ViewTools.applyTextStyle(btnView, buttonItem.style);
        // btnView.setTypeface(null, Typeface.NORMAL);
        ViewTools.applyTextColor(btnView, buttonItem.color);
        if (buttonItem.background > 0) {
            btnView.setBackgroundResource(buttonItem.background);
        }
        btnView.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                trackClickAction(buttonItem);
                if (buttonItem.link == null || OrderButtonData.TYPE_CANCEL_ORDER.equals(buttonItem.type)
                        || OrderButtonData.TYPE_CANCEL.equals(buttonItem.type)) {
                    if (onButtonClickListener != null) {
                        onButtonClickListener.onClick(buttonItem);
                    }
                    return;
                }
                // send broadcast before to page
                if (!OrderButtonData.TYPE_POINTS_GBUYS.equalsIgnoreCase(buttonItem.type)
                        && !OrderButtonData.TYPE_LOGISTICS_TRACKING.equalsIgnoreCase(buttonItem.type)) {
                    LocalBroadcastManager.getInstance(context)
                            .sendBroadcast(new Intent(OrderListFragment.ACTION_REFRESH)
                                    .putExtra(OrderListFragment.BUNDLE_PAGE_NO, buttonItem.pageNo));
                }
                WebRouter.toPage(context, buttonItem.link);
            }
        });
        return btnView;
    }

    private void setBalloonTip(TextView btnView, OrderButtonData.ButtonItem buttonItem) {
        if (btnView == null || buttonItem == null || TextUtils.isEmpty(buttonItem.tips)) return;
        Context ctx = btnView.getContext();
        if (!(ctx instanceof ComponentActivity)) return;

        String key = getTipKey(buttonItem);
        if (key != null) {
            tipButtons.put(key, btnView);
            if (tipBalloons.containsKey(key)) {
                dismissTip(buttonItem);
            } else {
                tipBalloons.put(key, createBalloon(ctx, buttonItem, btnView));
            }
            showTipInScreen(btnView, buttonItem);
        }

    }

    private Balloon createBalloon(Context ctx, OrderButtonData.ButtonItem buttonItem, View btnView) {
        Balloon balloon = new Balloon.Builder(ctx)
                .setLifecycleOwner((ComponentActivity) ctx)
                .setWidth(BalloonSizeSpec.WRAP)
                .setHeight(BalloonSizeSpec.WRAP)
                .setBackgroundColorResource(R.color.color_tint_black_850)
                .setPaddingHorizontal(12)
                .setPaddingVertical(4)
                .setTextColorResource(R.color.color_surface_4_fg_default_idle)
                .setTextSize(11f)
                .setArrowSize(18)
                .setArrowPosition(0.5f)
                .setArrowTopPadding(8)
                .setCornerRadius(4f)
                .setBalloonAnimationStyle(0)
                .setDismissWhenTouchOutside(false)
                .setDismissWhenShowAgain(true)
                .setText(buttonItem.tips).build();
        return balloon;
    }

    private void showTipInScreen(TextView btnView, OrderButtonData.ButtonItem buttonItem) {
        btnView.post(() -> {
            if (isInScreen(btnView)) {
                showTip(buttonItem);
            }
        });
    }

    private boolean isInScreen(TextView btnView) {
        Rect rect = new Rect();
        boolean isVisible = btnView.getGlobalVisibleRect(rect);
        if (isVisible) {
            int top = rect.top;
            int bottom = rect.bottom;
            int screeHeight = getScreeHeight(btnView.getContext());

            if (top > 0 && bottom < screeHeight) {
                return true;
            }
        }
        return false;
    }

    private int getScreeHeight(Context context) {
        if (context == null) return 0;
        Rect displayRect = new Rect();
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && context.getDisplay() != null) {
            DisplayMetrics metrics = new DisplayMetrics();
            context.getDisplay().getRealMetrics(metrics);
            // defaultDisplay need plus status bar height
            return metrics.heightPixels;
        } else {
            windowManager.getDefaultDisplay().getRectSize(displayRect);
            return displayRect.bottom - displayRect.top;
        }
    }

    private String getTipKey(OrderButtonData.ButtonItem item) {
        if (item == null) return null;
        if (TextUtils.isEmpty(item.tips)) return null;
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(item.ids)) {
            builder.append(item.ids.get(0));
            builder.append("_");
        }
        builder.append(item.type);
        return builder.toString();

    }

    @Override
    public void onViewDetachedFromWindow(AdapterViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
    }

    private void initTipView(ProviderOrderButtonBinding binding, TextView btnView, String tips) {
        if (TextUtils.isEmpty(tips)) return;
        TextView tipView = new TextView(context);
        ConstraintLayout.LayoutParams params = new ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);

        params.startToStart = btnView.getId();
        params.endToEnd = btnView.getId();
        params.bottomToTop = btnView.getId();

//        tipView.setBackgroundResource(R.mipmap.bg_order_post_tip);

        tipView.setLayoutParams(params);
        tipView.setGravity(Gravity.CENTER);
        tipView.setText(tips);
        ViewTools.applyTextStyle(btnView, R.style.style_fluid_root_utility_xs);
        ViewTools.applyTextColor(btnView, R.color.color_surface_1_fg_default_idle);

//        binding.getRoot().setClipToOutline(false);
//        tipView.setTranslationY(30);
//        tipView.setTranslationY(30);

        binding.getRoot().addView(tipView);
    }

    private static String getValueByResName(Context context, String resName) {
        String value = resName;
        try {
            Resources resources = context.getResources();
            int resourceId = resources.getIdentifier(resName, "string", context.getPackageName());
            value = resources.getString(resourceId);
        } catch (Exception e) {
        }
        return value;
    }

    private static void trackClickAction(OrderButtonData.ButtonItem buttonItem) {
        try {
            String targetName = buttonItem.type;
            Map<String, Object> ctx = new HashMap<>();
            if (OrderButtonData.TYPE_POST.equalsIgnoreCase(targetName)) {
                targetName = "post_a_review";
                ctx.put("review_target", buttonItem.ids.get(0));
            } else {
                ctx.put("filters", buttonItem.ids.get(0));
            }

            EagleTrackManger.get().trackEagleClickAction(null,
                    -1,
                    null,
                    -1,
                    targetName,
                    -1,
                    EagleTrackEvent.TargetType.NORMAL_BUTTON,
                    EagleTrackEvent.ClickType.VIEW,
                    ctx);
        } catch (Exception e) {
        }
    }

    public void dismissTips() {
        for (Balloon balloon : tipBalloons.values()) {
            dismissTip(balloon);
        }
    }

    private void dismissTip(OrderButtonData.ButtonItem item) {
        String key = getTipKey(item);
        if (key != null) {
            dismissTip(tipBalloons.get(key));
        }
    }

    private void dismissTip(Balloon balloonTip) {
        if (balloonTip == null) return;
        if (balloonTip.isShowing()) {
            balloonTip.getContentView().post(() -> {
                balloonTip.dismiss();
            });
        }
    }

    public void showTips(OrderButtonData data) {
        if (data != null && data.items != null) {
            for (OrderButtonData.ButtonItem item : data.items) {
                showTip(item);
            }
        }
    }

    private void showTip(OrderButtonData.ButtonItem item) {
        String key = getTipKey(item);
        if (key != null) {
            TextView btnView = tipButtons.get(key);
            if (btnView == null) return;
            if (!isInScreen(btnView)) return;
            Balloon balloonTip = tipBalloons.get(key);
            if (balloonTip != null) {
                try {
                    int xOffset = 0;
                    int yOffset = CommonTools.dp2px(20);
                    if (!balloonTip.isShowing() && btnView.isAttachedToWindow()) {
                        balloonTip.showAlignTop(btnView, xOffset, yOffset);
                    }
                } catch (Exception ignore) {
                }
            }
        }
    }

    public void destroy() {
        tipButtons.clear();
        tipBalloons.clear();
    }

    public interface OnButtonClickListener {
        void onClick(OrderButtonData.ButtonItem buttonItem);
    }

}
