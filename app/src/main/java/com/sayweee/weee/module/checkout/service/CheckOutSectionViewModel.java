package com.sayweee.weee.module.checkout.service;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.bean.CheckOutPointsData;
import com.sayweee.weee.module.checkout.bean.CheckOutPointsTitleData;
import com.sayweee.weee.module.checkout.bean.CheckoutBean;
import com.sayweee.weee.module.checkout.bean.CheckoutCouponData;
import com.sayweee.weee.module.checkout.bean.CheckoutCurrentBenefitsData;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutOrderSummaryData;
import com.sayweee.weee.module.checkout.bean.CheckoutPaymentData;
import com.sayweee.weee.module.checkout.bean.CheckoutRemindTopData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CheckoutTermsData;
import com.sayweee.weee.module.checkout.bean.CheckoutTipData;
import com.sayweee.weee.module.checkout.bean.CheckoutVipData;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutTipInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.pay.PaymentHelper;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.reactivex.Observer;

/**
 * Author:  wld
 */
public class CheckOutSectionViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public static final String ORDER_EMPTY = "SO10012"; // 您的购物车暂时为空哦
    public static final String ORDER_FORCE_REFRESH = "SO10001"; // 结算数据异常，需要强制刷新
    public static final String ORDER_FORCE_REFRESH_2 = "SO10005"; // 结算数据异常，需要强制刷新，同上
    public static final String ORDER_ADDRESS_EMPTY = "SO90010"; // 地址信息为空
    public static final String ORDER_COUPON_ERROR = "SO90001"; // 优惠券信息异常
    public static final String ORDER_ADDRESS_ERROR = "SO90002"; // 地址信息错误
    public static final String ORDER_PHONE_ERROR = "SO90003"; // 联系方式错误
    public static final String ORDER_SHOW_ERROR = "SO90004"; // 接口通知前端显示错误信息并刷新数据
    public static final String ORDER_NOT_MAIL = "SO90113"; // 当前地址不在配送范围

    public static final String TAG_FORCE_REFRESH = "force"; //

    public static final int REFRESH_FLAG_DISABLE = -1; // 暂停自动刷新，需要手动取消
    public static final int REFRESH_FLAG_ENABLE = 0; // 启用自动刷新
    public static final int REFRESH_FLAG_DISABLE_ONCE = 1; // 暂停一次自动刷新
    public static final int REFRESH_FLAG_CITCON = 2; // 暂停自动刷新，需要手动取消

    public MutableLiveData<List<AdapterDataType>> adapterData = new MutableLiveData<>();
    public MutableLiveData<PreCheckoutV2Bean> responseData = new MutableLiveData<>();
    //结算数据
    public MutableLiveData<CheckoutBean> checkoutData = new MutableLiveData<>();
    public MutableLiveData<FailureBean> failureData = new MutableLiveData<>();
    public MutableLiveData<Integer> payLoadingData = new MutableLiveData<>();
    public MutableLiveData<String> payResultData = new MutableLiveData<>();
    public MutableLiveData<String> refreshData = new MutableLiveData<>();
    //Coupon列表数据
    public MutableLiveData<CouponBean> couponData = new MutableLiveData<>();
    public int couponSize;
    private String cartDomain;
    private String[] cartIds;
    private static final String PAYMENT_ROUTING = "control";

    public CheckOutSectionViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 准备结算V2
     */
    public void preCheckoutV2(boolean isSilent, String cartDomain, PreCheckoutTipInfoBean.OptionsBean optionsBean, String planId, String deliveryWindowIds) {
        this.cartDomain = cartDomain;
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", cartDomain)
                .putNonNull("tip_option", optionsBean)
                .putNonNull("plan_id", planId)
                .putNonNull("referral_id", AccountManager.get().getReferralId()) //跟卖信息， 如果是从跟卖跳转的要带这个参数
                .putNonNull("delivery_window_id", deliveryWindowIds)
                .putNonNull("payment_routing", PAYMENT_ROUTING);
        String preV2 = "/ec/so/order/checkout/pre/v2";
        String preCart = "/ec/so/order/checkout/pre/cart";//可以根据cart_id结算对应的购物车
        boolean usePreCart = cartIds != null && cartIds.length > 0;
        String api = usePreCart ? preCart : preV2;
        if (usePreCart) {
            requestParams.put("cart_ids", cartIds);
        }
        getLoader().getHttpService()
                .preCheckoutV2(api, requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(getPreCheckoutObserver(isSilent));
    }

    private Observer<ResponseBean<PreCheckoutV2Bean>> getPreCheckoutObserver(boolean isSilent) {
        return new ResponseObserver<ResponseBean<PreCheckoutV2Bean>>() {

            @Override
            public void onBegin() {
                if (!isSilent) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onResponse(ResponseBean<PreCheckoutV2Bean> response) {
                PreCheckoutV2Bean data = response.getData();
                handleCheckoutPreData(data);
            }

            @Override
            public void onError(FailureBean failure) {
                super.onError(failure);
                failureData.postValue(failure);
            }

            @Override
            public void onFinish() {
                if (!isSilent) {
                    setLoadingStatus(false);
                }
            }
        };
    }

    public void handleCheckoutPreData(PreCheckoutV2Bean data) {
        responseData.postValue(data);
        List<AdapterDataType> list = new ArrayList<>();
        if (!EmptyUtils.isEmpty(data.reminder_content_top)) {
            list.add(new CheckoutRemindTopData(data.reminder_content_top, CheckOutSectionAdapter.TYPE_REMIND_TOP));
        }
        list.add(new CheckoutDeliveryData(data.address_info, CheckOutSectionAdapter.TYPE_DELIVERY_INFORMATION));
        boolean saveRewardsChecked = false;
        boolean hasSaveRewards = !EmptyUtils.isEmpty(data.member_upgrade_plans);
        if (hasSaveRewards) {
            for (PreCheckoutV2Bean.MemberUpgradePlansBean bean : data.member_upgrade_plans) {
                if (bean.selected) {
                    saveRewardsChecked = true;
                    break;
                }
            }
        }
        if (data.payment_info != null) {
            list.add(new CheckoutPaymentData(data.payment_info, data.point_info, CheckOutSectionAdapter.TYPE_PAYMENT_METHOD).setSaveRewardsChecked(saveRewardsChecked));
        }

        // Review order
        if (!EmptyUtils.isEmpty(data.order_reviews)) {
            list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_REVIEW_ORDER));
            for (PreCheckoutOrderReviewsBean bean : data.order_reviews) {
                list.add(new CheckoutReviewOrderData(bean, CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST));
            }
        }

        if (EmptyUtils.isEmpty(data.coupon_info)) {
            getCoupons(cartDomain);
        }
        list.add(new CheckoutCouponData(data.coupon_info, CheckOutSectionAdapter.TYPE_APPLY_COUPON).setCouponSize(couponSize));
        boolean visible = data.tip_info != null && data.tip_info.show;
        if (visible) {
            visible = !EmptyUtils.isEmpty(data.tip_info.options);
            if (visible) {
                list.add(new CheckoutTipData(data.tip_info, CheckOutSectionAdapter.TYPE_DELIVERY_TIP));
            }
        }
        if (!EmptyUtils.isEmpty(data.order_summary)) {
            list.add(new CheckoutOrderSummaryData(data.order_summary, CheckOutSectionAdapter.TYPE_ORDER_SUMMARY).setFeeInfo(data.fee_info));
        }

        if (!EmptyUtils.isEmpty(data.current_benefits)) {
            list.add(new CheckoutCurrentBenefitsData(data.current_benefits, CheckOutSectionAdapter.TYPE_CURRENT_BENEFITS));
        }

        if (hasSaveRewards) {
            list.add(new CheckOutPointsTitleData(
                    CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS_TITLE,
                    data.member_upgrade_plan_title,
                    data.member_upgrade_plan_sub_title
            ));
            list.add(new CheckOutPointsData(
                    CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS,
                    data.member_upgrade_plans
            ));
        }

        if (!EmptyUtils.isEmpty(data.point_info) && !EmptyUtils.isEmpty(data.point_info.order_reward_points_desc_v2)) {
            list.add(new CheckoutVipData(data.point_info, CheckOutSectionAdapter.TYPE_VIP_MEMBER));
        }

        // Always fill terms layout.
        // Jira: https://sayweee.atlassian.net/browse/PEP-4048
        // Jira: https://sayweee.atlassian.net/browse/PCORE-2487
        int alcoholAgreementType = data.contain_alcohol
                ? Constants.AlcoholAgreementType.GROCERY
                : Constants.AlcoholAgreementType.NONE;
        list.add(new CheckoutTermsData(
                /* type= */CheckOutSectionAdapter.TYPE_TERMS,
                /* containFrozen= */data.contain_forzen,
                /* alcoholAgreementType= */alcoholAgreementType
        ));

        list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_PLACE));
        adapterData.postValue(list);
    }

    /**
     * 结算
     */
    public void checkout(CheckoutRequest request) {
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", request.cart_domain)
                .putNonNull("checkoutAmount", request.checkoutAmount) //配送信息
                .putNonNull("checkout_pre_id", request.checkout_pre_id) //暂不传参
                .putNonNull("cvv_token", request.cvv_token) //小费
                .putNonNull("tip_info", request.tipBean) //是否自提
                .putNonNull("referral_id", AccountManager.get().getReferralId()) //跟卖信息 暂不传参
                .putNonNull("braintree_device_data", request.deviceData) //braintree device data
                .putNonNull("plan_id", request.plan_id)
                .putNonNull("delivery_window_id", request.deliveryWindowIds);
        getLoader().getHttpService()
                .checkoutV3(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<CheckoutBean>>() {
                    @Override
                    public void onResponse(ResponseBean<CheckoutBean> response) {
                        checkoutData.postValue(response.getData());
                        OrderProvider.get().refreshSimplePreOrder();
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        failure.setObject(TAG_FORCE_REFRESH);
                        failureData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    /**
     * alipay payment
     *
     * @param isPayByCitcon 是否基于citcon支付
     * @param isCombinePay  是否为组合支付
     * @param orderIds      当前支付的订单id组合
     * @param amount        需要支付的总金额
     */
    public void execAlipay(boolean isPayByCitcon, boolean isCombinePay, List<Integer> orderIds, double amount) {
        if (orderIds == null || orderIds.isEmpty()) {
            return;
        }
        setLoadingStatus(true);
        payLoadingData.postValue(isPayByCitcon ? REFRESH_FLAG_CITCON : REFRESH_FLAG_DISABLE); //目前 2 citcon 3 stripe
        PaymentHelper.prepareAliPay(isPayByCitcon, isCombinePay, orderIds, amount, new PaymentHelper.OnPaymentEventCallback() {
            @Override
            public void onError(FailureBean failure) {
                setLoadingStatus(false);
                payLoadingData.postValue(REFRESH_FLAG_ENABLE);
                failureData.postValue(failure);
            }

            @Override
            public void onResult(boolean result, String url) {
                setLoadingStatus(false);
                if (!EmptyUtils.isEmpty(url)) {
                    payResultData.postValue(url);
                }
            }
        });
    }

    /**
     * wechat payment
     *
     * @param isPayByCitcon 是否基于citcon支付
     * @param isCombinePay  是否为组合支付
     * @param orderIds      当前支付的订单id组合
     * @param amount        需要支付的总金额
     */
    public void execWechatPay(boolean isPayByCitcon, boolean isCombinePay, List<Integer> orderIds, double amount) {
        if (orderIds == null || orderIds.isEmpty()) {
            return;
        }
        setLoadingStatus(true);
        payLoadingData.postValue(isPayByCitcon ? REFRESH_FLAG_CITCON : REFRESH_FLAG_DISABLE); //目前 2 citcon 3 stripe
        PaymentHelper.prepareWechatPayByCitcon(isCombinePay, orderIds, amount, new PaymentHelper.OnPaymentEventCallback() {
            @Override
            public void onError(FailureBean failure) {
                setLoadingStatus(false);
                payLoadingData.postValue(REFRESH_FLAG_ENABLE);
                failureData.postValue(failure);
            }

            @Override
            public void onResult(boolean result, String url) {
                setLoadingStatus(false);
                if (!EmptyUtils.isEmpty(url)) {
                    payResultData.postValue(url);
                }
            }
        });
    }

    public void usePoints(String payType, int profileId, boolean usePoints) {
        getLoader()
                .getHttpService()
                .changePayment(new RequestParams()
                        .put("payment_category", payType)
                        .putNonNull("profile_id", profileId)
                        .put("points", usePoints)
                        .get())
                .compose(DisposableTransformer.scheduler(this, true))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", "checkout");
                        EagleTrackManger.get().trackEagleInfoUpdate(EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                response.result,
                                "weee_points",
                                map);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showToast(failure.getMessage());
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", "checkout");
                        EagleTrackManger.get().trackEagleInfoUpdate(EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                false,
                                "weee_points",
                                map);
                    }

                    @Override
                    public void onFinish() {
                        refreshData.postValue("");
                        super.onFinish();
                    }
                });
    }

    public void getCoupons(String cartDomain) {
        getLoader()
                .getHttpService()
                .getCoupons(cartDomain)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<CouponBean>>() {

                    @Override
                    public void onResponse(ResponseBean<CouponBean> response) {
                        couponData.postValue(response.getData());
                    }
                });
    }

    public void showToast(String msg) {
        if (!TextUtils.isEmpty(msg)) {
            Toaster.showToast(msg);
        }
    }

    public boolean ensureDeliveryInfo(@NonNull PreCheckoutV2Bean preCheckoutV2Bean) {
        boolean hasAddress = preCheckoutV2Bean.address_info != null
                && !EmptyUtils.isEmpty(preCheckoutV2Bean.address_info.address);
        if (!hasAddress) {
            boolean requireAddress = CollectionUtils.any(preCheckoutV2Bean.order_reviews, orderReview -> {
                PreCheckoutOrderReviewsBean.ShippingInfoBean shippingInfo;
                shippingInfo = orderReview != null ? orderReview.shipping_info : null;
                String deliveryMode = shippingInfo != null ? shippingInfo.delivery_mode : null;
                return "delivery".equalsIgnoreCase(deliveryMode) || "shipping".equalsIgnoreCase(deliveryMode);
            });
            return !requireAddress;
        }
        return true;
    }

    public void setCartId(String cartId) {
        if (TextUtils.isEmpty(cartId)) return;
        cartIds = cartId.split(",");
    }

    public static class CheckoutRequest {
        public String cart_domain;
        public String checkoutAmount;
        public String checkout_pre_id;
        public String cvv_token;
        public PreCheckoutTipInfoBean.OptionsBean tipBean;
        public String deviceData;
        public String plan_id;
        public String deliveryWindowIds;
    }
}
