package com.sayweee.weee.module.launch.service;


import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.launch.bean.ConfigListBean;
import com.sayweee.weee.module.launch.bean.IpConvertBean;
import com.sayweee.weee.module.launch.bean.LanguageBean;
import com.sayweee.weee.module.launch.bean.StatesBean;
import com.sayweee.weee.module.launch.bean.UpgradeVersionBean;
import com.sayweee.weee.module.launch.bean.ZipcodeAreaBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;

import java.io.Serializable;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/16.
 * Desc:
 */
public interface LaunchApi {

    /**
     * 系统语言选择
     */
    @GET("/ec/customer/language/support_language/v1")
    Observable<ResponseBean<LanguageBean>> getSupportLanguage(@Query("auth_token_channel") String authTokenChannel);

    /**
     * APP版本检测接口
     */
    @GET("/ec/mkt/version/check")
    Observable<ResponseBean<UpgradeVersionBean>> updateCheck(@Query("platform") String platform, @Query("version") String version);

    /**
     * 购物车简要查询
     */
    @GET("/ec/so/porder/simple")
    Observable<ResponseBean<SimplePreOrderBean>> getSimplePreOrder();

    /**
     * 新建pre order
     */
    @POST("/ec/so/porder")
    Observable<ResponseBean<SimplePreOrderBean>> createPreOrder(@Body RequestBody body);

    /**
     * 订阅未开通地址提示信息
     */
    @POST("/ec/customer/email/subscribe")
    Observable<SimpleResponseBean> subscribe(@Body RequestBody body);

    @GET("/ec/content/config/getConfig")
    Observable<ResponseBean<ConfigListBean>> getConfig(@Query("configKeyList") String config);

    //根据ip获取zipcode
    @POST("ec/so/porder/ip2zipcode")
    Observable<ResponseBean<IpConvertBean>> getZipcodeByIp();

    @POST("ec/growth/push/open/event/app")
    Observable<SimpleResponseBean> logKlaviyoEvent(@Body Map<String, Serializable> params);

    @GET("/ec/customer/user/states")
    Observable<ResponseBean<StatesBean>> getUserStates(@Query("state_types") String stateTypes);

    @GET("/ec/growth/address/zipcode_area")
    Observable<ResponseBean<ZipcodeAreaBean>> getZipcodeArea(@Query("zipcode") String zipcode);
}
