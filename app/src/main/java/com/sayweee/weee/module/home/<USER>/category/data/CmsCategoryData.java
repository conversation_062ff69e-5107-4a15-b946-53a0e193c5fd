package com.sayweee.weee.module.home.provider.category.data;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.home.bean.CategoriesBean;
import com.sayweee.weee.module.home.bean.CategoriesProperty;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2023/5/30.
 * Desc:
 */
public class CmsCategoryData extends ComponentData<CategoriesBean, CategoriesProperty> implements ICache {

    private boolean gridStyle;

    public CmsCategoryData() {
        super(CmsItemType.CATEGORIES);
    }

    public CmsCategoryData(int type) {
        super(type);
    }

    @Override
    public boolean isValid() {
        if (t == null) {
            return false;
        }
        int categoryCount = CollectionUtils.size(t.category_list);
        if (displayGridStyle() || displayCapsuleStyle() || displayBarStyle()) {
            return categoryCount > 0;
        } else {
            return categoryCount >= 5;
        }
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        if (isValid()) {
            ArrayList<AdapterDataType> list = new ArrayList<>();
            list.add(this);
            if (displayGridStyle()) {
                list.add(new CmsBlankData());
                list.add(new CmsBlankData().setDarkMode(true));
            }
            list.add(new CmsBlankData(/* isTransparent= */true));
            return list;
        }
        return null;
    }

    public String getEventKey() {
        return property != null && property.event_key != null ? property.event_key : componentKey;
    }

    public boolean displayGridStyle() {
        return gridStyle || (property != null && property.displayGridStyle());
    }

    public boolean displayCapsuleStyle() {
        return property != null && property.displayCapsuleStyle();
    }

    public boolean displayBarStyle() {
        return property != null && property.displayBarStyle();
    }

    public void changeGridStyle(boolean gridStyle) {
        this.gridStyle = gridStyle;
    }

    public String getDisplayStyle() {
        return property != null && property.style != null ? property.style : "";
    }

    public boolean isAutoScroll() {
        return property != null && property.isAutoScroll();
    }

    public int getDisplaySpanCount() {
        String displayStyle = getDisplayStyle();
        if (CategoriesProperty.STYLE_CAPSULE_1.equals(displayStyle)
                || CategoriesProperty.STYLE_BAR_1.equals(displayStyle)) {
            return 1;
        }
        return 2;
    }
}
