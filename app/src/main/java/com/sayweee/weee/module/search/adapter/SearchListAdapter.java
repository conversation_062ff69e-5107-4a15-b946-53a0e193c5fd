package com.sayweee.weee.module.search.adapter;


import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.alibaba.fastjson.JSON;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cate.adapter.CateListAdapter;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.mkpl.LabelScrollAdapter;
import com.sayweee.weee.module.search.bean.BottomData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.spanner.DefaultURLClickListenerImpl;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.widget.tagflow.TagFlowLabelLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  ycy
 * 分类页面RV adapter
 * Desc:
 */
public class SearchListAdapter extends BaseQuickAdapter<Object, AdapterViewHolder> implements EagleImpressionAdapter, LabelScrollAdapter {

    public static final int TYPE_ADAPTER_SEARCH = 1;
    public static final int TYPE_ADAPTER_BRAND = 2;

    public int adapterType = TYPE_ADAPTER_SEARCH;

    private static final int TYPE_NORMAL_LIST = 3;//vertical list
    private static final int TYPE_X_SELL = 4;//x sell
    private static final int TYPE_HEADER = 1;//副标题header
    private static final int TYPE_BOTTOM = 6;

    private List<ProductBean> productList;
    private String traceId;
    private String category;
    //搜索页面搜索结果样式
    private String catalogueNum, sortString, filter, currentInput, pageTab, globalVendor;
    private String brandName;
    private boolean displayGridStyle;

    public SearchListAdapter() {
        super(null);
    }

    public void setAdapterType(int adapterType) {
        this.adapterType = adapterType;
    }

    public void setDisplayStyle(boolean displayGridStyle) {
        this.displayGridStyle = displayGridStyle;
    }

    /*排序+重置数据*/
    public void resetData(List<ProductBean> productList, String category, String brandKey) {
        this.category = category;
        this.productList = productList;
        List<Object> data = new ArrayList<>();
        for (ProductBean bean : productList) {
            String source = "app_brand-" + brandKey;//
            AdapterProductData item = new AdapterProductData(CateListAdapter.TYPE_NORMAL_PRODUCT, bean).setProductSource(source);
            data.add(item);
        }
        data.add(new BottomData());
        setNewData(data);
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder.getItemViewType() == TYPE_NORMAL_LIST || holder.getItemViewType() == TYPE_X_SELL) {
            if (displayGridStyle) {
                ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
                if (layoutParams instanceof StaggeredGridLayoutManager.LayoutParams) {
                    final int outerMargin = CommonTools.dp2px(10);
                    final int innerMargin = CommonTools.dp2px(2);
                    StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) layoutParams;
                    int spanIndex = params.getSpanIndex();
                    if (spanIndex == 0) {
                        params.leftMargin = outerMargin;
                        params.rightMargin = innerMargin;
                    } else {
                        params.leftMargin = innerMargin;
                        params.rightMargin = outerMargin;
                    }
                }
            } else {
                int interval = CommonTools.dp2px(8);
                int layoutPosition = holder.getLayoutPosition();
                ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
                if (layoutParams instanceof RecyclerView.LayoutParams) {
                    RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                    params.topMargin = layoutPosition == 0 ? 0 : interval;
                    params.bottomMargin = 0;
                    params.leftMargin = interval * 2;
                    params.rightMargin = interval * 2;
                }
            }
        } else if (holder.getItemViewType() == TYPE_BOTTOM) {
            ViewGroup.LayoutParams lp = holder.itemView.getLayoutParams();
            if (lp instanceof StaggeredGridLayoutManager.LayoutParams) {
                StaggeredGridLayoutManager.LayoutParams layoutParams = ((StaggeredGridLayoutManager.LayoutParams) lp);
                layoutParams.setFullSpan(true);
                holder.itemView.setLayoutParams(layoutParams);
            }
        }
    }

    @Override
    protected int getDefItemViewType(int position) {
        Object item = mData.get(position);
        if (item instanceof AdapterProductData) {
            return TYPE_NORMAL_LIST;
        } else if (item instanceof String) {
            return TYPE_HEADER;
        } else if (item instanceof BottomData) {
            return TYPE_BOTTOM;
        }
        return TYPE_NORMAL_LIST;
    }

    @Override
    protected AdapterViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case TYPE_HEADER://副标题 — title —
                return createBaseViewHolder(getItemView(R.layout.search_item_header, parent));
            case TYPE_NORMAL_LIST:
                if (displayGridStyle) {
                    return createBaseViewHolder(getItemView(R.layout.item_product_card, parent));
                } else {
                    return createBaseViewHolder(getItemView(R.layout.item_search_list, parent));
                }
            case TYPE_BOTTOM:
                return createBaseViewHolder(getItemView(R.layout.item_bottom_brand, parent));
        }
        return super.onCreateDefViewHolder(parent, viewType);
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, Object item) {
        switch (helper.getItemViewType()) {
            case TYPE_NORMAL_LIST:
                if (item instanceof AdapterProductData) {
                    convertProductList(helper, (AdapterProductData) item);
                }
                break;
            case TYPE_HEADER:
                if (item instanceof String) {
                    convertHeader(helper, (String) item);
                }
                break;
            default:
                break;
        }
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, Object item, @NonNull List<Object> payloads) {
        ProductSyncHelper.convertPayloads(helper, item, payloads);
        Object o = CollectionUtils.firstOrNull(payloads);
        if (o instanceof AdapterProductData) {
            TagFlowLabelLayout tagView = helper.getView(R.id.tfl_label);
            if (tagView != null) {
                tagView.startAutoScroll();
            }
        }
    }

    private void convertHeader(final BaseViewHolder helper, String searchTip) {
        //自动纠错
        if (!TextUtils.isEmpty(searchTip)) {
            HtmlTextView tv = helper.getView(R.id.tv_instead);
            tv.setVisibility(View.VISIBLE);
            tv.setMovementMethod(LinkMovementMethod.getInstance());
            ViewTools.setViewHtml(tv, searchTip, new DefaultURLClickListenerImpl<SearchListAdapter>(SearchListAdapter.this) {
                @Override
                public void onClick(@NonNull View view, String url) {
                    if (url.matches(Constants.UrlPattern.SEARCH)) {
                        SearchListAdapter adapter = getWeakObject();
                        Map<String, String> params = CommonTools.parseQueryParams(url);
                        if (!EmptyUtils.isEmpty(params.get("keyword")) && adapter != null && adapter.onForceSearchListener != null) {
                            adapter.onForceSearchListener.forceSearch(params.get("keyword"));
                        }
                    } else {
                        super.onClick(view, url);
                    }
                }
            });
        }
    }

    private void convertProductList(AdapterViewHolder helper, AdapterProductData item) {
        ProductBean bean = item.t;
        Map filterMap = null;
        try {
            filterMap = JSON.parseObject(filter);
        } catch (Exception ignored) {

        }
        if (filterMap != null) {
            filterMap.remove("catalogue_num");
        }
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, catalogueNum, sortString, filterMap, currentInput, pageTab, brandName, globalVendor, traceId);
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.ITEM_LIST)
                .setMod_pos(1).build()
                .getElement();
        bean.prod_pos = helper.getLayoutPosition();
        ProductView productView = helper.getView(R.id.layout_product_view);
        //搜索store不展示配送信息
        productView.setShowMkplVendor(EmptyUtils.isEmpty(globalVendor));
        productView.setTraceId(traceId);
        productView.setBackgroundResource(R.drawable.shape_corner_white_5);
        productView.setCollectClickCallback(new ProductView.OnCollectClickCallback() {
            @Override
            public void onCollectClick() {
                //收藏
                if (item.t.entrance_tag != null) {
                    ctx.put("tag_key", item.t.entrance_tag.tag_key);
                    ctx.put("tag_name", item.t.entrance_tag.tag_name);
                }
                boolean isCollect = CollectManager.get().isProductCollect(bean.id);
                EagleTrackManger.get().trackEagleClickAction("item_list",
                        1,
                        null,
                        -1,
                        String.valueOf(bean.id),
                        helper.getLayoutPosition(),
                        EagleTrackEvent.TargetType.PRODUCT,
                        isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE,
                        CollectionUtils.putAfterCopy(ctx, "volume_price_support", item.t.volume_price_support),
                        null,
                        bean.isSeller());
            }
        });
        productView.setAttachedProduct(bean, displayGridStyle ? ProductView.STYLE_CARD : ProductView.STYLE_LIST, new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                ctx.remove("tag_key");
                ctx.remove("tag_name");
                OpHelper.helperOp(layoutOp, bean, item, item.source, new OpLayout.OnOperateListener() {
                    @Override
                    public void operateLeft(View view) {
                    }

                    @Override
                    public void operateRight(View view) {

                    }
                }, element, ctx);
            }
        });
        helper.addOnClickListener(R.id.layout_product);
        helper.setOnClickListener(R.id.tv_status, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //12.9 已售罄状态不能取消提醒
                if (CollectManager.get().isProductCollect(bean.getProductId()) && ProductView.isSoldOut(bean.sold_status)) {
                    return;
                }
                productView.onStatusClick(v, bean, displayGridStyle ? ProductView.STYLE_CARD : ProductView.STYLE_LIST, new ProductView.OnStatusClickCallback() {
                    @Override
                    public void onStatusClick(String targetType, String clickType) {
                        if (item.t.entrance_tag != null) {
                            ctx.put("tag_key", item.t.entrance_tag.tag_key);
                            ctx.put("tag_name", item.t.entrance_tag.tag_name);
                        }
                        EagleTrackManger.get().trackEagleClickAction("item_list",
                                1,
                                null,
                                -1,
                                String.valueOf(bean.id),
                                helper.getLayoutPosition(),
                                targetType,
                                clickType,
                                CollectionUtils.putAfterCopy(ctx, "volume_price_support", item.t.volume_price_support),
                                null,
                                bean.isSeller());
                    }
                });
            }
        });
        helper.setOnViewClickListener(R.id.v_top_x_click_area, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (item.t.entrance_tag != null && !EmptyUtils.isEmpty(item.t.entrance_tag.more_link)) {
                    ctx.put("tag_key", item.t.entrance_tag.tag_key);
                    ctx.put("tag_name", item.t.entrance_tag.tag_name);
                    AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                            .setMod_nm("item_list")
                            .setMod_pos(1)
                            .setTargetNm(String.valueOf(bean.id))
                            .setTargetPos(helper.getLayoutPosition())
                            .setTargetType("chart")
                            .setClickType(EagleTrackEvent.ClickType.VIEW)
                            .addCtx(CollectionUtils.putAfterCopy(ctx, "volume_price_support", item.t.volume_price_support))
                            .build().getParams());
                }
                mContext.startActivity(WebViewActivity.getIntent(mContext, item.t.entrance_tag.more_link));
            }
        });
    }

    public static final String REACH_LIMIT = "reach_limit";
    public static final String CHANGE_OTHER_DAY = "change_other_day";
    public static final String SOLD_OUT = "sold_out";

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        if (DevConfig.isDebug()) {
            List<String> keys = new ArrayList<>();
            for (ImpressionBean item : list) {
                keys.add(item.getKey());

            }
            Logger.d(TAG, "getEagleImpressionData WEEE size:" + list.size() + " keys:" + keys);
        }
        return list;
    }

    private ImpressionBean getEagleImpressionEvent(Object item) {
        if (item instanceof AdapterProductData) {
            ProductBean bean = ((AdapterProductData) item).t;
            String productId = String.valueOf(bean.id);
            int pos = productList != null ? productList.indexOf(bean) : mData.indexOf(item);
            String key = pos + "_" + productId;

            Map filterMap = null;
            try {
                filterMap = JSON.parseObject(filter);
            } catch (Exception ignored) {

            }
            if (filterMap != null) {
                filterMap.remove("catalogue_num");
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, catalogueNum, sortString, filterMap, currentInput, pageTab, null, globalVendor, traceId);
            Map<String, Object> element = EagleTrackManger.get().getElement("item_list", 1, null, -1);
            Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_LIST).setTarget(bean, pos).setElement(element).setContext(ctx).get();

            if (bean.ads_creative != null) {
                boolean shouldSkipTrack = AdsManager.isTrackImpressionFlagSet(bean);
                if (!shouldSkipTrack) {
                    AdsManager.setTrackImpressionFlag(bean, true);
                    AdsManager.get().trackImpression(bean, pos, "weeeProductImpression");
                }
            }

            return new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key);
        }
        return null;
    }

    public void setCtxInfo(String catalogueNum, String sortString, String filter, String currentInput, String pageTab, String sellerId) {
        this.catalogueNum = catalogueNum;
        this.sortString = sortString;
        this.filter = filter;
        this.currentInput = currentInput;
        this.pageTab = pageTab;
        this.globalVendor = sellerId;
    }

    public void setBrand(String brandName) {
        this.brandName = brandName;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public interface OnForceSearchListener {
        void forceSearch(String keyword);
    }

    private OnForceSearchListener onForceSearchListener;

    public void setOnForceSearchListener(OnForceSearchListener listener) {
        onForceSearchListener = listener;
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        for (int i = start; i <= end; i++) {
            Object data = getItem(i);
            if (data instanceof AdapterProductData) {
                notifyItemChanged(i, data);
            }
        }
    }
}
