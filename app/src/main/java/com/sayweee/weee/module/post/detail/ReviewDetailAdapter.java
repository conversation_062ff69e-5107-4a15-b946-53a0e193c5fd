package com.sayweee.weee.module.post.detail;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.app.Dialog;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatRatingBar;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.convenientbanner.holder.CBViewHolderCreator;
import com.bigkoo.convenientbanner.holder.Holder;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cate.product.ImagePreviewActivity;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.detail.bean.CommentStatsData;
import com.sayweee.weee.module.post.detail.bean.PostBannerData;
import com.sayweee.weee.module.post.detail.bean.PostContentData;
import com.sayweee.weee.module.post.detail.bean.ReviewDetailBean;
import com.sayweee.weee.module.post.helper.FreeBieHelper;
import com.sayweee.weee.module.post.helper.SocialStatusHelper;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnBannerItemSafeClickListener;
import com.sayweee.weee.utils.listener.OnBannerPageChangeListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.PreciseBanner;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/7/3.
 * Desc:
 */
public class ReviewDetailAdapter extends CommentAdapter implements EagleImpressionAdapter {

    public static final int TYPE_BANNER = TYPE_LINE + 10;
    public static final int TYPE_CONTENT = TYPE_LINE + 20;
    public static final int TYPE_PRODUCT = TYPE_LINE + 30;
    public static final int TYPE_COMMENT_STATS = TYPE_LINE + 40;
    public static final int TYPE_COMMENT_EMPTY = TYPE_LINE + 50;
    public static final int TYPE_PLACE = TYPE_LINE + 70;
    private int lastBannerIndex;
    private int reviewId;

    public void setAdapterData(List<AdapterDataType> list) {
        setNewData(list);
    }

    @Override
    protected void registerAdapterType() {
        super.registerAdapterType();
        registerItemType(TYPE_BANNER, R.layout.item_review_detail_banner);
        registerItemType(TYPE_CONTENT, R.layout.item_review_detail_content);
        registerItemType(TYPE_PRODUCT, R.layout.item_review_detail_product);
        registerItemType(TYPE_COMMENT_STATS, R.layout.item_review_detail_comment_stats);
        registerItemType(TYPE_COMMENT_EMPTY, R.layout.item_review_detail_empty);
        registerItemType(TYPE_PLACE, R.layout.item_review_detail_place);
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (TYPE_PRODUCT == holder.getItemViewType()) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            int interval = CommonTools.dp2px(8);
            if (layoutParams instanceof RecyclerView.LayoutParams) {
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                params.topMargin = interval;
                params.leftMargin = CommonTools.dp2px(12);
                params.rightMargin = CommonTools.dp2px(15);
            }
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        switch (item.getType()) {
            case TYPE_BANNER:
                convertBanner(helper, (PostBannerData) item);
                break;
            case TYPE_CONTENT:
                convertContent(helper, (PostContentData) item);
                break;
            case TYPE_PRODUCT:
                convertProduct(helper, (AdapterProductData) item);
                break;
            case TYPE_COMMENT_STATS:
                convertStats(helper, (CommentStatsData) item);
                break;
            case TYPE_COMMENT_EMPTY:
                convertEmpty(helper);
                break;
            case TYPE_PLACE:

                break;
            default:
                super.convert(helper, item);
                break;
        }
    }

    private void convertBanner(AdapterViewHolder helper, PostBannerData item) {
        PreciseBanner banner = helper.getView(R.id.banner);
        ImageView ivLike = helper.getView(R.id.iv_like);
        LinearLayout layoutIndicator = helper.getView(R.id.indicator);
        banner.setBackgroundResource(R.mipmap.pic_banner_bg);
        helper.setText(R.id.tv_cmt, item.commentNum <= 0 ? mContext.getString(R.string.commented) : String.valueOf(item.commentNum));
        helper.setText(R.id.tv_like, item.likeNum <= 0 ? mContext.getString(R.string.like) : PostCollectManager.get().getLikeCountLabel(item.likeNum));
        ivLike.setImageResource(item.likeStatus ? R.mipmap.pic_black_bg_like : R.mipmap.pic_black_bg_unlike);
        List<String> banners = item.banner != null ? item.banner : new ArrayList<>();
        int count = banners.size();
        banner.setPages(new CBViewHolderCreator() {
            @Override
            public Holder<String> createHolder(View itemView) {
                return new Holder<String>(itemView) {

                    ImageView image;

                    @Override
                    protected void initView(View itemView) {
                        image = itemView.findViewById(R.id.iv_image);
                    }

                    @Override
                    public void updateUI(String data) {
                        ImageLoader.load(mContext, image, WebpManager.convert(ImageSpec.SPEC_375_AUTO, data), R.mipmap.pic_place_holder_new);
                    }
                };
            }

            @Override
            public int getLayoutId() {
                return R.layout.item_post_detail_images;
            }
        }, banners);
        banner.setCanLoop(count > 1);
        banner.setPointViewVisible(false);
        int position = lastBannerIndex >= count ? 0 : lastBannerIndex;
        if (count > 1) {
            banner.setCurrentItem(position, false);
            banner.setFirstItemPos(position);
//            bannerIndicator.setVisibility(View.VISIBLE);
//            bannerIndicator.setText(String.format(Locale.getDefault(), "%d/%d", position + 1, count));
            fillIndicator(layoutIndicator, true, count, position);
            banner.setPrecisePageChangeListener(new OnBannerPageChangeListener() {
                @Override
                public void onPageSelected(int index, boolean isAuto) {
                    fillIndicator(layoutIndicator, false, count, index);
                    lastBannerIndex = index;
                }
            });
        } else {
            layoutIndicator.setVisibility(View.INVISIBLE);
        }
        banner.setOnItemClickListener(new OnBannerItemSafeClickListener() {
            @Override
            public void onItemClickSafely(int position) {
                if (banners != null && banners.size() > position && mContext != null) {
                    mContext.startActivity(ImagePreviewActivity.getIntent(mContext, new ArrayList<>(banners), position));
                }
            }
        });

        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                switch (v.getId()) {
                    case R.id.layout_cmt:
                        //评论
                        if (listener instanceof OnPostPageEventListener) {
                            ((OnPostPageEventListener) listener).onCommentClick();
                        }
                        break;

                    case R.id.layout_like:
                        if (listener instanceof OnPostPageEventListener) {
                            if (AccountManager.get().isLogin()) {
                                item.likeStatus = !item.likeStatus;
                                item.likeNum = item.likeStatus ? item.likeNum + 1 : item.likeNum - 1;
                                ((OnPostPageEventListener) listener).onToggleCollect(item.likeStatus, item.likeNum);
                                ivLike.setImageResource(item.likeStatus ? R.mipmap.pic_black_bg_like : R.mipmap.pic_black_bg_unlike);
                                helper.setText(R.id.tv_like, item.likeNum <= 0 ? mContext.getString(R.string.like) : PostCollectManager.get().getLikeCountLabel(item.likeNum));
                            } else {
                                mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                            }
                        }
                        break;
                }
            }
        }, R.id.layout_like, R.id.layout_cmt);

    }

    private void fillIndicator(LinearLayout container, boolean init, int count, int index) {
        if (init) {
            container.removeAllViews();
            container.setGravity(Gravity.CENTER_VERTICAL);
            container.setPadding(CommonTools.dp2px(1), 0, CommonTools.dp2px(1), 0);
            for (int i = 0; i < count; i++) {
                ImageView pointView = new ImageView(container.getContext());
                pointView.setImageResource(i == index ? R.drawable.shape_circle_white : R.drawable.shape_circle_notchoose);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        CommonTools.dp2px(7f),
                        CommonTools.dp2px(7f)
                );
                layoutParams.setMargins(0, 0, CommonTools.dp2px(4), 0);
                pointView.setLayoutParams(layoutParams);
                container.addView(pointView);
            }
        } else {
            for (int i = 0; i < count; i++) {
                View view = container.getChildAt(i);
                if (view != null) {
                    ((ImageView) view).setImageResource(i == index ? R.drawable.shape_circle_white : R.drawable.shape_circle_notchoose);
                }
            }
        }
    }

    private void convertContent(AdapterViewHolder helper, PostContentData item) {
        // TYPE_CONTENT
        // R.layout.item_review_detail_content
        ReviewDetailBean bean = item.detail;
        helper.setVisibleCompat(R.id.rating_bar, bean.rating > 0);
        if (bean.rating > 0) {
            AppCompatRatingBar ratingBar = helper.getView(R.id.rating_bar);
            ratingBar.setNumStars(5);
            ratingBar.setRating(bean.rating);
            ratingBar.setIsIndicator(true);
        }
        boolean freeVisible = !EmptyUtils.isEmpty(bean.external_tags) && bean.external_tags.get(0).isFree();
        helper.setGone(R.id.tv_freebie, freeVisible);
        if (freeVisible) {
            FreeBieHelper.setViewStatus(mContext, helper.getView(R.id.tv_freebie), bean.external_tags.get(0));
        }
        ImageLoader.load(
                mContext,
                helper.getView(R.id.iv_header),
                WebpManager.get().getConvertUrl(SPEC_AVATAR, bean.user_avatar),
                R.mipmap.post_user_placeholder
        );
        LinearLayout ll = helper.getView(R.id.ll_name);
        TextView tv = helper.getView(R.id.tv_name);
        if (!EmptyUtils.isEmpty(bean.user_badge)) {
            ImageLoader.load(mContext, helper.getView(R.id.iv_badge), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, bean.user_badge));
        }
        ViewTreeObserver vt = helper.itemView.getViewTreeObserver();
        vt.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (ll.getWidth() > 0) {
                    helper.itemView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                    int maxWidth = ll.getWidth();
                    if (maxWidth > 0) {
                        if (!EmptyUtils.isEmpty(bean.user_badge)) {
                            maxWidth = maxWidth - CommonTools.dp2px(16);
                        }
                        tv.setMaxWidth(maxWidth);
                    }
                    helper.setText(R.id.tv_name, bean.user_name);
                }
            }
        });

        helper.setGone(R.id.ll_follow, VariantConfig.IS_USER_FOLLOW_BTN_ENABLED && item.detail.user_id != AccountManager.get().getUserIdInt());
        helper.setText(R.id.tv_date, DateUtils.formatReviewData(mContext, bean.rec_create_time));

        Spanny spanny = new Spanny();
        if (bean.featured == PostBean.VIDEO_FEATURED) {
            Drawable drawable = ContextCompat.getDrawable(mContext, R.mipmap.post_star);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(18), CommonTools.dp2px(18));
            }
            spanny.append("", new CenterImageSpan(drawable)).append(" ");
        }
        helper.setText(R.id.tv_translate, bean.useOrigin() ? R.string.detail_translate : R.string.detail_show_original);
        String content = bean.hasToggled() ? bean.comment : bean.comment_lang;
        String title = bean.hasToggled() ? bean.title : bean.title_lang;
        helper.setText(R.id.tv_content, content == null ? spanny : spanny.append(content));
        helper.setText(R.id.tv_title, title == null ? "" : title);
        helper.setVisibleCompat(R.id.tv_translate, bean.showTranslatePortal());
        helper.setVisibleCompat(R.id.ll_rate_translate, bean.showTranslatePortal());
        helper.setOnViewClickListener(R.id.tv_translate, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) { //点击翻译
                bean.toggleTranslateStatus();
                spanny.clear();
                if (bean.featured == 1) {
                    Drawable drawable = ContextCompat.getDrawable(mContext, R.mipmap.post_star);
                    if (drawable != null) {
                        drawable.setBounds(0, 0, CommonTools.dp2px(18), CommonTools.dp2px(18));
                    }
                    spanny.append("", new CenterImageSpan(drawable)).append(" ");
                }
                String content = bean.hasToggled() ? bean.comment : bean.comment_lang;
                String title = bean.hasToggled() ? bean.title : bean.title_lang;

                helper.setText(R.id.tv_title, title);
                helper.setText(R.id.tv_content, spanny.append(content));
                helper.setText(R.id.tv_translate, bean.useOrigin() ? R.string.detail_translate : R.string.detail_show_original);
            }
        });

        helper.setOnViewClickListener(R.id.tv_rate_translate, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (listener instanceof OnPostPageEventListener) {
                    ((OnPostPageEventListener) listener).onRateTranslation(item.detail);
                }
            }
        });
        // Followed / + Follow
        boolean isFollowing = bean.isFollowing();
        final ShapeTextView stvFollow = helper.getView(R.id.tv_follow);
        updateFollowing(bean.isFollowing(), stvFollow, bean, helper.getView(R.id.iv_add_follow));
        helper.setOnViewClickListener(R.id.ll_follow, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (AccountManager.get().isLogin()) {
                    if (bean.isFollowing()) {
                        new CompatDialog(mContext, CompatDialog.STYLE_VERTICAL)
                                .setUp(new OnDialogClickListener() {
                                           @Override
                                           public void onClick(WrapperDialog dialog, View view) {
                                               dialog.dismiss();
                                               // ##### OnPostPageEventListener
                                               if (listener instanceof OnPostPageEventListener) {
                                                   ((OnPostPageEventListener) listener).followClick(bean);
                                               }

                                               updateFollowing(!bean.isFollowing(), stvFollow, bean, helper.getView(R.id.iv_add_follow));

                                           }
                                       },
                                        mContext.getString(R.string.s_unfollow_confirm_title),
                                        mContext.getString(R.string.s_unfollow_confirm_confirm),
                                        mContext.getString(R.string.cancel))
                                .addHelperCallback(new WrapperDialog.HelperCallback() {
                                    @Override
                                    public void help(Dialog dialog, ViewHelper helper) {
                                        ((TextView) helper.getView(R.id.tv_content)).setTextSize(TypedValue.COMPLEX_UNIT_SP, 20);
                                    }
                                })
                                .show();
                    } else {
                        if (listener instanceof OnPostPageEventListener) {
                            ((OnPostPageEventListener) listener).followClick(bean);
                        }

                        updateFollowing(!bean.isFollowing(), stvFollow, bean, helper.getView(R.id.iv_add_follow));

                    }
                } else {
                    mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                }
            }
        });
        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (listener instanceof OnPostPageEventListener && VariantConfig.IS_REVIEW_TO_PROFILE_ENABLED) {
                    ((OnPostPageEventListener) listener).toProfileClick(item.detail.user_id, item.detail.uid);
                }
            }
        }, R.id.iv_header, R.id.tv_name);
    }

    private void convertProduct(AdapterViewHolder helper, AdapterProductData item) {
        ProductView productView = helper.getView(R.id.layout_product_view);
        productView.setBackgroundResource(R.drawable.shape_corner_white_5);
        ProductBean bean = item.t;
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(reviewId), null);
        productView.setCollectClickCallback(new ProductView.OnCollectClickCallback() {
            @Override
            public void onCollectClick() {
                boolean isCollect = CollectManager.get().isProductCollect(bean.id);
                EagleTrackManger.get().trackEagleClickAction(
                        String.valueOf(bean.id),
                        -1,
                        EagleTrackEvent.TargetType.PRODUCT,
                        isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE,
                        CollectionUtils.putAfterCopy(ctx, "volume_price_support", bean.volume_price_support),
                        bean.isSeller());
            }
        });
        productView.setAttachedProduct(bean, ProductView.STYLE_LIST, new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                OpHelper.helperOp(layoutOp, bean, item, item.source, Collections.emptyMap(), ctx);
            }
        });
        helper.addOnClickListener(R.id.layout_product);
    }

    private void convertStats(AdapterViewHolder helper, CommentStatsData data) {
        helper.setText(R.id.tv_stats, data.total > 0 ? data.total + " " + mContext.getString(R.string.comments) : mContext.getString(R.string.comment));
    }

    private void convertEmpty(AdapterViewHolder helper) {
        helper.itemView.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //评论
                if (listener instanceof OnPostPageEventListener) {
                    ((OnPostPageEventListener) listener).onCommentClick();
                }
            }
        });
    }

    private void updateFollowing(boolean isFollowing, ShapeTextView stvFollow, ReviewDetailBean bean, ImageView iv) {
        bean.social_status = isFollowing ? "Followed" : "unFollowed";
        SocialStatusHelper.showEnkiSocialStatus(bean.social_status, stvFollow, mContext, iv);
    }

    public int setCommentCount(String type, int count) {
        int total = 0;
        for (int i = 0; i < mData.size(); i++) {
            AdapterDataType item = mData.get(i);
            if (item instanceof CommentStatsData) {
                CommentStatsData data = (CommentStatsData) item;
                data.total = type.equalsIgnoreCase("comment") ? count :
                        type.equalsIgnoreCase("delete") ? data.total - 1 : data.total + 1;

                if (data.total <= 0) {
                    mData.add(new SimpleAdapterDataType(ReviewDetailAdapter.TYPE_COMMENT_EMPTY));
                }
                total = data.total;
            }
        }
        return total;
    }

    public void setReviewId(int reviewId) {
        this.reviewId = reviewId;
    }

    public interface OnPostPageEventListener extends OnPostEventListener {

        void onCommentClick();

        void specialClick(ProductBean bean);

        void collectClick(ProductBean bean);

        void productClick(ProductBean bean);

        void followClick(ReviewDetailBean bean);

        void toProfileClick(int userId, String uid);

        void onRateTranslation(ReviewDetailBean bean);

        void onToggleCollect(boolean likeStatus, int likeCount);

    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    public ImpressionBean getEagleImpressionEvent(AdapterDataType item) {
        if (item != null) {
            int type = item.getType();
            if (type == TYPE_PRODUCT && item instanceof AdapterProductData) {
                ProductBean bean = ((AdapterProductData) item).t;
                String key = String.valueOf(bean.id);
                Map<String, Object> element = EagleTrackManger.get().getElement(null, -1, null, -1);
                Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_LIST).setTarget(bean, 0).setElement(element).get();
                return new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key);
            }
        }
        return null;
    }
}
