package com.sayweee.weee.module.cate.service;

import com.sayweee.weee.module.cart.bean.FilterProductListBean;
import com.sayweee.weee.module.cart.bean.ProductListBean;
import com.sayweee.weee.module.cate.bean.CateBean;
import com.sayweee.weee.module.cate.bean.MkplFilterProductListBean;
import com.sayweee.weee.module.cate.bean.PurchaseBean;
import com.sayweee.weee.module.cate.bean.RelatedProductListBean;
import com.sayweee.wrapper.bean.ResponseBean;

import java.util.Map;

import io.reactivex.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;

/**
 * Author:  ycy
 * Category
 */
public interface CateApi {

    //获取分类列表
    String API_CATEGORY = "/ec/item/v5/categories";

    @GET(API_CATEGORY)
    Observable<ResponseBean<CateBean>> getCate();

    //商品搜索-分类&search
    String API_CATEGORY_PRODUCTS = "/ec/item/search";

    @GET(API_CATEGORY_PRODUCTS)
    Observable<ResponseBean<ProductListBean>> getCateProducts(@Query("filter_sub_category") String filter_sub_category);

    //商品增删改(暂未使用)
    String API_ORDER_CHANGE = "/ec/so/porder/items";

    @PUT(API_ORDER_CHANGE)
    Observable<ResponseBean<PurchaseBean>> putProducts(@Body RequestBody body);

    //category關聯商品
    String API_RELATED_PRODUCTS = "/ec/item/items/{product_id}/related";

    @GET(API_RELATED_PRODUCTS)
    Observable<ResponseBean<RelatedProductListBean>> getRelatedProducts(@Path("product_id") String product_id);

    /**********************************************************************************************/

    //菜系大分类查询
    String API_CUISINES = "/ec/item/cuisines/category";

    @GET(API_CUISINES)
    Observable<ResponseBean<CateBean>> getCuisines();

    //菜系商品list搜索
    String API_CUISINE_PRODUCTS = "/ec/item/v2/search/cuisine_filter";

    @GET(API_CUISINE_PRODUCTS)
    Observable<ResponseBean<FilterProductListBean>> getCuisineProducts(@QueryMap Map<String, String> map);

    // 新版对接分类的接口
    String API_CATEGORY_PRODUCTS_NEW = "/ec/item/v3/search/catalogue";

    @GET(API_CATEGORY_PRODUCTS_NEW)
    Observable<ResponseBean<FilterProductListBean>> filterCateProducts(@QueryMap Map<String, String> map);

    /*****************************************新样式接口********************************************/

    @GET
    Observable<ResponseBean<MkplFilterProductListBean>> filterProducts(@Url String url, @QueryMap Map<String, String> map);
}
