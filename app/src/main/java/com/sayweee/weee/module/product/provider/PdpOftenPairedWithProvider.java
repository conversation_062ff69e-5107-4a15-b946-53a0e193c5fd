package com.sayweee.weee.module.product.provider;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductUpdateBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.mkpl.TrackingInfoAdapter;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.data.PdpProductsData;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.widget.shape.ShapeConstraintLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PdpOftenPairedWithProvider extends SimpleSectionProvider<PdpProductsData, AdapterViewHolder>
        implements ImpressionProvider<PdpProductsData>, TrackingInfoAdapter {

    String pageTarget;
    protected final EagleImpressionTrackerIml tracker = new EagleImpressionTrackerIml();
    private ShapeConstraintLayout layoutBtnAddRef; // 记录按钮视图

    @Override
    public int getItemType() {
        return PdpItemType.OFTEN_PAIRED_WITH;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_often_pair_with;
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, PdpProductsData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        Object object = CollectionUtils.firstOrNull(payloads);
        if (object instanceof String) {
            String payload = (String) object;
            for (int i = 0; i < item.t.product_list.size(); i++) {
                ProductBean bean = item.t.product_list.get(i);
                ProductView productView = getProductViewAt(helper, i);
                if (productView == null) {
                    continue;
                }
                if (PayloadKey.CART_QTY.equalsIgnoreCase(payload)) {
                    CartOpLayout view = productView.findViewById(R.id.layout_op);
                    ProductSyncHelper.helperCartOp(view, new AdapterProductData(bean));
                } else if (PayloadKey.COLLECT.equalsIgnoreCase(payload)) {
                    ImageView ivCollect = productView.findViewById(R.id.iv_collect);
                    ProductViewHelper.setProductCollect(ivCollect, CollectManager.get().isProductCollect(bean.id));
                }
            }
            covertBtn(helper, item);
        } else if (ProductTraceViewHelper.shouldConvertPayload(object) && item.t != null && !EmptyUtils.isEmpty(item.t.product_list)) {
            for (int i = 0; i < item.t.product_list.size(); i++) {
                ProductBean bean = item.t.product_list.get(i);
                ProductView productView = getProductViewAt(helper, i);
                convertProductTrace(productView, bean, item.modNm);
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpProductsData item) {
        helper.setText(R.id.tv_title, item.t.title);
        ViewTools.applyTextStyleAndColor(helper.getView(R.id.tv_title), R.style.style_body_base_medium, R.color.color_surface_100_fg_default);
        List<ProductBean> list = item.t.product_list;
        convertProduct(helper, helper.getView(R.id.layout_product_a), list.get(0), item, 0);
        convertProduct(helper, helper.getView(R.id.layout_product_b), list.get(1), item, 1);
        convertProduct(helper, helper.getView(R.id.layout_product_c), list.get(2), item, 2);
        covertBtn(helper, item);
    }

    private void covertBtn(AdapterViewHolder helper, PdpProductsData item) {
        List<ProductBean> list = item.t.product_list;
        Map<String, Object> ctx = new ArrayMap<>();
        ctx.put(EagleTrackEvent.Ctx.PAGE_TARGET, pageTarget);
        ctx.put("trace_id", item.traceId);
        ShapeConstraintLayout layoutBtnAdd = helper.getView(R.id.layout_btn_add);
        layoutBtnAddRef = layoutBtnAdd;
        ImageView ivBtn = helper.getView(R.id.iv_btn);
        double amount = 0;
        int remainQty = 0;
        // 循环处理产品（最多3个）
        int maxProducts = Math.min(list.size(), 3); // 防止越界
        for (int i = 0; i < maxProducts; i++) {
            ProductBean product = list.get(i);
            SimplePreOrderBean.ItemsBean orderItem = OrderManager.get().getSimpleOrderItem(
                    product.getProductId(), product.getProductKey()
            );
            if (orderItem == null) {
                double productTotal = DecimalTools.multiply(product.price, product.getOrderMinQuantity());
                amount = DecimalTools.add(amount, productTotal);
                remainQty++;
            }
        }
        String s = "";
        if (remainQty == 3) {
            s = context.getString(R.string.s_add_remaining_three);
        } else if (remainQty == 0) {
            s = context.getString(R.string.s_added_all);
        } else {
            if (remainQty == 1) {
                s = String.format(context.getString(R.string.s_add_remaining_one), remainQty);
            } else {
                s = String.format(context.getString(R.string.s_add_remaining), remainQty);
            }
        }
        helper.setText(R.id.tv_btn, s);
        ViewTools.applyTextColor(helper.getView(R.id.tv_btn), remainQty > 0 ? R.color.color_surface_1_fg_default_idle : R.color.brand_color_primary_1);
        //helper.setVisibleCompat(R.id.btn_shadow, remainQty > 0);
        helper.getView(R.id.btn_shadow).setVisibility(remainQty > 0 ? View.VISIBLE : View.INVISIBLE);
        helper.setVisibleCompat(R.id.tv_price_btn, remainQty > 0);
        helper.setText(R.id.tv_price_btn, OrderHelper.formatUSMoney(amount));
        ivBtn.setImageResource(remainQty > 0 ? R.mipmap.ic_cart_add : R.mipmap.check_green);
        layoutBtnAdd.setBackgroundStrokeDrawable(ContextCompat.getColor(context, R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(remainQty > 0 ? 0 : 1), CommonTools.dp2px(26));
        int finalRemainQty = remainQty;
        helper.setOnViewClickListener(R.id.layout_btn_add, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (finalRemainQty == 0) {
                    return;
                }
                List<ProductUpdateBean> temp = new ArrayList<>();
                for (ProductBean bean : item.t.product_list) {
                    SimplePreOrderBean.ItemsBean itemsBean = OrderManager.get().getSimpleOrderItem(bean.getProductId(), bean.getProductKey());
                    if (itemsBean == null) {
                        int num = OrderHelper.editNum(true, 0, bean.min_order_quantity, bean.getOrderMaxQuantity(), bean.getVolumeThreshold());
                        ProductUpdateBean updateBean = new ProductUpdateBean();
                        updateBean.product_id = bean.id;
                        updateBean.quantity = num;
                        updateBean.source = WeeeEvent.RELATED_PDP_SOURCE + bean.id;
                        updateBean.refer_type = bean.getProductType();
                        updateBean.refer_value = bean.getReferValue();
                        updateBean.source_store = StoreManager.get().getStoreKey();
                        updateBean.product_key = bean.getProductKey();
                        temp.add(updateBean);
                    }
                }
                if (!EmptyUtils.isEmpty(temp)) {
                    OrderManager.get().setProductChanged(temp);
                    //convert(helper, item);//一件全部加购
                    for (int i = 0; i < item.t.product_list.size(); i++) {
                        ProductBean bean = item.t.product_list.get(i);
                        ProductView productView;
                        if (i == 0) {
                            productView = helper.getView(R.id.layout_product_a);
                        } else if (i == 1) {
                            productView = helper.getView(R.id.layout_product_b);
                        } else {
                            productView = helper.getView(R.id.layout_product_c);
                        }
                        CartOpLayout view = productView.findViewById(R.id.layout_op);
                        ProductSyncHelper.helperCartOp(view, new AdapterProductData(bean));
                        covertBtn(helper, item);
                    }
                }
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(item.modNm)
                        .setMod_pos(item.modPos)
                        .setTargetNm("combine_add")
                        .setTargetPos(-1)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.NORMAL)
                        .addCtx(ctx)
                        .build().getParams());
            }
        });
    }

    private void convertProduct(AdapterViewHolder helper, ProductView view, ProductBean productBean, PdpProductsData item, int i) {
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(item.modNm)
                .setMod_pos(item.modPos)
                .build()
                .getElement();
        Map<String, Object> ctx = new ArrayMap<>();
        ctx.put(EagleTrackEvent.Ctx.PAGE_TARGET, pageTarget);
        ctx.put("trace_id", item.traceId);
        view.setCollectClickCallback(new ProductView.OnCollectClickCallback() {
            @Override
            public void onCollectClick() {
                boolean isCollect = CollectManager.get().isProductCollect(productBean.id);
                EagleTrackManger.get().trackEagleClickAction(item.modNm,
                        item.modPos,
                        null,
                        -1,
                        String.valueOf(productBean.id),
                        i,
                        EagleTrackEvent.TargetType.PRODUCT,
                        isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE,
                        CollectionUtils.putAfterCopy(ctx, "volume_price_support", productBean.volume_price_support));
            }
        });
        view.setPdpClickCallback(new ProductView.OnPdpClickCallback() {
            @Override
            public void onPdpClick() {
                //pdp click action
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(item.modNm)
                        .setMod_pos(item.modPos)
                        .setTargetNm(String.valueOf(productBean.id))
                        .setTargetPos(i)
                        .setTargetType("product")
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .addCtx(CollectionUtils.putAfterCopy(ctx, "volume_price_support", productBean.volume_price_support))
                        .build().getParams());
                ProductHelper.toProductDetail(context, productBean);
            }
        });
        view.setAttachedProduct(productBean, ProductView.STYLE_ITEM_NORMAL, new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                OpHelper.helperOp(layoutOp, bean, new AdapterProductData(bean), "app_product-" + item.modNm, new OpLayout.OnOperateListener() {
                    @Override
                    public void operateLeft(View view) {
                        covertBtn(helper, item);
                    }

                    @Override
                    public void operateRight(View view) {
                        covertBtn(helper, item);
                    }
                }, element, ctx);
            }
        });
        CartOpLayout layoutOp = view.getCartOpLayout();
        if (layoutOp != null) {
            layoutOp.setTipsMaxWidth(CommonTools.dp2px(100));
        }

        convertProductTrace(view, productBean, item.modNm);
    }

    @Nullable
    private ProductView getProductViewAt(AdapterViewHolder helper, int index) {
        if (index == 0) {
            return helper.getView(R.id.layout_product_a);
        } else if (index == 1) {
            return helper.getView(R.id.layout_product_b);
        } else if (index == 2) {
            return helper.getView(R.id.layout_product_c);
        } else {
            return null;
        }
    }

    private void convertProductTrace(@Nullable View productView, ProductBean productBean, String modNm) {
        if (productView == null || productBean == null) {
            return;
        }
        if (productView.getParent() instanceof ViewGroup) {
            ProductTraceViewHelper.convert(
                    (ViewGroup) productView.getParent(),
                    ProductTraceKey.of(productBean.id, productBean.recommendation_trace_id, ProductTraceKey.generateUniqueKey(modNm, null))
            );
        }
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(PdpProductsData item, int position) {
        List<ImpressionBean> list = new ArrayList<>();
        if (item.t.isValid()) {
            Map<String, Object> ctx = new ArrayMap<>();
            ctx.put(EagleTrackEvent.Ctx.PAGE_TARGET, pageTarget);
            ctx.put("trace_id", item.traceId);
            for (ProductBean bean : item.t.product_list) {
                String productId = String.valueOf(bean.id);
                String key = position + item.modNm + productId;
                Map<String, Object> element = EagleTrackManger.get().getElement(item.modNm, item.modPos, null, -1);
                Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_ITEM_SMALL).setTarget(bean, item.t.product_list.indexOf(bean)).setElement(element).setContext(ctx).get();
                list.add(new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key));
            }
            if (isViewVisibleInScreen(layoutBtnAddRef)) {
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(item.modNm)
                        .setMod_pos(item.modPos)
                        .setSec_nm("combine_add")
                        .addCtx(ctx)
                        .build().getParams();
                list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, item.modNm + "combine_add" + position));
            }
        }
        return list;
    }

    private boolean isViewVisibleInScreen(View view) {
        if (view == null || view.getVisibility() != View.VISIBLE) return false;
        Rect rect = new Rect();
        boolean isVisible = view.getGlobalVisibleRect(rect);
        return isVisible && rect.height() > 0 && rect.width() > 0;
    }

    @Override
    public void onCtxAdded(String filterSubCategory, String catalogueNum, String sort, Map<String, String> filters, String pageTarget, String pageTab, String globalVendor) {
        this.pageTarget = pageTarget;
    }
}
