package com.sayweee.weee.module.preload;

import androidx.annotation.NonNull;

import com.sayweee.logger.Logger;
import com.sayweee.preload.PreloadInGroup;
import com.sayweee.preload.PreloadTask;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.util.Map;

//
// Created by <PERSON><PERSON> on 9/4/24.
// Copyright (c) 2024 Weee LLC. All rights reserved.
//
public class PreloadProductMiddleBannerTask implements PreloadTask<PdpMiddleBannerBean>, PreloadInGroup {

    public static final String KEY_PDP_MIDDLE_BANNER = "pdp_middle_banner";

    ProductPageParams productParams;

    public PreloadProductMiddleBannerTask(ProductPageParams params) {
        this.productParams = params;
    }

    @Override
    public PdpMiddleBannerBean loadData() {
        try {
            int productId = productParams.productId;
            if (productId < 1 && productParams.product != null) {
                productId = productParams.product.id;
            }
            //104042
            Map<String, Serializable> params = new RequestParams().put("slot", "pdpMiddleCarouselBanner").putNonNull("searchTerm", productId).putNonNull("fromPage", "pdp").get();
            return RetrofitIml.get().getHttpService(PreloadApi.class)
                    .getBannerInfo(params).execute().body().getData();
        } catch (Exception e) {
            Logger.e("pdp_preload", KEY_PDP_MIDDLE_BANNER + e.getMessage());
            return null;
        }
    }

    @NonNull
    @Override
    public String keyInGroup() {
        return KEY_PDP_MIDDLE_BANNER;
    }
}
