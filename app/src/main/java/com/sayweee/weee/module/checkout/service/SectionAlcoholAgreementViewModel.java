package com.sayweee.weee.module.checkout.service;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.http.support.RequestParams;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/3/12.
 * Desc:
 */
public class SectionAlcoholAgreementViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public MutableLiveData<Boolean> recordAlcoholResultData = new MutableLiveData<>();

    public SectionAlcoholAgreementViewModel(@NonNull Application application) {
        super(application);
    }

    public void recordAlcoholAgreement(@Nullable String type) {
        RequestParams requestParams = new RequestParams();
        String referType = !EmptyUtils.isEmpty(type) ? type : "normal";
        requestParams.putNonNull("refer_type", referType);
        getLoader().getHttpService()
                .recordAlcohol(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        recordAlcoholResultData.postValue(true);
                    }
                });
    }

}
