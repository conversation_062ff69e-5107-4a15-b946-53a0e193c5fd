package com.sayweee.weee.module.post.widget;

import android.app.Activity;
import android.content.Context;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.account.helper.OnValidationListener;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.DefaultTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.SimpleTextWatcher;
import com.sayweee.wrapper.utils.KeyboardUtils;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Author:  Chuan
 * Email:   <EMAIL>
 * Date:    2022/6/5
 * UpdateDate:
 * Desc:
 */
public class SearchTermView extends FrameLayout {
    private ImageView ivDelete;
    private EditText etInput;
    private Context context;
    private boolean listenForChanges = true;//防setText方法干扰监听输入
    private SearchTermListener searchTermListener;
    private String hint;
    private boolean editOnClearButtonClick;
    private int etInputBackgroundRes = R.drawable.selector_bg_input_search_enki;

    public interface SearchTermListener {
        void onEtInputClick();

        void goSearch(String words);

        void goSearch(String words,boolean force);

        void onDelete();

        void onBack();

        void onEtEmptyObserver();

        void getSuggestions(String words);
    }

    public SearchTermView(@NonNull Context context) {
        this(context, null);
    }

    public SearchTermView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SearchTermView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    public void setSearchTermListener(SearchTermListener searchTermListener) {
        this.searchTermListener = searchTermListener;
    }

    public void setEtText(String words) {
        if (TextUtils.isEmpty(words)) {
            etInput.setText("");
        } else {
            listenForChanges = false;
            etInput.setText(words);
            //etInput.setSelection(keyWord.length());//光标定位到最后
            setEditState(etInput, false);
            listenForChanges = true;
        }
    }

    public void autoFillEtText(String words) {
        if (!TextUtils.isEmpty(words)) {
            etInput.setText(words);
            setEditState(etInput, true);
            listenForChanges = true;
            etInput.setSelection(words.length());
        }
    }

    public void click(View view) {
        switch (view.getId()) {
            case R.id.iv_delete:
                etInput.setText("");
                if (searchTermListener != null) searchTermListener.onDelete();
                if (editOnClearButtonClick)
                {
                    setEditState(etInput, true);
                    listenForChanges = true;
                }
                break;
            case R.id.iv_back:
                if (searchTermListener != null) searchTermListener.onBack();
                break;
        }
    }

    public EditText getEtText() {
        return etInput;
    }

    @Nullable
    public String getEtTextContent() {
        Editable editable = etInput.getText();
        return editable != null ? editable.toString() : null;
    }

    public ImageView getIvDelete() {
        return ivDelete;
    }

    private void initView(Context context) {
        this.context = context;
        inflate(getContext(), R.layout.view_search_term, this);
        etInput = findViewById(R.id.et_input);
        ivDelete = findViewById(R.id.iv_delete);
        ivDelete.setOnClickListener(this::click);
        findViewById(R.id.iv_back).setOnClickListener(this::click);
        etInput.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                setEditState(etInput, true);
                if (searchTermListener != null) searchTermListener.onEtInputClick();
            }
        });
        setEditorActionListener(etInput);
        setInputAttachWatcher(ivDelete, etInput);
        setEditObserver(etInput, ivDelete, new OnValidationListener() {
            @Override
            public void onValidate(String content) {

            }
        });
        //进入search 页面默认样式
        setEditState(etInput, true);
    }

    public void setSelectAllOnFocus(boolean selectAllOnFocus) {
        etInput.setSelectAllOnFocus(selectAllOnFocus);
    }

    public void setEditOnClearButtonClick(boolean editOnClearButtonClick) {
        this.editOnClearButtonClick = editOnClearButtonClick;
    }

    public void enableDelayedChangeNotification(int delay) {
        this.delayedChangeNotificationDuration = delay;
    }

    public void diableDelayedChangeNotification() {
        this.delayedChangeNotificationDuration = null;
    }

    public void setEtInputBackgroundRes(@DrawableRes int res) {
        etInputBackgroundRes = res;
        etInput.setBackground(ResourcesCompat.getDrawable(getResources(), etInputBackgroundRes, null));
    }

    private void setEditorActionListener(EditText etInput) {
        etInput.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            private long lastActionTime;
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                long time = SystemClock.uptimeMillis();
                if (time - lastActionTime <= 400) {
                    return false; // 防止快速点击
                }
                lastActionTime = time;
                if (actionId == EditorInfo.IME_ACTION_SEARCH || (DevConfig.isEmulator() && DevConfig.isDebug() && actionId == EditorInfo.IME_ACTION_UNSPECIFIED)) {
                    String keyWord = v.getText().toString();
                    if (TextUtils.isEmpty(keyWord) && !EmptyUtils.isEmpty(hint)) {
                        keyWord = hint;
                    }
                    if (!TextUtils.isEmpty(keyWord)) {
                        if (searchTermListener != null) searchTermListener.goSearch(keyWord);
                        //键盘搜索按钮tracking
                        EagleTrackManger.get().trackEagleClickAction("search_bar",
                                0,
                                null,
                                -1,
                                keyWord,
                                -1,
                                EagleTrackEvent.TargetType.SEARCH_TARGET,
                                EagleTrackEvent.ClickType.VIEW);
                    }
                }
                return false;
            }
        });
    }

    public void setEditState() {
        setEditState(etInput, true);
    }
    private void setEditState(EditText editText, boolean isEdit) {
        editText.setFocusable(isEdit);
        editText.setFocusableInTouchMode(isEdit);
        editText.setCursorVisible(isEdit);
        if (isEdit) editText.requestFocus();
    }

    public boolean isSearchActive() {
        return etInput.isFocusable();
    }

    static final long DURATION = 1000;// 规定有效时间

    long requestTiming = 0;
    Integer delayedChangeNotificationDuration;
    Timer delayedChangeNotificationTimer;
    TimerTask delayedChangeNotificationTask;

    public void release() {
        if (this.delayedChangeNotificationTimer != null) {
            this.delayedChangeNotificationTimer.cancel();
            this.delayedChangeNotificationTimer.purge();
            this.delayedChangeNotificationTimer = null;
            this.delayedChangeNotificationTask = null;
        }
    }

    private void setInputAttachWatcher(final View delete, TextView view) {
        if (delete != null && view != null) {
            view.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    delete.setVisibility((s != null && s.length() > 0) ? View.VISIBLE : View.GONE);
                    if (listenForChanges && searchTermListener != null) {
                        if (s != null && s.length() > 0) {
                            final String searchTerm = s.toString();
                            if (delayedChangeNotificationDuration != null) {
                                if (delayedChangeNotificationDuration > 0) {
                                    if (delayedChangeNotificationTimer == null) {
                                        delayedChangeNotificationTimer = new Timer();
                                    }
                                    if (delayedChangeNotificationTask != null) {
                                        delayedChangeNotificationTask.cancel();
                                    }
                                    delayedChangeNotificationTask = new TimerTask() {
                                        @Override
                                        public void run() {
                                            try {
                                                if (getContext() != null) {
                                                    if (isSearchActive()) {
                                                        searchTermListener.getSuggestions(searchTerm);
                                                    }
                                                    delayedChangeNotificationTask = null;
                                                    delayedChangeNotificationTimer.purge();
                                                }
                                            } catch (Throwable e) {
                                                if (DevConfig.isDebug()) Logger.e(e);
                                                throw e;
                                            }
                                        }
                                    };
                                    delayedChangeNotificationTimer.schedule(delayedChangeNotificationTask, delayedChangeNotificationDuration);
                                } else if (isSearchActive()) {
                                    searchTermListener.getSuggestions(searchTerm);
                                }
                            } else {
                                if (System.currentTimeMillis() - requestTiming > DURATION) {
                                    searchTermListener.getSuggestions(searchTerm);
                                    requestTiming = System.currentTimeMillis();
                                }
                            }
                        } else {
                            searchTermListener.onEtEmptyObserver();
                        }
                    }
                }
            });
        }
    }

    private void setEditObserver(final TextView view, final ImageView delete, final OnValidationListener listener) {
        view.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                String text = DefaultTools.getText(view);
                if (hasFocus) {
                    etInput.setBackground(ResourcesCompat.getDrawable(getResources(), etInputBackgroundRes, null));
                    if (TextUtils.isEmpty(text)) {
                        delete.setVisibility(View.GONE);
                    } else {
                        delete.setVisibility(View.VISIBLE);
                        if (searchTermListener != null) searchTermListener.getSuggestions(text);
                    }
                } else {
                    //delete.setVisibility(View.GONE);
                    if (TextUtils.isEmpty(text)) {
                        view.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                view.setHint(!EmptyUtils.isEmpty(hint) ? hint : context.getString(R.string.s_what_do_you_want_today));
                            }
                        }, 200);
                    } else {
                    }
                    if (!TextUtils.isEmpty(text) && listener != null) {
                        listener.onValidate(text);
                    }
                }
                setKeyboardStateOnFocusChange(view, hasFocus);
            }
        });
    }

    private Runnable setKeyboardStateTask;

    private void setKeyboardStateOnFocusChange(final View target, boolean visible) {
        if (target != null) {
            target.removeCallbacks(setKeyboardStateTask);
            setKeyboardStateTask = () -> {
                KeyboardUtils.setKeyboardVisible((Activity) context, target, visible);
            };
            target.postDelayed(setKeyboardStateTask, 100);
        }
    }

    public void setHint(String hint) {
        this.hint = hint;
        etInput.setHint(hint);
    }
}