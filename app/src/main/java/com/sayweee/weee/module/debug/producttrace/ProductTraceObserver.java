package com.sayweee.weee.module.debug.producttrace;

import android.app.Activity;
import android.graphics.Rect;
import android.os.SystemClock;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.sayweee.weee.module.debug.producttrace.ui.ProductTraceFloatView;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.swfloat.SWFloat;

import java.lang.ref.WeakReference;

public abstract class ProductTraceObserver
        implements LifecycleEventObserver,
        ProductTraceManager.ProductTraceChangeListener {

    private final WeakReference<LifecycleOwner> mWeakLifecycleOwner;
    private String mExtraTopic;

    private ProductTraceFloatView floatingView;
    private String floatTag;

    private long mLastDispatchTime = 0L;
    private long mIncomingTime = 0L;

    public ProductTraceObserver(@NonNull LifecycleOwner lifecycleOwner) {
        this(lifecycleOwner, null);
    }

    public ProductTraceObserver(@NonNull LifecycleOwner lifecycleOwner, String topic) {
        this.mWeakLifecycleOwner = new WeakReference<>(lifecycleOwner);
        this.mExtraTopic = topic;
    }

    @Override
    public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
        Lifecycle lifecycle = lifecycleOwner.getLifecycle();
        if (event == Lifecycle.Event.ON_START) {
            refreshFloatingView();
        } else if (event == Lifecycle.Event.ON_DESTROY) {
            ProductTraceManager.get().unregisterObserver(this);
            lifecycle.removeObserver(this);
        } else if (lifecycle.getCurrentState().isAtLeast(Lifecycle.State.RESUMED)) {
            // Lifecycle is at least RESUMED, we can handle incoming changes
            if (mIncomingTime > 0 && mIncomingTime > mLastDispatchTime) {
                mLastDispatchTime = mIncomingTime;
                dispatchProductSalesTraceChange();
            }
        }
    }

    @Override
    public void onProductTraceChanged(@NonNull String topic) {
        boolean handles = shouldHandleProductSalesTraceChange(topic);
        if (!handles) {
            return;
        }
        Lifecycle lifecycle = getLifecycle();
        if (lifecycle != null && lifecycle.getCurrentState().isAtLeast(Lifecycle.State.STARTED)) {
            mLastDispatchTime = mIncomingTime = now();
            dispatchProductSalesTraceChange();
        } else {
            mIncomingTime = now();
        }
    }

    protected boolean shouldHandleProductSalesTraceChange(String topic) {
        return (topic != null && topic.equals(mExtraTopic))
                || ProductTraceManager.CONFIG_CHANGED.equals(topic)
                || ProductTraceManager.DOMAIN_CHANGED.equals(topic);
    }

    protected void dispatchProductSalesTraceChange() {
        refreshFloatingView();
        handleProductSalesTraceChange();
    }

    protected abstract void handleProductSalesTraceChange();

    public ProductTraceObserver setExtraTopic(String extraTopic) {
        this.mExtraTopic = extraTopic;
        return this;
    }

    public void start() {
        Lifecycle lifecycle = getLifecycle();
        if (lifecycle != null) {
            ProductTraceManager.get().registerObserver(this);
            lifecycle.addObserver(this);
        }
    }

    private long now() {
        return SystemClock.uptimeMillis();
    }

    @Nullable
    protected Lifecycle getLifecycle() {
        LifecycleOwner lifecycleOwner = mWeakLifecycleOwner.get();
        if (lifecycleOwner != null) {
            return lifecycleOwner.getLifecycle();
        }
        return null;
    }

    private void refreshFloatingView() {
        if (ProductTraceManager.get().isEnabled()) {
            // show or update
            if (floatingView != null) {
                floatingView.update();
            } else {
                createFloatingView(mWeakLifecycleOwner.get());
            }
        } else {
            // dismiss
            if (floatTag != null) {
                floatingView = null;
                SWFloat.dismiss(floatTag);
            }
        }
    }

    private void createFloatingView(LifecycleOwner lifecycleOwner) {
        Activity activity = findActivity(lifecycleOwner);
        if (activity != null && lifecycleOwner != null && lifecycleOwner.getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.CREATED)) {
            floatTag = "product_trace_float_" + activity.hashCode();
            if (!SWFloat.isExists(floatTag)) {
                floatingView = new ProductTraceFloatView(activity);
                SWFloat.with(activity)
                        .setView(floatingView)
                        .setTag(floatTag)
                        .setInitialInsets(new Rect(0, 0, 0, CommonTools.dp2px(120)))
                        .show();
            }
        }
    }

    private Activity findActivity(LifecycleOwner lifecycleOwner) {
        if (lifecycleOwner instanceof Fragment) {
            return ((Fragment) lifecycleOwner).getActivity();
        } else if (lifecycleOwner instanceof Activity) {
            return (Activity) lifecycleOwner;
        }
        return null;
    }

}
