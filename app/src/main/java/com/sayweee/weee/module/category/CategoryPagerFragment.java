package com.sayweee.weee.module.category;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_32;

import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cate.CateActivity;
import com.sayweee.weee.module.cate.LazyPagerFragment;
import com.sayweee.weee.module.cate.bean.CateBean;
import com.sayweee.weee.module.cate.product.bean.RelatedBean;
import com.sayweee.weee.module.category.adapter.CategoryEndProvider;
import com.sayweee.weee.module.category.adapter.CategoryPagerItemAdapter;
import com.sayweee.weee.module.category.adapter.OnRequestBuyTogetherListener;
import com.sayweee.weee.module.category.bean.CategoryFilterData;
import com.sayweee.weee.module.category.bean.CategoryPagerBean;
import com.sayweee.weee.module.category.bean.CategoryPagerBundle;
import com.sayweee.weee.module.category.bean.ProductFilterBean;
import com.sayweee.weee.module.category.bean.ProductSortBean;
import com.sayweee.weee.module.category.bean.RelatedData;
import com.sayweee.weee.module.category.bean.SecondaryFilterData;
import com.sayweee.weee.module.category.bean.WebFilterData;
import com.sayweee.weee.module.category.service.CategoryPagerViewModel;
import com.sayweee.weee.module.category.service.CategoryProgressBarViewModel;
import com.sayweee.weee.module.category.service.CategoryViewModel;
import com.sayweee.weee.module.cms.iml.product.ProductItemProvider;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.dialog.FilterDialog;
import com.sayweee.weee.module.dialog.SingleFilterDialog;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.product.provider.PayloadKey;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BitmapUtils;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.indicator.CompatNavigatorAdapter;
import com.sayweee.weee.widget.indicator.CompatSimplePagerTitleView;
import com.sayweee.weee.widget.indicator.IndicatorTools;
import com.sayweee.weee.widget.indicator.TrackCommonPagerTitleView;
import com.sayweee.weee.widget.indicator.TrackNavigator;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.CenterSmoothScroller;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.refresh.LoadingMoreView;
import com.sayweee.weee.widget.tab.ITabSelectedEventDispatch;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import java.util.List;
import java.util.Map;

public class CategoryPagerFragment extends LazyPagerFragment<CategoryPagerViewModel>
        implements ITabSelectedEventDispatch, OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {

    CompatMagicIndicator indicator, filterIndicator;
    View llIndicator, llFilterIndicator;
    View ivFilter, ivFilterSec;
    TextView tvFilterQty, tvFilterQtySec;
    SmartRefreshLayout smartRefreshLayout;
    RecyclerView recyclerView;
    View shadow;
    View layoutBottomEnd;
    CategoryPagerItemAdapter adapter;
    int indicatorIndex;
    String catalogueNumByUrl;
    String sortByUrl;
    Map<String, String> filterByUrl;

    private CategoryProgressBarViewModel progressBarViewModel;

    private RecyclerViewScrollStatePersist scrollStatePersist;
    private static final RecyclerViewScrollStatePersist.ScrollStateKeyProvider SCROLL_STATE_KEY_PROVIDER =
            new RecyclerViewScrollStatePersist.DefaultScrollStateKeyProviderImpl();
    private Map<String, Object> trackingCtx;

    private ProductTraceObserver productTraceObserver;

    public static Fragment newInstance(CategoryPagerBundle categoryPagerBundle) {
        CategoryPagerFragment fragment = new CategoryPagerFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("bundle", categoryPagerBundle);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_category_pager;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        indicator = findViewById(R.id.indicator);
        filterIndicator = findViewById(R.id.indicator_filter);
        smartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        recyclerView = findViewById(R.id.rv);
        shadow = findViewById(R.id.shadow);
        llIndicator = findViewById(R.id.ll_indicator);
        ivFilter = findViewById(R.id.iv_sort);//排序模块
        ivFilterSec = findViewById(R.id.iv_sort_secondary);//二级filter排序模块
        tvFilterQty = findViewById(R.id.tv_filter_num);//排序filter个数
        tvFilterQtySec = findViewById(R.id.tv_filter_num_secondary);
        layoutBottomEnd = findViewById(R.id.layout_bottom_end);
        //新filter模块
        llFilterIndicator = findViewById(R.id.ll_filter);
        smartRefreshLayout.setEnableRefresh(true);
        smartRefreshLayout.setOnRefreshListener(this);
        recyclerView.setLayoutManager(new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL));
        adapter = new CategoryPagerItemAdapter();
        //ProductItemProvider
        adapter.addItemProvider(new ProductItemProvider() {
            @Override
            public void onProductClick(ProductItemData item, AdapterViewHolder helper, Map<String, Object> ctx, ProductView view) {
                super.onProductClick(item, helper, ctx, view);
                int position = adapter.getData().indexOf(item);
                if (!(adapter.getItem(position + 1) instanceof RelatedData)) {
                    viewModel.getPdpRelated(item, position);
                }
            }
        });
        adapter.setIsCategory();
        recyclerView.setAdapter(adapter);
        adapter.setPreLoadNumber(5);
        adapter.setLoadMoreView(new LoadingMoreView());
        adapter.setOnLoadMoreListener(this, recyclerView);
        adapter.setEmptyView(View.inflate(activity, R.layout.layout_status_empty_page_category, null));
        setCategoryToggleListener();
        //滑动到整块可视自动播放
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    adapter.onPageScrollStateChanged(recyclerView, newState);
                    LabelScrollHandler.notifyScrollStateChanged(recyclerView);//label scroll
                }
                OpActionHelper.notifyScrollStateChanged(newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                showShadow(recyclerView);
            }
        });

        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);
        scrollStatePersist.setupRecyclerView(recyclerView, RecyclerView.VERTICAL, SCROLL_STATE_KEY_PROVIDER);
        updateScrollStateKey();

        ViewTools.setViewVisible(false, llFilterIndicator, layoutBottomEnd);
        showTagVeil(true);
        showProductVeil(true);
        setOnItemChildClickListener();
        setOnRequestBuyTogetherListener();
    }

    private void updateScrollStateKey() {
        String key = "recycler-";
        key += viewModel != null ? viewModel.currentCatalogueNum : null;
        recyclerView.setTag(R.id.tag_scroll_state_key, key);
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    public void loadData() {
        viewModel.injectParentFragment(getParentFragment());
        Bundle bundle = getArguments();
        if (bundle != null) {
            CategoryPagerBundle categoryPagerBundle = (CategoryPagerBundle) bundle.getSerializable("bundle");
            viewModel.injectCategoryPagerBundle(categoryPagerBundle);
            reload(false);
        }
        initProductTraceObserver();
    }

    @Override
    public <VM> VM createModel() {
        Fragment parentFragment = getParentFragment();
        if (parentFragment != null) {
            progressBarViewModel = ViewModelProviders.of(parentFragment).get(CategoryProgressBarViewModel.class);
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        updateScrollStateKey();
        viewModel.indicatorData.observe(this, new Observer<List<CategoryPagerBean.Categories>>() {
            @Override
            public void onChanged(List<CategoryPagerBean.Categories> categories) {
                fillIndicator(categories);
            }
        });

        viewModel.filterData.observe(this, new Observer<CategoryFilterData>() {
            @Override
            public void onChanged(CategoryFilterData categoryFilterData) {
                if (categoryFilterData != null) {
                    fillFilter(categoryFilterData);
                    boolean isShowFilterIndicator = CollectionUtils.isNotEmpty(categoryFilterData.filtersSec);
                    ViewTools.setViewVisible(isShowFilterIndicator, llFilterIndicator, ivFilterSec);
                    ViewTools.setViewVisible(!isShowFilterIndicator, ivFilter);//副分类filter icon隐藏
                    if (isShowFilterIndicator) {
                        fillFilterIndicator(categoryFilterData);//filter show out
                    }
                }
            }
        });

        viewModel.filterQtyData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer != null) {
                    tvFilterQty.setText(String.valueOf(integer));
                    tvFilterQtySec.setText(String.valueOf(integer));
                    tvFilterQty.setVisibility(ivFilter.getVisibility() == View.VISIBLE && integer > 0 ? View.VISIBLE : View.INVISIBLE);
                    tvFilterQtySec.setVisibility(ivFilterSec.getVisibility() == View.VISIBLE && integer > 0 ? View.VISIBLE : View.INVISIBLE);
                } else {
                    ViewTools.setViewVisible(View.INVISIBLE, tvFilterQty, tvFilterQtySec);
                }
            }
        });

        viewModel.veilData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                showProductVeil(aBoolean);
                if (aBoolean) {
                    ViewTools.setViewVisible(layoutBottomEnd, false);
                }
            }
        });

        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> componentData) {
                smartRefreshLayout.finishRefresh();
                updateScrollStateKey();
                if (scrollStatePersist != null) {
                    scrollStatePersist.restoreScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
                }
                Map<String, String> filterMap = viewModel.getCtxFilter();
                String sort = EmptyUtils.isEmpty(viewModel.sort) ? "recommend" : viewModel.sort;
                adapter.onCtxAdded(viewModel.categoryPagerBundle.current.key, viewModel.currentCatalogueNum, sort, filterMap, null, null, null);
                trackingCtx = new EagleContext().setFilterSubCategory(viewModel.categoryPagerBundle.current.key)
                        .setCatalogueNum(viewModel.currentCatalogueNum)
                        .setSort(sort)
                        .setFilters(filterMap).asMap();
                adapter.setNewData(componentData);
                adapter.notifyPageDataSetChanged(recyclerView);
                boolean isShowBottom = 0 == indicatorIndex && EmptyUtils.isEmpty(componentData);
                ViewTools.setViewVisible(layoutBottomEnd, isShowBottom);
                if (isShowBottom) {
                    fillBottom();
                }
                LabelScrollHandler.notifyAdapterDataChanged(recyclerView);
            }
        });

        viewModel.adapterAppendData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> componentData) {
                adapter.addData(componentData);
            }
        });

        viewModel.nextPageExistData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean nextPageExist) {
                if (nextPageExist != null && nextPageExist) {
                    adapter.loadMoreComplete();
                } else {
                    if (indicatorIndex == 0) {
                        adapter.loadMoreEnd(true);
                    } else {
                        adapter.loadMoreEnd();
                    }
                }
            }
        });

        if (getParentFragment() != null) {
            CategoryViewModel categoryViewModel = ViewModelProviders.of(getParentFragment()).get(CategoryViewModel.class);
            categoryViewModel.syncData.observe(this, new Observer<String>() {
                @Override
                public void onChanged(String key) {
                    viewModel.requestSync(key);
                }
            });
        }

        //web filter参数
        SharedViewModel.get().newWebFilter.observe(this, new Observer<WebFilterData>() {
            @Override
            public void onChanged(WebFilterData webFilterData) {
                Bundle bundle = getArguments();
                if (bundle != null) {
                    CategoryPagerBundle categoryPagerBundle = (CategoryPagerBundle) bundle.getSerializable("bundle");
                    if (categoryPagerBundle != null && webFilterData.key.equalsIgnoreCase(categoryPagerBundle.current.key)) {
                        if (!EmptyUtils.isEmpty(webFilterData.params)) {
                            if (!(getActivity() instanceof CateActivity) && "new_page".equalsIgnoreCase(webFilterData.params.get("showmode"))) {
                                //当前页面无需接受发送给category activity样式的web url filter事件
                                return;
                            }
                            filterByUrl(webFilterData.params);
                        }
                    }
                }
            }
        });

        viewModel.indexByUrlData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer indexByUrl) {
                handlePageSelect(indexByUrl, catalogueNumByUrl);
                catalogueNumByUrl = null;
            }
        });

        SharedViewModel.get().collectsData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
            }
        });

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (progressBarViewModel != null && viewLifecycleOwner != null) {
            progressBarViewModel.getOnProgressBarDismissSignal().observe(viewLifecycleOwner, sig -> {
                if (recyclerView != null) {
                    recyclerView.postDelayed(() -> {
                        if (adapter != null) {
                            adapter.notifyProductSync();
                            ProductSyncHelper.onPageResume(adapter);
                        }
                    }, 200L);
                }
            });
        }

        viewModel.relatedCardData.observe(this, new Observer<RelatedData>() {
            @Override
            public void onChanged(RelatedData relatedData) {
                int position = relatedData.position + 1;
                if (position <= adapter.getData().size()) {
                    addRelatedData(relatedData, position);
                }
            }
        });

        viewModel.errorData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                adapter.setNewData(null);
            }
        });
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        adapter.onPageResume(recyclerView);
        ProductSyncHelper.onPageResume(adapter);
        if (indicator != null && indicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) indicator.getNavigator()).onPageResume();
        }
        if (filterIndicator != null && filterIndicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) filterIndicator.getNavigator()).onPageResume();
        }
        if (viewModel != null) {
            viewModel.checkRelatedCache();
        }
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause(recyclerView);
        if (indicator != null && indicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) indicator.getNavigator()).onPagePause();
        }
        if (filterIndicator != null && filterIndicator.getNavigator() instanceof TrackNavigator) {
            ((TrackNavigator) filterIndicator.getNavigator()).onPagePause();
        }
    }

    @Override
    public void onTabDoubleTap() {
        ViewTools.smoothScrollToPosition(recyclerView);
    }

    @Override
    public void onTabUnselected(int position) {
        deleteRelatedData();
    }

    @Override
    public void onLoadMoreRequested() {
        viewModel.requestMore();
        if (progressBarViewModel != null) {
            progressBarViewModel.handleLoadMoreRequest();
        }
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        updateScrollStateKey();
        if (scrollStatePersist != null) {
            scrollStatePersist.clearScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
        }
        adapter.setEnableLoadMore(false);
        reload(true);
    }

    private void fillIndicator(List<CategoryPagerBean.Categories> categories) {
        showTagVeil(false);
        String color = viewModel.categoryPagerBundle.current.color != null ? viewModel.categoryPagerBundle.current.color : "#999999";
        IndicatorTools.fillIndicator(activity, indicator, new CompatNavigatorAdapter<CategoryPagerBean.Categories>(categories) {

            @Override
            public IPagerTitleView getTitleView(Context context, int index) {
                CategoryPagerBean.Categories item = getItem(index);
                String catalogueNum = item.catalogue_num;
                CompatSimplePagerTitleView child = new CompatSimplePagerTitleView(context);
                CommonPagerTitleView titleView = new TrackCommonPagerTitleView(context) {
                    @Override
                    public void onImpressionTrigger() {
                        String key = viewModel.categoryPagerBundle.current.key + "_" + catalogueNum;
                        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_CATEGORY, key)) {
                            Map<String, Object> params = new EagleTrackModel.Builder()
                                    .setMod_nm(EagleTrackEvent.ModNm.FILTER_BUTTONS)
                                    .setMod_pos(1)
                                    .setButton_nm(catalogueNum)
                                    .setButton_pos(index)
                                    .setIs_selected(index == indicatorIndex)
                                    .build().getParams();
                            AppAnalytics.logFilterButtonImp(params);
                            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_CATEGORY, key);
                        }
                    }
                };
                titleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {

                    @Override
                    public void onSelected(int index, int totalCount) {
                        child.onSelected(index, totalCount);
                        ShapeHelper.setBackgroundSolidDrawable(
                                child,
                                Color.parseColor(color),
                                CommonTools.dp2px(12)
                        );
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        child.onDeselected(index, totalCount);
                        ShapeHelper.setBackgroundDrawable(
                                child,
                                ContextCompat.getColor(context, R.color.transparent),
                                CommonTools.dp2px(12),
                                ContextCompat.getColor(context, R.color.color_surface_100_hairline),
                                CommonTools.dp2px(1.5f)
                        );
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {
                    }
                });
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
                int margin = CommonTools.dp2px(8);
                params.leftMargin = margin;
                params.rightMargin = index == categories.size() - 1 ? margin : 0;
                titleView.setContentView(child, params);
                int left = CommonTools.dp2px(10);
                int top = CommonTools.dp2px(7);
                child.setUseBoldOnSelectedOrNormal(false);
                child.setPadding(left, top, left, top);
                child.setPagerTitleStyle(
                        item.catalogue_name,
                        R.style.style_body_xs_medium,
                        getResources().getColor(R.color.color_surface_100_fg_minor),
                        getResources().getColor(R.color.color_surface_400_fg_default),
                        new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.FILTER_BUTTONS,
                                        1,
                                        null,
                                        -1,
                                        catalogueNum,
                                        index,
                                        EagleTrackEvent.TargetType.FILTER_BUTTON,
                                        EagleTrackEvent.ClickType.VIEW);
                                deleteRelatedData();
                                handlePageSelect(index, catalogueNum);
                            }
                        });
                return titleView;
            }
        });
        if (!EmptyUtils.isEmpty(catalogueNumByUrl)) {
            for (int i = 0; i < categories.size(); i++) {
                if (catalogueNumByUrl.equalsIgnoreCase(categories.get(i).catalogue_num)) {
                    viewModel.requestFilterByUrl(i, filterByUrl, sortByUrl);
                    break;
                }
            }
        }
    }

    private void handlePageSelect(int index, String catalogueNum) {
        indicatorIndex = index;
        viewModel.requestCatalogueNum(catalogueNum);
        indicator.handlePageSelected(index, false);
    }

    private void fillFilterIndicator(CategoryFilterData categoryFilterData) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(viewModel.categoryPagerBundle.current.key, viewModel.currentCatalogueNum
                , null, null, null, null);
        if (!EmptyUtils.isEmpty(categoryFilterData.tagIds)) {
            ctx.put("tag_id", categoryFilterData.tagIds.toString());
        }
        List<SecondaryFilterData> filtersSec = categoryFilterData.filtersSec;
        IndicatorTools.fillIndicator(activity, filterIndicator, new CompatNavigatorAdapter<SecondaryFilterData>(filtersSec) {

            @Override
            public IPagerTitleView getTitleView(Context context, int index) {
                SecondaryFilterData bean = getItem(index);
                String propertyKey = bean.isFilterBean() ? bean.filterBean.property_key : bean.valueBean.value_key;
                CommonPagerTitleView titleView = new TrackCommonPagerTitleView(context) {
                    @Override
                    public void onImpressionTrigger() {
                        String key = viewModel.categoryPagerBundle.current.key + "_" + propertyKey;
                        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_CATEGORY, key)) {
                            Map<String, Object> params = new EagleTrackModel.Builder()
                                    .setMod_nm(EagleTrackEvent.ModNm.FILTER_BUTTONS)
                                    .setMod_pos(2)
                                    .setButton_nm(propertyKey)
                                    .setButton_pos(index)
                                    .setIs_selected(bean.isSelected())
                                    .addCtx(ctx)
                                    .build().getParams();
                            AppAnalytics.logFilterButtonImp(params);
                            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_CATEGORY, key);
                        }
                    }
                };
                //
                View customLayout = LayoutInflater.from(context).inflate(R.layout.tab_filter_explore, null);
                ShapeConstraintLayout layoutBg = customLayout.findViewById(R.id.layout);
                TextView tvName = customLayout.findViewById(R.id.tv_filter_name);
                TextView tvQty = customLayout.findViewById(R.id.tv_filter_qty);
                ImageView ivEnd = customLayout.findViewById(R.id.iv_filter_end);
                ViewTools.setViewVisible(false, tvQty, ivEnd);
                tvName.setText(bean.isFilterBean() ? bean.filterBean.property_name : bean.valueBean.value_name);
                if (bean.isFilterBean()) {
                    if (bean.filterBean.isMultiple() || bean.filterBean.isSingle()) {
                        layoutBg.setBackgroundSolidDrawable(ContextCompat.getColor(activity, R.color.color_surface_200_bg), CommonTools.dp2px(13));
                        ViewTools.applyTextColor(tvName, R.color.color_surface_1_fg_major_idle);
                        ViewTools.setViewVisible(true, ivEnd);
                        ivEnd.setImageResource(R.mipmap.iv_filter_multi);
                        ViewTools.setViewVisible(bean.filterBean.getSelectedQty() > 0, tvQty);
                        tvQty.setText(String.valueOf(bean.filterBean.getSelectedQty()));
                    } else if (bean.filterBean.isBoolean()) {
                        if (bean.filterBean.isBooleanSelected()) {
                            //Boolean选中
                            layoutBg.setBackgroundSolidDrawable(ContextCompat.getColor(activity, R.color.color_root_energy_blue_light_3), CommonTools.dp2px(13));
                            ViewTools.applyTextColor(tvName, R.color.color_primary_1);
                            ViewTools.setViewVisible(true, ivEnd);
                            ivEnd.setImageDrawable(BitmapUtils.tint(context, R.mipmap.iv_filter_check, ContextCompat.getColor(context, R.color.color_primary_1)));
                        } else {
                            //Boolean未选中
                            layoutBg.setBackgroundSolidDrawable(ContextCompat.getColor(activity, R.color.color_surface_200_bg), CommonTools.dp2px(13));
                            ViewTools.applyTextColor(tvName, R.color.color_surface_1_fg_major_idle);
                        }
                    }
                } else {
                    //被选中的Multiple和Single里的===》filter子选项卡片
                    layoutBg.setBackgroundSolidDrawable(ContextCompat.getColor(activity, R.color.color_surface_200_bg), CommonTools.dp2px(13));
                    ViewTools.applyTextColor(tvName, R.color.brand_color_tone_blue_spectrum_19);
                    ViewTools.setViewVisible(true, ivEnd);
                    ivEnd.setImageResource(R.mipmap.iv_filter_close);
                    TalkBackHelper.setContentDesc(ivEnd, TalkBackHelper.Type.FLAG_CLOSE);
                }
                //margin setting
                int margin = CommonTools.dp2px(8);
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, CommonTools.dp2px(30));
                params.leftMargin = margin;
                params.rightMargin = index == filtersSec.size() - 1 ? margin : 0;
                titleView.setContentView(customLayout, params);
                titleView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (bean.isFilterBean()) {
                            ProductFilterBean filterBean = bean.filterBean;
                            if (filterBean.isMultiple() || filterBean.isSingle()) {
                                //弹出弹窗
                                new SingleFilterDialog(activity)
                                        .setFilterData(CollectionUtils.arrayListOf(filterBean), "category", ctx)
                                        .setOnApplyListener(new SingleFilterDialog.OnApplyListener() {
                                            @Override
                                            public void apply(SingleFilterDialog dialog, List<ProductFilterBean> filters) {
                                                viewModel.requestSingleFilter(categoryFilterData, filters.get(0));//点击二级filter弹出的dialog
                                                dialog.dismiss();
                                                //apply按钮
                                                EagleTrackManger.get().trackEagleClickAction(filters.get(0).property_key + "_popup", -1, null, -1, "apply", 0, EagleTrackEvent.TargetType.APPLY_BUTTON, EagleTrackEvent.ClickType.VIEW, ctx);
                                            }
                                        }).show();
                                EagleTrackManger.get().trackEagleClickAction("filter_buttons",
                                        2,
                                        null,
                                        -1,
                                        "cuisines",
                                        index,
                                        "filter_button",
                                        EagleTrackEvent.ClickType.VIEW, ctx, null, null);
                            } else if (filterBean.isBoolean()) {
                                //boolean单选
                                filterBean.getBooleanValue().selected = !filterBean.isBooleanSelected();
                                viewModel.requestFilterIndicator(viewModel.filterData.getValue(), filterBean.getBooleanValue());
                                notifyDataSetChanged();
                            }
                        } else {
                            //删除当前子选项卡
                            filtersSec.remove(index);
                            notifyDataSetChanged();
                            bean.valueBean.selected = false;
                            viewModel.requestFilterIndicator(viewModel.filterData.getValue(), bean.valueBean);
                            EagleTrackManger.get().trackEagleClickAction("filter_buttons",
                                    2,
                                    null,
                                    -1,
                                    bean.valueBean.value_key,
                                    index,
                                    "filter_button",
                                    "unselect", ctx, null, null);
                        }
                    }
                });
                return titleView;
            }
        });
        filterIndicator.onPageSelected(-1);
    }

    private void fillFilter(CategoryFilterData categoryFilterData) {
        int color = ViewTools.parseColor(getContext(), viewModel.categoryPagerBundle.current.color, R.color.color_btn_secondary_bg);
        tvFilterQty.setBackground(ShapeHelper.buildSolidDrawable(color, CommonTools.dp2px(12)));
        tvFilterQtySec.setBackground(ShapeHelper.buildSolidDrawable(color, CommonTools.dp2px(12)));
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(viewModel.categoryPagerBundle.current.key, viewModel.currentCatalogueNum
                , null, null, null, null);
        ViewTools.setViewOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                new FilterDialog(activity)
                        .setFilterData(categoryFilterData.sorts, categoryFilterData.filters, "category", ctx)
                        .setOnApplyListener(new FilterDialog.OnApplyListener() {
                            @Override
                            public void apply(FilterDialog dialog, List<ProductSortBean> sorts, List<ProductFilterBean> filters) {
                                categoryFilterData.sorts = sorts;
                                categoryFilterData.filters = filters;
                                viewModel.requestFilter(categoryFilterData);//点击filter dialog
                                dialog.dismiss();
                                //apply按钮
                                Map<String, String> filterMap = viewModel.getCtxFilter();
                                Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, viewModel.sort, filterMap, null, null);
                                EagleTrackManger.get().trackEagleClickAction(null, -1, null, -1, "apply", -1, EagleTrackEvent.TargetType.APPLY_BUTTON, EagleTrackEvent.ClickType.VIEW, ctx);
                            }
                        })
                        .show();
            }
        }, ivFilter, ivFilterSec);
    }

    private void reload(boolean isRefresh) {
        if (viewModel != null) {
            viewModel.getData(isRefresh ? CategoryPagerViewModel.REQUEST_TYPE_REFRESH : CategoryPagerViewModel.REQUEST_TYPE_CATEGORY);
        }
    }

    private void filterByUrl(Map<String, String> params) {
        String sortByUrl = params.get(Constants.UrlMapParams.SORT);
        Map<String, Object> temp = JsonUtils.parseObject(params.get(Constants.UrlMapParams.FILTERS), Map.class);
        Map<String, String> filterByUrl = new ArrayMap<>();
        if (!EmptyUtils.isEmpty(temp)) {
            for (Map.Entry<String, Object> entry : temp.entrySet()) {
                filterByUrl.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
            String catalogueNum = filterByUrl.get(CategoryPagerViewModel.KEY_CATALOGUE_NUM);
            filterByUrl.remove(CategoryPagerViewModel.KEY_CATALOGUE_NUM);
            if (EmptyUtils.isEmpty(catalogueNum)) {
                catalogueNum = CategoryPagerViewModel.ALL;
            }
            catalogueNumByUrl = catalogueNum;
            List<CategoryPagerBean.Categories> categories = viewModel.indicatorData.getValue();
            if (EmptyUtils.isEmpty(categories)) {
                //首次进入该分类
                this.filterByUrl = filterByUrl;
                this.sortByUrl = sortByUrl;
            } else {
                //再次进入该分类
                for (CategoryPagerBean.Categories category : categories) {
                    if (catalogueNum.equalsIgnoreCase(category.catalogue_num)) {
                        viewModel.requestFilterByUrl(categories.indexOf(category), filterByUrl, sortByUrl);
                        break;
                    }
                }
            }
        }
    }

    private void fillBottom() {
        CateBean.CategoryListBean previous = viewModel.categoryPagerBundle.previous;
        CateBean.CategoryListBean next = viewModel.categoryPagerBundle.next;
        if (previous != null) {
            ViewTools.setViewVisible(findViewById(R.id.layout_bottom_left), true);
            loadImage(findViewById(R.id.iv_bottom_left), previous.top_img_url);
            findViewById(R.id.layout_bottom_left).setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    toggleCategory(previous.key);
                }
            });
        }
        if (next != null) {
            ViewTools.setViewVisible(findViewById(R.id.layout_bottom_right), true);
            loadImage(findViewById(R.id.iv_bottom_right), next.top_img_url);
            findViewById(R.id.layout_bottom_right).setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    toggleCategory(next.key);
                }
            });
        }
    }

    private void setCategoryToggleListener() {
        if (adapter.getCategoryEndProvider() instanceof CategoryEndProvider) {
            CategoryEndProvider categoryEndProvider = (CategoryEndProvider) adapter.getCategoryEndProvider();
            categoryEndProvider.setOnCategoryToggleListener(new CategoryEndProvider.OnCategoryToggleListener() {
                @Override
                public void onToggle(String key) {
                    toggleCategory(key);
                }
            });
        }
    }

    private void toggleCategory(String key) {
        if (getParentFragment() != null) {
            CategoryViewModel categoryViewModel = ViewModelProviders.of(getParentFragment()).get(CategoryViewModel.class);
            categoryViewModel.toggleCategory(key);
        }
    }

    private void loadImage(ImageView view, String url) {
        ImageLoader.load(activity, view, WebpManager.get().getConvertUrl(SPEC_32, url), R.color.color_place);
    }

    public void showShadow(@NonNull RecyclerView recyclerView) {
        boolean b = recyclerView.canScrollVertically(-1);
        shadow.setVisibility(b ? View.VISIBLE : View.GONE);
    }

    private void showTagVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_category_tag);
        if (vl != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.INVISIBLE);
                vl.unVeil();
            }
        }
    }

    private void showProductVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_category_list);
        if (vl != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.INVISIBLE);
                vl.unVeil();
            }
        }
    }

    private void setOnItemChildClickListener() {
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                Object o = adapter.getItem(position);
                if (view.getId() == R.id.tv_related_more) {
                    if (o instanceof RelatedData) {
                        RelatedData item = (RelatedData) o;
                        RelatedBean relatedBean = item.t;
                        trackingCtx.put("related_info", item.productId);
                        item.filterSubCategory = viewModel.categoryPagerBundle.current.key;
                        item.catalogueNum = viewModel.currentCatalogueNum;
                        item.sort = EmptyUtils.isEmpty(viewModel.sort) ? "recommend" : viewModel.sort;
                        item.filters = viewModel.getCtxFilter();
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(item.modNm)
                                .setMod_pos(item.modPos)
                                .setSec_nm(relatedBean.type)
                                .setTargetNm("explore_more")
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .addCtx(trackingCtx)
                                .build().getParams());
                        String tag = RelatedProductFragment.class.getSimpleName() + item.productId;
                        RelatedProductFragment fragment = RelatedProductFragment.newInstance(item);
                        fragment.show(getChildFragmentManager(), tag);
                        fragment.setOnDismissListener(new DialogInterface.OnDismissListener() {
                            @Override
                            public void onDismiss(DialogInterface dialog) {
                                adapter.notifyItemChanged(position, PayloadKey.CART_QTY);
                            }
                        });
                    }
                }
            }
        });
    }

    private void setOnRequestBuyTogetherListener() {
        adapter.setOnRequestBuyTogetherListener(new OnRequestBuyTogetherListener() {
            @Override
            public void requestBuyTogether(ProductItemData data, int position) {
                if (!(adapter.getItem(position + 1) instanceof RelatedData)) {
                    viewModel.getBuyTogether(data, position);
                }
            }
        });
    }

    private void addRelatedData(RelatedData relatedData, int position) {
        adapter.addData(position, relatedData);
        recyclerView.postDelayed(() -> {
            CenterSmoothScroller smoothScroller = new CenterSmoothScroller(recyclerView.getContext());
            smoothScroller.setTargetPosition(position);
            RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
            if (layoutManager != null) {
                layoutManager.startSmoothScroll(smoothScroller);
            }
        }, 350L);
    }

    private void deleteRelatedData() {
        if (adapter != null) {
            adapter.deleteRelatedData();
        }
    }

    private void initProductTraceObserver() {
        if (productTraceObserver == null) {
            productTraceObserver = new ProductTraceObserver(this) {
                @Override
                protected void handleProductSalesTraceChange() {
                    ProductTraceViewHelper.notify(recyclerView);
                }
            };
            productTraceObserver.start();
        }
        if (viewModel != null) {
            productTraceObserver.setExtraTopic(viewModel.getProductTraceTopic());
        }
    }
}
