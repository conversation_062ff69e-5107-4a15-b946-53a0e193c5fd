package com.sayweee.weee.module.search.v2.adapters.viewholders;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.search.v2.adapters.BaseListAdapter;
import com.sayweee.weee.module.search.v2.adapters.SearchResultsV2StreamAdapter;
import com.sayweee.weee.module.search.v2.bean.SearchJsonField;
import com.sayweee.weee.utils.DebugTimer;
import com.sayweee.weee.utils.JsonTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.product.ProductView;

import org.json.JSONObject;

import java.util.Map;

public class SearchResultsMainSectionProductViewHolder extends SearchResultsProductViewHolderBase {

    private View imageMask;
    private FrameLayout imageContainer;

    private Boolean usingImageSizeWithoutPaddingState;

    public static SearchResultsMainSectionProductViewHolder create(ViewGroup parent, int layoutColumns, boolean cardElevation) {
        return create(LayoutInflater.from(parent.getContext()), parent, layoutColumns, cardElevation);
    }

    public static SearchResultsMainSectionProductViewHolder create(LayoutInflater inflater, ViewGroup parent, int layoutColumns, boolean cardElevation) {
        if (Constants.SearchV2.ENABLE_MAIN_SECTION_PRODUCT_VIEW_LAYOUT) {
            return new SearchResultsMainSectionProductViewHolder(inflate(inflater, parent, layoutColumns));
        } else {
            return new SearchResultsMainSectionProductViewHolder(inflate(inflater, parent, layoutColumns), cardElevation);
        }
    }

    public static View inflate(LayoutInflater inflater, ViewGroup parent, int layoutColumns) {
        boolean isFullRowLayout = layoutColumns <= 1;
        int layoutRes = isFullRowLayout
                ? (Constants.SearchV2.ENABLE_MAIN_SECTION_PRODUCT_VIEW_LAYOUT ? R.layout.item_search_list : R.layout.search_v2_main_section_product_row)
                : R.layout.search_v2_main_section_product_grid;

        return inflater.inflate(layoutRes, parent, false);
    }

    public SearchResultsMainSectionProductViewHolder(View itemView) {
        super(itemView);
        this.bottomLineDividerView = itemView.findViewById(R.id.divider);
    }

    public SearchResultsMainSectionProductViewHolder(View itemView, boolean cardElevation) {
        super(itemView, false);

        this.imageMask = itemView.findViewById(R.id.search_result_product_image_mask);
        if (this.imageMask != null && Constants.SearchV2.PRODUCT_CARD_SIZE_FACTOR_TO_SHOW_MORE_PRODUCTS != 1) {
            ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams)this.imageMask.getLayoutParams();
            clp.matchConstraintPercentWidth = clp.matchConstraintPercentWidth * Constants.SearchV2.PRODUCT_CARD_SIZE_FACTOR_TO_SHOW_MORE_PRODUCTS;
            this.imageMask.setLayoutParams(clp);
        }

        this.imageContainer = itemView.findViewById(R.id.search_result_product_image_container);
        if (!cardElevation) {
            this.itemView.setElevation(0);
            if (this.itemView instanceof CardView) {
                CardView cardView = (CardView)this.itemView;
                cardView.setRadius(0);
            }
        }
    }

    @Override
    public void bind(JSONObject jsonObject, int position, Map<String, Object> state, BaseListAdapter.OnItemClickListener onItemClickListener) {
        super.bind(jsonObject, position, state, onItemClickListener);
        final String currentSku = jsonObject != null ? jsonObject.optString(SearchJsonField._SKU) : null;
        final DebugTimer timer = DEBUG_VERBOSE ? new DebugTimer() : null;

        if (this.productView != null) {
            this.bindProductView(jsonObject, position, state, onItemClickListener, timer);
        }
        else {
            if (this.imageContainer != null) {
                final boolean shouldUseImageSizeWithoutPadding = getSearchResultsBaseListAdapter().shouldUseImageSizeWithoutPadding(jsonObject);
                if (usingImageSizeWithoutPaddingState == null || usingImageSizeWithoutPaddingState != shouldUseImageSizeWithoutPadding) {
                    ViewGroup.MarginLayoutParams mlp = (ViewGroup.MarginLayoutParams) this.imageContainer.getLayoutParams();
                    final int cachedImageSpecialPadding = getImagePadding(getContext());
                    if (shouldUseImageSizeWithoutPadding) {
                        usingImageSizeWithoutPaddingState = true;
                        mlp.setMargins(0, 0, 0, 0);
                    } else {
                        usingImageSizeWithoutPaddingState = false;
                        mlp.setMargins(cachedImageSpecialPadding, cachedImageSpecialPadding, 0, cachedImageSpecialPadding);
                    }
                    this.imageContainer.setLayoutParams(mlp);
                }
            }

            if (onItemClickListener != null) {
                this.itemView.setOnClickListener(v -> {
                    boolean isSponsored = jsonObject.optBoolean(SearchJsonField._IS_SPONSORED);
                    if (currentSku != null && isSponsored) {
                        String event = AdsManager.TRACK_ADD_TO_CART_URL;
                        final String cachedSkuTrackKey = SearchJsonField._ADS_TRACKING_CACHE_PREFIX + event;
                        final boolean alreadyTracked = jsonObject.has(cachedSkuTrackKey);
                        if (!alreadyTracked || SearchResultsFragmentV2.ENABLE_SEARCH_RESULTS_ALWAYS_TRACK_ADD_TO_CART_CONVERSION) {
                            JSONObject adsCreative = jsonObject.optJSONObject(SearchJsonField.ADS_CREATIVE);
                            final String skuTrackUrl = adsCreative != null ? adsCreative.optString(event, null) : null;
                            if (skuTrackUrl != null) {
                                try {
                                    jsonObject.put(AdsManager.PARAM_ADS_TRACK_URL, skuTrackUrl);
                                    jsonObject.put(AdsManager.PARAM_ADS_TRACK_POSITION, position);
                                } catch (Throwable e) {
                                    if (DevConfig.isDebug()) {
                                        Logger.e(TAG, "prepare for addPendingAdsTrackingInfo error:"+e.getMessage(), e);
                                    }
                                }

                                AdsManager.addAdsTrackPendingInfo(currentSku, jsonObject);
                            }
                        }
                    }

                    onItemClickListener.onItemClick(jsonObject, position, SearchResultsMainSectionProductViewHolder.this, BaseListAdapter.CLICK_ACTION_DEFAULT, null);

                    SearchResultsV2StreamAdapter adapter = getSearchResultsBaseListAdapter();
                    if (adapter != null) adapter.trackAdsEventIfNeeded(AdsManager.TRACK_CLICK_URL, jsonObject, position);
                });
            }
        }
    }

    public void bindProductView(JSONObject jsonObject, int position, Map<String, Object> state, BaseListAdapter.OnItemClickListener onItemClickListener, DebugTimer timer) {
        try {
            if (productView != null) {
                SearchResultsFragmentV2 searchResultsFragmentV2 = getSearchResultsBaseListAdapter().getSearchResultsFragmentV2();
                String traceId = searchResultsFragmentV2.getTraceId();
                productView.setTraceId(traceId);
            }
        } catch (Exception e) {
            if (DEBUG_VERBOSE) {
                Logger.e(TAG, e);
            }
        }

        ProductBean product = JsonTools.getProductBeanCached(getContext(), jsonObject);
        if (timer != null) timer.mark("getProductBeanCached");
        if (product != null) {
            product.prod_pos = position;

            SearchResultsV2StreamAdapter adapter = getSearchResultsBaseListAdapter();
            SearchResultsFragmentV2 searchResultsFragmentV2 = getSearchResultsFragmentV2();

            String eagleTrackEventModNm = adapter.getEagleTrackEventModNm();

            productView.setCollectClickCallback(() -> onNotifyMeButtonClick(jsonObject, position, onItemClickListener));
            if (timer != null) timer.mark("setCollectClickCallback");

            productView.setAttachedProduct(product, ProductView.STYLE_LIST, (layoutOp, bean) -> {
                cartControlsLayoutOp = layoutOp;
                bindCartControls(jsonObject, position);
            });

            if (timer != null) timer.mark("setAttachedProduct");

            productView.setOnViewClickListener(R.id.layout_product, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    searchResultsFragmentV2.showProductDetail(adapter, position, sku, jsonObject, eagleTrackEventModNm);
                }
            });
            if (timer != null) timer.mark("setOnViewClickListener");
        }

        if (DEBUG_RENDER && timer != null) {
            Logger.d(TAG, "[bind:productView:" + timer.getTotalTime() + "ms] pos:" + position + " isSecondary:" + isSecondarySectionAdapter() + " sku:" + this.sku + " timer:", timer);
        }
    }

    @NonNull
    @Override
    public String toString() {
        return super.toString() + " '" + titleView.getText() + "'";
    }
}
