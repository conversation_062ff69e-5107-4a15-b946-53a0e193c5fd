package com.sayweee.weee.module.mkpl;

import androidx.annotation.Nullable;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.base.adapter.SimpleItemAdapter;
import com.sayweee.weee.module.cart.adapter.OnCartEditListener;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.iml.banner.CarouselBannerProviderFactory;
import com.sayweee.weee.module.cms.iml.blank.BlankProvider;
import com.sayweee.weee.module.cms.iml.line.LineProvider;
import com.sayweee.weee.module.cms.iml.product.ProductLineProvider;
import com.sayweee.weee.module.cms.iml.seller.SellerItemProvider;
import com.sayweee.weee.module.cms.iml.seller.SellerLineProvider;
import com.sayweee.weee.module.cms.iml.seller.data.SellerItemData;
import com.sayweee.weee.module.cms.iml.seller.data.SellerLineData;
import com.sayweee.weee.module.cms.iml.title.SimpleTitleProvider;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.function.Transform;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;

import java.util.List;

/**
 * Author:  winds
 * Date:    2023/4/11.
 * Desc:
 */
public class GlobalItemAdapter extends SimpleItemAdapter<AdapterDataType, AdapterViewHolder> implements LabelScrollAdapter {

    protected String ctxPageKey;

    @Nullable
    private final RecyclerViewScrollStatePersist scrollStatePersist;
    private final OnCartEditListener onCartEditListener;

    public GlobalItemAdapter(
            @Nullable RecyclerViewScrollStatePersist scrollStatePersist,
            @Nullable OnCartEditListener onCartEditListener
    ) {
        this.scrollStatePersist = scrollStatePersist;
        this.onCartEditListener = onCartEditListener;
    }

    public void setCtxPageKey(String ctxPageKey) {
        this.ctxPageKey = ctxPageKey;
        onCtxAdded(
                /* filterSubCategory = */ctxPageKey,
                /* catalogueNum = */null,
                /* sort = */null,
                /* filters = */null,
                /* pageTarget = */null,
                /* pageTab = */null,
                /* globalVendor = */null
        );
    }

    @SuppressWarnings("rawtypes")
    @Nullable
    @Override
    protected SectionProviderFactory getSectionProviderFactory() {
        return new SectionProviderFactory(super.getSectionProviderFactory()) {

            @Nullable
            @Override
            public ISectionProvider getItemProvider(int viewType) {
                ISectionProvider provider;
                switch (viewType) {
                    case CmsItemType.LINE: // splitter
                        provider = new LineProvider();
                        break;
                    case CmsItemType.BLANK: // space 8dp
                        provider = new BlankProvider();
                        break;
                    case CmsItemType.TITLE: // title
                        provider = new SimpleTitleProvider();
                        break;
                    case CmsItemType.CAROUSEL_BANNER: // banner section
                        provider = new CarouselBannerProviderFactory().get()
                                .setTracker(tracker)
                                .setFilterSubCategory(ctxPageKey);
                        break;
                    case CmsItemType.PRODUCT_LINE: // horizontal product list
                        provider = new ProductLineProvider()
                                .setOnCartEditListener(onCartEditListener);
                        break;
                    case CmsItemType.SELLER_LINE: // on sale section
                        provider = new SellerLineProvider()
                                .setScrollStatePersist(scrollStatePersist)
                                .setOnCartEditListener(onCartEditListener);
                        break;
                    case CmsItemType.SELLER_ITEM: // all store seller item
                        provider = new SellerItemProvider()
                                .setScrollStatePersist(scrollStatePersist)
                                .setOnCartEditListener(onCartEditListener);
                        break;
                    default:
                        provider = parentFactory != null ? parentFactory.getItemProvider(viewType) : null;
                        break;
                }
                return provider;
            }
        };
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        for (int i = start; i <= end; i++) {
            AdapterDataType data = getItem(i);
            if (data instanceof SellerItemData || data instanceof SellerLineData) {
                notifyItemChanged(i, data);
            }
        }
    }

    public static int getRealItemCount(List<AdapterDataType> data) {
        Transform<AdapterDataType, Integer> transformer = item -> {
            switch (item.getType()) {
                case CmsItemType.LINE: // splitter
                case CmsItemType.BLANK: // space 8dp
                    return 0;
                default:
                    return 1;
            }
        };
        return CollectionUtils.sumOf(data, transformer);
    }
}
