package com.sayweee.weee.module.base.adapter;

import static com.chad.library.adapter.base.BaseMultiItemQuickAdapter.TYPE_NOT_FOUND;

import android.util.SparseArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.helper.ViewMonitorHelper;
import com.sayweee.weee.widget.refresh.LoadingMoreView;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/15.
 * Desc:
 */
public class SimpleSectionAdapter<T extends AdapterDataType, V extends AdapterViewHolder> extends BaseQuickAdapter<T, V> implements EagleImpressionAdapter {

    @SuppressWarnings("rawtypes")
    @NonNull
    protected final SparseArray<ISectionProvider> providers;

    public SimpleSectionAdapter() {
        super(null);
        setLoadMoreView(new LoadingMoreView());
        providers = new SparseArray<>();
        addAdapterProvider();
        mLayoutResId = R.layout.item_cms_empty;
    }

    @CallSuper
    protected void addAdapterProvider() {
        //
    }

    @SuppressWarnings("rawtypes")
    public void addItemProvider(ISectionProvider provider) {
        if (provider != null) {
            providers.put(provider.getItemType(), provider);
        }
    }

    @SuppressWarnings("unchecked")
    public void setAdapterData(List<? extends T> list) {
        setNewData((List<T>) list);
    }

    @Override
    protected int getDefItemViewType(int position) {
        T item = getItem(position);
        if (item != null) {
            return item.getType();
        }
        return super.getDefItemViewType(position);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public void onViewAttachedToWindow(@NonNull V holder) {
        super.onViewAttachedToWindow(holder);
        ISectionProvider provider = getItemProvider(holder.getItemViewType());
        if (provider != null) {
            provider.onViewAttachedToWindow(holder);
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public void onViewDetachedFromWindow(@NonNull V holder) {
        super.onViewDetachedFromWindow(holder);
        ISectionProvider provider = getItemProvider(holder.getItemViewType());
        if (provider != null) {
            provider.onViewDetachedFromWindow(holder);
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    protected V onCreateDefViewHolder(ViewGroup parent, int viewType) {
        ISectionProvider provider = getItemProviderOrCreate(viewType);
        if (provider != null) {
            provider.setContext(parent.getContext());
            int layoutId = provider.getItemLayoutId();
            if (layoutId != TYPE_NOT_FOUND) {
                String tag = "Create " + provider.getClass().getSimpleName();
                ViewMonitorHelper.start(tag);
                V viewHolder = createBaseViewHolder(parent, layoutId);
                provider.onViewHolderCreated(viewHolder);
                ViewMonitorHelper.end(tag);
                return viewHolder;
            }
        }
        return super.onCreateDefViewHolder(parent, viewType);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    protected void convert(@NonNull V helper, T item) {
        ISectionProvider provider = getItemProvider(helper.getItemViewType());
        if (provider != null) {
            String tag = "Convert " + provider.getClass().getSimpleName();
            ViewMonitorHelper.start(tag);
            provider.convert(helper, item);
            ViewMonitorHelper.end(tag);
        } else {
            convertAbnormal(helper, item);
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    protected void convertPayloads(@NonNull V helper, T item, @NonNull List<Object> payloads) {
        ProductSyncHelper.convertPayloads(helper, item, payloads);
        ISectionProvider provider = getItemProvider(helper.getItemViewType());
        if (provider != null) {
            provider.convertPayloads(helper, item, payloads);
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public void onViewRecycled(@NonNull V helper) {
        super.onViewRecycled(helper);
        ISectionProvider provider = getItemProvider(helper.getItemViewType());
        if (provider != null) {
            provider.onViewRecycled(helper);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                List<ImpressionBean> event = fetchImpressionData(start);
                if (event != null) {
                    list.addAll(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    List<ImpressionBean> event = fetchImpressionData(i);
                    if (event != null) {
                        list.addAll(event);
                    }
                }
            }
        }
        return list;
    }

    @SuppressWarnings("rawtypes")
    protected ISectionProvider getItemProvider(int viewType) {
        return providers.get(viewType);
    }

    @SuppressWarnings("rawtypes")
    protected ISectionProvider getItemProviderOrCreate(int viewType) {
        ISectionProvider provider = providers.get(viewType);
        if (provider != null) return provider;

        SectionProviderFactory factory = getSectionProviderFactory();
        if (factory != null) {
            ISectionProvider newProvider = factory.getItemProvider(viewType);
            if (newProvider != null) {
                addItemProvider(newProvider);
                return newProvider;
            }
        }
        return null;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    protected List<ImpressionBean> fetchImpressionData(int position) {
        T item = getItem(position);
        if (item != null) {
            ISectionProvider provider = getItemProvider(item.getType());
            if (provider instanceof ImpressionProvider) {
                return ((ImpressionProvider) provider).fetchImpressionData(item, position);
            }
        }
        return null;
    }

    protected void convertAbnormal(@NonNull V helper, T item) {
        helper.itemView.setVisibility(View.GONE);
        if (DevConfig.isDebug()) {
            helper.itemView.setVisibility(View.VISIBLE);
            View view = helper.getView(R.id.tv_title);
            if (view instanceof TextView) {
                if (item instanceof ComponentData) {
                    ((TextView) view).setText("-- " + ((ComponentData) item).componentKey + " coming soon --");
                } else if (item != null) {
                    ((TextView) view).setText("-- item type = " + item.getType() + " coming soon --");
                }
            }
        }
    }

    @Nullable
    protected SectionProviderFactory getSectionProviderFactory() {
        return null;
    }

    public abstract static class SectionProviderFactory {

        protected final SectionProviderFactory parentFactory;

        protected SectionProviderFactory() {
            this(null);
        }

        protected SectionProviderFactory(@Nullable SectionProviderFactory parent) {
            this.parentFactory = parent;
        }

        @SuppressWarnings("rawtypes")
        @Nullable
        public abstract ISectionProvider getItemProvider(int viewType);
    }

}
