package com.sayweee.weee.module.product.data;

import android.content.Context;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.cate.product.bean.PromotionBean;
import com.sayweee.weee.module.product.bean.PdpItemBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;

public class PdpItemData extends AdapterWrapperData<PdpItemBean> {

    public static final int TYPE_FREE_GIFT = 901;
    public static final int TYPE_HOT_DEAL = 902;
    public static final int TYPE_ACTIVITY_BOGO = 903;
    public static final int TYPE_PROMOTION_EVENT = 904;
    public static final int TYPE_TOP_RANKING = 905;
    public static final int TYPE_BRAND = 906;
    public static final int TYPE_PRE_SALE = 907;
    public static final int TYPE_MKPL_VENDOR = 908;
    public static final int TYPE_FRESH = 909;
    public static final int TYPE_ALCOHOL = 910;
    public static final int TYPE_COLD_PACKAGE = 911;
    public static final int TYPE_POLICY = 912;
    public static final int TYPE_FULFILLMENT = 913;
    public static final int TYPE_AFFILIATE = 914;

    public static final String MOD_FULLFILLMENT_INFO = "fulfillment_info";

    public PdpItemData() {
        super(PdpItemType.PDP_PRODUCT_ITEM);
    }

    private void init(PdpItemBean bean) {
        this.type = PdpItemType.PDP_PRODUCT_ITEM;
        this.t = bean;
    }

    public PdpItemData fromFreeGift(ProductDetailBean.ProductFeatureBean featureBean, int productId, int modPos,
                           String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = featureBean.giftInfo.bar_tag;
        bean.subTitle = featureBean.giftInfo.bar_tip;
        bean.iconUrl = featureBean.giftInfo.bar_icon;
        bean.iconId = R.mipmap.pic_promo_icon;
        bean.traceId = traceId;
        bean.modNm = "free_gifts";
        bean.modPos = modPos;
        bean.targetNm = bean.modNm;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(featureBean.giftInfo.gift_list);
        bean.status = PdpItemBean.FREE_GIFT;
        bean.giftInfo = featureBean.giftInfo;
        init(bean);
        return this;
    }

    public PdpItemData fromHotDeal(PromotionBean promotionBean, int productId, int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = promotionBean.promote_title;
        bean.subTitle = promotionBean.rule_desc;
        bean.iconUrl = promotionBean.promote_icon;
        bean.iconId = R.mipmap.pic_promo_icon;
        bean.traceId = traceId;
        bean.modNm = "promotion";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(promotionBean.use_url);
        bean.url = promotionBean.use_url;
        init(bean);
        return this;
    }

    public PdpItemData fromActivity(ProductDetailBean.ProductFeatureBean.ActivityListBean activityBean,
                       int productId, int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = activityBean.title;
        bean.iconId = R.mipmap.pic_promo_icon;
        bean.traceId = traceId;
        bean.modNm = "bogo";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(activityBean.activity_link);
        bean.url = activityBean.activity_link;
        init(bean);
        return this;
    }

    public PdpItemData fromPromotion(PromotionBean promotionBean, int productId,
                       int modPos, int secPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = promotionBean.promote_title;
        bean.subTitle = promotionBean.rule_desc;
        bean.iconUrl = promotionBean.promote_icon;
        bean.iconId = R.mipmap.pic_promo_icon;
        bean.traceId = traceId;
        bean.modNm = promotionBean.type;
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(promotionBean.use_url);
        bean.url = promotionBean.use_url;
        bean.secNm = String.valueOf(promotionBean.ps_id);
        bean.secPos = secPos;
        bean.targetNm = "promotion_event";
        bean.targetPos = 0;
        init(bean);
        return this;
    }

    public PdpItemData fromTopRanking(ProductBean.BarInfoModules barInfoModules, int productId,
                       int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = barInfoModules.info_html;
        bean.iconUrl = barInfoModules.icon;
        bean.traceId = traceId;
        bean.modNm = "top_ranking";
        bean.modPos = modPos;
        bean.secNm = barInfoModules.key;
        bean.targetNm = barInfoModules.key;
        bean.productId = productId;
        bean.status = PdpItemBean.TOP_RANKING;
        bean.needItemClick = !EmptyUtils.isEmpty(barInfoModules.more_link);
        bean.url = barInfoModules.more_link;
        init(bean);
        return this;
    }


    public PdpItemData fromBrand(ProductBean.BarInfoModules barInfoModules, int productId,
                       int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = barInfoModules.info_html;
        bean.iconUrl = barInfoModules.icon;
        bean.iconId = R.mipmap.pic_brand_icon;
        bean.traceId = traceId;
        bean.modNm = "bakery".equals(barInfoModules.key) || "fresh_deli".equals(barInfoModules.key) ? "fbw_seller" : "seller";
        bean.modPos = modPos;
        bean.targetNm = bean.modNm;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(barInfoModules.more_link);
        bean.url = barInfoModules.more_link;
        init(bean);
        return this;
    }

    public PdpItemData fromPreSale(ProductBean.BarInfoModules barInfo, int productId,
                                 int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = barInfo.info_html;
        bean.iconUrl = barInfo.icon;
        bean.traceId = traceId;
        bean.modNm = barInfo.module_type;
        bean.modPos = modPos;
        bean.targetNm = bean.modNm;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(barInfo.more_link);
        bean.url = barInfo.more_link;
        init(bean);
        return this;
    }

    public PdpItemData fromPantry(Context context, ProductDetailBean.ProductFeatureBean productFeatureBean, int productId,
                                  int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        long time = DateUtils.getTime(DateUtils.YYYY_MM_DD, productFeatureBean.predict_delivery_date);
        String text = DateUtils.getFormatCartDate(context, time,
                LanguageManager.get().isSimpleChinese() || LanguageManager.get().isTraditionalChinese());
        text = String.format(context.getString(R.string.s_product_eta), text);
        bean.title = text;
        bean.iconDrawable = PantryHelper.getProductPantryDrawable(context);
//        bean.iconString = "Pantry+";
//        bean.iconStringBgColor = R.color.root_color_teal_spectrum_2;
//        bean.iconStringTextColor = R.color.root_color_teal_key_dark;
        bean.traceId = traceId;
        bean.modNm = "pantry_info";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = true;
        bean.status = PdpItemBean.PANTRY;
        init(bean);
        return this;
    }

    public PdpItemData fromFresh(ProductDetailBean.ProductFeatureBean productFeatureBean,
                                 int productId,
                                 int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = productFeatureBean.policy_title_bold;
        bean.subTitle = productFeatureBean.policy_title;
        bean.iconString = productFeatureBean.policy_icon_title;
        bean.iconStringTextColor = R.color.color_decorative_2_surface_2_fg_default_idle;
        bean.iconStringBgColor = R.color.color_decorative_2_surface_2_bg_idle;
        bean.traceId = traceId;
        bean.modNm = "freshness";
        bean.modPos = modPos;
        bean.targetNm = bean.modNm;
        bean.productId = productId;
        bean.needItemClick = true;
        bean.url = productFeatureBean.policy_url;
        init(bean);
        return this;
    }

    public PdpItemData fromAlcohol(Context context, ProductDetailBean.ProductFeatureBean productFeatureBean,
                                   String desc, int productId,
                                   int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = context.getString(R.string.s_alcohol);
        bean.subTitle = desc;
        bean.iconId = R.mipmap.pic_alcohol_v2;
        bean.traceId = traceId;
        bean.modNm = "alcohol_info";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.targetNm = bean.modNm;
        bean.status = PdpItemBean.ALCOHOL;
        bean.needItemClick = true;
        bean.url = AlcoholHelper.getAlcoholFaqUrl(
                productFeatureBean.isSeller(),
                productFeatureBean.vender_info_view != null ? productFeatureBean.vender_info_view.vender_id : null
        );
        init(bean);
        return this;
    }

    public PdpItemData fromColdPack(Context context, ProductDetailBean.ProductFeatureBean productFeatureBean,
                                 int productId,
                                  int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = context.getString(R.string.s_cold_pack);
        bean.subTitle = context.getString(R.string.s_free_applies);
        bean.iconId = R.mipmap.pic_cold_pack_icon;
        bean.traceId = traceId;
        bean.modNm = "cold_pack_info";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.targetNm = bean.modNm;
        bean.needItemClick = true;
        bean.status = PdpItemBean.COLD_PACK;
        init(bean);
        return this;
    }

    public PdpItemData fromPolicy(ProductDetailBean.ProductFeatureBean productFeatureBean,
                                 int productId,
                                  int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = productFeatureBean.policy_title;
        bean.iconId = R.mipmap.pic_freshness;
        bean.traceId = traceId;
        bean.modNm = "freshness";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = !EmptyUtils.isEmpty(productFeatureBean.policy_pop_config_key);
        bean.policy_pop_config_key = productFeatureBean.policy_pop_config_key;
        init(bean);
        return this;
    }

    public PdpItemData fromFulfillment(ProductDetailBean.ProductFeatureBean productFeatureBean,
                                 int productId,
                                  int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = productFeatureBean.fullfillment_info.title_html;
        bean.subTitle = productFeatureBean.fullfillment_info.sub_title_html;
//        bean.iconUrl = productFeatureBean.fullfillment_info.icon;
        bean.traceId = traceId;
        bean.modNm = MOD_FULLFILLMENT_INFO;
        bean.modPos = modPos;
        bean.productId = productId;
        bean.targetNm = bean.modNm;
        init(bean);
        return this;
    }

    public PdpItemData fromAffiliate(Context context, ProductDetailBean.ProductFeatureBean productFeatureBean,
                                 int productId,
                                  int modPos, String traceId) {
        PdpItemBean bean = new PdpItemBean();
        bean.title = context.getString(productFeatureBean.affiliate_in ? R.string.s_added_to_affiliate_lists_new : R.string.s_add_to_affiliate_lists_new);
        bean.iconId = R.mipmap.pic_affiliate_icon;
        bean.traceId = traceId;
        bean.modNm = "affiliate";
        bean.modPos = modPos;
        bean.productId = productId;
        bean.needItemClick = true;
        bean.status = PdpItemBean.AFFILIATE;
        init(bean);
        return this;
    }

}
