package com.sayweee.weee.module.home.provider.message;

import com.sayweee.weee.module.cms.bean.CmsBean;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.service.ICmsParser;
import com.sayweee.weee.module.home.bean.TopMessageV3Bean;
import com.sayweee.weee.module.home.provider.message.data.CmsTopMessageV3Data;

@Deprecated
public class TopMessageV3Parser implements ICmsParser {
    @Override
    public ComponentData packetData(String componentId, CmsBean.LayoutBean.LayoutSectionBean.LayoutComponentBean component, CmsBean.DataSourceBean source, CmsPageParam pageParam) {
        CmsTopMessageV3Data data = new CmsTopMessageV3Data();
        if (component.properties != null) {
            data.setProperty(new CmsProperty().parseProperty(component.properties));
        }
        return data;
    }

    @Override
    public Class<?> getTargetClazz() {
        return TopMessageV3Bean.class;
    }
}
