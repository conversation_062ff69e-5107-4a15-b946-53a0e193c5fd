package com.sayweee.weee.module.web.page;

import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.webkit.WebView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.checkout.SectionAlcoholAgreementActivity;
import com.sayweee.weee.module.web.BaseWebFragment;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.WebViewModel;
import com.sayweee.weee.module.web.bean.JSFunction;
import com.sayweee.weee.module.web.handler.BaseWebHandler;
import com.sayweee.weee.module.web.handler.CartHandler;
import com.sayweee.weee.module.web.handler.HandlerHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.tab.ITabEventDispatch;

/**
 * Author:  winds
 * Date:    2020/4/27.
 * Desc:
 */
public class PageFragment extends BaseWebFragment<WebViewModel> implements ITabEventDispatch {

    public static final int PAGE_POST = 10;
    public static final int PAGE_RESTAURANT = 11;
    public static final int PAGE_ALCOHOL_AGREEMENT = 12;
    public static final int PAGE_ALCOHOL_AGREEMENT_SELLER = 13;

    int page = PAGE_POST;

    public static PageFragment newInstance(int page) {
        PageFragment fragment = new PageFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("page", page);
        if (page == PAGE_RESTAURANT) {
            bundle.putString("url", Constants.Url.RESTAURANTS);
        } else if (page == PAGE_POST) {
            bundle.putString("url", Constants.Url.POST);
        } else if (page == PAGE_ALCOHOL_AGREEMENT) {
            bundle.putString("url", Constants.Url.AGREEMENT_OF_ALCOHOL_FIRST);
        } else if (page == PAGE_ALCOHOL_AGREEMENT_SELLER) {
            bundle.putString("url", Constants.Url.AGREEMENT_OF_ALCOHOL_FIRST_SELLER);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        super.initView(view, savedInstanceState);
        ViewTools.setViewVisible(ivBack, false);
        Bundle arguments = getArguments();
        if (arguments != null) {
            page = arguments.getInt("page", PAGE_POST);
        }
        if (page == PAGE_RESTAURANT) {
            view.setPadding(view.getPaddingLeft(), view.getPaddingTop(), view.getPaddingRight(), getResources().getDimensionPixelSize(R.dimen.default_tab_height));
        }
        registerHandler(1, new CartHandler());
        registerHandler(2, new BaseWebHandler() {
            @Override
            public boolean handler(WebView view, String url) {
                if (page == PAGE_ALCOHOL_AGREEMENT || page == PAGE_ALCOHOL_AGREEMENT_SELLER) {
                    if (url.contains(Constants.JSBridgeFunction.JS2APP)) {
                        String data = decodeJs2AppData(url);
                        if (data != null) {
                            JSFunction<JSFunction.AlcoholCheckoutArgs> function;
                            function = JSFunction.parseFunction(data, JSFunction.AlcoholCheckoutArgs.class);
                            if (function != null && Constants.JSBridgeFunction.ALCOHOL_CHECKOUT.equalsIgnoreCase(function.functionname)) {
                                dispatchAlcoholAgreementResult(function.args);
                                return true;
                            }
                        }
                    }
                    return false;
                }
                if (url.matches(Constants.UrlPattern.NATIVE_APP) || url.matches(Constants.UrlPattern.HOME_PAGE)) {
                    HandlerHelper.handleHome(url);
                    return true;
                } else if ((url.matches(Constants.UrlPattern.SHOPPING_LIST)) || (url.matches(Constants.UrlPattern.CATEGORIES))) {
                    HandlerHelper.handleCategory(url);
                    return true;
                } else if (url.matches(Constants.UrlPattern.CART) || url.matches(Constants.UrlPattern.CART2)) {
                    HandlerHelper.handleCart(url);
                    return true;
                } else if (url.matches(Constants.UrlPattern.ME)) {
                    HandlerHelper.handleMe();
                    return true;
                }
                if (page == PAGE_RESTAURANT) {
                    if (url.matches(Constants.UrlPattern.COMMUNITY_HOME)) {
                        HandlerHelper.handlePost();
                        return true;
                    }
                    if (url.contains(Constants.JSBridgeFunction.JS2APP)
                            || url.contains("/porder/refresh_token?back_url=")) {
                        return false;
                    }
                }
                if (page == PAGE_POST) {
                    if (url.contains(Constants.JSBridgeFunction.JS2APP) || url.matches(Constants.UrlPattern.COMMUNITY_HOME)) {
                        return false;
                    }
                }
                startActivity(WebViewActivity.getIntent(activity, url));
                return true;
            }
        });
    }

    @Override
    public void loadData() {
        super.loadData();
    }

    @Override
    public void attachModel() {
        super.attachModel();
        if (page != PAGE_POST) {
            SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
                @Override
                public void onChanged(Integer integer) {
                    loadWebUrl(url);
                }
            });
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (page == PAGE_POST) {
            loadWebUrl(url);
        }
    }

    @Override
    public void onTabDoubleTap() {
        if (isAdded() && web != null) {
            stopScroll(web);
            scrollToTop(web);
        }
    }

    public void stopScroll(View view) {
        try {
            MotionEvent event = MotionEvent.obtain(System.currentTimeMillis(), System.currentTimeMillis(), MotionEvent.ACTION_DOWN, 0, 0, 0);
            MotionEvent event1 = MotionEvent.obtain(System.currentTimeMillis(), System.currentTimeMillis(), MotionEvent.ACTION_CANCEL, 0, 0, 0);
            view.dispatchTouchEvent(event);
            view.dispatchTouchEvent(event1);
        } catch (Exception ignored) {
        }
    }

    public void scrollToTop(WebView view) {
        ObjectAnimator anim = ObjectAnimator.ofInt(view, "scrollY", view.getScrollY(), 0);
        anim.setDuration(800).start();
    }

    private void dispatchAlcoholAgreementResult(@Nullable JSFunction.AlcoholCheckoutArgs args) {
        if (getActivity() instanceof SectionAlcoholAgreementActivity) {
            SectionAlcoholAgreementActivity page = (SectionAlcoholAgreementActivity) getActivity();
            page.execAlcoholCheck(args != null ? args.type : null);
        }
    }
}
