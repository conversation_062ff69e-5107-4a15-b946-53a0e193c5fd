package com.sayweee.weee.module.post.detail;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.post.PostInputFragment;
import com.sayweee.weee.module.post.PostRateTranslationFragment;
import com.sayweee.weee.module.post.base.CmtBaseActivity;
import com.sayweee.weee.module.post.bean.RateTranslationBean;
import com.sayweee.weee.module.post.detail.bean.CommentStatsData;
import com.sayweee.weee.module.post.detail.bean.PostBannerData;
import com.sayweee.weee.module.post.detail.bean.PostCommentData;
import com.sayweee.weee.module.post.detail.bean.PostContentData;
import com.sayweee.weee.module.post.detail.bean.ReviewCommentBean;
import com.sayweee.weee.module.post.detail.bean.ReviewDeleteReplyData;
import com.sayweee.weee.module.post.detail.bean.ReviewDetailBean;
import com.sayweee.weee.module.post.detail.bean.ReviewReplyData;
import com.sayweee.weee.module.post.helper.CommentHelper;
import com.sayweee.weee.module.post.profile.ProfileIntentCreator;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.post.widget.ReviewDealRightDialog;
import com.sayweee.weee.module.post.widget.VideoBottomDialog;
import com.sayweee.weee.module.product.service.ProductOpHelper;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.player.bean.MediaBean;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.share.ShareHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.SharedCartView;
import com.sayweee.weee.widget.op.BottomOpLayout;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.widget.toaster.IToaster;
import com.sayweee.widget.toaster.IToasterController;
import com.sayweee.widget.toaster.IToasterOptions;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.utils.Spanny;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/7/3.
 * Desc:
 */
public class ReviewDetailActivity extends CmtBaseActivity<ReviewViewModel>
        implements BaseQuickAdapter.RequestLoadMoreListener, IToasterController {

    public static final int FROM_NORMAL = 10000; //通用流程
    public static final int FROM_MESSAGE_CENTER = 10010; //消息中心
    public static final int FROM_SHARE = 10020; //通过deep link，通知等方式分享进入

    public int fromType; //从何处进入
    ImageView ivTitleBack;
    View ivTitleMore, layoutTitle, layoutInput, llPostDetailDark;
    TextView tvTitle, tvSend;
    ImageView ivMore;
    //EditText etInput;
    int limit = 20;
    int reviewId;
    ReviewDetailBean detailBean;
    ReviewDetailAdapter adapter;
    int page = 1;
    boolean isComment; //当前是否为评论 true 评论 false 回复
    int toReplyId;  //当前回复的id
    int parentId;   //当前回复的父id
    private KeyboardChangeHelper helper;
    private static final int FOLLOWRESULT = 99;
    private String mid = "0";
    private RecyclerView rvList;
    private PostInputFragment inputFragment;
    private Boolean isSelf;
    private EagleImpressionTrackerIml eagleImpressionTrackerIml;
    private BottomOpLayout layoutOp;
    private int productId;
    private String productKey;
    private int num = -1;
    private ProductBean product;
    private int min;
    private int max;
    private int volumeThreshold;
    private String sold_status;
    private ProductDetailBean detail;
    private String cartSource;
    private String traceId;

    public static Intent getIntent(Context context, int reviewId) {
        return getIntent(context, FROM_NORMAL, reviewId, null, null, null, null);
    }

    public static Intent getIntent(Context context, int reviewId, String cartSource, String traceId, ProductBean product) {
        Intent intent = getIntent(context, FROM_NORMAL, reviewId, null, null, cartSource, traceId);
        intent.putExtra("product", product);
        return intent;
    }

    public static Intent getIntent(Context context, int fromType, int reviewId, String msgId, String msgType, String cartSource, String traceId) {
        return new Intent(context, ReviewDetailActivity.class)
                .putExtra("fromType", fromType)
                .putExtra("reviewId", reviewId)
                .putExtra(Constants.MessageParams.MSG_ID, msgId)
                .putExtra(Constants.MessageParams.MSG_TYPE, msgType)
                .putExtra("cartSource", cartSource)
                .putExtra("traceId", traceId);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_review_detail;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        eagleImpressionTrackerIml = new EagleImpressionTrackerIml();
        reviewId = getIntent().getIntExtra("reviewId", 0);
        fromType = getIntent().getIntExtra("fromType", FROM_NORMAL);
        mid = getIntent().getStringExtra(Constants.MessageParams.MSG_ID);
        cartSource = getIntent().getStringExtra("cartSource");
        if (cartSource == null || Constants.Source.PRODUCT_PDP_REVIEW_LIST.equals(cartSource)) {
            cartSource = String.format(Constants.Source.PRODUCT_REVIEW_DETAIL, reviewId);
        }
        traceId = getIntent().getStringExtra("traceId");
        rvList = findViewById(R.id.mRecyclerView);
        ivTitleBack = findViewById(R.id.iv_title_back);
        ivTitleMore = findViewById(R.id.iv_title_more);
        ivMore = findViewById(R.id.iv_more);
        layoutTitle = findViewById(R.id.layout_title);
        layoutInput = findViewById(R.id.layout_input);
        tvTitle = findViewById(R.id.tv_title);
        tvSend = findViewById(R.id.tv_send);
        llPostDetailDark = findViewById(R.id.ll_post_detail_dark);
        layoutOp = findViewById(R.id.layout_op);
        SharedCartView cartView = findViewById(R.id.bgv_cart);
        Drawable drawable = ContextCompat.getDrawable(activity, R.mipmap.pic_black_bg);
        cartView.setCartBackground(drawable);
        Drawable drawableCart = ContextCompat.getDrawable(activity, R.mipmap.pic_white_cart);
        cartView.setCartDrawable(drawableCart);
        layoutTitle.setPadding(0, CommonTools.getStatusBarHeight(activity), 0, 0);

        rvList.setLayoutManager(new LinearLayoutManager(activity));
        adapter = new ReviewDetailAdapter();
        adapter.setReviewId(reviewId);
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter1, View view, int position) {
                AdapterDataType item = adapter.getItem(position);
                if (item instanceof AdapterProductData) {
                    AdapterProductData data = (AdapterProductData) item;
                    ProductBean bean = data.t;

                    AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                            new TrackParams().put("post_id", reviewId)
                                    .put("content_type", "p")
                                    .put("click type", "post_detail_to_pdp").get());
                    //pdp tracking click
                    Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(reviewId), null);
                    ctx.put("volume_price_support", bean.volume_price_support);
                    EagleTrackManger.get().trackEagleClickAction(
                            String.valueOf(bean.id),
                            -1,
                            EagleTrackEvent.TargetType.PRODUCT,
                            EagleTrackEvent.ClickType.VIEW,
                            ctx,
                            bean.isSeller());

                    if (ProductHelper.isSpecialProduct(bean)) {
                        startActivity(WebViewActivity.getIntent(activity, bean.view_link));
                    } else {
                        startActivity(ProductIntentCreator.getIntentByCartSource(activity, bean, String.format(Constants.Source.PORTAL_POST_DETAIL_PDP, "p", reviewId)));
                    }
                }
            }
        });
        adapter.setOnLoadMoreListener(this, rvList);
        adapter.setOnPostEventListener(new ReviewDetailAdapter.OnPostPageEventListener() {

            @Override
            public void onCommentClick() {
                if (AccountManager.get().isLogin()) {
                    onCommentTrigger();
                    AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                            new TrackParams().put("post_id", reviewId)
                                    .put("click type", "post_detail_comment").put("content_type", "p").get());
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void onReplyLoadMore(int parentId, int page) {
                viewModel.getMoreReplyData(reviewId, parentId, page, limit, mid);
            }

            @Override
            public void onReplyClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean) {
                onReplyTrigger(bean.id, bean.user_name, parentId);
                AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                        new TrackParams().put("post_id", reviewId)
                                .put("click type", "post_reply")
                                .put("content_type", "p")
                                .put("target", parentId).get());
            }

            @Override
            public void onReplyPraise(int position, int parentId, ReviewCommentBean.CommentItemBean bean) {
                if (AccountManager.get().isLogin()) {
                    viewModel.postCommentPraise(reviewId, parentId, bean.is_set_like);
                    AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                            new TrackParams().put("post_id", reviewId)
                                    .put("click type", "post_detail_like_comment")
                                    .put("content_type", "p")
                                    .put("para1", parentId).get());
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void onReplyContentClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean) {
                if (AccountManager.get().isLogin()) {
                    showBottomDialog(bean, bean.is_show_more, parentId);
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void onCommentDeleted(int qty) {
                //删除评论后回调
            }

            @Override
            public void onItemLongClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean) {
                //长按事件
                if (AccountManager.get().isLogin() && bean != null) {
                    showVideoBottomDialog(bean, bean.is_show_more, parentId);
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void specialClick(ProductBean bean) {
                if (OrderManager.CHANGE_OTHER_DAY.equals(bean.sold_status)) {
                    startActivity(DateActivity.getIntent(activity, String.valueOf(bean.id), "product modify me"));
                } else if (OrderManager.SOLD_OUT.equals(bean.sold_status)) {
                    if (AccountManager.get().isLogin()) {
                        int productId = bean.id;
                        CollectManager.get().toggleProductCollect(productId);
                        adapter.notifyDataSetChanged();
                    } else {
                        startActivity(AccountIntentCreator.getIntent(activity));
                    }
                }
            }

            @Override
            public void collectClick(ProductBean bean) {
                if (AccountManager.get().isLogin()) {
                    int productId = bean.id;
                    CollectManager.get().toggleProductCollect(productId);
                    adapter.notifyDataSetChanged();
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void productClick(ProductBean bean) {

            }


            @Override
            public void followClick(ReviewDetailBean bean) {
                viewModel.followUser(bean.uid, !bean.isFollowing());
            }

            @Override
            public void toProfileClick(int userId, String uid) {
                Intent a = ProfileIntentCreator.getIntentOnClickReviewAvatar(activity, "post-" + reviewId + "-p", uid);
                if (a != null) {
                    activity.startActivityForResult(a, FOLLOWRESULT);
                }
            }

            @Override
            public void onRateTranslation(ReviewDetailBean bean) {
                onRateTranslationTrigger(bean);
            }

            @Override
            public void onToggleCollect(boolean likeStatus, int likeCount) {
                if (detailBean != null) {
                    PostCollectManager.get().toggleCollect(false, detailBean.id, likeStatus, likeCount, true);
                    AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_DETAIL_CLICK,
                            new TrackParams().put("post_id", reviewId)
                                    .put("click type", "post_detail_like_post")
                                    .put("content_type", "p").get());
                }
            }

        });
        rvList.setAdapter(adapter);

        setRefreshSettings();
        setScrollConfig(rvList);
        setKeyboardObserver();
        setOnClickListener(
                new OnSafeClickListener() {
                    @SuppressLint("NonConstantResourceId")
                    @Override
                    public void onClickSafely(View v) {
                        switch (v.getId()) {
                            case R.id.iv_title_back:

                            case R.id.iv_back:
                                finish();
                                break;
                            case R.id.iv_title_more:
                            case R.id.iv_more:
                                showPrivilegeDialog();
                                break;
                            case R.id.iv_share: //分享
                            case R.id.iv_share2:
                                onShareTrigger();
                                break;
                            case R.id.tv_send: //发送评论或者回复
                                onSendTrigger("");
                                break;
                            case R.id.ll_post_detail_dark:
                                break;
                        }
                    }
                }
                , R.id.iv_title_back, R.id.iv_back, R.id.iv_more, R.id.iv_title_more
                , R.id.iv_share, R.id.iv_share2
                , R.id.tv_send, R.id.ll_post_detail_dark);
        Serializable serializable = getIntent().getSerializableExtra("product");
        if (serializable instanceof ProductBean) {
            product = (ProductBean) serializable;
            productId = product.getProductId();
            productKey = product.product_key;
            setProductOpConfig();
        }
    }


    @Override
    public void loadData() {
        viewModel.getReviewDetail(false, reviewId, page, limit, mid);
    }

    @Override
    public void attachModel() {
        //修改订单日期地址等信息后刷新页面
        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                page = 1;
                viewModel.getReviewDetail(true, reviewId, page, limit, mid);
            }
        });

        SharedOrderViewModel.get().cartNumData.observe(this, integer -> {
            refreshProductOpNum();
        });

        viewModel.reviewOriginData.observe(this, detail -> {
            boolean findTargetMessageByMid = detailBean == null && !EmptyUtils.isEmpty(mid);
            detailBean = detail;
            productId = detailBean.product_id;
            viewModel.getProductDetail(productId, false);
            tvTitle.setText(detail.product != null ? detail.product.name : "");
//            setCommentCount(detail.comments_count);
//            ivTitleMore.setVisibility(View.VISIBLE);
//            ivMore.setVisibility(View.VISIBLE);
            findViewById(R.id.iv_share).setVisibility(detailBean.shareVisible() ? View.VISIBLE : View.GONE);
            findViewById(R.id.iv_share2).setVisibility(detailBean.shareVisible() ? View.VISIBLE : View.GONE);
            adapter.setUid(detail.uid);
            if (findTargetMessageByMid) {
                scrollTargetMessageByMid();
            }
            if (isSelf == null) {
                isSelf = detailBean.user_id == AccountManager.get().getUserIdInt();
                AppAnalytics.logPageView(isSelf ? WeeeEvent.PageView.ME_REVIEW_DETAILS_PAGE_VIEW : WeeeEvent.PageView.PRODUCT_REVIEW_PAGE_VIEW, activity);
            }
        });

        viewModel.reviewAdapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                adapter.setAdapterData(list);
                for (int i = 0; i < list.size(); i++) {
                    AdapterDataType item = list.get(i);
                    if (item instanceof CommentStatsData) {
                        int total = ((CommentStatsData) item).count;
                        if (total > limit) {
                            adapter.loadMoreComplete();
                        } else {
                            adapter.loadMoreEnd(total == 0);
                        }
                        break;
                    }
                }
                setRefreshFinish();
                reportImpressionEvent();
            }
        });

        viewModel.shareData.observe(this, this::shareData);

        viewModel.reviewCommentData.observe(this, new Observer<ReviewCommentBean>() {
            @Override
            public void onChanged(ReviewCommentBean bean) {
                adapter.appendCommentData(bean.list);
                if (EmptyUtils.isEmpty(bean.list)) {
                    adapter.loadMoreEnd();
                } else {
                    adapter.loadMoreComplete();
                }
            }
        });

        viewModel.reviewReplyData.observe(this, new Observer<ReviewReplyData>() {
            @Override
            public void onChanged(ReviewReplyData data) {
                if (data.isReset) {
                    int count = adapter.setCommentCount("reply", 1);
                    setCommentCount(count);
                }
                adapter.setReplyData(data);
            }
        });

        viewModel.deleteData.observe(this, new Observer<ReviewDeleteReplyData>() {
            @Override
            public void onChanged(ReviewDeleteReplyData data) {
                int count = adapter.setCommentCount("delete", 1);
                setCommentCount(count);
                adapter.setCommentOrReplyDeleted(data.parentId, data.targetId);
            }
        });

        viewModel.failDetailData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                findViewById(R.id.cl_review).setBackgroundColor(getResources().getColor(R.color.text_super));
                findViewById(R.id.tv_no_review).setVisibility(View.VISIBLE);
                findViewById(R.id.bgv_cart).setVisibility(View.GONE);
                layoutOp.setVisibility(View.GONE);
//                layoutFunPanel.setVisibility(View.GONE);
            }
        });

        viewModel.productDetailData.observe(this, new Observer<ProductDetailBean>() {
            @Override
            public void onChanged(ProductDetailBean detailBean) {
                if (!EmptyUtils.isEmpty(detailBean.product)) {
                    layoutOp.setVisibility(View.VISIBLE);
                    findViewById(R.id.bgv_cart).setVisibility(View.VISIBLE);
                    productKey = detailBean.product.product_key;
                    product = detailBean.product;
                    detail = detailBean;
                    setProductOpConfig();
                }
            }
        });

        SharedViewModel.get().collectsData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                updateProductCollect();
                setProductOpConfig();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        ProductSyncHelper.onPageResume(adapter);
        if (isSelf != null) {
            AppAnalytics.logPageView(isSelf ? WeeeEvent.PageView.ME_REVIEW_DETAILS_PAGE_VIEW : WeeeEvent.PageView.PRODUCT_REVIEW_PAGE_VIEW, activity);
        }
        eagleImpressionTrackerIml.onPageResume(rvList);
        if (num >= 0) {
            ProductSyncHelper.onPageResume(layoutOp, productId, productKey, min, max);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        eagleImpressionTrackerIml.onPagePause(rvList);
    }

    @Override
    protected void onDestroy() {
        if (helper != null) {
            helper.endObserve();
        }
        super.onDestroy();
    }

    @Override
    public void onLoadMoreRequested() {
        viewModel.getReviewComment(true, reviewId, ++page, limit, mid);
    }

    protected void setKeyboardObserver() {
        helper = new KeyboardChangeHelper(getContentView());
        helper.startObserve().setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
                //dismissInputPanel(false);
            }
        });
    }

    private void setRefreshSettings() {
        SmartRefreshLayout mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        if (mSmartRefreshLayout != null) {
            mSmartRefreshLayout.setEnableRefresh(true);
            mSmartRefreshLayout.setOnRefreshListener(refreshLayout -> {
                page = 1;
                viewModel.getReviewDetail(true, reviewId, page, limit, mid);
            });
        }
    }

    private void setRefreshFinish() {
        SmartRefreshLayout mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        if (mSmartRefreshLayout != null) {
            mSmartRefreshLayout.finishRefresh();
        }
    }

    private void setScrollConfig(RecyclerView recyclerView) {
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                retryCalcTitleAnchor();
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent();//Scroll
                }
                OpActionHelper.notifyScrollStateChanged(newState);
            }
        });
    }

    private void setTitleVisible(boolean visible) {
        boolean isPublished = detailBean != null && detailBean.shareVisible();
        layoutTitle.setVisibility(visible ? View.VISIBLE : View.GONE);
        ivTitleBack.setVisibility(visible ? View.GONE : View.VISIBLE);
        ViewTools.setViewVisible(!visible && isPublished, findViewById(R.id.iv_share));
        ViewTools.setViewVisible(!visible, findViewById(R.id.bgv_cart));
        ViewTools.setViewVisible(visible, findViewById(R.id.v_shadow));
        boolean isNotMine = detailBean != null && detailBean.user_id != AccountManager.get().getUserIdInt();
//        int moreVisibility = visible ? View.GONE : detailBean != null && detailBean.privilege ? View.VISIBLE : View.GONE;
        ivMore.setVisibility(visible && isNotMine ? View.VISIBLE : View.GONE);
        ivTitleMore.setVisibility(!visible && isNotMine ? View.VISIBLE : View.GONE);
    }

    private void onRateTranslationTrigger(ReviewDetailBean bean) {
        if (AccountManager.get().isLogin()) {
            if (bean != null) {
                String[] questions = {getString(R.string.rate_translation_first_question)
                        , getString(R.string.rate_translation_second_question)
                        , getString(R.string.rate_translation_third_question)
                        , getString(R.string.rate_translation_fourth_question)};
                List<RateTranslationBean> list = new ArrayList<>();
                for (int i = 0; i < questions.length; i++) {
                    RateTranslationBean data = new RateTranslationBean();
                    data.pos = i;
                    data.name = questions[i];
                    list.add(data);
                }
                String tag = PostRateTranslationFragment.class.getSimpleName() + bean.id + "rate";
                PostRateTranslationFragment.newInstance("translation", "review", String.valueOf(bean.id), list, bean.origin_lang)
                        .show(getSupportFragmentManager(), tag);
            }
        } else {
            startActivity(AccountIntentCreator.getIntent(activity));
        }
    }

    private void scrollTargetMessageByMid() {
        if (!EmptyUtils.isEmpty(mid)) {
            View view = getView();
            if (view != null) {
                view.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (activity.isFinishing() || activity.isDestroyed()) {
                            return;
                        }
                        int position = 0;
                        int size = adapter.getData().size();
                        for (int i = 0; i < size; i++) {
                            AdapterDataType item = adapter.getItem(i);
                            if (item instanceof PostCommentData) {
                                position = i;
                                break;
                            }
                        }
                        if (position > 0) {
                            scrollToPosition(rvList, position);
                            retryCalcTitleAnchor();
                        }
                    }
                }, 50);
            }
        }
    }

    private void scrollToPosition(RecyclerView view, int index) {
        RecyclerView.LayoutManager manager = view.getLayoutManager();
        if (manager instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) manager;
            layoutManager.scrollToPositionWithOffset(index, 0);
        }
    }

    private void retryCalcTitleAnchor() {
        RecyclerView.LayoutManager manager = rvList.getLayoutManager();
        if (manager instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) manager;
            int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
            setTitleVisible(firstVisibleItemPosition >= 1);
        }
    }

    protected void onCommentTrigger() {
        setInputTag(true, -1, -1);
        showInputPanel(true, getString(R.string.s_say_something));
    }

    private void onReplyTrigger(int toReplyId, String toReplyName, int parentId) {
        setInputTag(false, toReplyId, parentId);
        showInputPanel(true, String.format(getString(R.string.replying_to), toReplyName));
    }

    protected void dismissInputPanel(boolean result) {
        if (inputFragment != null) {
            if (result) {
                inputFragment.setInputText(null);
            }
            inputFragment.dismissAllowingStateLoss();
        }
    }

    private void onShareTrigger() {
        MutableLiveData<ShareBean> shareData = viewModel.shareData;
        ShareBean bean = shareData.getValue();
        if (bean == null) {
            viewModel.getReviewShare(reviewId);
        } else {
            shareData(bean);
        }
    }

    private void onSendTrigger(String content) {
        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(activity));
            return;
        }
        if (TextUtils.isEmpty(content)) {
            return;
        }
        if (isComment) {
            viewModel.postComment(reviewId, content, limit);
        } else {
            viewModel.postReply(reviewId, toReplyId, parentId, content, limit, mid);
        }
        showInputPanel(false, null, true);
    }

    private void setInputTag(boolean isComment, int toReplyId, int parentId) {
        this.isComment = isComment;
        this.toReplyId = toReplyId;
        this.parentId = parentId;
    }

    private void showInputPanel(boolean visible, String hint) {
        showInputPanel(visible, hint, false);
    }

    private void showInputPanel(boolean visible, String hint, boolean result) {
        if (visible) {
            if (detailBean != null) {
                if (inputFragment == null) {
                    inputFragment = PostInputFragment.newInstance().setUid(detailBean.uid).setonInputCallback(new PostInputFragment.OnInputCallback() {
                        @Override
                        public void onInputTrigger(String content) {
                            onSendTrigger(content);
                        }
                    });
                }
                String tag = PostInputFragment.class.getSimpleName() + detailBean.id;
                inputFragment.setInputHint(hint).show(getSupportFragmentManager(), tag);
            }
        } else {
            if (inputFragment != null) {
                if (result) {
                    inputFragment.setInputText(null);
                }
                inputFragment.dismissAllowingStateLoss();
            }
        }
    }

    private void shareData(ShareBean shareBean) {
        if (shareBean != null) {
            PopupManager.get().showOnQueue(new ShareDialog(activity).setShareData(shareBean).setAttachedData(reviewId, "review"));
        }
    }

    private void showBottomDialog(ReviewCommentBean.CommentItemBean bean, boolean canDelete, int parentId) {
        new BottomDialog(this)
                .setBottomData(canDelete)
                .setOnClickListener(new BottomDialog.OnClickListener() {
                    @Override
                    public void onReplyClick(BottomDialog dialog) {
                        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
                        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
                        dialog.dismiss();
                        onReplyTrigger(bean.id, bean.user_name, parentId);
                        //showInputPanel(true, String.format(getString(R.string.replying_to), bean.user_name));
                    }

                    @Override
                    public void onDeleteClick(BottomDialog dialog) {
                        dialog.dismiss();
                        showCenterDialog(reviewId, bean.id, parentId);
                    }
                })
                .show();
    }

    private void showVideoBottomDialog(ReviewCommentBean.CommentItemBean bean, boolean canDelete, int parentId) {
        String content = bean.getCurrentContent();
        new VideoBottomDialog(activity)
                .setBottomData(canDelete, bean.user_name + ": " + content, bean.user_id, bean.user_name, bean.reply_user_name)
                .setOnClickListener(new VideoBottomDialog.OnClickListener() {
                    @Override
                    public void onCopyClick(VideoBottomDialog dialog) {
                        dialog.dismiss();
                        Spanny copyS = CommentHelper.resolveStyle(CommentHelper.STYLE_COPY_UPLOAD, content, Color.parseColor("#999999"), false, null);
                        ShareHelper.copyLink(copyS.toString() + " ");
                    }

                    @Override
                    public void onReplyClick(VideoBottomDialog dialog) {
                        dialog.dismiss();
                        onReplyTrigger(bean.id, bean.user_name, parentId);
                    }

                    @Override
                    public void onDeleteClick(VideoBottomDialog dialog) {
                        dialog.dismiss();
                        showCenterDialog(reviewId, bean.id, parentId);
                    }

                    @Override
                    public void onReportClick(VideoBottomDialog dialog) {
                        dialog.dismiss();
                        onReportTrigger(bean.uid);
                    }

                    @Override
                    public void onRateTranslationClick(VideoBottomDialog dialog) {
                        dialog.dismiss();
                        onRateTranslationTrigger(String.valueOf(bean.id), bean.original_lang);
                    }
                })
                .show();
    }

    private void onRateTranslationTrigger(String commentId, String originalLang) {
        if (AccountManager.get().isLogin()) {
            String[] questions = {getString(R.string.rate_translation_first_question)
                    , getString(R.string.rate_translation_second_question)
                    , getString(R.string.rate_translation_third_question)
                    , getString(R.string.rate_translation_fourth_question)};
            List<RateTranslationBean> list = new ArrayList<>();
            for (int i = 0; i < questions.length; i++) {
                RateTranslationBean bean = new RateTranslationBean();
                bean.pos = i;
                bean.name = questions[i];
                list.add(bean);
            }
            String tag = PostRateTranslationFragment.class.getSimpleName() + commentId + "rate";
            PostRateTranslationFragment.newInstance("translation", "comment", commentId, list, originalLang)
                    .show(getSupportFragmentManager(), tag);
        } else {
            startActivity(AccountIntentCreator.getIntent(activity));
        }


    }

    private void onReportTrigger(String commentId) {
        if (AccountManager.get().isLogin()) {
            String[] questions = {getString(R.string.spam)
                    , getString(R.string.racist_language)
                    , getString(R.string.bullying)
                    , getString(R.string.nudity)
                    , getString(R.string.other)};
            List<RateTranslationBean> list = new ArrayList<>();
            for (int i = 0; i < questions.length; i++) {
                RateTranslationBean bean = new RateTranslationBean();
                bean.pos = i;
                bean.name = questions[i];
                list.add(bean);
            }
            String tag = PostRateTranslationFragment.class.getSimpleName() + commentId;
            PostRateTranslationFragment.newInstance("report", "comment", commentId, list, null)
                    .show(getSupportFragmentManager(), tag);
        } else {
            startActivity(AccountIntentCreator.getIntent(activity));
        }
    }

    private void showCenterDialog(int reviewId, int targetId, int parentId) {
        new CompatDialog(activity)
                .setTitleUp((dialog, view) -> {
                    dialog.dismiss();
                    viewModel.deleteReviewComment(reviewId, targetId, parentId);
                }, getString(R.string.delete_comment), getString(R.string.are_you_sure_you_want_to_delete_this_comment), getString(R.string.delete), getString(R.string.s_cancel))
                .show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case FOLLOWRESULT:
                    if (data != null && !EmptyUtils.isEmpty(detailBean)) {
                        boolean isFollow = data.getBooleanExtra("isFollow", false);
                        detailBean.social_status = isFollow ? "followed" : "unfollow";
                        for (int i = 0; i < adapter.getData().size(); i++) {
                            AdapterDataType item = adapter.getData().get(i);
                            if (item instanceof PostContentData) {
                                adapter.notifyItemChanged(i);
                            }
                        }
                    }
                    break;
            }

        }
    }

    private void reportImpressionEvent() {
        if (rvList != null) {
            rvList.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (eagleImpressionTrackerIml != null) {
                        eagleImpressionTrackerIml.trackImpression(rvList);
                    }
                }
            }, 0);
        }
    }

    private void showPrivilegeDialog() {
        if (detailBean == null) {
            return;
        }
        new ReviewDealRightDialog(activity).setData(detailBean.privilege).setOnClickListener(new ReviewDealRightDialog.OnDialogClickListener() {

            @Override
            public void onAdminClick() {
                startActivity(WebViewActivity.getIntent(activity, Constants.Url.POST_ADMIN));
            }

            @Override
            public void onReportClick() {
                if (AccountManager.get().isLogin()) {
                    onMoreReportTrigger();
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }
        }).show();
    }

    private void onMoreReportTrigger() {
        if (detailBean != null) {
            String[] questions = {getString(R.string.its_irrelevant_to_the_reviewed_product)
                    , getString(R.string.it_contains_photos_or_content_that_are_unoriginal)
                    , getString(R.string.its_inappropriate)
                    , getString(R.string.its_spam_review)
                    , getString(R.string.rate_translation_fourth_question)
            };
            List<RateTranslationBean> list = new ArrayList<>();
            for (int i = 0; i < questions.length; i++) {
                RateTranslationBean bean = new RateTranslationBean();
                bean.pos = i;
                bean.name = questions[i];
                list.add(bean);
            }
            String tag = PostRateTranslationFragment.class.getSimpleName() + detailBean.id + "report";
            PostRateTranslationFragment.newInstance("report", "review", String.valueOf(detailBean.id), list, getString(R.string.is_there_a_problem_with_the_review), getString(R.string.report_review), false)
                    .show(getSupportFragmentManager(), tag);
        }
    }

    public void setCommentCount(int commentCount) {
        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType adapterDataType = adapter.getData().get(i);
            if (adapterDataType instanceof PostBannerData) {
                PostBannerData item = (PostBannerData) adapterDataType;
                item.setCommentNum(commentCount);
                adapter.notifyItemChanged(i);
            }
        }
    }

    private void refreshProductOpNum() {
        if (layoutOp != null) {
            ProductSyncHelper.onPageResume(layoutOp, productId, productKey, min, max);
        }
        if (!EmptyUtils.isEmpty(adapter)) {
            ProductSyncHelper.onPageResume(adapter);
        }
    }

    private void setProductOpConfig() {
        refreshProductOpNum();
        SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(productId, productKey);
        num = item != null ? item.quantity : 0;
        boolean reachLimit = false;
        if (product != null) {
            min = product.min_order_quantity;
            max = product.getOrderMaxQuantity();
            volumeThreshold = product.getVolumeThreshold();
            sold_status = product.sold_status;
            reachLimit = OrderManager.get().isReachLimit(product);
        }
        layoutOp.dismissTips();
        if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(sold_status)) {
            //修改日期
            num = BottomOpLayout.TYPE_CHANGED_DATE;
            layoutOp.showTips(getString(R.string.s_product_change_date_tips));
        } else if (reachLimit) {
            //购买已达限量
            num = BottomOpLayout.TYPE_PURCHASED;
            layoutOp.showTips(getString(R.string.s_product_purchased_tips));
        } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(sold_status)) {
            //售罄
            boolean isCollect = CollectManager.get().isProductCollect(productId);
            num = isCollect ? BottomOpLayout.TYPE_REMINDED_YET : BottomOpLayout.TYPE_REMINDED;
            if (!isCollect) {
                if (product instanceof ProductDetailBean.ProductFeatureBean) {
                    if (!EmptyUtils.isEmpty(((ProductDetailBean.ProductFeatureBean) product).restockInfo)) {
                        layoutOp.showTips(((ProductDetailBean.ProductFeatureBean) product).restockInfo);
                    }
                }
            }
        }
        layoutOp.setOpStyle(num, min, max);
        boolean finalReachLimit = reachLimit;
        layoutOp.setOnCartActionListener(new BottomOpLayout.OnCartActionListener() {
            @Override
            public void onClickLeft(View view) {
                layoutOp.dismissReachedTips();
                ProductOpHelper.editProduct(false, productId, min, max, productKey, layoutOp, product,
                        ReviewDetailActivity.this, detail, cartSource, traceId, String.valueOf(productId));
            }

            @Override
            public void onClickRight(View view) {
                int lastNum = ProductOpHelper.getProductNum(productId, productKey);
                int currentNum = ProductOpHelper.editProductNum(true, productId, min, max, productKey, volumeThreshold);
                if (currentNum > 0 && lastNum == currentNum) {
                    layoutOp.showReachedTips(getString(R.string.s_qty_limit_reached));
                } else {
                    ProductOpHelper.editProduct(true, productId, min, max, productKey, layoutOp, product,
                            ReviewDetailActivity.this, detail, cartSource, traceId, String.valueOf(productId));
                }
            }

            @Override
            public void onClickCart(View view) {
                SharedViewModel.get().toCart();
                finish();
            }

            @Override
            public void onClickPanel(View view) {
                if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(sold_status)) {
                    bottomBtnClickTrack("product_change_date", EagleTrackEvent.ClickType.VIEW);
                    //修改日期
                    startActivity(DateActivity.getIntent(activity, String.valueOf(productId), "product modify me"));
                } else if (finalReachLimit) {
                    //购买已达限量
                } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(sold_status)) {
                    if (AccountManager.get().isLogin()) {
                        bottomBtnClickTrack("product_notify_me", EagleTrackEvent.ClickType.NORMAL);
                        boolean isCollected = CollectManager.get().isProductCollect(productId);
                        if (isCollected) {
                            //version 12.9 售罄 不允许取消收藏
                            return;
                        }
                        boolean isCollect = ProductOpHelper.fillCollect(productId, true, sold_status, layoutOp);
                        if (isCollect) {
                            //已收藏
                            showCollectTips();
                        }
                        updateProductCollect();
                    } else {
                        toLoginPage();
                    }
                } else { //正常商品
                    SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(productId, productKey);
                    int num = item != null ? item.quantity : 0;
                    if (num <= 0) {
                        ProductOpHelper.editProduct(true, productId, min, max, productKey, layoutOp, product,
                                ReviewDetailActivity.this, detail, cartSource, traceId, String.valueOf(productId));
                    }
                }
            }
        });
    }

    private void showCollectTips() {
        layoutOp.showAutoTips(getString(R.string.s_added_to_my_list), getString(R.string.s_view_up_case), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去收藏
                boolean toNext = toWebPage(AppConfig.HOST_WEB + Constants.Url.COLLECT);
                if (toNext) {
                    layoutOp.dismissAutoTips();
                }
            }
        });
    }

    private void toLoginPage() {
        startActivity(AccountIntentCreator.getIntent(activity));
    }

//    private void showGrouping(List<GroupProperty> propertyList, List<GroupProduct> productList, String tag) {
//        ProductGroupingFragment productGroupingFragment = ProductGroupingFragment
//                .newInstance(propertyList, productList, productId, cartSource)
//                .setOnGroupedCallback(new ProductGroupingFragment.OnGroupedCallback() {
//                    @Override
//                    public void onGrouped(int newProductId) {
//                        productId = newProductId;
//                        loadData();
//                    }
//
//                    @Override
//                    public void onDismissed(GroupProduct groupProduct, int num) {
//                        if (num >= 0) {
//                            ProductSyncHelper.onPageResume(layoutOp, groupProduct.product_id, null, groupProduct.min_order_quantity, groupProduct.max_order_quantity);
//                        }
//                    }
//
//                    @Override
//                    public void onProductStatusChanged() {
//
//                    }
//                });
//        productGroupingFragment.show(getSupportFragmentManager(), tag);
//        //tracking page sec imp
//        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
//                String.valueOf(productId), null, null, null, traceId);
//        for (GroupProperty property : propertyList) {
//            Map<String, Object> element = EagleTrackManger.get().getElement("group_feature", 1, property.property_id, propertyList.indexOf(property));
//            AppAnalytics.logPageSecImp(new EagleTrackModel.Builder()
//                    .addElement(element)
//                    .addContent(new TrackParams().put("default_nm", property.property_id_chosen).put("qty", property.property_value_list.size()).get())
//                    .addCtx(ctx)
//                    .build().getParams());
//        }
//    }

    private boolean toWebPage(String url) {
        startActivity(WebViewActivity.getIntent(activity, url));
        return true;
    }

    private void updateProductCollect() {
        if (!EmptyUtils.isEmpty(adapter.getData())) {
            for (int i = 0; i < adapter.getData().size(); i++) {
                if (adapter.getData().get(i) instanceof AdapterProductData) {
                    adapter.notifyItemChanged(i);
                    return;
                }
            }
        }
    }

    private void bottomBtnClickTrack(String targetType, String clickType) {
        if (product != null) {
            Map<String, Object> content = new TrackParams()
                    .put("prod_name", product.name)
                    .put("prod_id", product.id)
                    .put("price", product.price)
                    .put("sold_status", product.sold_status)
                    .put("is_pantry", product.is_pantry)
                    .put("is_limit_product", product.is_limit_product)
                    .put("is_sponsored", product.is_sponsored)
                    .put("is_hotdish", product.is_hotdish)
                    .get();
            MediaBean mediaBean = product.getFirstMedia();
            if (mediaBean != null && !TextUtils.isEmpty(mediaBean.media_url)) {
                content.put("media_url", mediaBean.media_url);
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(product.id), null, null, null, traceId);
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setIsMkpl(product.isSeller())
                    .setMod_nm(EagleTrackEvent.ModNm.PRODUCT_DETAIL)
                    .setMod_pos(0)
                    .setTargetNm(String.valueOf(product.id))
                    .setTargetPos(0)
                    .setTargetType(targetType)
                    .addContent(content)
                    .addCtx(ctx)
                    .setClickType(clickType)
                    .build().getParams());
        }
    }

    @Override
    public void onApplyToasterOptions(@NonNull IToaster<? extends IToasterOptions> toaster, @NonNull IToasterOptions options) {
        if (toaster.getType() == Toaster.TYPE_SNACK_BAR) {
            options.setContentMarginBottom(CommonTools.dp2px(72));
        }
    }
}
