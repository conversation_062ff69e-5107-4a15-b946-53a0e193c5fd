package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.IFrameUi;
import com.sayweee.weee.module.cart.bean.NewSectionBean;

import java.util.Map;

//凑单、bogo、promotion
public class SectionCartTipsData extends SimpleAdapterDataType implements IFrameUi {

    protected boolean isActivityTips;
    //凑单、换购数据
    public NewSectionBean.ShopMoreInfo shopMoreInfo;
    //活动数据
    public NewSectionBean.ActivityInfo activityInfo;

    public CartFrameUiData uiData = new CartFrameUiData();

    private String sectionType;
    private String vendorId;
    private int cartType;
    protected String cartId;
    private int activitySectionIndex;
    private int activityIndex;
    public Map<String, Object> element;
    public Map<String, Object> ctx;

    public SectionCartTipsData() {
        super(CartSectionType.TYPE_TIPS);
    }

    public SectionCartTipsData(int cartSectionType) {
        super(cartSectionType);
    }

    public void setCartType(int cartType) {
        this.cartType = cartType;
    }

    public int getCartType() {
        return cartType;
    }

    public SectionCartTipsData setShopMoreInfo(NewSectionBean.ShopMoreInfo shopMoreInfo) {
        this.isActivityTips = false;
        this.shopMoreInfo = shopMoreInfo;
        return this;
    }

    public SectionCartTipsData setActivityInfo(NewSectionBean.ActivityInfo activityInfo) {
        this.isActivityTips = true;
        this.activityInfo = activityInfo;
        return this;
    }

    public boolean isActivityTips() {
        return isActivityTips;
    }

    @Override
    public void setFrameUiData(CartFrameUiData uiData) {
        this.uiData = uiData;
    }

    @Override
    public CartFrameUiData getFrameUiData() {
        return uiData;
    }

    public SectionCartTipsData setTrackingInfo(Map<String, Object> element, Map<String, Object> ctx) {
        this.element = element;
        this.ctx = ctx;
        return this;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public SectionCartTipsData setCartId(String cartId) {
        this.cartId = cartId;
        return this;
    }

    public int getActivitySectionIndex() {
        return activitySectionIndex;
    }

    public SectionCartTipsData setActivitySectionIndex(int activityIndex) {
        this.activitySectionIndex = activityIndex;
        return this;
    }

    public int getActivityIndex() {
        return activityIndex;
    }

    public SectionCartTipsData setActivityIndex(int activityIndex) {
        this.activityIndex = activityIndex;
        return this;
    }

    public String getSectionType() {
        return sectionType;
    }

    public SectionCartTipsData setSectionType(String sectionType) {
        this.sectionType = sectionType;
        return this;
    }
}
