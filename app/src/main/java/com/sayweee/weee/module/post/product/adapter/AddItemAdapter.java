
package com.sayweee.weee.module.post.product.adapter;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  ycy
 * add item页面RV adapter
 * Desc:
 */
public class AddItemAdapter extends BaseQuickAdapter<SimpleProductBean, AdapterViewHolder> {

    private final List<SimpleProductBean> checkedList = new ArrayList<>();

    public AddItemAdapter(boolean displayGridStyle) {
        super(displayGridStyle ? R.layout.item_add_card_items : R.layout.item_add_items, null);
    }

    public void setAdapterData(List<SimpleProductBean> list) {
        setNewData(list);
    }

    public void setCheckedList(List<SimpleProductBean> list) {
        checkedList.clear();
        if (!EmptyUtils.isEmpty(list)) {
            checkedList.addAll(list);
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, SimpleProductBean bean) {
        helper.addOnClickListener(R.id.layout_product);
        ViewTools.setViewVisibilityIfChanged(
                helper.getView(R.id.line),
                helper.getLayoutPosition() < mData.size() - 1
        );
        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.get().getConvertUrl(SPEC_PRODUCT, bean.getHeadImageUrl()), R.mipmap.iv_product_placeholder);
        //product name
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        LabelHelper.setTitleLabel(tvProductName, bean.name, bean.is_pantry, false, bean.is_colding_package, 1, null);
        //price
        helper.getView(R.id.layout_volume).setVisibility(View.GONE);
        helper.getView(R.id.tv_price).setVisibility(View.INVISIBLE);
        boolean showBasePrice = bean.base_price > 0;
        if (bean.volume_price_support) {
            //Volume Pricing
            helper.setVisible(R.id.layout_volume, true);
            TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
            if (tvPriceVolume != null) {
                Spanny s = new Spanny()
                        .append(OrderHelper.formatUSMoney(bean.price), new TextAppearanceSpan(mContext, R.style.style_fluid_root_numeral_base))
                        .append(mContext.getString(R.string.s_volume_threshold_simple, bean.volume_threshold));
                helper.setText(R.id.tv_price_volume, s);
            }
            //Volume single price
            TextView tvPriceSingle = helper.getView(R.id.tv_price_single);
            if (tvPriceSingle != null) {
                helper.setText(R.id.tv_price_single
                        , (showBasePrice ? new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()).append(" ") : new Spanny())
                                .append(mContext.getString(R.string.s_volume_threshold_one_qty, OrderHelper.formatUSMoney(bean.volume_price))));
            }
        } else {
            helper.setVisible(R.id.tv_price, true);
            helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(bean.price));
        }
        //bundle或者hot dish
        helper.setGone(R.id.tv_special_tag, false);
        if (Constants.ProductType.BUNDLE.equals(bean.category)) {
            helper.setVisible(R.id.tv_special_tag, true);
            helper.setText(R.id.tv_special_tag, R.string.s_bundle_status);
        }
        if (Constants.ProductType.VALUE_HOT_DISH.equals(bean.is_hotdish)) {
            helper.setVisible(R.id.tv_special_tag, true);
            helper.setText(R.id.tv_special_tag, R.string.s_restaurant_status);
        }
        //是否勾选
        helper.setImageResource(R.id.iv_toggle, R.drawable.drawable_checkmark_circle_empty);
        for (SimpleProductBean productBean : checkedList) {
            if (productBean.id == bean.id) {
                helper.setImageResource(R.id.iv_toggle, R.drawable.drawable_checkmark_circle_filled);
            }
        }
        helper.setVisible(R.id.tv_sold_out, ProductView.isSoldOut(bean.sold_status));

        //vendor info
        TextView tvVendor = helper.getView(R.id.tv_vender);
        if (tvVendor != null) {
            String vendorDeliveryDesc = bean.getVendorDeliveryNewDesc(bean.freeShippingDescShow(bean));
            boolean showVendor = !EmptyUtils.isEmpty(vendorDeliveryDesc);
            tvVendor.setVisibility(showVendor ? View.VISIBLE : View.GONE);
            if (showVendor) {
                tvVendor.setText(ViewTools.fromHtml(vendorDeliveryDesc, (view, url) -> {
                    //无点击事件
                }));
            }
        }
    }
}
