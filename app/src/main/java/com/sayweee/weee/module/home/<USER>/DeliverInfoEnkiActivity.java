package com.sayweee.weee.module.home.zipcode;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.collection.ArrayMap;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.checkout.DeliveryAddressEditActivity;
import com.sayweee.weee.module.dialog.AddressTipDialog;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.ConflictProductDialog;
import com.sayweee.weee.module.home.date.bean.ChangeDateBean;
import com.sayweee.weee.module.home.date.bean.DateBean;
import com.sayweee.weee.module.home.zipcode.adapter.DeliverInfoEnkiAdapter;
import com.sayweee.weee.module.home.zipcode.bean.AddressChangedResult;
import com.sayweee.weee.module.home.zipcode.bean.AddressEnkiBean;
import com.sayweee.weee.module.home.zipcode.bean.AddressesBean;
import com.sayweee.weee.module.home.zipcode.service.LocationViewModel;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.mkpl.common.SimpleVeilData;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.listener.OnDialogClickListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * Author:  ycy
 * Desc: enki delivery address and date
 */
public class DeliverInfoEnkiActivity extends WrapperMvvmActivity<LocationViewModel> {

    RecyclerView rvList;
    DeliverInfoEnkiAdapter adapter;
    String dateSelect;
    private static final String KEY_SOURCE = "source";
    private String source;
    private final List<AddressesBean> addresses = new ArrayList<>();
    public static final String KEY_CHANGE = "change";
    private String editAddressId;

    public static Intent getIntent(Context context, String source) {
        return new Intent(context, DeliverInfoEnkiActivity.class).putExtra(KEY_SOURCE, source);
    }

    public static Intent getIntent(Context context) {
        return new Intent(context, DeliverInfoEnkiActivity.class);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_deliver_info_enki;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        source = getIntent().getStringExtra(KEY_SOURCE);
        rvList = findViewById(R.id.rv_list);
        setOnClickListener(R.id.iv_back, this::click);
        setWrapperDivider(null);
        getWrapperTitle().setVisibility(View.GONE);
        initRv();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (AccountManager.get().isLogin()) {
            viewModel.getMyAddresses();
        }
        AppAnalytics.logPageView(WeeeEvent.PageView.CHANGE_DELIVERY_OPTION, this);
    }

    @Override
    public void loadData() {
        adapter.addData(new SimpleVeilData(0));
        if (!AccountManager.get().isLogin()) {
            AddressEnkiBean addressEnkiBean = new AddressEnkiBean();
            SimplePreOrderBean preOrder = OrderManager.get().getSimplePreOrder();
            if (preOrder != null) {
                addressEnkiBean.orderAddress = preOrder.addr_zipcode + " - " + preOrder.addr_city;
            }
            adapter.setAddressData(addressEnkiBean);
        }
        viewModel.getDeliveryDate(false);
    }

    @Override
    public void attachModel() {
        //刷新我的地址模块
        viewModel.addressData.observe(this, new Observer<List<AddressesBean>>() {
            @Override
            public void onChanged(List<AddressesBean> addresses) {
                DeliverInfoEnkiActivity.this.addresses.clear();
                if (CommonTools.isNotEmptyList(addresses)) {
                    DeliverInfoEnkiActivity.this.addresses.addAll(addresses);
                    AddressEnkiBean addressEnkiBean = new AddressEnkiBean();
                    addressEnkiBean.addresses = addresses;
                    adapter.setAddressData(addressEnkiBean);
                    if (!EmptyUtils.isEmpty(editAddressId)) {
                        for (AddressesBean bean : addresses) {
                            if (bean.id.equalsIgnoreCase(editAddressId)) {
                                showPopUp(bean);
                                editAddressId = "";
                                return;
                            }
                        }
                    }
                } else {
                    //已登录，接口无地址
                    AddressEnkiBean addressEnkiBean = new AddressEnkiBean();
                    SimplePreOrderBean preOrder = OrderManager.get().getSimplePreOrder();
                    if (preOrder != null) {
                        addressEnkiBean.orderAddress = preOrder.addr_zipcode + " - " + preOrder.addr_city;
                    }
                    adapter.setAddressData(addressEnkiBean);
                }
            }
        });

        viewModel.dateData.observe(this, new Observer<DateBean>() {
            @Override
            public void onChanged(DateBean bean) {
                List<DateBean.DeliveryBean> deliveryBeans = bean.delivery;
                if (CommonTools.isNotEmptyList(deliveryBeans)) {
                    adapter.setDateData(deliveryBeans);
                }
            }
        });
        viewModel.changeDateData.observe(this, new Observer<ChangeDateBean>() {
            @Override
            public void onChanged(ChangeDateBean bean) {
                new ConflictProductDialog(activity)
                        .setData(bean.effect_products, bean.effect_all_products)
                        .setOnClickListener(new ConflictProductDialog.OnClickListener() {
                            @Override
                            public void onClick(Dialog dialog, boolean isConfirm) {
                                dialog.dismiss();
                                if (isConfirm) {
                                    viewModel.forceChangeDate(source);
                                } else {
                                    if (!OrderManager.get().isLackOfOrder()) {
                                        adapter.setDate(OrderManager.get().getDeliveryPickupDate());//还原之前日期
                                    }
                                    Map<String, Object> map = new ArrayMap<>();
                                    map.put("source", source);
                                    EagleTrackManger.get().trackEagleInfoUpdate(EagleTrackEvent.InfoName.DELIVERY_DATE,
                                            EagleTrackEvent.ActionType.CHANGE,
                                            false,
                                            null,
                                            dateSelect,
                                            map);
                                }
                            }
                        }).show();
            }
        });
        viewModel.addressConflictData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String addressId) {
                for (AddressesBean address : addresses) {
                    if (addressId.equals(address.id)) {
                        showLocationConflictTips(addressId, address.addr_zipcode);
                        break;
                    }
                }
            }
        });
        viewModel.addressFailureData.observe(this, new Observer<Map<String, FailureBean>>() {
            @Override
            public void onChanged(Map<String, FailureBean> map) {
                for (Map.Entry<String, FailureBean> entry : map.entrySet()) {
                    FailureBean bean = entry.getValue();
                    if (bean != null && AddressHelper.handlerTooFarError(activity, bean.getMessageId())) {
                        break;
                    }
                    FailureBean value = entry.getValue();
                    if (value != null) {
                        showTipsDialog(value.getMessage());
                    }
                }
            }
        });
        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                showTipsDialog(failureBean.getMessage());
            }
        });
        viewModel.finishData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isFinish) {
                finish();
            }
        });

        SharedViewModel.get().addressChangedResultData.observe(this, new Observer<AddressChangedResult>() {
            @Override
            public void onChanged(AddressChangedResult result) {
                if (result.fromType != AddressHelper.Type.TYPE_EDIT) {
                    finish();
                } else {
                    if (adapter != null && !adapter.getSelectAddressId().equalsIgnoreCase(result.addressId)) {
                        editAddressId = result.addressId;
                    }
                }
            }
        });
    }

    private void showPopUp(AddressesBean address) {
        new AddressTipDialog(activity)
                .setData(address, KEY_CHANGE)
                .setOnClickListener(new AddressTipDialog.OnClickListener() {
                    @Override
                    public void onClick(Dialog dialog, boolean isConfirm) {
                        dialog.dismiss();
                        if (isConfirm) {
                            viewModel.changeAddressId(address.id, false, source);
                        }
                    }
                }).show();
    }

    private void initRv() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setOrientation(RecyclerView.VERTICAL);
        rvList.setLayoutManager(layoutManager);
        adapter = new DeliverInfoEnkiAdapter(this);
        rvList.setAdapter(adapter);
        adapter.setOnClickListener(new DeliverInfoEnkiAdapter.OnClickListener() {
            @Override
            public void changeDate(String date) {
                viewModel.changeDate(date, false, source);
                dateSelect = date;
            }

            @Override
            public void changeAddressId(String id) {
                viewModel.changeAddressId(id, false, source);
            }

            @Override
            public void edit(AddressesBean item) {
                startActivity(DeliveryAddressEditActivity.getIntent(activity, AddressHelper.Type.TYPE_EDIT, false, item, null, WeeeAnalytics.get().getCleanRefererPageKey()));
            }

            @Override
            public void goToSearch() {
                startActivity(SearchAddressEnkiActivity.getIntent(activity, source));
            }
        });
    }

    public void click(View view) {
        int viewId = view.getId();
        if (viewId == R.id.iv_back) {
            finish();
        }
    }

    private void showLocationConflictTips(String addressId, String zipcode) {
        String title = String.format(getString(R.string.s_storefront_unavailable), StoreManager.get().getStoreName(), zipcode);
        String cancel = String.format(getString(R.string.s_take_me_back), OrderManager.get().getZipCode());
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setTitleUp(new OnDialogClickListener() {
                                @Override
                                public void onClick(WrapperDialog dialog, View view) {
                                    viewModel.changeAddressId(addressId, true, source);
                                }
                            }, title
                        , getString(R.string.s_would_you_like_to_browse_another_store)
                        , getString(R.string.s_show_me_other_stores)
                        , cancel)
                .show();
    }

    private void showTipsDialog(String message) {
        if (message != null && message.length() > 0) {
            new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                    .setUp(null, message, activity.getString(R.string.sure))
                    .show();
        }
    }
}
