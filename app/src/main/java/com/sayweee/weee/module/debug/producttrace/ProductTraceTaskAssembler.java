package com.sayweee.weee.module.debug.producttrace;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;

import java.util.List;

public interface ProductTraceTaskAssembler {

    <T> void addAll(Iterable<T> iterable);

    <T> void add(T item);

    @NonNull
    List<ProductTraceTask> assemble(String topic, String pageKey);

    static ProductTraceTaskAssembler create() {
        if (ProductTraceManager.get().isEnabled()) {
            return new ProductTraceTaskAssemblers.RealImpl();
        } else {
            return new ProductTraceTaskAssemblers.NoOpImpl();
        }
    }

}
