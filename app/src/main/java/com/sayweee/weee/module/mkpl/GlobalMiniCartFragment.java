package com.sayweee.weee.module.mkpl;

import android.animation.FloatEvaluator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.LinearLayout;
import android.widget.Space;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.motion.widget.MotionLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.util.Pair;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import androidx.transition.Fade;
import androidx.transition.TransitionManager;
import androidx.transition.TransitionSet;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentGlobalMiniCartBinding;
import com.sayweee.weee.databinding.LayoutGlobalMiniCartBarBinding;
import com.sayweee.weee.databinding.LayoutGlobalMiniCartBarFloatBinding;
import com.sayweee.weee.databinding.LayoutSellerBottomPromotionBarBinding;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.checkout.SectionAlcoholAgreementActivity;
import com.sayweee.weee.module.mkpl.bean.GlobalCartBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartBrief;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListRequest;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.mkpl.bean.GlobalCartRecommendResponse;
import com.sayweee.weee.module.mkpl.bean.GlobalMiniCartAction;
import com.sayweee.weee.module.mkpl.common.MiniCartItemData;
import com.sayweee.weee.module.mkpl.common.SimpleVeilData;
import com.sayweee.weee.module.mkpl.view.GlobalCartBriefListView;
import com.sayweee.weee.module.mkpl.view.GlobalCartBriefView;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.seller.SellerPageParams;
import com.sayweee.weee.module.seller.bean.SellerGroupStatusBean;
import com.sayweee.weee.module.seller.bean.SellerPromotionDetailBean;
import com.sayweee.weee.module.seller.sheet.SellerPromotionDetailFragment;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.IntentTools;
import com.sayweee.weee.utils.ObjectUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.function.Transform;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.weee.widget.HorizontalScrollViewCompat;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.snackbar.ToastySnackBarView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.snackbar.SnackBarOptions;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.core.view.WrapperMvvmFragment;
import com.sayweee.wrapper.utils.Spanny;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

public class GlobalMiniCartFragment extends WrapperMvvmFragment<GlobalMiniCartViewModel> {

    private static final String EXTRA_MODE = "EXTRA_MODE";
    private static final String EXTRA_SELLER_ID = "EXTRA_SELLER_ID";
    private static final String EXTRA_CART_DATA = "EXTRA_CART_DATA";
    private static final String EXTRA_CART_LIST_DATA = "EXTRA_CART_LIST_DATA";
    private static final String EXTRA_IN_TAB = "EXTRA_IN_TAB";

    private static final String MODE_NORMAL = "normal";
    private static final String MODE_FLOAT = "float";

    private static final long SWITCH_CART_BAR_DURATION = 300L;

    public static final String REQUEST_KEY = "global_mini_cart";
    public static final String RESULT_BUNDLE_DISMISS = "dismiss";
    public static final String RESULT_BUNDLE_CHECKOUT = "checkout";
    public static final String RESULT_BUNDLE_GROUP_ORDER = "groupOrder";

    private SellerPageParams sellerPageParams;

    public static int getEnterAnim() {
        return androidx.appcompat.R.anim.abc_slide_in_bottom;
    }

    public static int getExitAnim() {
        return androidx.appcompat.R.anim.abc_slide_out_bottom;
    }

    public static String getFragmentTag(String sellerId) {
        return "com.sayweee.weee.module.mkpl.GlobalMiniCartFragment-" + sellerId;
    }

    public static GlobalMiniCartFragment getFragment(
            @Nullable String sellerId,
            @Nullable GlobalCartBean initialData,
            @Nullable SellerPageParams sellerParams) {
        GlobalMiniCartFragment fragment = new GlobalMiniCartFragment();
        Bundle arguments = new Bundle();
        arguments.putString(EXTRA_SELLER_ID, sellerId);
        arguments.putSerializable(EXTRA_CART_DATA, initialData);
        arguments.putString(EXTRA_MODE, EmptyUtils.isEmpty(sellerId) ? MODE_FLOAT : MODE_NORMAL);
        arguments.putParcelable(SellerPageParams.EXTRA_SELLER_PARAMS, sellerParams);
        fragment.setArguments(arguments);
        return fragment;
    }

    public static GlobalMiniCartFragment getFragment(@Nullable GlobalCartListBean initialData, boolean isInTab) {
        GlobalMiniCartFragment fragment = new GlobalMiniCartFragment();
        Bundle arguments = new Bundle();
        arguments.putSerializable(EXTRA_CART_LIST_DATA, initialData);
        arguments.putString(EXTRA_MODE, MODE_FLOAT);
        arguments.putBoolean(EXTRA_IN_TAB, isInTab);
        fragment.setArguments(arguments);
        return fragment;
    }

    private FragmentGlobalMiniCartBinding binding;
    private BottomSheetBehavior<View> miniCartBehavior;

    private GlobalMiniCartAdapter adapter;

    private final GlobalOnCartEditListener onCartEditListener = new GlobalOnCartEditListener(
            new GlobalOnCartEditListener.SimpleOnGlobalCartUpdateListener() {

                @Override
                public void onGlobalCartItemPrepareEdit(@NonNull Object item) {
                    if (item instanceof MiniCartItemData) {
                        int cartItemCount = viewModel.getCartCachedBean() != null ? viewModel.getCartCachedBean().getItemCount() : 0;
                        int totalItemCount = adapter.getItemCount();
                        if (cartItemCount > 0 && cartItemCount < totalItemCount) {
                            adapter.notifyItemRangeChanged(cartItemCount + 1, totalItemCount - cartItemCount, new GlobalMiniCartAction.Collapse(-1));
                        }
                    } else if (item instanceof AdapterProductData) {
                        onCartEditListener.resetEditData();
                        int productId = ((AdapterProductData) item).getProductId();
                        int cartItemCount = viewModel.getCartCachedBean() != null ? viewModel.getCartCachedBean().getItemCount() : 0;
                        if (cartItemCount != 0) {
                            adapter.notifyItemRangeChanged(0, cartItemCount, new GlobalMiniCartAction.Collapse(productId));
                        }
                    }
                }

                @Override
                public void onGlobalCartItemDataRemove(@NonNull AdapterDataType item) {
                    Pair<Integer, AdapterDataType> pair = CollectionUtils.firstOrNullWithIndex(adapter.getData(), it -> it == item);
                    if (pair != null) {
                        adapter.remove(pair.first);
                    }
                }

                @Override
                public void onGlobalCartListUpdate(@NonNull GlobalCartListResponse response) {
                    if (!GlobalMiniCartFragment.this.getClass().getName().equals(response.getRequest().getToken()))
                        return;
                    if (!response.isSuccess(false)) return;

                    String requestSellerId = response.getRequest().getRequestSellerId();
                    if (requestSellerId == null) return;

                    GlobalCartListBean cartListBean = response.getResponse();
                    GlobalCartBean cart = cartListBean != null ? cartListBean.getGlobalCartBean(requestSellerId) : null;

                    GlobalCartBean fakeCart = cart;
                    if (fakeCart == null) {
                        fakeCart = new GlobalCartBean();
                        fakeCart.quantity = 0;
                        GlobalCartBean.VendorInfo vendorInfo = new GlobalCartBean.VendorInfo();
                        vendorInfo.vendor_id = DecimalTools.intValue(requestSellerId, 0);
                        fakeCart.vendor_info = vendorInfo;
                    }
                    updateCartBarFloatAfterModify(fakeCart);

                    String selectedSellerId = getSelectedSellerId();
                    if (!Objects.equals(requestSellerId, selectedSellerId)) return;
                    if (cart != null && !cart.isEmpty()) {
                        updateCartData(cart);
                    } else {
                        if (isFloatMode()) {
                            collapseCart();
                        } else {
                            dismissCart();
                        }
                    }
                }
            });


    private OnBackPressedCallback onBackPressedCallback;
    private boolean inAnimation = false;
    private boolean isExpended = false;

    @Nullable
    private String getExtraSellerId() {
        return getArguments() != null ? getArguments().getString(EXTRA_SELLER_ID) : null;
    }

    @NonNull
    private String getExtraMode() {
        return getArguments() != null ? getArguments().getString(EXTRA_MODE, MODE_NORMAL) : MODE_NORMAL;
    }

    private boolean isInTab() {
        return getArguments() != null && getArguments().getBoolean(EXTRA_IN_TAB, false);
    }

    private boolean isFloatMode() {
        return MODE_FLOAT.equals(getExtraMode());
    }

    @Nullable
    private FragmentManager safeGetParentFragmentManager() {
        try {
            return getParentFragmentManager();
        } catch (Exception ignored) {
            return null;
        }
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_global_mini_cart;
    }

    @SuppressWarnings("unchecked")
    @Override
    public GlobalMiniCartViewModel createModel() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            return ViewModelProviders.of(activity).get(GlobalMiniCartViewModel.class);
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        GlobalCartBean initialData;
        initialData = IntentTools.getSerializable(getArguments(), EXTRA_CART_DATA, GlobalCartBean.class);
        updateCartData(initialData);

        GlobalCartListBean initialListData;
        initialListData = IntentTools.getSerializable(getArguments(), EXTRA_CART_LIST_DATA, GlobalCartListBean.class);
        updateCartListData(initialListData);

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;
        viewModel.getGlobalCartListResponseLiveData().observe(viewLifecycleOwner, this::handleGlobalCartResponse);
        viewModel.getGlobalCartRecommendResponseLiveData().observe(viewLifecycleOwner, this::handleGlobalCartRecommendResponse);
        viewModel.getSellerGroupStatusLiveData().observe(viewLifecycleOwner, this::handleSellerGroupStatusLiveData);
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        binding = FragmentGlobalMiniCartBinding.bind(contentView);

        initRecyclerView(view.getContext());
        initMiniCart(view.getContext());
        initCartBarFloat(view.getContext());
        initListener();

        if (isFloatMode()) {
            binding.llStoreButton.setVisibility(View.VISIBLE);
            binding.clCartBarFloatRoot.setVisibility(View.VISIBLE);
            binding.clCartBar.getRoot().setVisibility(View.GONE);
        } else {
            binding.llStoreButton.setVisibility(View.GONE);
            binding.clCartBarFloatRoot.setVisibility(View.GONE);
            binding.clCartBar.getRoot().setVisibility(View.VISIBLE);
        }
        binding.llPromotionBarRoot.setVisibility(View.GONE);
    }

    private void initRecyclerView(Context context) {
        onCartEditListener.setToken(getClass().getName());
        onCartEditListener.setTargetSellerId(getExtraSellerId());
        adapter = new GlobalMiniCartAdapter(onCartEditListener);
        View footerView = new Space(context);
        footerView.setLayoutParams(
                new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        CommonTools.dp2px(160)
                )
        );
        adapter.setFooterView(footerView);
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @SuppressWarnings("rawtypes")
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                handleChildClick(adapter, view, position);
            }
        });
        binding.rcvMiniCart.setAdapter(adapter);

        RecyclerView.LayoutManager layoutManager;
        layoutManager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        binding.rcvMiniCart.setLayoutManager(layoutManager);
        binding.rcvMiniCart.addItemDecoration(new SimpleSectionItemDecoration());
        binding.rcvMiniCart.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
                if (oldState == RecyclerView.SCROLL_STATE_IDLE && newState != RecyclerView.SCROLL_STATE_IDLE) {
                    onCartEditListener.resetEditData();
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                final BottomSheetBehavior<View> behavior = miniCartBehavior;
                if (behavior == null) return;
                boolean isTop = !recyclerView.canScrollVertically(-1);
                if (isTop) {
                    recyclerView.postDelayed(() -> behavior.setDraggable(true), 50);
                } else {
                    behavior.setDraggable(false);
                }
            }
        });
    }

    @SuppressWarnings("rawtypes")
    private void handleChildClick(BaseQuickAdapter adapter, View view, int position) {
        int viewId = view.getId();
        if (viewId == R.id.layout_cart_item_root) { // cart item
            Object item = adapter.getItem(position);
            if (item instanceof MiniCartItemData) {
                MiniCartItemData productData = (MiniCartItemData) item;

                Map<String, Object> ctx = new EagleContext().setPageTarget(getExtraSellerId()).asMap();
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(EagleTrackEvent.ModNm.MKPL_CART_ITEM)
                        .setMod_pos(0)
                        .setTargetNm(String.valueOf(productData.t.product_id))
                        .setTargetPos(productData.prodPos)
                        .setTargetType("product")
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .addCtx(ctx)
                        .build().getParams()
                );
                leavePage();
                gotoPdpPage(productData);
            }
        } else if (viewId == R.id.ll_mini_cart_action_add_items) { // add items
            leavePage();
            gotoSellerPage();
        } else if (viewId == R.id.ll_mini_cart_action_group_order) { // group order
            prepareShowGroupOrderDialog();
        }
    }

    private void initMiniCart(Context context) {
        miniCartBehavior = BottomSheetBehavior.from(binding.clMiniCart);
        miniCartBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
        miniCartBehavior.setExpandedOffset(ScreenUtils.getStatusBarHeight(context));
        miniCartBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {

            private final FloatEvaluator evaluator = new FloatEvaluator();
            private static final float TOTAL = 2f;

            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    isExpended = false;
                    collapseCartStep2();
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                float offset = 1 + slideOffset;
                float fraction = offset / TOTAL;
                float alpha = ((int) (evaluator.evaluate(fraction, 0, 1) * 100f)) / 100f;
                binding.vBackground.setAlpha(alpha);
            }
        });

        binding.clCartBar.clActionButton.removeTransitionListener(actionButtonTransitionListener);
        binding.clCartBar.clActionButton.addTransitionListener(actionButtonTransitionListener);
    }

    private final MotionLayout.TransitionListener actionButtonTransitionListener =
            new MotionLayout.TransitionListener() {
                @Override
                public void onTransitionStarted(MotionLayout motionLayout, int startId, int endId) {
                    inAnimation = true;
                }

                @Override
                public void onTransitionChange(MotionLayout motionLayout, int startId, int endId, float progress) {
                    // do nothing
                }

                @Override
                public void onTransitionCompleted(MotionLayout motionLayout, int currentId) {
                    inAnimation = false;
                }

                @Override
                public void onTransitionTrigger(MotionLayout motionLayout, int triggerId, boolean positive, float progress) {
                    // do nothing
                }
            };

    private void initCartBarFloat(Context context) {
        // change bottom margin for float cart bar
        CoordinatorLayout.LayoutParams clCartBarFloatRootLayoutParams;
        clCartBarFloatRootLayoutParams = (CoordinatorLayout.LayoutParams) binding.clCartBarFloatRoot.getLayoutParams();
        if (isInTab()) {
            clCartBarFloatRootLayoutParams.bottomMargin =
                    context.getResources().getDimensionPixelOffset(R.dimen.default_tab_height);
        } else {
            clCartBarFloatRootLayoutParams.bottomMargin =
                    context.getResources().getDimensionPixelOffset(R.dimen.sw_30dp);
        }

        final LayoutGlobalMiniCartBarFloatBinding b = binding.clCartBarFloat;
        int clCartInfoWidth = ScreenUtils.getScreenWidth(context)
                - CommonTools.dp2px(20) * 2 // horizontal side
                - CommonTools.dp2px(62); // cart icon
        MotionLayout motionLayout = b.getRoot();
        motionLayout.getConstraintSet(R.id.start).getConstraint(R.id.cl_cart_bar_info).layout.mWidth = clCartInfoWidth;

        final HorizontalScrollViewCompat scrollView = b.scrollView;
        scrollView.setOnScrollChangeListenerCompat(this::onCartBarFloatScrollChanged);

        GlobalCartBriefListView.OnGlobalCartBriefListUpdateListener onGlobalCartBriefListUpdateListener;
        onGlobalCartBriefListUpdateListener = new GlobalCartBriefListView.OnGlobalCartBriefListUpdateListener() {
            @Override
            public void onGlobalCartBriefListUpdate(@NonNull GlobalCartBriefListView view, @Nullable View targetView) {
                long delay = b.llCartBriefList.getLayoutTransitionDuration();
                if (targetView != null) {
                    scrollView.postDelayed(
                            () -> scrollView.smoothScrollTo((int) targetView.getX(), 0),
                            delay
                    );
                }
                scrollView.postDelayed(() -> onCartBarFloatScrollChanged(scrollView, 0, 0, 0, 0), delay + 200L);
            }

            @Override
            public void onGlobalCartBriefViewClick(@Nullable GlobalCartBriefView targetView, int targetViewPosition) {
                GlobalCartBrief data = targetView != null ? targetView.getData() : null;
                String sellerId = data != null ? data.getSellerId() : null;
                GlobalMiniCartFragment.this.onSelectGlobalCartBrief(sellerId, targetViewPosition);
            }
        };
        b.llCartBriefList.setOnGlobalCartBriefListUpdateListener(onGlobalCartBriefListUpdateListener);

        ViewTools.setViewOnSafeClickListener(binding.clCartBarFloat.ivCartBarArrowLeft, v -> {
            int itemWidth = CommonTools.dp2px(51);
            int scrollX = scrollView.getScrollX();
            int targetX = ((scrollX / itemWidth) - 1) * itemWidth;
            scrollView.smoothScrollBy(targetX - scrollX - CommonTools.dp2px(1), 0);
        });

        ViewTools.setViewOnSafeClickListener(binding.clCartBarFloat.ivCartBarArrowRight, v -> {
            int itemWidth = CommonTools.dp2px(51);
            int scrollX = scrollView.getScrollX();
            int targetX = ((scrollX / itemWidth) + 1) * itemWidth;
            scrollView.smoothScrollBy(targetX - scrollX + CommonTools.dp2px(1), 0);
        });
    }

    private void onCartBarFloatScrollChanged(@NonNull HorizontalScrollViewCompat scrollView, int l, int t, int oldLeft, int oldTop) {
        boolean mostLeft = scrollView.canScrollHorizontally(-1);
        boolean mostRight = scrollView.canScrollHorizontally(1);
        binding.clCartBarFloat.ivCartBarArrowLeft.setAlpha(mostLeft ? 1f : .3f);
        binding.clCartBarFloat.ivCartBarShadowLeft.setAlpha(mostLeft ? 1f : 0f);
        binding.clCartBarFloat.ivCartBarArrowRight.setAlpha(mostRight ? 1f : .3f);
        binding.clCartBarFloat.ivCartBarShadowRight.setAlpha(mostRight ? 1f : 0f);
    }

    private void initListener() {
        ViewTools.setViewOnSafeClickListener(binding.ivClose, v -> collapseCart());
        ViewTools.setViewOnSafeClickListener(binding.clCartBar.getRoot(), v -> {
            if (isFloatMode()) {
                collapseCart();
            } else {
                if (getActionButtonState() == R.id.start) {
                    expendCart(getExtraSellerId(), 0, /* forceRefresh= */false);
                } else {
                    collapseCart();
                }
            }
        });
        ViewTools.setViewOnSafeClickListener(binding.clCartBar.clActionButton, v -> {
            if (getActionButtonState() == R.id.start) {
                expendCart(getExtraSellerId(), 0, /* forceRefresh= */false);
            } else {
                leavePage();
                gotoCheckoutPage();
            }
        });
        ViewTools.setViewOnSafeClickListener(binding.llStoreButton, v -> {
            leavePage();
            gotoSellerPage();
        });
    }

    private void initOnBackPressedCallback() {
        final AppCompatActivity activity = (AppCompatActivity) getActivity();
        if (activity == null) return;
        if (onBackPressedCallback != null) {
            onBackPressedCallback.setEnabled(false);
            onBackPressedCallback.remove();
        }

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                SellerPromotionDetailFragment fragment;
                fragment = getSellerPromotionDetailFragment();
                if (fragment != null) {
                    dismissPromotionDetailSheet();
                } else if (isExpended()) {
                    collapseCart();
                } else {
                    AppCompatActivity a = (AppCompatActivity) getActivity();
                    if (a != null) {
                        setEnabled(false);
                        a.getOnBackPressedDispatcher().onBackPressed();
                    }
                }
            }
        };
        activity.getOnBackPressedDispatcher().addCallback(viewLifecycleOwner, callback);
        this.onBackPressedCallback = callback;
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (isExpended()) {
            initOnBackPressedCallback();
            AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_MINI_CART, this);
            if (isFloatMode()) {
                String sellerId = getSelectedSellerId();
                if (sellerId != null) {
                    refreshData(sellerId, true);
                }
            }
        }
    }

    @Override
    public void loadData() {
    }

    private void refreshData(@NonNull String sellerId, boolean refreshCart) {
        if (refreshCart) {
            viewModel.getSellerCartFloat(getClass().getName(), /* sellerId= */sellerId);
        }
        GlobalCartRecommendResponse oldCartRecommendResponse = viewModel.getGlobalCartRecommendResponseLiveData().getValue();
        if (oldCartRecommendResponse == null
                || !Objects.equals(oldCartRecommendResponse.getRequest().getSellerId(), sellerId)) {
            viewModel.getSellerCartRecommend(sellerId);
        } else {
            handleGlobalCartRecommendResponse(oldCartRecommendResponse);
        }
    }

    private int getActionButtonState() {
        return binding.clCartBar.clActionButton.getCurrentState();
    }

    private void expendCart(@Nullable String targetSellerId, int position, boolean forceRefresh) {
        String sellerId = !EmptyUtils.isEmpty(targetSellerId) ? targetSellerId : getExtraSellerId();
        if (sellerId == null || sellerId.isEmpty()) return;
        if (inAnimation) return;
        if (getActionButtonState() != R.id.start) return;
        isExpended = true;
        updatePromotionBarVisibility();
        dismissPromotionDetailSheet();
        miniCartBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        expendActionButton();
        binding.rcvMiniCart.scrollToPosition(0);
        viewModel.notifyGlobalMiniCartPageStateLiveDataChange(
                GlobalMiniCartAction.PageState.expended(sellerId, position)
        );

        GlobalCartBean cart = GlobalCartListBean.getGlobalCartBean(viewModel.getCartListCachedBean(), sellerId);
        if (cart != null) {
            updateCartData(cart);
        }
        boolean refreshCart = forceRefresh || cart == null || cart.isEmpty();
        if (refreshCart) {
            showLoadingVeil(true);
        }
        refreshData(sellerId, refreshCart);

        initOnBackPressedCallback();
        AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_MINI_CART, this);
        adapter.onPageResume(binding.rcvMiniCart);
    }

    private void collapseCart() {
        if (inAnimation) return;
        if (getActionButtonState() != R.id.end) return;
        isExpended = false;
        miniCartBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
        collapseCartStep2();
    }

    private void collapseCartStep2() {
        updatePromotionBarVisibility();
        dismissPromotionDetailSheet();
        collapseActionButton();
        viewModel.setSelectedSellerId(null);
        updateCartTitle(null);
        adapter.setAdapterData(null);
        onCartEditListener.resetEditData();
        clearRecommendItems();
        if (isFloatMode()) {
            showCartBarFloatWithAnim(/* isShow= */true);
        }
        adapter.onPagePause(binding.rcvMiniCart);
        viewModel.notifyGlobalMiniCartPageStateLiveDataChange(
                GlobalMiniCartAction.PageState.collapsed()
        );
    }

    public void dismissCart() {
        collapseCart();
        binding.getRoot().postDelayed(() -> {
            ObjectUtils.let(
                    safeGetParentFragmentManager(),
                    fm -> fm.beginTransaction()
                            .setCustomAnimations(GlobalMiniCartFragment.getEnterAnim(), GlobalMiniCartFragment.getExitAnim())
                            .remove(GlobalMiniCartFragment.this)
                            .commitAllowingStateLoss()
            );
            notifyDismissed();
        }, 300L);
    }

    private void expendActionButton() {
        binding.clCartBar.clActionButton.transitionToState(R.id.end);
    }

    private void collapseActionButton() {
        binding.clCartBar.clActionButton.transitionToState(R.id.start);
    }

    private void collapseAllOpLayout() {
        int itemCount = adapter.getItemCount();
        adapter.notifyItemRangeChanged(0, itemCount, new GlobalMiniCartAction.Collapse(-1));
    }

    public void showLoadingVeil(boolean visible) {
        ViewTools.setViewVisible(binding.tvCartSellerTitle, !visible);
        ViewTools.setViewVisible(findViewById(R.id.in_cart_seller_title_veil), visible);
        ViewTools.setViewVisible(binding.tvCartSellerDesc, !visible);
        ViewTools.setViewVisible(findViewById(R.id.in_cart_seller_desc_veil), visible);
        ViewTools.setViewVisible(binding.llStoreButton, !visible && isFloatMode());

        List<SimpleVeilData> veils = new ArrayList<>();
        if (visible) {
            for (int i = 0; i < 6; i++) {
                veils.add(new SimpleVeilData(R.layout.global_mini_cart_item_veil, true));
            }
        }
        if (adapter != null) {
            adapter.setAdapterData(veils);
        }
    }

    public boolean isExpended() {
        return isExpended;
    }

    public void updateCartData(@Nullable GlobalCartBean cart) {
        initOnBackPressedCallback();
        viewModel.updateCartCachedBean(cart);
        if (cart == null) return;
        updateCartTitle(cart);
        updateCartBar(cart);
        updateCartRecyclerView(cart);
    }

    public void updateCartListData(@Nullable GlobalCartListBean cartList) {
        initOnBackPressedCallback();
        final Runnable runnable = () -> {
            viewModel.updateCartListCachedBean(cartList);
            if (cartList == null || cartList.isEmpty()) return;
            updateCartBarFloat(cartList);
        };
        if (isFloatMode()) {
            if (!isExpended()) {
                runnable.run();
            }
        } else {
            runnable.run();
        }
    }

    private void updateCartTitle(@Nullable GlobalCartBean cart) {
        String vendorName = cart != null ? cart.getVendorName() : null;
        String shippingShipmentDate = cart != null ? cart.getShippingShipmentDate() : null;
        binding.tvCartSellerTitle.setText(vendorName);
        binding.tvCartSellerDesc.setText(shippingShipmentDate);
    }

    private void updateCartRecyclerView(@NonNull GlobalCartBean cart) {
        Context context = getContext();
        if (context == null) return;

        List<AdapterDataType> items = viewModel.handleGlobalCartData(context, cart, getSelectedSellerId());
        adapter.submitData(items);
        adapter.notifyPageDataSetChanged(binding.rcvMiniCart);
        ProductSyncHelper.onPageResume(adapter);
    }

    private void clearRecommendItems() {
        viewModel.clearRecommendItems();
    }

    private void updateCartBarFloatAfterModify(@NonNull GlobalCartBean cart) {
        GlobalCartListBean cartListCachedBean = viewModel.getCartListCachedBean();
        List<GlobalCartBean> oldList = cartListCachedBean != null ? cartListCachedBean.seller_float_cart_list : null;
        if (EmptyUtils.isEmpty(oldList)) return;
        List<GlobalCartBean> newList = new ArrayList<>();
        for (GlobalCartBean oldItem : oldList) {
            if (Objects.equals(oldItem.getSellerId(), cart.getSellerId())) {
                if (cart.quantity > 0) {
                    newList.add(cart);
                }
            } else {
                newList.add(oldItem);
            }
        }
        if (!EmptyUtils.isEmpty(newList)) {
            GlobalCartListBean cartListBean = new GlobalCartListBean(newList);
            viewModel.updateCartListCachedBean(cartListBean);
            updateCartBarFloat(cartListBean);
        } else {
            dismissCart();
        }
    }

    private void updateCartBarFloat(@NonNull GlobalCartListBean cartList) {
        LayoutGlobalMiniCartBarFloatBinding b = binding.clCartBarFloat;
        int cartCount = cartList.getCartCount();

        ViewTools.setViewOnSafeClickListener(b.clCartBarInfo, null);
        if (cartCount == 1) {
            GlobalCartBean cart = CollectionUtils.firstOrNull(cartList.seller_float_cart_list);
            if (cart != null) {
                updateCartBarFloat(cart);
                ViewTools.setViewOnSafeClickListener(b.clCartBarInfo, v -> onSelectGlobalCartBrief(cart.getSellerId(), 0));
            }
        }
        List<GlobalCartBrief> cartBriefList = cartList.getGlobalCartBriefList();
        MotionLayout motionLayout = b.getRoot();
        if (cartCount == 1) {
            long delay = motionLayout.getCurrentState() == R.id.end ? motionLayout.getTransitionTimeMs() : 100L;
            b.groupCartBarIconLeft.setVisibility(View.GONE);
            b.groupCartBarIconRight.setVisibility(View.GONE);
            b.llCartBriefList.bind(cartBriefList);
            motionLayout.postDelayed(motionLayout::transitionToStart, delay);
        } else {
            long delay = motionLayout.getCurrentState() == R.id.start ? motionLayout.getTransitionTimeMs() : 100L;
            motionLayout.transitionToEnd();
            b.llCartBriefList.postDelayed(() -> {
                b.groupCartBarIconLeft.setVisibility(cartCount > 5 ? View.VISIBLE : View.GONE);
                b.groupCartBarIconRight.setVisibility(cartCount > 5 ? View.VISIBLE : View.GONE);
                onCartBarFloatScrollChanged(b.scrollView, 0, 0, 0, 0);
                b.llCartBriefList.bind(cartBriefList);
            }, delay);
        }
    }

    private void updateCartBarFloat(@NonNull GlobalCartBean cart) {
        LayoutGlobalMiniCartBarFloatBinding b = binding.clCartBarFloat;

        // price
        if (cart.fee_info != null) {
            b.tvCartPriceFloat.setText(OrderHelper.formatUSMoney(cart.fee_info.getTotalPrice()));
            if (!EmptyUtils.isEmpty(cart.fee_info.getTotalPrice())
                    && !EmptyUtils.isEmpty(cart.fee_info.sub_total_base_price)
                    && !cart.fee_info.getTotalPrice().equals(cart.fee_info.sub_total_base_price)
            ) {
                Spanny basePrice = new Spanny();
                basePrice.append(OrderHelper.formatUSMoney(cart.fee_info.sub_total_base_price), Spans.strikethrough());
                b.tvCartBasePriceFloat.setText(basePrice);
            } else {
                b.tvCartBasePriceFloat.setText(null);
            }
        } else {
            b.tvCartPriceFloat.setText(OrderHelper.formatUSMoney("0.00"));
            b.tvCartBasePriceFloat.setText(null);
        }

        if (cart.coupon_reminder != null && cart.coupon_reminder.tag_text != null) {
            // coupon
            ViewTools.setViewHtml(b.tvCartDeliveryFeeFloat, cart.coupon_reminder.tag_text);
        } else if (!EmptyUtils.isEmpty(cart.promotion_tip)) {
            // promotion_bar
            b.tvCartDeliveryFeeFloat.setText(ViewTools.fromHtml(cart.promotion_tip));
        } else {
            if (cart.fee_info != null) {
                if (DecimalTools.doubleValue(cart.fee_info.shipping_fee, 0.0) > 0) {
                    b.tvCartDeliveryFeeFloat.setText(getString(R.string.s_mkpl_delivery_fee, cart.fee_info.shipping_fee));
                } else {
                    b.tvCartDeliveryFeeFloat.setText(getString(R.string.s_mkpl_delivery_fee_free));
                }
            } else {
                b.tvCartDeliveryFeeFloat.setText(getString(R.string.s_mkpl_delivery_fee_free));
            }
        }
    }

    private void updateCartBar(@Nullable GlobalCartBean cart) {
        LayoutGlobalMiniCartBarBinding b = binding.clCartBar;

        // quantity
        b.cartView.setBadgeNum(cart != null ? cart.quantity : 0);

        // price
        if (cart != null && cart.fee_info != null) {
            b.tvCartPrice.setText(OrderHelper.formatUSMoney(cart.fee_info.getTotalPrice()));
            if (!EmptyUtils.isEmpty(cart.fee_info.getTotalPrice())
                    && !EmptyUtils.isEmpty(cart.fee_info.sub_total_base_price)
                    && !cart.fee_info.getTotalPrice().equals(cart.fee_info.sub_total_base_price)
            ) {
                Spanny basePrice = new Spanny();
                basePrice.append(OrderHelper.formatUSMoney(cart.fee_info.sub_total_base_price), Spans.strikethrough());
                b.tvCartBasePrice.setText(basePrice);
            } else {
                b.tvCartBasePrice.setText(null);
            }
            if (cart.coupon_reminder != null && cart.coupon_reminder.tag_text != null) {
                ViewTools.setViewHtml(b.tvCartDeliveryFee, cart.coupon_reminder.tag_text);
            } else if (DecimalTools.doubleValue(cart.fee_info.shipping_fee, 0.0) > 0) {
                b.tvCartDeliveryFee.setText(getString(R.string.s_mkpl_delivery_fee, cart.fee_info.shipping_fee));
            } else {
                b.tvCartDeliveryFee.setText(getString(R.string.s_mkpl_delivery_fee_free));
            }
        } else {
            b.tvCartPrice.setText(OrderHelper.formatUSMoney("0.00"));
            b.tvCartBasePrice.setText(null);
            b.tvCartDeliveryFee.setText(getString(R.string.s_mkpl_delivery_fee_free));
        }

        // promotion_bar
        updatePromotionBar(cart);
    }

    private void updatePromotionBar(@Nullable GlobalCartBean cart) {
        LayoutSellerBottomPromotionBarBinding clPromotionBar = binding.llPromotionBar;
        if (cart != null && !EmptyUtils.isEmpty(cart.promotion_tip)) {
            clPromotionBar.tvPromotionContent.setText(ViewTools.fromHtml(cart.promotion_tip));
        } else {
            clPromotionBar.tvPromotionContent.setText(null);
        }

        // reset job
        ViewTools.setViewOnSafeClickListener(binding.llPromotionBarRoot, v -> {
        });
        clPromotionBar.tvPromotionAction.setText(null);
        ViewTools.setViewVisibilityIfChanged(clPromotionBar.tvPromotionAction, false);
        clPromotionBar.tvPromotionAction.setText(null);
        ViewTools.setViewOnSafeClickListener(clPromotionBar.tvPromotionAction, v -> {
        });

        updatePromotionBarVisibility();

        if (cart != null) {
            updatePromotionDetail(cart);
        }
    }

    private void updatePromotionBarVisibility() {
        boolean isShow;
        if (isFloatMode()) {
            isShow = isExpended;
        } else {
            isShow = true;
        }
        if (!isShow) {
            ViewTools.setViewVisibilityIfChanged(binding.llPromotionBarRoot, false);
            return;
        }
        CharSequence content = binding.llPromotionBar.tvPromotionContent.getText();
        boolean hasContent = content != null && content.length() != 0;
        ViewTools.setViewVisibilityIfChanged(binding.llPromotionBarRoot, hasContent);
    }

    private void updatePromotionDetail(@NonNull GlobalCartBean cart) {
        LayoutSellerBottomPromotionBarBinding clPromotionBar = binding.llPromotionBar;
        ArrayList<SellerPromotionDetailBean> promotions = new ArrayList<>();
        Transform<GlobalCartBean.PromotionDetail, SellerPromotionDetailBean> mapper;
        mapper = detail -> {
            SellerPromotionDetailBean result;
            result = new SellerPromotionDetailBean();
            result.setIconUrl(detail.getIconUrl());
            result.setTitle(detail.getTitle());
            result.setMoreLink(detail.getViewLink());
            if (detail.group_order_button != null && detail.group_order_button.show) {
                result.setAction(
                        SellerPromotionDetailBean.ACTION_TYPE_GROUP_ORDER,
                        detail.group_order_button.desc,
                        null
                );
            }
            return result;
        };
        CollectionUtils.mapNotNullTo(
                promotions,
                CollectionUtils.orEmptyList(cart.promotion_details),
                mapper
        );

        boolean showArrow = false;
        SellerPromotionDetailBean firstPromotion = CollectionUtils.firstOrNull(promotions);
        if (promotions.size() == 1) {
            if (firstPromotion != null && !EmptyUtils.isEmpty(firstPromotion.getMoreLink())) {
                showArrow = true;
                ViewTools.setViewOnSafeClickListener(
                        binding.llPromotionBarRoot,
                        v -> gotoWebViewActivity(activity, firstPromotion.getMoreLink())
                );
            }
        } else if (promotions.size() > 1) {
            showArrow = true;
            ViewTools.setViewOnSafeClickListener(
                    binding.llPromotionBarRoot,
                    v -> showSellerPromotionDetailFragment(promotions)
            );
            clPromotionBar.tvPromotionAction.setVisibility(View.GONE);
        }

        if (firstPromotion != null && !EmptyUtils.isEmpty(firstPromotion.getActionTitle())) {
            showArrow = true;
            clPromotionBar.tvPromotionAction.setText(firstPromotion.getActionTitle());
            ViewTools.setViewVisibilityIfChanged(clPromotionBar.tvPromotionAction, true);
            ViewTools.setViewOnSafeClickListener(
                    clPromotionBar.tvPromotionAction,
                    v -> prepareShowGroupOrderDialog()
            );
        }

        if (showArrow) {
            clPromotionBar.tvPromotionContent.setEllipsize(null);
            ViewTools.setViewVisibilityIfChanged(clPromotionBar.groupPromotionArrow, true);
        } else {
            clPromotionBar.tvPromotionContent.setEllipsize(TextUtils.TruncateAt.END);
            ViewTools.setViewVisibilityIfChanged(clPromotionBar.groupPromotionArrow, false);
        }
    }

    @Nullable
    private SellerPromotionDetailFragment getSellerPromotionDetailFragment() {
        if (getActivity() == null) {
            return null;
        }
        String fragmentTag = SellerPromotionDetailFragment.TAG;
        return (SellerPromotionDetailFragment) getChildFragmentManager().findFragmentByTag(fragmentTag);
    }

    private void showSellerPromotionDetailFragment(ArrayList<SellerPromotionDetailBean> promotions) {
        if (promotions == null || promotions.isEmpty()) return;

        SellerPromotionDetailFragment fragment;
        fragment = getSellerPromotionDetailFragment();
        if (fragment != null) {
            return;
        }

        binding.vBackgroundSheet.setAlpha(0);
        binding.vBackgroundSheet.animate().setDuration(200L).alpha(.4f).start();
        ViewTools.setViewOnSafeClickListener(binding.vBackgroundSheet, v -> dismissPromotionDetailSheet());

        ViewTools.setViewVisibilityIfChanged(binding.vTopShadow, true);

        fragment = SellerPromotionDetailFragment.newInstance(promotions);
        fragment.setOnDismissListener(dialog -> dismissPromotionDetailSheet());

        FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
        transaction.setCustomAnimations(getEnterAnim(), getExitAnim());
        transaction.add(R.id.fragment_container_mini_cart, fragment, SellerPromotionDetailFragment.TAG);
        transaction.commitAllowingStateLoss();
    }

    private void dismissPromotionDetailSheet() {
        SellerPromotionDetailFragment fragment;
        fragment = getSellerPromotionDetailFragment();
        if (fragment == null) {
            return;
        }

        ViewTools.setViewOnSafeClickListener(binding.vBackgroundSheet, null);
        binding.vBackgroundSheet.setAlpha(.4f);
        binding.vBackgroundSheet.animate().setDuration(200L).alpha(0).start();
        ViewTools.setViewVisibilityIfChanged(binding.vTopShadow, false);
        fragment.dismiss();
    }

    private void showCartBarFloatWithAnim(boolean isShow) {
        boolean isCartBarFloatShow = ViewTools.isViewVisible(binding.clCartBarFloatRoot);
        if (isShow == isCartBarFloatShow) return;

        TransitionSet transitionSet = new TransitionSet();
        Fade fadeOut = new Fade(Fade.OUT);
        if (!isShow) {
            fadeOut.addTarget(binding.clCartBarFloatRoot);
            Fade fadeIn = new Fade(Fade.IN);
            fadeIn.addTarget(binding.clCartBar.getRoot());
            transitionSet.addTransition(fadeIn);
            transitionSet.addTransition(fadeOut);
            transitionSet.setInterpolator(new AccelerateDecelerateInterpolator());
            transitionSet.setDuration(SWITCH_CART_BAR_DURATION);
            TransitionManager.beginDelayedTransition(binding.getRoot(), transitionSet);
            binding.clCartBarFloatRoot.setVisibility(View.GONE);
            binding.clCartBar.getRoot().setVisibility(View.VISIBLE);
        } else {
            fadeOut.addTarget(binding.clCartBar.getRoot());
            Fade fadeIn = new Fade(Fade.IN);
            fadeIn.addTarget(binding.clCartBarFloatRoot);
            transitionSet.addTransition(fadeIn);
            transitionSet.addTransition(fadeOut);
            transitionSet.setInterpolator(new AccelerateDecelerateInterpolator());
            transitionSet.setDuration(SWITCH_CART_BAR_DURATION);
            TransitionManager.beginDelayedTransition(binding.getRoot(), transitionSet);
            binding.clCartBarFloatRoot.setVisibility(View.VISIBLE);
            binding.clCartBar.getRoot().setVisibility(View.GONE);
        }
    }

    @Nullable
    private String getSelectedSellerId() {
        String selectedSellerId = viewModel.getSelectedSellerId();
        return !EmptyUtils.isEmpty(selectedSellerId) ? selectedSellerId : getExtraSellerId();
    }

    private void onSelectGlobalCartBrief(@Nullable String sellerId, int position) {
        viewModel.setSelectedSellerId(sellerId);
        onCartEditListener.setTargetSellerId(sellerId);
        if (EmptyUtils.isEmpty(sellerId)) return;
        GlobalCartBean cart = GlobalCartListBean.getGlobalCartBean(viewModel.getCartListCachedBean(), sellerId);
        if (cart != null) {
            updateCartBar(cart);
        }
        showCartBarFloatWithAnim(/* isShow= */false);
        binding.getRoot().postDelayed(
                () -> expendCart(sellerId, position, /* forceRefresh= */true),
                SWITCH_CART_BAR_DURATION
        );
    }

    private void handleGlobalCartResponse(@Nullable GlobalCartListResponse response) {
        if (response == null) return;
        if (!getClass().getName().equals(response.getRequest().getToken())) return;

        String requestSellerId = response.getRequest().getRequestSellerId();
        if (requestSellerId == null) return;

        showLoadingVeil(false);

        GlobalCartListBean cartListBean = response.getResponse();
        GlobalCartBean cart = cartListBean != null ? cartListBean.getGlobalCartBean(requestSellerId) : null;

        GlobalCartBean fakeCart = cart;
        if (fakeCart == null) {
            fakeCart = new GlobalCartBean();
            fakeCart.quantity = 0;
            GlobalCartBean.VendorInfo vendorInfo = new GlobalCartBean.VendorInfo();
            vendorInfo.vendor_id = DecimalTools.intValue(requestSellerId, 0);
            fakeCart.vendor_info = vendorInfo;
        }
        updateCartBarFloatAfterModify(fakeCart);

        String selectedSellerId = getSelectedSellerId();
        if (!Objects.equals(requestSellerId, selectedSellerId)) return;
        if (cart != null && !cart.isEmpty()) {
            updateCartData(cart);
        } else {
            if (isFloatMode()) {
                collapseCart();
            } else {
                dismissCart();
            }
        }

        if (cart != null && response.getRequest() instanceof GlobalCartListRequest) {
            Map<String, Object> content = viewModel.getCartImpressionContent(cart);
            Map<String, Object> ctx = new EagleContext().setGlobalVendor(requestSellerId).asMap();
            AppAnalytics.logEvent(
                    EagleTrackEvent.EventType.CART_IMP,
                    new EagleTrackModel.Builder()
                            .addElement(EagleTrackManger.get().getElement(EagleTrackEvent.ModNm.MKPL_CART_ITEM, 0, null, -1))
                            .addContent(content)
                            .addCtx(ctx).build().getParams()
            );
        }
    }

    private void handleGlobalCartRecommendResponse(@Nullable GlobalCartRecommendResponse response) {
        if (response == null) return;
        if (!response.isSuccess()) {
            return;
        }
        String sellerId = response.getRequest().getSellerId();
        String selectedSellerId = getSelectedSellerId();
        if (!EmptyUtils.isEmpty(selectedSellerId) && !Objects.equals(sellerId, selectedSellerId)) {
            return;
        }
        List<AdapterDataType> recommendItems;
        recommendItems = viewModel.handleSellerCartRecommendData(selectedSellerId, response.requireResponse());
        GlobalCartBean cart = viewModel.getCartCachedBean();
        if (cart != null && Objects.equals(cart.getSellerId(), sellerId) && !EmptyUtils.isEmpty(recommendItems)) {
            updateCartRecyclerView(cart);
        }
    }

    private void notifyDismissed() {
        Bundle result = new Bundle();
        result.putBoolean(RESULT_BUNDLE_DISMISS, true);
        ObjectUtils.let(safeGetParentFragmentManager(), fm -> fm.setFragmentResult(REQUEST_KEY, result));
    }

    private void notifyCheckout() {
        Bundle result = new Bundle();
        result.putBoolean(RESULT_BUNDLE_CHECKOUT, true);
        ObjectUtils.let(safeGetParentFragmentManager(), fm -> fm.setFragmentResult(REQUEST_KEY, result));
    }

    private void notifyGroupOrder() {
        Bundle result = new Bundle();
        result.putBoolean(RESULT_BUNDLE_GROUP_ORDER, true);
        ObjectUtils.let(safeGetParentFragmentManager(), fm -> fm.setFragmentResult(REQUEST_KEY, result));
    }

    private void leavePage() {
        onCartEditListener.resetEditData();
        viewModel.resetSellerGroupStatusLiveData();
        collapseAllOpLayout();
    }

    private static void gotoWebViewActivity(@Nullable Context context, @Nullable String linkUrl) {
        if (context != null && !EmptyUtils.isEmpty(linkUrl)) {
            context.startActivity(WebViewActivity.getIntent(context, linkUrl));
        }
    }

    private void gotoPdpPage(@NonNull MiniCartItemData productData) {
        final FragmentActivity activity = getActivity();
        if (activity == null) return;
        startActivity(ProductIntentCreator.getIntent(
                activity,
                OrderHelper.toProductBeanV5(productData.t, productData.convertSoldStatus())));
    }

    private void gotoSellerPage() {
        String sellerId = getSelectedSellerId();

        Map<String, Object> ctx = new EagleContext().setPageTarget(sellerId).asMap();
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */"explore_more",
                /* targetPos= */-1,
                /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType= */EagleTrackEvent.ClickType.VIEW,
                /* ctx= */ctx
        );

        collapseCart();

        final FragmentActivity activity = getActivity();
        if (activity == null) return;
        if (isFloatMode() && !EmptyUtils.isEmpty(sellerId)) {
            Intent a = SellerActivity.getIntent(activity, sellerId);
            startActivity(a);
        }
    }

    private void gotoCheckoutPage() {
        if (inAnimation) return;

        notifyCheckout();

        String sellerId = getSelectedSellerId();
        GlobalCartBean cart = GlobalCartListBean.getGlobalCartBean(viewModel.getCartListCachedBean(), sellerId);
        if (cart == null) {
            // In seller page, cart list is null
            cart = viewModel.getCartCachedBean();
        }

        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(sellerId)
                .asMap();
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */EagleTrackEvent.TargetNm.CHECKOUT,
                /* targetPos= */-1,
                /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType= */EagleTrackEvent.ClickType.VIEW,
                /* ctx= */ctx
        );

        String url = "/order/checkout?type=seller&vendor_id=%s&cart_domain=" + Constants.CartDomain.DOMAIN_GROCERY;
        url = String.format(Locale.ROOT, url, sellerId);
        int alcoholAgreementType;
        alcoholAgreementType = AlcoholHelper.getAlcoholAgreementType(
                /* orderSubType= */cart != null ? cart.sub_type : null,
                /* containsAlcohol= */false,
                /* isRecordAlcohol= */false,
                /* isRecordSellerAlcohol= */cart != null && cart.is_record_seller_alcohol
        );
        if (AlcoholHelper.isShowAlcoholAgreement(alcoholAgreementType)) {
            Context context = activity;
            if (context != null) {
                Intent a = SectionAlcoholAgreementActivity.getIntent(
                        context,
                        Constants.CartDomain.DOMAIN_GROCERY,
                        alcoholAgreementType,
                        url
                );
                context.startActivity(a);
            }
        } else {
            gotoWebViewActivity(activity, url);
        }
    }

    public void prepareShowGroupOrderDialog() {
        Context context = getContext();
        if (context == null) return;

        String sellerId = getSelectedSellerId();
        if (EmptyUtils.isEmpty(sellerId)) return;

        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(sellerId)
                .asMap();
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm= */null,
                /* modPos= */-1,
                /* secNm= */null,
                /* secPos= */-1,
                /* targetNm= */EagleTrackEvent.TargetNm.GROUP_ORDER,
                /* targetPos= */-1,
                /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType= */EagleTrackEvent.ClickType.VIEW,
                /* ctx= */ctx
        );

        if (!AccountManager.get().isLogin()) {
            startActivity(AccountIntentCreator.getIntent(context));
            return;
        }
        viewModel.checkGroupOrderExists();
    }

    private void handleSellerGroupStatusLiveData(@Nullable SellerGroupStatusBean response) {
        String currentVendorId = getSelectedSellerId();
        if (!EmptyUtils.isEmpty(currentVendorId) && response != null) {
            showGroupOrderPopup(currentVendorId, response);
            notifyGroupOrder();
            leavePage();
            collapseCart();
        }
    }

    private void showGroupOrderPopup(String currentVendorId, SellerGroupStatusBean statusBean) {
        String vendorName;
        try {
            vendorName = URLEncoder.encode(statusBean.vendor_name, "UTF-8");
        } catch (Exception ignored) {
            vendorName = statusBean.vendor_name;
        }
        String url = String.format(
                Constants.Url.SELLER_GROUP_ORDER_POPUP,
                statusBean.vendor_id, vendorName, statusBean.status, statusBean.key, currentVendorId
        );
        PopupSlideDialog dialog = new PopupSlideDialog();
        dialog.loadUrl(AppConfig.HOST_WEB + url);
        int dialogHeight = (int) (getResources().getDisplayMetrics().heightPixels * 0.5);
        if (statusBean.status == 3) {
            dialogHeight = CommonTools.dp2px(335);
        } else if (statusBean.status == 2) {
            dialogHeight = CommonTools.dp2px(425);
        } else if (statusBean.status == 1) {
            dialogHeight = CommonTools.dp2px(440);
        }
        dialog.callDialogSize(-1, dialogHeight);
        dialog.show();
    }

    public void showCartInitial() {
        if (getArguments() == null) return;
        SellerPageParams sellerParams = getArguments().getParcelable(SellerPageParams.EXTRA_SELLER_PARAMS);
        if (sellerParams != null && (sellerParams.openCart == 1
                || !TextUtils.isEmpty(sellerParams.toastMsg))) {
            binding.clCartBar.getRoot().performClick();

            if (!TextUtils.isEmpty(sellerParams.toastMsg)) {
                showToast(sellerParams.toastMsg);
            }
            getArguments().putParcelable(SellerPageParams.EXTRA_SELLER_PARAMS, null);
        }
    }

    private void showToast(String msg) {
        ToastySnackBarView snackBarView = new ToastySnackBarView(binding.getRoot().getContext());
        snackBarView.convert(new ActionSnackBarData(msg));
        Toaster.asSnackBar(binding.getRoot())
                .setView(snackBarView)
                .setOptions(new SnackBarOptions.Builder().duration(2500).build())
                .build()
                .show(this);
    }

}
