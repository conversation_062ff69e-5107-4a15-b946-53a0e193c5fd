package com.sayweee.weee.module.mkpl.feed;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentContentFeedBinding;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.PostCollectManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.mkpl.GlobalMiniCartViewModel;
import com.sayweee.weee.module.mkpl.GlobalOnCartEditListener;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.mkpl.common.SimpleVeilData;
import com.sayweee.weee.module.mkpl.home.GlobalPlusFragment;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentCategoryBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedSellerItemBean;
import com.sayweee.weee.module.mkpl.provider.data.CmsContentFeedStore;
import com.sayweee.weee.module.mkpl.provider.data.CmsFeedSellerData;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.post.PostProductNewFragment;
import com.sayweee.weee.module.post.PostVideoDetailActivity;
import com.sayweee.weee.module.post.adapter.PostBottomProductAdapter;
import com.sayweee.weee.module.post.bean.PostBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.bean.ProductNewBean;
import com.sayweee.weee.module.post.detail.ReviewDetailActivity;
import com.sayweee.weee.module.post.explore.provider.PostExploreItemProvider;
import com.sayweee.weee.module.post.explore.provider.data.PostExploreItemData;
import com.sayweee.weee.module.post.inspiration.InspirationFragment;
import com.sayweee.weee.module.post.inspiration.provider.data.EventItemData;
import com.sayweee.weee.module.product.ProductDetailActivity;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.player.mute.PostCoverVideoHelper;
import com.sayweee.weee.player.mute.PostCoverVideoManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.helper.StatusHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.MapUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.refresh.BetterLoadingMoreView;
import com.sayweee.wrapper.bean.FailureBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Desc:
 */
@SuppressWarnings("squid:S110")
public class ContentFeedFragment extends ContentFeedBaseFragment<ContentFeedViewModel> {

    private static final String BUNDLE_FEED_BEAN = "feed_bean";
    private static final String BUNDLE_CATEGORY_INDEX = "category_index";

    private FragmentContentFeedBinding binding;
    private ContentFeedAdapter adapter;

    private IContentFeedSharedViewModel shareViewModel;

    private GlobalMiniCartViewModel cartViewModel;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;

    private ProductTraceObserver productTraceObserver;

    public static ContentFeedFragment newInstance(CmsContentCategoryBean feedBean, int index) {
        ContentFeedFragment fragment = new ContentFeedFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(BUNDLE_CATEGORY_INDEX, index);
        if (feedBean != null) {
            bundle.putSerializable(BUNDLE_FEED_BEAN, feedBean);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_content_feed;
    }

    protected int getContentCategoryIndex() {
        return getArguments() != null ? getArguments().getInt(BUNDLE_CATEGORY_INDEX, 0) : 0;
    }

    @Nullable
    protected CmsContentCategoryBean getContentCategoryBean() {
        return getArguments() != null ? (CmsContentCategoryBean) getArguments().getSerializable(BUNDLE_FEED_BEAN) : null;
    }

    @NonNull
    protected String getContentCategoryKey() {
        CmsContentCategoryBean categoryBean = getContentCategoryBean();
        return categoryBean != null ? EmptyUtils.orEmpty(categoryBean.key) : "";
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);

        binding = FragmentContentFeedBinding.bind(contentView);
        setupPage();
    }

    private void setupPage() {
        Context context = getContext();
        if (context == null) return;

        SafeStaggeredGridLayoutManager layoutManager;
        layoutManager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        binding.recyclerView.setLayoutManager(layoutManager);
        adapter = new ContentFeedAdapter(String.valueOf(hashCode()), onCartEditListener);
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                Object data = adapter.getItem(position);
                handleClick(view, data, position);
            }
        });
        adapter.setOnItemLoadListener(new PostExploreItemProvider.OnItemLoadListener() {
            @Override
            public void showItem(Object data, int viewId) {
                if (viewId == R.id.layout_recipe_video_relate && data instanceof PostExploreItemData) {
                    clickRecipeVideoRelate((PostExploreItemData) data, false);
                }
            }
        });
        adapter.setOnLoadMoreListener(this::loadMore, binding.recyclerView);
        adapter.setEnableLoadMore(true);
        setLoadMoreView();

        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.addItemDecoration(new SimpleSectionItemDecoration());

        PostCoverVideoHelper.attachScrollPlay(binding.recyclerView);
        addShowScrollTopListener();
    }

    protected GlobalOnCartEditListener onCartEditListener = new GlobalOnCartEditListener(
            null,
            new GlobalOnCartEditListener.SimpleOnGlobalCartUpdateListener() {
                @Override
                public void onGlobalCartListUpdate(@NonNull GlobalCartListResponse response) {
                    handleGlobalCartListResponse(response);
                }
            }
    );

    private void setLoadMoreView() {
        Fragment fragment = getParentFragment();
        int marginBottom = 0;
        if (fragment instanceof GlobalPlusFragment) {
            if (((GlobalPlusFragment) fragment).getExtraInTab()) {
                marginBottom = CommonTools.dp2px(60);
            }
        } else if (getActivity() instanceof ProductDetailActivity) {
            marginBottom = CommonTools.dp2px(120);
        }
        adapter.setLoadMoreView(new BetterLoadingMoreView(marginBottom));
    }

    private void handleClick(View view, Object data, int position) {
        int viewId = view.getId();
        // seller click
        if (viewId == R.id.cl_content_feed_seller_root && data instanceof CmsFeedSellerData) {
            clickFeedSeller((CmsFeedSellerData) data, position);
        }

        // post video click
        if (viewId == R.id.layout_product && data instanceof PostExploreItemData) {
            clickPostVideo((PostExploreItemData) data);
        }

        // post collect click
        if (viewId == R.id.iv_collect && data instanceof PostExploreItemData) {
            clickPostLike((PostExploreItemData) data, position);
        }

        // post recipe relate more
        if (viewId == R.id.layout_recipe_video_relate && data instanceof PostExploreItemData) {
            clickRecipeVideoRelate((PostExploreItemData) data, true);
        }

        // event click
        if (viewId == R.id.cl_content_feed_event_root && data instanceof EventItemData) {
            clickEvent((EventItemData) data);
        }
    }

    private void clickFeedSeller(CmsFeedSellerData item, int position) {
        CmsContentFeedSellerItemBean seller = item.t;
        if (seller == null || getContext() == null) return;
        if (!EmptyUtils.isEmpty(seller.seller_url)) {

            if (item.impression != null) {
                Map params = item.impression.params;
                Object obj = params.get("co");
                if (obj instanceof Map) {
                    Map content = (Map) obj;
                    Map<String, Object> ctx = new HashMap<>();
                    if (viewModel.categoryBean != null) {
                        ctx.put("filter_sub_category", viewModel.categoryBean.key);
                    }
                    EagleTrackManger.get().trackEagleClickAction(
                            MapUtils.getString(params, "mod_nm"),
                            MapUtils.getInt(params, "mod_pos"),
                            null, -1,
                            MapUtils.getString(content, "banner_id"),
                            MapUtils.getInt(content, "banner_pos"),
                            MapUtils.getString(content, "banner_type"),
                            EagleTrackEvent.ClickType.VIEW,
                            ctx

                    );
                }

            }

            getContext().startActivity(WebViewActivity.getIntent(
                    getContext(), seller.seller_url));
        }
    }

    private void clickPostVideo(PostExploreItemData item) {
        PostCategoryBean.ListBean bean = item.t;

        if (item.impression != null) {
            Map params = item.impression.params;
            Object obj = params.get("co");
            if (obj instanceof Map) {
                Map content = (Map) obj;
                Map<String, Object> ctx = new HashMap<>();
                if (viewModel.categoryBean != null) {
                    ctx.put("filter_sub_category", viewModel.categoryBean.key);
                }
                EagleTrackManger.get().trackEagleClickAction(
                        MapUtils.getString(params, "mod_nm"),
                        MapUtils.getInt(params, "mod_pos"),
                        null, -1,
                        MapUtils.getString(content, "banner_id"),
                        MapUtils.getInt(content, "banner_pos"),
                        MapUtils.getString(content, "banner_type"),
                        EagleTrackEvent.ClickType.VIEW,
                        ctx
                );
            }

        }

        if (bean.isVideoPost()) {
            List<PostBean> postBeanList = getPostBeanList(adapter.getData(), bean);

            Intent intent;
            if (GlobalPlusFragment.FROM_PAGE.equals(shareViewModel.getContentFeedFromPageKey())) {
                intent = PostVideoDetailActivity.getIntentForGlobal(
                        getContext(), viewModel.recommendSession, bean, postBeanList);
            } else {
                intent = PostVideoDetailActivity.getIntentForInspiration(
                        getContext(), viewModel.recommendSession, bean, postBeanList);
            }
            startActivity(intent);
        } else {
            startActivity(ReviewDetailActivity.getIntent(activity, bean.id));
        }
    }

    private List<PostBean> getPostBeanList(List<AdapterDataType> datas, PostBean bean) {
        List<PostBean> postBeanList = new ArrayList<>();
        boolean isStartAdd = false;
        for (AdapterDataType data : datas) {
            try {
                if (data instanceof PostExploreItemData) {
                    PostCategoryBean.ListBean listBean = ((PostExploreItemData) data).t;
                    if (listBean.id == bean.id) {
                        isStartAdd = true;
                    }
                    if (listBean.isVideoPost() && isStartAdd) {
                        postBeanList.add(listBean);
                    }
                    if (postBeanList.size() == 10) {
                        break;
                    }
                }
            } catch (Exception e) {
                // no op
            }
        }
        return postBeanList;
    }

    private void clickPostLike(PostExploreItemData item, int position) {
        PostCategoryBean.ListBean bean = item.t;
        if (AccountManager.get().isLogin()) {
            bean.is_set_like = !bean.is_set_like;
            bean.like_count = bean.is_set_like ? bean.like_count + 1 : bean.like_count - 1;
            PostCollectManager.get().toggleCollect(bean.isVideoPost(), bean.id, bean.is_set_like, bean.like_count,
                    true);
            this.adapter.notifyItemStatusChanged(position);

            if (item.impression != null) {
                Map params = item.impression.params;
                Object obj = params.get("co");
                if (obj instanceof Map) {
                    Map content = (Map) obj;
                    Map<String, Object> ctx = new HashMap<>();
                    if (viewModel.categoryBean != null) {
                        ctx.put("filter_sub_category", viewModel.categoryBean.key);
                    }
                    EagleTrackManger.get().trackEagleClickAction(
                            MapUtils.getString(params, "mod_nm"),
                            MapUtils.getInt(params, "mod_pos"),
                            null, -1,
                            MapUtils.getString(content, "banner_id"),
                            MapUtils.getInt(content, "banner_pos"),
                            MapUtils.getString(content, "banner_type"),
                            bean.is_set_like ? EagleTrackEvent.ClickType.LIKE : EagleTrackEvent.ClickType.UNLIKE,
                            ctx
                    );
                }

            }
        } else {
            startActivity(AccountIntentCreator.getIntent(activity));
        }
    }

    List<ProductBean> productList = new ArrayList<>();
    List<ProductNewBean> productNewList = new ArrayList<>();
    PostCategoryBean.ListBean relateProductBean;

    private void clickRecipeVideoRelate(PostExploreItemData data, boolean isShow) {
        try {
            relateProductBean = data.t;
            if (CollectionUtils.isNotEmpty(data.relateProducts)) {
                if (isShow) {
                    trackClickRecipeVideoRelate(data);
                    showRelateProducts(data.relateProducts);
                }
                return;
            }
            if (viewModel.getPostNewProducts(data, String.valueOf(relateProductBean.id), isShow)) {
                trackClickRecipeVideoRelate(data);
            }
        } catch (Exception e) {
        }
    }

    private static void trackClickRecipeVideoRelate(PostExploreItemData data) {
        if (data.impression != null) {
            Map params = data.impression.params;
            Object obj = params.get("co");
            if (obj instanceof Map) {
                Map content = (Map) obj;
                Map<String, Object> ctx = new HashMap<>();
                ctx.put("related_info", data.t.id);
                EagleTrackManger.get().trackEagleClickAction(
                        MapUtils.getString(params, "mod_nm"),
                        MapUtils.getInt(params, "mod_pos"),
                        null, -1,
                        EagleTrackEvent.TargetNm.EXPLORE_MORE,
                        MapUtils.getInt(content, "banner_pos"),
                        MapUtils.getString(content, "banner_type"),
                        EagleTrackEvent.ClickType.VIEW,
                        ctx
                );
            }
        }
    }

    private void showRelateProduct() {
        if (!EmptyUtils.isEmpty(productNewList)) {
            String tag = PostProductNewFragment.class.getSimpleName() + relateProductBean.id;
            PostProductNewFragment postProductNewFragment = (PostProductNewFragment) PostProductNewFragment.newInstance(
                    productNewList, relateProductBean.id, String.format(Constants.Source.PORTAL_POST_DETAIL_PDP, "v", relateProductBean.id),
                    productList.size(), false, shareViewModel.getContentFeedFromPageKey());
            getChildFragmentManager().beginTransaction().remove(postProductNewFragment).commit();
            postProductNewFragment.show(getChildFragmentManager(), tag);
            postProductNewFragment.setOnPostAddItemListener(new PostProductNewFragment.updateListener() {
                @Override
                public void updateData() {
                    if (cartViewModel != null) {
                        cartViewModel.getSellerCartFloat(/* sellerId= */null);
                    }
                    ProductSyncHelper.onPageResume(adapter);
                }

                @Override
                public void changedState(View bottomSheet, int state) {
                }

                @Override
                public void changedOffset(View bottomSheet, float slideOffset) {
                }
            });
        }
    }

    private void clickEvent(@NonNull EventItemData item) {
        String linkUrl = item.t.link_url;
        ImpressionBean impressionBean = item.impression;
        if (impressionBean != null) {
            Map<String, Object> params = impressionBean.params;
            Object obj = params.get("co");
            if (obj instanceof Map) {
                Map<String, Object> content = (Map<String, Object>) obj;
                AppAnalytics.logClickAction(
                        new EagleTrackModel.Builder()
                                .setMod_nm(MapUtils.getString(params, "mod_nm"))
                                .setMod_pos(MapUtils.getInt(params, "mod_pos"))
                                .setBannerId(String.valueOf(item.t.id))
                                .setBanner_type(EagleTrackEvent.BannerType.SOCIAL_EVENT)
                                .setBanner_pos(MapUtils.getInt(content, "banner_pos"))
                                .setUrl(linkUrl)
                                .build()
                                .getParams()
                );
            }
        }
        FragmentActivity activity = getActivity();
        if (activity != null) {
            activity.startActivity(WebViewActivity.getIntent(getContext(), linkUrl));
        }
    }

    @Override
    public void loadData() {
        int index = getContentCategoryIndex();
        CmsContentFeedPacket sharedPacket = shareViewModel.getContentFeedPacket();
        if (sharedPacket != null) {
            sharedPacket.setSelectedIndex(index);
        }

        CmsContentCategoryBean categoryBean = getContentCategoryBean();
        viewModel.categoryBean = categoryBean;
        if (categoryBean != null) {
            adapter.setFilterSubCategory(categoryBean.key);
        }
        CmsContentFeedStore dataStore = viewModel.dataStore;
        if (dataStore != null && adapter.getItemCount() == 0) {
            if (!CollectionUtils.isEmpty(dataStore.getData())) {
                viewModel.initData();
            } else {
                showLoadVeil(true);
                viewModel.fetchFeedData(categoryBean);
            }
        }
        initProductTraceObserver();
    }

    private void initProductTraceObserver() {
        if (productTraceObserver == null) {
            productTraceObserver = new ProductTraceObserver(this) {
                @Override
                protected void handleProductSalesTraceChange() {
                    ProductTraceViewHelper.notify(binding.recyclerView);
                }
            };
            productTraceObserver.start();
        }
        if (viewModel != null) {
            productTraceObserver.setExtraTopic(viewModel.getProductTraceTopic());
        }
    }

    @Override
    public ContentFeedViewModel createModel() {
        Fragment fragment = getParentFragment();
        if (fragment instanceof IContentFeedSharedModelProvider) {
            shareViewModel = ((IContentFeedSharedModelProvider) fragment).getContentFeedSharedViewModel();
        } else if (getActivity() instanceof ProductDetailActivity) {
            shareViewModel = ((IContentFeedSharedModelProvider) getActivity()).getContentFeedSharedViewModel();
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }

        migrateViewModel();
        updateView();

        cartViewModel = new ViewModelProvider(activity).get(GlobalMiniCartViewModel.class);
        cartViewModel.injectLifecycle(getLifecycle());

        viewModel.adapterFeedData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                adapter.setNewData(list);
                adapter.loadMoreComplete();
                adapter.notifyPageDataSetChanged(binding.recyclerView);
                if (!EmptyUtils.isEmpty(list)) {
                    adapter.setPreLoadNumber(10);
                } else {
                    adapter.setPreLoadNumber(1);
                }
                ViewTools.setViewVisible(EmptyUtils.isEmpty(list), findViewById(R.id.layout_empty));
                LabelScrollHandler.notifyAdapterDataChanged(binding.recyclerView);
            }
        });

        viewModel.adapterAppendData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                if (!EmptyUtils.isEmpty(list)) {
                    adapter.addData(list);
                    adapter.loadMoreComplete();
                } else {
                    adapter.loadMoreEnd();
                }
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                showLoadVeil(false);
                StatusHelper.showStatus(getStatusManager(), failureBean, true,
                        new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                getStatusManager().hideStatus();
                                loadData();
                            }
                        });
            }
        });

        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                adapter.toggleCollect(map);
            }
        });

        SharedViewModel.get().followChangeData.observe(this, new Observer<ArrayMap<String, Integer>>() {
            @Override
            public void onChanged(ArrayMap<String, Integer> data) {
                adapter.trendingPostDataUpdated(data);
            }
        });

        SharedViewModel.get().collectsData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
            }
        });

        viewModel.postProductsNewBean.observe(this, new Observer<List<ProductNewBean>>() {
            @Override
            public void onChanged(List<ProductNewBean> productNewBeans) {
                showRelateProducts(productNewBeans);
            }
        });

        attachCartViewModel();
    }

    private void showRelateProducts(List<ProductNewBean> productNewBeans) {
        productNewList.clear();
        productNewList.addAll(productNewBeans);
        if (!EmptyUtils.isEmpty(productNewBeans)) {
            productList.clear();
            List<AdapterProductData> adapterData = new ArrayList<>();
            for (ProductNewBean bean : productNewBeans) {
                productList.addAll(bean.product_list);
            }
            if (null != relateProductBean && !EmptyUtils.isEmpty(relateProductBean.valid_product_ids)) {
                String[] validIds = relateProductBean.valid_product_ids.split(",");
                List<ProductBean> validSortList = new ArrayList<>();
                for (String a : validIds) {
                    for (ProductBean bean : productList) {
                        if (!EmptyUtils.isEmpty(bean.relate_product)) {
                            bean.relate_product.get(0).setSimilar(true);
                        }
                        if (a.equalsIgnoreCase(!EmptyUtils.isEmpty(bean.relate_product) ? String.valueOf(bean.relate_product.get(0).id) : String.valueOf(bean.id))) {
                            validSortList.add(!EmptyUtils.isEmpty(bean.relate_product) ? bean.relate_product.get(0) : bean);
                        }
                    }
                }
                for (ProductBean item : validSortList) {
                    if ((!item.isSoldOut() || !EmptyUtils.isEmpty(item.relate_product)) && !item.isHotDish() && !item.isChangeDate()) {
                        adapterData.add(new AdapterProductData(PostBottomProductAdapter.TYPE_PRODUCT, !EmptyUtils.isEmpty(item.relate_product) ? item.relate_product.get(0) : item));
                    }
                }
            }
        }
        showRelateProduct();
    }

    private void updateView() {
        if (InspirationFragment.FROM_PAGE.equalsIgnoreCase(shareViewModel.getContentFeedFromPageKey())
                || WeeeEvent.PageView.HOME.equals(shareViewModel.getContentFeedFromPageKey())
        ) {
            binding.recyclerView.setBackgroundResource(R.drawable.bg_content_feed);
        } else {
            binding.recyclerView.setBackground(null);
        }
    }

    private void attachCartViewModel() {
        if (cartViewModel == null) return;
        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;

        cartViewModel.getGlobalMiniCartPageStateLiveData().observe(viewLifecycleOwner, pageState -> {
            if (!isSupportVisible()) return;
            if (!pageState.isExpended()) {
                adapter.onPageResume(binding.recyclerView);
                ProductSyncHelper.onPageResume(adapter);
            }
        });
    }

    private void migrateViewModel() {
        try {
            CmsContentFeedPacket myPacket = new CmsContentFeedPacket();
            CmsContentFeedPacket sharedPacket = shareViewModel.getContentFeedPacket();
            if (sharedPacket != null) {
                String key = getContentCategoryKey();
                CmsContentFeedStore dataStore = sharedPacket.getDataStore(key);
                viewModel.dataStore = dataStore;
                CmsContentFeedListBean listBean = new CmsContentFeedListBean();
                listBean.tabs = sharedPacket.t.tabs;
                if (dataStore != null) {
                    listBean.contents = dataStore.getData();
                }
                myPacket.t = listBean;
                myPacket.lastPageSize = 0;
                myPacket.componentKey = sharedPacket.componentKey;
                myPacket.pageTarget = sharedPacket.pageTarget;
                myPacket.position = sharedPacket.position;
                myPacket.property = sharedPacket.property;
                myPacket.type = sharedPacket.type;
                myPacket.traceId = sharedPacket.traceId;
                myPacket.setBaseDataSource(sharedPacket.getBaseDataSource());
                viewModel.recommendSession = sharedPacket.recommendSession;
            }
            viewModel.contentFeedPacket = myPacket;
            viewModel.fromPage = shareViewModel.getContentFeedFromPageKey();

            onCartEditListener.setEnabled(shareViewModel.isOnCartEditListenerEnabled());
        } catch (Exception ignored) {
            // no op
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        showScrollTop();
        adapter.onPageResume(binding.recyclerView);
        PostCoverVideoHelper.resumeVideo(binding.recyclerView);
        ProductSyncHelper.onPageResume(adapter);
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause(binding.recyclerView);
    }

    @Override
    public void onStop() {
        super.onStop();
        PostCoverVideoHelper.pauseVideo(binding.recyclerView, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (adapter != null) {
            BaseQuickAdapter.RequestLoadMoreListener emptyLoadMoreListener = () -> {
            };
            adapter.setOnLoadMoreListener(emptyLoadMoreListener, binding.recyclerView);
            adapter.setOnItemChildClickListener(null);
            adapter.setOnCartEditListener(null);
            adapter.destroy();
            binding.recyclerView.removeOnScrollListener(scrollTopListener);
            scrollTopListener = null;
            binding.recyclerView.destroyView();
        }
        adapter = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PostCoverVideoManager.clearVideo(String.valueOf(hashCode()));
    }

    protected void loadMore() {
        viewModel.fetchMoreFeedData();
    }

    private void showLoadVeil(boolean show) {
        List<AdapterDataType> veils;
        if (show) {
            veils = new ArrayList<>(6);
            for (int i = 0; i < 6; i++) {
                veils.add(new SimpleVeilData(R.layout.veil_global_feed_item));
            }
        } else {
            veils = CollectionUtils.emptyList();
        }
        if (adapter != null) {
            adapter.setNewData(veils);
        }
    }

    RecyclerView.OnScrollListener scrollTopListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                adapter.onPageScrollStateChanged(recyclerView, newState);
                LabelScrollHandler.notifyScrollStateChanged(recyclerView);//label scroll
            }
            OpActionHelper.notifyScrollStateChanged(newState);
        }

        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            showScrollTop();
        }
    };

    private void addShowScrollTopListener() {
        binding.recyclerView.addOnScrollListener(scrollTopListener);
    }

    private void showScrollTop() {
        Fragment fragment = getParentFragment();
        if (fragment instanceof IContentScrollTop) {
            ((IContentScrollTop) fragment).showScrollTop(binding.recyclerView);
        }
    }

    public void scrollToTop() {
        binding.recyclerView.scrollToPosition(0);
    }

    private void handleGlobalCartListResponse(@Nullable GlobalCartListResponse response) {
        if (cartViewModel == null) return;
        cartViewModel.notifyGlobalCartListResponseLiveDataChange(response);
    }
}
