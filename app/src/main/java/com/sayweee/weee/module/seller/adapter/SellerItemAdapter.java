package com.sayweee.weee.module.seller.adapter;

import androidx.annotation.Nullable;
import androidx.core.util.Pair;

import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.base.adapter.SimpleItemAdapter;
import com.sayweee.weee.module.cart.adapter.OnCartEditListener;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.adapter.payload.CmsMultiDataSourceUpdate;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.CarouselBannerProviderFactory;
import com.sayweee.weee.module.cms.iml.blank.BlankProvider;
import com.sayweee.weee.module.cms.iml.line.LineProvider;
import com.sayweee.weee.module.cms.iml.product.ProductItemProvider;
import com.sayweee.weee.module.cms.iml.product.ProductLineProvider;
import com.sayweee.weee.module.cms.iml.product.data.CmsProductLineData;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.cms.track.IPageLifecycle;
import com.sayweee.weee.module.home.adapter.OnRemindListener;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.provider.product.LightingDealsProvider;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.mkpl.LabelScrollAdapter;
import com.sayweee.weee.module.mkpl.TrackingInfoAdapter;
import com.sayweee.weee.module.seller.provider.SellerSimpleTitleProvider;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ObjectUtils;
import com.sayweee.weee.widget.timer.OnTimerListener;

import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/4/12.
 * Desc:
 */
public class SellerItemAdapter extends SimpleItemAdapter<AdapterDataType, AdapterViewHolder>
        implements IPageLifecycle, TrackingInfoAdapter, LabelScrollAdapter {

    private String filterSubCategory;
    private String catalogueNum;
    private String sort;
    private Map<String, String> filters;
    private String pageTarget;
    private String pageTab;
    private String globalVendor;
    private final OnCartEditListener onCartEditListener;

    public SellerItemAdapter(OnCartEditListener onCartEditListener) {
        super();
        this.onCartEditListener = onCartEditListener;
    }

    @Override
    protected void addAdapterProvider() {
        super.addAdapterProvider();
        addItemProvider(new LineProvider());
        addItemProvider(new BlankProvider());
        addItemProvider(new CarouselBannerProviderFactory().get());
        addItemProvider(new SellerSimpleTitleProvider());
    }

    @SuppressWarnings("rawtypes")
    @Nullable
    @Override
    protected SectionProviderFactory getSectionProviderFactory() {
        return new SectionProviderFactory(super.getSectionProviderFactory()) {

            @Nullable
            @Override
            public ISectionProvider getItemProvider(int viewType) {
                ISectionProvider provider;
                switch (viewType) {
                    case CmsItemType.PRODUCT_ITEM: // product item
                        provider = ObjectUtils.apply(new ProductItemProvider(), p -> {
                            p.setShowMkplVendor(false);
                            p.setOnCartEditListener(onCartEditListener);
                            p.onCtxAdded(filterSubCategory, catalogueNum, sort, filters, pageTarget, pageTab, globalVendor);
                        });
                        break;
                    case CmsItemType.PRODUCT_LINE: // product line
                        provider = ObjectUtils.apply(new ProductLineProvider(), p -> {
                            p.setShowMkplVendor(false);
                            p.setOnCartEditListener(onCartEditListener);
                            p.onCtxAdded(filterSubCategory, catalogueNum, sort, filters, pageTarget, pageTab, globalVendor);
                        });
                        break;
                    case CmsItemType.LIGHTNING_DEALS: {
                        provider = ObjectUtils.apply(new LightingDealsProvider(), p -> {
                            p.setOnCartEditListener(onCartEditListener);
                            p.setOnTimerListener(onTimerListener);
                            p.setOnRemindListener(onRemindListener);
                        });
                        break;
                    }
                    default:
                        provider = parentFactory != null ? parentFactory.getItemProvider(viewType) : null;
                        break;
                }
                return provider;
            }
        };
    }

    @Override
    public void onCtxAdded(String filterSubCategory, String catalogueNum, String sort, Map<String, String> filters, String pageTarget, String pageTab, String globalVendor) {
        this.filterSubCategory = filterSubCategory;
        this.catalogueNum = catalogueNum;
        this.sort = sort;
        this.filters = filters;
        this.pageTarget = pageTarget;
        this.pageTab = pageTab;
        this.globalVendor = globalVendor;
        if (!EmptyUtils.isEmpty(providers)) {
            for (int i = 0; i < providers.size(); i++) {
                ISectionProvider<?, ?> sectionProvider = providers.valueAt(i);
                if (sectionProvider instanceof TrackingInfoAdapter) {
                    ((TrackingInfoAdapter) sectionProvider).onCtxAdded(filterSubCategory, catalogueNum, sort, filters, pageTarget, pageTab, globalVendor);
                }
            }
        }
    }

    private OnTimerListener onTimerListener;

    public void setOnLightingDealTimerListener(OnTimerListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.LIGHTNING_DEALS);
        if (provider instanceof LightingDealsProvider) {
            ((LightingDealsProvider) provider).setOnTimerListener(listener);
        } else {
            onTimerListener = listener;
        }
    }

    private OnRemindListener onRemindListener;

    public void setOnRemindListener(OnRemindListener listener) {
        onRemindListener = listener;
        ISectionProvider<?, ?> provider = getItemProvider(CmsItemType.LIGHTNING_DEALS);
        if (provider instanceof LightingDealsProvider) {
            ((LightingDealsProvider) provider).setOnRemindListener(listener);
        }
    }

    public void revokeRemind(int productId) {
        if (CollectManager.get().isProductCollect(productId)) {
            CollectManager.get().toggleProductCollect(productId);
            if (refreshRemindSet(productId, false)) {
                refreshLightDealsData(productId);
            }
        }
    }

    public void refreshLightDealsData(@Nullable Integer productId) {
        int lightingDealsIndex = CollectionUtils.indexOfFirst(mData, it -> it instanceof CmsLightingDealsData);
        AdapterDataType data = CollectionUtils.getOrNull(mData, lightingDealsIndex);
        if (data instanceof CmsLightingDealsData) {
            CmsLightingDealsData item = (CmsLightingDealsData) data;
            if (productId == null) {
                notifyItemChanged(lightingDealsIndex);
            } else {
                for (int i = 0, size = item.t.products.size(); i < size; i++) {
                    LightningDealsProductBean bean = item.t.products.get(i);
                    if (bean.id == productId) {
                        notifyItemChanged(lightingDealsIndex, i);
                    }
                }
            }
        }
    }

    public boolean refreshRemindSet(int productId, boolean isRemind) {
        List<AdapterDataType> items = getData();
        if (items.isEmpty()) {
            return false;
        }
        boolean success = false;
        for (AdapterDataType item : items) {
            if (item instanceof CmsLightingDealsData) {
                for (LightningDealsProductBean product : ((CmsLightingDealsData) item).t.products) {
                    if (product.id == (int) productId) {
                        product.remind_set = isRemind;
                        success = true;
                    }
                }
            }
        }
        return success;
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        for (int i = start; i <= end; i++) {
            AdapterDataType data = getItem(i);
            if (data instanceof CmsProductLineData || data instanceof ProductItemData) {
                notifyItemChanged(i, data);
            }
        }
    }

    public void notifyMultiDataSourceUpdate(String componentId) {
        if (componentId == null || componentId.isEmpty()) return;
        Pair<Integer, AdapterDataType> pair = CollectionUtils.firstOrNullWithIndex(
                getData(),
                item -> {
                    if (item instanceof ComponentData) {
                        ComponentData<?, ?> data = (ComponentData<?, ?>) item;
                        return componentId.equals(data.getComponentId());
                    }
                    return false;
                }
        );
        if (pair != null) {
            notifyItemChanged(pair.first, new CmsMultiDataSourceUpdate(componentId));
        }
    }

}