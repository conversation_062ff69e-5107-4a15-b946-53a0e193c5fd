package com.sayweee.weee.module.account.service;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.appsflyer.AFInAppEventType;
import com.sayweee.core.order.OrderRecreateType;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.service.ContextService;
import com.sayweee.service.context.store.bean.StoreInfo;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.LoginBean;
import com.sayweee.weee.module.account.bean.LoginMethodBean;
import com.sayweee.weee.module.account.bean.LoginOptionsBean;
import com.sayweee.weee.module.account.bean.LoginShowMessageBean;
import com.sayweee.weee.module.account.bean.SendVerifyBean;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.account.bean.TokenBean;
import com.sayweee.weee.module.account.bean.VerifyInfoBean;
import com.sayweee.weee.module.launch.service.LaunchApi;
import com.sayweee.weee.module.launch.service.OnboardingHelper;
import com.sayweee.weee.module.launch.service.ReferrerManager;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.popup.bean.PopupCenterBean;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.helper.PushHelper;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.timer.bean.NewMsgBean;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.StringUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import okhttp3.RequestBody;

public class LoginMethodViewModel extends BaseViewModel<NewAccountModel> {

    public MutableLiveData<LoginMethodBean> loginMethodData = new MutableLiveData<>();
    public MutableLiveData<LoginShowMessageBean> accountCheckData = new MutableLiveData<>();

    public MutableLiveData<Boolean> btnEnable = new MutableLiveData<>();
    private LoginBean loginBean;
    //失败的提示
    public MutableLiveData<FailureBean> failureLiveData = new MutableLiveData<>();
    private boolean lackPreOrder; //该信息在登陆成功后获取订单信息判断
    //登陆成功数据
    public MutableLiveData<LoginBean> loginData = new MutableLiveData<>();
    //发送验证码成功 1 sms 2 phone
    public MutableLiveData<Integer> sendCodeData = new MutableLiveData<>();
    public MutableLiveData<LoginOptionsBean> loginOptionData = new MutableLiveData<>();
    public MutableLiveData<VerifyInfoBean> verifyInfoData = new MutableLiveData<>();
    public MutableLiveData<SendVerifyBean> sendVerifyData = new MutableLiveData<>();

    protected boolean isEduEmailCheck;
    public MutableLiveData<LoginBean> registerResultData = new MutableLiveData<>();

    public LoginMethodViewModel(@NonNull Application application) {
        super(application);
    }

    public void getLoginMethod() {
        getLoginMethodSubscribe(getLoader().getLoginMethod());
    }

    private void getLoginMethodSubscribe(Observable<ResponseBean<LoginMethodBean>> observable) {
        observable.subscribe(new ResponseObserver<ResponseBean<LoginMethodBean>>() {
            @Override
            public void onBegin() {
                super.onBegin();
                setLoadingStatus(true);
            }

            @Override
            public void onResponse(ResponseBean<LoginMethodBean> response) {
                loginMethodData.postValue(response.getData());
            }

            @Override
            public void onFinish() {
                super.onFinish();
                setLoadingStatus(false);
            }
        });
    }

    public void checkAccount(String email) {
        getLoader()
                .postLoginShowMessage(new RequestParams().put("value", email).create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<LoginShowMessageBean>>() {
                    @Override
                    public void onResponse(ResponseBean<LoginShowMessageBean> response) {
                        LoginShowMessageBean data = response.getData();
                        if (data.check_email) {
                            long nowDate = System.currentTimeMillis();
                            String deviceId = ContextService.get().getDeviceId(LifecycleProvider.get().getApplication());
                            String md5 = StringUtils.MD5(email + deviceId + nowDate + "7l0MW3HDXlC11eqfdXeD5eUkNSZA");
                            registerByEmail(new RequestParams()
                                            .put("email", email)
                                            .put("from", "first_time_use")
                                            .put("source", "newFlow")
                                            .put("timestamp", nowDate)
                                            .put("channelID", PushHelper.getChannelId())
                                            .put("pushToken", PushHelper.getPushToken())
                                            .create()
                                    , md5);
                        } else {
                            accountCheckData.postValue(data);
                            btnEnable.postValue(true);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (failure.getMessage() != null) {
                            failureLiveData.postValue(failure);
                        }
                        btnEnable.postValue(true);
                    }
                });
    }

    public void loginByGoogle(RequestBody body) {
        loginSubscribe(getLoader().loginByGoogle(body), true);
    }

    public void loginByLine(RequestBody body) {
        loginSubscribe(getLoader().loginByLine(body), true);
    }

    public void loginByFacebook(RequestBody body) {
        loginSubscribe(getLoader().loginByFacebook(body), true);
    }

    public void loginByEmail(RequestBody body, boolean isLoading) {
        loginSubscribe(getLoader().loginByEmail(body), isLoading);
    }

    public void loginByWeChat(RequestBody body) {
        loginSubscribe(getLoader().loginByWeChat(body), true);
    }

    public void loginByTiktok(Map<String, Serializable> body, boolean isLoading) {
        loginSubscribe(getLoader().getHttpService().loginByTiktok(body).compose(ResponseTransformer.scheduler(this)), isLoading);
    }


    public void loginSubscribe(Observable<ResponseBean<LoginBean>> observable, boolean isLoading) {
        observable.subscribe(new ResponseObserver<ResponseBean<LoginBean>>() {
            @Override
            public void onBegin() {
                super.onBegin();
                if (isLoading) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onResponse(ResponseBean<LoginBean> response) {
                onLoginResult(response.getData(), isLoading);
            }

            @Override
            public void onError(FailureBean failure) {
                if (failure.getMessage() != null) {
                    failureLiveData.postValue(failure);
                }
            }

            @Override
            public void onFinish() {
                super.onFinish();
                if (isLoading) {
                    setLoadingStatus(false);
                }
            }
        });
    }

    private void bindSubscribe(Observable<ResponseBean<LoginBean>> observable, boolean isLoading) {
        observable.subscribe(new ViewModelResponseObserver<ResponseBean<LoginBean>>() {

            @Override
            public void onResponse(ResponseBean<LoginBean> response) {
                //绑定返回的数据可能为空或者数据不准确 以登陆返回为准 此处仅用于请求结束处理
                loginBean = response.getData();
                SharedViewModel.get().refreshAccountInfo();
                getSimplePreOrder(isLoading);
            }

            @Override
            public void onError(FailureBean failure) {
                if (failure.getMessage() != null) {
                    failureLiveData.postValue(failure);
                } else {
                    if (DevConfig.isDevelop()) {
                        showErrorToast(failure);
                    }
                }
            }
        });
    }

    public void onLoginResult(LoginBean loginBean, boolean isLoading) {
        this.loginBean = loginBean;
        onAccountLogin(loginBean);
        SharedViewModel.get().refreshAccountInfo();
        getSimplePreOrder(isLoading);
    }

    private void onAccountLogin(LoginBean bean) {
        if (bean != null) {
            trackOnAccountLogin(bean);
            TokenBean token = new TokenBean();
            token.token = bean.token;
            token.token_expire = bean.token_expire;
            AccountManager.get().setToken(token);
            AccountManager.get().setUserId(bean.user_id);
            AccountManager.get().setUID(bean.uid);
            AccountManager.get().setUserType(bean.user_type);
            AccountManager.get().login();
            CollectManager.get().onLogin();
        }
    }

    private void trackOnAccountLogin(LoginBean bean) {
        AppTracker.get().setTrackUserId(bean.user_id);
        if (bean.is_new_user) {
            AppTracker.get().trackByAF(AFInAppEventType.COMPLETE_REGISTRATION);
        }
    }

    /**
     * 登陆成功 先获取preOrder信息
     */
    public void getSimplePreOrder(boolean isLoading) {
        getLoader().createHttpService(LaunchApi.class)
                .getSimplePreOrder()
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<SimplePreOrderBean>>() {
                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (isLoading) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<SimplePreOrderBean> response) {
                        OrderManager.get().setSimpleOrderData(response.getData());
                        SharedOrderViewModel.get().onPreOrderRecreate(OrderRecreateType.ACCOUNT_CHANGED);
                        StoreManager.get().getStoreInfoOnAccountChanged(OrderManager.get().getZipCode(), new SimpleObserver<StoreInfo>() {
                            @Override
                            public void onComplete() {
                                onSimplePreOrderRequested();
                            }
                        });
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        boolean lack = OrderManager.get().lackOfPreOrder(failure.getMessageId());
                        if (lack) { //缺少preOrder，需要输入zipCode，重新生成
                            lackPreOrder = true;
                        }
                        onSimplePreOrderRequested();
                    }
                });
    }

    private void onSimplePreOrderRequested() {
        if (loginBean != null) {
            String targetAction = PopupCenterManager.ACTION_LOGIN_REGISTER_SUCCESS;
            if (PopupCenterManager.get().isTargetPreCheck(targetAction)) {
                PopupCenterManager.get().execCheckPopup(targetAction, new ResponseObserver<ResponseBean<PopupCenterBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PopupCenterBean> response) {
                        PopupCenterManager.get().processPopupResponse(targetAction, response.getData());
                    }

                    @Override
                    public void onFinish() {
                        if (!loginBean.is_binded || loginBean.is_need_bind == 1) {
                            getNewUserCouponAmount();
                        } else {
                            onAccountDataRequestedAll();
                        }
                    }
                });
            } else {
                if (!loginBean.is_binded || loginBean.is_need_bind == 1) {
                    getNewUserCouponAmount();
                } else {
                    onAccountDataRequestedAll();
                }
            }
        }
        setLoadingStatus(false);
    }

    //Get login amount
    public void getNewUserCouponAmount() {
        getLoader().getHttpService().getNewUserMessage().compose(ResponseTransformer.scheduler(this))
                .subscribe(new ViewModelResponseObserver<ResponseBean<NewMsgBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewMsgBean> response) {
                        NewMsgBean data = response.getData();
                        loginBean.bindTitle = data.getContentText(data.android_subtitle_bindPhone);
                        loginBean.bindDesc = data.getContentText(data.android_bindPhoneTip_phone_connect);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        onAccountDataRequestedAll();
                    }
                });
    }

    private void onAccountDataRequestedAll() {
        if (isEduEmailCheck) {
            isEduEmailCheck = false;
            registerResultData.postValue(loginBean);
        } else {
            loginData.postValue(loginBean);
        }
    }

    public boolean lackOfPreOrder() {
        return lackPreOrder;
    }

    public void getLoginOption() {
        Observable.create(new ObservableOnSubscribe<LoginOptionsBean>() {
                    @Override
                    public void subscribe(ObservableEmitter<LoginOptionsBean> emitter) throws Exception {
                        LoginOptionsBean bean = AccountManager.get().getLoginOption();
                        if (bean == null) {
                            bean = new LoginOptionsBean();
                        }
                        emitter.onNext(bean);
                        emitter.onComplete();
                    }
                }).compose(ResponseTransformer.scheduler(this))
                .subscribe(new SimpleObserver<LoginOptionsBean>() {
                    @Override
                    public void onNext(LoginOptionsBean result) {
                        if (result != null && result.connects != null) {
                            loginOptionData.postValue(result);
                        }
                        loginConnectsCompat(result == null || result.connects == null);
                    }
                });
    }

    private void loginConnectsCompat(boolean loading) {
        long timestamp = System.currentTimeMillis();
        Observable<ResponseBean<LoginOptionsBean>> loginConnectsObservable = getLoader().loginConnects();
        Observable<ResponseBean<NewMsgBean>> couponMessageObservable = getLoader().getHttpService().getNewUserMessage().compose(ResponseTransformer.scheduler(this));
        Observable.mergeDelayError(loginConnectsObservable, couponMessageObservable)
                .compose(DisposableTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<?>>() {
                    LoginOptionsBean loginOptionsBean;
                    NewMsgBean couponMessageBean;

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (loading) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<?> response) {
                        Object responseData = response.getData();
                        if (responseData instanceof LoginOptionsBean) {
                            loginOptionsBean = (LoginOptionsBean) responseData;
                        } else if (responseData instanceof NewMsgBean) {
                            couponMessageBean = (NewMsgBean) responseData;
                        }
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        if (loading) {
                            long diff = System.currentTimeMillis() - timestamp;
                            delaySetLoadingStatusFalse(800 - diff);
                        }

                        if (loginOptionsBean != null) {
                            if (couponMessageBean != null) {
                                loginOptionsBean.app_sign_message_title = couponMessageBean.getContentText(couponMessageBean.android_title_signup);
                                loginOptionsBean.app_sign_message_sub_title = couponMessageBean.getContentText(couponMessageBean.android_subTitle_signup);
                                loginOptionsBean.app_bind_message_title = couponMessageBean.getContentText(couponMessageBean.android_subtitle_bindPhone);
                                loginOptionsBean.app_bind_message_phone_connect = couponMessageBean.getContentText(couponMessageBean.android_bindPhoneTip_phone_connect);
                            } else {
                                loginOptionsBean.app_sign_message_title = null;
                                loginOptionsBean.app_sign_message_sub_title = null;
                                loginOptionsBean.app_bind_message_title = null;
                                loginOptionsBean.app_bind_message_phone_connect = null;
                            }
                            LoginOptionsBean value = loginOptionData.getValue();
                            LoginOptionsBean options = loginOptionsBean;
                            if (value != null && options != null) {
                                if (value.equals(options)) {
                                    //和上次相同
                                    return;
                                }
                            }
                            AccountManager.get().setLoginOption(options);
                            loginOptionData.postValue(options);
                        }
                    }
                });
    }

    private void delaySetLoadingStatusFalse(long duration) {
        if (duration <= 0) {
            setLoadingStatus(false);
        } else {
            Observable.timer(duration, TimeUnit.MILLISECONDS)
                    .subscribe(new SimpleObserver<Long>() {
                        @Override
                        public void onNext(Long aLong) {
                            setLoadingStatus(false);
                        }
                    });
        }
    }

    public void loginByKakao(RequestBody body) {
        loginSubscribe(getLoader().loginByKakao(body), true);
    }

    public void sendErrorCode(RequestBody body) {
        getLoader().sendErrorCode(body)
                .subscribe(new ResponseObserver<SimpleResponseBean>(false) {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        if (!EmptyUtils.isEmpty(response.object)) {
                            Toaster.showToast(response.object);
                        }
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    public void registerByEmail(RequestBody body, String header) {
        getLoader()
                .registerByEmailV1(body, header)
                .compose(DisposableTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<LoginBean>>(false) {
                    @Override
                    public void onResponse(ResponseBean<LoginBean> response) {
                        LoginBean data = response.getData();
                        TokenBean token = new TokenBean();
                        token.token = data.token;
                        token.token_expire = data.token_expire;
                        AccountManager.get().setToken(token);

                        //edu check因流程较长，在此页面走完pOrder相关逻辑，正常用户在设置密码页面走完
                        isEduEmailCheck = data.is_edu_email_check;
                        if (isEduEmailCheck) {
                            onLoginResult(data, false);
                        } else {
                            OnboardingHelper.get().setGuided();
                            registerResultData.postValue(data);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (failure.getMessage() != null) {
                            failureLiveData.postValue(failure);
                        }
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        btnEnable.postValue(true);
                    }
                });
    }

    public void loginByTiktok(String code, String codeVerifier) {
        loginByTiktok(new RequestParams()
                .put("code", code)
                .put("codeVerifier", codeVerifier)
                .put("channelID", PushHelper.getChannelId())
                .put("pushToken", PushHelper.getPushToken())
                .put("source", "newFlow")
                .putNonNull("additional", null)
                .putNonNull("referrer_id", ReferrerManager.get().getLatestReferrerId())
                .putNonNull("auth_token_channel", VariantConfig.AUTH_TOKEN_CHANNEL)
                .get(), true);
    }

    public void loginByVerifyCode(RequestBody body) {
        loginSubscribe(getLoader().loginByVerifyCode(body), false);
    }

    public void signupByVerifyCode(RequestBody body) {
        loginSubscribe(getLoader().signupByVerifyCode(body), false);
    }

    public void getVerifyInfo(String code, String email) {
        getLoader().getHttpService()
                .getVerifyInfo(new RequestParams().put("key", code).put("email", email).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<VerifyInfoBean>>() {

                    @Override
                    public void onResponse(ResponseBean<VerifyInfoBean> response) {
                        verifyInfoData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                    }
                });
    }

    public void getPhoneVerifyInfo(String phone_number) {
        getLoader().getHttpService()
                .getPhoneVerifyInfo(new RequestParams().put("phone_number", phone_number).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<VerifyInfoBean>>() {

                    @Override
                    public void onResponse(ResponseBean<VerifyInfoBean> response) {
                        verifyInfoData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                    }
                });
    }

    public void sendVerifyInfo(String code, boolean isLoading) {
        getLoader().getHttpService()
                .sendEmailVerifyCode(new RequestParams().put("key", code).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<SendVerifyBean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (isLoading) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<SendVerifyBean> response) {
                        sendVerifyData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        failureLiveData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        if (isLoading) {
                            setLoadingStatus(false);
                        }
                    }
                });
    }

    public void sendErrorVerifyInfo(String code, boolean isLoading) {
        getLoader().getHttpService()
                .sendEmailErrorVerifyCode(new RequestParams().put("key", code).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<SendVerifyBean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (isLoading) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<SendVerifyBean> response) {
                        sendVerifyData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        failureLiveData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        if (isLoading) {
                            setLoadingStatus(false);
                        }
                    }
                });
    }

    public void sendVerifyInfoByNoPwd(String code, boolean isLoading) {
        getLoader().getHttpService()
                .sendEmailVerifyCode(new RequestParams().put("key", code).create())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<SendVerifyBean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        if (isLoading) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<SendVerifyBean> response) {
                        sendVerifyData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        failureLiveData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        if (isLoading) {
                            setLoadingStatus(false);
                        }
                    }
                });
    }

    public void sendVerifyCode(RequestBody body) {
        getLoader().sendVerifyCode(body)
                .subscribe(new ResponseObserver<ResponseBean<Void>>(false) {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(ResponseBean<Void> response) {
                        sendCodeData.postValue(1);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (failure.getMessage() != null) {
                            failureLiveData.postValue(failure);
                        }
                    }
                });
    }

    public void loginByPhone(RequestBody body) {
        loginSubscribe(getLoader().loginByPhone(body), false);
    }

    public void bindPhone(RequestBody body) {
        bindSubscribe(getLoader().bindByPhone(body), false);
    }

    public void restByEmail(RequestBody body, LoginBean loginBean) {
        getLoader()
                .getHttpService()
                .resetWithEmail(body)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onBegin() {
                        //
                    }

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        onLoginResult(loginBean, false);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        failureLiveData.postValue(failure);
                    }
                });
    }

    public void sendVoiceVerifyCode(RequestBody body) {
        getLoader()
                .getHttpService()
                .sendVoiceVerifyCode(body)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<Void>>(false) {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(ResponseBean<Void> response) {
                        sendCodeData.postValue(2);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (failure.getMessage() != null) {
                            failureLiveData.postValue(failure);
                        }
                    }
                });
    }
}
