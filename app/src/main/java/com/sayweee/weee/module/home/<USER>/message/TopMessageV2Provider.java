package com.sayweee.weee.module.home.provider.message;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_32;
import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.dialog.TopMsgDialog;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.bean.TopMessageV2Bean;
import com.sayweee.weee.module.home.bean.TopMessageV2ItemBean;
import com.sayweee.weee.module.home.provider.message.data.CmsTopMessageV2Data;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.timer.OnSimpleTimerListener;
import com.sayweee.weee.widget.timer.OnTimerListener;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jp.wasabeef.blurry.Blurry;

@Deprecated
public class TopMessageV2Provider extends SimpleSectionProvider<CmsTopMessageV2Data, AdapterViewHolder> implements ImpressionProvider<CmsTopMessageV2Data> {

    OnTimerListener listener;

    @Override
    public int getItemType() {
        return CmsItemType.TOP_MESSAGE_V2;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_top_message_v2;
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsTopMessageV2Data item) {
        TopMessageV2Bean messageV2Bean = item.t;
        //上半部分
        boolean hasBesideList = CollectionUtils.isNotEmpty(messageV2Bean.beside_list);
        helper.setVisibleCompat(R.id.group_beside_list, hasBesideList);
        helper.addOnClickListener(R.id.iv_icon_beside_left, R.id.tv_beside_left, R.id.iv_icon_beside_right, R.id.tv_beside_right);
        if (hasBesideList) {
            TopMessageV2ItemBean besideLeft = messageV2Bean.beside_list.get(0);
            helper.setVisibleCompat(R.id.iv_icon_beside_left, EmptyUtils.isEmpty(besideLeft.icon_img));
            ImageLoader.load(context, helper.getView(R.id.iv_icon_beside_left), WebpManager.get().getConvertUrl(SPEC_32, besideLeft.icon_img), R.drawable.shape_oval_place_size_50);
            ViewTools.setViewHtml(helper.getView(R.id.tv_beside_left), besideLeft.getShortMessage());
            if (messageV2Bean.beside_list.size() > 1) {
                TopMessageV2ItemBean besideRight = messageV2Bean.beside_list.get(1);
                helper.setVisibleCompat(R.id.iv_icon_beside_right, EmptyUtils.isEmpty(besideRight.icon_img));
                ImageLoader.load(context, helper.getView(R.id.iv_icon_beside_right), WebpManager.get().getConvertUrl(SPEC_32, besideRight.icon_img), R.drawable.shape_oval_place_size_50);
                ViewTools.setViewHtml(helper.getView(R.id.tv_beside_right), besideRight.getShortMessage());
            }
        }
        //下半部分
        boolean hasVerticalList = CollectionUtils.isNotEmpty(messageV2Bean.vertical_list);
        helper.setVisibleCompat(R.id.layout_top_message, hasVerticalList);
        if (!hasVerticalList) {
            return;
        }
        List<TopMessageV2ItemBean> list = messageV2Bean.vertical_list;
        TopMessageV2ItemBean bean = list.get(0);
        HtmlTextView tvTitle = helper.getView(R.id.tv_title);
        TextView tvSubTitle = helper.getView(R.id.tv_sub_title);
        ShapeConstraintLayout layoutTopMessage = helper.getView(R.id.layout_top_message);
        TimerTextView tvTimer = helper.getView(R.id.tv_timer);
        if (!EmptyUtils.isEmpty(bean.background)) {
            if (!EmptyUtils.isEmpty(bean.background.color)) {
                layoutTopMessage.setBackgroundSolidDrawable(Color.parseColor(bean.background.color), CommonTools.dp2px(12));
            } else if (!EmptyUtils.isEmpty(bean.background.img)) {
                Glide.with(context).load(bean.background.img).placeholder(R.mipmap.video_placeholder_new).into(new CustomTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        layoutTopMessage.setBackground(resource);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {

                    }
                });
            }
        } else {
            layoutTopMessage.setBackgroundSolidDrawable(ContextCompat.getColor(context, R.color.root_color_blue_spectrum_2), CommonTools.dp2px(12));
        }
        tvTitle.setMaxLines(!EmptyUtils.isEmpty(bean.content.sub_message) ? 2 : 3);
        tvTitle.setGravity(item.onlyTitle() ? Gravity.CENTER : Gravity.CENTER_VERTICAL | Gravity.START);
        if (!EmptyUtils.isEmpty(bean.content.short_message)) {
            tvTitle.setHtmlText(bean.content.short_message);
        }
        helper.itemView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                if (helper.itemView.getMeasuredWidth() > 0) {
                    helper.itemView.removeOnLayoutChangeListener(this);
                    if (tvTitle.getLineCount() > 1) {
                        tvSubTitle.setMaxLines(1);
                    } else {
                        tvSubTitle.setMaxLines(2);
                    }
                    ViewTools.setViewHtml(tvSubTitle, bean.content.sub_message);
                }
            }
        });
        boolean isShow = false;
        long timeInterval = 0;
        if (!EmptyUtils.isEmpty(bean.countdown)) {
            timeInterval = bean.countdown.end_time - bean.sys_time;
            isShow = timeInterval < 60 * 60 * 24;
        }

        if (isShow) {
            tvTimer.start(timeInterval + item.systemTime);
            tvTimer.setOnTimerListener(new OnSimpleTimerListener() {
                @Override
                public void onEnd() {
                    if (listener != null) {
                        listener.onEnd();
                    }
                }
            });
        } else if (item.contentIsVisible()) {
            tvTimer.setText(ViewTools.fromHtml(bean.content.sub_content));
        }
        ImageLoader.load(context, helper.getView(R.id.iv_icon), WebpManager.get().getConvertUrl(SPEC_64, bean.icon_img), R.color.color_place);
        if (item.rightImgIsVisible()) {
            ImageLoader.load(context, helper.getView(R.id.iv_link), WebpManager.get().getConvertUrl(SPEC_64, bean.right_arrow.img), R.color.color_place);
        }
        helper.setGone(R.id.tv_sub_title, !EmptyUtils.isEmpty(bean.content.sub_message));
        helper.setGone(R.id.tv_timer, isShow || item.contentIsVisible());
        helper.setGone(R.id.iv_icon, !EmptyUtils.isEmpty(bean.icon_img));
        helper.setGone(R.id.iv_link, item.rightImgIsVisible());
        helper.setOnViewClickListener(R.id.layout_top_message, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                EagleTrackManger.get().trackEagleClickAction(item.getEventKey(), item.position
                        , null
                        , -1
                        , String.valueOf(bean.id)
                        , CollectionUtils.isNotEmpty(item.t.beside_list) ? 2 : 0
                        , item.getEventKey()
                        , EagleTrackEvent.ClickType.VIEW);
                if (item.isSkipPopup()) {
                    context.startActivity(WebViewActivity.getIntent(context, bean.link));
                } else {
                    ViewGroup rootView = null;
                    ImageView imageView = null;
                    Activity activity = LifecycleProvider.get().getTopActivity();
                    if (activity != null) {
                        imageView = new ImageView(context);
                        rootView = activity.findViewById(android.R.id.content);
                        if (rootView instanceof FrameLayout) {
                            rootView.addView(imageView);
                            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(rootView.getWidth(), rootView.getHeight());
                            imageView.setLayoutParams(lp);
                            imageView.bringToFront();
                            Blurry.with(context).capture(rootView).into(imageView);
                        }
                    }

                    ViewGroup finalRootView = rootView;
                    ImageView finalImageView = imageView;
                    new TopMsgDialog(context)
                            .setTopMsgV2Data(list)
                            .setOnClickListener(new TopMsgDialog.OnClickListener() {
                                @Override
                                public void onClick(TopMsgDialog dialog, String url) {
                                    dialog.dismiss();
                                    context.startActivity(WebViewActivity.getIntent(context, url));
                                }
                            })
                            .setDismissListener(new TopMsgDialog.OnDismissListener() {
                                @Override
                                public void onDismiss(TopMsgDialog dialog) {
                                    if (finalRootView != null && finalImageView != null) {
                                        finalRootView.removeView(finalImageView);
                                    }
                                }
                            })
                            .show();
                }
            }
        });
    }

    public void setOnTimerListener(OnTimerListener listener) {
        this.listener = listener;
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CmsTopMessageV2Data item, int position) {
        String module = item.getEventKey();
        int pos = item.position;
        String key = pos + "_" + module;
        ArrayList<ImpressionBean> list = new ArrayList<>();
        //消息模块
        if (item.isValid()) {
            //上半部分
            if (CollectionUtils.isNotEmpty(item.t.beside_list)) {
                for (int i = 0; i < item.t.beside_list.size(); i++) {
                    TopMessageV2ItemBean beanBeside = item.t.beside_list.get(i);
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(module)
                            .setMod_pos(pos)
                            .setBannerId(String.valueOf(beanBeside.id))
                            .setBanner_key(null)
                            .setBanner_pos(i)
                            .setBanner_type(module)
                            .setUrl(beanBeside.link)
                            .build().getParams();
                    ImpressionBean impressionBean = new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, key + i);
                    list.add(impressionBean);
                }
            }
            //下半部分
            if (CollectionUtils.isNotEmpty(item.t.vertical_list)) {
                int i = CollectionUtils.isNotEmpty(item.t.beside_list) ? 2 : 0;
                TopMessageV2ItemBean bean = item.t.vertical_list.get(0);
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(module)
                        .setMod_pos(pos)
                        .setBannerId(String.valueOf(bean.id))
                        .setBanner_key(null)
                        .setBanner_pos(i)
                        .setBanner_type(module)
                        .setUrl(bean.link)
                        .build().getParams();
                ImpressionBean impressionBean = new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, key + i);
                list.add(impressionBean);
            }
        }
        return list;
    }
}
