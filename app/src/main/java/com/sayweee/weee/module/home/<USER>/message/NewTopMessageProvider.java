package com.sayweee.weee.module.home.provider.message;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.style.StrikethroughSpan;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.ads.bean.AdsCreativeBean;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.adapter.payload.CmsMultiDataSourceUpdate;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.cms.widget.timer.WrapperOnCmsComponentTimerListener;
import com.sayweee.weee.module.dialog.TopMsgDialog;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.bean.NewTopMessageBean;
import com.sayweee.weee.module.home.bean.TopMessageProgressBean;
import com.sayweee.weee.module.home.provider.message.data.CmsNewTopMessageData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.HorizontalProgressBar;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.timer.OnTimerListener;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jp.wasabeef.blurry.Blurry;

/**
 * Author:  wld
 * Date:    2023/7/6.
 * Desc:
 */
public class NewTopMessageProvider extends SimpleSectionProvider<CmsNewTopMessageData, AdapterViewHolder> implements ImpressionProvider<CmsNewTopMessageData> {

    private OnTimerListener onTimerListener;

    @Override
    public int getItemType() {
        return CmsItemType.NEW_TOP_MESSAGE;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_new_top_message;
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, CmsNewTopMessageData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        for (Object object : payloads) {
            if (object instanceof CmsMultiDataSourceUpdate) {
                convert(helper, item);
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsNewTopMessageData item) {
        View layoutTopMessage = helper.getView(R.id.layout_top_message);
        View layoutTopSku = helper.getView(R.id.layout_top_sku);
        if (item.isSkuType()) {
            ViewTools.setViewVisibilityIfChanged(layoutTopMessage, false);
            convertTopSku(helper, item);
        } else {
            ViewTools.setViewVisibilityIfChanged(layoutTopSku, false);
            convertTopMsg(helper, item);
        }
    }

    private void convertTopSku(AdapterViewHolder helper, CmsNewTopMessageData item) {
        View layoutTopSku = helper.getView(R.id.layout_top_sku);
        ProductBean bean = item.getProduct();
        if (bean == null) {
            ViewTools.setViewVisibilityIfChanged(layoutTopSku, false);
            return;
        }
        ViewTools.setViewVisibilityIfChanged(layoutTopSku, true);
        ImageView imageView = helper.getView(R.id.iv_icon_sku);
        ImageLoader.load(
                context,
                imageView,
                WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.getHeadImageUrl()),
                R.mipmap.iv_product_placeholder);
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        tvProductName.setText(bean.name);
        //price
        TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
        TextView tvPrice = helper.getView(R.id.tv_price);
        TextView tvBasePrice = helper.getView(R.id.tv_price_delete);
        View llTvProductMark = helper.getView(R.id.ll_tv_product_mark);
        ViewTools.setViewVisible(false, tvPriceVolume, tvPrice, tvBasePrice, llTvProductMark);
        if (bean.showVolumePrice()) {
            ViewTools.setViewVisibilityIfChanged(tvPriceVolume, true);
            Spanny s = new Spanny()
                    .append(OrderHelper.formatUSMoney(bean.price))
                    .append(context.getString(R.string.s_volume_threshold_simple, bean.volume_threshold));
            tvPriceVolume.setText(s);
        } else {
            boolean showBasePrice = bean.base_price > 0;
            ViewTools.setViewVisibilityIfChanged(tvPrice, true);
            ViewTools.setViewVisibilityIfChanged(tvBasePrice, showBasePrice);
            tvPrice.setText(OrderHelper.formatUSMoney(bean.price));
            if (showBasePrice) {
                tvBasePrice.setText(new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()));
            }
            ProductBean.LabelListBean label = CollectionUtils.firstOrNull(bean.label_list, ProductBean.LabelListBean::isOffLabelKey);
            if (label != null) {
                ViewTools.setViewVisibilityIfChanged(llTvProductMark, true);
                ShapeTextView tvMarker = helper.getView(R.id.tv_product_mark);
                tvMarker.setText(label.label_name);
            }
        }
        String modNm = item.getEventKey();
        int modPos = item.position;
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .build()
                .getElement();
        //加购action
        CartOpLayout layoutOp = helper.getView(R.id.layout_op);
        if (ProductView.isPreSell(bean.sold_status)) {
            layoutOp.setDisableStyle();
        } else {
            String source = ProductHelper.getAddCartSource(modNm);
            OpHelper.helperOp(layoutOp, bean, item, source, element);
            layoutOp.setOnShowTipsListener(view -> {
            });
        }
        //click
        AdsCreativeBean adsCreative = bean.ads_creative;
        EagleContext ctx = new EagleContext();
        ctx.setVolumePriceSupport(bean.volume_price_support);
        helper.getView(R.id.ll_click).setOnClickListener(v -> {
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm(modNm)
                    .setMod_pos(modPos)
                    .setTargetNm(String.valueOf(bean.id))
                    .setTargetPos(0)
                    .setTargetType("product")
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .addCtx(ctx.asMap())
                    .build().getParams());
            context.startActivity(WebViewActivity.getIntent(context, bean.view_link));
            if (adsCreative != null) {
                AdsManager.addAdsTrackPendingInfo(bean);
                AdsManager.get().trackClick(
                        /* productBean= */bean,
                        /* position= */0,
                        /* dbgInfo= */null
                );
            }
        });
    }

    private void convertTopMsg(AdapterViewHolder helper, CmsNewTopMessageData item) {
        ShapeConstraintLayout layoutTopMessage = helper.getView(R.id.layout_top_message);
        TimerTextView tvTimer = helper.getView(R.id.tv_timer);
        if (item == null || !item.isContentValid()) {
            ViewTools.setViewVisibilityIfChanged(layoutTopMessage, false);
            tvTimer.stop();
            return;
        }
        ViewTools.setViewVisibilityIfChanged(layoutTopMessage, true);
        List<NewTopMessageBean> list = item.t;
        NewTopMessageBean bean = list.get(0);
        //timer
        boolean isShow = false;
        long timeInterval = 0;
        if (!EmptyUtils.isEmpty(bean.countdown)) {
            timeInterval = bean.countdown.end_time - bean.sys_time;
            isShow = timeInterval > 0 && timeInterval < 60 * 60 * 24;
        }
        helper.setVisibleCompat(tvTimer, isShow || item.contentIsVisible());
        helper.getView(R.id.layout_content).requestLayout();

        boolean unpaidReminder = bean.id == 25;
        helper.setTextColor(R.id.tv_timer, ContextCompat.getColor(context, unpaidReminder ? R.color.color_link_base_1 : R.color.brand_color_tone_blue_key_primary));
        HtmlTextView tvTitle = helper.getView(R.id.tv_title);
        TextView tvSubTitle = helper.getView(R.id.tv_sub_title);
        if (!EmptyUtils.isEmpty(bean.background)) {
            if (!EmptyUtils.isEmpty(bean.background.color)) {
                layoutTopMessage.setBackgroundSolidDrawable(Color.parseColor(bean.background.color), CommonTools.dp2px(12));
            } else if (!EmptyUtils.isEmpty(bean.background.img)) {
                Glide.with(context).load(bean.background.img).placeholder(R.mipmap.video_placeholder_new).into(new CustomTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        layoutTopMessage.setBackground(resource);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {
                        //
                    }
                });
            }
        } else {
            layoutTopMessage.setBackgroundSolidDrawable(ContextCompat.getColor(context, R.color.root_color_blue_spectrum_2), CommonTools.dp2px(12));
        }
        //title
        boolean hasShortMsg = !EmptyUtils.isEmpty(bean.message.short_message);//是否显示主title
        boolean hasSubMessage = !EmptyUtils.isEmpty(bean.message.sub_message);
        helper.setVisibleCompat(tvTitle, hasShortMsg);
        helper.setVisibleCompat(tvSubTitle, hasSubMessage);
        if (hasShortMsg) {
            tvTitle.setMaxLines(hasSubMessage ? 2 : 3);
            tvTitle.setGravity(item.onlyTitle() ? Gravity.CENTER : Gravity.CENTER_VERTICAL | Gravity.START);
            tvTitle.setHtmlText(bean.message.short_message);
        }
        if (onLayoutChangeListener != null) {
            helper.itemView.removeOnLayoutChangeListener(onLayoutChangeListener);
        }
        if (hasSubMessage) {
            ViewTools.setViewHtml(tvSubTitle, bean.message.sub_message);
            adjustSubTitleLines(helper, hasShortMsg, tvTitle, tvSubTitle);
        }

        if (isShow) {
            tvTimer.start(timeInterval + item.systemTime);
            tvTimer.setOnTimerListener(new WrapperOnCmsComponentTimerListener(item.getComponentId(), onTimerListener));
        } else {
            tvTimer.stop();
            tvTimer.setOnTimerListener(null);
            if (item.contentIsVisible()) {
                tvTimer.setText(ViewTools.fromHtml(bean.message.sub_content));
            } else {
                tvTimer.setText(null);
            }
        }

        ImageLoader.load(context, helper.getView(R.id.iv_icon), WebpManager.get().getConvertUrl(SPEC_64, bean.icon_img), R.color.color_place);
        if (item.rightImgIsVisible()) {
            ImageLoader.load(context, helper.getView(R.id.iv_link), WebpManager.get().getConvertUrl(SPEC_64, bean.right_cta.img), R.color.color_place);
        }
        helper.setGone(R.id.iv_icon, !EmptyUtils.isEmpty(bean.icon_img));
        helper.setGone(R.id.iv_link, item.rightImgIsVisible());
        helper.setOnViewClickListener(R.id.layout_top_message, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(item.getEventKey())
                        .setMod_pos(item.position)
                        .setSec_nm(null)
                        .setSec_pos(-1)
                        .setTargetNm(bean.message.short_message)
                        .setTargetPos(-1)
                        .setTargetType(EagleTrackEvent.BannerType.MESSAGE)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .setUrl(item.isSkipPopup() ? bean.link : null)
                        .build().getParams());

                if (item.isSkipPopup()) {
                    context.startActivity(WebViewActivity.getIntent(context, bean.link));
                } else {
                    ViewGroup rootView = null;
                    ImageView imageView = null;
                    Activity activity = LifecycleProvider.get().getTopActivity();
                    if (activity != null) {
                        imageView = new ImageView(context);
                        rootView = activity.findViewById(android.R.id.content);
                        if (rootView instanceof FrameLayout) {
                            rootView.addView(imageView);
                            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(rootView.getWidth(), rootView.getHeight());
                            imageView.setLayoutParams(lp);
                            imageView.bringToFront();
                            Blurry.with(context).capture(rootView).into(imageView);
                        }
                    }

                    ViewGroup finalRootView = rootView;
                    ImageView finalImageView = imageView;
                    new TopMsgDialog(context)
                            .setNewTopMsgData(list)
                            .setOnClickListener(new TopMsgDialog.OnClickListener() {
                                @Override
                                public void onClick(TopMsgDialog dialog, String url) {
                                    dialog.dismiss();
                                    context.startActivity(WebViewActivity.getIntent(context, url));
                                }
                            })
                            .setDismissListener(new TopMsgDialog.OnDismissListener() {
                                @Override
                                public void onDismiss(TopMsgDialog dialog) {
                                    if (finalRootView != null && finalImageView != null) {
                                        finalRootView.removeView(finalImageView);
                                    }
                                }
                            })
                            .show();
                }
            }
        });
        TopMessageProgressBean progress = bean.progress;
        boolean showProgress = progress != null;
        helper.setVisibleCompat(showProgress, R.id.pb_value, R.id.tv_progress);
        if (showProgress) {
            HorizontalProgressBar pb = helper.getView(R.id.pb_value);
            pb.setBgColor(ViewTools.parseColor(progress.background_color, Color.WHITE));
            pb.setProgressColor(ViewTools.parseColor(progress.foreground_color, Color.BLACK));
            pb.setProgress(progress.progress_value);
            helper.setTextHtml(R.id.tv_progress, progress.title);
        }
    }

    private View.OnLayoutChangeListener onLayoutChangeListener;

    private void adjustSubTitleLines(AdapterViewHolder helper, boolean hasShortMsg, TextView tvTitle, TextView tvSubTitle) {
        onLayoutChangeListener = new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                if (helper.itemView.getMeasuredWidth() > 0) {
                    helper.itemView.removeOnLayoutChangeListener(this);
                    if (hasShortMsg && tvTitle.getLineCount() > 1) {
                        tvSubTitle.setMaxLines(1);
                    } else {
                        tvSubTitle.setMaxLines(2);
                    }
                }
            }
        };
        helper.itemView.addOnLayoutChangeListener(onLayoutChangeListener);
    }

    public void setOnTimerListener(OnTimerListener listener) {
        this.onTimerListener = listener;
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CmsNewTopMessageData item, int position) {
        String module = item.getEventKey();
        int pos = item.position;
        String key = pos + "_" + module;
        ArrayList<ImpressionBean> list = new ArrayList<>();
        if (item.isSkuType()) {
            //sku模块
            ProductBean product = item.getProduct();
            if (product == null) {
                return list;
            }

            EagleContext ctx = new EagleContext().setVolumePriceSupport(product.volume_price_support);
            Map<String, Object> element = EagleTrackManger.get().getElement(module, pos, null, -1);
            Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_LIST).setTarget(product, 0).setElement(element).setContext(ctx.asMap()).get();
            list.add(new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key));
            //ads_creative
            if (product.ads_creative != null && !product.ads_creative.isImpressionTracked) {
                product.ads_creative.isImpressionTracked = true;
                AdsManager.get().trackImpression(
                        /* productBean= */product,
                        /* position= */0,
                        /* dbgInfo= */null
                );
            }
        } else if (item.isContentValid()) {
            //消息模块
            NewTopMessageBean bean = item.t.get(0);
            String bannerKey = bean.message != null ? bean.message.short_message : null;
            Map<String, Object> params = new EagleTrackModel.Builder()
                    .setMod_nm(module)
                    .setMod_pos(pos)
                    .setBannerId(String.valueOf(bean.id))
                    .setBanner_key(bannerKey)
                    .setBanner_pos(0)
                    .setBanner_type(EagleTrackEvent.BannerType.MESSAGE)
                    .setUrl(bean.link)
                    .build().getParams();
            ImpressionBean impressionBean = new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, key);
            list.add(impressionBean);
        }
        return list;
    }
}
