package com.sayweee.weee.module.cart.bean;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.annotation.JSONField;
import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.cart.bean.setcion.CartActivityType;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class NewSectionBean implements Serializable {

    public String cart_id;
    public String type;
    public int quantity;
    public int deal_id;
    public TagInfo tag_info;
    public VendorInfo vendor_info;
    public ShippingInfo shipping_info;
    public FeeInfo fee_info;
    public ShopMoreInfo shop_more_info;
    public boolean selected;
    public String sub_type;
    public List<ActivityInfo> activity_info;
    public List<NewItemBean> items;
    public List<ServiceFeeItem> service_fee_list;

    public String getCartId() {
        return cart_id != null ? cart_id : "";
    }

    public boolean isSeller() {
        return "seller".equalsIgnoreCase(type);
    }

    public boolean isPantry() {
        return Constants.OrderType.PANTRY.equalsIgnoreCase(type);
    }

    public boolean isPresale() {
        return NewSectionBean.isPresale(type);
    }

    public List<NewItemBean> getCartItems() {
        List<NewItemBean> list = new ArrayList<>();
        if (hasActivityInfo()) {
            for (ActivityInfo activityInfo : activity_info) {
                if (activityInfo.hasItems()) {
                    list.addAll(activityInfo.items);
                }
            }
        }
        if (!EmptyUtils.isEmpty(items)) {
            list.addAll(items);
        }
        return list;
    }

    public List<NewItemBean> getValidCartItems() {
        List<NewItemBean> list = new ArrayList<>();
        List<NewItemBean> cartItems = getCartItems();
        if (!EmptyUtils.isEmpty(cartItems)) {
            for (NewItemBean cartItem : cartItems) {
                if (OrderProvider.get().isValidProduct(cartItem.status) || cartItem.is_gift) {
                    list.add(cartItem);
                }
            }
        }
        return list;
    }

    public boolean isAllInvalid() {
        boolean b = true;
        for (NewItemBean item : getCartItems()) {
            if (OrderProvider.get().isValidProduct(item.status)) {
                b = false;
                break;
            }
        }
        return b;
    }

    public boolean hasActivityInfo() {
        return !EmptyUtils.isEmpty(activity_info);
    }

    @Nullable
    public String getVendorIdString() {
        return vendor_info != null ? String.valueOf(vendor_info.vendor_id) : null;
    }

    @Nullable
    public Integer getVendorIdInt() {
        return vendor_info != null ? vendor_info.vendor_id : null;
    }

    public boolean hasFreeShippingDiffDesc() {
        return shipping_info != null && !EmptyUtils.isEmpty(shipping_info.free_shipping_diff_desc);
    }

    public static boolean isPresale(String type) {
        return type != null && type.startsWith(Constants.OrderType.PRESALE);
    }

    public boolean isIndividualCheckout() {
        return Constants.OrderSubType.COMMISSION_PARTNER.equals(sub_type);
    }

    public static class VendorInfo implements Serializable {

        /**
         * vendor_id : 6943
         * vendor_name : 十秒到云南过桥米线 x Mango Mango
         * vendor_is_opened : true
         * is_vender_cut_off : false
         * vender_logo_url : null
         * show_restaurant_invalid_label : null
         * pre_order_desc : null
         */

        public int vendor_id;
        public String vendor_name;
        public boolean vendor_is_opened;
        public boolean is_vender_cut_off;
        public String vender_logo_url;
        public String show_restaurant_invalid_label;
        public String pre_order_desc;
    }

    public static class ShippingInfo implements Serializable {
        /**
         * delivery_mode : delivery
         * delivery_pickup_date : 2023-07-03
         * estimated_time : null
         * shipping_shipment_date : 周一, 07/03
         * delivery_time_id : 18
         * original_shipping_free_fee : 35.00
         * shipping_free_fee : 35.00
         * free_shipping_desc : 去凑单
         * free_shipping_desc_url : https://tb1.sayweee.net/zh/category/trending?filter_sub_category=trending&filters=%7B%22delivery%22%3Atrue%7D&showmode=new_page
         * orignal_shipping_fee : 5.00
         * shipping_fee : 0.00
         * shipping_icon_url : https://img06.weeecdn.com/common/image/000/638/16751444170000.png
         * shipping_type_desc : 本地配送
         * shipping_desc : 请留意Weee!的送货车
         * shipping_delay_desc : null
         * short_shipping_delay_desc : null
         * cold_package_fee : null
         * orignal_cold_package_fee : null
         * free_cold_package_fee : null
         * shipping_fee_type : 1
         * delivery_time : 11:00 AM - 10:00 PM
         * delivery_time_desc : null
         * delivery_content : null
         * shipping_minimum_threshold : null
         * hotdish_wave : null
         * hotdish_delivery_time_content : null
         * pickup_time : null
         * pickup_date : null
         * self_pickup_address : null
         * pick_up_point : null
         * eta_time_content : null
         * is_support_change_date : true
         */

        public String delivery_mode;
        public String delivery_pickup_date;
        public String estimated_time;
        public String shipping_shipment_date;
        public int delivery_time_id;
        public String original_shipping_free_fee;
        public double shipping_free_fee;
        public String free_shipping_desc;
        public String free_shipping_desc_url;
        public double orignal_shipping_fee;
        public String shipping_fee;
        public String shipping_icon_url;
        public String shipping_type_desc;
        public String shipping_desc;
        public String shipping_delay_desc;
        public String short_shipping_delay_desc;
        public String cold_package_fee;
        public double orignal_cold_package_fee;
        public double free_cold_package_fee;
        public int shipping_fee_type;
        public String delivery_time;
        public Object delivery_time_desc;
        public Object delivery_content;
        public Object shipping_minimum_threshold;
        public HotdishWave hotdish_wave;
        public String hotdish_delivery_time_content;
        public String pickup_time;
        public Object pickup_date;
        public Object self_pickup_address;
        public Object pick_up_point;
        public String eta_time_content;
        public boolean is_support_change_date;
        public String free_shipping_diff_desc;
        public String shipping_fee_popup_url;

        @JSONField(serialize = false, deserialize = false)
        public boolean isFreeDelivery() {
            if (!EmptyUtils.isEmpty(shipping_fee)) {
                double shippingFeeD = Double.parseDouble(shipping_fee);
                return shippingFeeD <= 0;
            } else {
                return true;
            }
        }
    }

    public static class FeeInfo implements Serializable {
        /**
         * sub_total_price : 72.60
         * shipping_fee : 0.00
         * tax : 0.00
         * tip : 0.00
         * service_fee : 0.00
         * points_price : 0.00
         * activity_save_amount : 0.00
         * total_price_with_activity : 72.60
         * coupon_discount : 0.00
         * final_amount : 72.60
         * discount : 0.00
         */

        public String sub_total_price;
        //public String shipping_fee;
        public String tax;
        public String tip;
        public String service_fee;
        public String points_price;
        public String activity_save_amount;
        public String total_price_with_activity;
        public String coupon_discount;
        public String final_amount;
        public String discount;
    }

    public static class ShopMoreInfo implements Serializable {
        /**
         * tag : {"tag_type":"text","tag_text":"换购","tag_type_text_color":"#FFFFFF","tag_type_bg_color":"#E42D12","tag_icon_url":null}
         * content : <span style= "color:#111111;font-weight:600;font-size:11px">您选择了0/5换购商品！</span>
         * desc : {"tag_type":"text","tag_text":"去选择","tag_type_text_color":"#0975D8","tag_type_bg_color":null,"tag_icon_url":null}
         * url : https://tb1.sayweee.net/zh/promotion/trade-in?type=normal&deal_id=483405&delivery_sale_org_id=1&filter_alcohol=false
         * shipping_fee_diff_price : null
         */

        public Tag tag;
        public String content;
        public Desc desc;
        public String url;
        public Object shipping_fee_diff_price;
        public List<Button> buttons;

        public static class Tag implements Serializable {
            /**
             * tag_type : text
             * tag_text : 换购
             * tag_type_text_color : #FFFFFF
             * tag_type_bg_color : #E42D12
             * tag_icon_url : null
             */

            public String tag_type; //todo
            public String tag_text;
            public String tag_type_text_color;
            public String tag_type_bg_color;
            public Object tag_icon_url; //todo ？ 用在何处
        }

        public static class Desc implements Serializable {
            /**
             * tag_type : text
             * tag_text : 去选择
             * tag_type_text_color : #0975D8
             * tag_type_bg_color : null
             * tag_icon_url : null
             */

            public String tag_type; //todo
            public String tag_text;
            public String tag_type_text_color;
            public Object tag_type_bg_color; //todo
            public Object tag_icon_url;
        }

        public static class Button implements Serializable {

            public static final String TYPE_GROUP_BUY = "group_buy";

            public Desc desc;
            public String url;
            public String tip;
            public String type;
        }
    }

    public static class ActivityInfo implements Serializable {
        /**
         * id : null
         * type : trade_in
         * original_type : null
         * tag : 换购
         * content : 您选择了%s/5换购商品！
         * url : https://tb1.sayweee.net/zh/promotion/trade-in?type=normal&deal_id=483405&delivery_sale_org_id=1&filter_alcohol=false
         * desc : 去选择
         * limit_amount : 68.0
         * limit_count : 5
         * limit_content : 再花$%s可以解锁特殊优惠！
         * cart_content : null
         * cart_content_enki : null
         * diff_amount : 0
         * order_original_amount : null
         * order_final_amount : null
         * is_offer : true
         * offer_content : 您选择了%s/5换购商品！
         * save_amount : null
         * items : []
         * gift_items : null
         * activity_items : null
         * rule_title_color : null
         * promote_tip_color : null
         * background_color : null
         * rule_show : null
         * rule_title : null
         * rule_desc : null
         * source : null
         * icon_type : null
         * limit_time : null
         * use_start_time : null
         * use_end_time : null
         * promo_type : null
         * discount : null
         * discount_price : null
         * max_discount_amount : null
         * order_sku_threshold : null
         * vendor_pay_flat : null
         * vendor_pay_factor : null
         * pc_popup : true
         * product_ids : null
         */

        public String id;
        public String type;
        public String original_type;
        public String tag;
        public String content;
        public String url;
        public String desc;
        public double limit_amount;
        public double diff_amount;
        public int limit_count;
        public String limit_content;
        public String cart_content;
        public String cart_content_enki;
        public double order_original_amount;
        public double order_final_amount;
        public boolean is_offer;
        public String offer_content;
        public double save_amount;
        public List<NewItemBean> gift_items;
        public Object activity_items;
        public String rule_title_color;
        public String promote_tip_color;
        public String background_color;
        public Object rule_show;
        public Object rule_title;
        public Object rule_desc;
        public Object source;
        public Object icon_type;
        public Object limit_time;
        public Object use_start_time;
        public Object use_end_time;
        public Object promo_type;
        public Object discount;
        public Object discount_price;
        public Object max_discount_amount;
        public Object order_sku_threshold;
        public Object vendor_pay_flat;
        public Object vendor_pay_factor;
        public boolean pc_popup;
        public List<String> product_ids;
        public List<NewItemBean> items;
        public RecommendBean recommend;
        public String tag_icon;
        public boolean is_popup;

        @JSONField(serialize = false, deserialize = false)
        public boolean hasItems() {
            return CollectionUtils.isNotEmpty(items);
        }

        //非trade in的直接展示旧样式
        @JSONField(serialize = false, deserialize = false)
        public boolean isCartDealType() {
            //return "cart_deal".equalsIgnoreCase(type) && recommend != null && "trade_in".equalsIgnoreCase(original_type);
            return "cart_deal".equalsIgnoreCase(type);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean hasRecommendItems() {
            return recommend != null && CollectionUtils.isNotEmpty(recommend.items);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean isTradeInOriginalType() {
            return "trade_in".equalsIgnoreCase(original_type);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean hasNextUrl() {
            return recommend != null && !EmptyUtils.isEmpty(recommend.next_url);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean isFreeShippingType() {
            return "free_shipping".equalsIgnoreCase(type);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean isTradeInNewType() {
            return CartActivityType.TRADE_IN_NEW.equals(type);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean isTradeInType() {
            return CartActivityType.TRADE_IN.equals(type);
        }

        @JSONField(serialize = false, deserialize = false)
        public boolean isTargetCouponType() {
            return "target_coupon".equalsIgnoreCase(type);
        }

    }

}
