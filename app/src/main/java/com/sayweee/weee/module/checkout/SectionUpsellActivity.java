package com.sayweee.weee.module.checkout;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.bean.UpsellMoreData;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.checkout.bean.UpSellListData;
import com.sayweee.weee.module.checkout.service.SectionUpsellViewModel;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.adapter.UpSellItemAdapter;
import com.sayweee.weee.module.presale.IPresaleIgnoreMoreLinkPage;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.refresh.HorizontalLoadingMoreView;
import com.sayweee.widget.toaster.IToaster;
import com.sayweee.widget.toaster.IToasterController;
import com.sayweee.widget.toaster.IToasterOptions;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

import java.io.Serializable;
import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/5/8.
 * Desc:
 */
public class SectionUpsellActivity extends WrapperMvvmActivity<SectionUpsellViewModel>
        implements IToasterController, IPresaleIgnoreMoreLinkPage {

    private RecyclerView mRecyclerView;
    private TextView tvAmount;

    private double amount;
    private final List<ProductBean> list = new ArrayList<>();
    private EagleImpressionTrackerIml eagleImpressionTracker;
    private final List<SoftReference<BaseQuickAdapter<?, ?>>> childAdapterCaches = new ArrayList<>();
    private double total;

    public static Intent getIntent(Context context, UpSellBean bean, double amount, boolean isShowAlcoholAgreement) {
        if (isShowAlcoholAgreement) {
            return SectionAlcoholAgreementActivity.getIntentBeforeUpsell(context, bean, amount);
        } else {
            return new Intent(context, SectionUpsellActivity.class)
                    .putExtra("bean", bean)
                    .putExtra("amount", amount);
        }
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_up_sell;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        if (useWrapper()) {
            TextView tvTitleCenter = getWrapperTitle().findViewById(R.id.tv_title_center);
            tvTitleCenter.setTextColor(getColor(R.color.color_navbar_fg_default));
            ImageView ivTitleLeft = getWrapperTitle().getView(R.id.iv_title_left);
            ViewTools.tintImageView(ivTitleLeft, R.color.color_navbar_fg_default);
        }

        eagleImpressionTracker = new EagleImpressionTrackerIml();
        tvAmount = findViewById(R.id.tv_amount);
        mRecyclerView = findViewById(R.id.mRecyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        mRecyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onPageImpressionTrigger();
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });

        setOnClickListener(R.id.layout_next, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Map<String, Object> context = new TrackParams()
                        .put("purchase_amount", total).get();
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setTargetNm("continue")
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.NORMAL)
                        .addCtx(context)
                        .build().getParams());
                startActivity(CheckoutSectionActivity.getIntent(activity, Constants.CartDomain.DOMAIN_GROCERY, null));
                finish();
            }
        });

        new ProductTraceObserver(this) {
            @Override
            protected void handleProductSalesTraceChange() {
                ProductTraceViewHelper.notify(mRecyclerView);
            }
        }.setExtraTopic(WeeeEvent.PageView.BEFORE_YOU_CHECKOUT).start();
    }

    @Override
    public void loadData() {
        Intent intent = getIntent();
        Serializable serializable = intent.getSerializableExtra("bean");
        if (serializable instanceof UpSellBean) {
            UpSellBean upSellBean = (UpSellBean) serializable;
            setWrapperTitle(upSellBean.title);
            viewModel.loadData(upSellBean);
        }

        amount = intent.getDoubleExtra("amount", 0);
        calcOrderAmount();
    }

    @Override
    public void attachModel() {
        viewModel.adapterData.observe(this, this::convertUpsellData);

        viewModel.appendData.observe(this, new Observer<UpsellMoreData>() {
            @Override
            public void onChanged(UpsellMoreData upsellMoreData) {
                RecyclerView.Adapter<?> adapter = mRecyclerView.getAdapter();
                if (adapter != null) {
                    adapter.notifyItemChanged(upsellMoreData.index, upsellMoreData);
                }
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        onPageResumeImpression(true);
    }

    @Override
    protected void onPause() {
        super.onPause();
        onPageResumeImpression(false);
    }

    @Override
    protected void onDestroy() {
        childAdapterCaches.clear();
        super.onDestroy();
    }

    protected void convertUpsellData(List<UpSellListData> list) {
        if (list != null) {
            mRecyclerView.setAdapter(new BaseQuickAdapter<UpSellListData, AdapterViewHolder>(R.layout.item_up_sell, list) {

                @Override
                public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
                    super.onViewAttachedToWindow(holder);
                    ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
                    if (layoutParams instanceof RecyclerView.LayoutParams) {
                        RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                        int position = params.getViewLayoutPosition();
                        params.bottomMargin = CommonTools.dp2px(position == getItemCount() - 1 ? 80 : 0);
                    }
                    //UI test
                    TextView tvTitle = holder.getView(R.id.tv_title);
                    tvTitle.setTextSize(16);
                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) tvTitle.getLayoutParams();
                    params.topMargin = CommonTools.dp2px(10);
                    params.bottomMargin = CommonTools.dp2px(16);
                }

                @Override
                protected void convert(@NonNull AdapterViewHolder helper, UpSellListData item) {
                    int index = mData.indexOf(item);
                    helper.setVisibleCompat(R.id.v_place, index != getItemCount() - 1);
                    convertUpsellHorizontal(helper, item.t, index);
                }

                @Override
                protected void convertPayloads(@NonNull AdapterViewHolder helper, UpSellListData item, @NonNull List<Object> payloads) {
                    super.convertPayloads(helper, item, payloads);
                    for (Object payload : payloads) {
                        if (payload instanceof UpsellMoreData) {
                            UpsellMoreData moreData = (UpsellMoreData) payload;
                            RecyclerView rvList = helper.getView(R.id.rv_list);
                            RecyclerView.Adapter<?> adapter = rvList.getAdapter();
                            if (adapter instanceof UpSellItemAdapter) {
                                UpSellItemAdapter upSellItemAdapter = (UpSellItemAdapter) adapter;
                                if (moreData.isEmptyList()) {
                                    upSellItemAdapter.loadMoreEnd(true);
                                    item.t.view_more_link = null;
                                } else {
                                    upSellItemAdapter.loadMoreComplete();
                                    upSellItemAdapter.addData(moreData.products);
                                    item.t.items.addAll(moreData.products);
                                }
                            }
                        } else if (ProductTraceViewHelper.shouldConvertPayload(payload)) {
                            RecyclerView rvList = helper.getView(R.id.rv_list);
                            ProductTraceViewHelper.notify(rvList);
                        }
                    }
                }
            });
        }
    }

    protected void convertUpsellHorizontal(AdapterViewHolder helper, UpSellBean.UpSellListBean item, int index) {
        helper.setText(R.id.tv_title, item.title);
        RecyclerView rvList = helper.getView(R.id.rv_list);
        RecyclerView.Adapter<?> rvAdapter = rvList.getAdapter();
        UpSellItemAdapter adapter;
        if (rvAdapter instanceof UpSellItemAdapter) {
            adapter = (UpSellItemAdapter) rvAdapter;
        } else {
            rvList.setLayoutManager(new LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false));
            adapter = new UpSellItemAdapter(new ArrayList<>());
            rvList.setAdapter(adapter);
        }
        List<Object> data = new ArrayList<>(item.items);
        adapter.setNewData(data);
        adapter.setModInfo(item.type, index);
        adapter.setToProductDetailsListener(new UpSellItemAdapter.OnToProductDetailsListener() {
                    @Override
                    public void toDetails(ProductBean bean) {
                        if (!TextUtils.isEmpty(bean.view_link)) {
                            //view_link 不为空，表示允许进入pdp
                            List<ProductBean> items = item.items;
                            if (!EmptyUtils.isEmpty(items)) {
                                //eagle track click
                                EagleTrackManger.get().trackEagleClickAction(item.type,
                                        index,
                                        null,
                                        -1,
                                        String.valueOf(bean.id),
                                        item.items.indexOf(bean),
                                        EagleTrackEvent.TargetType.PRODUCT,
                                        EagleTrackEvent.ClickType.VIEW,
                                        null,
                                        null,
                                        bean.isSeller());
                            }
                            startActivity(ProductIntentCreator.getIntent(activity, bean, true));
                        }
                    }
                })
                .setOnProductOperateListener(new UpSellItemAdapter.OnProductOperateListener() {
                    @Override
                    public void onOperate(ProductBean bean, boolean isAdd) {
                        if (!list.contains(bean)) {
                            list.add(bean);
                        }
                        calcOrderAmount();
                    }
                })
                .setAdapterModule(item.type)
                .setProductSource(item.type);
        rvList.clearOnScrollListeners();
        rvList.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent(recyclerView);
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });
        adapter.setAttachView(rvList);
        childAdapterCaches.add(new SoftReference<>(adapter));
        //横向load more
        if (item.hasMoreLink()) {
            adapter.setLoadMoreView(new HorizontalLoadingMoreView());
            adapter.setOnLoadMoreListener(() -> {
                item.offset = item.offset + item.getLimit();
                viewModel.loadMore(item.type, item.getMoreLink(), item.offset, index);
            }, mRecyclerView);
        } else {
            adapter.setEnableLoadMore(false);
        }
    }

    private void calcOrderAmount() {
        total = amount;
        if (!EmptyUtils.isEmpty(list)) {
            for (ProductBean bean : list) {
                SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(bean.id, bean.product_key);
                int num = item != null ? item.quantity : 0;
                double price = OrderHelper.isVip() && bean.show_member_price ? bean.member_price : bean.price;
                total = DecimalTools.add(total, DecimalTools.multiply(num, price));
            }
        }
        tvAmount.setText(OrderHelper.formatUSMoney(total));
    }

    private void onPageResumeImpression(boolean isResume) {
        if (childAdapterCaches != null && !childAdapterCaches.isEmpty()) {
            List<SoftReference<BaseQuickAdapter<?, ?>>> invalids = new ArrayList<>();
            for (SoftReference<BaseQuickAdapter<?, ?>> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter<?, ?> adapter = reference.get();
                    if (adapter != null) {
                        RecyclerView attachView = null;
                        if (adapter instanceof ImpressionChild) {
                            attachView = ((ImpressionChild) adapter).getAttachView();
                        }
                        if (attachView != null) {
                            if (isResume) {
                                eagleImpressionTracker.onPageResume(attachView);
                            } else {
                                eagleImpressionTracker.onPagePause(attachView);
                            }
                        }
                    } else {
                        invalids.add(reference);
                    }
                }
            }
            if (!invalids.isEmpty()) {
                childAdapterCaches.removeAll(invalids);
            }
        }
    }

    private void onPageImpressionTrigger() {
        if (childAdapterCaches != null && !childAdapterCaches.isEmpty()) {
            for (SoftReference<BaseQuickAdapter<?, ?>> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter<?, ?> adapter = reference.get();
                    if (adapter != null) {
                        RecyclerView attachView = null;
                        if (adapter instanceof ImpressionChild) {
                            attachView = ((ImpressionChild) adapter).getAttachView();
                        }
                        if (attachView != null) {
                            eagleImpressionTracker.trackImpression(attachView);
                        }
                    }
                }
            }
        }
    }

    private void reportImpressionEvent(RecyclerView view) {
        if (eagleImpressionTracker != null && view != null && view.getVisibility() == View.VISIBLE) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    eagleImpressionTracker.trackImpression(view);
                }
            });
        }
    }

    @Override
    public void onApplyToasterOptions(@NonNull IToaster<? extends IToasterOptions> toaster, @NonNull IToasterOptions options) {
        if (toaster.getType() == Toaster.TYPE_SNACK_BAR) {
            options.setContentMarginBottom(CommonTools.dp2px(72));
        }
    }
}
