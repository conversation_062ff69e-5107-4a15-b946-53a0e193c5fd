package com.sayweee.weee.module.mkpl.provider.data;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsBackgroundStyle;
import com.sayweee.weee.module.cms.bean.CmsProperty;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.cms.iml.product.data.ProductListBean;
import com.sayweee.weee.module.cms.iml.title.data.CmsTitleData;
import com.sayweee.weee.module.cms.iml.title.data.CmsTitleProperty;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

//
// Created by Thomsen on 17/10/2023.
// Copyright (c) 2023 Weee LLC. All rights reserved.
//
public class CmsProductSingleData extends ComponentData<ProductListBean, CmsProperty>
        implements ProductTraceTask.SectionProvider {

    public CmsProductSingleData() {
        super(CmsItemType.PRODUCT_SINGLE);
    }

    @Override
    public boolean isValid() {
        if (filter) {
            return t != null && ProductHelper.filterReachLimitValid(t.products, 0);
        } else {
            return t != null && CollectionUtils.isNotEmpty(t.products);
        }
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        List<AdapterDataType> list = new ArrayList<>();
        if (isValid()) {
            CmsBackgroundStyle backgroundStyle = null;
            if (property != null && !EmptyUtils.isEmpty(property.background)) {
                backgroundStyle = JsonUtils.parseObject(property.background, CmsBackgroundStyle.class);
            }
            CmsTitleProperty titleProperty = CmsTitleProperty.from(getProperty());
            if (titleProperty != null) {
                CmsTitleData titleData = new CmsTitleData();
                titleData.setProperty(titleProperty);
                titleData.position = position;
                titleData.setPageTarget(pageTarget);
                if (!EmptyUtils.isEmpty(titleData.getTitle())) {
                    titleData.property.more_link = null;
                    list.add(titleData);
                }
            }

            ProductItemData data = new ProductItemData(CmsItemType.PRODUCT_SINGLE, t.products.get(0));
            data.setModNm(getEventKey());
            data.setModPos(position);
            data.setProdPos(0);
            data.setPageTarget(pageTarget);
            data.setBackgroundStyle(backgroundStyle);
            data.setProductSource(ProductHelper.getAddCartSource(getEventKey(), getPageTarget()));
            list.add(data);
        }
        return list;
    }

    @Override
    public String getEventKey() {
        if (property != null && !EmptyUtils.isEmpty(property.event_key)) {
            return property.event_key;
        }
        return getComponentKey();
    }

    // =========================================
    // Implementation of ProductSalesTraceTask.SectionProvider interface
    // =========================================
    @Override
    public void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder) {
        if (isValid()) {
            ProductBean product = CollectionUtils.firstOrNull(t.products);
            if (product != null) {
                builder.add(product.recommendation_trace_id, product.id);
            }
            builder.setModNm(getEventKey());
            builder.setUniqueKey(ProductTraceKey.generateUniqueKey(getEventKey(), null));
        }
    }
}
