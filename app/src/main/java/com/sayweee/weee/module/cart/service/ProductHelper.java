package com.sayweee.weee.module.cart.service;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * Author:  winds
 * Date:    2021/9/1.
 * Desc:
 */
public final class ProductHelper {

    private ProductHelper() {

    }

    public static String getAddCartSource(String modNm) {
        return getAddCartSource(WeeeAnalytics.get().getPageKey(), modNm, null);
    }

    public static String getAddCartSource(String modNm, String pageTarget) {
        return getAddCartSource(WeeeAnalytics.get().getPageKey(), modNm, pageTarget);
    }

    public static String getAddCartSource(String pageKey, String modNm, String pageTarget) {
        return pageKey + "-" + modNm + "-" + ((pageTarget != null && pageTarget.length() > 0) ? pageTarget : "null");
    }

    public static boolean isSoldOut(String status) {
        return Constants.ProductStatus.SOLD_OUT.equalsIgnoreCase(status);
    }

    public static boolean isChangeOtherDay(String status) {
        return Constants.ProductStatus.CHANGE_OTHER_DAY.equalsIgnoreCase(status);
    }

    public static boolean isPreSell(String status) {
        return Constants.ProductStatus.PRE_SELL.equalsIgnoreCase(status);
    }

    public static boolean isReachLimit(String status) {
        return Constants.ProductStatus.REACH_LIMIT.equalsIgnoreCase(status);
    }

    public static boolean isSpecialProduct(ProductBean product) {
        return product != null &&
                (Constants.ProductType.BUNDLE.equals(product.category)
                        || Constants.ProductType.VALUE_HOT_DISH.equals(product.is_hotdish)
                        || Constants.ProductStatus.PRE_SELL.equalsIgnoreCase(product.sold_status)
                );
    }

    public static boolean isProductValid(String status) {
        return !isReachLimit(status)
                && !isChangeOtherDay(status)
                && !isSoldOut(status);
    }

    public static void toProductDetail(Context context, ProductBean product) {
        if (isSpecialProduct(product)) {
            context.startActivity(WebViewActivity.getIntent(context, product.view_link));
        } else {
            context.startActivity(ProductIntentCreator.getIntent(context, product));
        }
    }

    @NonNull
    public static SimpleProductBean simplify(@NonNull ProductBean product) {
        SimpleProductBean bean = new SimpleProductBean();
        bean.id = product.id;
        bean.product_key = product.product_key;
        bean.name = product.name;
        bean.img = product.img;
        if (product.img_urls != null && !product.img_urls.isEmpty()) {
            bean.img_urls = new ArrayList<>(product.img_urls);
        } else {
            bean.img_urls = null;
        }
        bean.price = product.price;
        bean.base_price = product.base_price;
        bean.category = product.category;
        bean.is_hotdish = product.is_hotdish;
        bean.sold_status = product.sold_status;
        bean.view_link = product.view_link;

        bean.volume_price_support = product.volume_price_support;
        bean.volume_price = product.volume_price;
        bean.volume_threshold = product.volume_threshold;

        bean.biz_type = product.biz_type;
        bean.vender_info_view = product.vender_info_view;
        bean.product_tag_list = product.product_tag_list;
        //for name label
        bean.is_pantry = product.is_pantry;
        bean.is_colding_package = product.is_colding_package;

        return bean;
    }

    @NonNull
    public static List<SimpleProductBean> simplifyList(@Nullable Collection<ProductBean> productList) {
        if (productList == null || productList.isEmpty()) {
            return Collections.emptyList();
        }
        List<SimpleProductBean> newList = new ArrayList<>(productList.size());
        for (ProductBean product : productList) {
            if (product != null) {
                newList.add(simplify(product));
            }
        }
        return newList;
    }

    public static boolean filterReachLimitValid(List<? extends ProductBean> list, int minSize) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        Iterator<? extends ProductBean> iterator = list.iterator();
        while (iterator.hasNext()) {
            ProductBean bean = iterator.next();
            boolean isReachLimit = OrderManager.get().isReachLimit(bean);
            if (isReachLimit) {
                iterator.remove();
            }
        }
        return list.size() > minSize;
    }

}
