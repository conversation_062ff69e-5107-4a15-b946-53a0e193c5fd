package com.sayweee.weee.module.home.provider.banner;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.banner.data.BannerListData;
import com.sayweee.weee.module.cms.iml.banner.data.TextArrayData;
import com.sayweee.weee.module.cms.iml.banner.data.VerticalBannerData;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.HomeBannerData;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.bean.MainBannerBean;
import com.sayweee.weee.module.home.bean.TextArrayBean;
import com.sayweee.weee.module.home.bean.adapter.BannerHelper;
import com.sayweee.weee.module.home.provider.banner.data.CmsLayoutLarData;
import com.sayweee.weee.module.mkpl.view.IndicatorDotView;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.weee.widget.banner.ex.ExBannerLayoutProvider;
import com.sayweee.weee.widget.banner.ex.ExCarouselBanner;
import com.sayweee.weee.widget.banner.ex.PlayerTriggerAdapter;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.youth.banner.Banner;
import com.youth.banner.listener.OnBannerListener;
import com.youth.banner.listener.OnPageChangeListener;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CmsLayoutLarProvider extends SimpleSectionProvider<CmsLayoutLarData, AdapterViewHolder> implements ImpressionProvider<CmsLayoutLarData>, PlayerTriggerAdapter {
    public int lastBannerIndex;
    public SoftReference<ExCarouselBanner> softBanner;

    @Override
    public int getItemType() {
        return CmsItemType.LAYOUT_LAR;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_layout_lar;
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
        if (holder.getItemViewType() == getItemType()) {
            View view1 = holder.getView(R.id.banner_vertical);
            bannerStart(view1);
            View view2 = holder.getView(R.id.banner_text_array);
            bannerStart(view2);
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull AdapterViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        if (holder.getItemViewType() == getItemType()) {
            View view1 = holder.getView(R.id.banner_vertical);
            bannerStop(view1);
            View view2 = holder.getView(R.id.banner_text_array);
            bannerStop(view2);
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsLayoutLarData item) {
        LinearLayout layoutLar = helper.getView(R.id.layout_lar);
        ViewGroup.LayoutParams layoutParams = layoutLar.getLayoutParams();
        int windowWidth = CommonTools.getWindowWidth(context);
        double width = DecimalTools.divide(windowWidth * 1.0 - CommonTools.dp2px(52), 2);
        double height = DecimalTools.multiply(width, DecimalTools.divide(283, 169));
        layoutParams.height = (int) height;
        layoutLar.removeAllViews();

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams((int) width, ViewGroup.LayoutParams.MATCH_PARENT);
        for (int i = 0, size = item.getItems().size(); i < size; i++) {
            if (i == 1) {
                layoutLar.addView(ViewTools.getHelperView(layoutLar, R.layout.item_margin, null));
            }
            ComponentData subItem = item.getItems().get(i);
            if (subItem instanceof VerticalBannerData) {
                //VerticalBanner 图片轮播
                covertVerticalBanner(layoutLar, params, (VerticalBannerData) subItem, item);
            } else if (subItem instanceof BannerListData) {
                //BannerList
                covertBannerList(layoutLar, height, params, (BannerListData) subItem, item);
            } else if (subItem instanceof TextArrayData) {
                //TextArray 文字轮播
                covertTextArray(layoutLar, params, (TextArrayData) subItem, height, item);
            }
        }
    }

    private void covertVerticalBanner(LinearLayout layoutLar, LinearLayout.LayoutParams params, VerticalBannerData subItem, CmsLayoutLarData item) {
        layoutLar.addView(ViewTools.getHelperView(layoutLar, R.layout.item_vertical_banner, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                ExCarouselBanner banner = helper.getView(R.id.banner_vertical);
                softBanner = new SoftReference<>(banner);
                List<HomeBannerData> list = subItem.t;
                int count = list.size();
                boolean isLoop = false;
                boolean isAutoScroll = false;
                banner.setBannerGalleryEffect(0, 0, 0, 1);
                if (count > 1) {
                    isLoop = subItem.loop;
                    isAutoScroll = subItem.autoplay;
                    if (isLoop) {
                        banner.setStartPosition(lastBannerIndex + 1);
                    }
                }
                banner.setTag(isAutoScroll);
                banner.isAutoLoop(isAutoScroll);
                //V3版本显示指示器
                IndicatorDotView indicatorDot = helper.getView(R.id.indicator_dot);
                boolean isShowIndicator = count > 1;
                helper.setVisible(R.id.indicator_dot, isShowIndicator);
                if (isShowIndicator) {
                    banner.removeIndicator();
                    indicatorDot.setSize(8, 5);
                    indicatorDot.setBgRes(R.drawable.banner_indicator_dot_with_stroke);
                    indicatorDot.setCount(count);
                }
                banner.setBannerData(list, isLoop, new ExBannerLayoutProvider.Factory(
                        R.layout.item_carousel_banner_lar_image,
                        R.layout.item_carousel_banner_lar_video
                ));
                banner.setOnBannerListener(new OnBannerListener<Object>() {
                    @Override
                    public void OnBannerClick(Object data, int position) {
                        if (list.size() > position) {
                            MainBannerBean.CarouselBean bean = list.get(position).t;
                            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                    .setMod_nm(item.getEventKey())
                                    .setMod_pos(item.position)
                                    .setSec_nm(subItem.getEventKey())
                                    .setSec_pos(subItem.position)
                                    .setTargetNm(bean.id)
                                    .setTargetPos(position)
                                    .setTargetType(bean.media_type)
                                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                                    .setUrl(bean.detail)
                                    .build().getParams());
                            context.startActivity(WebViewActivity.getIntent(context, bean.detail));
                        }
                    }
                });
                banner.addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
                    @Override
                    public void onPageSelected(int position, boolean isAuto) {
                        if (isShowIndicator) {
                            indicatorDot.setSelectedIndex(position);
                        }
                        if (list.size() > position) {
                            MainBannerBean.CarouselBean bean = list.get(position).t;
                            lastBannerIndex = position;
                            trackEagleBannerImp(item.getEventKey()
                                    , item.position
                                    , subItem.getEventKey()
                                    , subItem.position
                                    , CommonTools.parseInt(bean.id, 0)
                                    , null
                                    , position
                                    , bean.media_type
                                    , bean.detail
                                    , subItem.getEventKey() + bean.detail);
                        }
                    }
                });
                if (!isLoop && lastBannerIndex > 0 && count > lastBannerIndex) {
                    banner.setCurrentItem(lastBannerIndex, false);
                }
            }
        }), params);
    }

    private void covertBannerList(LinearLayout layoutLar, double height, LinearLayout.LayoutParams params, BannerListData subItem, CmsLayoutLarData item) {
        double bannerHeight = DecimalTools.divide((height - CommonTools.dp2px(28)), 3);
        layoutLar.addView(ViewTools.getHelperView(layoutLar, R.layout.item_banner_list, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                covertBanner(helper.getView(R.id.iv_imga), 0, bannerHeight, subItem, item);
                covertBanner(helper.getView(R.id.iv_imgb), 1, bannerHeight, subItem, item);
                covertBanner(helper.getView(R.id.iv_imgc), 2, bannerHeight, subItem, item);
            }
        }), params);
    }

    private void covertBanner(ImageView imageView, int position, double bannerHeight, BannerListData subItem, CmsLayoutLarData item) {
        MainBannerBean.CarouselBean carouselBean = subItem.t.get(position).t;
        ViewTools.updateViewSize(imageView, ViewGroup.LayoutParams.MATCH_PARENT, (int) bannerHeight);
        ImageLoader.load(context, imageView, WebpManager.get().getConvertUrl(ImageSpec.SPEC_375_AUTO, carouselBean.img_url), R.mipmap.iv_banner_placeholder_375);
        imageView.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(item.getEventKey())
                        .setMod_pos(item.position)
                        .setSec_nm(subItem.getEventKey())
                        .setSec_pos(subItem.position)
                        .setTargetNm(String.valueOf(carouselBean.id))
                        .setTargetPos(position)
                        .setTargetType(carouselBean.media_type)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .setUrl(carouselBean.detail)
                        .build().getParams());
                context.startActivity(WebViewActivity.getIntent(context, carouselBean.detail));
            }
        });
    }

    private void covertTextArray(LinearLayout layoutLar, LinearLayout.LayoutParams params, TextArrayData subItem, double height, CmsLayoutLarData item) {
        layoutLar.addView(ViewTools.getHelperView(layoutLar, R.layout.item_text_array, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                TextArrayAdapter adapter = new TextArrayAdapter();
                adapter.setContext(context);
                adapter.setLayoutHeight(height);
                adapter.setOnTextClickListener(new TextArrayAdapter.OnTextClickListener() {
                    @Override
                    public void onTextClick(TextArrayBean.Hots hot, String key) {
                        Map<String, Object> ctx = new EagleContext().setTagId(key).asMap();
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(item.getEventKey())
                                .setMod_pos(item.position)
                                .setSec_nm(subItem.getEventKey())
                                .setSec_pos(subItem.position)
                                .setTargetNm(hot.keyword)
                                .setTargetType(EagleTrackEvent.BannerType.HOT_KEYWORD)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setUrl(hot.jump_url)
                                .addCtx(ctx)
                                .build().getParams());
                        context.startActivity(WebViewActivity.getIntent(context, hot.jump_url));
                    }
                });
                Banner banner = helper.getView(R.id.banner_text_array);
                banner.setTag(true);
                banner.setLoopTime(BannerHelper.getBannerLoopInterval());
                IndicatorDotView indicatorDot = helper.getView(R.id.indicator_dot);
                banner.setAdapter(adapter);
                banner.addOnPageChangeListener(new OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                    }

                    @Override
                    public void onPageSelected(int position) {
                        indicatorDot.setSelectedIndex(position);
                        banner.getAdapter().notifyItemChanged(position);
                        if (subItem.t.size() > position) {
                            TextArrayBean bean = subItem.t.get(position);
                            if (bean != null) {
                                trackEagleBannerImp(item.getEventKey()
                                        , item.position
                                        , subItem.getEventKey()
                                        , subItem.position
                                        , -1
                                        , bean.key
                                        , position
                                        , EagleTrackEvent.BannerType.HOT_KEYWORD
                                        , null
                                        , subItem.getEventKey() + bean.key);
                            }
                        }
                    }

                    @Override
                    public void onPageScrollStateChanged(int state) {

                    }
                });
                indicatorDot.setMaxDotSize(10);
                indicatorDot.setCount(subItem.t.size());
                adapter.setDatas(subItem.t);
            }
        }), params);
    }

    private void trackEagleBannerImp(String mod_nm, int mod_pos, String sec_nm, int sec_pos, int banner_id, String banner_key, int banner_pos, String banner_type, String url, String value) {
        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_HOME, value)) {
            AppAnalytics.logBannerImp(new EagleTrackModel.Builder()
                    .setMod_nm(mod_nm)
                    .setMod_pos(mod_pos)
                    .setSec_nm(sec_nm)
                    .setSec_pos(sec_pos)
                    .setBanner_id(banner_id)
                    .setBanner_key(banner_key)
                    .setBanner_pos(banner_pos)
                    .setBanner_type(banner_type)
                    .setUrl(url)
                    .build().getParams());
            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_HOME, value);
        }
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(CmsLayoutLarData item, int position) {
        List<ImpressionBean> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(item.items)) {
            ComponentData subItem = CollectionUtils.firstOrNull(item.items, t -> t instanceof BannerListData);
            if (subItem instanceof BannerListData) {
                BannerListData bannerListBean = (BannerListData) subItem;
                for (int i = 0; i < bannerListBean.t.size(); i++) {
                    HomeBannerData carouselBean = bannerListBean.t.get(i);
                    Map<String, Object> params = new EagleTrackModel.Builder()
                            .setMod_nm(item.getEventKey())
                            .setMod_pos(item.position)
                            .setSec_nm(bannerListBean.getEventKey())
                            .setSec_pos(bannerListBean.position)
                            .setBannerId(carouselBean.t.id)
                            .setBanner_type(carouselBean.t.media_type)
                            .setBanner_pos(i)
                            .setUrl(carouselBean.t.detail)
                            .build()
                            .getParams();
                    list.add(new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, position + bannerListBean.getEventKey() + carouselBean.t.id));
                }
            }
        }
        return list;
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, CmsLayoutLarData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        Object o = CollectionUtils.getOrNull(payloads, 0);
        if (o instanceof PlayerPayload) {
            int status = ((PlayerPayload) o).status;
            notifyItemPlayByPosition(0, 0, status);
        }
    }

    @Override
    public void notifyItemPlayByPosition(int start, int end, int status) {
        if (softBanner != null) {
            ExCarouselBanner banner = softBanner.get();
            if (banner != null) {
                banner.notifyLifecycleChanged(status);
            }
        }
    }

    private void bannerStart(View view) {
        if (view instanceof Banner) {
            Object tag = view.getTag();
            if (tag instanceof Boolean && ((Boolean) tag)) {
                ((Banner) view).setLoopTime(BannerHelper.getBannerLoopInterval());
                ((Banner) view).start();
            }
        }
    }

    private void bannerStop(View view) {
        if (view instanceof Banner) {
            ((Banner) view).stop();
        }
    }
}
