package com.sayweee.weee.module.launch;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.gyf.immersionbar.ImmersionBar;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.account.LoginMethodActivity;
import com.sayweee.weee.module.account.service.AccountHelper;
import com.sayweee.weee.module.launch.bean.LanguageBean;
import com.sayweee.weee.module.launch.service.LaunchViewModel;
import com.sayweee.weee.module.launch.service.OnboardingHelper;
import com.sayweee.weee.module.launch.service.ReferrerManager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.player.GuideVideoPlayer;
import com.sayweee.weee.player.bean.MediaData;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.listener.OnViewHelper;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

public class NewGuideActivity extends WrapperMvvmActivity<LaunchViewModel> {

    private ImageView ivOnboarding;
    private TextView tvLanguage;
    private GuideVideoPlayer videoView;

    public static Intent getIntent(Context context) {
        return getIntent(context, null);
    }

    public static Intent getIntent(Context context, LanguageBean language) {
        return new Intent(context, NewGuideActivity.class)
                .putExtra("language", language);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void initStatusBar() {
        ImmersionBar.with(this).statusBarView(R.id.v_status)
                .statusBarDarkFont(true)
                .navigationBarDarkIcon(true)
                .transparentNavigationBar()
                .navigationBarColorInt(Color.TRANSPARENT, 0.01f)
                .init();
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_new_guide;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        videoView = findViewById(R.id.video);
        videoView.setRadius(0);
        ivOnboarding = findViewById(R.id.iv_onboarding);
        tvLanguage = findViewById(R.id.tv_language);

        setLanguageChooser();
        setOnBoardingView();
        setOnClickListener(R.id.tv_commit, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                startActivityForResult(LoginMethodActivity.getIntent(activity), 100);
            }
        });
    }

    @Override
    public void loadData() {
        ReferrerManager.get().onTokenAchieve();
        OnboardingHelper.get().setInOnboarding(true);

        String url = "android.resource://" + activity.getPackageName() + "/" + R.raw.guide_video;
        videoView.startVideo(new MediaData<String>() {
            @Override
            public int autoplay() {
                return -1;
            }

            @Override
            public String getImagePath() {
                return null;
            }

            @Override
            public String getVideoPath() {
                return url;
            }
        });
    }

    @Override
    public void attachModel() {
        SharedViewModel.get().loginStatusData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean flag) {
                if (flag != null && flag) {
                    finish();
                }
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        videoView.onVideoResume(true);
    }

    @Override
    protected void onPause() {
        super.onPause();
        getView().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isDestroyed() || isFinishing()) {
                    return;
                }
                if (videoView.isPlaying()) {
                    videoView.onVideoPause();
                }
            }
        }, 400);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AccountHelper.putActivity(activity);
    }

    @Override
    protected void onDestroy() {
        videoView.onVideoRelease();
        AccountHelper.removeActivity(activity);
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            finish();
        }
    }

    private void setOnBoardingView() {
        String language = LanguageManager.get().getLanguage();
        switch (language) {
            case LanguageManager.Language.CHINESE:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_zh);
                break;
            case LanguageManager.Language.TRADITIONAL_CHINESE:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_zh_hant);
                break;
            case LanguageManager.Language.JAPANESE:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_ja);
                break;
            case LanguageManager.Language.KOREAN:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_ko);
                break;
            case LanguageManager.Language.SPANISH:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_es);
                break;
            case LanguageManager.Language.VIETNAMESE:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_vi);
                break;
            case LanguageManager.Language.PORTUGUESE:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_pt);
                break;
            case LanguageManager.Language.THAI:
                ivOnboarding.setImageResource(R.mipmap.onboarding_introduce_th);
                break;
            case LanguageManager.Language.ENGLISH:
            default:
                ivOnboarding.setImageResource(R.mipmap.onboarding_en);
                break;
        }
    }

    private void setLanguageChooser() {
        LanguageBean language = getExtraLanguage();
        if (language.isEmpty()) {
            return;
        }

        View layoutLanguage = findViewById(R.id.layout_language);
        layoutLanguage.setVisibility(View.VISIBLE);
        layoutLanguage.setOnClickListener(new OnSafeClickListener(800) {
            @Override
            public void onClickSafely(View v) {
                showLanguagePopup(language);
                EagleTrackManger.get().trackEagleClickAction(
                        /* targetNm= */LanguageManager.get().getLanguage(),
                        /* targetPos= */0,
                        /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                        /* clickType= */EagleTrackEvent.ClickType.VIEW
                );
            }
        });

        String enLabel = getEnLabelByKey(language, LanguageManager.get().getLanguage());
        if (tvLanguage != null) {
            tvLanguage.setText(enLabel);
        }
        TalkBackHelper.setContentDesc(layoutLanguage, getString(R.string.a_switch_lang_with_current, enLabel));
    }

    private void showLanguagePopup(LanguageBean language) {
        if (tvLanguage == null) {
            return;
        }

        final Collection<LanguageBean.Lang> supportedLanguages;
        supportedLanguages = CollectionUtils.orEmpty(language.language);

        new WrapperDialog(activity, R.style.BottomDialogTheme) {

            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_guide_language;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }

            @Override
            public void help(ViewHelper helper) {
                LinearLayout layout = helper.getView(R.id.layout_language);
                ViewTools.removeAllViews(layout);
                for (LanguageBean.Lang lang : supportedLanguages) {
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                            LinearLayout.LayoutParams.MATCH_PARENT,
                            CommonTools.dp2px(58)
                    );
                    layout.addView(ViewTools.getHelperView(layout, R.layout.item_guide_language, new OnViewHelper() {
                        @Override
                        public void help(ViewHelper helper) {
                            helper.setText(R.id.tv_name, getDisplayLabel(lang));
                            helper.itemView.setOnClickListener(new OnSafeClickListener() {
                                @Override
                                public void onClickSafely(View v) {
                                    LanguageManager.get().changeLanguage(activity, lang.key);
                                    dismiss();
                                    startActivity(getIntent(activity, language));
                                    overridePendingTransition(R.anim.activity_alpha_in, R.anim.activity_alpha_out);
                                }
                            });
                        }
                    }), params);
                }

                helper.setOnClickListener(R.id.tv_close, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dismiss();
                    }
                });
            }
        }.show();
    }

    @NonNull
    private LanguageBean getExtraLanguage() {
        LanguageBean extraLang = new LanguageBean();
        extraLang.language = new ArrayList<>();
        Intent intent = getIntent();
        if (getIntent() != null) {
            Serializable serializable = intent.getSerializableExtra("language");
            if (serializable instanceof LanguageBean) {
                LanguageBean serverLang = (LanguageBean) serializable;
                for (LanguageBean.Lang lang : serverLang.language) {
                    if (!AppFilter.LangConfig.isNotSupport(lang.key)) {
                        extraLang.language.add(lang);
                    }
                }
            }
        }
        return extraLang;
    }

    private String getDisplayLabel(@NonNull LanguageBean.Lang lang) {
        if (LanguageManager.Language.ENGLISH.equals(lang.key)) {
            return lang.label_en;
        }
        return lang.label_en + " (" + lang.label + ")";
    }

    private String getEnLabelByKey(@NonNull LanguageBean language, String tag) {
        String label = null;
        for (LanguageBean.Lang lang : CollectionUtils.orEmpty(language.language)) {
            if (lang.key != null && lang.key.equals(tag)) {
                label = lang.label_en;
                break;
            }
        }
        // Fallback to app defined language
        if (label == null || label.isEmpty()) {
            int langRes = LanguageManager.get().getLanguageEnStringRes(null);
            label = getResources().getString(langRes);
        }
        return label;
    }

}
