package com.sayweee.weee.module.checkout2;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelKt;

import com.sayweee.service.PaymentService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutOrderSummaryData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CheckoutTermsData;
import com.sayweee.weee.module.checkout.bean.CheckoutVipData;
import com.sayweee.weee.module.checkout.bean.DealPayMemberPlanData;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout2.bean.CheckoutV4Bean;
import com.sayweee.weee.module.checkout2.bean.DealPayV2Bean;
import com.sayweee.weee.module.checkout2.data.CheckoutPurchaseChannelData;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.Observer;

public class DealPayViewModel<M extends BaseLoaderModel<OrderApi>> extends AbstractPreCheckoutViewModel<M> {

    static final String ORDER_ABNORMAL = "SO20005"; // SO20005 订单自动取消

    final MutableLiveData<DealPayV2Bean> dealPayV2LiveData = new MutableLiveData<>();
    final MutableLiveData<CheckoutV4Bean> checkoutLiveData = new MutableLiveData<>();

    private static final String PAYMENT_ROUTING = "test";
    private static final String SOURCE = "deal_pay";

    private final DealPayAgent agent = new DealPayAgent();

    public DealPayViewModel(@NonNull Application application) {
        super(application);
    }

    void setCheckoutId(String checkoutId) {
        agent.setCheckoutId(checkoutId);
    }

    public void dealPayV2(boolean showLoading) {
        rxDealPayV2().subscribe(getDealPayV2Observer(showLoading));
    }

    private Observable<ResponseBean<DealPayV2Bean>> rxDealPayV2() {
        RequestParams requestParams = agent.prepareDealPayQueryRequest();
        requestParams.put(KEY_PAYMENT_ROUTING, PAYMENT_ROUTING);
        return getLoader().getHttpService()
                .dealPayV2(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false));
    }

    private Observer<ResponseBean<DealPayV2Bean>> getDealPayV2Observer(boolean showLoading) {
        return new ResponseObserver<ResponseBean<DealPayV2Bean>>() {

            @Override
            public void onBegin() {
                if (showLoading) {
                    setLoadingStatus(true);
                }
            }

            @Override
            public void onResponse(ResponseBean<DealPayV2Bean> response) {
                dealPayV2LiveData.postValue(response.getData());
            }

            @Override
            public void onError(FailureBean failure) {
                super.onError(failure);
                failureData.postValue(failure);
            }

            @Override
            public void onFinish() {
                if (showLoading) {
                    setLoadingStatus(false);
                }
            }
        };
    }

    public void renderAdapterData(@NonNull DealPayV2Bean data) {
        List<AdapterDataType> list = new ArrayList<>();
        // Address
        list.add(new CheckoutDeliveryData(data.address_info, CheckOutSectionAdapter.TYPE_DELIVERY_INFORMATION).setNoEdit(true));

        // Pay with
        CheckoutPurchaseChannelData purchaseChannelData = new CheckoutPurchaseChannelData(
                /* purchaseChannels= */agent.getSupportChannels(),
                /* cvcText= */agent.cvcText,
                /* firstPurchasedAmount= */agent.firstPurchasedAmount,
                /* extraData= */agent.getPurchaseChannelExtraData()
        );
        list.add(purchaseChannelData);

        // Points
        boolean hasPoints = data.use_points_pay && data.point_info != null && data.point_info.points_current > 0;
        if (hasPoints) {
            boolean saveRewardsChecked = data.show_member_plan && data.vip_level > 1;
            boolean isAllCommissionPartner = agent.isAllCommissionPartner();
            CheckoutUsePointsData pointsData = new CheckoutUsePointsData(
                    /* pointInfo= */data.point_info,
                    /* saveRewardsChecked= */saveRewardsChecked,
                    /* isAllCommissionPartner= */isAllCommissionPartner
            );
            list.add(pointsData);
        }

        // Review order
        if (!EmptyUtils.isEmpty(data.order_reviews)) {
            list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_REVIEW_ORDER));
            for (PreCheckoutOrderReviewsBean bean : data.order_reviews) {
                if ("V-point-0".equals(bean.biz_type) && data.show_member_plan) {
                    continue;
                }
                list.add(new CheckoutReviewOrderData(bean, CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST));
            }
        }

        // Summary
        if (!EmptyUtils.isEmpty(data.order_summary)) {
            list.add(new CheckoutOrderSummaryData(data.order_summary, CheckOutSectionAdapter.TYPE_ORDER_SUMMARY).setFeeInfo(data.fee_info));
        }

        //rewards会员升级省更多
        if (data.show_member_plan) {
            String finalAmount = data.fee_info != null ? data.fee_info.final_amount : null;
            list.add(new DealPayMemberPlanData(data.vip_level, finalAmount));
        }

        //vip会员返利
        if (!EmptyUtils.isEmpty(data.point_info) && !EmptyUtils.isEmpty(data.point_info.order_reward_points_desc_v2)) {
            list.add(new CheckoutVipData(data.point_info, CheckOutSectionAdapter.TYPE_VIP_MEMBER));
        }

        // 冷冻保存的文案
        // https://sayweee.atlassian.net/browse/MKPL-14457 DealPay 页面不显示酒类协议
        int alcoholAgreementType = Constants.AlcoholAgreementType.NONE;
        CheckoutTermsData termsData = new CheckoutTermsData(
                /* type= */CheckOutSectionAdapter.TYPE_TERMS,
                /* containFrozen= */data.contain_forzen,
                /* alcoholAgreementType= */alcoholAgreementType
        );
        termsData.showDefaultTerm = false;
        list.add(termsData);

        list.add(new SimpleAdapterDataType(CheckOutSectionAdapter.TYPE_PLACE));

        adapterData.postValue(list);
    }

    /**
     * 结算
     */
    public void checkout() {
        final RequestParams requestParams = agent.prepareEnrollPaymentRequest();
        requestParams.putNonNull("source", SOURCE);
        getLoader().getHttpService()
                .enrollPayPayments(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<CheckoutV4Bean>>() {

                    @Override
                    public void onBegin() {
                        super.onBegin();
                        setLoadingStatus(true);
                    }

                    @Override
                    public void onResponse(ResponseBean<CheckoutV4Bean> response) {
                        checkoutLiveData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        failureData.postValue(failure);
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        setLoadingStatus(false);
                    }
                });
    }

    public void updatePaymentCategory(boolean usePoints, boolean removeEbt) {
        RequestParams requestParams = getAgent()
                .generateChangePaymentCategoryParams(usePoints, removeEbt);
        PaymentService.get().changePaymentMethods(
                ViewModelKt.getViewModelScope(this),
                requestParams.get(),
                (result, failure) -> {
                    if (result != null && result) {
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", SOURCE);
                        EagleTrackManger.get().trackEagleInfoUpdate(
                                EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                true,
                                "weee_points",
                                map
                        );
                        toastLiveData.postValue(R.string.s_checkout_payment_updated);
                    } else {
                        if (failure != null) {
                            showToast(failure.getMessage());
                        }
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("source", SOURCE);
                        EagleTrackManger.get().trackEagleInfoUpdate(
                                EagleTrackEvent.InfoName.PAYMENT_METHOD,
                                usePoints ? EagleTrackEvent.ActionType.SELECT : EagleTrackEvent.ActionType.UNSELECT,
                                false,
                                "weee_points",
                                map
                        );
                    }
                    refreshData();
                });
    }

    public static void showToast(String msg) {
        String toastMsg = !EmptyUtils.isEmpty(msg) ? msg : "Sorry, something went wrong, please try again later.";
        Toaster.showToast(toastMsg);
    }

    public void setCvcText(String cvcText) {
        getAgent().cvcText = cvcText;
    }

    DealPayAgent getAgent() {
        return agent;
    }

    public void setDealPayData(DealPayV2Bean dealPayV2Bean) {
        agent.wrap(dealPayV2Bean);
    }
}
