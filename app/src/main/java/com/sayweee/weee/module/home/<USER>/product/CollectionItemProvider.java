package com.sayweee.weee.module.home.provider.product;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.adapter.ProductItemMoreAdapter;
import com.sayweee.weee.module.home.provider.product.data.CmsCollectionItemData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/5/29.
 * Desc:
 */
public class CollectionItemProvider extends SimpleHorizontalImpressionProvider<CmsCollectionItemData, AdapterViewHolder> {

    protected int productDisplayStyle = ProductView.STYLE_ITEM_SMALL;

    @Override
    public int getItemType() {
        return CmsItemType.COLLECTION_ITEM;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_home_item_line;
    }

    @Override
    public void onPageResumeImpression(BaseQuickAdapter adapter) {
        super.onPageResumeImpression(adapter);
        ProductSyncHelper.onPageResume(adapter);
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        setFullSpan(helper);

        RecyclerView rvList = helper.getView(R.id.rv_list);
        ProductItemMoreAdapter adapter;
        adapter = new ProductItemMoreAdapter(new ArrayList<>(), productDisplayStyle);
        adapter.setAttachView(rvList);
        rvList.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        rvList.setAdapter(adapter);
        rvList.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onPageScrollStateChangedImpression(adapter);
                }
            }
        });
        addAdapterToCache(adapter);
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsCollectionItemData item) {
        String title = item.t.title;
        boolean hasMoreLink = !EmptyUtils.isEmpty(item.t.link_url);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.v_title), !EmptyUtils.isEmpty(title) || hasMoreLink);

        helper.setText(R.id.tv_title, item.t.title);
        helper.setVisibleCompat(R.id.layout_view_all, hasMoreLink);
        Map<String, Object> ctx = new EagleContext().setPageTarget(item.pageTarget).asMap();
        if (hasMoreLink) {
            helper.setOnViewClickListener(R.id.v_title, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    EagleTrackManger.get().trackEagleClickAction(item.getEventKey(), item.position
                            , item.t.key
                            , item.secPos
                            , "explore_more"
                            , -1
                            , null
                            , EagleTrackEvent.ClickType.VIEW
                            , ctx);
                    context.startActivity(WebViewActivity.getIntent(context, item.t.link_url));
                }
            });
        }

        RecyclerView rvList = helper.getView(R.id.rv_list);
        RecyclerView.Adapter<?> a = rvList.getAdapter();
        if (a instanceof ProductItemMoreAdapter) {
            List<ProductBean> list = item.getAllProduct();
            ProductItemMoreAdapter adapter = (ProductItemMoreAdapter) a;
            adapter.pageTarget = item.pageTarget;
            adapter.setProductSource(ProductHelper.getAddCartSource(item.getEventKey(), item.pageTarget));
            adapter.setAdapterModule(item.getEventKey());
            adapter.setModInfo(item.getEventKey(), item.position);
            adapter.setSecInfo(item.t.key, item.secPos);
            adapter.setProductData(list);
            adapter.setMoreData(item.t.total_count > list.size() && item.t.link_url != null, item.t.link_url);
        }
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, CmsCollectionItemData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        for (Object payload : payloads) {
            if (ProductTraceViewHelper.shouldConvertPayload(payload)) {
                RecyclerView recyclerView = helper.getView(R.id.rv_list);
                ProductTraceViewHelper.notify(recyclerView);
            }
        }
    }

    @Override
    public void notifyPageDataSetChanged(RecyclerView view) {
        List<BaseQuickAdapter> list = findAdapterByCache();
        for (BaseQuickAdapter adapter : list) {
            onPageScrollStateChangedImpression(adapter);
        }
    }

    @Override
    public void onPageResume(RecyclerView view) {
        super.onPageResume(view);
        List<BaseQuickAdapter> list = findAdapterByCache();
        for (BaseQuickAdapter adapter : list) {
            ProductSyncHelper.onPageResume(adapter);
        }
    }

    public CollectionItemProvider setProductDisplayStyle(@ProductView.DisplayStyle int productDisplayStyle) {
        this.productDisplayStyle = productDisplayStyle;
        return this;
    }
}
