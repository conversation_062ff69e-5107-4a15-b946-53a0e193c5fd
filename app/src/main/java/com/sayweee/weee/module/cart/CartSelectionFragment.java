package com.sayweee.weee.module.cart;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.LoginPanelActivity;
import com.sayweee.weee.module.cart.adapter.CartSelectionAdapter;
import com.sayweee.weee.module.cart.bean.CartSelectionData;
import com.sayweee.weee.module.cart.bean.CartSelectionTitleData;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.WrapperUpSellBean;
import com.sayweee.weee.module.cart.service.CartSelectionViewModel;
import com.sayweee.weee.module.cart.widget.CheckOutBottomView;
import com.sayweee.weee.module.checkout.SectionAlcoholAgreementActivity;
import com.sayweee.weee.module.checkout.SectionUpsellActivity;
import com.sayweee.weee.module.checkout2.CheckoutSectionActivity;
import com.sayweee.weee.module.post.base.WrapperBottomSheetFragment;
import com.sayweee.weee.module.post.entity.TabEntity;
import com.sayweee.weee.service.VibratorManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleElement;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnItemSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.toast.ToastOptions;

import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CartSelectionFragment extends WrapperBottomSheetFragment<CartSelectionViewModel> {

    View layoutSelectAll;
    ImageView ivSelectAll;
    TextView tvSelectAll;
    RecyclerView recyclerView;
    CartSelectionAdapter adapter;
    TextView tvBanner;
    CheckOutBottomView bottomView;
    private NewPreOrderBean preOrder;
    EagleImpressionTrackerIml eagleImpressionTracker;
    MagicIndicator tabCategory;
    View topShadow;

    public static CartSelectionFragment newInstance(NewPreOrderBean preOrder) {
        CartSelectionFragment fragment = new CartSelectionFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("preOrder", preOrder);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_cart_selection;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        eagleImpressionTracker = new EagleImpressionTrackerIml();
        topShadow = findViewById(R.id.shadow_indicator_panel);
        tabCategory = findViewById(R.id.tab_category);
        layoutSelectAll = findViewById(R.id.layout_select_all);
        ivSelectAll = findViewById(R.id.iv_select_all);
        tvSelectAll = findViewById(R.id.tv_select_all);
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(activity));
        adapter = new CartSelectionAdapter();
        recyclerView.setAdapter(adapter);
        recyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportEagleImpressionEvent();
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                ViewTools.setViewVisibilityIfChanged(topShadow, !RecyclerViewTools.isTopMost(recyclerView));
            }
        });
        adapter.setOnItemClickListener(new OnItemSafeClickListener() {
            @Override
            public void onItemClickSafely(BaseQuickAdapter adapter, View view, int position) {
                handleOnAdapterItemClick(adapter, view, position);
            }
        });
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int position) {
                handleOnAdapterItemClick(adapter, view, position);
            }
        });
        tvBanner = findViewById(R.id.tv_banner);
        bottomView = findViewById(R.id.bottom_view);
        setCanCheckOut(false);
        findViewById(R.id.iv_title_left).setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                finish();
            }
        });
    }

    private void handleOnAdapterItemClick(BaseQuickAdapter adapter, View view, int position) {
        Object item = adapter.getItem(position);
        if (item instanceof CartSelectionData) {
            NewSectionBean section = ((CartSelectionData) item).t;
            if (view.getId() == R.id.btn_checkout_individual) {
                onIndividualCheckoutButtonClick((CartSelectionData) item, position);
            } else if (!section.isIndividualCheckout()) {
                section.selected = !section.selected;
                adapter.notifyItemChanged(position, section);
                vibrate();
                bottomView.loading();
                viewModel.requestSelectCart(section, position);
            }
        }
    }

    @Override
    protected void setDialogDisplayConfig() {
        setDialogParams(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
        getBehavior().setSkipCollapsed(true);
        if (getDialog() != null) {
            getDialog().setCanceledOnTouchOutside(true);
        }
    }

    @Override
    public void loadData() {
        Bundle arguments = getArguments();
        if (arguments == null) {
            return;
        }
        Serializable serializable = arguments.getSerializable("preOrder");
        if (serializable instanceof NewPreOrderBean) {
            preOrder = (NewPreOrderBean) serializable;
            viewModel.initPreOrder(preOrder);
            initTab();
            fillBottom(null);
            ViewTools.setViewOnSafeClickListener(layoutSelectAll, v -> {
                vibrate();
                viewModel.selectAllCart();
                bottomView.loading();
                adapter.selectAllCart();
            });
            onCartTabSelected(CartSelectionViewModel.TAB_TYPE_GROCERY);
        }
    }

    @Override
    public void attachModel() {
        viewModel.tabDataObservable.observe(this, new Observer<CartSelectionViewModel.TabData>() {
            @Override
            public void onChanged(CartSelectionViewModel.TabData tabData) {
                if (TextUtils.equals(tabData.tabType, viewModel.currentTabType)) {
                    fillSelectTitle(tabData.titleData);
                    adapter.setNewData(tabData.adapterData);
                    reportEagleImpressionEvent();

                    boolean inGroceryTab = CartSelectionViewModel.TAB_TYPE_GROCERY.equals(tabData.tabType);
                    if (inGroceryTab) {
                        setCanCheckOut(tabData.enableBtn);
                    }
                }
            }
        });
        viewModel.preOrderData.observe(this, new Observer<NewPreOrderBean>() {
            @Override
            public void onChanged(NewPreOrderBean newPreOrderBean) {
                if (newPreOrderBean != null) {
                    preOrder = newPreOrderBean;
                }
                fillBottom(newPreOrderBean);
            }
        });
        viewModel.upSellData.observe(this, new Observer<WrapperUpSellBean>() {
            @Override
            public void onChanged(WrapperUpSellBean bean) {
                int alcoholAgreementType = AlcoholHelper.getAlcoholAgreementTypeUpsell(preOrder);
                boolean isShowAlcoholAgreement = AlcoholHelper.isShowAlcoholAgreement(alcoholAgreementType);
                if (bean.dispatchUpSell() && preOrder != null) {
                    startActivity(SectionUpsellActivity.getIntent(activity, bean.upSellBean, Double.parseDouble(preOrder.final_amount), isShowAlcoholAgreement));
                } else {
                    gotoCheckoutActivity(alcoholAgreementType);
                }
                finish();
            }
        });
        viewModel.selectSectionObservable.observe(this, section -> {
            if (section != null) {
                int alcoholAgreementType = AlcoholHelper.getAlcoholAgreementType(
                        /* orderSubType= */section.sub_type,
                        /* containsAlcohol= */preOrder != null && preOrder.contain_alcohol,
                        /* isRecordAlcohol= */preOrder != null && preOrder.is_record_alcohol,
                        /* isRecordSellerAlcohol= */preOrder != null && preOrder.is_record_seller_alcohol
                );
                gotoCheckoutActivity(alcoholAgreementType);
                finish();
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        AppAnalytics.logPageView(WeeeEvent.PageView.CART_SELECTION, this);
        eagleImpressionTracker.onPageResume(recyclerView);
    }

    @Override
    public void onPause() {
        super.onPause();
        eagleImpressionTracker.onPagePause(recyclerView);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            View bottomSheet = dialog.findViewById(R.id.design_bottom_sheet);
            bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.MATCH_PARENT;
        }
        final View view = getView();
        if (view != null) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    View parent = (View) view.getParent();
                    CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) (parent).getLayoutParams();
                    CoordinatorLayout.Behavior<?> behavior = params.getBehavior();
                    if (behavior instanceof BottomSheetBehavior) {
                        ((BottomSheetBehavior<?>) behavior).setPeekHeight(view.getMeasuredHeight());
                    }
                }
            });
        }
    }

    private void initTab() {
        if (!viewModel.hasTabTypes) {
            ViewTools.setViewVisibilityIfChanged(tabCategory, false);
            return;
        }

        ViewTools.setViewVisibilityIfChanged(tabCategory, true);
        final List<TabEntity> tabEntities = new ArrayList<>();
        tabEntities.clear();
        tabEntities.add(
                new TabEntity(
                        getString(R.string.s_cart_selection_tab_grocery),
                        R.mipmap.iccmpt_produce_filled_20x20,
                        R.mipmap.iccmpt_produce_filled_20x20
                ).bindData(CartSelectionViewModel.TAB_TYPE_GROCERY)
        );
        tabEntities.add(
                new TabEntity(
                        getString(R.string.s_cart_selection_tab_alcohol),
                        R.mipmap.iccmpt_beverages_filled_20x20,
                        R.mipmap.iccmpt_beverages_filled_20x20
                ).bindData(CartSelectionViewModel.TAB_TYPE_ALCOHOL)
        );

        CommonNavigator navigator = new CommonNavigator(getContext());
        navigator.setAdjustMode(true);
        navigator.setAdapter(new CommonNavigatorAdapter() {

            @Override
            public int getCount() {
                return tabEntities.size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                final TabEntity tab = tabEntities.get(index);

                final CommonPagerTitleView titleView = new CommonPagerTitleView(context);
                final View customLayout = LayoutInflater.from(context).inflate(R.layout.tab_cart_selection_item, titleView, false);

                final ImageView ivTabIcon = customLayout.findViewById(R.id.iv_tab_icon);
                final TextView tvTabName = customLayout.findViewById(R.id.tv_tab_name);
                tvTabName.setText(tab.name);

                titleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {
                    @Override
                    public void onSelected(int index, int totalCount) {
                        customLayout.setBackground(ContextCompat.getDrawable(activity, R.drawable.shape_btn_secondary_bg_radius_90));
                        ivTabIcon.setImageResource(tab.selectedIcon);
                        ViewTools.tintImageView(ivTabIcon, R.color.color_btn_secondary_fg_default);
                        ViewTools.applyTextColor(tvTabName, R.color.color_btn_secondary_fg_default);
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        customLayout.setBackground(null);
                        ivTabIcon.setImageResource(tab.normalIcon);
                        ViewTools.tintImageView(ivTabIcon, R.color.color_btn_disabled_fg_default);
                        ViewTools.applyTextColor(tvTabName, R.color.color_btn_disabled_fg_default);
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {

                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {

                    }
                });
                titleView.setContentView(customLayout);
                titleView.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        onCartTabSelected((String) tab.data);
                        // binding.pager2.setCurrentItem(index);
                    }
                });
                return titleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });
        tabCategory.setNavigator(navigator);
    }

    private void onCartTabSelected(@Nullable String tabType) {
        boolean inGroceryTab = CartSelectionViewModel.TAB_TYPE_GROCERY.equals(tabType);
        if (inGroceryTab) {
            tabCategory.onPageSelected(0);
            viewModel.renderTabData(CartSelectionViewModel.TAB_TYPE_GROCERY);
            updateGroceryBanner(/* isMulti= */true);
        } else if (CartSelectionViewModel.TAB_TYPE_ALCOHOL.equals(tabType)) {
            tabCategory.onPageSelected(1);
            viewModel.renderTabData(CartSelectionViewModel.TAB_TYPE_ALCOHOL);
            updateAlcoholBanner();
        }
        ViewTools.setViewVisibilityIfChanged(layoutSelectAll, inGroceryTab);
        ViewTools.setViewVisibilityIfChanged(bottomView, inGroceryTab);
    }

    private void fillSelectTitle(CartSelectionTitleData titleData) {
        ivSelectAll.setImageResource(titleData.selectCount == titleData.total ? R.drawable.drawable_checkbox_checked : R.drawable.drawable_checkbox_empty_disabled);
        String title = String.format(getString(R.string.s_select_all_carts), titleData.selectCount, titleData.total);
        tvSelectAll.setText(title);
        TalkBackHelper.setContentDescRadio(layoutSelectAll, title, titleData.selectCount == titleData.total);
        int noFreeDeliveryQty = viewModel.getNoFreeDeliveryQty();
        if (titleData.selectCount == titleData.total && noFreeDeliveryQty > 0 && titleData.isClickSelectAll) {//点击title全选才会show
            Context context = getContext();
            if (context != null) {
                ToastOptions options = new ToastOptions();
                options.setContentMarginBottom(CommonTools.dp2px(112));
                Toaster.asToast(context)
                        .setText(String.format(getString(R.string.s_qty_of_the_carts_didnt), noFreeDeliveryQty))
                        .setOptions(options)
                        .build()
                        .show();
            }
        }
    }

    private void fillBottom(NewPreOrderBean preOrder) {
        bottomView.setAmount(preOrder != null ? preOrder.final_amount : "0", preOrder != null ? preOrder.base_final_amount : null)
                .setCouponReminder(preOrder != null ? preOrder.coupon_reminder : null)
                .setAttachedData();
        bottomView.setOnViewClickListener(R.id.tv_checkout, new OnSafeClickListener(1800) {
            @Override
            public void onClickSafely(View v) {
                viewModel.upSell();
                bottomView.loading();
                Map<String, Object> context = new TrackParams()
                        .put("purchase_amount", preOrder != null ? preOrder.final_amount : 0).get();
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(null)
                        .setMod_pos(-1)
                        .setSec_nm(null)
                        .setSec_pos(-1)
                        .setTargetNm(EagleTrackEvent.TargetNm.CHECKOUT)
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.NORMAL)
                        .setClickResult(true)
                        .addCtx(context)
                        .build().getParams());
            }
        });
        bottomView.setOnViewClickListener(R.id.tv_checkout_unable, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //此时checkout按钮不可用
                updateGroceryBanner(/* isMulti= */false);
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(null)
                        .setMod_pos(-1)
                        .setSec_nm(null)
                        .setSec_pos(-1)
                        .setTargetNm(EagleTrackEvent.TargetNm.CHECKOUT)
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.NORMAL)
                        .setClickResult(false)
                        .build().getParams());
            }
        });
    }

    public void setCanCheckOut(boolean enable) {
        bottomView.setCanCheckOut(enable);
        updateGroceryBanner(/* isMulti= */true);
    }

    private void vibrate() {
        VibratorManager.vibrate();
    }

    private void reportEagleImpressionEvent() {
        if (recyclerView != null) {
            recyclerView.post(new Runnable() {
                @Override
                public void run() {
                    if (eagleImpressionTracker != null) {
                        eagleImpressionTracker.trackImpression(recyclerView);
                    }
                }
            });
        }
    }

    private void finish() {
        dismissAllowingStateLoss();
    }

    private void updateGroceryBanner(boolean isMulti) {
        Context context = tvBanner.getContext();
        if (isMulti) {
            tvBanner.setBackground(ContextCompat.getDrawable(context, R.color.color_root_energy_blue_light_2));
            tvBanner.setTextColor(ContextCompat.getColor(context, R.color.color_root_energy_blue_dark_5));
            tvBanner.setText(R.string.s_select_multi);
        } else {
            tvBanner.setBackground(ContextCompat.getDrawable(context, R.color.root_color_red_spectrum_2));
            tvBanner.setTextColor(ContextCompat.getColor(context, R.color.root_color_red_spectrum_15));
            tvBanner.setText(R.string.s_select_none);
        }
    }

    private void updateAlcoholBanner() {
        Context context = tvBanner.getContext();
        tvBanner.setBackground(ContextCompat.getDrawable(context, R.color.color_root_eggplant_purple_light_1));
        tvBanner.setTextColor(ContextCompat.getColor(context, R.color.color_root_eggplant_purple_dark_4));
        tvBanner.setText(R.string.s_cart_selection_tab_alcohol_banner);
    }

    private void onIndividualCheckoutButtonClick(CartSelectionData item, int position) {
        NewSectionBean section = item.t;
        EagleElement eagleElement = new EagleElement();
        Map<String, Object> content = new TrackParams()
                .put("target_nm", EagleTrackEvent.TargetNm.CHECKOUT_CART)
                .put("target_type", EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .put("target_pos", position)
                .put("click_type", EagleTrackEvent.ClickType.VIEW)
                .get();
        Map<String, Object> ctx = new TrackParams()
                .putNonNull("vendor_id", section.getVendorIdInt())
                .get();
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .addElement(eagleElement.asMap())
                .addContent(content)
                .addCtx(ctx)
                .build()
                .getParams()
        );

        section.selected = true;
        vibrate();
        viewModel.requestSelectIndividualCart(section, position);
    }

    private void gotoCheckoutActivity(int alcoholAgreementType) {
        Activity act = getActivity();
        if (act == null) {
            return;
        }
        if (AccountManager.get().isLogin()) {
            Intent a;
            if (AlcoholHelper.isShowAlcoholAgreement(alcoholAgreementType)) {
                a = SectionAlcoholAgreementActivity.getIntent(act, Constants.CartDomain.DOMAIN_GROCERY, alcoholAgreementType);
            } else {
                a = CheckoutSectionActivity.getIntent(act, Constants.CartDomain.DOMAIN_GROCERY, null);
            }
            startActivity(a);
        } else {
            startActivity(LoginPanelActivity.getIntent(act));
        }
    }
}
