package com.sayweee.weee.module.home.provider.category;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.home.PopCategoryFragment;
import com.sayweee.weee.module.home.bean.CategoriesBean;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.launch.service.ExperimentManager;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.BadgeTextView;
import com.sayweee.widget.shape.helper.ShapeHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class CmsCategoryBaseAdapter
        extends BaseQuickAdapter<CategoriesBean, AdapterViewHolder>
        implements EagleImpressionAdapter, ImpressionChild {

    protected static final String FLAG_MORE = "explore_more";

    protected String mod_nm;
    protected int mod_pos;

    protected RecyclerView attachView;
    protected List<CategoriesBean.CategoryListBean> list;

    public CmsCategoryBaseAdapter(int layoutResId) {
        super(layoutResId);
    }

    public void setModInfo(String moduleType, int layoutPosition) {
        this.mod_nm = moduleType;
        this.mod_pos = layoutPosition;
    }

    public abstract void setAdapterData(List<CategoriesBean.CategoryListBean> data, String style, String moreImageUrl);

    protected void fillItem(@NonNull AdapterViewHolder helper, @NonNull View itemView, CategoriesBean.CategoryListBean item) {
        Context context = itemView.getContext();

        // icon
        ImageView ivCategory = itemView.findViewById(R.id.iv_category);
        if (ivCategory != null) {
            ImageLoader.load(
                    context,
                    ivCategory,
                    WebpManager.get().getConvertUrl(SPEC_64, item.thumbnail_img_url),
                    R.drawable.shape_oval_place_size_50
            );
        }

        // title
        TextView tvCategory = itemView.findViewById(R.id.tv_category);
        if (FLAG_MORE.equalsIgnoreCase(item.key)) {
            tvCategory.setText(R.string.s_view_all);
        } else {
            tvCategory.setText(item.getTitle());
        }

        // badge
        boolean displayReward = false;
        View badgeView = itemView.findViewById(R.id.layout_out_reward);
        BadgeTextView tvReward = itemView.findViewById(R.id.tv_reward);
        if (!EmptyUtils.isEmpty(item.category_label)) {
            tvReward.setTextColor(ViewTools.parseColor(item.category_label.label_font_color, Color.WHITE));
            fillBadgeDrawable(tvReward, item.category_label);
            displayReward = item.category_label.label_name != null;
            if (displayReward) {
                String[] split = item.category_label.label_name.split("\n");
                if (split.length == 1) {
                    tvReward.bindData(CollectionUtils.arrayListOf(item.category_label.label_name));
                } else if (split.length > 1) {
                    tvReward.bindData(CollectionUtils.arrayListOf(split));
                    tvReward.startSwitch();
                }
            }
            ViewTools.setViewVisible(badgeView, displayReward);
        } else {
            tvReward.stopSwitch(true);
            ViewTools.setViewVisible(badgeView, displayReward);
        }

        // label new
        View labelView = itemView.findViewById(R.id.tv_label);
        ViewTools.setViewVisible(labelView, item.is_new);

        ViewTools.setViewOnSafeClickListener(itemView, v -> handleItemClick(helper, item));
    }

    protected void fillBadgeDrawable(View badgeView, CategoriesBean.CategoryListBean.CategoryLabel label) {
        ShapeHelper.setBackgroundDrawable(
                badgeView,
                Color.parseColor(label.label_color),
                CommonTools.dp2px(badgeView.getContext(), R.dimen.prop_badge_corner_size, 4f),
                Color.parseColor(label.label_color),
                CommonTools.dp2px(2)
        );
    }

    protected void handleItemClick(@NonNull AdapterViewHolder helper, CategoriesBean.CategoryListBean item) {
        Context context = helper.itemView.getContext();
        boolean isMore = FLAG_MORE.equalsIgnoreCase(item.key);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(mod_nm)
                .setMod_pos(mod_pos)
                .setSec_nm(null)
                .setSec_pos(-1)
                .setTargetNm(item.key)
                .setTargetPos(helper.getLayoutPosition())
                .setTargetType(EagleTrackEvent.BannerType.CATEGORY)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());

        if (isMore) {
            if (context instanceof FragmentActivity && list != null) {
                String tag = PopCategoryFragment.class.getName() + item.key;
                PopCategoryFragment popCategoryFragment = PopCategoryFragment.newInstance(list, mod_pos);
                popCategoryFragment.show(((FragmentActivity) context).getSupportFragmentManager(), tag);
            }
        } else {
            boolean pushNewCategory;
            if (DevConfig.isFlavorLatino()) {
                pushNewCategory = true;
            } else {
                pushNewCategory = ConfigService.get().inExperiment(ExperimentManager.ID_HOME_PUSH_NEW_CATEGORY);
            }
            String url = item.url;
            if (pushNewCategory) {
                Map<String, String> params = new ArrayMap<>();
                params.put(Constants.UrlMapParams.SHOWMODE, Constants.UrlMapParams.NEW_PAGE);
                url = CommonTools.packetUrlParams(url, params);
            }
            context.startActivity(WebViewActivity.getIntent(context, url));
        }
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    @Nullable
    protected ImpressionBean getEagleImpressionEvent(CategoriesBean item) {
        if (item == null) {
            return null;
        }

        CategoriesBean.CategoryListBean bean = item.category_list.get(0);
        int position = mData.indexOf(item);
        String key = position + "_" + bean.num;
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setMod_nm(mod_nm)
                .setMod_pos(mod_pos)
                .setEllipse_label(bean.key)
                .setEllipse_pos(position)
                .setEllipse_type(EagleTrackEvent.BannerType.CATEGORY)
                .setIs_new(bean.is_new)
                .build()
                .getParams();
        return new ImpressionBean(EagleTrackEvent.EventType.ELLIPSE_IMP, params, key);
    }

    @Override
    public void setAttachView(RecyclerView attachView) {
        this.attachView = attachView;
    }

    @Override
    public RecyclerView getAttachView() {
        return attachView;
    }
}
