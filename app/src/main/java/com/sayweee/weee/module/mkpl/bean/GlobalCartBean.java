package com.sayweee.weee.module.mkpl.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.sayweee.weee.module.cart.bean.CouponReminder;
import com.sayweee.weee.module.cart.bean.NewItemBean;

import java.io.Serializable;
import java.util.List;

import javax.annotation.Nullable;

public class GlobalCartBean implements Serializable {

    public int quantity;
    public List<NewItemBean> items;
    public FeeInfo fee_info;
    public ShippingInfo shipping_info;
    public VendorInfo vendor_info;
    public String promotion_tip;
    public List<PromotionDetail> promotion_details;
    public GroupOrderButton group_order_button;
    public CouponReminder coupon_reminder;
    public boolean is_record_seller_alcohol;
    public String sub_type;

    public static class VendorInfo implements Serializable {
        public int vendor_id;
        public String vendor_name;
        public String vender_logo_url;
        /*
        public boolean is_vender_cut_off;
        public String pre_order_desc;
        public boolean show_restaurant_invalid_label;
        public boolean vendor_is_opened;
         */
    }

    public static class FeeInfo implements Serializable {
        public String shipping_fee;
        public String sub_total_price;
        public String sub_total_base_price;
        public String total_price_with_activity;
        public String total_price_with_activity_and_coupon;
        /*
        public String activity_save_amount;
        public String coupon_discount;
        public String discount;
        public String final_amount;
        public String points_price;
        public String service_fee;
        public String tax;
        public String tip;
         */

        public String getTotalPrice() {
            return (total_price_with_activity_and_coupon == null)
                    ? total_price_with_activity : total_price_with_activity_and_coupon;
        }
    }

    public static class ShippingInfo implements Serializable {
        public String delivery_mode;
        public String delivery_pickup_date;
        public String orignal_shipping_fee;
        public String shipping_desc;
        public String shipping_fee;
        public String shipping_shipment_date;

        /*
//        private String cold_package_fee;
//        private String delivery_content;
//        private String delivery_time;
//        private String delivery_time_desc;
//        private float delivery_time_id;
//        private String estimated_time;
//        private String eta_time_content;
//        private String free_cold_package_fee;
//        private String free_shipping_desc;
//        private String free_shipping_desc_url;
//        private String hotdish_delivery_time_content;
//        private String hotdish_wave;
//        private boolean is_support_change_date;
//        private String original_shipping_free_fee;
//        private String orignal_cold_package_fee;
//        private String pick_up_point;
//        private String pickup_date;
//        private String pickup_time;
//        private String self_pickup_address;
//        private String shipping_delay_desc;
//        private float shipping_fee_type;
//        private String shipping_free_fee;
//        private String shipping_icon_url;
//        private String shipping_minimum_threshold;
//        private String shipping_shipment_date;
//        private String shipping_type_desc;
//        private String short_shipping_delay_desc;
         */
    }

    public static class PromotionDetail implements Serializable {

        public TagBean icon;
        public TagBean title;
        public List<ActivityItem> activity_items;
        public GroupOrderButton group_order_button;
        /*
//        public String amount;
//        public String base_amount;
         */

        public static class TagBean implements Serializable {
            public String tag_type;
            public String tag_text;
            public String tag_type_text_color;
            public String tag_type_bg_color;
            public String tag_icon_url;
        }

        public static class ActivityItem implements Serializable {
            public int product_id;
            public String product_key;
            public String title;
            public String img;
            public double price;
            public double base_price;
            public int quantity;
            public boolean is_claim;
            public String slug;
            public String view_link;
        }

        @JSONField(serialize = false, deserialize = false)
        @Nullable
        public String getTitle() {
            return title != null ? title.tag_text : null;
        }

        @JSONField(serialize = false, deserialize = false)
        @Nullable
        public String getIconUrl() {
            return icon != null ? icon.tag_icon_url : null;
        }

        @JSONField(serialize = false, deserialize = false)
        @Nullable
        public String getViewLink() {
            return activity_items != null && !activity_items.isEmpty() && activity_items.get(0) != null
                    ? activity_items.get(0).view_link
                    : null;
        }
    }

    public static class GroupOrderButton implements Serializable {
        public boolean show;
        public String desc;
        public String tip;
    }

    @JSONField(serialize = false, deserialize = false)
    @Nullable
    public String getSellerId() {
        return vendor_info != null ? String.valueOf(vendor_info.vendor_id) : null;
    }

    @JSONField(serialize = false, deserialize = false)
    @Nullable
    public String getVendorName() {
        return vendor_info != null ? vendor_info.vendor_name : null;
    }

    @JSONField(serialize = false, deserialize = false)
    @Nullable
    public String getShippingShipmentDate() {
        return shipping_info != null ? shipping_info.shipping_shipment_date : null;
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isEmpty() {
        return items == null || items.isEmpty();
    }

    @JSONField(serialize = false, deserialize = false)
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

}
