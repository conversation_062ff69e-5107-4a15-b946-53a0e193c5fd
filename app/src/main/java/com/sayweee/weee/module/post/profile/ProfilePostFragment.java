package com.sayweee.weee.module.post.profile;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.tabs.TabLayout;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.adapter.SafeStaggeredGridLayoutManager;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.post.PostVideoDetailActivity;
import com.sayweee.weee.module.post.base.CmtBaseLazyFragment;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.detail.ReviewDetailActivity;
import com.sayweee.weee.module.post.edit.PostEditorActivity;
import com.sayweee.weee.module.post.edit.bean.AdapterDraftData;
import com.sayweee.weee.module.post.edit.bean.AdapterUploadData;
import com.sayweee.weee.module.post.edit.service.PostUploadManager;
import com.sayweee.weee.module.post.edit.service.bean.PostDraftData;
import com.sayweee.weee.module.post.profile.adapter.ProfileItemAdapter;
import com.sayweee.weee.module.post.search.bean.FollowStatusBean;
import com.sayweee.weee.module.post.shared.CmtFailureData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.helper.PictureSelectorHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnItemSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.listener.OnViewHelper;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class ProfilePostFragment extends CmtBaseLazyFragment<ProfileViewModel> {
    public static final String TAG = "ProfilePostFragment";
    public static final int ACTIVITY_REQUEST_CODE = 0X01;
    private RecyclerView rvList;

    private static final String BUNDLE_CATE = "cate";
    private static final String BUNDLE_KEYWORD = "keyWord";
    private PostCategoryBean.CategoriesBean cate;

    private ProfileItemAdapter postListAdapter;

    private int page = 0;
    private TabLayout tbSecondType;
    private TextView emptyTv;
    private ImageView emptyIv;
    private TextView tvOops;
    private TextView tvNotYet;

    public static final String STATUS_DRAFT = "D";
    public static final String STATUS_REVIEW = "A";
    public static final String STATUS_PUBLISHED = "P";

    public static final String VIDEO = "video";
    public static final String POST = "post";
    public static final String REVIEW = "review";
    public static final String LIKES = "likes";
    public static final String COMMENTED = "commented";
    private EagleImpressionTrackerIml eagleImpressionTrackerIml;
    private String toReviewLink;

    public static Fragment newInstance(PostCategoryBean.CategoriesBean cate, String keyWord) {
        ProfilePostFragment fragment = new ProfilePostFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(BUNDLE_CATE, cate);
        bundle.putString(BUNDLE_KEYWORD, keyWord);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_item_profile;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        tbSecondType = findViewById(R.id.tb_second_type);
        rvList = findViewById(R.id.mRecyclerView);//商品list
        Bundle bundle = getArguments();
        if (bundle != null) {
            Serializable serializable = bundle.getSerializable(BUNDLE_CATE);
            if (serializable instanceof PostCategoryBean.CategoriesBean) {
                cate = (PostCategoryBean.CategoriesBean) serializable;
            }
        }
        eagleImpressionTrackerIml = new EagleImpressionTrackerIml();
        initRv();//瀑布流
        initSecondTab();
        rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent();
                }
            }
        });
    }

    @Override
    public void loadData() {
        onPageRefresh(true);
    }

    @Override
    public void attachModel() {
        viewModel.postCategoryBean.observe(this, new Observer<PostCategoryBean>() {
            @Override
            public void onChanged(PostCategoryBean bean) {
                toReviewLink = bean.to_review_link;
                postListAdapter.setJustViewedId(cate.justViewedId);
                if (page == 0) {
                    postListAdapter.isUseEmpty(EmptyUtils.isEmpty(bean.list));
                    postListAdapter.setAdapterData(bean.list);
                } else {
                    postListAdapter.appendAdapterData(bean.list);
                }
                if (postListAdapter.getData().size() >= bean.total) {
                    postListAdapter.isUseEmpty(true);
                    postListAdapter.loadMoreEnd();
                } else {
                    postListAdapter.loadMoreComplete();
                }
                if (!EmptyUtils.isEmpty(bean.list)) {
                    postListAdapter.isUseEmpty(false);
                } else {
                    postListAdapter.loadMoreEnd();
                }
                impressionData();
                reportImpressionEvent();
                if (isSelect) {
                    VeilTools.hide(findViewById(R.id.vl_post_list));
                    isSelect = false;
                } else {
                    showVeil(false);
                }
            }
        });
        //post点赞联动
        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                postListAdapter.toggleCollect(map);
            }
        });
        //post点赞联动
        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                postListAdapter.toggleCollect(map);
            }
        });
        //post详情删除后刷新列表
        SharedViewModel.get().postDeleteData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean b) {
                getList(null, false, true);
            }
        });
        viewModel.editPostDataMutableLiveData.observe(this, new Observer<List<PostCategoryBean.ListBean>>() {
            @Override
            public void onChanged(List<PostCategoryBean.ListBean> beanList) {
                if (beanList.isEmpty()) {
                    postListAdapter.isUseEmpty(true);
                }
                postListAdapter.setAdapterData(beanList);
                impressionData();
                reportImpressionEvent();
                if (isSelect) {
                    VeilTools.hide(findViewById(R.id.vl_post_list));
                    isSelect = false;
                } else {
                    showVeil(false);
                }
            }
        });
        viewModel.postDraftData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                if (list.isEmpty()) {
                    postListAdapter.isUseEmpty(true);
                }
                postListAdapter.setAdapterDraftData(list);
                if (isSelect) {
                    VeilTools.hide(findViewById(R.id.vl_post_list));
                    isSelect = false;
                } else {
                    showVeil(false);
                }
            }
        });
        CmtSharedViewModel.get().postPublishedTotal.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer total) {
                showPublishedCount(total);
            }
        });

        viewModel.updateReviewTotal.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer total) {
                showReviewPublishedCount(total);
            }
        });
        viewModel.postInReviewData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                setAdapterData(true, list);
                impressionData();
                reportImpressionEvent();
                if (isSelect) {
                    VeilTools.hide(findViewById(R.id.vl_post_list));
                    isSelect = false;
                } else {
                    showVeil(false);
                }
            }
        });
        viewModel.postInReviewAppendData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                setAdapterData(false, list);
                impressionData();
                reportImpressionEvent();
            }
        });

        SharedViewModel.get().toFragmentResultLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String status) {
                if (EmptyUtils.isEmpty(status)) return;
                onActivityData(status);
                SharedViewModel.get().toFragmentResult(null);
            }
        });

        SharedViewModel.get().followUserData.observe(this, new Observer<FollowStatusBean>() {
            @Override
            public void onChanged(FollowStatusBean bean) {
                if (!isSelf()) {
                    postListAdapter.refreshFollow(bean.followStatus);
                }
            }
        });

        CmtSharedViewModel.get().observeSpamFailureData(getViewLifecycleOwner(), new Observer<CmtFailureData>() {
            @Override
            public void onChanged(CmtFailureData data) {
                if (data == null) return;

            }
        });

    }
    //    @Override
//    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        if (resultCode == RESULT_OK) {
//            if (data != null) {
//                cate.status = data.getStringExtra("status");
//                tbSecondType.selectTab(tbSecondType.getTabAt(cate.status.equals(STATUS_PUBLISHED) ? 0 : cate.status.equals(STATUS_REVIEW) ? 1 : 2));
//                if (tbSecondType.getSelectedTabPosition() == 2) {
//                    rvList.scrollToPosition(0);
//                }
//                getList(null, false, true);
//                Logger.d(TAG, "status : " + cate.status);
//            }
//        }

    //    }
    private void onActivityData(String status) {
        if (!EmptyUtils.isEmpty(status)) {
            cate.status = status;
            tbSecondType.selectTab(tbSecondType.getTabAt(cate.status.equals(STATUS_PUBLISHED) ? 0 : cate.status.equals(STATUS_REVIEW) ? 1 : 2));
        }
        if (tbSecondType.getSelectedTabPosition() == 2) {
            rvList.scrollToPosition(0);
        }
        getList(null, false, true);
    }

    private boolean isSelect = false;

    private void initSecondTab() {
        if (isSelf()) {

//            tbSecondType.setVisibility(!cate.type.equals(LIKES) && !cate.type.equals(COMMENTED) ? View.VISIBLE : View.GONE);
            tbSecondType.addTab(tbSecondType.newTab().setText(getString(R.string.published)), true);
            if (cate.type.equals(REVIEW)) {
                tbSecondType.addTab(tbSecondType.newTab().setText(getString(R.string.reviewing)), false);
            }
            if (cate.type.equals(VIDEO)) {
                tbSecondType.addTab(tbSecondType.newTab().setText(getString(R.string.draft)), false);
            }
            tbSecondType.setTabMode(TabLayout.MODE_SCROLLABLE);
            tbSecondType.selectTab(tbSecondType.getTabAt(cate.view_pos));
            tbSecondType.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    postListAdapter.isUseEmpty(false);
                    postListAdapter.setAdapterData(null);
                    cate.view_pos = tab.getPosition();
                    VeilTools.show(findViewById(R.id.vl_post_list));
                    isSelect = true;
                    switch (tab.getPosition()) {
                        case 0:
                            cate.status = STATUS_PUBLISHED;
                            emptyTv.setVisibility(View.VISIBLE);
                            emptyIv.setImageResource(cate.type.equals(VIDEO)
                                    ? R.mipmap.pic_add_video_empty
                                    : R.mipmap.pic_no_reviews);
                            tvOops.setText(getResources().getString(cate.type.equals(VIDEO) ? R.string.no_posts_yet : R.string.no_reviews_yet));
                            break;

                        case 1:
                            emptyTv.setVisibility(View.VISIBLE);
                            if (cate.type.equals(VIDEO)) {
//                                emptyIv.setImageResource(R.mipmap.iv_empty_rhost);
//                                tvOops.setText(getResources().getString(R.string.uh_oh));
                                emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                                tvOops.setText(getResources().getString(R.string.no_drafts_yet));
                                cate.status = STATUS_DRAFT;
                            } else {
                                emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                                tvOops.setText(getResources().getString(R.string.nothing_in_review));
                                cate.status = STATUS_REVIEW;
                            }
                            emptyTv.setVisibility(cate.type.equals(VIDEO) ? View.VISIBLE : View.GONE);
                            break;

                        case 2:
                            cate.status = STATUS_DRAFT;
                            emptyIv.setImageResource(R.mipmap.pic_add_video_empty);
                            emptyTv.setVisibility(View.GONE);
                            tvOops.setText(getResources().getString(R.string.Hmm));
                            emptyTv.setVisibility(View.VISIBLE);

                            break;

                    }

                    onPageRefresh(false);
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {

                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {

                }
            });
        } else {
            tbSecondType.setVisibility(View.GONE);
        }
    }

    private void showPublishedCount(Integer total) {
        TabLayout.Tab tabPublished = tbSecondType.getTabAt(0);
        if (tabPublished != null && tabPublished.getText() != null) {
            if (tabPublished.getText().toString().startsWith(getString(R.string.published)) && VIDEO.equalsIgnoreCase(cate.type)) {
                StringBuilder strPublished = new StringBuilder();
                strPublished.append(getResources().getString(R.string.published));
                if (total != null && total > 0) {
                    strPublished.append(" · ").append(total);
                }
                tabPublished.setText(strPublished);
            }
        }
    }
    private void showReviewPublishedCount(Integer total) {
        TabLayout.Tab tabPublished = tbSecondType.getTabAt(0);
        if (tabPublished != null && tabPublished.getText() != null) {
            if (tabPublished.getText().toString().startsWith(getString(R.string.published)) && REVIEW.equalsIgnoreCase(cate.type)) {
                StringBuilder strPublished = new StringBuilder();
                strPublished.append(getResources().getString(R.string.published));
                if (total != null && total > 0) {
                    strPublished.append(" · ").append(total);
                }
                tabPublished.setText(strPublished);
            }
        }
    }

    private void initRv() {
        postListAdapter = new ProfileItemAdapter();
        StaggeredGridLayoutManager manager = new SafeStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        rvList.setLayoutManager(manager);
        rvList.setAdapter(postListAdapter);
        // empty view
        final View emptyView = ViewTools.getHelperView(activity, R.layout.profile_empty, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                emptyTv = helper.getView(R.id.tv_create);
                emptyIv = helper.getView(R.id.iv_empty);
                tvOops = helper.getView(R.id.tv_oops);
                tvNotYet = helper.getView(R.id.tv_not_yet);
                tvNotYet.setVisibility(View.GONE);
//                emptyTv.setVisibility(View.GONE);
                if (cate.isSelf()) {
                    switch (cate.type) {
                        case VIDEO:
                            if (cate.status.equals(STATUS_PUBLISHED)) {
                                emptyIv.setImageResource(R.mipmap.pic_add_video_empty);
                                tvOops.setText(getResources().getString(R.string.no_posts_yet));
                            } else {
                                emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                                emptyTv.setVisibility(View.GONE);
                                tvOops.setText(getResources().getString(R.string.no_drafts_yet));
                            }
                            emptyTv.setVisibility(View.VISIBLE);
                            emptyTv.setText(getString(R.string.create_a_post));
                            break;

                        case REVIEW:
                            emptyTv.setText(getString(R.string.post_earn_points));
                            if (!EmptyUtils.isEmpty(cate.status) && cate.status.equals(STATUS_PUBLISHED)) {
                                emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                                emptyTv.setVisibility(View.VISIBLE);
                                tvOops.setText(getResources().getString(R.string.no_reviews_yet
                                ));
                            } else {
                                emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                                tvOops.setText(getResources().getString(R.string.nothing_in_review));
                            }
                            break;

                        case LIKES:
                            emptyIv.setImageResource(R.mipmap.pic_like_video_empty);
                            tvOops.setText(getResources().getString(R.string.no_liked_posts_yet));
                            helper.getView(R.id.mEmptyLayout).setPadding(0, CommonTools.dp2px(28), 0, 0);
                            break;

                        case COMMENTED:
                            helper.getView(R.id.mEmptyLayout).setPadding(0, CommonTools.dp2px(28), 0, 0);
                            tvOops.setText(getResources().getString(R.string.no_comments_yet));
                            emptyIv.setImageResource(R.mipmap.pic_comment_empty);
                            break;
                    }
                } else {
                    switch (cate.type) {
                        case VIDEO:
                            emptyIv.setImageResource(R.mipmap.pic_add_video_empty);
                            tvOops.setText(getResources().getString(R.string.no_posts_yet));
                            break;
                        case REVIEW:
                            emptyIv.setImageResource(R.mipmap.pic_no_reviews);
                            tvOops.setText(getResources().getString(R.string.no_reviews_yet));
                            break;
                    }
                }
                emptyTv.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (cate.type.equals(VIDEO)) {
                            execVideoPicker();
                        } else {
                            startActivity(WebViewActivity.getIntent(activity, !EmptyUtils.isEmpty(toReviewLink) ? toReviewLink : Constants.Url.POST_NEW_PORTAL));
                        }
                    }
                });
//                helper.getView(R.id.mEmptyLayout).setBackgroundColor(getResources().getColor(R.color.color_back));
            }
        });
        postListAdapter.setEmptyView(emptyView);
        postListAdapter.isUseEmpty(false);

        postListAdapter.setOnItemClickListener(new OnItemSafeClickListener() {
            @Override
            public void onItemClickSafely(BaseQuickAdapter adapter, View view, int position) {
                AdapterDataType item = postListAdapter.getItem(position);
                if (item instanceof PostCategoryBean.ListBean) {
                    PostCategoryBean.ListBean bean = (PostCategoryBean.ListBean) item;
                    if (bean.isVideoPost() && cate != null) {
                        startActivityForResult(PostVideoDetailActivity.getIntent(activity, PostVideoDetailActivity.FROM_PROFILE
                                , bean
                                , cate.type.equals(VIDEO) ? PostVideoDetailActivity.KEY_SOURCE_HOME : cate.type.equals(LIKES) ? PostVideoDetailActivity.KEY_SOURCE_LIKES : PostVideoDetailActivity.KEY_SOURCE_COMMENTED
                                , (!isSelf()) ? String.valueOf(cate.uid) : (cate.type.equals(VIDEO) ? AccountManager.get().getUID() : null)
                                , cate.status), ACTIVITY_REQUEST_CODE);
                    } else {
                        startActivity(ReviewDetailActivity.getIntent(activity, bean.id));
                    }
                } else if (item instanceof AdapterDraftData) {
                    PostDraftData draftData = ((AdapterDraftData) item).t;
                    //跳转编辑
                    startActivityForResult(PostEditorActivity.getIntentOnDraft(activity, draftData), 999);
                } else if (item instanceof AdapterUploadData) {
                    AdapterUploadData uploadData = (AdapterUploadData) item;
                    if (uploadData.isStatusSuccess()) {
                        //已上传成功
                        startActivityForResult(PostVideoDetailActivity.getIntent(activity, PostVideoDetailActivity.FROM_PROFILE
                                , uploadData.convertPostBean()
                                , PostVideoDetailActivity.KEY_SOURCE_HOME
                                , AccountManager.get().getUID()
                                , cate.status), ACTIVITY_REQUEST_CODE);
                    }
                }
            }
        });

        postListAdapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {

            @Override
            public void onItemChildClickSafely(BaseQuickAdapter quickAdapter, View view, int position) {
                AdapterDataType item = postListAdapter.getItem(position);
                if (item instanceof AdapterDraftData) {
                    AdapterDraftData draftData = (AdapterDraftData) item;
                    if (view.getId() == R.id.iv_delete) {
                        showDraftRemoveDialog(draftData.getDraftId());
                    }
                } else if (item instanceof AdapterUploadData) {
                    AdapterUploadData uploadData = (AdapterUploadData) item;
                    if (view.getId() == R.id.tv_delete) {
                        //删除
                        showUploadRemoveDialog(uploadData.getUploadId());
                    } else if (view.getId() == R.id.tv_retry) {
                        //重试
                        uploadData.updateSelf(PostUploadManager.get().getUploadCacheData(uploadData.getUploadId()));
                        PostUploadManager.get().execUploadVideo(uploadData.t);
                    }
                } else if (item instanceof PostCategoryBean.ListBean) {

                }
            }
        });
        //loadMore
        postListAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if ((!EmptyUtils.isEmpty(postListAdapter.getData()) && cate.status != null && !cate.status.equals(STATUS_DRAFT)) || cate.type.equals(LIKES) || cate.type.equals(COMMENTED)) {
                    AdapterDataType item = postListAdapter.getData().get(postListAdapter.getData().size() - 1);
                    if (item instanceof PostCategoryBean.ListBean) {
                        PostCategoryBean.ListBean lastBean = (PostCategoryBean.ListBean) item;
                        getList(String.valueOf(lastBean.start_id), true, false);
                    }
                } else {
                    postListAdapter.loadMoreEnd();
                }
            }
        }, rvList);
    }

    private int oldPosition;

    public boolean isLikeModule() {
        return cate != null && LIKES.equalsIgnoreCase(cate.type);
    }

    public boolean isCommentedModule() {
        return cate != null && COMMENTED.equalsIgnoreCase(cate.type);
    }

    public boolean isPostDraft() {
        return cate != null && VIDEO.equalsIgnoreCase(cate.type) && STATUS_DRAFT.equalsIgnoreCase(cate.status);
    }

    private void getList(String start_id, boolean isSilent, boolean isLoadNewData) {
        if (cate == null) {
            return;
        }
        page = isLoadNewData ? 0 : -1;
        if (isLikeModule()) {
            viewModel.getMyLikes(start_id);
        } else if (isCommentedModule()) {
            viewModel.getMyCommented(start_id);
        } else {
            if (isSelf()) {
                if (cate.isPostInReview()) {
                    viewModel.getPostInReviewData(isSilent, isLoadNewData,
                            cate.type,
                            cate.status,
                            start_id);
                } else if (isPostDraft()) {
                    getDraftData();
                } else {
                    viewModel.getMyPostList(
                            isSilent,
                            cate.type,
                            cate.status,
                            start_id);
                }
            } else {
                viewModel.getOtherPostList(
                        cate.uid,
                        cate.type,
                        cate.status,
                        start_id);
            }
        }

    }

    private void onPageRefresh(boolean isSilent) {
        if (!isSelect) {
            showVeil(true);
        }
        getList(null, isSilent, true);
    }

    public void setAdapterData(boolean isNewData, List<AdapterDataType> list) {
        if (isNewData) {
            postListAdapter.setNewData(list);
            postListAdapter.isUseEmpty(EmptyUtils.isEmpty(list));
            AdapterDataType item = postListAdapter.getItem(list.size() - 1);
            if (item instanceof AdapterUploadData) {
                postListAdapter.loadMoreEnd();
                return;
            }
        } else {
            postListAdapter.addData(list);
        }
        if (list.size() < 10) {
            postListAdapter.loadMoreEnd();
        } else {
            postListAdapter.loadMoreComplete();
        }
    }

    public boolean isSelf() {
        return cate == null || cate.isSelf();
    }

    private void execVideoPicker() {
        PictureSelectorHelper.showVideoSelector(activity, new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(List<LocalMedia> list) {
                if (!EmptyUtils.isEmpty(list)) {
                    LocalMedia media = list.get(0);
                    if (ProfilePostFragment.this.isAdded() && !ProfilePostFragment.this.isDetached()) {
                        startActivityForResult(PostEditorActivity.getIntentOnAdd(activity, media, null, "profile"), 999);
                    } else if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
                        activity.startActivityForResult(PostEditorActivity.getIntentOnAdd(activity, media, null, "profile"), 999);
                    }
                }
            }

            @Override
            public void onCancel() {

            }

        });
    }

    private void showDraftRemoveDialog(long draftId) {
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setUp(new OnDialogClickListener() {
                    @Override
                    public void onClick(WrapperDialog dialog, View view) {
                        dialog.dismiss();
                        removeDraftItem(draftId);
                    }
                }, getString(R.string.are_you_sure_you_want_to_delete_the_draft), getString(R.string.delete), getString(R.string.cancel))
                .show();

    }

    private void showUploadRemoveDialog(long upload) {
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setUp(new OnDialogClickListener() {
                    @Override
                    public void onClick(WrapperDialog dialog, View view) {
                        dialog.dismiss();
                        removeUploadItem(upload);
                    }
                }, getString(R.string.are_you_sure_you_want_to_delete_this_video), getString(R.string.delete), getString(R.string.cancel))
                .show();

    }

    private void getDraftData() {
        viewModel.getDraftData();
    }

    private void removeUploadItem(long uploadId) {
        List<AdapterDataType> list = postListAdapter.getData();
        viewModel.removeUploadData(list, uploadId);
    }

    private void removeDraftItem(long draftId) {
        List<AdapterDataType> list = postListAdapter.getData();
        viewModel.removeDraftData(list, draftId);
    }

    private void impressionData() {
        String type = cate.type.equals(VIDEO) ? "Posts" : cate.type.equals(REVIEW) ? "Reviews" : cate.type.equals(LIKES) ? "Liked" : "Commented";
        int pos = cate.type.equals(VIDEO) ? 0 : cate.type.equals(REVIEW) ? 1 : cate.type.equals(LIKES) ? 2 : 3;
        postListAdapter.setSecInfo(type, pos);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isPostDraft()) {
            getDraftData();
        }
    }


    private void trackPv() {
        String trackType = "";
        switch (cate.type) {
            case VIDEO:
                trackType = isSelf() ? "me_profile_post" : "comm_profile_post";
                break;

            case REVIEW:
                trackType = isSelf() ? "me_profile_review" : "comm_profile_review";
                break;

            case LIKES:
                trackType = "me_profile_like";
                break;

            case COMMENTED:
                trackType = "me_profile_comment";
                break;

        }
        AppAnalytics.logPageView(trackType, this);
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        trackPv();
        eagleImpressionTrackerIml.onPageResume(rvList);
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        eagleImpressionTrackerIml.onPagePause(rvList);
    }

    private void reportImpressionEvent() {
        reportImpressionEvent(0);
    }

    private void reportImpressionEvent(int delay) {
        if (rvList != null) {
            rvList.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (eagleImpressionTrackerIml != null && isAdded() && isVisible()) {
                        eagleImpressionTrackerIml.trackImpression(rvList);
                    }
                }
            }, delay);
        }
    }

    private void showVeil(boolean show) {
        SharedViewModel.get().profileListVeil.postValue(show);
        if (isSelf()) {
            tbSecondType.postDelayed(new Runnable() {
                @Override
                public void run() {
                    tbSecondType.setVisibility(!cate.type.equals(LIKES) && !cate.type.equals(COMMENTED) ? View.VISIBLE : View.GONE);
                }
            }, 0);
        }
        VeilTools.show(findViewById(R.id.vl_post_list), show);
    }
}
