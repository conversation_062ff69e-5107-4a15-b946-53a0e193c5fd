package com.sayweee.weee.module.search.v2.adapters.viewholders;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Spannable;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Space;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.search.service.SearchPanelViewModel;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.search.v2.SearchV2Manager;
import com.sayweee.weee.module.search.v2.SearchV2TrackingManager;
import com.sayweee.weee.module.search.v2.UserPersonalizationManager;
import com.sayweee.weee.module.search.v2.adapters.BaseListAdapter;
import com.sayweee.weee.module.search.v2.adapters.SearchResultsV2StreamAdapter;
import com.sayweee.weee.module.search.v2.bean.SearchJsonField;
import com.sayweee.weee.module.search.v2.widget.CustomBadgeView;
import com.sayweee.weee.module.search.v2.widget.CustomImageView;
import com.sayweee.weee.module.search.v2.widget.CustomRemoteUrlBadgeView;
import com.sayweee.weee.module.search.v2.widget.SearchV2FlowLayout;
import com.sayweee.weee.module.search.v2.widget.TextIconsBadgeView;
import com.sayweee.weee.module.search.v2.widget.TopRankingBadgeView;
import com.sayweee.weee.module.search.v2.widget.WeeeRewardsBadgeView;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.utils.DebugTimer;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonTools;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.StringUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.support.DeviceUtils;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.utils.Spanny;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public abstract class SearchResultsProductViewHolderBase extends SearchResultsViewHolderBase {

    static final String TAG = "SearchResultsProductViewHolderBase";

    static final boolean DEBUG_RENDER = DevConfig.isDebug();
    static final boolean DEBUG_VERBOSE = DevConfig.isDebug();

    static final String S_GET_IT = "get it ";

    static final boolean ENABLED_SHIPS_FROM_FOR_BRAND = false;
    static final boolean ENABLED_SHIPS_FROM_CLICKABLE = false;
    static final boolean ENABLED_SHIPS_FROM_ETA_FULL_BOLD = true;
    static final boolean ENABLED_NEW_NOTIFY_ME_BUTTON_DESIGN = true;
    static final boolean ENABLED_NEW_CHANGE_DATE_BUTTON_DESIGN = true;
    static final boolean ENABLED_NEW_PURCHASED_BUTTON_DESIGN = true;
    static final int MAXIMUM_REMAINING_ITEMS = 300;
    static final int MINIMUM_WEEKLY_SOLD = 50;
    static final int MINIMUM_REMAINING_COUNT = 0;
    static final int WILL_SELL_OUT_SOON_WEEK_HIGH_VOLUME_LIMIT = 1000;
    static final int WILL_SELL_OUT_SOON_WEEK_MEDIUM_VOLUME_LIMIT = 500;
    static final double WILL_SELL_OUT_SOON_WEEK_THRESHOLD_HIGH_VOLUME = 0.1;
    static final double WILL_SELL_OUT_SOON_WEEK_THRESHOLD_MEDIUM_VOLUME = 0.15;
    static final double WILL_SELL_OUT_SOON_WEEK_THRESHOLD_LOW_VOLUME = 0.2;
    static final int WILL_SELL_OUT_SOON_CARD_INFO_MAX_WITHOUT_SOLD = 0; // 50;
    static final boolean CONSIDER_ONE_DAY_SKU_SOLD_COUNT = true;

    protected static Integer CACHE_IMAGE_SPECIAL_PADDING = null;

    private static Integer DIMEN_COMPACT_BTN_COLLAPSED_SIZE = null;

    static Integer COLOR_SHIPS_FROM_NAME = null;
    static Integer COLOR_SHIPS_FROM_ETA = null;

    static Integer COLOR_TITLE_OSS = null;

    static Integer DIMEN_BADGES_CONTAINER_SPACING = null;

    static Integer BTN_NOTIFY_ME_TEXT_COLOR_ACTIVE = null;

    static Integer BTN_NOTIFY_ME_TEXT_COLOR_NORMAL = null;

    static Drawable BTN_NOTIFY_ME_BG_DRAWABLE_NORMAL = null;

    static Drawable BTN_NOTIFY_ME_BG_DRAWABLE_ACTIVE = null;

    private static Integer COLOR_SHADOW = null;
    private static Float DIMEN_CARD_ELEVATION = null;
    private static Integer CUSTOM_BADGE_ICON_SIZE = null;
    private static String EVERYDAY_VALUE_LABEL = null;

    protected ProductView productView;

    protected View productContainer;

    protected TextView brandView;
    public TextView titleView;
    public TextView unitPriceView;
    Integer originalTitleViewTextColor = null;

    public View priceContainer;
    public View everydayValueDividerView;
    public TextView everydayValueTextView;
    public View volumePriceContainerView;
    public TextView volumePriceValueView;
    public TextView volumePriceThresholdView;
    public TextView volumePriceRegularView;
    public TextView volumePriceRegularViewStriked;
    public TextView priceView;
    public TextView priceStrikedView;
    public TextView soldAmountView;

    private Space contentTextViewsSpacer;

    public View bottomLineDividerView;

    private TextView shipsFromEtaView;

    public CustomImageView imageView;

    public Integer imageSize;

    public View outOfStockView;
    public TextView shipsFromView;
    public TextView remainingTipView;

    protected TextView uiDebugTextView;

    protected String cartControlsLayoutOpSku;
    protected CartOpLayout cartControlsLayoutOp;

    public TextView discountPercentageTag;

    public TextView promoTagView;
    public Runnable promoTagViewStartMarqueeTask;

    private HashMap<String, View> badgesViews;
    public ViewGroup badgesContainerView;
    public TopRankingBadgeView topRankingBadgeView;
    private View topRankingBadgeViewTouchFeedback;

    protected String sku;
    protected String title;
    public boolean isCartControlsNotifyMeButton = false;
    public boolean isCartControlsChangeDateAvailable = false;
    public boolean isCartControlsPurchasedAvailable = false;

    private ViewGroup btnNotifyMeContainer;

    private AppCompatImageView btnNotifyMeContainerImageViewIcon;

    private TextView btnNotifyMeContainerTextView;

    private ViewGroup btnChangeDateContainer;

    private ViewGroup btnPurchasedContainer;

    private TextView sposoredLabelTextView;

    boolean isBtnNotifyMeActiveState = false; // inactive state by default

    private ImageView relatedProductsButton;

    protected boolean isCondensed;

    public SearchResultsProductViewHolderBase(View itemView) {
        super(itemView);
    }

    @SuppressLint("ClickableViewAccessibility")
    public SearchResultsProductViewHolderBase(View itemView, boolean isCondensed) {
        super(itemView);

        this.isCondensed = isCondensed;

        this.uiDebugTextView = SearchResultsV2StreamAdapter.UI_DEBUG_TYPE != SearchResultsV2StreamAdapter.UI_DEBUG_OFF  ? itemView.findViewById(R.id.search_results_ui_debug) : null;
        if (this.uiDebugTextView != null) {
            this.uiDebugTextView.setVisibility(View.GONE);
        }

        this.productView = findProductView();
        this.cartControlsLayoutOp = itemView.findViewById(R.id.layout_op);

        boolean isUsingProductViewLayout = isUsingProductViewLayout();
        if (isUsingProductViewLayout) return;

        this.productContainer = itemView.findViewById(R.id.search_result_product_container);
        if (this.productContainer != null) {
            this.productContainer.setClipToOutline(false);
        }

        this.titleView = itemView.findViewById(R.id.search_result_product_title);
        this.unitPriceView = SearchResultsFragmentV2.SHOW_PRODUCT_UNIT_PRICE ? itemView.findViewById(R.id.search_result_product_unit_price) : null;

        this.bottomLineDividerView = itemView.findViewById(R.id.search_result_product_v2_bottom_line_divider);
        if (SearchResultsFragmentV2.ENABLE_LIST_VIEW_DIVIDER_DECORATION && this.bottomLineDividerView != null) {
            this.bottomLineDividerView.setVisibility(View.GONE);
        }

        this.brandView = itemView.findViewById(R.id.search_result_product_tips_brand_name);
        this.priceContainer = itemView.findViewById(R.id.search_result_product_price_container);

        View vPriceContainer = this.priceContainer != null ? this.priceContainer : itemView;

        this.priceView = vPriceContainer != null ? vPriceContainer.findViewById(R.id.search_result_product_price) : null;
        this.priceStrikedView = vPriceContainer != null ? vPriceContainer.findViewById(R.id.search_result_product_price_striked) : null;
        if (this.priceStrikedView != null) {
            this.priceStrikedView.setPaintFlags(priceStrikedView.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
        }

        this.volumePriceContainerView = vPriceContainer != null ? vPriceContainer.findViewById(R.id.search_result_product_volume_price_container) : null;
        this.volumePriceValueView = volumePriceContainerView != null ? volumePriceContainerView.findViewById(R.id.search_result_product_volume_price_value) : null;
        this.volumePriceThresholdView = volumePriceContainerView != null ? volumePriceContainerView.findViewById(R.id.search_result_product_volume_price_threshold) : null;
        this.volumePriceRegularView = vPriceContainer != null ? vPriceContainer.findViewById(R.id.search_result_product_volume_price_regular) : null;
        this.volumePriceRegularViewStriked = vPriceContainer != null ? vPriceContainer.findViewById(R.id.search_result_product_volume_price_regular_striked) : null;
        if (this.volumePriceRegularViewStriked != null) {
            this.volumePriceRegularViewStriked.setPaintFlags(this.volumePriceRegularViewStriked.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
        }
        this.everydayValueDividerView = volumePriceContainerView != null ? volumePriceContainerView.findViewById(R.id.search_result_product_everyday_value_divider) : null;
        this.everydayValueTextView = volumePriceContainerView != null ? volumePriceContainerView.findViewById(R.id.search_result_product_everyday_value_text) : null;

        this.discountPercentageTag = itemView.findViewById(R.id.search_result_product_discount_percentage_tag);
        this.promoTagView = itemView.findViewById(R.id.search_result_product_badge_promo);
        this.badgesContainerView = itemView.findViewById(R.id.search_result_product_badges_container);
        if (this.badgesContainerView != null) {
            this.badgesViews = new HashMap<>();
            int childCount = this.badgesContainerView.getChildCount();
            for (int i = childCount -1; i >= 0; i--) {
                View view = this.badgesContainerView.getChildAt(i);
                if (view != null) {
                    Object tag = view.getTag();
                    if (tag != null && tag instanceof String) {
                        this.badgesContainerView.removeView(view);
                        this.badgesViews.put((String) tag, view);
                    }
                }
            }
        }

        this.topRankingBadgeView = itemView.findViewById(R.id.search_result_product_badge_top_ranking);
        this.topRankingBadgeViewTouchFeedback = itemView.findViewById(R.id.search_result_product_badge_top_ranking_touch_feedback);

        this.contentTextViewsSpacer = itemView.findViewById(R.id.s_search_result_product_row_pricing_container_spacing);
        this.soldAmountView = itemView.findViewById(R.id.search_result_product_sold_amount);
        this.shipsFromView = itemView.findViewById(R.id.search_result_product_ships_from);
        this.shipsFromEtaView = itemView.findViewById(R.id.search_result_product_ships_eta);
        this.remainingTipView = itemView.findViewById(R.id.search_result_product_remaining_tip);

        this.outOfStockView = itemView.findViewById(R.id.search_result_product_sold_out);

        this.btnChangeDateContainer = itemView.findViewById(R.id.search_result_product_v2_btn_change_date);
        if (btnChangeDateContainer instanceof CardView) {
            setCartControlsShadowStyle((CardView) btnChangeDateContainer);
        }

        this.btnPurchasedContainer = itemView.findViewById(R.id.search_result_product_v2_btn_purchased);
        if (btnPurchasedContainer instanceof CardView) {
            setCartControlsShadowStyle((CardView) btnPurchasedContainer);
        }

        this.btnNotifyMeContainer = itemView.findViewById(R.id.search_result_product_v2_btn_notify_me);
        if (this.btnNotifyMeContainer != null) {
            this.btnNotifyMeContainerImageViewIcon = this.btnNotifyMeContainer.findViewById(R.id.search_result_product_v2_btn_notify_me_icon);
            this.btnNotifyMeContainerTextView = this.btnNotifyMeContainer.findViewById(R.id.search_result_product_v2_btn_notify_me_text);
        }
        if (btnNotifyMeContainer instanceof CardView) {
            setCartControlsShadowStyle((CardView) btnNotifyMeContainer);
        }

        if (btnNotifyMeContainer != null || btnChangeDateContainer != null || btnPurchasedContainer != null) {
            final boolean isCompactMode = isSecondarySectionAdapter();
            if (isCompactMode) {
                if (DIMEN_COMPACT_BTN_COLLAPSED_SIZE == null) {
                    DIMEN_COMPACT_BTN_COLLAPSED_SIZE = getContext().getResources().getDimensionPixelSize(R.dimen.search_results_product_cart_controls_compact_collapsed_size);
                }
                ViewGroup.LayoutParams notifyMeLayoutParams = btnNotifyMeContainer.getLayoutParams();
                if (notifyMeLayoutParams != null) {
                    notifyMeLayoutParams.width = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                    notifyMeLayoutParams.height = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                }
                ViewGroup.LayoutParams changeDateLayoutParams = btnChangeDateContainer.getLayoutParams();
                if (changeDateLayoutParams != null) {
                    changeDateLayoutParams.width = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                    changeDateLayoutParams.height = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                }
                ViewGroup.LayoutParams purchasedLayoutParams = btnPurchasedContainer.getLayoutParams();
                if (purchasedLayoutParams != null) {
                    purchasedLayoutParams.width = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                    purchasedLayoutParams.height = DIMEN_COMPACT_BTN_COLLAPSED_SIZE;
                }
            }
        }

        this.imageView = itemView.findViewById(R.id.search_result_product_image);
        if (this.imageView != null) {
            this.imageView.removeTouchFeedbackView();
        }

        this.sposoredLabelTextView = itemView.findViewById(R.id.search_result_product_sponsored_label);

        if (Constants.SearchV2.IS_RELATED_PRODUCTS_ENABLED) {
            this.relatedProductsButton = itemView.findViewById(R.id.search_v2_related_products_button);
        }
    }

    public SearchResultsProductViewHolderBase self() {
        return this;
    }

    protected boolean isSecondarySectionAdapter() {
        return false;
    }

    protected boolean isUsingProductViewLayout() {
        return productView != null;
    }

    protected ProductView findProductView() {
        return null;
    }

    @Override
    public void bind(JSONObject jsonObject, int position, Map<String, Object> state, BaseListAdapter.OnItemClickListener onItemClickListener) {
        DebugTimer timer = DEBUG_RENDER ? new DebugTimer(false) : null;

        // I'll keep this here for testing purpose
        /*if (currentJsonObjectBind != null && currentJsonObjectBind.optLong("id") == jsonObject.optLong("id")) {
            final boolean isMainSection = this instanceof SearchResultsMainSectionProductViewHolder;
            Logger.d("bindDbg isMainSection:"+isMainSection+" position:" + position + " jsonObject.id:" + jsonObject.optString("id") + " jsonObject.hashCode:" + jsonObject.hashCode() + " currentJsonObjectBind.id:" + currentJsonObjectBind.optString("id") + " currentJsonObjectBind.hashCode:" + currentJsonObjectBind.hashCode());
        }*/

        if (setCurrentJsonObjectBind(jsonObject)) {
            if (DEBUG_RENDER) timer.mark("setCurrentJsonObjectBind");
            AdsManager.setTrackImpressionFlag(jsonObject, false);
            if (DEBUG_RENDER) timer.mark("setTrackImpressionFlag");
        }

        try {
            if (this.itemView.getVisibility() != View.VISIBLE) {
                this.itemView.setVisibility(View.VISIBLE);
            }
            if (DEBUG_RENDER) timer.mark("itemViewVisibility");

            this.sku = jsonObject.optString(SearchJsonField._SKU);
            this.title = !jsonObject.isNull(SearchJsonField.WEEE_NAME) ? jsonObject.optString(SearchJsonField.WEEE_NAME, EmptyUtils.EMPTY_STRING) : EmptyUtils.EMPTY_STRING;
            if (DEBUG_RENDER) timer.mark("getSkuInfo");

            registerCache(this.sku, position);
            if (DEBUG_RENDER) timer.mark("registerCache");

            bindUIDebug(jsonObject, position);
            if (DEBUG_RENDER) timer.mark("bindUIDebug");

            String soldStatus = jsonObject.optString(SearchJsonField.WEEE_SOLD_STATUS);
            this.isCartControlsNotifyMeButton = Constants.ProductStatus.SOLD_OUT.equalsIgnoreCase(soldStatus);
            this.isCartControlsChangeDateAvailable = Constants.ProductStatus.CHANGE_OTHER_DAY.equalsIgnoreCase(soldStatus);
            this.isCartControlsPurchasedAvailable = isReachLimit(jsonObject);

            boolean isUsingProductViewLayout = isUsingProductViewLayout();
            if (!isUsingProductViewLayout) {
                final boolean isSponsored = jsonObject.optBoolean(SearchJsonField._IS_SPONSORED, false);

                ViewTools.setViewVisible(this.sposoredLabelTextView, isSponsored);

                final boolean isFreshly = jsonObject.optBoolean(SearchJsonField._IS_FRESHLY);

                boolean isBtnNotifyVisible = false;
                boolean isBtnChangeDateVisible = false;
                boolean isBtnPurchasedVisible = false;

                bindCartControls(jsonObject, position);
                if (DEBUG_RENDER) timer.mark("bindCartControls");

                isBtnNotifyVisible = this.bindNotifyMeButton();
                if (isBtnNotifyVisible) {
                    if (DEBUG_RENDER) timer.mark("bindNotifyMeButton");
                }

                isBtnChangeDateVisible = this.bindChangeDateButton();
                if (isBtnChangeDateVisible) {
                    if (DEBUG_RENDER) timer.mark("bindChangeDateButton");
                }

                isBtnPurchasedVisible = this.bindPurchasedButton();
                if (isBtnPurchasedVisible) {
                    if (DEBUG_RENDER) timer.mark("bindPurchasedButton");
                }

                final boolean shouldShowAsOutOfStock = shouldShowAsOutOfStock();

                if (this.imageView != null) {
                    bindImage(jsonObject, position);
                    if (DEBUG_RENDER) timer.mark("bindImage");
                }

                if (this.titleView != null) {
                    bindTitle();
                    if (DEBUG_RENDER) timer.mark("bindTitle");
                }

                if (this.unitPriceView != null) {
                    bindUnitPrice(jsonObject);
                    if (DEBUG_RENDER) timer.mark("bindUnitPrice");
                }

                if (this.brandView != null) {
                    if (isFreshly && !ENABLED_SHIPS_FROM_FOR_BRAND) {
                        bindBrand(jsonObject);
                    } else {
                        this.brandView.setVisibility(View.GONE);
                    }
                    if (DEBUG_RENDER) timer.mark("bindBrandV2-isFreshDaily:" + isFreshly);
                }

                if (this.priceView != null) {
                    if (shouldShowAsOutOfStock) {
                        final int hiddenVisibility = isSecondarySectionAdapter() ? View.INVISIBLE : View.GONE;

                        this.priceView.setVisibility(hiddenVisibility);
                        if (this.priceStrikedView != null) {
                            final int priceStrikedHiddenVisibility = getPriceStrikedHiddenVisibility();
                            this.priceStrikedView.setVisibility(priceStrikedHiddenVisibility);
                        }

                        if (this.volumePriceRegularView != null) {
                            this.volumePriceRegularView.setVisibility(hiddenVisibility);
                        }

                        if (this.volumePriceRegularViewStriked != null) {
                            this.volumePriceRegularViewStriked.setVisibility(hiddenVisibility);
                        }

                        if (this.volumePriceContainerView != null) {
                            this.volumePriceContainerView.setVisibility(hiddenVisibility);
                        }
                    } else {
                        bindPrice(jsonObject);
                    }
                    if (DEBUG_RENDER) timer.mark("bindPrice");
                }

                if (this.badgesContainerView != null || this.topRankingBadgeView != null || this.discountPercentageTag != null) {
                    if (shouldShowAsOutOfStock) {
                        if (this.badgesContainerView != null) {
                            this.badgesContainerView.setVisibility(View.GONE);
                        }

                        if (this.topRankingBadgeView != null) {
                            this.topRankingBadgeView.setVisibility(View.GONE);
                        }

                        if (this.discountPercentageTag != null) {
                            this.discountPercentageTag.setVisibility(View.GONE);
                        }
                    } else {
                        bindBadges(position, this.sku, jsonObject, onItemClickListener);
                    }
                    if (DEBUG_RENDER) timer.mark("bindBadges");
                }

                if (this.soldAmountView != null) {
                    if (shouldShowAsOutOfStock) {
                        if (this.soldAmountView != null) {
                            this.soldAmountView.setVisibility(View.GONE);
                        }
                    } else {
                        bindSoldAmount(jsonObject);
                    }
                    if (DEBUG_RENDER) timer.mark("bindSoldAmount");
                }


                if (this.outOfStockView != null) {
                    bindOutOfStock();
                    if (DEBUG_RENDER) timer.mark("bindOutOfStock");
                }

                if (this.remainingTipView != null) {
                    if (shouldShowAsOutOfStock) {
                        this.remainingTipView.setVisibility(View.GONE);
                    } else {
                        bindRemainingTip(jsonObject);
                    }
                    if (DEBUG_RENDER) timer.mark("bindRemainingTip");
                }

                if (this.shipsFromView != null) {
                    bindShipsFrom(position, jsonObject, isFreshly, onItemClickListener);
                    if (DEBUG_RENDER) timer.mark("bindShipsFrom");
                }

                if (contentTextViewsSpacer != null) {
                    if (shouldShowInfoLabelsSpacer()) {
                        contentTextViewsSpacer.setVisibility(View.VISIBLE);
                    } else {
                        contentTextViewsSpacer.setVisibility(View.GONE);
                    }
                }

                bindRelatedProducts(jsonObject, position, onItemClickListener);

                if (onItemClickListener != null) {
                    if (isBtnNotifyVisible) {
                        this.btnNotifyMeContainer.setOnClickListener(v -> onNotifyMeButtonClick(jsonObject, position, onItemClickListener));
                    }

                    if (isBtnChangeDateVisible) {
                        this.btnChangeDateContainer.setOnClickListener(v -> onChangeDateButtonClick(jsonObject, position, onItemClickListener));
                    }

                    if (DEBUG_RENDER) timer.mark("setOnClickListeners");
                }
            }

            if (SearchResultsFragmentV2.ENABLE_SEARCH_RESULTS_ADS_TRACK_AT_BIND) {
                SearchResultsV2StreamAdapter adapter = getSearchResultsBaseListAdapter();
                if (adapter != null) adapter.trackAdsEventIfNeeded(AdsManager.TRACK_VIEW_URL, jsonObject, position);
            }
        }
        catch (Exception e) {
            if (timer != null) timer.mark("ERROR-" + e.getMessage());

            if (DevConfig.isDebug()) {
                Logger.e(TAG, "[bind:productViewBase:"+timer.getTotalTime()+"ms] pos:" + position + " sku:" + this.sku + " ERROR timer:", timer, e);
            }
        }
        finally {
            if (DEBUG_RENDER) {
                Logger.d(TAG, "[bind:productViewBase:"+timer.getTotalTime()+"ms] pos:" + position + " isSecondary:" + isSecondarySectionAdapter() + " sku:" + this.sku + " timer:", timer);
            }
        }
    }

    protected int getPriceStrikedHiddenVisibility() {
        return isSecondarySectionAdapter() ? View.INVISIBLE : View.GONE;
    }

    private boolean shouldShowInfoLabelsSpacer() {
        final boolean topInfo = (priceView != null && priceView.getVisibility() == View.VISIBLE)
                || (outOfStockView != null && outOfStockView.getVisibility() == View.VISIBLE)
                || (volumePriceContainerView != null && volumePriceContainerView.getVisibility() == View.VISIBLE);
        final boolean bottomInfo = (shipsFromView != null && shipsFromView.getVisibility() == View.VISIBLE)
                || (discountPercentageTag != null && discountPercentageTag.getVisibility() == View.VISIBLE)
                || (soldAmountView != null && soldAmountView.getVisibility() == View.VISIBLE)
                || (shipsFromEtaView != null && shipsFromEtaView.getVisibility() == View.VISIBLE);
        return topInfo && bottomInfo;
    }

    private void bindBadges(int position, String sku, JSONObject jsonObject, BaseListAdapter.OnItemClickListener onItemClickListener) {
        if (this.badgesContainerView == null && this.discountPercentageTag == null) return;

        DebugTimer debugTimer = DEBUG_RENDER ? new DebugTimer(false) : null;
        JSONObject cachedBadgesMap = getCachedBadgesMap(jsonObject);
        if (debugTimer != null) debugTimer.mark("getCachedBadgesMap");

        // setup discount badge
        if (discountPercentageTag != null) {
            String discountBadgeLabel = null;
            if (!Constants.SearchV2.SHOW_DISCOUNT_PERCENTAGE_ONLY_FROM_LABEL_LIST) {
                final int discountPercentage = jsonObject.optInt(SearchJsonField.WEEE_DISCOUNT_PERCENTAGE);
                discountBadgeLabel = discountPercentage > 0 ? getContext().getString(R.string.s_percent_off, discountPercentage) : null;
            }
            if (EmptyUtils.isEmpty(discountBadgeLabel)) {
                JSONObject discountBadgeObj = cachedBadgesMap.optJSONObject(SearchJsonField.LABEL_PREFIX + SearchJsonField.Values.WEEE_LABEL_OFF);
                discountBadgeLabel = discountBadgeObj != null ? discountBadgeObj.optString(SearchJsonField._LABEL) : null;
            }
            if (!EmptyUtils.isEmpty(discountBadgeLabel)) {
                discountPercentageTag.setText(discountBadgeLabel);
                discountPercentageTag.setVisibility(View.VISIBLE);
            } else {
                discountPercentageTag.setVisibility(View.GONE);
            }
        }

        if (cachedBadgesMap != null && cachedBadgesMap.length() > 0) {
            // top ranking badge
            if (this.topRankingBadgeView != null) {
                JSONObject topRankingBadgeObj = cachedBadgesMap.optJSONObject(SearchJsonField.Values.WEEE_TAG_TOP_RANKING_KEY);
                if (topRankingBadgeObj != null) {
                    topRankingBadgeView.setText(topRankingBadgeObj.optString(SearchJsonField._LABEL));
                    topRankingBadgeViewTouchFeedback.setOnClickListener(v -> {
                        onBadgeClick(jsonObject, position, onItemClickListener, topRankingBadgeObj);
                    });
                    topRankingBadgeView.setVisibility(View.VISIBLE);
                } else {
                    topRankingBadgeViewTouchFeedback.setOnClickListener(null);
                    topRankingBadgeView.stopMarquee();
                    topRankingBadgeView.setVisibility(View.GONE);
                }
            }

            /*if (this.topRankingBadgeView != null && this.topRankingBadgeTextView != null) {
                topRankingBadgeTextView.setSelected(false);
                if (topRankingBadgeViewStartMarqueeTask != null) {
                    topRankingBadgeTextView.removeCallbacks(topRankingBadgeViewStartMarqueeTask);
                }

                JSONObject topRankingBadgeObj = cachedBadgesMap.optJSONObject(JsonField.Values.WEEE_TAG_TOP_RANKING_KEY);
                if (topRankingBadgeObj != null) {
                    topRankingBadgeView.setTag(topRankingBadgeObj.optString(JsonField._KEY));
                    topRankingBadgeViewStartMarqueeTask = () -> {
                        topRankingBadgeViewStartMarqueeTask = null;
                        //topRankingBadgeTextView.setSelected(true);
                    };
                    topRankingBadgeTextView.postDelayed(topRankingBadgeViewStartMarqueeTask, 3000);
                    topRankingBadgeTextView.setText(topRankingBadgeObj.optString(JsonField._LABEL));
                    topRankingBadgeView.setVisibility(View.VISIBLE);
                    hasBadges = true;
                } else {
                    //topRankingBadgeTextView.setSelected(false);
                    topRankingBadgeView.setTag(null);
                    topRankingBadgeView.setVisibility(View.GONE);
                }
            }*/

            // badges container setup
            if (this.badgesContainerView != null) {
                boolean hasBadges = false;

                // remove all attached badges views
                this.badgesContainerView.removeAllViews();

                // add badges views ordered by data
                JSONArray list = cachedBadgesMap.optJSONArray(SearchJsonField._LIST);
                if (list != null) {
                    int len = list.length();
                    for (int i = 0; i < len; i++) {
                        JSONObject badgeObj = list.optJSONObject(i);

                        String k = badgeObj.optString(SearchJsonField._KEY);
                        if (EmptyUtils.isEmpty(k) || k.equals(SearchJsonField.LABEL_PREFIX + SearchJsonField.Values.WEEE_LABEL_OFF)) {
                            continue;
                        }

                        View badgeView = badgesViews.get(k);
                        if (debugTimer != null) debugTimer.mark("findViewWithTag-" + k);

                        badgeView = setupBadgeView(badgesContainerView, k, badgeView, badgeObj, debugTimer);
                        final boolean isSetupBadgeView = badgeView != null;
                        if (debugTimer != null) debugTimer.mark("setupBadgeView-2");

                        if (isSetupBadgeView) {
                            hasBadges = true;

                            if (!badgesViews.containsKey(k)) {
                                badgesViews.put(k, badgeView);
                            }

                            SearchV2FlowLayout.LayoutParams params = new SearchV2FlowLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                            params.gravity = Gravity.START;
                            if (DIMEN_BADGES_CONTAINER_SPACING == null) {
                                DIMEN_BADGES_CONTAINER_SPACING = getContext().getResources().getDimensionPixelSize(R.dimen.search_result_row_v2_badge_spacing);
                            }
                            params.rightMargin = DIMEN_BADGES_CONTAINER_SPACING;
                            params.topMargin = DIMEN_BADGES_CONTAINER_SPACING;
                            badgesContainerView.addView(badgeView, params);
                            if (debugTimer != null) debugTimer.mark("addView");
                        }

                        if (SearchJsonField.Values.WEEE_TAG_PROMO_KEY.equals(k)) {
                            startPromoTagMarquee(3000);
                        }
                    }
                }

                int visibility = hasBadges ? View.VISIBLE : View.GONE;
                this.badgesContainerView.setVisibility(visibility);
            }

            if (DEBUG_RENDER && debugTimer != null) {
                Logger.d(TAG, "[bind:badges:"+debugTimer.getTotalTime()+"ms] " + sku + " timer:", debugTimer);
            }
        }
        else {
            if (this.badgesContainerView != null) {
                this.badgesContainerView.setVisibility(View.GONE);
            }

            if (topRankingBadgeView != null) {
                topRankingBadgeViewTouchFeedback.setOnClickListener(null);
                topRankingBadgeView.stopMarquee();
                topRankingBadgeView.setVisibility(View.GONE);
            }

            stopPromoTagMarquee();

            if (this.discountPercentageTag != null) {
                this.discountPercentageTag.setVisibility(View.GONE);
            }
        }
    }

    public void startPromoTagMarquee(int delayMillis) {
        if (promoTagView == null) return;
        if (this.promoTagViewStartMarqueeTask == null) {
            this.promoTagViewStartMarqueeTask = () -> {
                if (DeviceUtils.isSlowPerformanceDevice(getContext())) {
                    promoTagView.setHorizontalFadingEdgeEnabled(false);
                    promoTagView.setFadingEdgeLength(0);
                }
                promoTagView.setSelected(true);
            };
        }
        // prevent marquee restart on another views layout change: register
        promoTagView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right,
                                       int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                ViewGroup.LayoutParams params = v.getLayoutParams();
                params.width = right - left;
                params.height = bottom - top;
                v.removeOnLayoutChangeListener(this);
                v.setLayoutParams(params);
            }
        });
        promoTagView.postDelayed(promoTagViewStartMarqueeTask, delayMillis);
    }

    public void stopPromoTagMarquee() {
        if (promoTagView == null) return;
        if (promoTagViewStartMarqueeTask != null) {
            promoTagView.removeCallbacks(promoTagViewStartMarqueeTask);
            this.promoTagViewStartMarqueeTask = null;
            // prevent marquee restart on another views layout change: unregister
            ViewGroup.LayoutParams params = promoTagView.getLayoutParams();
            params.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            promoTagView.setLayoutParams(params);
        }
        promoTagView.setSelected(false);
    }

    private View setupBadgeView(ViewGroup badgesContainerView,
                                   String tagKey,
                                   View iBadgeView,
                                   JSONObject badgeJsonObj,
                                   DebugTimer debugTimer) {
        if (badgesContainerView == null) return null;
        Context context = badgesContainerView.getContext();

        String iconUrl = badgeJsonObj.optString(SearchJsonField._ICON_URL);
        String tailIconUrl = badgeJsonObj.optString(SearchJsonField._TAIL_ICON_URL);
        final boolean hasIcon = !EmptyUtils.isEmpty(iconUrl) || !EmptyUtils.isEmpty(tailIconUrl);

        View rootView = null;
        TextView textView = null;
        CustomImageView iconView = null;
        CustomImageView tailIconView = null;
        if (iBadgeView == null) {
            boolean isWeeeRewardsBadges = tagKey.startsWith(SearchJsonField.Values.WEEE_TAG_REWARDS_BADGES_KEY);
            boolean isWeeeTopRankingBadge = tagKey.startsWith(SearchJsonField.Values.WEEE_TAG_TOP_RANKING_KEY);
            if (isWeeeTopRankingBadge) {
                return null;
            }
            else if (isWeeeRewardsBadges) {
                int type = WeeeRewardsBadgeView.getTypeFromTagKey(tagKey);
                if (type <= 0) return null;
                WeeeRewardsBadgeView weeeRewardsBadgeView = new WeeeRewardsBadgeView(context, type);
                if (debugTimer != null) debugTimer.mark("inflate");

                rootView = weeeRewardsBadgeView;
                textView = weeeRewardsBadgeView.getTextView();
            }
            else {
                int textColor = Color.WHITE;
                int bgColor = Color.LTGRAY;

                try {
                    final String hexFontColor = badgeJsonObj.optString(SearchJsonField._FONT_COLOR, null);
                    if (!EmptyUtils.isEmpty(hexFontColor)) {
                        textColor = Color.parseColor(hexFontColor);
                    }
                } catch(Exception e) {
                    if (DevConfig.isDebug()) {
                        Logger.e(TAG, "setupBadgeView error: "+e.getMessage(), e);
                    }
                }

                try {
                    final String hexBgColor = badgeJsonObj.optString(SearchJsonField._COLOR, null);
                    if (!EmptyUtils.isEmpty(hexBgColor)) {
                        bgColor = Color.parseColor(hexBgColor);
                    }
                } catch (Throwable e) {
                    if (DevConfig.isDebug()) {
                        Logger.e(TAG, "setupBadgeView ERROR " + e.getMessage(), e);
                    }
                }

                if (hasIcon) {
                    TextIconsBadgeView textIconsBadgeView = new TextIconsBadgeView(getContext());
                    rootView = textIconsBadgeView;
                    textView = textIconsBadgeView.getTextView();
                    iconView = textIconsBadgeView.getIconView();
                    tailIconView = textIconsBadgeView.getTailIconView();
                } else {
                    rootView = LayoutInflater.from(context).inflate(
                            R.layout.search_v2_main_section_product_row_badge_text_view,
                            this.badgesContainerView,
                            false
                    );
                    textView = (TextView) rootView;
                }
                if (debugTimer != null) debugTimer.mark("inflate");

                if (textView != null) textView.setTextColor(textColor);
                if (debugTimer != null) debugTimer.mark("setTextColor");

                Drawable wrappedDrawable = null;
                try {
                    final Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.search_result_product_v2_tag_discount_bg).mutate();
                    wrappedDrawable = DrawableCompat.wrap(drawable);
                    DrawableCompat.setTint(wrappedDrawable, bgColor);
                    if (debugTimer != null) debugTimer.mark("DrawableCompat");
                } catch (Throwable e) {
                    if (DevConfig.isDebug()) {
                        Logger.e(TAG, "setupBadgeView ERROR " + e.getMessage(), e);
                    }
                }
                rootView.setBackground(wrappedDrawable);
            }

            rootView.setTag(tagKey);
            iBadgeView = rootView;
        }
        else if (iBadgeView instanceof TextView) {
            textView = (TextView) iBadgeView;
            rootView = textView;
        }
        // right now is just pantry plus - we're not showing the global+ on search v2 view holder
        else if (iBadgeView instanceof CustomRemoteUrlBadgeView) {
            ((CustomRemoteUrlBadgeView) iBadgeView).loadPantryPlusBadge();
            rootView = iBadgeView;
        }
        else if (iBadgeView instanceof CustomBadgeView) {
            CustomBadgeView customBadgeView = (CustomBadgeView) iBadgeView;
            rootView = customBadgeView.getContainerView();
            textView = customBadgeView.getTextView();

            if (iBadgeView instanceof TextIconsBadgeView) {
                TextIconsBadgeView textIconsBadgeView = (TextIconsBadgeView) iBadgeView;
                iconView = textIconsBadgeView.getIconView();
                tailIconView = textIconsBadgeView.getTailIconView();
            }
        }

        if (textView != null) {
            String label = badgeJsonObj.optString(SearchJsonField._LABEL);
            if (!EmptyUtils.isEmpty(label)) {
                if (isSecondarySectionAdapter() && textView.getEllipsize() != TextUtils.TruncateAt.MARQUEE) {
                    textView.setEllipsize(TextUtils.TruncateAt.END);
                    textView.setLines(1);
                }
                textView.setText(label);
            }
        }

        if (CUSTOM_BADGE_ICON_SIZE == null && (iconView != null || tailIconView != null)) {
            CUSTOM_BADGE_ICON_SIZE = ScreenUtils.dp2px(getContext(), 16);
        }
        if (iconView != null) {
            if (!EmptyUtils.isEmpty(iconUrl)) {
                int imageFlags = CustomImageView.getFlags(iconUrl);
                iconView.load(iconUrl, CUSTOM_BADGE_ICON_SIZE, CUSTOM_BADGE_ICON_SIZE, null, imageFlags, null, null).show();
                iconView.setVisibility(View.VISIBLE);
            } else {
                iconView.setVisibility(View.GONE);
            }
        }
        if (tailIconView != null) {
            if (!EmptyUtils.isEmpty(tailIconUrl)) {
                int imageFlags = CustomImageView.getFlags(tailIconUrl);
                tailIconView.load(tailIconUrl, CUSTOM_BADGE_ICON_SIZE, CUSTOM_BADGE_ICON_SIZE, null, imageFlags, null, null).show();
                tailIconView.setVisibility(View.VISIBLE);
            } else {
                tailIconView.setVisibility(View.GONE);
            }
        }

        if (rootView != null) {
            rootView.setVisibility(View.VISIBLE);
        }

        return iBadgeView;
    }

    public static JSONObject getCachedBadgesMap(JSONObject convertedPricedProduct) {
        if (Constants.SearchV2.ENABLE_MAIN_SECTION_PRODUCT_VIEW_LAYOUT && Constants.SearchV2.USE_HORIZONTAL_SECTION_PRODUCT_VIEW_LAYOUT) {
            // this is necessary here to avoid breaking the tags and labels
            return JsonUtils.EMPTY_JSON_OBJECT;
        }

        JSONObject result = convertedPricedProduct.optJSONObject(SearchJsonField._CACHE_BADGES_MAPS);
        if (result != null) return result;

        try {
            result = new JSONObject();
            convertedPricedProduct.put(SearchJsonField._CACHE_BADGES_MAPS, result);

            JSONArray cachedList = new JSONArray();
            result.put(SearchJsonField._LIST, cachedList);

            // tags
            JSONArray productTagList = convertedPricedProduct.optJSONArray(SearchJsonField.WEEE_PRODUCT_TAG_LIST);
            JSONObject entrangeTag = convertedPricedProduct.optJSONObject(SearchJsonField.WEEE_ENTRANCE_TAG);
            if (entrangeTag != null) {
                if (productTagList == null) productTagList = new JSONArray();
                productTagList.put(entrangeTag);
            }
            if (productTagList != null) {
                int productTagListLength = productTagList.length();
                for (int i = 0; i < productTagListLength; i++) {
                    JSONObject productTag = productTagList.optJSONObject(i);
                    if (productTag != null) {
                        String key = !productTag.isNull(SearchJsonField.WEEE_TAG_KEY) ? productTag.optString(SearchJsonField.WEEE_TAG_KEY) : null;
                        String type = !productTag.isNull(SearchJsonField.WEEE_TAG_TYPE) ? productTag.optString(SearchJsonField.WEEE_TAG_TYPE) : null;
                        if (SearchJsonField.Values.WEEE_TAG_TYPE_PROMO.equals(type)) key = SearchJsonField.Values.WEEE_TAG_PROMO;
                        if (SearchJsonField.Values.WEEE_TAG_TYPE_TOP_RANKING.equals(type) || SearchJsonField.Values.WEEE_TAG_TYPE_MKPL_TOP_RANKING.equals(type)) key = SearchJsonField.Values.WEEE_TAG_TOP_RANKING;

                        String name = !productTag.isNull(SearchJsonField.WEEE_TAG_NAME) ? productTag.optString(SearchJsonField.WEEE_TAG_NAME) : null;
                        if (!SearchJsonField.Values.WEEE_TAG_TYPE_TOP_RANKING.equals(type)) name = name.toUpperCase();

                        String bgColor = !productTag.isNull(SearchJsonField.WEEE_TAG_COLOR) ? productTag.optString(SearchJsonField.WEEE_TAG_COLOR, null) : null;
                        String fontColor = !productTag.isNull(SearchJsonField.WEEE_TAG_FONT_COLOR) ? productTag.optString(SearchJsonField.WEEE_TAG_FONT_COLOR, null) : null;
                        String iconUrl = !productTag.isNull(SearchJsonField.WEEE_TAG_ICON_URL) ? productTag.optString(SearchJsonField.WEEE_TAG_ICON_URL, null) : null;
                        String tailIconUrl = !productTag.isNull(SearchJsonField.WEEE_TAIL_ICON_URL) ? productTag.optString(SearchJsonField.WEEE_TAIL_ICON_URL, null) : null;
                        if (!EmptyUtils.isEmpty(key) && !EmptyUtils.isEmpty(name)) {
                            key = SearchJsonField.TAG_PREFIX + key;
                            productTag.put(SearchJsonField._KEY, key);
                            productTag.put(SearchJsonField._LABEL, name);
                            if (bgColor != null)
                                productTag.put(SearchJsonField._COLOR, bgColor);
                            if (fontColor != null)
                                productTag.put(SearchJsonField._FONT_COLOR, fontColor);
                            if (iconUrl != null)
                                productTag.put(SearchJsonField._ICON_URL, iconUrl);
                            if (tailIconUrl != null)
                                productTag.put(SearchJsonField._TAIL_ICON_URL, tailIconUrl);
                            result.put(key, productTag);
                            cachedList.put(productTag);
                        }
                    }
                }
            }

            // labels
            JSONArray labelList = convertedPricedProduct.optJSONArray(SearchJsonField.WEEE_LABEL_LIST);
            if (labelList != null) {
                int labelListLength = labelList.length();
                if (labelListLength > 0) {
                    JSONObject productLabel = labelList.optJSONObject(0);
                    if (productLabel != null) {
                        String key = !productLabel.isNull(SearchJsonField.WEEE_LABEL_KEY) ? productLabel.optString(SearchJsonField.WEEE_LABEL_KEY) : null;
                        String name = !productLabel.isNull(SearchJsonField.WEEE_LABEL_NAME) ? productLabel.optString(SearchJsonField.WEEE_LABEL_NAME).toUpperCase() : null;
                        String bgColor = !productLabel.isNull(SearchJsonField.WEEE_LABEL_COLOR) ? productLabel.optString(SearchJsonField.WEEE_LABEL_COLOR, null) : null;
                        String fontColor = !productLabel.isNull(SearchJsonField.WEEE_LABEL_FONT_COLOR) ? productLabel.optString(SearchJsonField.WEEE_LABEL_FONT_COLOR, null) : null;
                        String iconUrl = !productLabel.isNull(SearchJsonField.WEEE_LABEL_ICON_URL) ? productLabel.optString(SearchJsonField.WEEE_LABEL_ICON_URL, null) : null;
                        if (!EmptyUtils.isEmpty(key) && !EmptyUtils.isEmpty(name)) {
                            key = SearchJsonField.LABEL_PREFIX + key;
                            productLabel.put(SearchJsonField._KEY, key);
                            productLabel.put(SearchJsonField._LABEL, name);
                            if (bgColor != null)
                                productLabel.put(SearchJsonField._COLOR, bgColor);
                            if (fontColor != null)
                                productLabel.put(SearchJsonField._FONT_COLOR, fontColor);
                            if (iconUrl != null)
                                productLabel.put(SearchJsonField._ICON_URL, iconUrl);
                            result.put(key, productLabel);
                            cachedList.put(productLabel);
                        }
                    }
                }
            }

            // Pantry
            final boolean isPantry = convertedPricedProduct.optBoolean(SearchJsonField._IS_PANTRY);
            if (isPantry) {
                String k = SearchJsonField.Values.WEEE_TAG_PANTRY_KEY;
                String label = null;

                JSONObject obj = new JSONObject()
                        .put(SearchJsonField._KEY, k)
                        .put(SearchJsonField.KEY, k)
                        .put(SearchJsonField._LABEL, label);

                result.put(k, obj);

                JSONArray arr = result.optJSONArray(SearchJsonField._LIST);
                if (arr == null) {
                    arr = new JSONArray();
                    result.put(SearchJsonField._LIST, arr);
                }
                arr.put(obj);
            }

            // Cold pack
            final boolean isColdPack = convertedPricedProduct.optBoolean(SearchJsonField._IS_COLD_PACK);
            if (isColdPack) {
                String k = SearchJsonField.Values.WEEE_TAG_COLD_PACK_KEY;
                String label = null;

                JSONObject obj = new JSONObject()
                        .put(SearchJsonField._KEY, k)
                        .put(SearchJsonField.KEY, k)
                        .put(SearchJsonField._LABEL, label);

                result.put(k, obj);

                JSONArray arr = result.optJSONArray(SearchJsonField._LIST);
                if (arr == null) {
                    arr = new JSONArray();
                    result.put(SearchJsonField._LIST, arr);
                }
                arr.put(obj);
            }

            // Weee TAG Rewards
            JSONObject rewardsBadgeObj = result != null ? result.optJSONObject(SearchJsonField.Values.WEEE_TAG_REWARDS_BADGES_KEY) : null;
            final boolean isRewardsWeeklyPromotionActive = rewardsBadgeObj != null; //  && convertedPricedProduct.optBoolean(SearchJsonField.WEEE_MEMBER_SUPPORT);
            if (isRewardsWeeklyPromotionActive
                    && (!result.has(SearchJsonField.Values.WEEE_TAG_REWARDS_SILVER_BADGES_KEY)
                    && !result.has(SearchJsonField.Values.WEEE_TAG_REWARDS_GOLD_BADGES_KEY))
            ) {
                String k;
                String label = null;
                if (rewardsBadgeObj != null) {
                    final Integer level = UserPersonalizationManager.getLoyaltyLevel();

                    final String badgeName = !rewardsBadgeObj.isNull(SearchJsonField.WEEE_TAG_NAME) ? rewardsBadgeObj.optString(SearchJsonField.WEEE_TAG_NAME, null) : null;
                    if (level != null
                            && level > 1
                            && badgeName != null
                            && !badgeName.isEmpty()
                    ) {
                        label = badgeName;

                        if (!EmptyUtils.isEmpty(label)) {
                            if (level > 2) {
                                k = SearchJsonField.Values.WEEE_TAG_REWARDS_GOLD_BADGES_KEY;
                            } else {
                                k = SearchJsonField.Values.WEEE_TAG_REWARDS_SILVER_BADGES_KEY;
                            }

                            JSONObject obj = new JSONObject()
                                    .put(SearchJsonField._KEY, k)
                                    .put(SearchJsonField.KEY, k)
                                    .put(SearchJsonField._LABEL, label);

                            result.put(k, obj);

                            JSONArray arr = result.optJSONArray(SearchJsonField._LIST);
                            if (arr == null) {
                                arr = new JSONArray();
                                result.put(SearchJsonField._LIST, arr);
                            }

                            arr.put(obj);
                        }
                    }
                }
            }
        } catch (Throwable e) {
            if (DevConfig.isDebug()) {
                Logger.e(TAG, "getCachedBadgesMap ERROR", e);
            }
        }

        return result;
    }

    private void bindRemainingTip(JSONObject convertedPricedProduct) {
        if (this.remainingTipView != null) {
            if (isSecondarySectionAdapter()) {
                remainingTipView.setVisibility(View.GONE);
            }
            else {
                String remainingTipLabel = convertedPricedProduct.optString(SearchJsonField._REMAINING_TIP_LABEL, null);
                if (remainingTipLabel == null) {
                    int remainingCount;
                    boolean shoulsShowRemainingTip;
                    remainingCount = convertedPricedProduct.optInt(SearchJsonField.WEEE_REMAINING_COUNT);
                    int soldCount = convertedPricedProduct.optInt(SearchJsonField.WEEE_SOLD_COUNT);
                    shoulsShowRemainingTip = OrderHelper.isShowRemainingTip(remainingCount, soldCount);

                    if (shoulsShowRemainingTip && remainingCount > 0 && (MINIMUM_REMAINING_COUNT == 0 || remainingCount <= MINIMUM_REMAINING_COUNT)) {
                        remainingTipLabel = getContext().getString(R.string.s_remaining_tip_short, remainingCount);
                    }
                    else {
                        remainingTipLabel = EmptyUtils.EMPTY_STRING;
                    }

                    try {
                        convertedPricedProduct.put(SearchJsonField._REMAINING_TIP_LABEL, remainingTipLabel);
                    } catch (JSONException e) {
                        Logger.e(TAG, "bindRemainingTip error:"+e.getMessage());
                    }
                }

                if (remainingTipLabel != null && !remainingTipLabel.isEmpty()) {
                    remainingTipView.setText(remainingTipLabel);
                    remainingTipView.setVisibility(View.VISIBLE);
                } else {
                    remainingTipView.setVisibility(View.GONE);
                }
            }
        }
    }

    private void bindShipsFrom(int position, JSONObject convertedPricedProduct, boolean isFreshDaily, BaseListAdapter.OnItemClickListener onItemClickListener) {
        if (shipsFromView == null) return;

        try {
            String brandName = null;
            boolean shouldRenderBrand = ENABLED_SHIPS_FROM_FOR_BRAND && isFreshDaily;
            if (shouldRenderBrand) {
                brandName = !convertedPricedProduct.isNull(SearchJsonField.WEEE_BRAND_NAME) ? convertedPricedProduct.optString(SearchJsonField.WEEE_BRAND_NAME) : null;
                shouldRenderBrand = !EmptyUtils.isEmpty(brandName);
            }

            final String name = shouldRenderBrand
                    ? brandName
                    : convertedPricedProduct.optString(SearchJsonField._VENDOR_NAME, null);

            final boolean shouldShowShipsFrom = name != null && !name.isEmpty();
            if (shouldShowShipsFrom) {
                String shipsFromText = shouldRenderBrand
                        ? getContext().getString(R.string.s_from, name)
                        : convertedPricedProduct.optString(SearchJsonField._VENDOR_LABEL, null);

                if (ENABLED_SHIPS_FROM_CLICKABLE) {
                    Spanny shipsFromTextFormated = new Spanny(shipsFromText);

                    int brandIndex = shipsFromText.indexOf(name);
                    if (brandIndex >= 0) {
                        if (COLOR_SHIPS_FROM_NAME == null) {
                            COLOR_SHIPS_FROM_NAME = ContextCompat.getColor(getContext(), R.color.search_results_row_v2_ships_from_name_color);
                        }

                        ForegroundColorSpan colorSpan = new ForegroundColorSpan(COLOR_SHIPS_FROM_NAME);
                        shipsFromTextFormated.setSpan(colorSpan, brandIndex, brandIndex + name.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

                        StyleSpan boldSpan = new StyleSpan(Typeface.BOLD);
                        shipsFromTextFormated.setSpan(boldSpan, brandIndex, brandIndex + name.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }

                    shipsFromView.setOnClickListener((v) -> onShipsFromClick(convertedPricedProduct, position, onItemClickListener));
                    shipsFromView.setClickable(true);

                    shipsFromView.setText(shipsFromTextFormated);
                } else {
                    shipsFromView.setOnClickListener(null);
                    shipsFromView.setClickable(false);

                    shipsFromView.setText(shipsFromText);
                }

                shipsFromView.setVisibility(View.VISIBLE);
            }
            else {
                shipsFromView.setVisibility(View.GONE);
            }

            String shipsFromEta = convertedPricedProduct.optString(SearchJsonField._VENDOR_ETA);
            if (!EmptyUtils.isEmpty(shipsFromEta)) {
                Spanny shipsFromEtaFormated = null;

                if (!ENABLED_SHIPS_FROM_ETA_FULL_BOLD) {
                    Object cache = convertedPricedProduct.opt(SearchJsonField._VENDOR_ETA_SPANNABLE_CACHE);
                    shipsFromEtaFormated = cache instanceof Spanny ? (Spanny) cache : null;

                    if (shipsFromEtaFormated == null) {
                        int dateStartIdx = shipsFromEta.toLowerCase().indexOf(S_GET_IT);
                        if (dateStartIdx != -1) {
                            dateStartIdx += S_GET_IT.length() - 1;
                            String datePart = shipsFromEta.substring(dateStartIdx);

                            if (!EmptyUtils.isEmpty(datePart)) {
                                shipsFromEtaFormated = new Spanny(shipsFromEta);

                                int dateEndIdx = dateStartIdx + datePart.length();
                                if (COLOR_SHIPS_FROM_ETA == null) {
                                    COLOR_SHIPS_FROM_ETA = ContextCompat.getColor(getContext(), R.color.search_results_row_v2_ships_from_eta_color);
                                }

                                ForegroundColorSpan colorSpan = new ForegroundColorSpan(COLOR_SHIPS_FROM_ETA);
                                StyleSpan boldSpan = new StyleSpan(Typeface.BOLD);
                                shipsFromEtaFormated.setSpan(colorSpan, dateStartIdx, dateEndIdx, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                                shipsFromEtaFormated.setSpan(boldSpan, dateStartIdx, dateEndIdx, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                                JsonTools.putSafely(convertedPricedProduct, SearchJsonField._VENDOR_ETA_SPANNABLE_CACHE, shipsFromEtaFormated);
                            }
                        }
                    }
                }

                if (shipsFromEtaFormated != null) {
                    shipsFromEtaView.setText(shipsFromEtaFormated);
                } else {
                    shipsFromEtaView.setText(shipsFromEta);
                }
                shipsFromEtaView.setVisibility(View.VISIBLE);
            }
            else {
                shipsFromEtaView.setVisibility(View.GONE);
            }
        }
        catch (Exception e) {
            shipsFromView.setVisibility(View.GONE);
            if (DevConfig.isDebug()) {
                Logger.e(TAG, "bindShipsFrom ERROR", e);
            }
        }
    }

    @Override
    public void recycle(@NonNull RecyclerView.ViewHolder holder, int position, Map<String, Object> outState) {
        if (topRankingBadgeView != null) {
            topRankingBadgeView.stopMarquee();
        }
        /*if (topRankingBadgeViewStartMarqueeTask != null) {
            topRankingBadgeTextView.removeCallbacks(topRankingBadgeViewStartMarqueeTask);
        }*/

        if (this.imageView != null) {
            this.imageView.cancelAndClear();
        }

        stopPromoTagMarquee();

        unregisterFromCache(this.sku);
        super.recycle(holder, position, outState);
    }

    private void registerCache(String sku, int position) {
        DebugTimer timer = DEBUG_RENDER ? new DebugTimer(false) : null;
        SearchResultsFragmentV2 searchResultsFragmentV2 = this.getSearchResultsFragmentV2();
        if (DEBUG_RENDER) timer.mark("getSearchResultsFragment");
        if (searchResultsFragmentV2 != null) {
            SearchResultsV2StreamAdapter searchResultsBaseListAdapter = this.getSearchResultsBaseListAdapter();
            if (DEBUG_RENDER) timer.mark("getSearchResultsBaseListAdapter");
            if (searchResultsBaseListAdapter != null) {
                Map<String, Map<SearchResultsV2StreamAdapter, Integer>> cachedSkuPositions = searchResultsFragmentV2.getCachedSkuPositions();
                Map<SearchResultsV2StreamAdapter, Integer> cachedPosition = cachedSkuPositions != null ? cachedSkuPositions.get(sku) : null;
                if (DEBUG_RENDER) timer.mark("getSku");
                if (cachedPosition == null) {
                    cachedPosition = new HashMap<>();
                    cachedSkuPositions.put(sku, cachedPosition);
                    if (DEBUG_RENDER) timer.mark("putSku");
                }
                cachedPosition.put(searchResultsBaseListAdapter, position);
                if (DEBUG_RENDER) timer.mark("putPosition");
            }
        }
        if (DEBUG_RENDER) Logger.d(TAG, "registerCache: " + timer);
    }

    private void unregisterFromCache(String sku) {
        DebugTimer timer = DEBUG_RENDER ? new DebugTimer(false) : null;
        SearchResultsFragmentV2 searchResultsFragmentV2 = this.getSearchResultsFragmentV2();
        if (DEBUG_RENDER) timer.mark("getSearchResultsFragment");
        if (searchResultsFragmentV2 != null) {
            SearchResultsV2StreamAdapter searchResultsBaseListAdapter = this.getSearchResultsBaseListAdapter();
            if (DEBUG_RENDER) timer.mark("getSearchResultsBaseListAdapter");
            if (searchResultsBaseListAdapter != null) {
                Map<String, Map<SearchResultsV2StreamAdapter, Integer>> cachedSkuPositions = searchResultsFragmentV2.getCachedSkuPositions();
                Map<SearchResultsV2StreamAdapter, Integer> cachedPosition = cachedSkuPositions != null ? cachedSkuPositions.get(sku) : null;
                if (DEBUG_RENDER) timer.mark("getSku");
                if (cachedPosition != null) {
                    Integer position = cachedPosition.remove(searchResultsBaseListAdapter);
                    if (DEBUG_RENDER) timer.mark("removePosition");
                    if (position != null) {
                        if (cachedPosition.isEmpty()) {
                            cachedSkuPositions.remove(sku);
                            if (DEBUG_RENDER) timer.mark("removeSku");
                        }
                    }
                }
            }
        }
        if (DEBUG_RENDER) Logger.d(TAG, "unregisterFromCache: " + timer);
    }

    protected void onNotifyMeButtonClick(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener) {
        if (this.isCartControlsNotifyMeButton) {
            if (isBtnNotifyMeActiveState) {
                onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_GO_TO_NOTIFY_LIST, null);
            } else {
                onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_UPDATE_NOTIFY_LIST, null);
            }
        }
    }

    protected void onChangeDateButtonClick(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener) {
        if (this.isCartControlsChangeDateAvailable) {
            onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_CHANGE_DATE, null);
        }
    }

    protected void onShipsFromClick(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener) {
        onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_SHIPS_FROM, null);
    }

    protected void onBadgeClick(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener, JSONObject badgeData) {
        if (badgeData != null) {
            String link = badgeData.optString(SearchJsonField.LINK);
            if (EmptyUtils.isEmpty(link)) {
                link = badgeData.optString(SearchJsonField.MORE_LINK);
            }
            if (link != null && !link.isEmpty()) {
                onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_OPEN_LINK, link);
                return;
            }
        }

        onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_BADGE, badgeData);
    }

    private void bindRelatedProducts(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener) {
        if (relatedProductsButton == null) return;

        relatedProductsButton.setOnClickListener(v -> onRelatedProductsButtonClick(jsonObject, position, onItemClickListener));
        relatedProductsButton.setVisibility(View.VISIBLE);
    }

    protected void onRelatedProductsButtonClick(JSONObject jsonObject, int position, BaseListAdapter.OnItemClickListener onItemClickListener) {
        onItemClickListener.onItemClick(jsonObject, position, this, BaseListAdapter.CLICK_ACTION_RELATED_PRODUCTS_VIEW, null);
    }

    private boolean isReachLimit(JSONObject jsonObject) {
        String soldStatus = jsonObject.optString(SearchJsonField.WEEE_SOLD_STATUS);
        if (Constants.ProductStatus.REACH_LIMIT.equalsIgnoreCase(soldStatus)) {
            return true;
        }

        int productId = jsonObject.optInt(SearchJsonField.WEEE_ID);
        boolean isLimitProduct = jsonObject.optBoolean(SearchJsonField.WEEE_IS_LIMIT_PRODUCT, false);
        int minOrderQuantity = jsonObject.optInt(SearchJsonField.WEEE_MIN_ORDER_QUANTITY, 0);
        int maxOrderQuantity = jsonObject.optInt(SearchJsonField.WEEE_MAX_ORDER_QUANTITY, 0);
        int remainingCount = jsonObject.optInt(SearchJsonField.WEEE_REMAINING_COUNT, 0);
        boolean isPantry = jsonObject.optBoolean(SearchJsonField.WEEE_IS_PANTRY, false);

        int orderMaxQuantity = OrderManager.get().getOrderMaxQuantity(isLimitProduct, productId, isPantry, maxOrderQuantity, remainingCount);
        boolean isReachLimit = OrderManager.get().isReachLimit(soldStatus, isLimitProduct, orderMaxQuantity, minOrderQuantity);
        return isReachLimit;
    }

    public void bindCartControls(JSONObject convertedPricedProduct, int position) {
        if (cartControlsLayoutOp == null) return;

        boolean shouldShowCartControls = !isCartControlsNotifyMeButton && !isCartControlsChangeDateAvailable && !isCartControlsPurchasedAvailable;
        if (shouldShowCartControls) {
            if (!EmptyUtils.isEmpty(cartControlsLayoutOpSku) && cartControlsLayoutOpSku.equals(sku)) {
                ProductBean product = JsonTools.getProductBeanCached(getContext(), convertedPricedProduct);
                Integer cartItemQuantity = SearchPanelViewModel.getCartItemQuantity(sku);
                if (product != null && cartItemQuantity != null) {
                    int currentQuantity = cartItemQuantity;
                    product.setProductQuantity(currentQuantity);
                    if (cartControlsLayoutOp != null) {
                        cartControlsLayoutOp.setOpStyle(currentQuantity, product.min_order_quantity, product.getOrderMaxQuantity());
                        if (cartControlsLayoutOp.getOpStatus() != CartOpLayout.OP_STATUS_EXPAND) {
                            cartControlsLayoutOp.setOpNum(currentQuantity);
                        }
                    }
                }
                return;
            }

            cartControlsLayoutOpSku = sku;

            SearchResultsV2StreamAdapter adapter = getSearchResultsBaseListAdapter();
            SearchResultsFragmentV2 searchResultsFragmentV2 = adapter.getSearchResultsFragmentV2();

            String eagleTrackSource = adapter.getEagleTrackSource();
            String eagleTrackEventModNm = adapter.getEagleTrackEventModNm();

            String secNm = adapter.getEagleTrackSecNm();
            int secPos = adapter.getEagleTrackSecPos();

            Map<String, Object> context = searchResultsFragmentV2.getEagleTrackContext(adapter);
            Map<String, Object> element = new EagleTrackModel.Builder()
                    .setMod_nm(eagleTrackEventModNm)
                    .setMod_pos(1)
                    .setSec_nm(secNm)
                    .setSec_pos(secPos).build().getElement();

            ProductBean productBean = JsonTools.getProductBeanCached(getContext(), convertedPricedProduct);
            if (productBean != null) {
                //OpHelper.helperOp(cartControlsLayoutOp, productBean, productBean, eagleTrackSource, element, context);
                OpHelper.helperOpBySearchV2(cartControlsLayoutOp, productBean, productBean, eagleTrackSource, new OpLayout.OnExOperateListener() {
                    @Override
                    public void onNumClick(View view) {
                        if (searchResultsFragmentV2 == null) return;
                        searchResultsFragmentV2.setActiveRowViewHolder(self());
                    }

                    @Override
                    public void operateLeft(View view) {
                        onEdit(false);
                    }

                    @Override
                    public void operateRight(View view) {
                        onEdit(true);
                    }

                    private void onEdit(boolean isAdd) {
                        if (searchResultsFragmentV2 == null) return;
                        searchResultsFragmentV2.setActiveRowViewHolder(self());
                        searchResultsFragmentV2.onCartItemUpdated(
                                self().getSearchResultsBaseListAdapter(),
                                convertedPricedProduct,
                                position,
                                productBean,
                                isAdd);
                    }
                }, element, context);
            }

            cartControlsLayoutOp.setVisibility(productBean != null ? View.VISIBLE : View.GONE);
        } else {
            cartControlsLayoutOp.setVisibility(View.GONE);
        }
    }

    public boolean bindNotifyMeButton() {
        if (this.btnNotifyMeContainer == null) return false;

        boolean isBtnNotifyVisible = ENABLED_NEW_NOTIFY_ME_BUTTON_DESIGN
                && this.isCartControlsNotifyMeButton
                && this.btnNotifyMeContainer != null;

        if (!isBtnNotifyVisible) {
            this.btnNotifyMeContainer.setVisibility(View.GONE);
            return false;
        }

        final int productId = Integer.parseInt(this.sku);
        final boolean alreadyAtNotifyList = CollectManager.get().isProductCollect(productId);

        if (alreadyAtNotifyList && !isBtnNotifyMeActiveState) {
            btnNotifyMeContainer.setBackgroundResource(R.drawable.touch_feedback_search_results_status_button_active);

            if (btnNotifyMeContainerImageViewIcon != null) {
                if (BTN_NOTIFY_ME_BG_DRAWABLE_ACTIVE == null) {
                    BTN_NOTIFY_ME_BG_DRAWABLE_ACTIVE = ContextCompat.getDrawable(getContext(), R.drawable.ic_btn_notify_me_bell_active);
                }

                btnNotifyMeContainerImageViewIcon.setImageDrawable(BTN_NOTIFY_ME_BG_DRAWABLE_ACTIVE);
            }

            if (btnNotifyMeContainerTextView != null) {
                if (BTN_NOTIFY_ME_TEXT_COLOR_NORMAL == null) {
                    BTN_NOTIFY_ME_TEXT_COLOR_NORMAL = btnNotifyMeContainerTextView.getCurrentTextColor();
                }

                if (BTN_NOTIFY_ME_TEXT_COLOR_ACTIVE == null) {
                    BTN_NOTIFY_ME_TEXT_COLOR_ACTIVE = ContextCompat.getColor(getContext(), R.color.search_results_secondary_v2_btn_status_active);
                }

                btnNotifyMeContainerTextView.setTextColor(BTN_NOTIFY_ME_TEXT_COLOR_ACTIVE);
                btnNotifyMeContainerTextView.setText(getContext().getString(R.string.s_notification_set));
            }

            isBtnNotifyMeActiveState = true;
        }
        else if (!alreadyAtNotifyList && isBtnNotifyMeActiveState) {
            btnNotifyMeContainer.setBackgroundResource(R.drawable.touch_feedback_search_results_status_button_normal);

            if (btnNotifyMeContainerImageViewIcon != null) {
                if (BTN_NOTIFY_ME_BG_DRAWABLE_NORMAL == null) {
                    BTN_NOTIFY_ME_BG_DRAWABLE_NORMAL = ContextCompat.getDrawable(getContext(), R.drawable.ic_btn_notify_me_bell_normal);
                }

                btnNotifyMeContainerImageViewIcon.setImageDrawable(BTN_NOTIFY_ME_BG_DRAWABLE_NORMAL);
            }

            if (btnNotifyMeContainerTextView != null) {
                btnNotifyMeContainerTextView.setTextColor(BTN_NOTIFY_ME_TEXT_COLOR_NORMAL);
                btnNotifyMeContainerTextView.setText(getContext().getString(R.string.s_notify_me));
            }

            isBtnNotifyMeActiveState = false;
        }

        if (this.btnNotifyMeContainer.getVisibility() != View.VISIBLE) {
            this.btnNotifyMeContainer.setVisibility(View.VISIBLE);
        }

        return true;
    }

    private boolean bindChangeDateButton() {
        if (this.btnChangeDateContainer == null) return false;

        boolean isBtnChangeDateVisible = ENABLED_NEW_CHANGE_DATE_BUTTON_DESIGN
                && this.isCartControlsChangeDateAvailable
                && this.btnChangeDateContainer != null;

        if (!isBtnChangeDateVisible) {
            this.btnChangeDateContainer.setVisibility(View.GONE);
            return false;
        }

        if (this.btnChangeDateContainer.getVisibility() != View.VISIBLE) {
            this.btnChangeDateContainer.setVisibility(View.VISIBLE);
        }

        return true;
    }

    private boolean bindPurchasedButton() {
        if (this.btnPurchasedContainer == null) return false;

        boolean isBtnPurchasedVisible = ENABLED_NEW_PURCHASED_BUTTON_DESIGN
                && this.isCartControlsPurchasedAvailable
                && this.btnPurchasedContainer != null;

        if (!isBtnPurchasedVisible) {
            this.btnPurchasedContainer.setVisibility(View.GONE);
            return false;
        }

        if (this.btnPurchasedContainer.getVisibility() != View.VISIBLE) {
            this.btnPurchasedContainer.setVisibility(View.VISIBLE);
        }

        return true;
    }

    protected void bindTitle() {
        if (this.titleView == null) return;

        this.titleView.setText(title);

        if (shouldShowAsOutOfStock()) {
            if (COLOR_TITLE_OSS == null) {
                COLOR_TITLE_OSS = ContextCompat.getColor(getContext(), R.color.search_results_row_v2_oss_title);
            }

            if (this.originalTitleViewTextColor == null) {
                this.originalTitleViewTextColor = this.titleView.getCurrentTextColor();
            }

            if (this.titleView.getCurrentTextColor() != COLOR_TITLE_OSS) {
                this.titleView.setTextColor(COLOR_TITLE_OSS);
            }
        }
        else if (originalTitleViewTextColor != null) {
            this.titleView.setTextColor(this.originalTitleViewTextColor);
            this.originalTitleViewTextColor = null;
        }
    }

    protected void bindUnitPrice(JSONObject convertedPricedProduct) {
        if (this.unitPriceView == null) return;

        final String unitPrice = !convertedPricedProduct.isNull(SearchJsonField.WEEE_UNIT_PRICE) ? convertedPricedProduct.optString(SearchJsonField.WEEE_UNIT_PRICE, EmptyUtils.EMPTY_STRING) : null;

        if (!EmptyUtils.isEmpty(unitPrice)) {
            this.unitPriceView.setText(unitPrice);
            this.unitPriceView.setVisibility(View.VISIBLE);
        } else {
            this.unitPriceView.setVisibility(View.GONE);
        }
    }

    private void setCartControlsShadowStyle(CardView view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // change shadow color and opacity
            if (COLOR_SHADOW == null) {
                COLOR_SHADOW = ContextCompat.getColor(view.getContext(), R.color.search_result_product_cart_controls_shadow);
            }
            final int shadowColorValue = COLOR_SHADOW;
            view.setOutlineAmbientShadowColor(shadowColorValue);
            view.setOutlineSpotShadowColor(shadowColorValue);
            //view.setCardElevation(getContext().getResources().getDimensionPixelSize(R.dimen.search_results_product_cart_controls_elevation));
        } else {
            // use half elevation because cannot change shadow color and opacity
            if (DIMEN_CARD_ELEVATION == null) {
                DIMEN_CARD_ELEVATION = getContext().getResources().getDimensionPixelSize(R.dimen.search_results_product_cart_controls_elevation) / 2f;
            }
            view.setCardElevation(DIMEN_CARD_ELEVATION);
        }
    }

    protected void bindBrand(JSONObject convertedPricedProduct) {
        if (this.brandView == null) return;

        if (isSecondarySectionAdapter()) {
            this.brandView.setVisibility(View.GONE);
        }
        else {
            String brand = !convertedPricedProduct.isNull(SearchJsonField.WEEE_BRAND_NAME) ? convertedPricedProduct.optString(SearchJsonField.WEEE_BRAND_NAME) : null;
            if (!EmptyUtils.isEmpty(brand)) {
                this.brandView.setText(brand);
                this.brandView.setVisibility(View.VISIBLE);
            }
            else {
                this.brandView.setVisibility(View.GONE);
            }
        }
    }

    protected void bindPrice(JSONObject convertedPricedProduct) {
        if (this.priceView == null) return;

        String priceLabel = convertedPricedProduct.optString(SearchJsonField._PRICE_LABEL, null);
        if (priceLabel == null) {
            setupPriceLabel(itemView.getContext(), convertedPricedProduct);
            priceLabel = convertedPricedProduct.optString(SearchJsonField._PRICE_LABEL, null);
            if (priceLabel == null) {
                try {
                    convertedPricedProduct.put(SearchJsonField._PRICE_LABEL, EmptyUtils.EMPTY_STRING);
                } catch (Exception e) {
                    if (DevConfig.isDebug()) {
                        Logger.e("bindPrice ERROR on convertedPricedProduct.put(JsonField._PRICE_LABEL, EmptyUtils.EMPTY_STRING)", e);
                    }
                }
            }
        }

        final int hiddenVisibility = isSecondarySectionAdapter() ? View.INVISIBLE : View.GONE;

        String volumePriceBreakdownLabel = convertedPricedProduct.optString(SearchJsonField._VOLUME_PRICE_BREAKDOWN_LABEL, null);
        boolean everydayLowPrice = convertedPricedProduct.optBoolean(SearchJsonField.WEEE_EVERYDAY_LOW_PRICE);
        if (this.volumePriceContainerView != null && (everydayLowPrice || volumePriceBreakdownLabel != null)) {
            if (this.priceView != null) this.priceView.setVisibility(hiddenVisibility);
            if (this.priceStrikedView != null) {
                final int priceStrikedHiddenVisibility = getPriceStrikedHiddenVisibility();
                this.priceStrikedView.setVisibility(priceStrikedHiddenVisibility);
            }

            this.volumePriceValueView.setText(priceLabel);
            this.volumePriceValueView.requestLayout();

            if (everydayLowPrice) {
                if (EVERYDAY_VALUE_LABEL == null) {
                    EVERYDAY_VALUE_LABEL = getContext().getString(R.string.s_everyday_value);
                }
                if (this.everydayValueTextView != null) this.everydayValueTextView.setText(EVERYDAY_VALUE_LABEL);
                if (this.volumePriceRegularView != null) this.volumePriceRegularView.setVisibility(hiddenVisibility);
                if (this.volumePriceThresholdView != null) volumePriceThresholdView.setVisibility(View.GONE);
            } else {
                if (this.volumePriceThresholdView != null) {
                    this.volumePriceThresholdView.setText(volumePriceBreakdownLabel);
                    this.volumePriceThresholdView.requestLayout();
                    this.volumePriceThresholdView.setVisibility(View.VISIBLE);
                }

                if (volumePriceRegularView != null) {
                    String volumePriceRegularLabel = convertedPricedProduct.optString(SearchJsonField._VOLUME_PRICE_REGULAR_LABEL, null);
                    this.volumePriceRegularView.setText(volumePriceRegularLabel);
                    this.volumePriceRegularView.setVisibility(View.VISIBLE);
                }

                if (this.volumePriceRegularViewStriked != null) {
                    String fullPriceStriked = convertedPricedProduct.optString(SearchJsonField._VOLUME_PRICE_REGULAR_LABEL_STRIKED, null);
                    if (fullPriceStriked != null) {
                        this.volumePriceRegularViewStriked.setText(fullPriceStriked);
                        ViewTools.setViewVisibilityIfChanged(this.volumePriceRegularViewStriked, View.VISIBLE);
                    }
                    else {
                        ViewTools.setViewVisibilityIfChanged(this.volumePriceRegularViewStriked, View.GONE);
                    }
                }
            }

            if (this.everydayValueDividerView!= null) this.everydayValueDividerView.setVisibility(everydayLowPrice ? View.VISIBLE : View.GONE);
            if (this.everydayValueTextView != null) this.everydayValueTextView.setVisibility(everydayLowPrice ? View.VISIBLE : View.GONE);

            this.volumePriceContainerView.setVisibility(View.VISIBLE);
            this.volumePriceContainerView.requestLayout();
        }
        else {
            if (this.volumePriceContainerView != null) this.volumePriceContainerView.setVisibility(hiddenVisibility);
            if (this.volumePriceRegularView != null) this.volumePriceRegularView.setVisibility(hiddenVisibility);
            if (this.volumePriceRegularViewStriked != null) this.volumePriceRegularViewStriked.setVisibility(hiddenVisibility);

            String priceStrikethroughLabel = null;
            if (priceLabel != null && !priceLabel.isEmpty()) {
                if (this.priceView != null) {
                    this.priceView.setText(priceLabel);
                    this.priceView.setVisibility(View.VISIBLE);
                }
                if (this.priceContainer != null) {
                    ViewTools.setViewVisibilityIfChanged(this.priceContainer, View.VISIBLE);
                }
                else {
                    ViewTools.setViewVisibilityIfChanged(this.priceView, View.VISIBLE);
                }

                if (this.priceStrikedView != null) {
                    if (priceStrikethroughLabel == null) {
                        priceStrikethroughLabel = convertedPricedProduct.optString(SearchJsonField._PRICE_STRIKETHROUGH_LABEL, null);
                    }

                    if (priceStrikethroughLabel != null && !priceStrikethroughLabel.isEmpty()) {
                        this.priceStrikedView.setText(priceStrikethroughLabel);
                        this.priceStrikedView.setVisibility(View.VISIBLE);
                    }
                    else {
                        final int priceStrikedHiddenVisibility = getPriceStrikedHiddenVisibility();
                        this.priceStrikedView.setVisibility(priceStrikedHiddenVisibility);
                    }
                }
            }
            else {
                if (this.priceContainer != null) {
                    this.priceContainer.setVisibility(hiddenVisibility);
                }
                else {
                    if (this.priceView.getVisibility() != hiddenVisibility) {
                        this.priceView.setVisibility(hiddenVisibility);
                    }

                    final int priceStrikedHiddenVisibility = getPriceStrikedHiddenVisibility();
                    if (this.priceStrikedView != null && this.priceStrikedView.getVisibility() != priceStrikedHiddenVisibility) {
                        this.priceStrikedView.setVisibility(priceStrikedHiddenVisibility);
                    }
                }
            }
        }
    }

    public static void setupPriceLabel(Context context, JSONObject convertedPricedProduct) {
        try {
            double price = convertedPricedProduct.optDouble(SearchJsonField.PRICE, 0);

            final double volumePrice = convertedPricedProduct.optDouble(SearchJsonField.WEEE_VOLUME_PRICE);
            final int volumePriceThreshold = convertedPricedProduct.optInt(SearchJsonField.WEEE_VOLUME_THRESHOLD);
            final boolean volumePriceSupport = convertedPricedProduct.optBoolean(SearchJsonField.WEEE_VOLUME_PRICE_SUPPORT);

            final double fullPrice = convertedPricedProduct.optDouble(SearchJsonField.WEEE_BASE_PRICE, -1);

            if (volumePriceSupport) {
                String priceLabel = "$" + DecimalTools.formatCurrency(price);
                String volumePriceLabel = "$" + DecimalTools.formatCurrency(volumePrice);

                String volumePriceBreakdownLabel = context.getString(R.string.s_volume_threshold_simple, volumePriceThreshold);
                String volumePriceRegularLabel = context.getString(R.string.s_volume_threshold_one_qty, volumePriceLabel);

                if (fullPrice != -1 && fullPrice > volumePrice) {
                    String fullPriceLabel = "$" + DecimalTools.formatCurrency(fullPrice);
                    if (!fullPriceLabel.equals(volumePriceLabel)) {
                        convertedPricedProduct.put(SearchJsonField._VOLUME_PRICE_REGULAR_LABEL_STRIKED, fullPriceLabel);
                    }
                }

                convertedPricedProduct.put(SearchJsonField._PRICE_LABEL, priceLabel);
                convertedPricedProduct.put(SearchJsonField._VOLUME_PRICE_BREAKDOWN_LABEL, volumePriceBreakdownLabel);
                convertedPricedProduct.put(SearchJsonField._VOLUME_PRICE_REGULAR_LABEL, volumePriceRegularLabel);
            }
            else if (price > 0) {
                String priceLabel = "$" + DecimalTools.formatCurrency(price);

                String priceStrikethroughLabel = convertedPricedProduct.optString(SearchJsonField._PRICE_STRIKETHROUGH_LABEL, null);
                if (priceStrikethroughLabel == null) {
                    if (fullPrice > 0 && price != fullPrice) {
                        priceStrikethroughLabel = "$" + DecimalTools.formatCurrency(fullPrice);
                    }
                    else {
                        priceStrikethroughLabel = EmptyUtils.EMPTY_STRING;
                    }

                    convertedPricedProduct.put(SearchJsonField._PRICE_STRIKETHROUGH_LABEL, priceStrikethroughLabel);
                }

                convertedPricedProduct.put(SearchJsonField._PRICE_LABEL, priceLabel);
            }
        }
        catch (Exception e) {
            if (DevConfig.isDebug()) {
                Logger.e(TAG, "bindPrice setupPriceLabel ERROR", e);
            }
        }
    }

    public static int getImagePadding(Context context) {
        if (CACHE_IMAGE_SPECIAL_PADDING == null) {
            CACHE_IMAGE_SPECIAL_PADDING = context.getResources().getDimensionPixelSize(R.dimen.search_result_row_v3_image_special_padding);
        }
        return CACHE_IMAGE_SPECIAL_PADDING;
    }

    protected void bindImage(JSONObject jsonObject, int position) {
        if (this.imageView == null) return;

        final boolean isOutOfStock = shouldShowAsOutOfStock();
        SearchResultsV2StreamAdapter searchResultsBaseListAdapter = getSearchResultsBaseListAdapter();
        final boolean shouldUseImageSizeWithoutPadding = searchResultsBaseListAdapter == null || searchResultsBaseListAdapter
                .shouldUseImageSizeWithoutPadding(jsonObject);

        if (isOutOfStock) {
            this.imageView.setCustomAlpha(0.7f);
        }
        else {
            this.imageView.setCustomAlpha(null);
        }

        if (Constants.SearchV2.DEBUG_B64_PLACEHOLDER) {
            this.imageView.removeCallbacks(null);
        }
        this.imageView.cancelAndClear();

        final Integer productImageSize = this.getCachedImageSize(shouldUseImageSizeWithoutPadding);
        if (productImageSize != null) {
            final String imageUrl = jsonObject.optString(SearchJsonField._IMAGE_URL, null);
            if (!EmptyUtils.isEmpty(imageUrl)) {
                int imageFlags = jsonObject.optInt(SearchJsonField._IMAGE_FLAGS, 0);
                String filter = getImageFilter(jsonObject);

                final String b64;
                if (Constants.SearchV2.USE_B64_PLACEHOLDER && !isOutOfStock) {
                    b64 = !jsonObject.isNull(SearchJsonField.IMAGE_B64) ? jsonObject.optString(SearchJsonField.IMAGE_B64) : null;
                } else {
                    b64 = null;
                }

                if (Constants.SearchV2.DEBUG_B64_PLACEHOLDER) {
                    this.imageView.load(imageUrl, productImageSize, productImageSize, filter, imageFlags, null, b64);
                    this.imageView.postDelayed(imageView::show, 2000);
                }
                else {
                    this.imageView.load(imageUrl, productImageSize, productImageSize, filter, imageFlags, null, b64).show();
                }

                if (Constants.SearchV2.USE_PDP_PRELOADED_IMAGE_URL_CACHED) {
                    try {
                        jsonObject.put(SearchJsonField._PDP_PRELOADED_IMAGE_URL_CACHED, this.imageView.getCurrentPath());
                    } catch (Exception e) {
                        if (DevConfig.isDebug()) {
                            Logger.e(TAG, "bindImage: set image url cached ERROR", e);
                        }
                    }
                }

                this.imageView.setVisibility(View.VISIBLE);
            }
            else {
                this.imageView.setVisibility(View.GONE);
            }
        }
        else {
            this.imageView.setVisibility(View.GONE);
        }
    }

    private static String getImageFilter(JSONObject convertedPricedProduct) {
        final boolean isFreshly = convertedPricedProduct.optBoolean(SearchJsonField._IS_FRESHLY);
        String filter = isFreshly ? null : convertedPricedProduct.optString(SearchJsonField.IMAGE_FILTER, null);
        return filter;
    }

    public Integer getCachedImageSize(boolean withoutPadding) {
        if (this.imageSize == null) {
            SearchResultsV2StreamAdapter searchResultsBaseListAdapter = this.getSearchResultsBaseListAdapter();
            if (searchResultsBaseListAdapter != null) {
                this.imageSize = searchResultsBaseListAdapter.getImageSize(withoutPadding);
            }
        }
        return this.imageSize;
    }

    protected void bindSoldAmount(JSONObject convertedPricedProduct) {
        if (this.soldAmountView == null) return;

        if (isSecondarySectionAdapter()) {
            soldAmountView.setVisibility(View.GONE);
        }
        else {
            String lastWeekSoldRoundedString = convertedPricedProduct.optString(SearchJsonField._SOLD_COUNT_LABEL, null);
            boolean showSoldCount = convertedPricedProduct.optBoolean(SearchJsonField._SOLD_COUNT_VISIBLE);
            if (showSoldCount && lastWeekSoldRoundedString != null && !lastWeekSoldRoundedString.isEmpty()) {
                soldAmountView.setText(lastWeekSoldRoundedString);
                soldAmountView.setVisibility(View.VISIBLE);
            }
            else {
                if (isSecondarySectionAdapter()) {
                    soldAmountView.setVisibility(View.INVISIBLE);
                } else {
                    soldAmountView.setVisibility(View.GONE);
                }
            }
        }
    }

    private void bindOutOfStock() {
        if (this.outOfStockView != null) {
            final boolean shouldShowAsOutOfStock = shouldShowAsOutOfStock();
            this.outOfStockView.setVisibility(shouldShowAsOutOfStock ? View.VISIBLE : View.GONE);
        }
    }

    private boolean shouldShowAsOutOfStock() {
        return this.isCartControlsNotifyMeButton && !this.isCartControlsChangeDateAvailable && !this.isCartControlsPurchasedAvailable;
    }

    public static boolean isOutOfStock(JSONObject convertedPricedProduct) {
        if (convertedPricedProduct == null) return false;

        if (convertedPricedProduct.has(SearchJsonField._IS_OUT_OF_STOCK)) {
            return convertedPricedProduct.optBoolean(SearchJsonField._IS_OUT_OF_STOCK, false);
        }

        String soldStatus = convertedPricedProduct.optString(SearchJsonField.WEEE_SOLD_STATUS);
        boolean isOutOfStock = Constants.ProductStatus.SOLD_OUT.equalsIgnoreCase(soldStatus);

        try {
            convertedPricedProduct.put(SearchJsonField._IS_OUT_OF_STOCK, isOutOfStock);
        } catch (Exception e) {
            if (DevConfig.isDebug()) {
                Logger.e(TAG, "isOutOfStock check", e);
            }
        }

        return isOutOfStock;
    }

    public static void transformConvertedPricedProduct(Context context,
                                                       JSONObject convertedPricedProduct,
                                                       boolean isProductMainSection,
                                                       JSONObject mainResponseExtras,
                                                       JSONObject skuCreative,
                                                       boolean hasSectionTabs,
                                                       boolean renderHorizontal) {
        if (convertedPricedProduct == null) {
            return;
        }

        if (convertedPricedProduct.has(SearchJsonField._CACHE_CONVERTED_PRICED_PRODUCT_TRANSFORMED)) {
            return;
        } else {
            try {
                convertedPricedProduct.put(SearchJsonField._CACHE_CONVERTED_PRICED_PRODUCT_TRANSFORMED, true);
            } catch (Throwable ignore) { }
        }

        final DebugTimer debugTimer = DevConfig.isDebug() ? new DebugTimer() : null;

        String sku = null;

        try {
            final boolean isProductViewLayout = Constants.SearchV2.USE_HORIZONTAL_SECTION_PRODUCT_VIEW_LAYOUT && !isProductMainSection && renderHorizontal;

            sku = String.valueOf(convertedPricedProduct.optInt(SearchJsonField.WEEE_ID, 0));

            convertedPricedProduct.put(SearchJsonField.VIEW_TYPE, BaseListAdapter.ViewType.SEARCH_RESULTS_MAIN_SECTION_PRODUCT_ROW);

            if (!EmptyUtils.isEmpty(sku)) {
                convertedPricedProduct.put(SearchJsonField._SKU, sku);

                if (UserPersonalizationManager.get().isBuyAgain(sku)) {
                    JSONArray productTagList = convertedPricedProduct.optJSONArray(SearchJsonField.WEEE_PRODUCT_TAG_LIST);
                    if (productTagList == null) {
                        productTagList = new JSONArray();
                        convertedPricedProduct.put(SearchJsonField.WEEE_PRODUCT_TAG_LIST, productTagList);
                    }
                    JSONObject productTag = new JSONObject();
                    productTag.put(SearchJsonField.WEEE_TAG_KEY, SearchJsonField.Values.WEEE_TAG_BUY_AGAIN);
                    String tagName = context.getString(R.string.s_buy_it_again);
                    productTag.put(SearchJsonField.WEEE_TAG_NAME, isProductViewLayout ? tagName.toUpperCase() : tagName.toUpperCase());
                    int tagColor = context.getColor(R.color.search_results_row_v2_badge_buy_again_bg_color);
                    String tagColorHex = "#"+Integer.toHexString(tagColor);
                    productTag.put(SearchJsonField.WEEE_TAG_COLOR, tagColorHex);
                    int tagFontColor = context.getColor(R.color.search_results_row_v2_badge_buy_again_font_color);
                    String tagFontColorHex = "#"+Integer.toHexString(tagFontColor);
                    productTag.put(SearchJsonField.WEEE_TAG_FONT_COLOR, tagFontColorHex);
                    JsonUtils.insert(productTagList, 0, productTag);
                }
            }

            JSONObject venderInfoView = convertedPricedProduct.optJSONObject(SearchJsonField.WEEE_VENDER_INFO_VIEW);
            final int vendorId = venderInfoView != null && !venderInfoView.isNull(SearchJsonField.WEEE_VENDER_ID) ? venderInfoView.optInt(SearchJsonField.WEEE_VENDER_ID) : 0;
            final String vendorName = venderInfoView != null && !venderInfoView.isNull(SearchJsonField.WEEE_VENDER_NAME) ? venderInfoView.optString(SearchJsonField.WEEE_VENDER_NAME) : null;
            final String vendorLabel = venderInfoView != null && !venderInfoView.isNull(SearchJsonField.WEEE_VENDER_DELIVERY_DESC) ? StringUtils.stringAtSingleHtmlTag(venderInfoView.optString(SearchJsonField.WEEE_VENDER_DELIVERY_DESC)) : null;
            final String vendorEta = venderInfoView != null && !venderInfoView.isNull(SearchJsonField.WEEE_VENDER_ETA_RANGE) ? StringUtils.stringAtSingleHtmlTag(venderInfoView.optString(SearchJsonField.WEEE_VENDER_ETA_RANGE)) : null;
            if (vendorId > 0) {
                convertedPricedProduct.put(SearchJsonField._VENDOR_ID, vendorId);
                convertedPricedProduct.put(SearchJsonField._VENDOR_NAME, vendorName);
                convertedPricedProduct.put(SearchJsonField._VENDOR_LABEL, vendorLabel);
                convertedPricedProduct.put(SearchJsonField._VENDOR_ETA, vendorEta);
            }

            convertedPricedProduct.put(SearchJsonField._CACHE_PRODUCT_MAIN_RESULTS_SECTION, isProductMainSection);

            String t = !convertedPricedProduct.isNull(SearchJsonField.WEEE_NAME) ? convertedPricedProduct.optString(SearchJsonField.WEEE_NAME, null) : null;
            if (t != null) {
                convertedPricedProduct.put(SearchJsonField.WEEE_NAME, t.trim());
                if (debugTimer != null) debugTimer.mark("trimTitle");
            }

            String imageFilter = null;
            boolean hasImageFilter = false;
            // check for image filter
            if (mainResponseExtras != null) {
                JSONObject uiProps = mainResponseExtras.optJSONObject(SearchJsonField.UI_PROPS);
                if (uiProps != null) {
                    imageFilter = uiProps.optString(SearchJsonField.IMAGE_FILTER, null);
                    hasImageFilter = !EmptyUtils.isEmpty(imageFilter);
                    if (hasImageFilter) {
                        convertedPricedProduct.put(SearchJsonField.IMAGE_FILTER, imageFilter);
                    }
                }
            }

            if (hasImageFilter) {
                final String imageUrl = SearchV2Manager.get().getProductImageUrlBySku(sku);
                convertedPricedProduct.put(SearchJsonField._IMAGE_URL, imageUrl);
                convertedPricedProduct.put(SearchJsonField._IMAGE_FLAGS, CustomImageView.FLAG_IS_ANYCART_URL);
            }
            else {
                final String imageUrl = convertedPricedProduct.optString(SearchJsonField.IMG, null);
                convertedPricedProduct.put(SearchJsonField._IMAGE_URL, imageUrl);
                convertedPricedProduct.put(SearchJsonField._IMAGE_FLAGS, CustomImageView.FLAG_IS_WEEECDN_URL);
            }
            if (debugTimer != null) debugTimer.mark("imageFlags");

            if (!isProductViewLayout) {
                setupAmountSoldInfo(context, convertedPricedProduct);
                if (debugTimer != null) debugTimer.mark("setupAmountSoldInfo");
            }

            final boolean isPantry = convertedPricedProduct.optBoolean(SearchJsonField.WEEE_IS_PANTRY);
            convertedPricedProduct.put(SearchJsonField._IS_PANTRY, isPantry);

            final boolean isMarketplace = convertedPricedProduct.optJSONObject(SearchJsonField.WEEE_VENDER_INFO_VIEW) != null;
            convertedPricedProduct.put(SearchJsonField._IS_MARKETPLACE, isMarketplace);

            final boolean isColdPack = convertedPricedProduct.optBoolean(SearchJsonField.WEEE_IS_COLDING_PACKAGE);
            convertedPricedProduct.put(SearchJsonField._IS_COLD_PACK, isColdPack);

            if (skuCreative != null) {
                if (skuCreative.optBoolean(SearchJsonField._IS_SPONSORED)) {
                    convertedPricedProduct.put(SearchJsonField._IS_SPONSORED, true);
                }
                convertedPricedProduct.put(SearchJsonField.ADS_CREATIVE, skuCreative);
            }

            if (!isProductViewLayout) {
                JSONObject cachedBadgesMap = getCachedBadgesMap(convertedPricedProduct);
                if (debugTimer != null) debugTimer.mark("getCachedBadgesMap");
                final boolean isFreshly = cachedBadgesMap != null && cachedBadgesMap.has(SearchJsonField.TAG_PREFIX + SearchJsonField.Values.WEEE_TAG_FRESHLY);
                convertedPricedProduct.put(SearchJsonField._IS_FRESHLY, isFreshly);
            }

            // precache items
            if (!isProductViewLayout) {
                setupPriceLabel(context, convertedPricedProduct);
                if (debugTimer != null) debugTimer.mark("setupPriceLabel");
            }
            ProductBean productBeanCached = JsonTools.getProductBeanCached(context, convertedPricedProduct);
            if (debugTimer != null) debugTimer.mark("getProductBeanCached:"+(productBeanCached != null));
            if (isProductViewLayout && productBeanCached != null) {
                convertedPricedProduct.put(SearchJsonField._IS_PANTRY, productBeanCached.is_pantry);
                convertedPricedProduct.put(SearchJsonField._IS_MARKETPLACE, productBeanCached.isSeller());
            }
        }
        catch (Exception e) {
            if (DevConfig.isDebug()) {
                Logger.e(TAG, "transformConvertedPricedProduct ERROR", e);
            }
        }
        finally {
            if (DevConfig.isDebug()) {
                Logger.d(TAG, "transformConvertedPricedProduct DONE sku:" + sku + " timer:" + debugTimer);
            }
        }
    }

    private static void setupAmountSoldInfo(Context context, JSONObject convertedPricedProduct) throws JSONException {
        int remainingCount = convertedPricedProduct.optInt(SearchJsonField.WEEE_REMAINING_COUNT);
        int oneDaySoldCount = convertedPricedProduct.optInt(SearchJsonField.WEEE_ONE_DAY_SOLD_COUNT);
        int lastWeekSoldCount = convertedPricedProduct.optInt(SearchJsonField.WEEE_LAST_WEEK_SOLD_COUNT);
        String lastWeekSoldCountUI = !convertedPricedProduct.isNull(SearchJsonField.WEEE_LAST_WEEK_SOLD_COUNT_UI) ? convertedPricedProduct.optString(SearchJsonField.WEEE_LAST_WEEK_SOLD_COUNT_UI) : null;
        Boolean willSellOutSoon = null;

        int amountToUse = CONSIDER_ONE_DAY_SKU_SOLD_COUNT && oneDaySoldCount > lastWeekSoldCount
                ? oneDaySoldCount
                : lastWeekSoldCount;
        lastWeekSoldCount = amountToUse;

        final boolean shouldDisplaySoldCount = lastWeekSoldCount > MINIMUM_WEEKLY_SOLD || !EmptyUtils.isEmpty(lastWeekSoldCountUI);
        if (shouldDisplaySoldCount) {
            convertedPricedProduct.put(SearchJsonField.DISPLAY_LAST_WEEK_SOLD_COUNT, true);
        }

        if (EmptyUtils.isEmpty(lastWeekSoldCountUI)) {
            int lastWeekSoldRounded;
            if (lastWeekSoldCount >= 1000) {
                lastWeekSoldRounded = (lastWeekSoldCount / 1000) * 1000;
                lastWeekSoldCountUI = "1K+";
            } else if (lastWeekSoldCount > 100) {
                lastWeekSoldRounded = (lastWeekSoldCount / 100) * 100;
                lastWeekSoldCountUI = lastWeekSoldRounded + "+";
            } else {
                lastWeekSoldRounded = Math.max(10, (lastWeekSoldCount / 10) * 10);
                lastWeekSoldCountUI = lastWeekSoldRounded + "+";
            }
        }
        final String soldCountLabel = context.getString(R.string.s_search_sold, lastWeekSoldCountUI).toUpperCase();
        convertedPricedProduct.put(SearchJsonField._SOLD_COUNT_LABEL, soldCountLabel);

        if (willSellOutSoon == null) {
            willSellOutSoon = false;
            if (remainingCount > 0 && lastWeekSoldCount > 0) {
                if (remainingCount < MAXIMUM_REMAINING_ITEMS) {
                    double stockCoverageWeeks = (double) remainingCount / (double) lastWeekSoldCount;
                    if (
                            (lastWeekSoldCount >= WILL_SELL_OUT_SOON_WEEK_HIGH_VOLUME_LIMIT && stockCoverageWeeks <= WILL_SELL_OUT_SOON_WEEK_THRESHOLD_HIGH_VOLUME)
                                    || (lastWeekSoldCount >= WILL_SELL_OUT_SOON_WEEK_MEDIUM_VOLUME_LIMIT && stockCoverageWeeks <= WILL_SELL_OUT_SOON_WEEK_THRESHOLD_MEDIUM_VOLUME)
                                    || (stockCoverageWeeks <= WILL_SELL_OUT_SOON_WEEK_THRESHOLD_LOW_VOLUME)
                    ) {
                        willSellOutSoon = true;
                    }
                }
            }
        }
        convertedPricedProduct.put(SearchJsonField.WILL_SELL_OUT_SOON, willSellOutSoon);

        boolean showSoldCount = ((!willSellOutSoon && shouldDisplaySoldCount) || (willSellOutSoon && remainingCount >= WILL_SELL_OUT_SOON_CARD_INFO_MAX_WITHOUT_SOLD));
        convertedPricedProduct.put(SearchJsonField._SOLD_COUNT_VISIBLE, showSoldCount);
    }

    /* Only for develop / debug tests  */
    protected void bindUIDebug(JSONObject jsonObject, int position) {
        if (DevConfig.isDebug() && SearchResultsV2StreamAdapter.UI_DEBUG_TYPE != SearchResultsV2StreamAdapter.UI_DEBUG_OFF && this.uiDebugTextView != null) {
            if (SearchResultsV2StreamAdapter.UI_DEBUG_TYPE == SearchResultsV2StreamAdapter.UI_DEBUG_SKU_AND_POSITION) {
                String positionString = sku + " / " + String.valueOf(position);
                int productIndex = jsonObject.optInt(SearchJsonField._PRODUCT_INDEX, -1);
                if (productIndex >= 0) {
                    positionString = positionString + " (" + productIndex + ")";
                }
                if (!positionString.equals(this.uiDebugTextView.getText())) {
                    this.uiDebugTextView.setText(positionString);
                    this.uiDebugTextView.setVisibility(View.VISIBLE);
                }
            }

            if (SearchResultsV2StreamAdapter.UI_DEBUG_TYPE == SearchResultsV2StreamAdapter.UI_DEBUG_CHECK_WRONG_STOCK) {
                this.uiDebugTextView.setVisibility(View.GONE);

                final boolean isOutOfStockState = shouldShowAsOutOfStock();
                getProductDetail(sku, (productDetail) -> {
                    Boolean isSoldOut = productDetail != null && productDetail.product != null ? productDetail.product.isSoldOut() : null;
                    if (isSoldOut != null && isOutOfStockState != isSoldOut) {
                        final String positionString = "!!! WRONG SOLD OUT !!! " + sku + " / " + String.valueOf(position);
                        if (!positionString.equals(this.uiDebugTextView)) {
                            this.uiDebugTextView.setText(positionString);
                            this.uiDebugTextView.setVisibility(View.VISIBLE);
                        }
                    }
                });
            }
        }
    }

    /* Only for develop / debug tests  */
    private void getProductDetail(String sku, Consumer<ProductDetailBean> callback) {
        if (!DevConfig.isDebug()) return;

        final int productId = Integer.valueOf(sku);
        RetrofitIml.get().getHttpService(OrderApi.class)
                .getProductDetail(productId, null, null)
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseBean<ProductDetailBean>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(ResponseBean<ProductDetailBean> productDetailBeanResponseBean) {
                        try {
                            ProductDetailBean product = productDetailBeanResponseBean != null ? productDetailBeanResponseBean.getData() : null;
                            if (callback != null) callback.accept(product);
                        } catch (Exception e) {
                            onError(e);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        try {
                            if (callback != null) callback.accept(null);
                        } catch (Exception ignored) { }
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    public void collapseCartControlButtonIfNeeded() {
        if (cartControlsLayoutOp != null) {
            cartControlsLayoutOp.collapseWithAnim();
        }
    }

    @NonNull
    @Override
    public String toString() {
        return super.toString() + " " + getSkuDescription();
    }

    public String getSkuDescription() {
        StringBuilder sb = new StringBuilder("sku:");
        sb.append(sku);
        if (!EmptyUtils.isEmpty(title)) sb.append(":'").append(title).append("'");
        return sb.toString();
    }

    public static void trackSearchV2EventIfNeeded(SearchResultsV2StreamAdapter adapter, String eventTypeClick, JSONObject jsonObject, int position, Integer newQuantity) {
        try {
            if (adapter == null) return;

            SearchV2TrackingManager trackingManager = adapter.getSearchV2TrackingManager();
            if (trackingManager != null) {
                SearchV2TrackingManager.Event event = adapter.createSearchV2Event(
                        eventTypeClick,
                        jsonObject,
                        position
                );
                event.setItemQty(newQuantity);
                trackingManager.enqueueEvent(event);
            }
        }
        catch (Exception e) {
            if (DEBUG_VERBOSE) {
                Logger.e(TAG, e);
            }
        }
    }
}
