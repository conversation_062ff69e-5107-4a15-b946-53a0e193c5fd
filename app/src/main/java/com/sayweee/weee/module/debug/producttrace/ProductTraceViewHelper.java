package com.sayweee.weee.module.debug.producttrace;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.style.ReplacementSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.module.debug.producttrace.bean.ProductTraceBean;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceData;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTracePayload;
import com.sayweee.weee.module.debug.producttrace.ui.ProductTraceView;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.utils.Spanny;

import java.util.List;

public class ProductTraceViewHelper {

    private static final int VIEW_ID = R.id.id_view_product_sales_trace;
    private static final String FORMAT_SPLIT = "###";
    private static final int FORMAT_SPLIT_LENGTH = FORMAT_SPLIT.length();

    public static void convert(View parentView, ProductTraceKey.Provider traceKeyProvider) {
        help(parentView, traceKeyProvider.getProductTraceKey());
    }

    public static void convert(View parentView, ProductTraceKey traceKey) {
        help(parentView, traceKey);
    }

    public static boolean shouldConvertPayload(Object payload) {
        return payload instanceof ProductTracePayload;
    }

    private static void help(View parentView, ProductTraceKey traceKey) {
        if (parentView == null || parentView.getVisibility() != View.VISIBLE) {
            return;
        }
        ViewGroup parentViewGroup = null;
        if (parentView instanceof ViewGroup) {
            parentViewGroup = (ViewGroup) parentView;
        }
        if (parentViewGroup == null) {
            return;
        }

        View traceView = findTraceView(parentViewGroup);
        if (ProductTraceManager.get().isEnabled() && ProductTraceManager.get().isRunning()) {
            ProductTraceData trace = ProductTraceManager.get().getTrace(traceKey);
            if (trace != null) {
                ProductTraceView view = getOrAddTraceView(parentViewGroup, traceView);
                showTraceView(parentViewGroup, view, trace);
            } else {
                hideTraceView(traceView);
            }
        } else {
            hideTraceView(traceView);
        }
    }

    @Nullable
    public static View findTraceView(@NonNull View parentView) {
        return parentView.findViewById(VIEW_ID);
    }

    private static ProductTraceView getOrAddTraceView(@NonNull ViewGroup parentView, @Nullable View traceView) {
        if (traceView instanceof ProductTraceView) {
            return (ProductTraceView) traceView;
        }
        ProductTraceView child = new ProductTraceView(parentView.getContext());
        ViewGroup.LayoutParams innerLp = createLayoutParamsForParent(parentView);
        child.setId(VIEW_ID);
        if (innerLp != null) {
            parentView.addView(child, innerLp);
        } else {
            parentView.addView(child);
        }
        return child;
    }

    @Nullable
    private static ViewGroup.LayoutParams createLayoutParamsForParent(@NonNull ViewGroup parentView) {
        ViewGroup.LayoutParams parentLp = parentView.getLayoutParams();
        if (parentLp instanceof FrameLayout.LayoutParams) {
            FrameLayout.LayoutParams plp = (FrameLayout.LayoutParams) parentLp;
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT
            );
            lp.setMargins(plp.leftMargin, plp.topMargin, plp.rightMargin, plp.bottomMargin);
            return lp;
        }
        return null;
    }

    private static void showTraceView(@NonNull ViewGroup parentView, @NonNull ProductTraceView traceView, @NonNull ProductTraceData trace) {
        traceView.setMaxHeight(parentView.getHeight());
        traceView.bindData(ProductTraceManager.get().getTraceDomain(), trace);
        traceView.setVisibility(View.VISIBLE);
    }

    private static void hideTraceView(@Nullable View traceView) {
        if (traceView == null || traceView.getVisibility() != View.VISIBLE) {
            return;
        }
        ViewTools.setViewVisibilityIfChanged(traceView, View.GONE);
    }

    public static CharSequence formatUndefinedDomain(Context context, String key, int status) {
        Spanny cs = new Spanny();
        formatTitle(context, cs, "NODATA:" + key, status);
        return cs;
    }

    static CharSequence formatTraceData(@NonNull ProductTraceBean.TraceData data, int status) {
        Context context = LifecycleProvider.get().getApplication();
        Spanny cs = new Spanny();
        formatTitle(context, cs, data.title, status);
        formatTrace(context, cs, data.value);
        return cs;
    }

    private static void formatTrace(Context context, @NonNull Spanny cs, @Nullable List<String> lines) {
        if (lines == null || lines.isEmpty()) {
            return;
        }
        for (String line : lines) {
            if (line == null || line.isEmpty()) {
                continue;
            }
            if (cs.length() != 0) {
                cs.append("\n");
            }
            formatTraceLine(context, cs, line);
        }
    }

    private static void formatTitle(Context context, @NonNull Spanny spanny, @NonNull String title, int type) {
        int fgColorRes = R.color.color_product_trace_title;
        int bgColorRes;
        if (type == 1) {
            bgColorRes = R.color.color_product_trace_title_level_1;
        } else if (type == 2) {
            bgColorRes = R.color.color_product_trace_title_level_2;
        } else {
            bgColorRes = R.color.color_product_trace_title_level_3;
        }
        Object backgroundSpan = new TitleBackgroundSpan(
                /* backgroundColor= */ContextCompat.getColor(context, bgColorRes),
                /* textColor= */ContextCompat.getColor(context, fgColorRes),
                /* cornerRadius= */CommonTools.dp2px(4),
                /* paddingHorizontal= */CommonTools.dp2px(4),
                /* paddingVertical= */CommonTools.dp2px(2)
        );
        spanny.append(
                title,
                backgroundSpan,
                Spans.textAppearanceSpan(context, R.style.style_body_3xs_strong)
        );
    }

    private static void formatTraceLine(Context context, @NonNull Spanny spanny, @NonNull String line) {
        int index = line.lastIndexOf(FORMAT_SPLIT);
        String text;
        Object[] spans = null;
        if (index != -1) {
            text = line.substring(0, index);
            String format = line.substring(index + FORMAT_SPLIT_LENGTH).trim();
            switch (format) {
                case "h1":
                    spans = new Object[]{
                            Spans.foregroundColorSpan(context, R.color.color_product_trace_content_h1),
                            Spans.textAppearanceSpan(context, R.style.style_body_3xs_medium)
                    };
                    break;
                case "h2":
                    spans = new Object[]{
                            Spans.foregroundColorSpan(context, R.color.color_product_trace_content_h2),
                            Spans.textAppearanceSpan(context, R.style.style_body_3xs)
                    };
                    break;
                case "h3":
                    spans = new Object[]{
                            Spans.foregroundColorSpan(context, R.color.color_product_trace_content_h3),
                            Spans.textAppearanceSpan(context, R.style.style_body_3xs)
                    };
                    break;
                default:
                    break;
            }
        } else {
            text = line;
        }
        if (spans != null) {
            spanny.append(text, spans);
        } else {
            spanny.append(text);
        }
    }

    public static void notify(@Nullable RecyclerView recyclerView) {
        if (recyclerView == null || !recyclerView.isAttachedToWindow()) {
            return;
        }
        final RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        if (adapter != null) {
            final Pair<Integer, Integer> range;
            range = findAllBindingPositionRange(recyclerView);
            if (range != null) {
                recyclerView.post(() -> {
                    adapter.notifyItemRangeChanged(
                            range.first,
                            range.second - range.first + 1,
                            new ProductTracePayload()
                    );
                });
            }
        }
    }

    @Nullable
    private static Pair<Integer, Integer> findAllBindingPositionRange(@NonNull RecyclerView recyclerView) {
        RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        if (adapter != null) {
            int itemCount = adapter.getItemCount();
            if (itemCount <= 0) {
                return null;
            }
            return new Pair<>(0, itemCount);
        }
        return null;
    }

    /**
     * Do not use this method.
     * Range error is possible, maybe fixed at future.
     */
    @SuppressWarnings("unused")
    @Nullable
    private static Pair<Integer, Integer> findChildrenBindingAdapterPositionRange(@NonNull RecyclerView recyclerView) {
        int first = RecyclerView.NO_POSITION;
        int last = RecyclerView.NO_POSITION;
        for (int i = 0, count = recyclerView.getChildCount(); i < count; i++) {
            RecyclerView.ViewHolder holder = recyclerView.getChildViewHolder(recyclerView.getChildAt(i));
            if (holder != null) {
                int position = holder.getBindingAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    if (first == RecyclerView.NO_POSITION && position > first) {
                        first = position;
                    } else if (position < first) {
                        first = position;
                    }
                    if (position > last) {
                        last = position;
                    }
                }
            }
        }
        if (first != RecyclerView.NO_POSITION && last != RecyclerView.NO_POSITION) {
            return new Pair<>(first, last);
        }
        return null;
    }


    private static class TitleBackgroundSpan extends ReplacementSpan {

        private final int backgroundColor;
        private final int textColor;
        private final float cornerRadius;
        private final float paddingHorizontal;
        private final float paddingVertical;

        private final RectF rectF = new RectF();

        public TitleBackgroundSpan(
                int backgroundColor,
                int textColor,
                float cornerRadius,
                float paddingHorizontal,
                float paddingVertical
        ) {
            this.backgroundColor = backgroundColor;
            this.textColor = textColor;
            this.cornerRadius = cornerRadius;
            this.paddingHorizontal = paddingHorizontal;
            this.paddingVertical = paddingVertical;
        }

        @Override
        public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
            float textWidth = paint.measureText(text.subSequence(start, end).toString());
            if (fm != null) {
                Paint.FontMetricsInt pfm = paint.getFontMetricsInt();
                int height = pfm.descent - pfm.ascent;
                fm.ascent = pfm.ascent - (int) paddingVertical;
                fm.descent = pfm.descent + (int) paddingVertical;
                fm.top = pfm.top - (int) paddingVertical;
                fm.bottom = pfm.bottom + (int) paddingVertical;
            }
            return (int) (textWidth + 2 * paddingHorizontal);
        }

        @Override
        public void draw(Canvas canvas, CharSequence text, int start, int end,
                         float x, int top, int y, int bottom, Paint paint) {
            String textToDraw = text.subSequence(start, end).toString();
            float textWidth = paint.measureText(textToDraw);

            Paint.FontMetricsInt fm = paint.getFontMetricsInt();
            rectF.left = x;
            rectF.top = y + fm.ascent - paddingVertical;
            rectF.right = x + textWidth + 2 * paddingHorizontal;
            rectF.bottom = y + fm.descent + paddingVertical;

            int oldColor = paint.getColor();
            paint.setColor(backgroundColor);
            canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint);

            paint.setColor(textColor);
            canvas.drawText(textToDraw, x + paddingHorizontal, y, paint);

            paint.setColor(oldColor);
        }
    }
}
