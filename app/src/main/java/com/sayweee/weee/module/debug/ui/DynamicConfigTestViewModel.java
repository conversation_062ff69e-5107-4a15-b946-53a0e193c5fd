package com.sayweee.weee.module.debug.ui;

import android.app.Application;

import androidx.annotation.NonNull;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.web.bean.WebViewConfigBean;
import com.sayweee.weee.service.config.ConfigApi;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.ConfigContentBean;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/19.
 * Desc:
 */
public class DynamicConfigTestViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public DynamicConfigTestViewModel(@NonNull Application application) {
        super(application);
    }

    private void updateConfig(String key, String value, boolean status) {
        updateConfig(key, value);
    }

    private void updateConfig(String key, String value) {
        getLoader().createHttpService(ConfigApi.class)
                .updateConfig(new RequestParams()
                        .put("key", key)
                        .put("value", value)
//                        .put("status", status ? 1 : 0)
                        .put("version", "v2")
                        .put("desc", "")
                        .get())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        toast("配置已执行");
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        String message = failure.getMessage();
                        toast(message);
                    }
                });
    }

    private void addConfig(String key, String value) {
        getLoader().createHttpService(ConfigApi.class)
                .addConfig(new RequestParams().put("key", key).put("value", value).get())
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ViewModelResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {

                    }
                });
    }

    public void checkDynamicConfig() {
        getLoader().createHttpService(ConfigApi.class).getConfig()
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ViewModelResponseObserver<ResponseBean<ConfigContentBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ConfigContentBean> response) {

                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        String message = failure.getMessage();
                        toast(message);
                    }
                });
    }

    public void toast(String text) {
        if (text != null) {
            Toaster.showToast(text);
        }
    }

    public void updateCommunityConfig() {
        String key = "community";
        String value = "{\"video_pop_enable\": true}";
        updateConfig(key, value, true);
    }

    public void updateImageSearchConfig() {
        String key = "xp_product_search_by_image_enable";
        String value = "{\"id\": 293}";
        updateConfig(key, value, true);
    }

    public void updateMobileLiveConfig() {
        String key = "new_mobile_live";
        String value = "{\"enable\":true,\"height\":82,\"width\":82,\"support\":[\"zh\",\"zh-Hant\"],\"imageUrl\":\"https://img06.weeecdn.com/common/image/000/000/icon_live_v2.png\"}";
        updateConfig(key, value);
    }

    public void updateCategoryConfig() {
        String key = "category";
        String value = "{\"display_original_style\": false}";
        updateConfig(key, value, true);
    }

    public void setCategoryConfig() {
        String key = "category";
        String value = "{\"display_original_style\": true}";
        updateConfig(key, value, true);
    }

    public void setHomeConfig(boolean status) {
        String key = "home";
        String value;
        if (status) {
            value = "{\"display_original_style\": false}";
        } else {
            value = "{\"display_original_style\": true}";
        }
        value = "{\"top_bar\":{\"icon\":\"https://video.weeecdn.com/item/video/10-year-story-logo.gif\",\"link\":\"/external/10th-anniversary?fullscreen=1\"}}";
        updateConfig(key, value, true);
    }

    public void setCartConfig(boolean status) {
        String key = "cart";
        String value;
        if (status) {
            value = "{\"display_original_style_android\": false}";
        } else {
            value = "{\"display_original_style_android\": true}";
        }
        updateConfig(key, value);
    }

    public void setAddressPositioningConfig(boolean status) {
        String key = "address_positioning";
        String value;
        if (status) {
            value = "{" +
                    "\"home_positioning_enable\":true," +
                    "\"home_positioning_check_distance\":100," +
                    "\"address_positioning_enable\":true," +
                    "\"address_positioning_check_distance\":1" +
                    "}";
        } else {
            value = "{" +
                    "\"home_positioning_enable\":false," +
                    "\"home_positioning_check_distance\":100," +
                    "\"address_positioning_enable\":false," +
                    "\"address_positioning_check_distance\":1" +
                    "}";
        }
        updateConfig(key, value);
    }

    public void setDynamicIslandConfig(boolean status) {
        String key = "dynamic_island";
        String value;
        if (status) {
            value = "{\"auto_create_disable\": false}";
        } else {
            value = "{\"auto_create_disable\": true}";
        }
        updateConfig(key, value);
    }

    public void setClarityConfig(boolean status) {
        //默认开启状态，true时禁用
        String key = ConfigManager.DynamicConfig.CLARITY;
        String value;
        if (status) {
            value = "{\"android_enable\": true}";
        } else {
            value = "{\"android_enable\": false}";
        }
        updateConfig(key, value);
    }

    public void setBrandConfig(boolean status) {
        //默认开启状态，true时禁用
        String key = ConfigManager.DynamicConfig.BRAND;
        String value;
        if (status) {
            value = "{\"android_enable\": true}";
        } else {
            value = "{\"android_enable\": false}";
        }
        updateConfig(key, value);
    }

    public void setProductConfig(boolean status) {
        String key = ConfigManager.DynamicConfig.PRODUCT;
        String value;
        if (status) {
            value = "{\"display_original_pdp_style_android\": true}";
        } else {
            value = "{\"display_original_pdp_style_android\": false}";
        }
        updateConfig(key, value);
    }

    public void setMonitorConfig() {
        String key = ConfigManager.DynamicConfig.MONITOR;
        String value = "{\"ios_version\":\"18.9.1\", \"android_version\":\"18.4\", \"coefficient\":0.01}";
        updateConfig(key, value);
    }

    public void setBannerConfig() {
        String key = ConfigManager.DynamicConfig.BANNER;
        String value = "{\"loop_interval\":5}"; //此值若小于3s，则不生效
        updateConfig(key, value);
    }

    public void updateFailbackConfig() {
        String key = ConfigManager.DynamicConfig.FAILBACK_TO_H5 + "18.7";
        String value = "{\"patterns\":[]}";
//        String value = "{\"patterns\":[\"/product/.*/(([0-9]+)(\\\\?.+)?)$\", \"me\"]}";
//        String key = "failback_to_h5_ios-" + "19.4";
//        String value = "{\"patterns\":[\"https?://(.+/)+cms/page/activity\", \"https?://(.+/)+me\"]}";
        updateConfig(key, value);
    }

    public void updateImageCDNConfig() {
        String key = "cdn_traffic_policy";
        String value = "{\"list\":[{\"currentHost\":\"img01.weeecdn.com\",\"order\":0,\"cdnAlts\":[{\"host\":\"img07.weeecdn.net\",\"rate\":0.99},{\"host\":\"img01.weeecdn.com\",\"rate\":0.01}]},{\"currentHost\":\"img06.weeecdn.com\",\"order\":1,\"cdnAlts\":[{\"host\":\"img08.weeecdn.net\",\"rate\":0.99},{\"host\":\"img06.weeecdn.com\",\"rate\":0.01}]},{\"currentHost\":\"static.weeecdn.com\",\"order\":2,\"cdnAlts\":[{\"host\":\"static.weeecdn.net\",\"rate\":0.99},{\"host\":\"static.weeecdn.com\",\"rate\":0.01}]},{\"currentHost\":\"video.test.weeecdn.com\",\"order\":3,\"cdnAlts\":[{\"host\":\"video-test.weeecdn.net\",\"rate\":0.99},{\"host\":\"video.test.weeecdn.com\",\"rate\":0.01}]}]}";
        updateConfig(key, value);
    }

    public void updateWebViewConfig() {
        String key = ConfigManager.DynamicConfig.WEBVIEW;
//        String value = "{\"share_image_cache_min_version\": true }";
        WebViewConfigBean configBean = new WebViewConfigBean();
        configBean.share_image_cache_min_version = "20.1";
        String value = JsonUtils.toJSONString(configBean);
        updateConfig(key, value);
    }

    public void updateDebugToolPassword() {
        String key = ConfigManager.DynamicConfig.DEBUG_TOOL;
        //insprec: SuperWeee!  tools: Sayweee!
        String value = "{\"insprec\":\"1af4e3c1074e3d8d27be5fb1f8b546ac\",\"tools\":\"f39fbf352ff46a820e1482a7c956496b\"}";
        addConfig(key, value);
        TaskScheduler.getMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                updateConfig(key, value);
            }
        }, 1000);
    }

}
