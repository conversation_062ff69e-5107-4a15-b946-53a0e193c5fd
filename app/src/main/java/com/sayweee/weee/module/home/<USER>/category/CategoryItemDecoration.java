package com.sayweee.weee.module.home.provider.category;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.weee.utils.CommonTools;

class CategoryItemDecoration extends RecyclerView.ItemDecoration {

    private static final int DEF_START_MARGIN = CommonTools.dp2px(16);
    private static final int DEF_END_MARGIN = CommonTools.dp2px(20);
    private static final int DEF_BOTTOM_MARGIN = CommonTools.dp2px(3);
    private static final int DEF_SPACE_MARGIN = CommonTools.dp2px(4);

    private final int START_MARGIN = DEF_START_MARGIN;
    private final int END_MARGIN = DEF_END_MARGIN;
    private final int BOTTOM_MARGIN = DEF_BOTTOM_MARGIN;
    private final int SPACE_MARGIN;

    public CategoryItemDecoration() {
        this(DEF_SPACE_MARGIN);
    }

    public CategoryItemDecoration(int spaceMargin) {
        this.SPACE_MARGIN = spaceMargin;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);

        RecyclerView.Adapter<?> adapter = parent.getAdapter();
        if (adapter == null) {
            return;
        }

        int spanCount = 2;
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (layoutManager instanceof StaggeredGridLayoutManager) {
            spanCount = ((StaggeredGridLayoutManager) layoutManager).getSpanCount();
        }

        int itemCount = adapter.getItemCount();
        if (itemCount == 0) {
            return;
        }

        ViewGroup.LayoutParams lp = view.getLayoutParams();
        if (!(lp instanceof StaggeredGridLayoutManager.LayoutParams)) {
            return;
        }

        StaggeredGridLayoutManager.LayoutParams layoutParams = (StaggeredGridLayoutManager.LayoutParams) lp;
        int spanIndex = layoutParams.getSpanIndex();
        if (spanIndex == StaggeredGridLayoutManager.LayoutParams.INVALID_SPAN_ID) {
            return;
        }

        int position = parent.getChildAdapterPosition(view);
        if (position == RecyclerView.NO_POSITION) {
            return;
        }

        if (spanCount == 1) {
            if (position == 0) {
                outRect.left = START_MARGIN;
            } else {
                outRect.left = SPACE_MARGIN;
            }
            if (position == itemCount - 1) {
                outRect.right = END_MARGIN;
            }
        } else {
            if (position == 0 || position == 1) {
                outRect.left = START_MARGIN;
            } else {
                outRect.left = SPACE_MARGIN;
            }
            if (position == itemCount - 1 || position == itemCount - 2) {
                outRect.right = END_MARGIN;
            }
            if (spanIndex == 0) {
                outRect.bottom = BOTTOM_MARGIN;
            }
        }
    }
}
