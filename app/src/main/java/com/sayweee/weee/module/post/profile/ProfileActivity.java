package com.sayweee.weee.module.post.profile;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.lifecycle.MutableLiveData;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ActivityProfileMineNewBinding;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.EditPostManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.dialog.ProfileLevelDialog;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.module.post.PostRateTranslationFragment;
import com.sayweee.weee.module.post.base.CmtBaseActivity;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.bean.RateTranslationBean;
import com.sayweee.weee.module.post.edit.bean.NotifyBean;
import com.sayweee.weee.module.post.edit.service.PostUploadManager;
import com.sayweee.weee.module.post.profile.adapter.ProfileViewPagerAdapter;
import com.sayweee.weee.module.post.profile.bean.FollowData;
import com.sayweee.weee.module.post.profile.bean.ProfileInformationData;
import com.sayweee.weee.module.post.profile.helper.LoginObserver;
import com.sayweee.weee.module.post.profile.helper.ProfileInformationHelper;
import com.sayweee.weee.module.post.search.bean.FollowStatusBean;
import com.sayweee.weee.module.post.shared.CmtFailureData;
import com.sayweee.weee.module.post.shared.CmtSharedViewModel;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.post.widget.BottomIosDialog;
import com.sayweee.weee.module.post.widget.ProfileDealRightDialog;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebRouter;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.share.ShareHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ObjectUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.spanner.DefaultURLClickListenerImpl;
import com.sayweee.weee.widget.BoldTextView;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.ExpandTextView2;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.utils.Spanny;
import com.sayweee.widget.toaster.Toaster;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Author:  wind
 * Date:    2021/8/10.
 * Desc:
 */
public class ProfileActivity extends CmtBaseActivity<ProfileViewModel> {

    public static final int FROM_NORMAL = 10000; //通用流程
    public static final int FROM_MESSAGE_CENTER = 10010; //消息中心
    public static final String STATUS_DRAFT = "D";
    public static final String STATUS_REVIEW = "A";
    public static final String STATUS_PUBLISHED = "P";
    public static final String VIDEO = "video";
    public static final String POST = "post";
    public static final String REVIEW = "review";
    public static final String LIKES = "likes";
    public static final String COMMENTED = "commented";

//    private ProfileActivityBinding binding;

    private ActivityProfileMineNewBinding binding;

    private ProfileViewPagerAdapter vpAdapter;

    private String uid;
    private String type;
    private boolean isMine = true;
    private String bio_url;
    private String badge_url;
    private final ArrayList<PostCategoryBean.CategoriesBean> categories = new ArrayList<>();
    private int tabPosition = 0;
    private String status = "";
    private String mid;
    private boolean isSmsSharing, isViewVisible;
    private int postId;
    private String inReviewTitle;
    private String inReviewDesc;
    private NotifyBean notifyBean;
    private ProfileInformationData profileInformationData;

    public static Intent getIntent(Context context) {
        return getIntent(context, null, AccountManager.get().getUID());
    }

    public static Intent getIntent(Context context, String status) {
        return getIntent(context, null, AccountManager.get().getUID()).putExtra("status", status);
    }

    public static Intent getIntent(Context context, String type, String status, String source) {
        return getIntent(context, null, AccountManager.get().getUID()).putExtra("type", type).putExtra("status", status).putExtra("source", source);
    }

    public static Intent getIntent(Context context, String source, int postId, String trackSource, String uid) {
        return new Intent(context, ProfileActivity.class)
                .putExtra("source", source)
                .putExtra("postId", postId)
                .putExtra("trackSource", trackSource)
                .putExtra("uid", uid);
    }

    public static Intent getIntent(Context context, String source, String uid) {
        return new Intent(context, ProfileActivity.class)
                .putExtra("source", source)
                .putExtra("uid", uid);
    }

    public static Intent getIntent(Context context, int fromType, String uid, String source, String msgId, String msgType) {
        return new Intent(context, ProfileActivity.class)
                .putExtra("fromType", fromType)
                .putExtra("uid", uid)
                .putExtra("source", source)
                .putExtra(Constants.MessageParams.MSG_ID, msgId)
                .putExtra(Constants.MessageParams.MSG_TYPE, msgType);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_profile_mine_new;
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        loadIntentExtra();

        binding = ActivityProfileMineNewBinding.bind(contentView);
        if (!EmptyUtils.isEmpty(uid) && !uid.equalsIgnoreCase(AccountManager.get().getUID())) {
            isMine = false;
        }
        setRefreshSettings();
        if (!isMine) {
            findViewById(R.id.vl_profile_list_tab).setVisibility(View.GONE);
        } else {
            findViewById(R.id.vl_profile_list_tab).setVisibility(View.VISIBLE);
        }
        isViewVisible = VariantConfig.IS_VIEW_VISIBLE;
        ViewTools.disallowTouchEvents(findViewById(R.id.ll_user_profile_veil));
        showVeil(true);
        View.OnClickListener onClickListener = new OnSafeClickListener() {

            @SuppressLint("NonConstantResourceId")
            @Override
            public void onClickSafely(View view) {
                ProfileInformationData info = profileInformationData;
                switch (view.getId()) {
                    case R.id.iv_close:
                        boolean isFollow = binding.inProfileInfo.tvEdit.getText().toString().equals(getString(R.string.yi_following));
                        setResult(RESULT_OK, new Intent().putExtra("isFollow", isFollow));
                        finish();
                        break;
                    case R.id.iv_share:
                        onShareTrigger();
                        break;
                    case R.id.ll_followers:
                        if (info != null && info.isNotDeleted()) {
                            startActivity(ProfileFollowActivity.getIntent(activity, uid, true, binding.inProfileInfo.tvName.getText().toString()));
                        }
                        break;
                    case R.id.ll_following:
                        if (info != null && info.isNotDeleted()) {
                            startActivity(ProfileFollowActivity.getIntent(activity, uid, false,
                                    binding.inProfileInfo.tvName.getText().toString()));
                        }
                        break;
                    case R.id.tv_badge:
                    case R.id.ll_star:
                        if (!EmptyUtils.isEmpty(badge_url)) {
                            if (!EmptyUtils.isEmpty(profileInformationData) && !EmptyUtils.isEmpty(profileInformationData.user_info)) {
                                t2ClickAction("tier" + profileInformationData.user_info.tier, 0);
                            }
                            startActivity(WebViewActivity.getIntent(activity, badge_url));
                        }
                        break;
                    case R.id.iv_dots:
                        showPrivilegeDialog();
                        break;
                }
            }
        };
        setOnClickListener(
                onClickListener,
                R.id.iv_close,
                R.id.iv_share,
                R.id.ll_followers,
                R.id.ll_following,
                R.id.tv_badge,
                R.id.iv_dots,
                R.id.ll_star
        );

        initAppBarLayout();
        setViewPagerPro();
        showVeil(true);
        convertAllTagsList();
        ViewTools.setViewVisible(binding.inProfileInfo.llFollowerStatus, isViewVisible);
    }

    private void loadIntentExtra() {
        Intent a = getIntent();
        if (a == null) return;

        uid = a.getStringExtra("uid");
//        userId = 10927640; // deleted User
        postId = a.getIntExtra("postId", -1);
        status = a.getStringExtra("status");
        if (!EmptyUtils.isEmpty(status)) {
            status = status.toUpperCase();
        }
        type = a.getStringExtra("type");
        mid = a.getStringExtra(Constants.MessageParams.MSG_ID);
        notifyBean = (NotifyBean) a.getSerializableExtra("notifyBean");
        if (!EmptyUtils.isEmpty(notifyBean)) {
            showCompatDialog();
        }

        String source = a.getStringExtra("source");
    }

    private void initAppBarLayout() {
        final AppBarLayout appBarLayout = findViewById(R.id.abl_event);
        appBarLayout.addOnOffsetChangedListener((appBarLayout1, verticalOffset) -> {
            if (Math.abs(verticalOffset) >= CommonTools.dp2px(80)) {
                if (binding.ivSmallHeader.getVisibility() == View.GONE) {
                    final TranslateAnimation anim;
                    anim = new TranslateAnimation(
                            TranslateAnimation.RELATIVE_TO_SELF, 0, TranslateAnimation.RELATIVE_TO_SELF, 0,
                            TranslateAnimation.RELATIVE_TO_SELF, 1, TranslateAnimation.RELATIVE_TO_SELF, 0
                    );
                    anim.setDuration(400L);
                    binding.ivSmallHeader.post(() -> {
                        binding.ivSmallHeader.setVisibility(View.VISIBLE);
                        binding.ivSmallHeader.startAnimation(anim);
                    });
                }
            } else {
                binding.ivSmallHeader.setVisibility(View.GONE);
            }

            final ProfileInformationData info = profileInformationData;
            boolean showSmallFollow = !isMine && info != null && info.isNotDeleted();
            if (showSmallFollow && Math.abs(verticalOffset) >= binding.inProfileInfo.clProfileTop.getMeasuredHeight()) {
                if (binding.llProfileSmallFollow.getVisibility() == View.GONE) {
                    final TranslateAnimation anim;
                    anim = new TranslateAnimation(
                            TranslateAnimation.RELATIVE_TO_SELF, 0, TranslateAnimation.RELATIVE_TO_SELF, 0,
                            TranslateAnimation.RELATIVE_TO_SELF, 1, TranslateAnimation.RELATIVE_TO_SELF, 0
                    );
                    anim.setDuration(400L);
                    binding.llProfileSmallFollow.post(() -> {
                        if (!EmptyUtils.isEmpty(info.profile_status)) {
                            binding.llProfileSmallFollow.setVisibility(info.isBlocked() ? View.GONE : View.VISIBLE);
                        }
                        binding.llProfileSmallFollow.startAnimation(anim);
                    });
                }
            } else {
                binding.llProfileSmallFollow.setVisibility(View.GONE);
            }
        });
    }

    @Override
    public void loadData() {

    }

    @Override
    public void attachModel() {
        viewModel.informationDataMutableLiveData.observe(this, informationData -> {
            binding.mSmartRefreshLayout.finishRefresh();
            profileInformationData = informationData;
            onInformationChange();
        });

        SharedViewModel.get().profileListVeil.observe(this, aBoolean -> {
            if (aBoolean) {
//                ViewTools.setViewInvisible(true, findViewById(R.id.ll_user_profile_veil));
            } else {
//                ViewTools.setViewInvisible(false, findViewById(R.id.ll_user_profile_veil));
                showVeil(false);
                binding.inProfileTags.tbType.post(() -> binding.inProfileTags.tbType.setVisibility(View.VISIBLE));
            }
        });

        viewModel.shareData.observe(this, this::shareData);

        SharedViewModel.get().inReviewData.observe(this, integer -> {
            if (EmptyUtils.isEmpty(inReviewTitle) && EmptyUtils.isEmpty(inReviewDesc)) {
                viewModel.getInReviewData();
            } else {
                showBottomDialog();
            }
        });

        viewModel.inReviewBean.observe(this, notifyBean -> {
            inReviewTitle = notifyBean.title;
            inReviewDesc = notifyBean.description;
            showBottomDialog();
        });

        CmtSharedViewModel.get().observeSpamFailureData(this, data -> {
            if (data == null) return;
            if (data.getType() == CmtFailureData.TYPE_SPAM_FOLLOW) {
                resetFollowStatus();
            }
        });
        SharedViewModel.get().loginStatusData.observe(this, loginObserver);

    }

    private LoginObserver loginObserver = new LoginObserver(new LoginObserver.Callback() {
        @Override
        public void toLogin() {
            startActivity(AccountIntentCreator.getIntent(activity));
        }

        @Override
        public void toRefresh() {
        }
    });

    private void setRefreshSettings() {
        if (binding.mSmartRefreshLayout == null) return;
        binding.mSmartRefreshLayout.setEnableRefresh(true);
        binding.mSmartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getData();
            initData();
            binding.inProfileList.stickViewpager.setCurrentItem(tabPosition, false);
            SharedViewModel.get().toFragmentResult(categories.get(tabPosition).status);
            vpAdapter.notifyDataSetChanged();
        });
    }

    private void convertAllTagsList() {
        binding.inProfileList.stickViewpager.setOffscreenPageLimit(4);
        initData();
        List<String> titleList = new ArrayList<>();
        titleList.add(getString(R.string.posts));
        titleList.add(getString(R.string.reviews));
        if (isMine) {
            titleList.add(getString(R.string.profile_likes));
            titleList.add(getString(R.string.commented));
        }
        vpAdapter = new ProfileViewPagerAdapter(getSupportFragmentManager(), getLifecycle(), categories, titleList);
        binding.inProfileList.stickViewpager.setAdapter(vpAdapter);
        binding.inProfileList.stickViewpager.setSaveEnabled(false);
        if (!EmptyUtils.isEmpty(type)) {
            tabPosition = type.equals(VIDEO) || type.equals(POST) ? 0 : type.equals(REVIEW) ? 1 : type.equals(LIKES) ? 2 : 3;
        }
        binding.inProfileList.stickViewpager.setCurrentItem(tabPosition, false);
        initTbType(binding.inProfileTags.tbType);

        binding.inProfileList.stickViewpager.removeOnPageChangeListener(pageChangeCallback);
        binding.inProfileList.stickViewpager.addOnPageChangeListener(pageChangeCallback);
    }

    private void initData() {
        List<Integer> positionList = new ArrayList<>();
        for (PostCategoryBean.CategoriesBean category : categories) {
            positionList.add(category.view_pos);
        }
        categories.clear();
        PostCategoryBean.CategoriesBean bean = new PostCategoryBean.CategoriesBean();
        PostCategoryBean.CategoriesBean bean1 = new PostCategoryBean.CategoriesBean();
        PostCategoryBean.CategoriesBean bean2 = new PostCategoryBean.CategoriesBean();
        PostCategoryBean.CategoriesBean bean3 = new PostCategoryBean.CategoriesBean();
        if (null != status && !status.isEmpty()) {
            if (EmptyUtils.isEmpty(type) || type.equals(POST)) {
                bean.status = status;
                bean.view_pos = status.equals(STATUS_DRAFT) ? 1 : 0;
            } else {
                bean.status = STATUS_PUBLISHED;
                bean.view_pos = 0;
            }
        } else {
            boolean isDraft = false;
            if (!positionList.isEmpty()) {
                isDraft = positionList.get(0) != 0;
            }
            bean.status = isDraft ? STATUS_DRAFT : STATUS_PUBLISHED;
//            bean.status = positionList.size() > 0 ? positionList.get(0) == 0 ? STATUS_PUBLISHED : positionList.get(0) == 1 ? STATUS_DRAFT : STATUS_DRAFT : STATUS_PUBLISHED;
        }
        bean.justViewedId = postId;
        bean.type = VIDEO;
        if (!isMine) {
            bean.uid = uid;
        }
        categories.add(bean);
        if (!positionList.isEmpty()) {
            categories.get(0).view_pos = positionList.get(0);
        }
        if (isMine) {
            bean1.type = REVIEW;
            if (null != status && !status.isEmpty()) {
                if (!EmptyUtils.isEmpty(type) && type.equals(REVIEW)) {
                    bean1.status = status;
                    bean1.view_pos = status.equals(STATUS_REVIEW) ? 1 : 0;
                } else {
                    bean1.status = STATUS_PUBLISHED;
                    bean1.view_pos = 0;
                }
            } else {
                bean1.status = positionList.size() > 1 ? positionList.get(1) == 0 ? STATUS_PUBLISHED : STATUS_REVIEW : STATUS_PUBLISHED;
            }
            categories.add(bean1);
            if (positionList.size() > 1) {
                categories.get(1).view_pos = positionList.get(1);
            }
            bean2.type = LIKES;
            categories.add(bean2);
            if (positionList.size() > 2) {
                categories.get(2).view_pos = positionList.get(2);
            }
            bean3.type = COMMENTED;
            categories.add(bean3);
            if (positionList.size() > 3) {
                categories.get(3).view_pos = positionList.get(3);
            }
        } else {
            bean1.uid = uid;
            bean1.type = REVIEW;
            if (!EmptyUtils.isEmpty(type) && type.equals(REVIEW)) {
                bean1.status = status;
                bean1.view_pos = STATUS_REVIEW.equalsIgnoreCase(status) ? 1 : 0;
            } else {
                bean1.status = positionList.size() > 1 ? positionList.get(1) == 0 ? STATUS_PUBLISHED : STATUS_REVIEW : STATUS_PUBLISHED;
            }
            categories.add(bean1);
            if (positionList.size() > 1) {
                categories.get(1).view_pos = positionList.get(1);
            }
        }

        if (!EmptyUtils.isEmpty(categories)) {
            if (categories.size() <= 2) {
                binding.inProfileTags.tbType.setTabMode(TabLayout.MODE_FIXED);
                binding.inProfileTags.tbType.getLayoutParams().width = ViewGroup.LayoutParams.MATCH_PARENT;
            } else {
                binding.inProfileTags.tbType.setTabMode(TabLayout.MODE_SCROLLABLE);
                binding.inProfileTags.tbType.getLayoutParams().width = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
            binding.inProfileTags.tbType.requestLayout();
        }
        status = "";
    }

    private void initTbType(TabLayout tbType) {
        if (LanguageManager.get().isSpanish() && isMine) {
            tbType.setTabMode(TabLayout.MODE_AUTO);
        }
        tbType.setupWithViewPager(binding.inProfileList.stickViewpager);
        tbType.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                tbType.selectTab(tbType.getTabAt(tbType.getSelectedTabPosition()));
            }
        });
        tbType.removeOnTabSelectedListener(onTbTypeSelectedListener);
        tbType.addOnTabSelectedListener(onTbTypeSelectedListener);
    }

    private final TabLayout.OnTabSelectedListener onTbTypeSelectedListener =
            new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    tabPosition = tab.getPosition();
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {
                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {

                }
            };

    private void onInformationChange() {
        final ProfileInformationData info = profileInformationData;
        if (info == null) return;

        ProfileInformationHelper helper = new ProfileInformationHelper(info, isMine);
        ViewTools.setViewVisible(binding.ivDots, !isMine && info.isNotDeleted());

        Object userHeadImageUrl = helper.getUserHeadImageUrl();
        if (userHeadImageUrl instanceof String) {
            ImageLoader.load(
                    activity,
                    binding.inProfileInfo.ivPic,
                    WebpManager.get().getConvertUrl(SPEC_AVATAR, (String) userHeadImageUrl),
                    R.mipmap.default_header
            );
            ImageLoader.load(
                    activity,
                    binding.ivSmallHeader,
                    WebpManager.get().getConvertUrl(SPEC_AVATAR, (String) userHeadImageUrl),
                    R.mipmap.default_header
            );
        } else if (userHeadImageUrl instanceof Integer) {
            binding.inProfileInfo.ivPic.setImageResource((int) userHeadImageUrl);
            binding.ivSmallHeader.setImageResource((int) userHeadImageUrl);
        }

        binding.inProfileInfo.tvName.setText(helper.getUserAlias(this));
        ViewTools.setViewVisible(binding.inProfileInfo.tvUserId, isMine);
        binding.inProfileInfo.tvUserId.setText(helper.getUserDisplayId());

        String userDescription = helper.getUserDescription();
        binding.inProfileInfo.tvUserStore.setEllipseStringType(2);
        binding.inProfileInfo.tvUserStore.initCloseEnd(
                ResourcesCompat.getColor(getResources(), R.color.brand_color_tone_neutral_spectrum_14, null),
                getString(R.string.s_read_more_cap),
                "\u00A0" + getString(R.string.s_show_less1)
        );
        binding.inProfileInfo.tvUserStore.initWidth(
                CommonTools.getWindowWidth(activity) - CommonTools.dp2px(20) * 2,
                ResourcesCompat.getColor(getResources(), R.color.brand_color_tone_neutral_spectrum_14, null)
        );
        binding.inProfileInfo.tvUserStore.setMaxLines(2);
        if (EmptyUtils.isEmpty(userDescription)) {
            binding.inProfileInfo.tvUserStore.setCloseTextOriginal(isMine ? emptySpanny() : "");
        } else {
            binding.inProfileInfo.tvUserStore.setCloseText(userDescription);
        }
        binding.inProfileInfo.tvUserStore.setVisibility(!isMine && EmptyUtils.isEmpty(userDescription) ? View.GONE : View.VISIBLE);
        binding.inProfileInfo.tvUserStore.setOnExpandTextViewClickListener(new ExpandTextView2.OnExpandTextViewClickListener() {
            @Override
            public void clickContent() {
                if (isMine && binding.inProfileInfo.tvUserStore.getText().toString().contains(getString(R.string.leave_a_short_bio_to_let_others_learn_a_bit_more_about_you))) {
                    if (!EmptyUtils.isEmpty(bio_url)) {
                        startActivity(WebViewActivity.getIntent(activity, bio_url));
                    }
                } else {
                    binding.inProfileInfo.tvUserStore.toggle();
                }
            }

            @Override
            public void clickExpand(boolean isExpand) {

            }
        });

        binding.inProfileInfo.llProfileInsights.setVisibility(isMine && info.show_insights? View.VISIBLE : View.GONE);
        binding.inProfileInfo.layoutFunction.setVisibility(isViewVisible? View.VISIBLE : View.GONE);

        ObjectUtils.let(helper.getSocialInfoFollowers(), it -> {
            binding.inProfileInfo.tvFollowerCount.setText(it.value);
            binding.inProfileInfo.tvFollowerLabel.setText(it.label);
        });
        ObjectUtils.let(helper.getSocialInfoFollowing(), it -> {
            binding.inProfileInfo.tvFollowingCount.setText(it.value);
            binding.inProfileInfo.tvFollowingLabel.setText(it.label);
        });
        ObjectUtils.let(helper.getSocialInfoLikes(), it -> {
            binding.inProfileInfo.tvLikesCount.setText(it.value);
            binding.inProfileInfo.tvLikesLabel.setText(it.label);
        });

        String followInfoMessage = helper.getFollowInfoMessage();
        if (!EmptyUtils.isEmpty(followInfoMessage)) {
            binding.inProfileInfo.tvUserFollowText.setVisibility(View.VISIBLE);
            binding.inProfileInfo.tvUserFollowText.setMovementMethod(LinkMovementMethod.getInstance());
            ViewTools.setViewHtml(
                    binding.inProfileInfo.tvUserFollowText,
                    followInfoMessage,
                    new DefaultURLClickListenerImpl<Void>()
            );
        } else {
            binding.inProfileInfo.tvUserFollowText.setVisibility(View.GONE);
        }

        updateFollowStatus();

        int maxWidth = binding.inProfileInfo.llName.getMeasuredWidth();

        // official verified
        setOfficialVerified(info);

        if (!EmptyUtils.isEmpty(info.user_tier_info) && !EmptyUtils.isEmpty(info.user_tier_info.img)) {
            binding.inProfileInfo.tvBadge.setVisibility(View.VISIBLE);
            binding.inProfileInfo.tvBadge.setBackgroundSolidDrawable(Color.parseColor(info.user_tier_info.bg_color), CommonTools.dp2px(25));
            binding.inProfileInfo.tvBadge.setTextColor(Color.parseColor(info.user_tier_info.color));
            binding.inProfileInfo.tvBadge.setText(info.user_tier_info.label);
            binding.inProfileInfo.llStar.setBackgroundSolidDrawable(Color.parseColor(info.user_star_info.bg_color), CommonTools.dp2px(25));
            binding.inProfileInfo.tvStar.setText(info.user_star_info.label);
            binding.inProfileInfo.tvStar.setTextColor(Color.parseColor(info.user_star_info.color));

            Paint tvStarPaint = binding.inProfileInfo.tvStar.getPaint();
            Paint tvBadgePaint = binding.inProfileInfo.tvBadge.getPaint();
            int textWidth = EmptyUtils.isEmpty(info.user_tier_info.label) ? 0 : (int) tvBadgePaint.measureText(info.user_tier_info.label);
            int starTextWidth = EmptyUtils.isEmpty(info.user_star_info.label) ? 0 : (int) tvStarPaint.measureText(info.user_star_info.label);
            maxWidth = maxWidth - CommonTools.dp2px(13) - textWidth - starTextWidth - CommonTools.dp2px(34);
            binding.inProfileInfo.tvName.setMaxWidth(maxWidth);
            if (!EmptyUtils.isEmpty(info.user_star_label)) {
                binding.inProfileInfo.llStar.setVisibility(View.VISIBLE);
                binding.inProfileInfo.tvStar.setText(info.user_star_label);
            } else {
                binding.inProfileInfo.llStar.setVisibility(View.GONE);
            }

            ImageLoader.load(activity, binding.inProfileInfo.ivStar, WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, info.user_tier_info.img));
        } else {
            binding.inProfileInfo.llStar.setVisibility(View.GONE);
            binding.inProfileInfo.tvBadge.setVisibility(View.GONE);
        }

        binding.inProfileInfo.llProfileInsights.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                t2ClickAction("account_insights", 2);
                startActivity(WebViewActivity.getIntent(activity, info.insights_url));
            }
        });

        final String profileUrl = info.profile_url;
        setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (isMine) {
                    t2ClickAction("edit_profile", 1);
                    startActivity(WebViewActivity.getIntent(activity, profileUrl));
                } else if (info.isNotDeleted()) {
                    if (AccountManager.get().isLogin()) {
                        String status = info.isUnFollowed() || info.isFollowBack() ? "A" : "C";
                        if (binding.inProfileInfo.tvEdit.getText().toString().equals(getString(R.string.yi_following))) {
                            showDialog(status, info);
                        } else {
                            FollowData data = new FollowData();
                            data.uid = profileInformationData.user_info.uid;
                            if (!info.isBlocked()) {
                                data.status = 1;
                            }
                            EditPostManager.setFollowData(data);
                            if (info.social_info != null) {
                                if (!info.social_info.isEmpty()) {
                                    if (isNumeric(info.social_info.get(0).value)) {
                                        if (!info.isBlocked()) {
                                            info.social_info.get(0).value = String.valueOf(DecimalTools.parseInt(info.social_info.get(0).value) + 1);
                                        }
                                        binding.inProfileInfo.tvFollowerCount.setText(info.social_info.get(0).value);
                                        binding.tvSmallEdit.setText(info.social_info.get(0).value);
                                    }
                                }
                            }
                            unBlockToast();
                            info.profile_status = info.isUnFollowed() || info.isFollowBack() ? "Followed" : "unFollowed";
                            viewModel.postFollow(uid, status, info.profile_status);
                            updateFollowStatus();
                        }
                    } else {
                        startActivity(AccountIntentCreator.getIntent(activity));
                    }
                }
            }
        }, R.id.ll_profile_edit, R.id.ll_profile_small_follow);

        bio_url = info.bio_url;
        badge_url = info.badge_url;
        if (!EmptyUtils.isEmpty(info.upgrade_popup)) {
            if (info.upgrade_popup.show) {
                showGoldBunDialog(info);
            }
        }
    }

    private void setOfficialVerified(ProfileInformationData info) {
        if (info.verified_seller) {
            binding.inProfileInfo.ivVerified.setVisibility(View.VISIBLE);
            binding.inProfileInfo.layStarLevel.setVisibility(View.GONE);
        } else {
            binding.inProfileInfo.ivVerified.setVisibility(View.GONE);
            binding.inProfileInfo.layStarLevel.setVisibility(View.VISIBLE);
        }

        if (!EmptyUtils.isEmpty(info.seller_url) && info.verified_seller) {
            binding.inProfileInfo.layViewStore.setVisibility(View.VISIBLE);
            binding.inProfileInfo.tvViewStore.setText(info.seller_link_text);
        } else {
            binding.inProfileInfo.layViewStore.setVisibility(View.GONE);
        }

        binding.inProfileInfo.layViewStore.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                WebRouter.toPage(v.getContext(), info.seller_url);
            }
        });
    }

    private void resetFollowStatus() {
        profileInformationData.profile_status = profileInformationData.isUnFollowed() || profileInformationData.isFollowBack() ? "Followed" : "unFollowed";
        updateFollowStatus();
    }

    private void setViewPagerPro() {
        try {
            Field field = ViewPager.class.getDeclaredField("mFlingDistance");
            field.setAccessible(true);
            field.set(binding.inProfileList.stickViewpager, 5);
        } catch (Exception ignored) {
        }
    }

    /**
     * 利用正则表达式判断字符串是否是数字
     */
    private boolean isNumeric(@Nullable String string) {
        if (EmptyUtils.isEmpty(string)) return false;
        return Pattern.compile("[0-9]*").matcher(string).matches();
    }

    private Spanny emptySpanny() {
        String text = getString(R.string.leave_a_short_bio_to_let_others_learn_a_bit_more_about_you);
        String space = " ";
        text = text + space;
        Spanny spanny = new Spanny();
        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.mipmap.pic_pencil_new, null);
        if (drawable != null) {
            drawable.setBounds(0, 0, CommonTools.dp2px(14), CommonTools.dp2px(14));
        }
        spanny.append(text);
        spanny.append("", new CenterImageSpan(drawable));
        return spanny;
    }

    private void onShareTrigger() {
        MutableLiveData<ShareBean> shareData = viewModel.shareData;
        ShareBean bean = shareData.getValue();
        if (bean == null) {
            viewModel.getProfileShare(uid);
        } else {
            shareData(bean);
        }
    }

    private ShareDialog shareDialog;

    private void shareData(ShareBean bean) {
        if (bean != null) {
            if (shareDialog == null) {
                shareDialog = new ShareDialog(activity).
                        setShareData(bean)
                        .setResultTip(getString(R.string.s_profile_share_success))
                        .setOnShareChannelListener((dialog, type) -> {
                            if (Constants.ShareChannel.SMS.equalsIgnoreCase(type)) {
                                isSmsSharing = true;
                            }
                        })
                        .setAttachedData(profileInformationData.user_info.user_id, "profile");
            }
            PopupManager.get().showOnQueue(shareDialog);
        }
    }

    private final ViewPager.OnPageChangeListener pageChangeCallback =
            new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    tabPosition = position;
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            };

    @Override
    protected void onResume() {
        super.onResume();
        if (loginObserver.isNotLogin) {
            finish();
            return;
        }
        getData();
        AppAnalytics.logPageView(isMine ? WeeeEvent.PageView.ME_PROFILE : WeeeEvent.PageView.COMM_PROFILE, this);
        if (isSmsSharing) {
            isSmsSharing = false;
            Toaster.showToast(getString(R.string.s_profile_share_success));
        }
        binding.ivShare.setVisibility(isMine && isViewVisible ? View.VISIBLE : View.INVISIBLE);
        ViewTools.setViewVisible(binding.ivDots, false);
    }

    private void getData() {
        if (isMine) {
            viewModel.initProfileInformation();
        } else {
            viewModel.initOtherProfileInformation(uid, mid);
        }
    }

    private void showDialog(String status, ProfileInformationData profileInformationData) {
        OnDialogClickListener onDialogClickListener = (dialog, view) -> {
            dialog.dismiss();
            FollowData data = new FollowData();
            data.uid = uid;
            data.status = 0;
            EditPostManager.setFollowData(data);
            binding.inProfileInfo.tvEdit.setText(profileInformationData.isUnFollowed() ? getString(R.string.yi_following) : getString(R.string.follow));
            binding.tvSmallEdit.setText(profileInformationData.isUnFollowed() ? getString(R.string.yi_following) : getString(R.string.follow));

            if (profileInformationData.social_info != null) {
                if (!profileInformationData.social_info.isEmpty()) {
                    if (isNumeric(profileInformationData.social_info.get(0).value)) {
                        if (!profileInformationData.isBlocked()) {
                            profileInformationData.social_info.get(0).value = String.valueOf(DecimalTools.parseInt(profileInformationData.social_info.get(0).value) - 1);
                        }
                        binding.inProfileInfo.tvFollowerCount.setText(profileInformationData.social_info.get(0).value);
                        binding.tvSmallEdit.setText(profileInformationData.social_info.get(0).value);
                    }
                }
            }
            profileInformationData.profile_status = profileInformationData.isUnFollowed() || profileInformationData.isFollowBack() ? "Followed" : "unFollowed";
            viewModel.postFollow(uid, status, profileInformationData.profile_status);
            updateFollowStatus();
        };

        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                .setUp(
                        onDialogClickListener,
                        getString(R.string.are_you_make_sure_stop_follow_this_user),
                        getString(R.string.yes_unfollow),
                        getString(R.string.cancel)
                )
                .addHelperCallback((dialog, helper) -> helper.setOnClickListener(R.id.tv_cancel, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dialog.dismiss();
                    }
                })).show();
    }

    private void showGoldBunDialog(ProfileInformationData profileInformationData) {
        ProfileInformationData.Info info = profileInformationData.upgrade_popup.info;
        new ProfileLevelDialog(activity, info.tier_style.contains("2") ? ProfileLevelDialog.STYLE_T2 : ProfileLevelDialog.STYLE_T3)
                .setUp((dialog, view) -> {
                    startActivity(WebViewActivity.getIntent(activity, info.btn_link));
                    dialog.dismiss();
                }, info.rights_list, info.title, info.sub_title)
                .addHelperCallback((dialog, helper) -> helper.setOnClickListener(R.id.tv_cancel, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dialog.dismiss();
                    }
                })).show();
    }

    private void updateFollowStatus() {
        final ProfileInformationData info = this.profileInformationData;
        if (info == null) return;

        final boolean isEdit = info.isEdit();
        final boolean isNotExist = !info.isNotDeleted();
        final boolean isUnFollowed = info.isUnFollowed();
        final boolean isFollowBack = info.isFollowBack();
        final boolean isFollowing = info.isFollowing();
        final boolean isBlocked = info.isBlocked();

        if (!isEdit && !EmptyUtils.isEmpty(info.profile_status)) {
            FollowStatusBean bean = new FollowStatusBean();
            bean.uid = uid;
            bean.followStatus = info.profile_status;
            bean.isSuccess = true;
            SharedViewModel.get().followUser(bean);
        }

        viewModel.postFollow(uid, status, info.profile_status);

        if (isEdit) {
            binding.inProfileInfo.ivProfileEdit.setImageResource(R.mipmap.pic_profile_edit);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.edit_profile));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.VISIBLE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_surface_1_fg_default_idle);
            binding.ivProfileSmallFollow.setVisibility(View.VISIBLE);
            binding.ivProfileSmallFollow.setImageResource(R.mipmap.pic_profile_edit);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else if (isNotExist) {
            binding.inProfileInfo.ivProfileEdit.setImageDrawable(null);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.user_not_found));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.GONE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_surface_1_fg_default_idle);
            binding.ivProfileSmallFollow.setVisibility(View.GONE);
            binding.ivProfileSmallFollow.setImageDrawable(null);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else if (isUnFollowed) {
            binding.inProfileInfo.ivProfileEdit.setImageResource(R.mipmap.ic_follow_button_plus_22x22);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.follow));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.VISIBLE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_surface_1_fg_default_idle);
            binding.ivProfileSmallFollow.setVisibility(View.VISIBLE);
            binding.ivProfileSmallFollow.setImageResource(R.mipmap.ic_follow_button_plus_15x15);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else if (isFollowBack) {
            binding.inProfileInfo.ivProfileEdit.setImageDrawable(null);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.follow_back));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.GONE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_primary_surface_1_bg_idle_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_primary_surface_1_fg_default_idle);
            binding.ivProfileSmallFollow.setVisibility(View.GONE);
            binding.ivProfileSmallFollow.setImageDrawable(null);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_primary_surface_1_bg_idle_corner_100);
        } else if (isFollowing) {
            binding.inProfileInfo.ivProfileEdit.setImageDrawable(null);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.yi_following));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.GONE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_surface_1_fg_subtle_idle);
            binding.ivProfileSmallFollow.setVisibility(View.GONE);
            binding.ivProfileSmallFollow.setImageDrawable(null);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        } else if (isBlocked) {
            binding.inProfileInfo.ivProfileEdit.setImageDrawable(null);
            binding.inProfileInfo.tvEdit.setText(getString(R.string.unblock));
            binding.inProfileInfo.ivProfileEdit.setVisibility(View.GONE);
            binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
            ViewTools.applyTextColor(binding.inProfileInfo.tvEdit, R.color.color_surface_1_fg_subtle_idle);
            binding.ivProfileSmallFollow.setVisibility(View.GONE);
            binding.ivProfileSmallFollow.setImageDrawable(null);
            binding.llProfileSmallFollow.setBackgroundResource(R.drawable.shape_bg_color_surface_1_fg_hairline_idle_stroke_1_corner_100);
        }

        binding.tvSmallEdit.setText(binding.inProfileInfo.tvEdit.getText().toString());
        binding.tvSmallEdit.setTextColor(binding.inProfileInfo.tvEdit.getTextColors());
        binding.inProfileList.stickViewpager.setVisibility(isBlocked ? View.GONE : View.VISIBLE);

        findViewById(R.id.ll_profile_bottom).setVisibility(info.isBlocked() ? View.GONE : View.VISIBLE);
        findViewById(R.id.ll_profile_block_bottom).setVisibility(!info.isBlocked() ? View.GONE : View.VISIBLE);

        if (info.isBlocked()) {
            appBarNotScroll();
        } else {
            appBarScroll();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PostUploadManager.get().clearCache();
        binding.inProfileList.stickViewpager.clearOnPageChangeListeners();
        // TODO LiveShowActivity.onBack(this);
    }

    private void t2ClickAction(String targetNm, int targetPos) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(targetNm)
                .setTargetPos(targetPos)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {

            // live show entrance
            String uid = intent.getStringExtra("uid");
            if (!EmptyUtils.isEmpty(uid) && !uid.equalsIgnoreCase(this.uid)) {
                // refresh data
                showVeil(true);
                this.uid = uid;
                isMine = uid.equalsIgnoreCase(AccountManager.get().getUID());
                binding.ablEvent.setExpanded(true);
                getData();
            }

            status = intent.getStringExtra("status");
            if (!EmptyUtils.isEmpty(status)) {
                status = status.toUpperCase();
            }
            notifyBean = (NotifyBean) intent.getSerializableExtra("notifyBean");
            categories.clear();
            convertAllTagsList();
            binding.inProfileList.stickViewpager.setCurrentItem(tabPosition, false);
            SharedViewModel.get().toFragmentResult(categories.get(tabPosition).status);
            vpAdapter.notifyDataSetChanged();
            if (!EmptyUtils.isEmpty(notifyBean)) {
                showCompatDialog();
            }
        }
    }

    private void showCompatDialog() {
        new CompatDialog(activity, CompatDialog.STYLE_VERTICAL).addHelperCallback(new WrapperDialog.HelperCallback() {
            @Override
            public void help(Dialog dialog, ViewHelper helper) {
                BoldTextView tvContent = helper.getView(R.id.tv_content);
                tvContent.setTextNormalStyle();
                TextView titleView = helper.getView(R.id.tv_title);
                TextView tvConfirm = helper.getView(R.id.tv_confirm);
                helper.setTextVisible(R.id.tv_title, notifyBean.title);
                helper.setVisible(R.id.tv_cancel, true);
                helper.setTextColor(R.id.tv_cancel, Color.parseColor("#666666"));
                titleView.setTextColor(Color.parseColor("#333333"));
                tvConfirm.setBackgroundResource(R.drawable.selector_profile_btn);
                if (LanguageManager.get().isEnglish()) {
                    titleView.setPadding(CommonTools.dp2px(32), 0, CommonTools.dp2px(32), 0);
                }
                tvContent.setMovementMethod(LinkMovementMethod.getInstance());
                ViewTools.setViewHtml(tvContent, notifyBean.description, new DefaultURLClickListenerImpl<Dialog>(dialog) {
                    @Override
                    public void onClick(@NonNull View view, String url) {
                        Dialog d = getWeakObject();
                        if (d != null) {
                            d.dismiss();
                        }
                        super.onClick(view, url);
                    }
                });
                helper.setText(R.id.tv_confirm, notifyBean.btn1);
                helper.setText(R.id.tv_cancel, notifyBean.btn2);

                helper.setOnClickListener(R.id.tv_confirm, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        view.setBackgroundResource(R.drawable.shape_click_to_transparent);
                        dialog.dismiss();
                    }
                });
                helper.setOnClickListener(R.id.tv_cancel, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dialog.dismiss();
                        AccountManager.get().setNotifyGuided(true);
                    }
                });
            }
        }).show();
    }

    private BottomDialog inReviewBottomDialog;

    private void showBottomDialog() {
        if (inReviewBottomDialog == null) {
            inReviewBottomDialog = (BottomDialog) new BottomDialog(activity) {
                @Override
                protected int getLayoutRes() {
                    return R.layout.dialog_in_review_bottom;
                }
            }.addHelperCallback((Dialog dialog, ViewHelper viewHelper) -> {
                TextView tvTitle = viewHelper.getView(R.id.tv_title);
                tvTitle.setText(inReviewTitle);

                TextView tvContent = viewHelper.getView(R.id.tv_content);
                tvContent.setClickable(true);
                tvContent.setLongClickable(false);
                tvContent.setMovementMethod(LinkMovementMethod.getInstance());
                ViewTools.setViewHtml(
                        tvContent,
                        inReviewDesc,
                        new DefaultURLClickListenerImpl<Void>()
                );
                viewHelper.setOnClickListener(R.id.iv_cancel, targetView -> dialog.dismiss());
            }).show();
        } else {
            inReviewBottomDialog.show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (!EmptyUtils.isEmpty(ShareHelper.getCallBackManager())) {
            ShareHelper.getCallBackManager().onActivityResult(requestCode, resultCode, data);
        }

        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null && !EmptyUtils.isEmpty(data.getStringExtra("status"))) {
            SharedViewModel.get().toFragmentResult(data.getStringExtra("status"));
        }
    }

    private void showVeil(boolean show) {
        if (!show) {
            ViewTools.setViewInvisible(false, findViewById(R.id.ll_user_profile_veil));
            findViewById(R.id.vl_profile_list_tab).setVisibility(View.GONE);
        } else {
            findViewById(R.id.ll_profile_me_tab).setVisibility(isMine ? View.VISIBLE : View.GONE);
            findViewById(R.id.ll_profile_user_tab).setVisibility(isMine ? View.GONE : View.VISIBLE);
            ViewTools.setViewInvisible(true, findViewById(R.id.ll_user_profile_veil));
        }
    }

    private void showPrivilegeDialog() {
        final ProfileInformationData info = profileInformationData;
        if (info == null) {
            return;
        }
        new ProfileDealRightDialog(activity).setData(info.profile_status.equalsIgnoreCase("Blocked")).setOnClickListener(new ProfileDealRightDialog.OnDialogClickListener() {
            @Override
            public void onShareClick() {
                onShareTrigger();
            }

            @Override
            public void onBlockClick() {
                if (AccountManager.get().isLogin()) {
                    if (info.profile_status.equalsIgnoreCase("Blocked")) {
                        unBlockToast();
                        info.profile_status = "unFollowed";
                        viewModel.postFollow(uid, "C", info.profile_status);
                        updateFollowStatus();
                    } else {
                        showBlockDialog();
                    }
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));
                }
            }

            @Override
            public void onReportClick() {
                if (AccountManager.get().isLogin()) {
                    onReportTrigger();
                } else {
                    startActivity(AccountIntentCreator.getIntent(activity));

                }
            }
        }).show();
    }

    private void onReportTrigger() {
        if (profileInformationData == null) {
            return;
        }
        String[] questions = {
                getString(R.string.its_posting_unoriginal_content),
                getString(R.string.its_a_duplicate_account),
                getString(R.string.its_spreading_false_information_or_hate_speech),
                getString(R.string.its_bullying_others),
                getString(R.string.rate_translation_fourth_question)
        };
        List<RateTranslationBean> list = new ArrayList<>();
        for (int i = 0; i < questions.length; i++) {
            RateTranslationBean bean = new RateTranslationBean();
            bean.pos = i;
            bean.name = questions[i];
            list.add(bean);
        }
        String tag = PostRateTranslationFragment.class.getSimpleName() + uid + "report";
        PostRateTranslationFragment fragment = (PostRateTranslationFragment) PostRateTranslationFragment.newInstance("report", "user", uid, list, getString(R.string.why_are_you_reporting_this_account), getString(R.string.report_account), profileInformationData.isBlocked());
        fragment.setOnClickListener(() -> {
            viewModel.postFollow(uid, "B", "Blocked");
            updateBlock();
            Toaster.showToast(getString(R.string.account_blocked));
        }).show(getSupportFragmentManager(), tag);
    }

    private void updateBlock() {
        binding.inProfileInfo.tvEdit.setText(getString(R.string.unblock));
        binding.inProfileInfo.ivProfileEdit.setVisibility(View.GONE);
        binding.inProfileInfo.llProfileEdit.setBackgroundResource(R.drawable.bg_profile_008ed6_bg_shape);
        binding.inProfileInfo.tvEdit.setTextColor(Color.parseColor("#ffffff"));
        binding.inProfileList.stickViewpager.setVisibility(View.GONE);
        profileInformationData.profile_status = "Blocked";
        FollowData data = new FollowData();
        data.uid = profileInformationData.user_info.uid;
        data.status = 0;
        EditPostManager.setFollowData(data);
        findViewById(R.id.ll_profile_bottom).setVisibility(View.GONE);
        findViewById(R.id.ll_profile_block_bottom).setVisibility(View.VISIBLE);
        binding.llProfileSmallFollow.setVisibility(View.GONE);
        appBarNotScroll();
    }

    private void showBlockDialog() {
        new BottomIosDialog(activity).setBottomData().setOnClickListener(() -> {
            viewModel.postFollow(uid, "B", "Blocked");
            updateBlock();
            Toaster.showToast(getString(R.string.account_blocked));
        }).show();
    }

    private void unBlockToast() {
        if (profileInformationData.isBlocked()) {
            Toaster.showToast(getString(R.string.account_unblocked));
        }
    }

    private void appBarScroll() {
        AppBarLayout.LayoutParams params = (AppBarLayout.LayoutParams) binding.stickTop.getLayoutParams();
        params.setScrollFlags(AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL | AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS_COLLAPSED);
        binding.stickTop.setLayoutParams(params);
    }

    private void appBarNotScroll() {
        AppBarLayout.LayoutParams params = (AppBarLayout.LayoutParams) binding.stickTop.getLayoutParams();
        params.setScrollFlags(AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED);
        binding.stickTop.setLayoutParams(params);
        binding.stickTop.setFocusable(true);
        binding.stickTop.setFocusableInTouchMode(true);
        binding.stickTop.requestFocus();
    }

}
