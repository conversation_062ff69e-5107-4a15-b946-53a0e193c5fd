package com.sayweee.weee.module.collection;

import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cate.widget.StatusView;
import com.sayweee.weee.module.cms.CmsBottledFragment;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsComponentRange;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.CmsPageParam;
import com.sayweee.weee.module.cms.iml.banner.data.CmsBannerItemData;
import com.sayweee.weee.module.cms.iml.banner.data.CmsBannerProperty;
import com.sayweee.weee.module.cms.iml.pagenav.data.CmsPageNavAction;
import com.sayweee.weee.module.cms.iml.pagenav.data.CmsPageNavData;
import com.sayweee.weee.module.cms.iml.product.ProductLineProvider;
import com.sayweee.weee.module.cms.widget.CmsBackgroundItemDecoration;
import com.sayweee.weee.module.collection.data.PageNavIndicatorData;
import com.sayweee.weee.module.collection.view.CollectionTitleHelper;
import com.sayweee.weee.module.collection.view.TitleStyle;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.home.adapter.ProductItemMoreAdapter;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.NestedRecyclerView;
import com.sayweee.weee.widget.SharedCartView;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.RecyclerViewPositionHelper;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.weee.widget.refresh.BetterLoadingMoreView;
import com.sayweee.widget.round.RoundImageView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.status.StatusBarProvider;
import com.sayweee.wrapper.http.ExceptionHandler;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.subjects.PublishSubject;

/**
 * 活动页
 */
public class CollectionFragment extends CmsBottledFragment<CollectionViewModel> {

    public static final String TAG = "com.sayweee.weee.module.collection.CollectionFragment";
    private String pageKey;
    private String pageName;

    public static CollectionFragment getIntent(String pageKey, String pageType, Map<String, String> params) {
        return getIntent(null, pageKey, pageType, params);
    }

    public static CollectionFragment getIntent(String url, String pageKey, String pageType, Map<String, String> params) {
        CollectionFragment fragment = new CollectionFragment();
        Bundle arguments = new Bundle();
        arguments.putString("url", url);
        arguments.putString("pageKey", pageKey);
        arguments.putString("pageType", pageType);
        arguments.putSerializable("params", params != null ? new HashMap<>(params) : null);
        fragment.setArguments(arguments);
        return fragment;
    }

    private NestedRecyclerView recyclerView;
    private SmartRefreshLayout refreshLayout;
    private View vShadow, vTitle, layoutTop;
    private ImageView ivBack, ivShare;
    private SharedCartView vCart;
    private TextView tvTitle;
    private View layoutRemindTips;
    private StatusView vNetworkError;
    private View layoutPageNav;
    private View vShadowPageNav;
    private CompatMagicIndicator indicatorPageNav;
    private View flPopBtn;
    private View ivPopBtn;

    private CollectionTitleHelper titleHelper;
    private RecyclerViewPositionHelper positionHelper;

    private WrapperDialog shareDialog;

    private boolean scrollToTopWhenReload = false;

    private ProductTraceObserver productTraceObserver;

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_collection;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        super.initView(view, savedInstanceState);
        StatusBarProvider.get().initStatusBar(this, findViewById(R.id.v_status), true);
        titleHelper = new CollectionTitleHelper();

        vShadow = findViewById(R.id.v_shadow);
        vTitle = findViewById(R.id.v_title);
        tvTitle = findViewById(R.id.tv_title);
        ivBack = findViewById(R.id.iv_back);
        ivShare = findViewById(R.id.iv_share);
        vCart = findViewById(R.id.v_cart);
        layoutTop = findViewById(R.id.layout_top);
        layoutRemindTips = getLightningDealsRemindLayout();
        vNetworkError = findViewById(R.id.v_network_error);
        layoutPageNav = findViewById(R.id.layout_page_nav);
        vShadowPageNav = findViewById(R.id.v_shadow_page_nav);
        indicatorPageNav = findViewById(R.id.indicator_page_nav);
        flPopBtn = findViewById(R.id.fl_pop_btn);
        ivPopBtn = findViewById(R.id.iv_pop_btn);
        updateTitleStyle(/* isTop= */true, /* isContentStickyTop= */false);

        ViewTools.setViewOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                onViewClick(v);
            }
        }, ivBack, ivShare, findViewById(R.id.layout_top));

        showVeilTemplate(true);
        setPageNavDisplay(false);
    }

    private void initProductTraceObserver() {
        if (productTraceObserver == null) {
            productTraceObserver = new ProductTraceObserver(this) {
                @Override
                protected void handleProductSalesTraceChange() {
                    ProductTraceViewHelper.notify(getRecyclerView());
                }
            };
            productTraceObserver.start();
        }
        if (viewModel != null) {
            productTraceObserver.setExtraTopic(viewModel.getProductTraceTopic());
        }
    }

    @NonNull
    @Override
    public NestedRecyclerView getRecyclerView() {
        if (recyclerView == null) {
            recyclerView = findViewById(R.id.mRecyclerView);
        }
        return recyclerView;
    }

    @Override
    protected void initRecyclerViewAdapter(Bundle savedInstanceState) {
        super.initRecyclerViewAdapter(savedInstanceState);
        addItemProvider();
        adapter.setPreLoadNumber(2);
        adapter.setLoadMoreView(new BetterLoadingMoreView(CommonTools.dp2px(100)));
        // fixme: breaks content feed
        // adapter.addFooterView(ViewTools.getHelperView(getRecyclerView(), R.layout.view_place, null));
    }

    @Override
    protected void initRecyclerView(NestedRecyclerView recyclerView) {
        super.initRecyclerView(recyclerView);
        recyclerView.addItemDecoration(new CmsBackgroundItemDecoration() {
            @Override
            protected boolean isSupportBackgroundStyle(int viewType, @Nullable Object item) {
                return viewType == CmsItemType.PRODUCT_ITEM;
            }
        });
        setRefreshConfig();
        if (positionHelper == null) {
            positionHelper = new RecyclerViewPositionHelper(recyclerView, RecyclerView.VERTICAL);
            positionHelper.setOffsetStart(getPositionHelperOffset());
        }
    }

    @Override
    protected int getNestedRecyclerViewStickyHeight() {
        int stickyHeight = CommonTools.getStatusBarHeight(getContext());
        stickyHeight += getResources().getDimensionPixelOffset(R.dimen.default_title_height);
        return stickyHeight;
    }

    private int getPositionHelperOffset() {
        int stickyHeight = CommonTools.getStatusBarHeight(getContext());
        stickyHeight += getResources().getDimensionPixelOffset(R.dimen.default_title_height);
        stickyHeight += getResources().getDimensionPixelOffset(R.dimen.default_nav_bar_height);
        return stickyHeight;
    }

    @Override
    public void loadData() {
        Bundle arguments = getArguments();
        if (arguments == null) {
            if (getActivity() != null) {
                getActivity().finish();
            }
            return;
        }
        String url = arguments.getString("url");
        pageKey = arguments.getString("pageKey");
        if (url != null && !url.trim().isEmpty()) {
            viewModel.setTargetUrl(url);
            viewModel.setPageTarget(pageKey);
            viewModel.fetchDataByCache();
        }
        Serializable params = arguments.getSerializable("params");
        Map<String, Serializable> cmsRequestParams = new HashMap<>();
        if (params instanceof HashMap) {
            cmsRequestParams.putAll((Map<? extends String, ? extends Serializable>) params);
        }
        if (url != null && !url.trim().isEmpty()) {
            if (!cmsRequestParams.containsKey("refer_page_url")) {
                cmsRequestParams.put("refer_page_url", url);
            }
        }
        Serializable urlFragment = cmsRequestParams.get("navbar");
        if (urlFragment instanceof String && !EmptyUtils.isEmpty((String) urlFragment)) {
            viewModel.setPageNavFragment((String) urlFragment);
        }

        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.CMS_ACTIVITY, String.valueOf(viewModel.hashCode()));
        initProductTraceObserver();

        viewModel.fetchCmsData(cmsRequestParams);
    }

    @Override
    protected boolean isLoadMoreEnable() {
        return true;
    }

    @Override
    public void onLoadMoreRequested() {
        viewModel.loadMoreData();
    }

    @Override
    public void attachModel() {
        super.attachModel();

        LifecycleOwner lifecycleOwner = getSafeViewLifecycleOwner();
        if (lifecycleOwner == null) {
            return;
        }
        SharedViewModel.get().collectsData.observe(lifecycleOwner, new Observer<Integer>() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onChanged(Integer integer) {
                if (adapter != null) {
                    adapter.refreshProductCollect();
                }
            }
        });
        viewModel.failureData.observe(lifecycleOwner, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                int errorCode = failureBean != null ? failureBean.getErrorCode() : ExceptionHandler.ERROR_UNKNOWN;
                if (ExceptionHandler.isConnectError(errorCode)) {
                    showNetworkError();
                } else {
                    fallback();
                }
            }
        });
        viewModel.getLoadMoreEndSignal().observe(lifecycleOwner, aBoolean -> {
            adapter.loadMoreEnd(true);
        });
        viewModel.getComponentConsumedSignal().observe(lifecycleOwner, aBoolean -> {
            adapter.notifyCmsPageNavDataChanged();
            recyclerView.postDelayed(() -> {
                notifyCmsPageNavProviderVeil(false);
            }, 50L);
            refreshPageNavPop();
            scrollToPageNavFragment(100L);
        });
        viewModel.shareData.observe(lifecycleOwner, new Observer<ShareBean>() {
            @Override
            public void onChanged(ShareBean shareBean) {
                if (shareDialog == null) {
                    shareDialog = new ShareDialog(activity)
                            .setShareData(shareBean);
                    shareDialog.show();
                } else {
                    if (!shareDialog.isShowing()) {
                        shareDialog.show();
                    }
                }
                trackShare();
            }
        });
        viewModel.cacheData.observe(lifecycleOwner, new Observer<CmsBannerItemData>() {
            @Override
            public void onChanged(CmsBannerItemData item) {
                if (item != null && item.isValid()) {
                    displayImageVeil(item);
                }
            }
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(lifecycleOwner, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                loadData();
                ViewTools.smoothQuicklyScrollToPosition(recyclerView);
            }
        });
    }

    @Override
    protected void onPageParamDataLoaded(@Nullable CmsPageParam pageParam) {
        super.onPageParamDataLoaded(pageParam);
        if (pageParam != null) {
            pageName = pageParam.page_name;
            tvTitle.setText(pageParam.page_name);
        }
    }

    @Override
    protected void onAdapterDataLoaded(@Nullable List<AdapterDataType> dataList) {
        // ensure product trace observer's topic is updated
        initProductTraceObserver();

        if (scrollToTopWhenReload) {
            scrollToTopWhenReload = false;
            recyclerView.post(() -> recyclerView.scrollToPosition(0));
        }

        int itemCount = CollectionUtils.size(dataList);
        if (itemCount == 0) {
            showEmptyView();
        }
        adapter.setNewData(dataList);
        adapter.loadMoreComplete();
        adapter.notifyPageDataSetChanged(recyclerView);
        setRefreshFinish();
        recyclerView.postDelayed(() -> {
            showVeilTemplate(false);
            LabelScrollHandler.notifyScrollStateChanged(recyclerView);
        }, 50L);
        if (viewModel.containsPageNavComponent()) {
            viewModel.loadMoreData();
        }
    }

    @Override
    protected void onAdapterDataAppend(@Nullable List<AdapterDataType> list) {
        if (list != null) {
            adapter.addData(list);
        }
        adapter.loadMoreComplete();
        adapter.notifyPageDataSetChanged(recyclerView);
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (getActivity() != null) {
            WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.CMS_ACTIVITY,
                    String.valueOf(getActivity().hashCode()));
        }
        String source = "activity_" + pageKey;
        AppAnalytics.logPageView(WeeeEvent.PageView.CMS_ACTIVITY, this, null, new TrackParams().put("source", source).get());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        adapter.onPageDestroy();
    }

    @Override
    protected void onCmsNavClick(int i, @Nullable CmsComponentRange.Item item) {
        selectIndicatorAndScrollRv(item);//click recycler view cms nav indicator & click recycler view cms nav pop up
    }

    private void onCmsNavPopClick(int i, CmsComponentRange.Item item) {
        notifyCmsPageNavProviderRotation();
        View view = getView();
        if (view != null) {
            view.postDelayed(() -> {
                onCmsNavClick(i, item);
            }, 250);
        }
    }

    private void onPageTopNavPopClick(int i, @Nullable CmsComponentRange.Item item) {
        selectIndicatorAndScrollRv(item);//click page top nav pop up
    }

    @Override
    protected void onCmsNavShowMoreClick(View view, List<CmsComponentRange.Item> items) {
        if (view == null || CollectionUtils.isEmpty(items)) {
            return;
        }
        List<PageNavIndicatorData> list = viewModel.toPageNavIndicatorData(items);
        list.get(0).select = true;
        view.postDelayed(() -> CmsPageNavPopHelper.showPopUpWindow(activity, view, list, R.layout.popup_cms_page_nav, this::onCmsNavPopClick, this::onCmsNavPopDismiss)
                , CmsPageNavPopHelper.DELAY_MILLIS);
    }

    private void onViewClick(View view) {
        int viewId = view != null ? view.getId() : View.NO_ID;
        if (viewId == R.id.layout_top) {
            ViewTools.smoothQuicklyScrollToPosition(recyclerView);
        } else if (viewId == R.id.iv_back) {
            if (getActivity() != null) {
                getActivity().finish();
            }
        } else if (viewId == R.id.iv_share) {
            viewModel.fetchShareData(pageKey);
        }
    }

    protected void updateTitleStyle(boolean isTop, boolean isContentStickyTop) {
        TitleStyle style = titleHelper.getTitleStyle(activity, isTop);
        ViewTools.setViewVisibilityIfChanged(vShadow, !isTop && !isContentStickyTop);
        ViewTools.setViewVisibilityIfChanged(tvTitle, !isTop);
        vTitle.setBackground(style.bgDrawable);
        ivBack.setImageDrawable(style.backDrawable);
        int padding = isTop ? 0 : CommonTools.dp2px(8);
        ViewTools.updatePaddings(ivBack, padding, padding, padding, padding);
        ivShare.setImageDrawable(style.shareDrawable);
        ivShare.setBackground(isTop ? style.cartBgDrawable : null);
        //ViewTools.updatePaddings(ivShare, padding, padding, padding, padding);
        vCart.setCartBackground(style.cartBgDrawable);
        vCart.setCartDrawable(style.cartDrawable);
    }

    private void updateErrorTitle() {
        updateTitleStyle(/* isTop= */false, /* isContentStickyTop= */false);
        ViewTools.setViewVisibilityIfChanged(tvTitle, false);
        ViewTools.setViewVisibilityIfChanged(ivShare, false);
        ViewTools.setViewVisibilityIfChanged(vShadow, false);
        ViewTools.setViewVisibilityIfChanged(layoutTop, false);
    }

    public void setRefreshConfig() {
        refreshLayout = findViewById(R.id.mSmartRefreshLayout);
        if (refreshLayout != null) {
            refreshLayout.setHeaderInsetStart(10);
            refreshLayout.setEnableRefresh(true);
            refreshLayout.setOnRefreshListener(new OnRefreshListener() {
                @Override
                public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                    loadData();
                }
            });
        }
    }

    public void setRefreshFinish() {
        if (refreshLayout != null) {
            refreshLayout.finishRefresh();
        }
    }

    private void displayImageVeil(CmsBannerItemData bean) {
        View view = findViewById(R.id.layout_veil);
        if (view != null && view.getVisibility() == View.VISIBLE) {
            ImageView imageView = view.findViewById(R.id.image);
            CmsBannerProperty property = bean.property;
            if (property != null) {
                int margin = CommonTools.dp2px(property.getParser().getHorizontalMargin());
                int verticalMargin = CommonTools.dp2px(property.getParser().getVerticalMargin());
                ViewTools.updatePaddings(imageView, margin, verticalMargin, margin, verticalMargin);
                if (imageView instanceof RoundImageView) {
                    ((RoundImageView) imageView).setRadius(CommonTools.dp2px(property.getParser().getRoundedCorner()));
                }
            }
            ImageLoader.load(activity, imageView, WebpManager.convert(ImageSpec.SPEC_375_AUTO, bean.t.img_url));
            imageView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onRecyclerViewScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
        boolean isTopMost = RecyclerViewTools.isTopMost(recyclerView);
        updateTitleStyle(/* isTop= */isTopMost, /* isContentStickyTop= */getRecyclerView().isStickyTop());
        int firstVisiblePosition = positionHelper.findFirstVisibleItemPosition();
        if (firstVisiblePosition != RecyclerView.NO_POSITION) {
            ViewTools.setViewVisibilityIfChanged(layoutTop, firstVisiblePosition > 3);
            setPageNavDisplay(recyclerView, firstVisiblePosition);
            updateCmsPageNav(firstVisiblePosition);
        }
    }

    private PublishSubject<Integer> firstPositionSubject;

    protected void updateCmsPageNav(int position) {
        if (firstPositionSubject == null) {
            firstPositionSubject = PublishSubject.create();
            firstPositionSubject
                    .distinctUntilChanged()
                    .subscribe(new SimpleObserver<Integer>() {
                        @Override
                        public void onNext(Integer integer) {
                            super.onNext(integer);
                            onCmsPageNavPositionChange(integer);
                        }
                    });
        }
        firstPositionSubject.onNext(position);
    }

    protected void onCmsPageNavPositionChange(int position) {
        CmsComponentRange.Item rangeItem = viewModel.findComponentRangeByPosition(position);
        if (rangeItem != null) {
            indicatorPageNav.handlePageSelected(rangeItem.navIndex);
        }
    }

    @Override
    protected void onLightningDealsReloadRequested(String componentId) {
        if (!EmptyUtils.isEmpty(componentId)) {
            CmsDataSource dataSource = viewModel.getDataSource(componentId);
            if (dataSource != null) {
                viewModel.requestMultiDataSource(dataSource, 1500L);
                return;
            }
        }
        loadData();
    }

    @Override
    protected void onCmsCountdownReloadRequested() {
        loadData();
        scrollToTopWhenReload = true;
    }

    @Nullable
    @Override
    protected View getLightningDealsRemindLayout() {
        if (layoutRemindTips == null) {
            layoutRemindTips = findViewById(R.id.layout_remind_tips);
            ViewTools.setViewOnSafeClickListener(
                    layoutRemindTips.findViewById(R.id.tv_remind_revoke),
                    v -> revokeRemind()
            );
        }
        return layoutRemindTips;
    }

    private void showVeilTemplate(boolean visible) {
        View view = getContentView();
        if (view != null) {
            View layoutVeil = view.findViewById(R.id.layout_veil);
            ViewTools.setViewVisibilityIfChanged(layoutVeil, visible);
            VeilTools.show(view.findViewById(R.id.vl_collection), visible);
        }
    }

    private void trackShare() {
        EagleTrackManger.get().trackEagleClickAction(
                "collection_page",
                -1,
                WeeeEvent.PageView.CMS_ACTIVITY,
                EagleTrackEvent.ClickType.SHARE
        );
    }

    @Override
    public IContentFeedSharedViewModel getContentFeedSharedViewModel() {
        return viewModel;
    }

    private void showEmptyView() {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        if (refreshLayout != null) {
            refreshLayout.setEnableRefresh(false);
        }

        recyclerView.clearOnScrollListeners();
        setRefreshFinish();
        updateErrorTitle();
        showVeilTemplate(false);
        int screenWidth = CommonTools.getWindowWidth(activity);
        View emptyView = ViewTools.getHelperView(activity, R.layout.common_empty, vh -> {
            if (vh.itemView instanceof LinearLayout) {
                ((LinearLayout) vh.itemView).setGravity(Gravity.CENTER_VERTICAL);
            }
            ImageView ivIcon = vh.getView(R.id.iv_empty);
            ViewTools.updateViewSize(ivIcon, CommonTools.dp2px(160), CommonTools.dp2px(102));
            ViewTools.updateMargins(ivIcon, null, 0, null, null);
            ivIcon.setImageResource(R.mipmap.ic_empty_collection);

            int gap = CommonTools.dp2px(16);
            TextView tvTitle = vh.getView(R.id.tv_oops);
            ViewTools.updateMargins(tvTitle, null, gap, null, null);
            ViewTools.applyTextStyleAndColor(tvTitle, R.style.style_fluid_root_heading_base, R.color.color_surface_1_fg_default_idle);
            tvTitle.setText(R.string.s_collection_empty_title);
            ViewTools.setViewVisibilityIfChanged(tvTitle, true);

            TextView tvSubtitle = vh.getView(R.id.tv_not_yet);
            ViewTools.updateMargins(tvSubtitle, null, gap, null, null);
            int hPadding = (int) (screenWidth * .2f);
            ViewTools.updatePaddings(tvSubtitle, hPadding, null, hPadding, null);
            ViewTools.applyTextStyleAndColor(tvSubtitle, R.style.style_fluid_root_body_base, R.color.color_surface_1_fg_minor_idle);
            tvSubtitle.setText(R.string.s_collection_empty_desc);
            ViewTools.setViewVisibilityIfChanged(tvSubtitle, true);

            TextView tvButton = vh.getView(R.id.tv_create);
            ViewTools.updateMargins(tvButton, null, gap, null, null);
            Integer buttonColorInt = ViewTools.getColorByResId(activity, R.color.color_primary_surface_1_bg_idle);
            if (buttonColorInt != null) {
                ShapeHelper.setBackgroundSolidDrawable(tvButton, buttonColorInt, CommonTools.dp2px(100));
            }
            ViewTools.applyTextStyleAndColor(tvButton, R.style.style_fluid_root_button_label_lg, R.color.color_primary_surface_1_fg_default_idle);
            tvButton.setText(R.string.continue_shopping);
            ViewTools.setViewVisibilityIfChanged(tvButton, true);
            ViewTools.setViewOnSafeClickListener(tvButton, v -> {
                SharedViewModel.get().toHome();
                activity.finish();
            });
        });
        adapter.setEmptyView(emptyView);
    }

    private void showNetworkError() {
        setRefreshFinish();
        updateErrorTitle();
        showVeilTemplate(false);
        ViewTools.setViewVisibilityIfChanged(vNetworkError, true);
        vNetworkError.showErrorView(v -> {
            ViewTools.setViewVisibilityIfChanged(vNetworkError, false);
            vNetworkError.resetStatus();
            showVeilTemplate(true);
            loadData();
        });
    }

    private void addItemProvider() {
        adapter.addItemProvider(new ProductLineProvider() {
            @NonNull
            @Override
            protected ProductItemMoreAdapter getAdapter() {
                return new ProductItemMoreAdapter(new ArrayList<>()) {
                    @Override
                    protected void convert(@NonNull AdapterViewHolder helper, Object data) {
                        super.convert(helper, data);
                        if (data instanceof ProductBean) {
                            ProductBean bean = (ProductBean) data;
                            boolean soldOut = ProductView.isSoldOut(bean.sold_status);
                            helper.setVisibleCompat(R.id.iv_collect, !soldOut);
                        }
                    }
                };
            }
        });
    }

    private void fallback() {
        CollectionFallbackFragment fragment = CollectionFallbackFragment.newInstance(pageKey, pageName);
        getParentFragmentManager().beginTransaction()
                .replace(R.id.fragment_container_view, fragment, CollectionFallbackFragment.class.getName())
                .commitAllowingStateLoss();
    }

    private void scrollToItem(@NonNull CmsComponentRange.Item item) {
        RecyclerView recyclerView = getRecyclerView();
        if (recyclerView.getLayoutManager() instanceof StaggeredGridLayoutManager) {
            ((StaggeredGridLayoutManager) recyclerView.getLayoutManager()).scrollToPositionWithOffset(item.firstPos, getPositionHelperOffset());
        } else {
            recyclerView.scrollToPosition(item.firstPos);
        }
    }

    private void onCmsNavPopDismiss() {
        notifyCmsPageNavProviderRotation();
        startRotation(ivPopBtn, 180f);
    }

    private void setPageNavDisplay(@NonNull RecyclerView recyclerView, int firstVisiblePosition) {
        if (adapter == null || recyclerView.getLayoutManager() == null) {
            setPageNavDisplay(false);
            return;
        }
        int index = CollectionUtils.indexOfFirst(adapter.getData(), CmsPageNavData.class::isInstance);
        boolean visible = index >= 0 && firstVisiblePosition >= index;
        if (visible && firstVisiblePosition == index) {
            visible = isCmsPageNavProviderSticky(recyclerView.getLayoutManager(), index);
        }
        CmsPageNavData cmsPageNavData = adapter.getFirstItemByClass(CmsPageNavData.class);
        boolean rvNavShow = cmsPageNavData != null && !cmsPageNavData.showVeil;
        setPageNavDisplay(visible & rvNavShow);
    }

    private boolean isCmsPageNavProviderSticky(@NonNull RecyclerView.LayoutManager layoutManager, int index) {
        View view = layoutManager.findViewByPosition(index);
        int heightCmsPageNavProvider = CommonTools.dp2px(58);
        return view != null && view.getY() < heightCmsPageNavProvider;
    }

    private void setPageNavDisplay(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.INVISIBLE;
        ViewTools.setViewVisibilityIfChanged(layoutPageNav, visibility);
        ViewTools.setViewVisibilityIfChanged(vShadowPageNav, visibility);
    }

    private void refreshPageNavPop() {
        //setPageNavData
        CmsPageNavData cmsPageNavData = adapter.getFirstItemByClass(CmsPageNavData.class);
        if (cmsPageNavData == null || CollectionUtils.isEmpty(cmsPageNavData.getRangeItems())) {
            return;
        }
        List<CmsComponentRange.Item> rangeItems = cmsPageNavData.getRangeItems();
        List<PageNavIndicatorData> list = viewModel.toPageNavIndicatorData(rangeItems);
        CmsPageNavPopHelper.fillNavIndicator(list, cmsPageNavData, activity, indicatorPageNav, (index, item) -> {
            selectIndicatorAndScrollRv(item);//click indicator
        });
        // pop up
        flPopBtn.setOnClickListener(v -> {
            Runnable r = () -> CmsPageNavPopHelper.showPopUpWindow(
                    activity,
                    indicatorPageNav,
                    list,
                    R.layout.popup_top_page_nav,
                    this::onPageTopNavPopClick,
                    this::onCmsNavPopDismiss
            );
            flPopBtn.postDelayed(r, CmsPageNavPopHelper.DELAY_MILLIS);
            startRotation(ivPopBtn, 0f);
        });
    }

    private void selectIndicatorAndScrollRv(@Nullable CmsComponentRange.Item item) {
        if (getActivity() == null) {
            return;
        }
        if (item == null || item.navIndex < 0 || item.firstPos < 0) {
            return;
        }
        indicatorPageNav.handlePageSelected(item.navIndex);
        scrollToItem(item);
        trackPageNavClick(item);
    }

    private void startRotation(View target, float currentRotation) {
        boolean to180degree = currentRotation == 0f;
        float targetRotation = to180degree ? 180f : 0f;
        ObjectAnimator rotationAnimator = ObjectAnimator.ofFloat(target, "rotation", currentRotation, targetRotation);
        rotationAnimator.setDuration(300);
        if (to180degree) {
            rotationAnimator.setStartDelay(CmsPageNavPopHelper.DELAY_MILLIS);
        }
        rotationAnimator.start();
    }

    private void trackPageNavClick(CmsComponentRange.Item item) {
        CmsPageNavData data = adapter.getFirstItemByClass(CmsPageNavData.class);
        String modNm = null;
        int modPos = 0;
        if (data != null) {
            modNm = data.getEventKey();
            modPos = data.position;
        }
        EagleTrackManger.get().trackEagleClickAction(modNm, modPos, null, -1,
                item.label,
                item.navIndex,
                EagleTrackEvent.TargetType.FILTER_BUTTON,
                EagleTrackEvent.ClickType.VIEW);
    }

    public void notifyCmsPageNavProviderRotation() {
        Object payload = new CmsPageNavAction.Rotation();
        adapter.notifyCmsPageNavProvider(payload);
    }

    public void notifyCmsPageNavProviderVeil(boolean show) {
        CmsPageNavAction.Veil veil = new CmsPageNavAction.Veil();
        veil.show = show;
        adapter.notifyCmsPageNavProvider(veil);
    }

    @SuppressWarnings("SameParameterValue")
    private void scrollToPageNavFragment(long delayMillis) {
        String componentInstanceId = viewModel.getPageNavFragment();
        if (componentInstanceId == null || componentInstanceId.isEmpty()) {
            return;
        }
        CmsComponentRange.Item item = viewModel.findComponentRangeByInstanceId(componentInstanceId);
        if (item != null && recyclerView != null) {
            if (delayMillis <= 0L) {
                selectIndicatorAndScrollRv(item);
            } else {
                recyclerView.postDelayed(() -> {
                    selectIndicatorAndScrollRv(item);
                }, delayMillis);
            }
        }
    }
}
