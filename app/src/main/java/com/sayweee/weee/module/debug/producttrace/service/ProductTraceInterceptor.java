package com.sayweee.weee.module.debug.producttrace.service;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class ProductTraceInterceptor implements Interceptor {

    private static final int DEFAULT_TIMEOUT_SEC = 60;

    public static ProductTraceInterceptor getInstance() {
        return getInstance(DEFAULT_TIMEOUT_SEC);
    }

    public static ProductTraceInterceptor getInstance(int timeoutSeconds) {
        return new ProductTraceInterceptor(timeoutSeconds);
    }

    private final int timeoutSeconds;

    private ProductTraceInterceptor(int timeout) {
        this.timeoutSeconds = timeout;
    }

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Request request = chain.request();
        if (ProductTraceManager.get().isEnabled()) {
            String path = request.url().encodedPath();
            if (ProductTraceApi.API_GET_PRODUCT_TRACE.equals(path)) {
                return chain.withReadTimeout(timeoutSeconds, TimeUnit.SECONDS).proceed(request);
            }
        }
        return chain.proceed(request);
    }
}
