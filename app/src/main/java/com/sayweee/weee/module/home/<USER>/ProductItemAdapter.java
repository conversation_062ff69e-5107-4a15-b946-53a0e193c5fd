package com.sayweee.weee.module.home.adapter;


import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.payload.RecyclerItemVisiblePositions;
import com.sayweee.weee.module.cart.adapter.OnCartEditListener;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.mkpl.LabelScrollAdapter;
import com.sayweee.weee.module.product.provider.PayloadKey;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewDisplayStrategy;
import com.sayweee.weee.widget.product.ProductViewElements;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.widget.tagflow.TagFlowLabelLayout;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/31.
 * Desc:
 */
public abstract class ProductItemAdapter
        extends BaseQuickAdapter<Object, AdapterViewHolder>
        implements EagleImpressionAdapter, ImpressionChild, LabelScrollAdapter {

    protected String source;    //加购source
    protected String module;    //埋点所用
    protected RecyclerView attachView; //所依附的view
    protected String modNm, secNm;//EagleTrack:埋点所用
    protected int modPos = -1, secPos = -1;//EagleTrack:埋点所用
    public String cateKey, catalogueNum, sortString, filter, pageTarget, pageTab, traceId, globalVendor;
    protected String relatedInfo;

    protected int cachedMinimumHeight = 0;

    @ProductView.DisplayStyle
    protected final int displayStyle;

    protected boolean showMkplVendor = true;
    protected OnCartEditListener onCartEditListener = null;

    public ProductItemAdapter(@Nullable List<ProductBean> list, @ProductView.DisplayStyle int displayStyle) {
        super(R.layout.item_product_item_small);
        this.displayStyle = displayStyle;
        setNewData((List) list);
    }

    @Override
    public void setNewData(@Nullable List<Object> data) {
        cachedMinimumHeight = 0;
        super.setNewData(data);
    }

    /**
     * 设置加购source
     *
     * @param source
     * @return
     */
    public ProductItemAdapter setProductSource(String source) {
        this.source = source;
        return this;
    }

    /**
     * 用于埋点标示当前所属module
     *
     * @param module
     */
    public ProductItemAdapter setAdapterModule(String module) {
        this.module = module;
        return this;
    }

    /**
     * 设置依附的view
     *
     * @param attachView
     * @return
     */
    @Override
    public void setAttachView(RecyclerView attachView) {
        this.attachView = attachView;
    }

    /**
     * 获取依附的view
     *
     * @return
     */
    @Override
    public RecyclerView getAttachView() {
        return attachView;
    }

    public ProductItemAdapter setOnCartEditListener(OnCartEditListener onCartEditListener) {
        this.onCartEditListener = onCartEditListener;
        return this;
    }

    public void setShowMkplVendor(boolean showMkplVendor) {
        this.showMkplVendor = showMkplVendor;
    }

    /**
     * EagleTrack:用于埋点标示当前所属module 信息
     */
    public ProductItemAdapter setModInfo(String mod_nm, int mod_pos) {
        this.modNm = mod_nm;
        this.modPos = mod_pos;
        return this;
    }

    /**
     * EagleTrack:用于埋点标示当前所属section 信息
     */
    public ProductItemAdapter setSecInfo(String sec_nm, int sec_pos) {
        this.secNm = sec_nm;
        this.secPos = sec_pos;
        return this;
    }

    /**
     * EagleTrack:用于埋点标示当前所属ctx 信息
     */
    public void setCtxInfo(String cateKey, String catalogueNum, String sortString, String filter) {
        this.cateKey = cateKey;
        this.catalogueNum = catalogueNum;
        this.sortString = sortString;
        this.filter = filter;
    }

    public void setCtxInfo(String cateKey, String catalogueNum, String sortString, String filter, String pageTarget, String pageTab) {
        setCtxInfo(cateKey, catalogueNum, sortString, filter);
        this.pageTarget = pageTarget;
        this.pageTab = pageTab;
    }

    public void setCtxInfo(String traceId) {
        this.traceId = traceId;
    }

    public void setCtxGlobalVendor(String globalVendor) {
        this.globalVendor = globalVendor;
    }

    protected void setItemMargin(AdapterViewHolder holder) {
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        if (layoutParams instanceof RecyclerView.LayoutParams) {
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
            int position = params.getViewLayoutPosition();
            params.leftMargin = CommonTools.dp2px(position == 0 ? 20 : 8);
            params.rightMargin = CommonTools.dp2px(position == getItemCount() - 1 ? 20 : 0);
        }
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setItemMargin(holder);

        try {
            int pos = holder.getBindingAdapterPosition();
            Object obj = mData.get(pos);
            if (obj instanceof ProductBean) {
                ProductBean bean = (ProductBean) obj;
                if (bean.ads_creative != null && !bean.ads_creative.isImpressionTracked) {
                    bean.ads_creative.isImpressionTracked = true;
                    AdsManager.get().trackImpression(
                            /* productBean= */bean,
                            /* position= */pos,
                            /* dbgInfo= */"ProductItemAdapter.onViewAttachedToWindow"
                    );
                }
            }
        } catch (Throwable e) {
            Logger.enable(DevConfig.isDebug()).e(e);
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, Object data) {
        if (data instanceof ProductBean) {
            convertProduct(helper, (ProductBean) data);
        }
    }

    protected void convertProduct(@NonNull AdapterViewHolder helper, ProductBean item) {
        helper.setTextSize(R.id.tv_remaining_tip, 11);
        ProductView productView = helper.getView(R.id.layout_product_view);
        int displayStyle = getDisplayStyle();
        if (displayStyle != ProductView.STYLE_ITEM_SMALL) {
            int viewWidth = getProductItemWidth(mContext, displayStyle);
            ViewTools.updateViewSize(helper.itemView, viewWidth, ViewGroup.LayoutParams.WRAP_CONTENT);
            if (productView.getCartOpLayout() != null) {
                productView.getCartOpLayout().setTipsMaxWidth(viewWidth - CommonTools.dp2px(20));
            }
        }
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .setSec_nm(secNm)
                .setSec_pos(secPos)
                .build()
                .getElement();

        final EagleContext finalCtx = new EagleContext();
        Map<String, String> filterMap;
        try {
            filterMap = JsonUtils.parseObject(filter, Map.class, new Type[]{String.class, String.class});
        } catch (Exception ignored) {
            filterMap = null;
        }
        finalCtx.putAll(cateKey, catalogueNum, sortString, filterMap, pageTarget, pageTab, null, globalVendor, traceId);
        finalCtx.setRelatedInfo(relatedInfo);
        item.prod_pos = helper.getLayoutPosition();
        productView.setShowMkplVendor(showMkplVendor);
        productView.setAttachedProduct(item, getDisplayStyle(), new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                if (onCartEditListener != null && onCartEditListener.isEnabled()) {
                    OpHelper.helperOpByDelegate(layoutOp, item, null, source, onCartEditListener, element, finalCtx.asMap());
                } else {
                    OpHelper.helperOp(layoutOp, item, source, element, finalCtx.asMap());
                }
            }
        });
        setDisplayMinimumHeight(productView);
        helper.setOnViewClickListener(R.id.layout_product, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                EagleContext ctx = new EagleContext(finalCtx);
                ctx.setVolumePriceSupport(item.volume_price_support);
                if (item.entrance_tag != null) {
                    ctx.setTagKey(item.entrance_tag.tag_key);
                    ctx.setTagName(item.entrance_tag.tag_name);
                }

                //pdp click action
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(modNm)
                        .setMod_pos(modPos)
                        .setSec_nm(secNm)
                        .setSec_pos(secPos)
                        .setTargetNm(String.valueOf(item.id))
                        .setTargetPos(helper.getLayoutPosition())
                        .setTargetType(EagleTrackEvent.TargetType.PRODUCT)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .setIsMkpl(item.isSeller())
                        .addCtx(ctx.asMap())
                        .build()
                        .getParams();

                AppAnalytics.logClickAction(params);

                if (item.ads_creative != null) {
                    try {
                        AdsManager.addAdsTrackPendingInfo(item);
                        AdsManager.get().trackClick(
                                /* productBean= */item,
                                /* position= */helper.getLayoutPosition(),
                                /* dbgInfo= */"ProductItemAdapter.onProductClick"
                        );
                    } catch (Exception e) {
                        Logger.enable(DevConfig.isDebug()).e(e);
                    }
                }

                productView.onProductClick(mContext, item);
            }
        });
        onCollectClick(helper, item, productView, finalCtx);
        helper.setOnViewClickListener(R.id.v_top_x_click_area, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (item.entrance_tag != null && !EmptyUtils.isEmpty(item.entrance_tag.more_link)) {
                    EagleContext ctx = new EagleContext(finalCtx);
                    ctx.setVolumePriceSupport(item.volume_price_support);
                    if (item.entrance_tag != null) {
                        ctx.setTagKey(item.entrance_tag.tag_key);
                        ctx.setTagName(item.entrance_tag.tag_name);
                    }
                    EagleTrackManger.get().trackEagleClickAction(
                            /* modNm= */modNm,
                            /* modPos= */modPos,
                            /* secNm= */secNm,
                            /* secPos= */secPos,
                            /* targetNm= */String.valueOf(item.id),
                            /* targetPos= */helper.getLayoutPosition(),
                            /* targetType= */EagleTrackEvent.TargetType.CHART,
                            /* clickType= */EagleTrackEvent.ClickType.VIEW,
                            /* ctx= */ctx.asMap()
                    );
                    ViewTools.toWeb(v, item.entrance_tag.more_link);
                }
            }
        });
        ProductTraceViewHelper.convert(
                helper.itemView,
                ProductTraceKey.of(item.id, item.recommendation_trace_id, ProductTraceKey.generateUniqueKey(modNm, secNm))
        );
    }

    protected void onCollectClick(@NonNull AdapterViewHolder helper, ProductBean item, ProductView productView, EagleContext finalCtx) {
        helper.setOnViewClickListener(R.id.iv_collect, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                productView.onCollectClick(v, item);
                boolean isCollect = CollectManager.get().isProductCollect(item.id);

                EagleContext ctx = new EagleContext(finalCtx);
                ctx.setVolumePriceSupport(item.volume_price_support);
                if (item.entrance_tag != null) {
                    ctx.setTagKey(item.entrance_tag.tag_key);
                    ctx.setTagName(item.entrance_tag.tag_name);
                }
                EagleTrackManger.get().trackEagleClickAction(
                        /* modNm= */modNm,
                        /* modPos= */modPos,
                        /* secNm= */secNm,
                        /* secPos= */secPos,
                        /* targetNm= */String.valueOf(item.id),
                        /* targetPos= */helper.getLayoutPosition(),
                        /* targetType= */EagleTrackEvent.TargetType.PRODUCT,
                        /* clickType= */isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE,
                        /* ctx= */ctx.asMap()
                );
            }
        });
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, Object item, @NonNull List<Object> payloads) {
        Object payload = CollectionUtils.firstOrNull(payloads);
        if (item instanceof ProductBean && PayloadKey.COLLECT.equals(payload)) {
            if (helper.getView(R.id.iv_collect).getVisibility() == View.VISIBLE) {
                ProductViewHelper.setProductCollect(helper.getView(R.id.iv_collect), CollectManager.get().isProductCollect(((ProductBean) item).id));
            }
            return;
        }
        if (payload instanceof RecyclerItemVisiblePositions) {
            int myPosition = helper.getBindingAdapterPosition() - getHeaderLayoutCount();
            RecyclerItemVisiblePositions positions = (RecyclerItemVisiblePositions) payload;
            TagFlowLabelLayout tagView = helper.getView(R.id.tfl_label);
            if (tagView != null) {
                if (positions.isVisible(myPosition)) {
                    tagView.startAutoScroll();
                } else {
                    tagView.stopAutoScroll();
                }
            }
        }
        ProductSyncHelper.convertPayloads(helper, item, payloads);
        if (ProductTraceViewHelper.shouldConvertPayload(payload) && item instanceof ProductBean) {
            ProductView productView = helper.getView(R.id.layout_product_view);
            ProductBean productBean = (ProductBean) item;
            if (productView != null) {
                ProductTraceViewHelper.convert(
                        helper.itemView,
                        ProductTraceKey.of(productBean.id, productBean.recommendation_trace_id, ProductTraceKey.generateUniqueKey(modNm, secNm)));
            }
        }
    }

    protected void setDisplayMinimumHeight(ProductView view) {
        if (view != null) {
            if (cachedMinimumHeight <= 0) {
                cachedMinimumHeight = getDisplayMinimumHeight(view.getContext(), view.getDisplayStrategy());
            }
            ViewTools.setMinimumHeight(view, cachedMinimumHeight);
        }
    }

    protected int getDisplayMinimumHeight(Context context, ProductViewDisplayStrategy strategy) {
        int displayStyle = getDisplayStyle();
        int baseHeight = getProductItemWidth(context, displayStyle);
        List<Object> items = CollectionUtils.newListOf(getData());
        ProductViewMinHeightCalculator calculator = new ProductViewMinHeightCalculator(baseHeight);
        int minHeight = baseHeight;
        for (Object it : items) {
            if (it instanceof ProductBean) {
                ProductBean bean = (ProductBean) it;
                ProductViewElements elements = strategy.getDisplayElements(bean);
                minHeight = Math.max(minHeight, calculator.getMinHeight(elements));
            }
        }
        minHeight += CommonTools.dp2px(8); // bottom extra margin
        return minHeight;
    }

    protected int getDisplayStyle() {
        return displayStyle;
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    protected ImpressionBean getEagleImpressionEvent(Object item) {
        if (item instanceof ProductBean) {
            ProductBean bean = (ProductBean) item;
            String productId = String.valueOf(bean.id);
            int pos = mData.indexOf(item);
            String key = pos + "_" + productId;
            Map<String, Object> element = EagleTrackManger.get().getElement(modNm, modPos, secNm, secPos);
            Map<String, String> filterMap;
            try {
                filterMap = JsonUtils.parseObject(filter, Map.class, new Type[]{String.class, String.class});
            } catch (Exception ignored) {
                filterMap = null;
            }
            Map<String, Object> ctx = new EagleContext()
                    .setFilterSubCategory(cateKey)
                    .setCatalogueNum(catalogueNum)
                    .setSort(sortString)
                    .setFilters(filterMap)
                    .setPageTarget(pageTarget)
                    .setPageTab(pageTab)
                    .setGlobalVendor(globalVendor)
                    .setBrandName(null)
                    .setTraceId(traceId)
                    .setRelatedInfo(relatedInfo)
                    .asMap();
            Map<String, Object> params = EagleFactory.getFactory(getDisplayStyle() == ProductView.STYLE_ITEM_SMALL ? EagleType.TYPE_ITEM_SMALL : EagleType.TYPE_ITEM_MEDIUM)
                    .setTarget(bean, pos)
                    .setElement(element)
                    .setContext(ctx)
                    .get();
            return new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key);
        }
        return null;
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        RecyclerItemVisiblePositions positions = RecyclerItemVisiblePositions.of(start, end);
        notifyItemRangeChanged(0, getItemCount(), positions);
    }

    public void setRelatedInfo(String relatedInfo) {
        this.relatedInfo = relatedInfo;
    }

    protected int getProductItemWidth(Context context, @ProductView.DisplayStyle int displayStyle) {
        return ProductViewHelper.getProductItemWidth(context, displayStyle);
    }
}
