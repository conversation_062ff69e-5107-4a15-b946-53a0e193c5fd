package com.sayweee.weee.module.product.provider;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.base.adapter.payload.RecyclerItemVisiblePositions;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.mkpl.LabelScrollHandler;
import com.sayweee.weee.module.mkpl.TrackingInfoAdapter;
import com.sayweee.weee.module.product.adapter.PdpProductItemAdapter;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.module.product.data.PdpProductsData;
import com.sayweee.weee.module.product.manager.UniformHeightLinearLayoutManager;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2023/5/9.
 * Desc:
 */
public class PdpProductLineProvider extends SimpleHorizontalImpressionProvider<PdpProductsData, AdapterViewHolder> implements TrackingInfoAdapter {

    public String filterSubCategory, pageTarget, sort;

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        setFullSpan(holder);
    }

    @Override
    public int getItemType() {
        return PdpItemType.PRODUCT_LINE;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_product_line;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);

        RecyclerView rv = helper.getView(R.id.rv_list);
        rv.setNestedScrollingEnabled(false);
        rv.setLayoutManager(new UniformHeightLinearLayoutManager(context));
        rv.setItemAnimator(null);
        PdpProductItemAdapter adapter = new PdpProductItemAdapter(new ArrayList<>(), ProductView.STYLE_ITEM_SMALL);
        adapter.setAttachView(rv);
        rv.setAdapter(adapter);
        rv.clearOnScrollListeners();
        rv.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onPageScrollStateChangedImpression(adapter);
                    LabelScrollHandler.notifyScrollStateChanged(recyclerView);
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });
        addAdapterToCache(adapter);
    }

    @Override
    public void convertPayloads(@NonNull AdapterViewHolder helper, PdpProductsData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        RecyclerView rv = helper.getView(R.id.rv_list);
        if (rv == null) {
            return;
        }
        PdpProductItemAdapter adapter = null;
        if (rv.getAdapter() instanceof PdpProductItemAdapter) {
            adapter = (PdpProductItemAdapter) rv.getAdapter();
        }
        if (adapter == null) {
            return;
        }
        for (Object payload : payloads) {
            if (payload instanceof String && PayloadKey.COLLECT.equalsIgnoreCase((String) payload)) {
                adapter.notifyItemRangeChanged(0, adapter.getItemCount(), PayloadKey.COLLECT);
            } else if (payload instanceof RecyclerItemVisiblePositions) {
                int myPosition = helper.getBindingAdapterPosition() - adapter.getHeaderLayoutCount();
                RecyclerItemVisiblePositions positions = (RecyclerItemVisiblePositions) payload;
                if (positions.isVisible(myPosition)) {
                    LabelScrollHandler.notifyScrollStateChanged(rv);
                } else {
                    adapter.notifyItemRangeChanged(0, adapter.getItemCount(), RecyclerItemVisiblePositions.EMPTY);
                }
            } else if (ProductTraceViewHelper.shouldConvertPayload(payload)) {
                ProductTraceViewHelper.notify(rv);
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpProductsData item) {
        helper.itemView.setPadding(helper.itemView.getPaddingLeft(), helper.itemView.getPaddingTop(), helper.itemView.getPaddingRight(), CommonTools.dp2px(8));
        PdpProductsBean bean = item.t;
        helper.setVisibleCompat(R.id.layout_view_all, item.vendorId > 0 || item.hasMoreLink());

        helper.setText(R.id.tv_title, bean.title);
        ViewTools.applyTextStyleAndColor(helper.getView(R.id.tv_title), R.style.style_body_base_medium, R.color.color_surface_100_fg_default);
        helper.setOnViewClickListener(R.id.v_title, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Context context = v.getContext();
                if (context == null) {
                    return;
                }
                if (item.hasMoreLink()) {
                    tractClickAction(item);
                    context.startActivity(WebViewActivity.getIntent(context, bean.more_link));
                } else if (item.vendorId > 0) {
                    tractClickAction(item);
                    context.startActivity(SellerActivity.getIntent(context, String.valueOf(item.vendorId)));
                }
            }
        });

        RecyclerView rv = helper.getView(R.id.rv_list);
        RecyclerView.Adapter<?> a = rv.getAdapter();
        if (a instanceof PdpProductItemAdapter) {
            PdpProductItemAdapter adapter = (PdpProductItemAdapter) a;
            //tracking info
            adapter.setModInfo(item.modNm, item.modPos);//item line
            adapter.setCtxInfo(filterSubCategory, null, sort, null, pageTarget, null);
            adapter.setCtxInfo(item.traceId);
            adapter.setProductSource(!EmptyUtils.isEmpty(item.source) ? item.source : "app_product-" + item.modNm + pageTarget);
            adapter.setAdapterModule(bean.module_key);
            adapter.setProductData(bean.product_list);
            adapter.setMoreData(item.hasMoreLink(), bean.more_link);

            LabelScrollHandler.notifyAdapterDataChanged(rv);
        }
    }

    @Override
    public void notifyPageDataSetChanged(RecyclerView view) {

    }

    @Override
    public void onPageResumeImpression(BaseQuickAdapter adapter) {
        super.onPageResumeImpression(adapter);
        ProductSyncHelper.onPageResume(adapter);
    }

    @Override
    public void onCtxAdded(String filterSubCategory, String catalogueNum, String sort, Map<String, String> filters, String pageTarget, String pageTab, String globalVendor) {
        this.filterSubCategory = filterSubCategory;
        this.pageTarget = pageTarget;
        this.sort = sort;
    }

    private void tractClickAction(PdpProductsData item) {
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm = */item.modNm,
                /* modPos = */item.modPos,
                /* secNm = */null,
                /* secPos = */-1,
                /* targetNm = */"explore_more",
                /* targetPos = */-1,
                /* targetType = */null,
                /* clickType = */EagleTrackEvent.ClickType.VIEW,
                /* ctx = */new EagleContext().setTraceId(item.traceId).asMap()
        );
    }

}
