package com.sayweee.weee.module.home.provider.product.data;

import androidx.annotation.NonNull;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.module.home.bean.CollectionBean;
import com.sayweee.weee.module.home.bean.CollectionProperty;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2023/6/5.
 * Desc:
 */
public class CmsCollectionItemData extends ComponentData<CollectionBean.DealsBean, CollectionProperty>
        implements ProductTraceTask.SectionProvider {

    public int secPos;

    public CmsCollectionItemData() {
        super(CmsItemType.COLLECTION_ITEM);
    }

    @Override
    public boolean isValid() {
        if (t == null) {
            return false;
        }
        if (!filter) {
            return !EmptyUtils.isEmpty(t.products) || !EmptyUtils.isEmpty(t.pre_sell_products);
        }
        boolean normalValid = ProductHelper.filterReachLimitValid(t.products, 2);
        boolean preSellValid = ProductHelper.filterReachLimitValid(t.pre_sell_products, 2);
        return normalValid || preSellValid;
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        if (isValid()) {
            List<AdapterDataType> list = new ArrayList<>();
            list.add(this);
            return list;
        }
        return null;
    }

    @Override
    public String getEventKey() {
        return property != null && property.event_key != null ? property.event_key : componentKey;
    }

    public List<ProductBean> getAllProduct() {
        ArrayList<ProductBean> list = new ArrayList<>();
        if (!EmptyUtils.isEmpty(t.products)) {
            list.addAll(t.products);
        }
        if (!EmptyUtils.isEmpty(t.pre_sell_products)) {
            for (int i = 0; i < t.pre_sell_products.size(); i++) {
                ProductBean bean = t.pre_sell_products.get(i);
                if (bean != null) {
                    bean.sold_status = Constants.ProductStatus.PRE_SELL;
                    list.add(bean);
                }
            }
        }
        return list;
    }

    // =========================================
    // Implementation of ProductSalesTraceTask.SectionProvider interface
    // =========================================
    @Override
    public void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder) {
        if (isValid()) {
            if (!EmptyUtils.isEmpty(t.products)) {
                for (ProductBean product : t.products) {
                    if (product != null) {
                        builder.add(product.recommendation_trace_id, product.id);
                    }
                }
            }
            if (!EmptyUtils.isEmpty(t.pre_sell_products)) {
                for (ProductBean product : t.products) {
                    if (product != null) {
                        builder.add(product.recommendation_trace_id, product.id);
                    }
                }
            }
            builder.setModNm(getEventKey());
            builder.setUniqueKey(ProductTraceKey.generateUniqueKey(getEventKey(), t.key));
        }
    }
}
