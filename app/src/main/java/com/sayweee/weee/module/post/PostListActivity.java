package com.sayweee.weee.module.post;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.post.adapter.PostMoreAdapter;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.bean.ReviewVisibleBean;
import com.sayweee.weee.module.post.detail.ReviewDetailActivity;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.module.post.review.ReviewEditActivity;
import com.sayweee.weee.module.post.review.bean.ToReviewConst;
import com.sayweee.weee.module.post.service.PostViewModel;
import com.sayweee.weee.module.product.service.ProductOpHelper;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.player.bean.MediaBean;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.MaxLinesFlowLayout;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.op.BottomOpLayout;
import com.sayweee.widget.shape.ShapeLinearLayout;
import com.sayweee.widget.toaster.IToaster;
import com.sayweee.widget.toaster.IToasterController;
import com.sayweee.widget.toaster.IToasterOptions;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.listener.OnViewHelper;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author:  ycy
 * Desc:    产品详情===>more post列表页
 */
public class PostListActivity extends WrapperMvvmActivity<PostViewModel> implements IToasterController {

    private int page = 0;
    private String productId;
    private PostMoreAdapter adapter;
    private String sortKey, sortName;
    private List<PostCategoryBean.SortBean> sortBeanList = new ArrayList<>();
    private TextView tvCount;
    private View vShadow;
    private String usp_id;
    private String reviewId;
    private String wordCloud;
    private CompatMagicIndicator tabHashTags;
    private boolean isFirst = true;
    private TextView tvSort, tvSort2;
    private BottomOpLayout layoutOp;

    private WrapperDialog dialog;
    private AlphaAnimation animation;
    private boolean haveWord;
    private String productKey;
    private int num = -1;
    private long orderId;
    private ProductBean product;
    private ProductDetailBean detail;
    private String cartSource;
    private String traceId;
    private int min;
    private int max;
    private int volumeThreshold;
    private String sold_status;
    private boolean disableOtherPage;
    private ProductBean productBean;
    private RecyclerView rvList;

    // review list 和 detail 不接 traceId， traceId 是从搜索到 pdp 的跟踪
    public static Intent getIntent(Context context, String productId, String cartSource, boolean disableOtherPage, ProductBean product) {
        return new Intent(context, PostListActivity.class)
                .putExtra("productId", productId)
                .putExtra("cartSource", cartSource)
                .putExtra("productBean", product)
                .putExtra("disableOtherPage", disableOtherPage);
    }

    public static Intent getIntent(Context context, String productId, String usp_id, String reviewId, String wordCloud) {
        return new Intent(context, PostListActivity.class).putExtra("productId", productId)
                .putExtra("usp_id", usp_id)
                .putExtra("review_id", reviewId)
                .putExtra("word_cloud", wordCloud);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_reviews_list;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        //product Id
        productId = getIntent().getStringExtra("productId");
        usp_id = getIntent().getStringExtra("usp_id");
        reviewId = getIntent().getStringExtra("review_id");
        wordCloud = getIntent().getStringExtra("word_cloud");
        productKey = getIntent().getStringExtra("productKey");
        cartSource = getIntent().getStringExtra("cartSource");
        if (cartSource == null) {
            cartSource = Constants.Source.PRODUCT_PDP_REVIEW_LIST;
        }
        traceId = getIntent().getStringExtra("traceId");
        disableOtherPage = getIntent().getBooleanExtra("disableOtherPage", false);
        Serializable serializable = getIntent().getSerializableExtra("productBean");
        if (serializable instanceof ProductBean) {
            productBean = (ProductBean) serializable;
        }
        //more post 列表
        rvList = findViewById(R.id.rv_list);
        tvCount = findViewById(R.id.tv_reviews_count);
        rvList.setLayoutManager(new LinearLayoutManager(activity));
        adapter = new PostMoreAdapter();
        vShadow = findViewById(R.id.v_shadow);
        tabHashTags = findViewById(R.id.tab_hash_tags);
        tvSort = findViewById(R.id.tv_sort);
        tvSort2 = findViewById(R.id.tv_sort2);
        layoutOp = findViewById(R.id.layout_op);
        rvList.setAdapter(adapter);
        //click
        setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (v.getId() == R.id.iv_back) {
                    finish();
                } else if (v.getId() == R.id.layout_sort || v.getId() == R.id.layout_sort2) {
                    if (dialog == null) {
                        showBottomDialog();
                    } else {
                        dialog.show();
                    }
                } else if (v.getId() == R.id.layout_add_review) {
                    if (!EmptyUtils.isEmpty(productBean)) {
                        startActivity(ReviewEditActivity.getIntent(activity, orderId, productBean, ToReviewConst.REVIEW_LIST_SOURCE));
                    } else {
                        startActivity(ReviewEditActivity.getIntent(activity, orderId, !EmptyUtils.isEmpty(productId) ? DecimalTools.parseInt(productId) : 0, ToReviewConst.REVIEW_LIST_SOURCE, false, null));
                    }
                }
            }
        }, R.id.iv_back, R.id.layout_sort, R.id.layout_sort2, R.id.layout_add_review);
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @SuppressLint("NonConstantResourceId")
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter quickAdapter, View view, int position) {
                AdapterDataType item = adapter.getItem(position);
                if (item instanceof PostCategoryBean.ListBean) {
                    PostCategoryBean.ListBean bean = (PostCategoryBean.ListBean) item;
                    switch (view.getId()) {
                        case R.id.layout_post: //商品
                            toReviewDetail(bean);
                            break;
                        case R.id.iv_user:
                        case R.id.tv_user:
                        case R.id.iv_badge:
                        case R.id.tv_verified_buyer:
                            startActivity(ProfileActivity.getIntent(activity, null, bean.uid));
                            break;
                    }
                }
            }

            @Override
            public void onItemChildClick(BaseQuickAdapter quickAdapter, View view, int position) {
                AdapterDataType item = adapter.getItem(position);
                if (item instanceof PostCategoryBean.ListBean) {
                    toReviewDetail((PostCategoryBean.ListBean) item);
                }
            }
        });
        //loadMore
        adapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                getList(true, ++page, sortKey);
            }
        }, rvList);

        final AppBarLayout appBarLayout = findViewById(R.id.abl_event);
        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                int absVerticalOffset = Math.abs(verticalOffset);
                boolean titleViewVisible = absVerticalOffset > 0;
                boolean tabCanVisible = titleViewVisible && findViewById(R.id.layout_word).getVisibility() == View.VISIBLE;
                vShadow.setVisibility(titleViewVisible ? View.VISIBLE : View.GONE);
//                ViewTools.setViewVisible(tabHashTags, tabCanVisible);
//                ViewTools.setViewVisible(findViewById(R.id.view), tabCanVisible);
//                ViewTools.setViewVisible(findViewById(R.id.view_hashtag), tabCanVisible);
                if (absVerticalOffset > 0 && absVerticalOffset < CommonTools.dp2px(40)) {
                    findViewById(R.id.layout_top).setAlpha(0.8f);
                    tabHashTags.setAlpha(0.5f);
                } else if (absVerticalOffset >= CommonTools.dp2px(40) && absVerticalOffset < CommonTools.dp2px(80)) {
                    findViewById(R.id.layout_top).setAlpha(0.5f);
                    tabHashTags.setAlpha(0.8f);
                } else {
                    tabHashTags.setAlpha(1f);
                }
                if (absVerticalOffset <= 0) {
                    findViewById(R.id.layout_top).setAlpha(1f);
                }

            }
        });

        rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                boolean isTop = recyclerView.canScrollVertically(-1);
                boolean visible = isTop && !haveWord;
                ViewTools.setViewVisible(findViewById(R.id.v_shadow), visible);
//                ViewTools.setViewVisible(findViewById(R.id.v_sort), visible);
            }
        });
        ViewTools.setViewVisibilityIfChanged(layoutOp, false);
    }

    private void toReviewDetail(PostCategoryBean.ListBean bean) {
        AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_WITHIN_PDP_CLICK,
                new TrackParams().put("post_id ", bean.id)
                        .put("content_type", bean.status).get());
        startActivity(ReviewDetailActivity.getIntent(activity, bean.id, cartSource, traceId, product));
    }


    @Override
    public void loadData() {
        refreshPage(false);
        if (!EmptyUtils.isEmpty(productId)) {
            viewModel.getProductDetail(Integer.parseInt(productId), false);
            viewModel.getReviewVisible(productId);
        }
    }

    @Override
    public void attachModel() {
        viewModel.postCategoryData.observe(this, new Observer<PostCategoryBean>() {
            @SuppressLint("SetTextI18n")
            @Override
            public void onChanged(PostCategoryBean bean) {
                if (page <= 1) {
                    tvCount.setText(" (" + bean.total + ")");
                    if (!EmptyUtils.isEmpty(bean.sort) && EmptyUtils.isEmpty(sortBeanList)) {
                        Collections.reverse(sortBeanList);
                        sortBeanList.addAll(bean.sort);
                        for (PostCategoryBean.SortBean item : bean.sort) {
                            if (item.checked) {
                                tvSort.setText(item.sort_name);
                                tvSort2.setText(item.sort_name);
                            }
                        }
                    }
                    adapter.setAdapterData(bean.list);
                    if (isFirst) {
                        if (bean.ai_sell_points == null) {
                            bean.ai_sell_points = new ArrayList<>();
                        }
                        PostCategoryBean.WordCloudBean wordCloudBean = new PostCategoryBean.WordCloudBean();
                        bean.ai_sell_points.add(0, wordCloudBean);
                        setWordCloud(findViewById(R.id.layout_word), bean);
                        isFirst = false;
                    }
                } else {
                    adapter.appendAdapterData(bean.list);
                }
                if (!EmptyUtils.isEmpty(bean.list)) {
                    adapter.loadMoreComplete();
                } else {
                    adapter.loadMoreEnd();
                }
            }
        });
        //post点赞联动
        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                adapter.toggleCollect(map);
            }
        });

        viewModel.productDetailData.observe(this, new Observer<ProductDetailBean>() {
            @Override
            public void onChanged(ProductDetailBean detailBean) {
                detail = detailBean;
                product = detail.product;
                productKey = product.product_key;
                setProductOpConfig();
            }
        });

        viewModel.reviewVisibleData.observe(this, new Observer<ReviewVisibleBean>() {
            @Override
            public void onChanged(ReviewVisibleBean reviewVisibleBean) {
                ViewTools.setViewVisible(findViewById(R.id.layout_add_review), reviewVisibleBean.show);
                orderId = reviewVisibleBean.order_id;
            }
        });
        SharedViewModel.get().updateWriteReviewStatus.observeForever(observer);

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                viewModel.getProductDetail(Integer.parseInt(productId), false);
            }
        });
    }

    private final Observer<Boolean> observer = new Observer<Boolean>() {
        @Override
        public void onChanged(Boolean aBoolean) {
            viewModel.getReviewVisible(productId);
        }
    };

    @Override
    protected void onResume() {
        super.onResume();
        if (num >= 0) {
            if (!EmptyUtils.isEmpty(productId)) {
                ProductSyncHelper.onPageResume(layoutOp, Integer.parseInt(productId), productKey, min, max);
            }
        }
    }


    private void refreshPage(boolean isSilent) {
        page = 0;
        getList(isSilent, ++page, sortKey);
    }

    private void getList(boolean isSilent, int page, String sort) {
        boolean hasMid = "relevance".equalsIgnoreCase(sort) || EmptyUtils.isEmpty(sort);
        viewModel.getReviewList(isSilent, productId, page, 20, sort, hasMid ? reviewId : null, usp_id, wordCloud);
    }

    private void updateBottomList(LinearLayout llContent, View btn) {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        int i = -1;
        final String[] sk = new String[1];
        for (PostCategoryBean.SortBean child : sortBeanList) {
            i++;
            llContent.addView(ViewTools.getHelperView(llContent, R.layout.item_reviews_new_tab, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    TextView tv = helper.getView(R.id.tv_title);
                    tv.setText(child.sort_name);
                    ImageView ivChecked = helper.getView(R.id.iv_checked);
//                    helper.setVisible(R.id.view, finalI != sortBeanList.size() - 1);
                    if (child.checked) {
                        ivChecked.setImageResource(R.drawable.drawable_radio_checked);
                    } else {
                        ivChecked.setImageResource(R.drawable.drawable_radio_empty);
                    }
                    if (child.checked) {
                        sortKey = child.sort_key;
                    }
                    helper.setOnClickListener(R.id.layout_checked, new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            if (!EmptyUtils.isEmpty(sk[0]) && sk[0].equalsIgnoreCase(child.sort_key)) {
                                return;
                            }
                            sk[0] = child.sort_key;
                            for (int j = 0; j < sortBeanList.size(); j++) {
                                if (!sk[0].equalsIgnoreCase(sortBeanList.get(j).sort_key)) {
                                    View view = llContent.getChildAt(j);
                                    if (view != null) {
                                        ImageView ivChecked = view.findViewById(R.id.iv_checked);
                                        ivChecked.setImageResource(R.drawable.drawable_radio_empty);
                                    }
                                }
                            }
                            ImageView ivChecked = helper.getView(R.id.iv_checked);
                            ivChecked.setImageResource(R.drawable.drawable_radio_checked);
                            sortName = child.sort_name;
                        }
                    });
                    btn.setOnClickListener(new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            sortKey = sk[0];
                            tvSort.setText(sortName);
                            tvSort2.setText(sortName);
                            MaxLinesFlowLayout flowLayout = findViewById(R.id.layout_word);
                            View childView = flowLayout.getChildAt(0);
                            if (childView instanceof ShapeLinearLayout) {
                                ShapeLinearLayout layout = (ShapeLinearLayout) childView;
                                TextView tv = layout.findViewById(R.id.tv_sort2);
                                if (tv != null) {
                                    tv.setText(sortName);
                                }
                            }
                            refreshPage(true);
                            int index = CollectionUtils.indexOfFirst(sortBeanList, sortBean
                                    -> (sortKey != null && sortKey.equalsIgnoreCase(sortBean.sort_key)));
                            trackClickAction(index);
                            rvList.scrollToPosition(0);
                            dialog.dismiss();
                        }
                    });
                }
            }), params);
        }
    }

    private void trackClickAction(int position) {
        Map<String, Object> map = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(productId), null);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder().addCtx(map)
                .setMod_nm(EagleTrackEvent.TargetType.SORT_FILTER)
                .setMod_pos(0)
                .setSec_nm(null)
                .setSec_pos(-1)
                .setTargetNm(sortKey)
                .setTargetPos(position)
                .setTargetType(EagleTrackEvent.TargetType.FILTER_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private void setWordCloud(MaxLinesFlowLayout layoutHashtags, PostCategoryBean bean) {
        if (!EmptyUtils.isEmpty(bean.ai_sell_points)) {
            layoutHashtags.setMaxLine(3);
            layoutHashtags.setVisibility(View.VISIBLE);
            layoutHashtags.removeAllViews();
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            params.rightMargin = CommonTools.dp2px(8);
            params.topMargin = CommonTools.dp2px(8);
            int i = -1;
            for (PostCategoryBean.WordCloudBean item : bean.ai_sell_points) {
                i++;
                int finalI = i;
                layoutHashtags.addView(ViewTools.getHelperView(layoutHashtags, i == 0 ? R.layout.layout_review_sort : R.layout.item_word_cloud, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper helper) {
                        if (finalI == 0) {
                            for (PostCategoryBean.SortBean item : bean.sort) {
                                if (item.checked) {
                                    helper.setText(R.id.tv_sort2, item.sort_name);
                                }
                            }
                            helper.itemView.setOnClickListener(new OnSafeClickListener() {
                                @Override
                                public void onClickSafely(View v) {
                                    if (dialog == null) {
                                        showBottomDialog();
                                    } else {
                                        dialog.show();
                                    }
                                }
                            });
                        } else {
                            boolean checked;
                            checked = String.valueOf(item.usp_id).equalsIgnoreCase(usp_id);
                            TextView tvTitle = helper.getView(R.id.tv_title);
                            TextView tvCount = helper.getView(R.id.tv_count);
                            TextView tvDot = helper.getView(R.id.tv_dot);
                            ShapeLinearLayout layout = helper.getView(R.id.layout_cloud);
                            setTagStyle(tvDot, tvTitle, tvCount, layout, checked);
                            helper.setText(R.id.tv_title, item.word);
                            helper.setText(R.id.tv_count, item.count_label);
                            helper.itemView.setOnClickListener(new OnSafeClickListener() {
                                @Override
                                public void onClickSafely(View v) {
                                    if (!EmptyUtils.isEmpty(usp_id) && usp_id.equalsIgnoreCase(String.valueOf(item.usp_id))) {
                                        usp_id = "";
                                    } else {
                                        usp_id = String.valueOf(item.usp_id);
                                    }
                                    setHashTags(bean);
                                    for (int j = 1; j < bean.ai_sell_points.size(); j++) {
                                        if (j != finalI || EmptyUtils.isEmpty(usp_id)) {
                                            View view = layoutHashtags.getChildAt(j);
                                            if (view != null) {
                                                TextView tvTitle = view.findViewById(R.id.tv_title);
                                                TextView tvCount = view.findViewById(R.id.tv_count);
                                                TextView tvDot = view.findViewById(R.id.tv_dot);
                                                ShapeLinearLayout layout = view.findViewById(R.id.layout_cloud);
                                                setTagStyle(tvDot, tvTitle, tvCount, layout, false);
                                            }
                                        }
                                    }
                                    TextView tvTitle = helper.getView(R.id.tv_title);
                                    TextView tvCount = helper.getView(R.id.tv_count);
                                    TextView tvDot = helper.getView(R.id.tv_dot);
                                    ShapeLinearLayout layout = helper.getView(R.id.layout_cloud);
                                    if (!EmptyUtils.isEmpty(usp_id)) {
                                        setTagStyle(tvDot, tvTitle, tvCount, layout, true);
                                    }
                                    refreshPage(true);
                                    clickTrack(productId, item.word, finalI - 1);
                                    rvList.scrollToPosition(0);
                                }
                            });
                        }
                    }
                }), params);
            }
        } else {
            layoutHashtags.setVisibility(View.GONE);
            findViewById(R.id.v_line).setVisibility(View.GONE);
        }
    }

    private void setTagStyle(TextView tvDot, TextView tvTitle, TextView tvCount, ShapeLinearLayout layout, boolean checked) {
        tvTitle.setTextColor(ContextCompat.getColor(activity, checked ? R.color.color_surface_100_bg : R.color.color_shade_cool_dark_4));
        tvCount.setTextColor(ContextCompat.getColor(activity, checked ? R.color.color_surface_100_bg : R.color.color_shade_cool_dark_4));
        tvDot.setTextColor(ContextCompat.getColor(activity, checked ? R.color.color_surface_100_bg : R.color.color_shade_cool_dark_4));
        if (checked) {
            layout.setBackgroundSolidDrawable(ContextCompat.getColor(activity, R.color.color_primary_atmosphere_blue), CommonTools.dp2px(12));
        } else {
            layout.setBackgroundStrokeDrawable(ContextCompat.getColor(activity, R.color.color_surface_300_bg), CommonTools.dp2px(1f), CommonTools.dp2px(12));
        }
    }

    private void setHashTags(PostCategoryBean bean) {
        final List<PostCategoryBean.WordCloudBean> children = bean.word_cloud;
        final Activity activity = this.activity;
        if (activity == null) return;
        if (EmptyUtils.isEmpty(children)) return;
        CommonNavigator commonNavigator = new CommonNavigator(activity);
        commonNavigator.setFollowTouch(true);
        MaxLinesFlowLayout flowLayout = findViewById(R.id.layout_word);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return flowLayout.getTotalByLine(3);
            }

            @Override
            public IPagerTitleView getTitleView(final Context context, final int index) {
                final PostCategoryBean.WordCloudBean listBean = children.get(index);
                CommonPagerTitleView layout = new CommonPagerTitleView(context);
                View child = LayoutInflater.from(context).inflate(R.layout.item_word_cloud, layout, false);
                final TextView tvName = child.findViewById(R.id.tv_title);
                final TextView tvCount = child.findViewById(R.id.tv_count);
                final ShapeLinearLayout layoutWord = child.findViewById(R.id.layout_cloud);
                tvName.setText(EmptyUtils.isEmpty(listBean.word) ? getString(R.string.s_title_all) : listBean.word);
                ViewTools.setViewVisible(tvCount, !EmptyUtils.isEmpty(listBean.count));
                tvCount.setText(listBean.count_label);
                layout.setPadding(CommonTools.dp2px(index == 0 ? 20 : 10), 0, index == flowLayout.getTotalByLine(3) - 1 ? CommonTools.dp2px(20) : 0, 0);
                child.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (tabHashTags.getIndicatorHelper().getSelectedIndex() != index) {
                            tabHashTags.getIndicatorHelper().setPageSelected(index);
                            tabHashTags.handlePageSelected(index, false);
                            wordCloud = children.get(index).word;
                            setWordCloud(findViewById(R.id.layout_word), bean);
                            refreshPage(true);
                            clickTrack(productId, wordCloud, index);
                        }
                    }
                });
                layout.setContentView(child);
                layout.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {
                    @Override
                    public void onSelected(int index, int totalCount) {
                        tvName.setTextColor(getResources().getColor(R.color.color_secondary_surface_1_fg_default_idle));
                        tvCount.setTextColor(getResources().getColor(R.color.color_secondary_surface_1_fg_default_idle));
                        layoutWord.setBackgroundSolidDrawable(getResources().getColor(R.color.color_surface_6_bg_idle), CommonTools.dp2px(25));
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        tvName.setTextColor(getResources().getColor(R.color.color_surface_1_fg_default_idle));
                        tvCount.setTextColor(getResources().getColor(R.color.color_surface_1_fg_subtle_idle));
                        layoutWord.setBackgroundStrokeDrawable(getResources().getColor(R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(1), CommonTools.dp2px(25));
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {
                    }
                });

                return layout;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });

        tabHashTags.setNavigator(commonNavigator);
        int i = -1;
        for (PostCategoryBean.WordCloudBean item : bean.word_cloud) {
            i++;
            if (EmptyUtils.isEmpty(item.word)) {
                if (EmptyUtils.isEmpty(wordCloud)) {
                    tabHashTags.getIndicatorHelper().setPageSelected(i);
                }
            } else {
                if (item.word.equalsIgnoreCase(wordCloud)) {
                    tabHashTags.getIndicatorHelper().setPageSelected(i);
                }
            }

        }
    }

    private void showBottomDialog() {
        dialog = new WrapperDialog(activity, R.style.BottomDialogTheme) {
            @Override
            protected int getLayoutRes() {
                return R.layout.item_bottom_dialog_sort;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }

            @Override
            public void help(ViewHelper helper) {
                updateBottomList(helper.getView(R.id.layout_sort), helper.getView(R.id.tv_apply));
                helper.setOnClickListener(R.id.iv_close, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dismiss();
                    }
                });

            }
        }.show();
    }

    private void clickTrack(String productId, String targetNm, int targetPos) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(productId), null, null, null, null);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(targetNm)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setTargetPos(targetPos)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private void refreshProductOpNum() {
        if (layoutOp != null) {
            layoutOp.setCartNum(OrderManager.get().getCartNum());
        }
    }

    private void setProductOpConfig() {
        ViewTools.setViewVisibilityIfChanged(layoutOp, true);
        refreshProductOpNum();
        if (EmptyUtils.isEmpty(productId)) {
            return;
        }
        int mProductId = Integer.parseInt(productId);
        SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(mProductId, productKey);
        num = item != null ? item.quantity : 0;
        boolean reachLimit = false;
        if (product != null) {
            min = product.min_order_quantity;
            max = product.getOrderMaxQuantity();
            volumeThreshold = product.getVolumeThreshold();
            sold_status = product.sold_status;
            reachLimit = OrderManager.get().isReachLimit(product);
        }
        layoutOp.dismissTips();
        if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(sold_status)) {
            //修改日期
            num = BottomOpLayout.TYPE_CHANGED_DATE;
            layoutOp.showTips(getString(R.string.s_product_change_date_tips));
        } else if (reachLimit) {
            //购买已达限量
            num = BottomOpLayout.TYPE_PURCHASED;
            layoutOp.showTips(getString(R.string.s_product_purchased_tips));
        } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(sold_status)) {
            //售罄
            boolean isCollect = CollectManager.get().isProductCollect(mProductId);
            num = isCollect ? BottomOpLayout.TYPE_REMINDED_YET : BottomOpLayout.TYPE_REMINDED;
            if (!isCollect) {
                if (product instanceof ProductDetailBean.ProductFeatureBean) {
                    if (!EmptyUtils.isEmpty(((ProductDetailBean.ProductFeatureBean) product).restockInfo)) {
                        layoutOp.showTips(((ProductDetailBean.ProductFeatureBean) product).restockInfo);
                    }
                }
            }
        }
        layoutOp.setOpStyle(num, min, max);
        boolean finalReachLimit = reachLimit;
        layoutOp.setOnCartActionListener(new BottomOpLayout.OnCartActionListener() {
            @Override
            public void onClickLeft(View view) {
                layoutOp.dismissReachedTips();
                ProductOpHelper.editProduct(false, mProductId, min, max, productKey, layoutOp, product, PostListActivity.this,
                        detail, cartSource, traceId, String.valueOf(mProductId));
            }

            @Override
            public void onClickRight(View view) {
                int lastNum = ProductOpHelper.getProductNum(mProductId, productKey);
                int currentNum = ProductOpHelper.editProductNum(true, mProductId, min, max, productKey, volumeThreshold);
                if (currentNum > 0 && lastNum == currentNum) {
                    layoutOp.showReachedTips(getString(R.string.s_qty_limit_reached));
                } else {
                    ProductOpHelper.editProduct(true, mProductId, min, max, productKey, layoutOp, product, PostListActivity.this,
                            detail, cartSource, traceId, String.valueOf(mProductId));
                }
            }

            @Override
            public void onClickCart(View view) {
                if (disableOtherPage) {
                    finish();
                    return;
                }
                SharedViewModel.get().toCart();
                finish();
            }

            @Override
            public void onClickPanel(View view) {
                if (OrderManager.CHANGE_OTHER_DAY.equalsIgnoreCase(sold_status)) {
                    bottomBtnClickTrack("product_change_date", EagleTrackEvent.ClickType.VIEW);
                    //修改日期
                    startActivity(DateActivity.getIntent(activity, String.valueOf(productId), "product modify me"));
                } else if (finalReachLimit) {
                    //购买已达限量
                } else if (OrderManager.SOLD_OUT.equalsIgnoreCase(sold_status)) {
                    if (AccountManager.get().isLogin()) {
                        bottomBtnClickTrack("product_notify_me", EagleTrackEvent.ClickType.NORMAL);
                        boolean isCollected = CollectManager.get().isProductCollect(mProductId);
                        if (isCollected) {
                            //version 12.9 售罄 不允许取消收藏
                            return;
                        }
                        boolean isCollect = ProductOpHelper.fillCollect(mProductId, true, sold_status, layoutOp);
                        if (isCollect) {
                            //已收藏
                            showCollectTips();
                        }
                    } else {
                        toLoginPage();
                    }
                } else { //正常商品
                    SimplePreOrderBean.ItemsBean item = OrderManager.get().getSimpleOrderItem(mProductId, productKey);
                    int num = item != null ? item.quantity : 0;
                    if (num <= 0) {
//                        if (detail != null && detail.product.hasGroup()) {
//                            String tag = ProductGroupingFragment.class.getName() + productId;
//                            List<GroupProperty> propertyList = detail.product.group.propertyList;
//                            List<GroupProduct> productList = detail.product.group.groupProductList;
//                            showGrouping(propertyList, productList, tag);//onClick底部Panel
//                        } else {
                        ProductOpHelper.editProduct(true, mProductId, min, max, productKey, layoutOp, product,
                                PostListActivity.this, detail, cartSource, traceId, String.valueOf(mProductId));
//                        }
                    }
                }
            }
        });
    }

    private void showCollectTips() {
        layoutOp.showAutoTips(getString(R.string.s_added_to_my_list), disableOtherPage ? null : getString(R.string.s_view_up_case), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去收藏
                boolean toNext = toWebPage(AppConfig.HOST_WEB + Constants.Url.COLLECT);
                if (toNext) {
                    layoutOp.dismissAutoTips();
                }
            }
        });
    }

    private void toLoginPage() {
        startActivity(AccountIntentCreator.getIntent(activity));
    }

//    private void showGrouping(List<GroupProperty> propertyList, List<GroupProduct> productList, String tag) {
//        ProductGroupingFragment productGroupingFragment = ProductGroupingFragment
//                .newInstance(propertyList, productList, Integer.parseInt(productId), cartSource)
//                .setOnGroupedCallback(new ProductGroupingFragment.OnGroupedCallback() {
//                    @Override
//                    public void onGrouped(int newProductId) {
//                        productId = String.valueOf(newProductId);
//                        loadData();
//                    }
//
//                    @Override
//                    public void onDismissed(GroupProduct groupProduct, int num) {
//                        if (num >= 0) {
//                            ProductSyncHelper.onPageResume(layoutOp, groupProduct.product_id, null, groupProduct.min_order_quantity, groupProduct.max_order_quantity);
//                        }
//                    }
//
//                    @Override
//                    public void onProductStatusChanged() {
//
//                    }
//                });
//        productGroupingFragment.show(getSupportFragmentManager(), tag);
//        //tracking page sec imp
//        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
//                String.valueOf(productId), null, null, null, traceId);
//        for (GroupProperty property : propertyList) {
//            Map<String, Object> element = EagleTrackManger.get().getElement("group_feature", 1, property.property_id, propertyList.indexOf(property));
//            AppAnalytics.logPageSecImp(new EagleTrackModel.Builder()
//                    .addElement(element)
//                    .addContent(new TrackParams().put("default_nm", property.property_id_chosen).put("qty", property.property_value_list.size()).get())
//                    .addCtx(ctx)
//                    .build().getParams());
//        }
//    }

    private void bottomBtnClickTrack(String targetType, String clickType) {
        if (product != null) {
            Map<String, Object> content = new TrackParams()
                    .put("prod_name", product.name)
                    .put("prod_id", product.id)
                    .put("price", product.price)
                    .put("sold_status", product.sold_status)
                    .put("is_pantry", product.is_pantry)
                    .put("is_limit_product", product.is_limit_product)
                    .put("is_sponsored", product.is_sponsored)
                    .put("is_hotdish", product.is_hotdish)
                    .get();
            MediaBean mediaBean = product.getFirstMedia();
            if (mediaBean != null && !TextUtils.isEmpty(mediaBean.media_url)) {
                content.put("media_url", mediaBean.media_url);
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, String.valueOf(product.id), null, null, null, traceId);
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setIsMkpl(product.isSeller())
                    .setMod_nm(EagleTrackEvent.ModNm.PRODUCT_DETAIL)
                    .setMod_pos(0)
                    .setTargetNm(String.valueOf(product.id))
                    .setTargetPos(0)
                    .setTargetType(targetType)
                    .addContent(content)
                    .addCtx(ctx)
                    .setClickType(clickType)
                    .build().getParams());
        }
    }

    private boolean toWebPage(String url) {
        if (disableOtherPage) {
            return false;
        }
        startActivity(WebViewActivity.getIntent(activity, url));
        return true;
    }

    @Override
    public void onApplyToasterOptions(@NonNull IToaster<? extends IToasterOptions> toaster, @NonNull IToasterOptions options) {
        if (toaster.getType() == Toaster.TYPE_SNACK_BAR) {
            options.setContentMarginBottom(CommonTools.dp2px(72));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SharedViewModel.get().updateWriteReviewStatus.removeObserver(observer);
    }
}