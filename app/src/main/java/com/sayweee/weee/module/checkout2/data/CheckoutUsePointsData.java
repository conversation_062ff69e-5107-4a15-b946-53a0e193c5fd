package com.sayweee.weee.module.checkout2.data;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.module.base.adapter.SimpleAdapterDataType;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.bean.PreCheckoutPointInfoBean;

public class CheckoutUsePointsData extends SimpleAdapterDataType {

    public static final int UNAVAILABLE_TYPE_AVAILABLE = 0;
    public static final int UNAVAILABLE_TYPE_MEMBER_PLAN = 1;
    public static final int UNAVAILABLE_TYPE_USE_EBT = 2;
    public static final int UNAVAILABLE_TYPE_COMMISSION_PARTNER = 3;

    @NonNull
    public final PreCheckoutPointInfoBean pointInfo;
    public boolean checked;
    public boolean pointsCanChecked = true;
    public boolean saveRewardsChecked;
    public final int unavailableType;

    public CheckoutUsePointsData(
            @NonNull PreCheckoutPointInfoBean pointInfo,
            boolean saveRewardsChecked,
            boolean isAllCommissionPartner
    ) {
        super(CheckOutSectionAdapter.TYPE_USE_POINTS);
        this.pointInfo = pointInfo;
        this.saveRewardsChecked = saveRewardsChecked;
        this.checked = pointInfo.points_price > 0;
        if (saveRewardsChecked) {
            unavailableType = UNAVAILABLE_TYPE_MEMBER_PLAN;
        } else if (isAllCommissionPartner) {
            unavailableType = UNAVAILABLE_TYPE_COMMISSION_PARTNER;
        } else if (pointInfo.unavailable_alert != null && pointInfo.unavailable_alert.status) {
            unavailableType = UNAVAILABLE_TYPE_USE_EBT;
        } else {
            unavailableType = UNAVAILABLE_TYPE_AVAILABLE;
        }
    }

    public boolean isUnavailable() {
        return unavailableType != UNAVAILABLE_TYPE_AVAILABLE;
    }

    @Nullable
    public String getUnavailableDesc() {
        return pointInfo.unavailable_alert != null ? pointInfo.unavailable_alert.desc : null;
    }
}
