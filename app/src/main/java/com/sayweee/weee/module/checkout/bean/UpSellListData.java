package com.sayweee.weee.module.checkout.bean;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.base.adapter.AdapterWrapperData;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.utils.EmptyUtils;

public class UpSellListData extends AdapterWrapperData<UpSellBean.UpSellListBean>
        implements ProductTraceTask.SectionProvider {

    private final String modNm;

    public UpSellListData(UpSellBean.UpSellListBean bean) {
        super(bean);
        ProductHelper.filterReachLimitValid(bean.items, 0);
        modNm = bean.type;
    }

    public boolean isValid() {
        return t != null && !EmptyUtils.isEmpty(t.items);
    }

    public String getEventKey() {
        return modNm;
    }

    // =========================================
    // Implementation of ProductSalesTraceTask.SectionProvider interface
    // =========================================
    @Override
    public void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder) {
        if (isValid()) {
            for (ProductBean product : t.items) {
                builder.add(product.recommendation_trace_id, product.id);
            }
            builder.setModNm(getEventKey());
            builder.setUniqueKey(ProductTraceKey.generateUniqueKey(getEventKey(), null));
        }
    }
}