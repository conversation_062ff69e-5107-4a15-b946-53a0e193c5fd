package com.sayweee.weee.module.product.service;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_PRODUCT;

import android.app.Application;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.ProductDetailBean;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cate.bean.VendorIntroductionBean;
import com.sayweee.weee.module.cate.product.ReferralTitleBean;
import com.sayweee.weee.module.cate.product.bean.GroupProduct;
import com.sayweee.weee.module.cate.product.bean.PromotionListBean;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.service.CmsPagingViewModel;
import com.sayweee.weee.module.cms.service.ComponentPool;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.bean.CmsContentFeedListBean;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedParser;
import com.sayweee.weee.module.post.bean.PdpVideoListBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.preload.PreloadProductWaterfallTask;
import com.sayweee.weee.module.product.bean.BnplBean;
import com.sayweee.weee.module.product.bean.PdpContactButtonTextBean;
import com.sayweee.weee.module.product.bean.PdpGiftCardBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpMiddleBannerBean;
import com.sayweee.weee.module.product.bean.PdpModulesBean;
import com.sayweee.weee.module.product.bean.PdpSectionBean;
import com.sayweee.weee.module.product.bean.PdpSummaryBean;
import com.sayweee.weee.module.product.data.GiftCardPriceRefreshBean;
import com.sayweee.weee.module.product.data.PdpBlankData;
import com.sayweee.weee.module.product.data.PdpBnplData;
import com.sayweee.weee.module.product.data.PdpProductTitleData;
import com.sayweee.weee.module.product.manager.ProductAffiliateRepository;
import com.sayweee.weee.module.product.manager.ProductCouponRepository;
import com.sayweee.weee.service.live.UnPeekLiveData;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;
import com.tencent.mmkv.MMKV;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Author:  winds
 * Date:    2023/5/9.
 * Desc:
 */
public class ProductDetailViewModel extends CmsPagingViewModel<BaseLoaderModel<OrderApi>> implements IContentFeedSharedViewModel {


    public MutableLiveData<Integer> itemUpdateData = new MutableLiveData<>();
    //详情数据
    public MutableLiveData<ProductDetailBean> productDetailData = new MutableLiveData<>();
    public MutableLiveData<List<String>> aiData = new MutableLiveData<>();

    public MutableLiveData<ShareBean> shareData = new MutableLiveData<>();

    public MutableLiveData<String> policyData = new MutableLiveData<>();

    public MutableLiveData<String> alcoholData = new MutableLiveData<>();
    public MutableLiveData<ProductBean> adsProductData = new MutableLiveData<>();
    public MutableLiveData<PdpSectionBean.BannerInfoBean> adsTopBannerData = new MutableLiveData<>();
    public MutableLiveData<Boolean> adsProductEmptyData = new MutableLiveData<>();

    public MutableLiveData<VendorIntroductionBean> vendorIntroductionData = new MutableLiveData<>();

    private final MutableLiveData<PostCategoryBean> reviewListLiveData = new UnPeekLiveData<>();
    public MutableLiveData<ProductDetailBean> refreshProductData = new MutableLiveData<>();

    // Affiliate List
    private ProductAffiliateRepository affiliateRepository;

    // Coupon
    private ProductCouponRepository couponRepository;


    public MutableLiveData<Boolean> errorStatusData = new MutableLiveData<>();
    protected ArrayMap<Integer, BnplBean> bnplMapData = new ArrayMap<>();


    public MutableLiveData<PdpContactButtonTextBean> buttonTextData = new MutableLiveData<>();
    public MutableLiveData<Boolean> updateTabData = new MutableLiveData<>();

    private boolean isFirst = true;
    @Nullable
    public CmsContentFeedPacket contentFeedPacket;

    public Drawable preloadDrawable;

    public PdpCombineData combineData;

    public boolean changeGroupFilter;

    public ProductDetailViewModel(@NonNull Application application) {
        super(application);
        configParser();
    }

    protected void configParser() {
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED, new CmsContentFeedParser());
    }

    @Nullable
    @Override
    public CmsContentFeedPacket getContentFeedPacket() {
        return contentFeedPacket;
    }

    @Nullable
    @Override
    public String getContentFeedFromPageKey() {
        return WeeeEvent.PageView.PRODUCT;
    }

    public void getProductShare(int productId) {
        getLoader().getHttpService().getProductShare(productId)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ViewModelResponseObserver<ResponseBean<ShareBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ShareBean> response) {
                        shareData.postValue(response.getData());
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showMessage(failure);
                    }
                });
    }

    //Policy
    public void getProductPolicy(String type) {
        getLoader().getHttpService()
                .getContentConfig(type)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<String>>() {
                    @Override
                    public void onResponse(ResponseBean<String> response) {
                        policyData.postValue(response.object);
                    }
                });
    }

    public void getAlcoholTips() {
        getLoader().getHttpService()
                .getContentConfig("item_alcohol_tips")
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<String>>() {
                    @Override
                    public void onResponse(ResponseBean<String> response) {
                        alcoholData.postValue(response.getData());
                    }
                });
    }

    public void getVendorIntroduction(String vendorId, String productId) {
        getLoader()
                .getHttpService()
                .getVendorIntroduction(vendorId)
                .compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new ResponseObserver<ResponseBean<VendorIntroductionBean>>() {
                    @Override
                    public void onResponse(ResponseBean<VendorIntroductionBean> response) {
                        if (!EmptyUtils.isEmpty(response.getData().introduction)) {
                            ArrayMap<String, String> params = new ArrayMap<>();
                            params.put("product_id", productId);
                            response.getData().trackParams = params;
                            vendorIntroductionData.postValue(response.getData());
                        }
                    }
                });
    }

    private void initAffiliateRepository() {
        if (getLoader() != null) {
            affiliateRepository = new ProductAffiliateRepository(getLoader());
        }
    }

    public ProductAffiliateRepository getAffiliateRepository() {
        if (affiliateRepository == null) {
            initAffiliateRepository();
        }
        return affiliateRepository;
    }

    private void initCouponRepository() {
        if (getLoader() != null) {
            couponRepository = new ProductCouponRepository(getLoader());
        }
    }

    public ProductCouponRepository getCouponRepository() {
        if (couponRepository == null) {
            initCouponRepository();
        }
        return couponRepository;
    }


    public static boolean hasShownShareTempter() {
        int today = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        int lastClickDay = getPreferences().decodeInt("share_tempter_clicked_day", 0);
        return today == lastClickDay;
    }

    public static MMKV getPreferences() {
        return MMKVManager.obtain(MMKVManager.ID_PRODUCT_DETAIL);
    }

    public LiveData<PostCategoryBean> getReviewListLiveData() {
        return reviewListLiveData;
    }

    public void refreshProductData(boolean displayReferral, ProductBean simpleProduct, int productId, String traceId, String cartSource, boolean disableOtherPage) {
        bnplMapData.clear();
        refreshProductData(true, displayReferral, simpleProduct, productId, traceId, cartSource, null, disableOtherPage);
    }

    public void refreshProductData(boolean displayReferral, ProductBean simpleProduct, int productId, String traceId, String cartSource, GiftCardPriceRefreshBean refreshBean) {
        bnplMapData.clear();
        refreshProductData(true, displayReferral, simpleProduct, productId, traceId, cartSource, refreshBean, false);
    }

    public void refreshProductData(boolean displayReferral, ProductBean simpleProduct, int productId, String traceId, String cartSource, GiftCardPriceRefreshBean refreshBean, boolean isSilent) {
        bnplMapData.clear();
        refreshProductData(isSilent, displayReferral, simpleProduct, productId, traceId, cartSource, refreshBean, false);
    }

    public void refreshProductData(boolean isSilent, boolean displayReferral, ProductBean transferProduct, int productId, String traceId, String cartSource, GiftCardPriceRefreshBean refreshBean, boolean disableOtherPage) {
        //详情接口
        Observable<ResponseBean<ProductDetailBean>> detailObservable = getLoader().getHttpService().getProductDetailV2(productId, null, sourcePage).compose(DisposableTransformer.scheduler(this));
        //SKU Coupon
        Observable<ResponseBean<PromotionListBean>> promotionListObservable = getLoader().getHttpService().getProductPromotion(productId).compose(DisposableTransformer.scheduler(this));
        //Review
        Observable<ResponseBean<PostCategoryBean>> reviewListObservable = getLoader().getHttpService()
                .getReviewCategory(new RequestParams().put("product_id", productId).put("page", 1).put("limit", 8).get()).compose(DisposableTransformer.scheduler(this));
        //POST
        Observable<ResponseBean<PdpVideoListBean>> videoListObservable = getLoader().getHttpService().getPostCategory(productId, "video").compose(DisposableTransformer.scheduler(this));
        //一键加购和product line模块
        Observable<ResponseBean<PdpModulesBean>> pdpModulesObservable = getLoader().getHttpService().getPdpModules(String.valueOf(productId)).compose(DisposableTransformer.scheduler(this));
//        Observable<ResponseBean<PdpModulesBean>> pdpRecentObservable = getLoader().getHttpService().getRecentViewed(String.valueOf(productId), "pdp").compose(DisposableTransformer.scheduler(this));

        // product sumary
        Observable<ResponseBean<PdpSummaryBean>> summaryObserver = getLoader().getHttpService().getSummary(productId).compose(DisposableTransformer.scheduler(this));
        //ai
        Observable<ResponseBean<List<String>>> aiQuestionObserver = null;
        if (AccountManager.get().isLogin()) {
            aiQuestionObserver = getLoader().getHttpService().getGenAiQuestion(productId).compose(DisposableTransformer.scheduler(this));
        }
        Map<String, Serializable> params = new RequestParams().put("slot", "pdpMiddleCarouselBanner").putNonNull("searchTerm", productId).put("fromPage", "pdp").get();

        Observable<ResponseBean<PdpMiddleBannerBean>> bannerObserver = getLoader().getHttpService().getBannerInfo(params).compose(DisposableTransformer.scheduler(this));
        Map<String, Serializable> paramsSection = new RequestParams().put("slot", "pdpSection").putNonNull("searchTerm", productId).put("fromPage", "pdp").get();

        Observable<ResponseBean<PdpSectionBean>> sectionObserver = getLoader().getHttpService().getPdpSection(paramsSection).compose(DisposableTransformer.scheduler(this));

        Observable<ResponseBean<?>> observable;
        if (displayReferral && !AccountManager.get().isNewUser() && !hasShownShareTempter()) {
            //分享文案
            Observable<ResponseBean<ReferralTitleBean>> referralObservable = getLoader().getHttpService().getReferralTitle().compose(DisposableTransformer.scheduler(this));
            observable = Observable.mergeArrayDelayError(detailObservable, promotionListObservable, reviewListObservable, videoListObservable, pdpModulesObservable, summaryObserver, referralObservable, aiQuestionObserver, bannerObserver, sectionObserver);
        } else {
            observable = Observable.mergeArrayDelayError(detailObservable, promotionListObservable, reviewListObservable, videoListObservable, pdpModulesObservable, summaryObserver, aiQuestionObserver, bannerObserver, sectionObserver);
        }
        combineData = new PdpCombineData();
        observable
                .subscribe(new ResponseObserver<ResponseBean<?>>() {

                    @Override
                    public void onBegin() {
                        if (!isSilent) {
                            setLoadingStatus(true);
                        }
                    }

                    @Override
                    public void onResponse(ResponseBean<?> response) {
                        Object responseData = response.getData();
                        handleResponse(responseData, combineData);
                        setCombineExtraData(combineData, transferProduct, traceId, cartSource, refreshBean, disableOtherPage);

                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                    }

                    @Override
                    public void onFinish() {
                        if (!isSilent) {
                            setLoadingStatus(false);
                        }
                        handleResponse(combineData);
                    }

                });
    }

    public void refreshProductData(int productId){
        Observable<ResponseBean<ProductDetailBean>> detailObservable = getLoader().getHttpService().getProductDetailV2(productId, null, sourcePage).compose(DisposableTransformer.scheduler(this));
        detailObservable
                .subscribe(new ResponseObserver<ResponseBean<ProductDetailBean>>() {


                    @Override
                    public void onResponse(ResponseBean<ProductDetailBean> response) {
                        ProductDetailBean responseData = response.getData();
                        refreshProductData.postValue(responseData);
                    }
                });
    }
    private void getWaterfallData(int productId, PdpCombineData combineData, int modPos) {
        CmsContentFeedPacket packet = new CmsContentFeedPacket();
        packet.setComponentId(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED);
        packet.setComponentKey(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED);
        Map<String, String> params = new ArrayMap<>();
        params.put("product_id", String.valueOf(productId));
        params.put("recommend_session", packet.recommendSession);
        params.put("page_num", "1");
        final int[] finalModPos = {modPos};
        CmsDataSource dataSource = CmsContentFeedParser.parseDataSource(packet, Constants.Url.API_WATERFALL, params);
        packet.setBaseDataSource(dataSource);
        contentFeedPacket = packet;
        Observable<ResponseBean<CmsContentFeedListBean>> pdpContentFeedObservable = getLoader().getHttpService()
                .getContentFeed(Constants.Url.API_WATERFALL, params).compose(DisposableTransformer.scheduler(this));
        pdpContentFeedObservable.subscribe(new ResponseObserver<ResponseBean<CmsContentFeedListBean>>() {
            @Override
            public void onResponse(ResponseBean<CmsContentFeedListBean> responseData) {
                combineData.contentFeedListBean = responseData.getData();
                parseWaterfall(combineData, modPos, productId);
            }
        });
    }

    private void setCombineExtraData(PdpCombineData combineData, ProductBean transferProduct,
                                     String traceId, String cartSource, GiftCardPriceRefreshBean refreshBean, boolean disableOtherPage) {
        combineData.transferProduct = transferProduct;
        combineData.traceId = traceId;
        combineData.cartSource = cartSource;
        combineData.refreshBean = refreshBean;
        combineData.disableOtherPage = disableOtherPage;
        combineData.changeGroupFilter = changeGroupFilter;
    }


    private PdpCombineData handleResponse(Object responseData, PdpCombineData combineData) {
        if (responseData instanceof ProductDetailBean) {
            ProductDetailBean detail = (ProductDetailBean) responseData;
            productDetailData.postValue(detail);
            if (detail.product != null && detail.product.bnpl_base_support) {
                fetchBnplData(detail.product.id, detail.product.price);
            }
            combineData.detail = detail;
        } else if (responseData instanceof PromotionListBean) {
            combineData.promotion = (PromotionListBean) responseData;
        } else if (responseData instanceof PdpVideoListBean) {
            PdpVideoListBean temp = (PdpVideoListBean) responseData;
            if (temp.total > 0 || !EmptyUtils.isEmpty(temp.tip)) {
                combineData.post = temp;
            }
        } else if (responseData instanceof PostCategoryBean) {
            combineData.review = (PostCategoryBean) responseData;
        } else if (responseData instanceof PdpModulesBean) {
            PdpModulesBean temp = (PdpModulesBean) responseData;
            if (temp.isValid()) {
                if (temp.isViewHistory()) {
                    combineData.recently = temp.modules.get(0);
                } else {
                    combineData.modules = temp;
                }
            }
        } else if (responseData instanceof ReferralTitleBean) {
//            String text = ((ReferralTitleBean) responseData).share_popup;
//            if (!TextUtils.isEmpty(text)) {
//                combineData.referralDesc = text;
//            }
            combineData.inviteFriendsGetAmount = ((ReferralTitleBean) responseData).invite_friends_get_amount;
        } else if (responseData instanceof CmsContentFeedListBean) {
            combineData.contentFeedListBean = (CmsContentFeedListBean) responseData;
        } else if (responseData instanceof PdpSummaryBean) {
            combineData.summaryBean = (PdpSummaryBean) responseData;
        } else if (responseData instanceof PdpMiddleBannerBean) {
            PdpMiddleBannerBean.BannerInfoBean middleBannerInfo = ((PdpMiddleBannerBean) responseData).banner_info;
            if (!EmptyUtils.isEmpty(middleBannerInfo) && !EmptyUtils.isEmpty(middleBannerInfo.carousel)) {
                combineData.middleBarInfoBean = ((PdpMiddleBannerBean) responseData);
            }
        } else if (responseData instanceof PdpSectionBean) {
            PdpSectionBean response = (PdpSectionBean) responseData;
            if (!EmptyUtils.isEmpty(response) && !EmptyUtils.isEmpty(response.ads_product_list)) {
                combineData.adsProductList = ((PdpSectionBean) responseData).ads_product_list;
                adsProductData.postValue(((PdpSectionBean) responseData).ads_product_list.get(0));
            } else if (!EmptyUtils.isEmpty(response) && !EmptyUtils.isEmpty(response.banner_info)) {
                combineData.topBarInfoBean = response.banner_info;
                adsTopBannerData.postValue(response.banner_info);
            }

        } else if (responseData instanceof List<?>) {
            List<?> rawList = (List<?>) responseData;
            List<String> stringList = new ArrayList<>(rawList.size());
            for (Object item : rawList) {
                if (item instanceof String) {
                    stringList.add((String) item);
                }
            }
            combineData.aiQuestionBean = stringList;
            if (!EmptyUtils.isEmpty(stringList)) {
                aiData.postValue(stringList);
            }
        }
        return combineData;
    }

    public void handleResponse(PdpCombineData combineData) {
        ProductDetailBean detail = null;
        ProductBean transferProduct = null;
        String traceId = null;
        GiftCardPriceRefreshBean refreshBean = null;
        if (combineData != null) {
            detail = combineData.detail;
            transferProduct = combineData.transferProduct;
            traceId = combineData.traceId;
            refreshBean = combineData.refreshBean;
            if (EmptyUtils.isEmpty(combineData.adsProductList) && EmptyUtils.isEmpty(combineData.topBarInfoBean)) {
                adsProductEmptyData.postValue(true);
            }
        }

        boolean hasDetail = detail != null && detail.product != null;
        boolean displayError = transferProduct == null && !hasDetail;
        if (!displayError) {
            if (hasDetail && detail.product.hasGroup()) {
                //product group
                ArrayList<GroupProduct> invalid = new ArrayList<>();
                int size = detail.product.group.propertyList.size();
                for (GroupProduct product : detail.product.group.groupProductList) {
                    List<String> ids = product.splitValueIds();
                    if (ids == null || ids.size() == 0 || ids.size() != size) {
                        invalid.add(product);
                    }
                }
                if (invalid.size() > 0) {
                    detail.product.group.groupProductList.removeAll(invalid);
                }
            }

            ProductBean product = hasDetail ? detail.product : transferProduct;
            PdpGiftCardBean giftCardInfo = hasDetail ? detail.gift_card_info : null;
            if (giftCardInfo != null) {
                parseGiftCardData(detail, traceId, refreshBean);
            } else {
                parseProductData(product, combineData);
            }
            WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.PRODUCT,
                    String.valueOf(ProductDetailViewModel.this.hashCode()));
        } else {
            errorStatusData.postValue(true);
        }
    }

    public void parseTransferData(ProductBean product, String traceId) {
        int modPos = 0;
        PdpResponseParser parser = new PdpResponseParser();
        PdpResponseParser.ProductParseResult result = parser.parseTransferData(product, traceId, modPos);
        if (product != null) {
            initPreloadDrawable(product);
        }
        adapterData.postValue(result.list);
    }

    private void parseWaterfall(PdpCombineData combineData, int modPos, int productId) {
        List<AdapterDataType> list = new ArrayList<>();
        boolean hasWaterfall = contentFeedPacket != null && !EmptyUtils.isEmpty(combineData.contentFeedListBean) && !EmptyUtils.isEmpty(combineData.contentFeedListBean.contents);
        if (hasWaterfall) {
            modPos = modPos + 1;
            list.add(PdpBlankData.defaultLine().height(6));
            list.add(PdpBlankData.create().height(28));
            list.add(new PdpProductTitleData(PdpItemType.PDP_PRODUCT_TITLE, combineData.contentFeedListBean.title)
                    .setStatus("also_like"));
            CmsContentFeedPacket packet = contentFeedPacket;
            packet.t = combineData.contentFeedListBean;
            packet.position = modPos;
            packet.componentKey = combineData.contentFeedListBean.module_key;
            packet.pageTarget = String.valueOf(productId);
            packet.status = "pdp";
            packet.traceId = combineData.traceId;
            packet.setData(combineData.contentFeedListBean);
            CmsContentFeedParser.parseComponent(list, null, contentFeedPacket);
            CmsContentFeedParser.setLightningDealsSystemTime(combineData.contentFeedListBean);
        } else {
            if (AccountManager.get().isLogin()) {
                list.add(PdpBlankData.create().height(120));
            } else {
                list.add(PdpBlankData.create().height(100));
            }
        }
        adapterAppendData.postValue(list);
        updateTabData.postValue(hasWaterfall);
    }

    private void parseGiftCardData(ProductDetailBean detail, String traceId, GiftCardPriceRefreshBean refreshBean) {
        PdpResponseParser parser = new PdpResponseParser();
        PdpResponseParser.ProductParseResult result = parser.parseGiftCardData(detail, traceId, refreshBean, isFirst);
        if (result == null) return;
        isFirst = false;
        adapterData.postValue(result.list);
    }

    protected void parseProductData(ProductBean product, PdpCombineData combineData) {
        PdpResponseParser responseParser = new PdpResponseParser();
        PdpResponseParser.ProductParseResult result = responseParser.parseProductData(product,
                preloadDrawable, bnplMapData, combineData);
        if (result.isGetAlcoholTips) {
            getAlcoholTips();
        }

        adapterData.postValue(result.list);

        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(result.list);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(WeeeEvent.PageView.PRODUCT, WeeeEvent.PageView.PRODUCT));

        getWaterfallData(product.id, combineData, result.modPos);
    }

    private void initPreloadDrawable(ProductBean transferProduct) {
        if (transferProduct != null) {
            String preloadPath = transferProduct.getHeadImageUrl();
            String convertUrl = WebpManager.get().getConvertUrl(SPEC_PRODUCT, preloadPath);
            RequestOptions options = new RequestOptions();
            ImageLoader.load(getApplication(), convertUrl, options, new CustomTarget<Drawable>() {
                @Override
                public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                    preloadDrawable = resource;
                }

                @Override
                public void onLoadCleared(@Nullable Drawable placeholder) {

                }
            });
        }
    }

    protected void fetchBnplData(int productId, double amount) {
        getLoader().getHttpService().getBnplData(amount)
                .compose(ResponseTransformer.scheduler(this))
                .subscribe(new ResponseObserver<ResponseBean<BnplBean>>() {
                    @Override
                    public void onResponse(ResponseBean<BnplBean> response) {
                        BnplBean bnplBean = response.getData();
                        List<AdapterDataType> list = adapterData.getValue();
                        int target = -1;
                        if (list != null && list.size() > 0) {
                            for (int i = 0; i < list.size(); i++) {
                                AdapterDataType item = list.get(i);
                                if (item instanceof PdpBnplData) {
                                    PdpBnplData bnplData = (PdpBnplData) item;
                                    if (bnplData.productId == productId) {
                                        bnplData.updateData(bnplBean);
                                        target = i;
                                    }
                                    break;
                                }
                            }
                        }
                        if (target != -1) {
                            itemUpdateData.postValue(target);
                        } else {
                            bnplMapData.put(productId, bnplBean);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        List<AdapterDataType> list = adapterData.getValue();

                        int target = -1;
                        if (list != null && list.size() > 0) {
                            for (int i = 0; i < list.size(); i++) {
                                AdapterDataType item = list.get(i);
                                if (item instanceof PdpBnplData) {
                                    PdpBnplData bnplData = (PdpBnplData) item;
                                    if (bnplData.productId == productId) {
                                        bnplData.hideDisplay = true;
                                        target = i;
                                    }
                                    break;
                                }
                            }
                        }
                        if (target > -1) {
                            itemUpdateData.postValue(target);
                        } else {
                            bnplMapData.put(productId, new BnplBean());
                        }
                    }
                });
    }

    protected BnplBean getBnplDataById(int productId) {
        return bnplMapData.get(productId);
    }

    String sourcePage;

    public void setSourcePage(String sourcePage) {
        this.sourcePage = sourcePage;
    }


    public void handlePreloadData(Map<String, Object> data, ProductBean transferProduct,
                                  String traceId, String cartSource, GiftCardPriceRefreshBean refreshBean, boolean disableOtherPage) {
        if (data != null) {
            combineData = new PdpCombineData();
            setCombineExtraData(combineData, transferProduct, traceId, cartSource, refreshBean, disableOtherPage);
            for (Object obj : data.values()) {
                if (obj instanceof PreloadProductWaterfallTask.PdpWaterfallData) {
                    PreloadProductWaterfallTask.PdpWaterfallData waterfallData =
                            (PreloadProductWaterfallTask.PdpWaterfallData) obj;
                    contentFeedPacket = waterfallData.packet;
                    handleResponse(waterfallData.feedListBean, combineData);
                } else {
                    handleResponse(obj, combineData);
                }
            }
            handleResponse(combineData);
        }
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        preloadDrawable = null;
    }
}
