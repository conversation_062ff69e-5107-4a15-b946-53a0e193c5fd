package com.sayweee.weee.module.cart.service;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.CartSelectionData;
import com.sayweee.weee.module.cart.bean.CartSelectionTitleData;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewPreOrderBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.UpSellBean;
import com.sayweee.weee.module.cart.bean.WrapperUpSellBean;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class CartSelectionViewModel extends BaseViewModel<BaseLoaderModel<OrderApi>> {

    public static final String TAB_TYPE_GROCERY = "grocery";
    public static final String TAB_TYPE_ALCOHOL = "alcohol";

    public MutableLiveData<NewPreOrderBean> preOrderData = new MutableLiveData<>();
    public MutableLiveData<WrapperUpSellBean> upSellData = new MutableLiveData<>();

    public MutableLiveData<TabData> tabDataObservable = new MutableLiveData<>();
    public MutableLiveData<NewSectionBean> selectSectionObservable = new MutableLiveData<>();

    //选中商品数据
    ArrayList<String> selectedProductKeys = new ArrayList<>();
    public NewPreOrderBean preOrder;
    public String currentTabType = TAB_TYPE_GROCERY;
    public boolean hasTabTypes = false;

    public CartSelectionViewModel(@NonNull Application application) {
        super(application);
    }

    public void initPreOrder(@NonNull NewPreOrderBean preOrder) {
        this.preOrder = preOrder;
        this.hasTabTypes = checkHasTabTypes(preOrder);
        for (NewSectionBean section : CollectionUtils.orEmpty(preOrder.sections)) {
            section.selected = false; // 初始化时全部置否
        }
    }

    private void updatePreOrder(@NonNull NewPreOrderBean preOrder) {
        this.preOrder = preOrder;
        this.hasTabTypes = checkHasTabTypes(preOrder);
        preOrderData.postValue(preOrder);
    }

    private boolean checkHasTabTypes(@NonNull NewPreOrderBean preOrder) {
        return CollectionUtils.any(
                preOrder.sections,
                section -> !section.isAllInvalid() && section.isIndividualCheckout()
        );
    }

    public void renderTabData(@NonNull String tabType) {
        currentTabType = tabType;
        renderTabDataInternal(tabType, /* isClickSelectAll= */false);
    }

    private void renderTabDataInternal(@NonNull String tabType, boolean isClickSelectAll) {
        List<AdapterDataType> adapterList = renderAdapterData(tabType);
        int total = 0;
        int selectCount = 0;
        if (TAB_TYPE_GROCERY.equals(tabType)) {
            for (AdapterDataType data : adapterList) {
                if (data instanceof CartSelectionData) {
                    NewSectionBean section = ((CartSelectionData) data).t;
                    if (section.selected) {
                        selectCount++;
                    }
                    total++;
                }
            }
        }
        CartSelectionTitleData titleData = new CartSelectionTitleData(selectCount, total);
        titleData.isClickSelectAll = isClickSelectAll;

        TabData tabData = new TabData(tabType, titleData, adapterList, selectCount > 0);
        tabDataObservable.postValue(tabData);
    }

    private List<AdapterDataType> renderAdapterData(@NonNull String tabType) {
        NewPreOrderBean cart = preOrder;
        List<AdapterDataType> adapterList = new ArrayList<>();
        if (cart == null || cart.sections == null || cart.sections.isEmpty()) {
            return adapterList;
        }
        List<NewSectionBean> sections = cart.sections;
        for (NewSectionBean section : filterSectionsByType(cart, tabType)) {
            CartSelectionData item = new CartSelectionData(section, sections.indexOf(section));
            adapterList.add(item);
        }
        return adapterList;
    }

    private List<NewSectionBean> filterSectionsByType(@Nullable NewPreOrderBean cart, @NonNull String tabType) {
        List<NewSectionBean> filteredSections = new ArrayList<>();
        List<NewSectionBean> sections = cart != null && cart.sections != null
                ? cart.sections
                : CollectionUtils.emptyList();
        for (NewSectionBean section : sections) {
            if (section.isAllInvalid()) {
                // 商品全部失效自动过滤
                continue;
            }
            if (TAB_TYPE_GROCERY.equals(tabType) && !section.isIndividualCheckout()) {
                filteredSections.add(section);
            } else if (TAB_TYPE_ALCOHOL.equals(tabType) && section.isIndividualCheckout()) {
                filteredSections.add(section);
            }
        }
        return filteredSections;
    }

    public void selectAllCart() {
        boolean isSelectAll = false;//全不选
        List<NewSectionBean> sections = filterSectionsByType(preOrder, TAB_TYPE_GROCERY);
        for (NewSectionBean section : sections) {
            if (!section.selected) {
                isSelectAll = true;//全选
                break;
            }
        }
        for (NewSectionBean section : sections) {
            section.selected = isSelectAll;
            updateSelectedProductKeys(section);
        }
        requestSelectCart(true);
        renderTabDataInternal(TAB_TYPE_GROCERY, false);

        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(null)
                .setMod_pos(-1)
                .setSec_nm(null)
                .setSec_pos(-1)
                .setTargetNm("all")
                .setTargetPos(0)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(isSelectAll ? "select" : "unselect")
                .setIsMkpl(preOrder.isContainMKpl())
                .build().getParams());
    }

    public void requestSelectCart(NewSectionBean section, int index) {
        updateSelectedProductKeys(section);
        requestSelectCart(false);
        renderTabDataInternal(TAB_TYPE_GROCERY, false);
        logSelectAction(section, index);
    }

    public void requestSelectIndividualCart(NewSectionBean section, int index) {
        logSelectAction(section, index);
        String cartDomain = Constants.CartDomain.DOMAIN_GROCERY;
        ArrayList<String> selectedKeys = new ArrayList<>();
        CollectionUtils.mapNotNullTo(selectedKeys, section.getCartItems(), it -> it.product_key);
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", cartDomain)
                .put("selected_product_keys", selectedKeys);
        getLoader().getHttpService().selectCartItems(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        selectSectionObservable.postValue(section);
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showMessage(failure);
                    }
                });
    }

    private void logSelectAction(NewSectionBean section, int index) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(null)
                .setMod_pos(-1)
                .setSec_nm(null)
                .setSec_pos(-1)
                .setTargetNm(section.type)
                .setTargetPos(index)
                .setTargetType("cart")
                .setClickType(section.selected ? "select" : "unselect")
                .setIsMkpl(section.isSeller())
                .build().getParams());
    }

    private void updateSelectedProductKeys(NewSectionBean section) {
        for (NewItemBean item : section.getCartItems()) {
            String productKey = item.product_key;
            if (section.selected) {
                if (!selectedProductKeys.contains(productKey)) {
                    selectedProductKeys.add(productKey);
                }
            } else {
                selectedProductKeys.remove(productKey);
            }
        }
    }

    int requestCount = 100;

    private void requestSelectCart(boolean isClickSelectAll) {
        int count = ++requestCount;
        String cartDomain = Constants.CartDomain.DOMAIN_GROCERY;
        RequestParams requestParams = new RequestParams()
                .put("cart_domain", cartDomain)
                .put("selected_product_keys", selectedProductKeys);
        getLoader().getHttpService()
                .selectCartItems(requestParams.get())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<NewPreOrderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<NewPreOrderBean> response) {
                        if (count == requestCount) {
                            NewPreOrderBean data = response.getData();
                            updatePreOrder(data);
                            renderTabDataInternal(TAB_TYPE_GROCERY, isClickSelectAll);
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        showMessage(failure);
                    }
                });
    }

    public void upSell() {
        getLoader().getHttpService()
                .upSellV2(Constants.CartDomain.DOMAIN_GROCERY)
                .timeout(DevConfig.isTb1() ? 6000 : 1800, TimeUnit.MILLISECONDS)
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<ResponseBean<UpSellBean>>() {
                    @Override
                    public void onResponse(ResponseBean<UpSellBean> response) {
                        upSellData.postValue(new WrapperUpSellBean(response.getData()));
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        upSellData.postValue(new WrapperUpSellBean());
                    }
                });
    }

    public int getNoFreeDeliveryQty() {
        int qty = 0;
        for (NewSectionBean section : filterSectionsByType(preOrder, TAB_TYPE_GROCERY)) {
            NewSectionBean.ShippingInfo shippingInfo = section.shipping_info;
            boolean isFreeDelivery = shippingInfo == null || shippingInfo.isFreeDelivery();
            if (!isFreeDelivery) {
                qty = qty + 1;
            }
        }
        return qty;
    }

    public static class TabData {

        public final String tabType;
        public final CartSelectionTitleData titleData;
        public final List<AdapterDataType> adapterData;
        public final boolean enableBtn;

        private TabData(String tabType, CartSelectionTitleData titleData, List<AdapterDataType> adapterData, boolean enableBtn) {
            this.tabType = tabType;
            this.titleData = titleData;
            this.adapterData = adapterData;
            this.enableBtn = enableBtn;
        }
    }
}
