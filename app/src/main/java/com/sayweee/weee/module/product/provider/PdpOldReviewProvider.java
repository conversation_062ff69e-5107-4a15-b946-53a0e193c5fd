package com.sayweee.weee.module.product.provider;

import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.account.bean.AccountBean;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cms.track.IPageLifecycle;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.post.PostListActivity;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.post.edit.PostEditorActivity;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.product.adapter.ReviewListAdapter;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.data.PdpReviewData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.helper.PictureSelectorHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.spanner.DefaultURLClickListenerImpl;
import com.sayweee.weee.widget.MaxLinesFlowLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.sayweee.wrapper.listener.OnViewHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class PdpOldReviewProvider extends SimpleHorizontalImpressionProvider<PdpReviewData, AdapterViewHolder> implements ImpressionProvider<PdpReviewData>, IPageLifecycle {

    protected final EagleImpressionTrackerIml tracker = new EagleImpressionTrackerIml();

    @Override
    public int getItemType() {
        return PdpItemType.PDP_PRODUCT_REVIEW;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_pdp_review;
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpReviewData item) {
        PostCategoryBean postCategoryBean = item.t;
        boolean hasTip = !EmptyUtils.isEmpty(postCategoryBean.tip) && postCategoryBean.show_entrance;
        ViewTools.setViewVisible(helper.getView(R.id.ll_review_tip), hasTip);
        if (hasTip) {
            helper.setText(R.id.tv_review_tip, ViewTools.fromHtml(postCategoryBean.tip));
        }
//        helper.setGone(R.id.line, hasTip && EmptyUtils.isEmpty(postCategoryBean.list));
        helper.setGone(R.id.v_blank, !hasTip);
        ViewTools.setViewVisible(helper.getView(R.id.tv_more), !EmptyUtils.isEmpty(postCategoryBean.list) && postCategoryBean.total > 3);
        ViewTools.setViewVisible(helper.getView(R.id.rv_post), !EmptyUtils.isEmpty(postCategoryBean.list));
        ViewTools.setViewVisible(helper.getView(R.id.layout_post_title), !EmptyUtils.isEmpty(postCategoryBean.list));
        ViewTools.setViewVisible(helper.getView(R.id.layout_ai), !EmptyUtils.isEmpty(postCategoryBean.ai_summary));
        ViewTools.setViewVisible(helper.getView(R.id.fl_sell_points), !EmptyUtils.isEmpty(postCategoryBean.ai_sell_points));
        if (!EmptyUtils.isEmpty(postCategoryBean.list)) {
            fillPost(helper, item);
        }

        if (!EmptyUtils.isEmpty(postCategoryBean.ai_summary)) {
            helper.setText(R.id.tv_ai_title, postCategoryBean.ai_summary.title);
            helper.setText(R.id.tv_ai_desc, postCategoryBean.ai_summary.description);
            helper.setText(R.id.tv_ai_tip, postCategoryBean.ai_summary.tip);
        }

        if (!EmptyUtils.isEmpty(postCategoryBean.ai_sell_points)) {
            fillAiList(helper, item);
        }

        helper.setOnViewClickListener(R.id.layout_ai, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                toMorePost(item, EagleTrackEvent.TargetType.NORMAL_BUTTON);
            }
        });
        helper.setOnViewClickListener(R.id.layout_post_title, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (!EmptyUtils.isEmpty(postCategoryBean.list)) {
                    toMorePost(item, EagleTrackEvent.TargetType.NORMAL_BUTTON);
                }
            }
        });

        helper.setOnViewClickListener(R.id.ll_review_tip, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                context.startActivity(WebViewActivity.getIntent(context, !EmptyUtils.isEmpty(postCategoryBean.to_review_link) ? postCategoryBean.to_review_link : Constants.Url.POST_PORTAL));
            }
        });
    }

    private void fillPost(AdapterViewHolder helper, PdpReviewData data) {
        PostCategoryBean post = data.t;
        ViewTools.setViewVisible(helper.getView(R.id.tv_post_num), post.total > 0);
        helper.setText(R.id.tv_post_num, " • " + post.total_count_label);
        RecyclerView rvPost = helper.getView(R.id.rv_post);
        ReviewListAdapter postAdapter = new ReviewListAdapter();
        rvPost.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        if (!EmptyUtils.isEmpty(post.list)) {
            postAdapter.setNewData(post.list);
        }
        postAdapter.setAttachView(rvPost);
        rvPost.setAdapter(postAdapter);
        addAdapterToCache(postAdapter);
        postAdapter.setOnItemClickListener(new OnItemSafeClickListener() {
            @Override
            public void onItemClickSafely(BaseQuickAdapter adapter, View view, int position) {
                AdapterDataType item = postAdapter.getItem(position);
                if (item instanceof PostCategoryBean.ListBean) {
                    int id = ((PostCategoryBean.ListBean) item).id;
                    if (id > 0) {
                        context.startActivity(WebViewActivity.getIntent(context, ((PostCategoryBean.ListBean) item).link));
                        //review list点击
                        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                                String.valueOf(data.productId), null, null, null, data.traceId);
                        EagleTrackManger.get().trackEagleClickAction("reviews_carousel",
                                3,
                                null,
                                -1,
                                String.valueOf(id),
                                position,
                                EagleTrackEvent.TargetType.REVIEW,
                                EagleTrackEvent.ClickType.VIEW,
                                ctx);
                    } else {
                        toMorePost(data, null);
                    }
                } else {
                    toMorePost(data, null);
                }
            }
        });
    }

    private void fillAiList(AdapterViewHolder helper, PdpReviewData data) {
        List<PostCategoryBean.WordCloudBean> children = data.t.ai_sell_points;
        MaxLinesFlowLayout layoutHashtags = helper.getView(R.id.fl_sell_points);
        layoutHashtags.setMaxLine(1);
        layoutHashtags.removeAllViews();
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        params.rightMargin = CommonTools.dp2px(8);
        int i = -1;
        for (PostCategoryBean.WordCloudBean item : children) {
            i++;
            int finalI = i;
            layoutHashtags.addView(ViewTools.getHelperView(layoutHashtags, R.layout.item_word_cloud, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    helper.setText(R.id.tv_title, item.word);
                    helper.setText(R.id.tv_count, item.count_label);
                    helper.itemView.setOnClickListener(new OnSafeClickListener() {
                        @Override
                        public void onClickSafely(View v) {
                            clickTrack(data.productId, data.modPos, EagleTrackEvent.TargetType.NORMAL_BUTTON, item.word, finalI, data.traceId);
                            context.startActivity(WebViewActivity.getIntent(context, item.link));
                        }
                    });
                }
            }), params);
        }
        layoutHashtags.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                data.wordCount = layoutHashtags.getTotalByLine(1);
            }
        });
    }

    private void showCenterDialog(PostCategoryBean bean) {
        new CompatDialog(context, CompatDialog.STYLE_VERTICAL)
                .setTitleUp(new OnDialogClickListener() {
                    @Override
                    public void onClick(WrapperDialog dialog, View view) {
                        dialog.dismiss();
                    }
                }, bean.pop_title, bean.pop_description.get(0), bean.pop_button)
                .show();
    }

    private void showBottomDialog(PdpReviewData data, boolean isPost) {
        PostCategoryBean bean = data.t;
        new BottomDialog(context) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_more_content_bottom;
            }
        }.addHelperCallback((Dialog dialog, ViewHelper viewHelper) -> {
            LinearLayout llContent = viewHelper.getView(R.id.ll_content);
            viewHelper.setText(R.id.tv_title, bean.pop_title);
            viewHelper.setText(R.id.post_btn, bean.pop_button);
            viewHelper.setOnClickListener(R.id.post_btn, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    if (isPost) {
                        toNewPostVideo(data.detail);
                    } else {
                        postReviewClickTrack();
                        context.startActivity(WebViewActivity.getIntent(context, bean.to_review_link));
                    }
                    dialog.dismiss();
                }
            });
            llContent.removeAllViews();
            updateBottomList(llContent, bean.pop_description);
            viewHelper.setOnClickListener(R.id.iv_cancel, (targetView) -> {
                dialog.dismiss();
            });
        }).show();
    }

    private void toNewPostVideo(ProductBean product) {
        if (!AccountManager.get().isLogin()) {
            toLoginPage();
            return;
        }
        AccountBean bean = AccountManager.get().getAccountInfo();
        if (bean != null && bean.post_privilege) {
            PictureSelectorHelper.showVideoSelector((Activity) context, new OnResultCallbackListener<LocalMedia>() {
                @Override
                public void onResult(List<LocalMedia> list) {
                    if (!EmptyUtils.isEmpty(list)) {
                        LocalMedia media = list.get(0);
                        context.startActivity(PostEditorActivity.getIntentOnPdpAdd(context, media, product != null ? Collections.singletonList(product) : null, "pdp"));
                    }
                }

                @Override
                public void onCancel() {

                }

            });
        } else {
            context.startActivity(WebViewActivity.getIntent(context, Constants.Url.POST_PORTAL));
        }
        AppTracker.get().trackExtend(WeeeEvent.EVENT_POST_LIST_CLICK, new TrackParams().put("click_type", "post_list_write_post").get());
    }

    private void updateBottomList(LinearLayout llContent, List<String> list) {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        for (String child : list) {
            llContent.addView(ViewTools.getHelperView(llContent, R.layout.item_pdp_bottom_tv, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    TextView tv = helper.getView(R.id.tv_name);
                    ViewTools.setViewHtml(tv, child, new DefaultURLClickListenerImpl<Void>());
                }
            }), params);
        }
    }

    private void toLoginPage() {
        context.startActivity(AccountIntentCreator.getIntent(context));
    }

    @Override
    public List<ImpressionBean> fetchImpressionData(PdpReviewData item, int position) {
        String productId = String.valueOf(item.productId);
        List<ImpressionBean> list = new ArrayList<>();
        List<String> buttonNm = new ArrayList<>();
        List<BaseQuickAdapter> cacheList = findAdapterByCache();
        for (BaseQuickAdapter adapter : cacheList) {
            onPageScrollStateChangedImpression(adapter);
        }
        if (!EmptyUtils.isEmpty(item.t.ai_sell_points)) {
            for (int i = 0; i < item.t.ai_sell_points.size(); i++) {
                PostCategoryBean.WordCloudBean sellPoint = item.t.ai_sell_points.get(i);
                buttonNm.add(sellPoint.word);
            }
            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, productId, null, null, null, item.traceId);
            Map<String, Object> content = new TrackParams()
                    .put("button_nm", String.join(",", buttonNm))
                    .get();
            Map<String, Object> params = new EagleTrackModel.Builder()
                    .setMod_nm(PdpReviewData.MOD_NM)
                    .setMod_pos(item.modPos)
                    .addCtx(ctx)
                    .addContent(content)
                    .build().getParams();
            list.add(new ImpressionBean(EagleTrackEvent.EventType.FILTER_BUTTON_IMP, params, position + PdpReviewData.MOD_NM + productId));
        }
        return list;
    }

    private void clickTrack(int productId, int modPos, String targetType, String targetNm, int targetPos, String traceId) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(productId), null, null, null, traceId);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(PdpReviewData.MOD_NM)
                .setMod_pos(modPos)
                .setTargetNm(targetNm)
                .setTargetType(targetType)
                .setTargetPos(targetPos)
                .addCtx(ctx)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private void toMorePost(PdpReviewData data, String targetType) {
        context.startActivity(PostListActivity.getIntent(context, String.valueOf(data.productId), data.cartSource, false, data.detail));
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null,
                String.valueOf(data.productId), null, null, null, data.traceId);
        EagleTrackManger.get().trackEagleClickAction("reviews_carousel",
                3,
                null,
                -1,
                "explore_more",
                0,
                targetType,
                EagleTrackEvent.ClickType.VIEW,
                ctx);
    }

    private void postBottomClickTrack() {
        EagleTrackManger.get().trackEagleClickAction("reviews"
                , 1
                , null
                , -1
                , "post"
                , -1
                , "normal_button"
                , "view"
        );
    }

    private void postReviewClickTrack() {
        EagleTrackManger.get().trackEagleClickAction("reviews_popup"
                , 1
                , null
                , -1
                , "post_a_review"
                , -1
                , "normal_button"
                , "view"
        );
    }
}
