package com.sayweee.weee.module.search;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.alibaba.fastjson.JSON;
import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.analytics.util.AnalyticsUtils;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.logger.Logger;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ConfigService;
import com.sayweee.track.model.ExtendEvent;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.cart.bean.FilterProductListBean;
import com.sayweee.weee.module.cart.bean.SimpleProductBean;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cate.adapter.FilterListAdapter;
import com.sayweee.weee.module.category.FirstAddItemDialog;
import com.sayweee.weee.module.category.bean.ProductFilterBean;
import com.sayweee.weee.module.category.bean.ProductPropertyValueBean;
import com.sayweee.weee.module.category.bean.ProductSortBean;
import com.sayweee.weee.module.debug.DebugIntentCreator;
import com.sayweee.weee.module.dialog.FilterDialog;
import com.sayweee.weee.module.launch.service.ExperimentManager;
import com.sayweee.weee.module.post.widget.SearchTermView;
import com.sayweee.weee.module.search.bean.CacheSwitchData;
import com.sayweee.weee.module.search.bean.SearchBean;
import com.sayweee.weee.module.search.service.SearchPanelViewModel;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.search.v2.SearchSuggestFragmentV4;
import com.sayweee.weee.module.search.v2.SearchV2Manager;
import com.sayweee.weee.module.search.v2.SearchV2TrackingManager;
import com.sayweee.weee.module.search.v2.SkuSuggestionsManager;
import com.sayweee.weee.module.search.v2.adapters.SearchResultsV2MainSectionAdapter;
import com.sayweee.weee.module.search.v2.bean.SearchEntry;
import com.sayweee.weee.module.search.v2.bean.SearchJsonField;
import com.sayweee.weee.module.search.v2.bean.SearchResultSection;
import com.sayweee.weee.module.search.v2.bean.SearchSuggestionsGroupsBean;
import com.sayweee.weee.module.search.v2.service.SearchResultsV2Context;
import com.sayweee.weee.module.search.v2.widget.CustomImageView;
import com.sayweee.weee.module.search.v2.widget.CustomRemoteUrlBadgeView;
import com.sayweee.weee.module.search.v2.widget.SearchPopoverProductsView;
import com.sayweee.weee.module.search.v2.widget.SkuSuggestionsView;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.handler.UrlTradeProcessor;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.helper.ProgressBarManager;
import com.sayweee.weee.service.search.SearchManager;
import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.service.timer.service.TimerChangedListener;
import com.sayweee.weee.service.track.EventsUploadManager;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.CountryUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.StringUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.indicator.TrackCommonPagerTitleView;
import com.sayweee.weee.widget.indicator.TrackNavigator;
import com.sayweee.weee.widget.viewpagerofbottomsheet.ScreenUtils;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.veil.VeilLayout;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmStatusActivity;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  Chuan
 * Email:   <EMAIL>
 * Date:    2022/6/2
 * Desc:    选择搜索
 */
public class SearchPanelActivity extends WrapperMvvmStatusActivity<SearchPanelViewModel> implements SearchTermView.SearchTermListener, SearchSuggestFragment.OnSuggestListener, SearchRecordFragment.TagClickListener, SearchRecordFragment.SuggestionsGroupListener {

    public static final String KEY_LIST_FROM_EDIT = "list_from_edit";
    public static final String KEY_IS_FINISH = "is_finish";
    public static final int TYPE_MAIN_SEARCH = 100;
    public static final int TYPE_POST_EDIT_SEARCH = 200;
    public static final int SEARCH_RESULTS_DEFAULT_EC_API = 1;
    public static final int SEARCH_RESULTS_NEW_SEARCH_API_AND_UI = 2;
    public static final int SEARCH_SUGGEST_DEFAULT = 1;
    public static final int SEARCH_SUGGEST_V4 = 4; // test only for now
    public static final int SEARCH_SUGGEST_V3_V4_NOTIFICATION_DELAY = 50; // 0 means no delay

    //页面展示类型
    private int pageType;
    private int indexSelectedOld;
    private boolean fallbackSearchResultsTypeRequested;
    private boolean isFallbackSearchResultsType;
    private Integer searchResultsType;
    private Integer searchSuggestType;
    public SearchTermView searchTermView;
    public SkuSuggestionsView searchSkuSuggestionsView;
    private SearchPopoverProductsView searchPopoverProductsView;
    // 搜索联想
    private SearchSuggestFragment searchSuggestFragment;
    private SearchSuggestFragmentV4 searchSuggestFragmentV4;
    public View searchSuggestFragmentContainer;
    // 搜索记录
    private SearchRecordFragment searchRecordFragment;
    //搜索结果页面
    private View layoutSearchResults;
    private CompatMagicIndicator mainTab;
    // grocery
    private String catalogueNum;//当前选中的副分类
    private String keyWord;
    private View layoutSubTab, layoutSubSubTab, layoutSectionTabsFilter, sectionTabsFilter;
    private ImageView ivFilterTag, ivSubSort;
    private TextView tvFilterNum, tvSubFilterNum;
    private CompatMagicIndicator subTab, subSubTab;
    private CommonNavigator filterNavigator;
    private ViewPager2 groceryViewPager2;
    private TextView tvConfirm;

    private View layoutEmptyPostSearch, layoutEmptyMainSearch, layoutRequestProduct;
    private ViewStub layoutEmptyMainSearchViewStub;
    private TextView tvKeySearchNull;
    private View layoutTimerBanner;
    private View shadow;
    //edit post->add items->传递来的勾选信息
    private ArrayList<SimpleProductBean> listChecked = new ArrayList<>();
    private boolean isMainTabShow = true;
    private boolean comment, isAffiliate;
    private String viewIdOnSearchLanding;
    private SearchBean searchBean;
    public String searchTip, traceId;
    private boolean isSubFilterBtnShow, isShowCategories;
    public String sellerId;
    public boolean global;

    private boolean reissuePageViewOnResume = false;

    // Search V2
    private boolean isNeedRefresh = false;
    private final int searchResultsTabsFragmentsAmount = 3;
    private SearchResultsFragmentV2 searchResultsSelectedTabFragment;
    private Map<Long, SearchResultsFragmentV2> searchResultsTabsFragments;
    private SearchV2TrackingManager searchV2TrackingManager;

    private View topSearchBarFilterBtn;
    private TextView topSearchBarFilterBtnCountBadge;

    ProgressBarManager.ProgressChangedListener progressChangedListener = new ProgressBarManager.ProgressChangedListener() {
        @Override
        public void onProgressChange(int productId, @Nullable String tagType, @Nullable UpdateResultBean resultBean) {
            if (ProgressBarManager.get().isFirstAddSellerItemNew(tagType)) {
                new FirstAddItemDialog(activity).setData(productId, tagType, resultBean.tagInfo).show();
            } else if (ProgressBarManager.get().isSellerItemExistNew(tagType)) {
                View rootView = getView();
                ProgressBarManager.get().showSellerItemExistNew(rootView, productId, tagType, resultBean.tagInfo);
            } else {
                ProgressBarManager.get().setProgressBar(WeeeEvent.PageView.SEARCH_LANDING, findViewById(R.id.layout_progress), resultBean);
            }
        }
    };

    TimerChangedListener timerChangedListener = new TimerChangedListener() {
        @Override
        public void onRegister() {
            TimerBannerManager.get().setTimerPage(findViewById(R.id.layout_timer_banner), ExtendEvent.EVENT_PAGE_SEARCH);
        }

        @Override
        public void onChanged(boolean display, int hour, int min, int sec) {
            TimerBannerManager.get().setTimerInfo(findViewById(R.id.layout_timer_banner), display, hour, min, sec);
        }
    };

    protected ViewPager2.OnPageChangeCallback refreshScrollHandlerPageChangeCallback;
    protected ViewPager2.OnPageChangeCallback pageChangeCallback = new ViewPager2.OnPageChangeCallback() {
        @Override
        public void onPageSelected(int position) {
            super.onPageSelected(position);
            // 处理tab切换的数据处理
            //Objects.requireNonNull(groceryViewPager2.getAdapter()).notifyItemChanged(position);
            viewModel.tabSwitch(position);//滑动viewpager切换副分类
            SharedViewModel.get().setListChecked(listChecked);
        }
    };
    private String keywordByIntent;
    private boolean noResults;

    public static Intent getIntent(Context context) {
        return new Intent(context, SearchPanelActivity.class)
                .putExtra("page", TYPE_MAIN_SEARCH);
    }

    public static Intent getIntent(Context context, int page, ArrayList<SimpleProductBean> list, boolean isComment, boolean isAffiliate) {
        return new Intent(context, SearchPanelActivity.class)
                .putExtra("page", page)
                .putExtra("simple_product_list", list)
                .putExtra("comment", isComment)
                .putExtra("isAffiliate", isAffiliate);
    }

    public static Intent getIntent(Context context, String keyword, String hint) {
        return new Intent(context, SearchPanelActivity.class)
                .putExtra("page", TYPE_MAIN_SEARCH)
                .putExtra("keyword", keyword)
                .putExtra("hint", hint);
    }

    public static Intent getIntent(Context context, String keyword, String fallbackSearchResultsType, String hint) {
        return new Intent(context, SearchPanelActivity.class)
                .putExtra("page", TYPE_MAIN_SEARCH)
                .putExtra("keyword", keyword)
                .putExtra("fallbackSearchResultsType", fallbackSearchResultsType)
                .putExtra("hint", hint);
    }

    //seller home page===>search
    public static Intent getIntentBySeller(Context context, String sellerId, String hint) {
        return getIntent(context)
                .putExtra("sellerId", sellerId)
                .putExtra("hint", hint);
    }

    //global page===>search
    public static Intent getIntentByGlobal(Context context, String keyword, String hint) {
        return new Intent(context, SearchPanelActivity.class)
                .putExtra("page", TYPE_MAIN_SEARCH)
                .putExtra("keyword", keyword)
                .putExtra("hint", hint)
                .putExtra("global", true);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SearchV2Manager.get().fetchCoreSearchExperimentsIfNeeded();
        ConfigService.get().fetchExperiment(ExperimentManager.ID_SEARCH_RESULTS_GRID);
        CustomRemoteUrlBadgeView.preloadRemoteImages(this);
    }

    @Override
    protected void beforeCreate() {
        super.beforeCreate();
        WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, getMonitorPageKey(), String.valueOf(hashCode()));
    }

    @Override
    protected void initStatusBar() {
        StatusBarManager.setStatusBar(activity, findViewById(R.id.v_status), true);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_select_search;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        viewIdOnSearchLanding = AnalyticsUtils.generateViewId();
        pageType = getIntent().getIntExtra("page", TYPE_MAIN_SEARCH);
        comment = getIntent().getBooleanExtra("comment", false);
        isAffiliate = getIntent().getBooleanExtra("isAffiliate", false);
        sellerId = getIntent().getStringExtra("sellerId");
        global = getIntent().getBooleanExtra("global", false);
        if (pageType == TYPE_POST_EDIT_SEARCH) {
            Serializable serializable = getIntent().getSerializableExtra("simple_product_list");
            if (serializable instanceof List) {
                listChecked.addAll((List<SimpleProductBean>) serializable);
            }
        }

        updateSearchResultsTypeIfNeeded();

        searchTermView = findViewById(R.id.layout_search);
        searchTermView.setSearchTermListener(this);
        if (isSearchV2ResultsEnabled() || isSearchV2SuggestUIEnabled() || isSearchAutocompleteHomeXp()) {
            activity.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
            searchTermView.setSelectAllOnFocus(true);
            searchTermView.setEditOnClearButtonClick(true);
            //searchTermView.setEtInputBackgroundRes(R.drawable.selector_bg_input_new_search);
            SkuSuggestionsManager.get().resetCache();
        }
        searchTermView.enableDelayedChangeNotification(SEARCH_SUGGEST_V3_V4_NOTIFICATION_DELAY);//can use this delay on both SUGGEST UI VERSION searchTermView

        if (Constants.SearchV2.IS_UP_NEXT_ENABLED && isSearchV2SkuSuggestionsEnabled()) {
            SkuSuggestionsManager.get().clearLastSkuSearchSuggestions();
            searchSkuSuggestionsView = findViewById(R.id.layout_sku_suggestions);
            if (searchSkuSuggestionsView != null)
                searchSkuSuggestionsView.setVisibility(View.VISIBLE);
        }

        //地址联想
        this.searchSuggestFragmentContainer = findViewById(R.id.fragment_search_suggest_container);
        if (this.searchSuggestFragmentContainer != null) {
            if (isSearchV2SuggestUIEnabled(SEARCH_SUGGEST_V4)) {
                searchSuggestFragmentV4 = (SearchSuggestFragmentV4) SearchSuggestFragmentV4.newInstance();
                searchSuggestFragmentV4.setSuggestListener(this);
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_search_suggest_container, searchSuggestFragmentV4, "SearchSuggestFragmentV4")
                        .hide(searchSuggestFragmentV4)
                        .commit();
            } else {
                searchSuggestFragment = (SearchSuggestFragment) SearchSuggestFragment.newInstance();
                searchSuggestFragment.setSuggestListener(this);
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_search_suggest_container, searchSuggestFragment, "SearchSuggestFragment")
                        .hide(searchSuggestFragment)
                        .commit();
            }
        }

        // 初始化搜素记录
        searchRecordFragment = (SearchRecordFragment) getSupportFragmentManager().findFragmentById(R.id.fragment_search_record);
        if (searchRecordFragment != null) {
            searchRecordFragment.setTagChickListener(this);
            if (isSearchAutocompleteHomeXp()) {
                searchRecordFragment.enableNewSearchAutocompleteHomeEnabled(this);
            }
        }
        //搜索结果
        layoutEmptyPostSearch = findViewById(R.id.layout_empty);
        layoutEmptyMainSearch = findViewById(R.id.layout_empty_main_search);
        layoutEmptyMainSearchViewStub = findViewById(R.id.vs_layout_empty_main_search);
        ViewTools.setViewVisible(false, layoutEmptyPostSearch, layoutEmptyMainSearch);
        layoutSearchResults = findViewById(R.id.layout_search_results);
        mainTab = findViewById(R.id.tab_category);
        layoutSubTab = findViewById(R.id.layout_sub_category);
        ivFilterTag = findViewById(R.id.iv_sort);//刷选按钮
        tvFilterNum = findViewById(R.id.tv_filter_num);//刷选按钮数量
        subTab = findViewById(R.id.sub_tab);//主标题
        if (subTab != null && isSearchV2SectionTabsEnabled()) {
            subTab.getLayoutParams().height = getResources().getDimensionPixelSize(R.dimen.search_v2_section_tabs_height);
        }
        //filter show out***************************************************************************
        layoutSubSubTab = findViewById(R.id.layout_sub_sub_category);
        ivSubSort = findViewById(R.id.sub_iv_sort);
        tvSubFilterNum = findViewById(R.id.sub_tv_filter_num);
        subSubTab = findViewById(R.id.sub_sub_tab);
        groceryViewPager2 = findViewById(R.id.grocery_viewpager2);//内容view pager
        groceryViewPager2.registerOnPageChangeCallback(pageChangeCallback);
        tvConfirm = findViewById(R.id.tv_confirm);
        shadow = findViewById(R.id.shadow);
        tvConfirm.setOnClickListener(this::click);
        refreshConfirmBtn();
        layoutTimerBanner = findViewById(R.id.layout_timer_banner);
        //hint
        String hint = getIntent().getStringExtra("hint");
        if (!EmptyUtils.isEmpty(hint)) {
            searchTermView.setHint(hint);
        }

        if (SearchResultsFragmentV2.USE_SEARCH_FILTERS_BUTTON_ON_SEARCH_BAR && Constants.SearchV2.IS_SEARCH_V2_ENABLED && !global) {
            this.topSearchBarFilterBtn = this.findViewById(R.id.iv_new_search_results_filter);
            this.topSearchBarFilterBtnCountBadge = topSearchBarFilterBtn.findViewById(R.id.iv_new_search_results_filter_num);
            if (SearchResultsFragmentV2.USE_SEARCH_FILTERS_BUTTON_ON_ACTIVE_SEARCH && this.topSearchBarFilterBtn != null) {
                setTopSearchBarFilterButtonVisibility(true);
            }
        }

        if (isSearchV2ResultsEnabled() || isSearchV2SuggestUIEnabled() || isSearchAutocompleteHomeXp()) {
            if (isSearchV2ResultsEnabled()) {
                mainTab.setVisibility(View.GONE);

                VeilLayout vlTopbar = findViewById(R.id.vl_search_sub_tab);
                ViewGroup.MarginLayoutParams vlTopbarLayoutParams = (ViewGroup.MarginLayoutParams)vlTopbar.getLayoutParams();
                if (vlTopbarLayoutParams != null) {
                    vlTopbarLayoutParams.topMargin = CommonTools.dp2px(7);
                    vlTopbar.setLayoutParams(vlTopbarLayoutParams);
                }

                VeilLayout vlResults = findViewById(R.id.vl_search_result);
                vlResults.setLayout(R.layout.layout_search_product_row_veil);

                ConstraintLayout.LayoutParams mainTabLayoutParams = (ConstraintLayout.LayoutParams) mainTab.getLayoutParams();
                mainTabLayoutParams.topMargin = 0;
                mainTab.setLayoutParams(mainTabLayoutParams);

                searchPopoverProductsView = findViewById(R.id.search_v2_popover_products);

                // TODO SEARCH: init sku suggestions and search record

//                if (searchSkuSuggestionsView != null) {
//                    searchSkuSuggestionsView.setupAdapter(SearchPanelActivity.this, Constants.Source.SHOPPING_SEARCH_UP_NEXT, EagleTrackEvent.ModNm.HORIZONTAL_ITEM_LIST, SearchResultsFragmentV2.TRACK_EVENT_SECTION_TYPE_UP_MEXT);
//                    searchSkuSuggestionsView.setHeader(getString(R.string.s_search_up_next), false);
//                    searchSkuSuggestionsView.setBottomDivider(true);
//                    searchSkuSuggestionsView.setListener(new SkuListView.Listener() {
//                        @Override
//                        public void onShopAllClick(String title) {
//
//                        }
//
//                        @Override
//                        public void onItemClick(JSONObject object, int position, RecyclerView.ViewHolder viewHolder, int action, Object actionData) {
//                            if (searchResultsFragmentV2.onItemClickListener != null) {
//                                searchResultsFragmentV2.onItemClickListener.onItemClick(object, position, viewHolder, action, actionData);
//                            }
//                        }
//                    });
//                }
//
//                if (searchRecordFragment != null && isSearchAutocompleteHomeXp()) {
//                    searchRecordFragment.initSkuSuggestions(SearchPanelActivity.this);
//                    searchRecordFragment.initBuyItAgain(SearchPanelActivity.this);
//                }
            }
        }

        layoutEmptyMainSearchViewStub.inflate();
        tvKeySearchNull = findViewById(R.id.tv_key_search_null);
        layoutRequestProduct = findViewById(R.id.layout_request_product);

        setHostTag();
    }

    private void updateSearchResultsTypeIfNeeded() {
        if (searchResultsType == null || searchSuggestType == null) {
            if (searchResultsType == null) {
                final boolean isNewDesignV1Xp = isNewDesignV1Xp();
                if (isNewDesignV1Xp) {
                    String fallbackSearchResultsType = getIntent().getStringExtra("fallbackSearchResultsType");
                    if (!EmptyUtils.isEmpty(fallbackSearchResultsType)) {
                        if (fallbackSearchResultsType.equals("legacy")) {
                            searchResultsType = SEARCH_RESULTS_DEFAULT_EC_API;
                        } else {
                            try {
                                searchResultsType = Integer.valueOf(fallbackSearchResultsType);
                            } catch (Throwable e) {
                                Logger.e("Invalid search results type error: " + e.getMessage(), e);
                            }
                        }
                    }

                    if (searchResultsType == null) {
                        searchResultsType = SEARCH_RESULTS_NEW_SEARCH_API_AND_UI;
                    }
                }

                if (searchResultsType == null) {
                    searchResultsType = SEARCH_RESULTS_DEFAULT_EC_API;
                }
            }

            if (searchSuggestType == null) {
                final boolean isNewAutocompleteDesignV1Xp = isNewAutocompleteDesignV1Xp();
                if (isNewAutocompleteDesignV1Xp) {
                    searchSuggestType = SEARCH_SUGGEST_V4;
                } else {
                    searchSuggestType = SEARCH_SUGGEST_DEFAULT;
                }
            }
        }
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        updateSearchResultsTypeIfNeeded();
    }

    @Override
    protected void onResume() {
        super.onResume();
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, getMonitorPageKey(), String.valueOf(hashCode()));
        updateSearchResultsTypeIfNeeded();
        if (isSearchResultsVisible()) {
            logPv();
            setTimerBannerVisible(pageType == TYPE_MAIN_SEARCH);
        } else {
            logLandingPv();
        }
        EagleTrackManger.get().resetTrackData(EagleTrackManger.PAGE_SEARCH);
        ProgressBarManager.get().registerProgressChangedListener(progressChangedListener);

        if (this.searchV2TrackingManager != null) {
            this.searchV2TrackingManager.onResume();
        }

        if (isSearchV2ResultsEnabled() && isNeedRefresh && !EmptyUtils.isEmpty(keyWord)) {
            isNeedRefresh = false;
            goSearch(keyWord);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        reissuePageViewOnResume = true;
        TimerBannerManager.get().unregisterTimerChangedListener(timerChangedListener);
        ProgressBarManager.get().unregisterProgressChangedListener(progressChangedListener);

        if (this.searchV2TrackingManager != null) {
            this.searchV2TrackingManager.onPause();
        }
    }

    @Override
    protected void onDestroy() {
        if (searchTermView != null) {
            searchTermView.release();
            searchTermView = null;
        }

        if (searchSkuSuggestionsView != null) {
            searchSkuSuggestionsView.release();
            searchSkuSuggestionsView = null;
        }

        if (this.searchV2TrackingManager != null) {
            this.searchV2TrackingManager.release();
            this.searchV2TrackingManager = null;
        }

        if (isSearchV2ResultsEnabled() || isSearchV2SuggestUIEnabled() || isSearchAutocompleteHomeXp()) {
            SkuSuggestionsManager.get().release();
        }

        if (searchPopoverProductsView != null) {
            searchPopoverProductsView.release();
            searchPopoverProductsView = null;
        }

        clearGroceryViewPager();

        AdsManager.get().flushAllEvents();
        EventsUploadManager.get().flushAll();

        SearchV2Manager.get().updateSearchCrashInfo(null);

        if (viewModel != null) {
            viewModel.release();
        }

        super.onDestroy();
    }

    @Override
    protected boolean isShouldHideInput(View v, MotionEvent event) {
        if (searchRecordFragment != null && searchRecordFragment.isShouldHideInput(event)) {
            return false;
        }
        return super.isShouldHideInput(v, event);
    }

    @Override
    public void loadData() {
        viewModel.setAllName(getResources().getString(R.string.s_cate_all));
        viewModel.setMonitorPageKey(getMonitorPageKey());

        keywordByIntent = getIntent().getStringExtra("keyword");
        if (!EmptyUtils.isEmpty(keywordByIntent)) {
            goSearchFromInit(keywordByIntent);
        } else {
            showFragment(/* isRecordShow= */true, /* isSuggestShow= */null);
        }

        if (!comment) {
            viewModel.getPostOptional();
        }
    }

    @Override
    public void attachModel() {
        // 数据返回处理
        viewModel.mSearchResultLiveData.observe(this, this::showResults);
        viewModel.mCacheSwitchData.observe(this, this::cacheSwitch);
        viewModel.mFailureData.observe(this, this::failureStatus);

        viewModel.directUrlData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                startActivity(WebViewActivity.getIntent(activity, s));
                finish();
            }
        });

        viewModel.searchResultsContextData.observe(this, this::onSearchResultsContextData);

        viewModel.relatedProductsData.observe(this, this::onRelatedProductsLoaded);

        if (isSearchV2ResultsEnabled()) {
            SharedViewModel.get().accountData.observe(this, new Observer<Integer>() {
                @Override
                public void onChanged(Integer integer) {
                    isNeedRefresh = !EmptyUtils.isEmpty(keyWord);
                }
            });
        }

        SharedOrderViewModel.get().productOpStatusData.observe(this, productId -> {
            String productSku = String.valueOf(productId);
            getViewModel().updateCartItemsCache(productSku);
        });

        SharedOrderViewModel.get().preOrderData.observe(this, timestamp -> {
            getViewModel().updateCartItemsCache();
        });

        SharedOrderViewModel.get().preOrderRecreateData.observe(this, timestamp -> {
            SearchEntry searchEntry = viewModel.getCurrentSearch();
            final String deliveryPickupDate = OrderManager.get().getDeliveryPickupDate();
            if (searchEntry != null && !deliveryPickupDate.equals(searchEntry.deliveryPickupDate)) {
                viewModel.search(this, searchEntry);
            }
        });
    }

    @Override
    public void onEtInputClick() {
        setSearchResultVisible(View.INVISIBLE);
    }

    private void goSearchFromInit(String keyWord) {
        goSearch(keywordByIntent);
        String fallbackSearchResultsType = getIntent().getStringExtra("fallbackSearchResultsType");
        this.isFallbackSearchResultsType = !EmptyUtils.isEmpty(fallbackSearchResultsType);
    }

    @Override
    public void goSearch(String keyWord) {
        goSearch(keyWord, false);
    }

    @Override
    public void goSearch(String keyWord, boolean force) {
        if (filterSearch(keyWord)) {
            return;//打开开发面板
        }
        if (isFallbackSearchResultsType) {
            searchRestoringFromFallbackSearchResultsType(keyWord);
            return;
        }

        if (catalogueNum == null || catalogueNum.isEmpty()) {
            catalogueNum = "all";
        }

        if (EmptyUtils.isEmpty(keyWord.trim())) {
            searchTermView.setEtText("");
            return;//空字符输入保护
        }

        clearGroceryViewPager();
        viewModel.clearGroceryTabList();
        showFragment(/* isRecordShow= */false, /* isSuggestShow= */false);
        searchTermView.setEtText(keyWord);
        if (searchRecordFragment != null) searchRecordFragment.recordHistory(keyWord, sellerId);
        viewModel.setIsForce(force);
        viewModel.sellerId = sellerId;
        viewModel.global = global;

        if (isSearchV2ResultsEnabled()) {
            setTopSearchBarFilterButtonVisibility(false);
            SearchResultsFragmentV2.updateSearchTermViewPadding(searchTermView, !Constants.SearchV2.ENABLE_SECTION_TABS);
            SearchV2Manager.get().updateSearchCrashInfo(this);
            resetSearchFilter();
            viewModel.loadDataV2(this, keyWord, pageType);
        } else {
            viewModel.loadData(keyWord, pageType);
        }
        viewModel.startMonitorAction();

        if (isMainTabShow) {
            showMainTabVeil(true);
        }
        showSubTabVeil(true);
        showPagerVeil(true);
        setSearchResultVisible(View.VISIBLE);//触发搜索行为立即显示
        filterNavigator = null;
        ViewTools.setViewVisible(false, layoutEmptyPostSearch, layoutEmptyMainSearch);
        hideKeyboard();
        if (isSearchV2ResultsEnabled()) {
            layoutSubTab.setVisibility(Constants.SearchV2.ENABLE_SECTION_TABS ? View.INVISIBLE : View.GONE);
        } else {
            layoutSubTab.setVisibility(View.INVISIBLE);
        }
        indexSelectedOld = -1;
        if (mainTab != null) {
            mainTab.handlePageSelected(0);
        }
        this.keyWord = keyWord;
    }

    @Override
    public void onDelete() {
        showFragment(/* isRecordShow= */null, /* isSuggestShow= */false);
    }

    @Override
    public void onBack() {
        back(false);
    }

    @Override
    public void onBackPressed() {
        back(false);
        if (!isSearchV2ResultsEnabled()) {
            super.onBackPressed();
        }
    }

    @Override
    public void onEtEmptyObserver() {
        setSearchResultVisible(View.INVISIBLE);
        showFragment(true, false);
    }

    @Override
    public void getSuggestions(String words) {
        noResults = true;

        SearchResultsFragmentV2 searchResultsSelectedTabFragment = getSearchResultsSelectedTabFragment();
        if (searchResultsSelectedTabFragment != null) {
            this.runOnUiThread(() -> {
                setTopSearchBarFilterButtonVisibility(false);
            });
        }

        if (searchSuggestFragmentV4 != null) {
            if (EmptyUtils.isEmpty(sellerId) && !global) {
                searchSuggestFragmentV4.getSuggest(words);
                if (searchSkuSuggestionsView != null && searchSkuSuggestionsView.hasSkuSuggestions()) {
                    this.runOnUiThread(() -> {
                        showFragment(false, true);
                    });
                }
            }
        } else {
            if (searchSuggestFragment != null && EmptyUtils.isEmpty(sellerId) && !global) {//seller搜索,global搜索无联想
                searchSuggestFragment.getSuggest(words);
            }
        }
    }

    @Override
    public void onItemClick(String keyWord) {
        showFragment(null, false);
        goSearch(keyWord);
    }

    @Override
    public void autoFill(String keyWord) {
        searchTermView.autoFillEtText(keyWord);
    }

    @Override
    public void onSuccess(String term, List suggestionList) {
        if (CommonTools.isNotEmptyList(suggestionList) && !TextUtils.isEmpty(searchTermView.getEtText().getText().toString()) && searchTermView.isSearchActive()) {
            showFragment(false, true);
            setSearchResultVisible(View.INVISIBLE);
        } else {
            showFragment(null, false);
        }
    }

    @Override
    public void onTagClick(String keyWord) {
        goSearch(keyWord);
    }

    public void click(View view) {
        switch (view.getId()) {
            case R.id.tv_confirm:
                back(true);
                break;
        }
    }

    private boolean isRecordShowing() {
        boolean isRecordShowing = searchRecordFragment != null ? searchRecordFragment.isSupportVisible() : false;
        return isRecordShowing;
    }

    private boolean isSuggestShowing() {
        boolean isSuggestShowing = searchSuggestFragmentContainer != null ? searchSuggestFragmentContainer.getVisibility() == View.VISIBLE : false;
        return isSuggestShowing;
    }

    private void showFragment(Boolean isRecordShow, Boolean isSuggestShow) {
        try {
            boolean isRecordShowing = isRecordShowing();
            boolean isSuggestShowing = isSuggestShowing();

            FragmentManager manager = getSupportFragmentManager();
            FragmentTransaction transaction = manager.beginTransaction();
            if (isRecordShow != null && searchRecordFragment != null) {
                if (isRecordShow && !searchRecordFragment.isSupportVisible()) {
                    if (searchSuggestFragmentV4 != null) searchSuggestFragmentV4.clearSuggestions();
                    setTimerBannerVisible(false);
                    transaction.show(searchRecordFragment);
                    isRecordShowing = true;
                    logLandingPv();
                } else if (!isRecordShow && (searchRecordFragment.isSupportVisible() || !searchRecordFragment.isResumed())) {
                    transaction.hide(searchRecordFragment);
                    isRecordShowing = false;
                }
            }
            if (isSuggestShow != null && searchSuggestFragmentContainer != null) {
                final boolean shouldUpdateVisibility = isSuggestShow != isSuggestShowing;
                if (shouldUpdateVisibility) {
                    searchSuggestFragmentContainer.setVisibility(isSuggestShow ? View.VISIBLE : View.INVISIBLE);
                    isSuggestShowing = isSuggestShow;
                }
                Fragment searchSuggestFragment = getSearchSuggestFragment();
                if (searchSuggestFragment != null) {
                    if (isSuggestShow && !searchSuggestFragment.isVisible()) {
                        setTimerBannerVisible(false);
                        transaction.show(searchSuggestFragment);
                        logLandingPv();//suggest 列表出现时上报 search landing PV
                    } else if (!isSuggestShow && searchSuggestFragment.isVisible()) {
                        transaction.hide(searchSuggestFragment);
                    }
                }
            }

            if (searchSkuSuggestionsView != null) {
                String currentSearch = searchTermView != null && searchTermView.getEtText() != null ? searchTermView.getEtText().getText().toString() : null;
                final boolean changed = this.keyWord == null || !this.keyWord.equals(currentSearch);
                searchSkuSuggestionsView.handleVisibility(isRecordShow, isSuggestShow, isRecordShowing, isSuggestShowing, changed);
            }

            if (searchRecordFragment != null) {
                searchRecordFragment.updateSuggestionsGroupsVisibility();
            }

            SearchResultsFragmentV2 searchResultsSelectedTabFragment = getSearchResultsSelectedTabFragment();
            if (searchResultsSelectedTabFragment != null) {
                boolean isFilterVisible = isSearchV2ResultsEnabled() && !isRecordShowing && !isSuggestShowing && !noResults;
                setTopSearchBarFilterButtonVisibility(isFilterVisible);
                searchResultsSelectedTabFragment.onPopoverVisibilityChanged(!isFilterVisible);
            }

            if (!transaction.isEmpty()) {
                transaction.commitAllowingStateLoss();
                manager.executePendingTransactions();
            }
        } catch (Exception e) {
            Logger.e(e);
        }
    }

    private void setSearchResultVisible(int visibility) {
        layoutSearchResults.setVisibility(visibility);
    }

    private void showResults(SearchBean bean) {
        if (!EmptyUtils.isEmpty(bean.trace_id)) {
            traceId = bean.trace_id;
        }
        if (bean.isGrocery()) {
            searchBean = bean;
            searchTip = bean.t != null ? bean.t.search_tip : null;
        }

        hideAllVeil();

        if (bean.t == null) {
            return;
        }
        logPv();//触发搜索结果page key
        if (!isSearchV2ResultsEnabled() || (!searchTermView.isSearchActive() && !isRecordShowing() && !isSuggestShowing())) {
            showFragment(false, false);
            setSearchResultVisible(View.VISIBLE);
        }
        //初始化主Tab(仅仅用户首次触发搜索时初始化一次)
        //普通搜索点击 rtg tab时无需再次初始化主Tab
        if (bean.pageType != 0
                && bean.catalogueNum == null) {
            initMainTab(bean);
        }
        setTimerBannerVisible(!bean.isShowEmpty);
        noResults = bean.isShowEmpty;
        if (bean.isShowEmpty) {
            layoutSubTab.setVisibility(View.GONE);
            groceryViewPager2.setVisibility(View.GONE);
            if (pageType == TYPE_MAIN_SEARCH) {
                layoutEmptyMainSearch.setVisibility(View.VISIBLE);
                final String s;
                if (isSearchV2ResultsEnabled()) {
                    s = getString(R.string.s_search_results_not_found, keyWord);
                } else {
                    s = getString(R.string.s_search_sry, keyWord);
                }
                tvKeySearchNull.setText(s);
                layoutRequestProduct.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startActivity(RequestProductActivity.getIntent(activity, bean.keyword));
                    }
                });
            } else {
                layoutEmptyPostSearch.setVisibility(View.VISIBLE);
                String s = String.format(getString(R.string.s_search_sry), keyWord);
                ViewTools.setViewText(findViewById(R.id.tv_key_search_null_post), s);
            }
            return;
        }
        ViewTools.setViewVisible(false, layoutEmptyPostSearch, layoutEmptyMainSearch);
        if (bean.isGrocery()) {
            //条目数据
            //是否显示分类
            final boolean shouldShowFilters = !isSearchV2ResultsEnabled() || isSearchV2SectionTabsEnabled() || (
                    SearchResultsFragmentV2.USE_SEARCH_FILTERS
                            && !SearchResultsFragmentV2.USE_SEARCH_FILTERS_BUTTON_ON_SEARCH_BAR
            );
            ViewTools.setViewVisible(layoutSubTab, shouldShowFilters);
            if (!isTypePostEditSearch()) {
                if (bean.t.show_categories) {
                    isShowCategories = true;
                    ViewTools.setViewVisible(subTab, true);
                    groceryViewPager2.setUserInputEnabled(true);
                } else {
                    ViewTools.setViewVisible(false, layoutSubTab, subTab);
                    groceryViewPager2.setUserInputEnabled(false);
                }
            }
            groceryFilter(bean);
            initFilterShowOutTab();
            groceryViewPager2.setVisibility(View.VISIBLE);
            // 填充指示器数据
            initGroceryTab(bean);
            // 填充filter show out指示器数据
            //initFilterShowOutTab();
            // 填充页面数据
            groceryViewPager(bean);
            // 刷选按钮处理
            //groceryFilter(bean);
        }
    }

    private void cacheSwitch(CacheSwitchData data) {
        ViewTools.setViewVisible(false, layoutEmptyPostSearch, layoutEmptyMainSearch);
        String searchType = data.searchType;
        showEmpty(data.cacheCount);
        if (TextUtils.equals(searchType, SearchBean.SEARCH_MODULE_GROCERY)) {
            layoutSubTab.setVisibility(data.cacheCount > 0 || (searchBean != null && searchBean.t != null && searchBean.t.show_categories) ? View.VISIBLE : View.GONE);
            layoutSubSubTab.setVisibility((!getFiltersShowOut().isEmpty() || isSubFilterBtnShow) ? View.VISIBLE : View.GONE);
            groceryViewPager2.setVisibility(View.VISIBLE);
        }
    }

    private void showEmpty(int cacheCount) {
        if (pageType == TYPE_MAIN_SEARCH) {
            if (isSearchV2ResultsEnabled() && cacheCount == 0) {
                layoutEmptyMainSearch.setVisibility(View.VISIBLE);
            } else {
                layoutEmptyMainSearch.setVisibility((cacheCount > 0 || isSubFilterBtnShow || isShowCategories) ? View.GONE : View.VISIBLE);
            }
        } else {
            layoutEmptyPostSearch.setVisibility(cacheCount > 0 ? View.GONE : View.VISIBLE);
        }
    }

    public void setShadowVisibility(boolean visible) {
        shadow.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public SearchTermView getSearchTermView() {
        return searchTermView;
    }

    public CompatMagicIndicator getMainTab() {
        return mainTab;
    }

    private void initMainTab(SearchBean searchBean) {
        boolean isTypePostEditSearch = pageType == TYPE_POST_EDIT_SEARCH;
        FilterProductListBean bean = searchBean.t;
        if (bean.srep_split_tables == null || (isTypePostEditSearch && !bean.support_rtg) || global) {
            isMainTabShow = false;
            mainTab.setVisibility(View.GONE);
            return;
        }
        List<FilterProductListBean.TabNodeBean.TabBean> tabBeanList = bean.srep_split_tables.serp_tabs;
        if (isTypePostEditSearch && !CommonTools.isNotEmptyList(tabBeanList)) {
            mainTab.setVisibility(View.GONE);
            return;
        }

        if (!isTypePostEditSearch) {
            isMainTabShow = false;
            mainTab.setVisibility(View.GONE);
            return;
        }

        if (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
            FilterProductListBean.TabNodeBean.TabBean filterTabBean = new FilterProductListBean.TabNodeBean.TabBean();
            filterTabBean.serp_tab_key = "filter";
            tabBeanList.add(filterTabBean);
        }

        mainTab.setVisibility(View.VISIBLE);
        CommonNavigator navigator = new CommonNavigator(activity);
        navigator.setAdjustMode(true);
        navigator.setAdapter(new CommonNavigatorAdapter() {

            static final int FILTER_BTN_IDX = 2;
            static final int FILTER_BTN_SIZE_DP = 57;
            int filterBtnSizePix = 0;

            private int getFilterBtnSizePix() {
                if (filterBtnSizePix == 0) {
                    filterBtnSizePix = ScreenUtils.dp2px(getApplication().getApplicationContext(), FILTER_BTN_SIZE_DP);
                }
                return filterBtnSizePix;
            }

            @Override
            public int getCount() {
                return isTypePostEditSearch
                        ? tabBeanList.size()
                        : (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED ? 3 : 2);
            }

            @Override
            public float getTitleWeight(Context context, int index) {
                if (isTypePostEditSearch || !Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
                    return super.getTitleWeight(context, index);
                } else {
                    int screenWidth = ScreenUtils.getScreenWidth(getApplication().getApplicationContext());
                    if (index == FILTER_BTN_IDX) {
                        return (float) getFilterBtnSizePix() / (float) screenWidth;
                    } else {
                        float ratio = (float) getFilterBtnSizePix() / (float) screenWidth;
                        float diff = 1f - ratio;
                        return diff / 2f;
                    }
                }
            }

            @Override
            public IPagerTitleView getTitleView(Context context, int index) {
                FilterProductListBean.TabNodeBean.TabBean tabBean = tabBeanList.get(index);
                final View customLayout;

                if (isTypePostEditSearch || !Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
                    customLayout = LayoutInflater.from(context).inflate(R.layout.tab_main_two_items, null);
                } else {
                    if (index == FILTER_BTN_IDX) {
                        customLayout = LayoutInflater.from(context).inflate(R.layout.tab_main_two_items_full_size_icon_only, null);
                    } else {
                        customLayout = LayoutInflater.from(context).inflate(R.layout.tab_main_two_items_full_size, null);
                    }
                }

                final ImageView tabIcon = customLayout.findViewById(R.id.iv_tab_icon);
                final TextView tabName = customLayout.findViewById(R.id.tv_tab_name);
                final TextView tabNum = customLayout.findViewById(R.id.tv_tab_num);

                if (tabName != null) {
                    tabName.setText(tabBean.serp_tab_title);
                }

                if (index == 0) {
                    tabNum.setText(tabBean.total_count_view);
                } else if (index == 1) {
                    tabNum.setText((isTypePostEditSearch ? tabBean.total_count_view : "0"));
                } else if (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED && index == FILTER_BTN_IDX) {
                    if (tabName != null) {
                        tabName.setVisibility(View.GONE);
                    }

                    if (tabNum != null) {
                        tabNum.setVisibility(View.GONE);
                    }
                }

                final View tabBottomLineIndicator = Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED ? customLayout.findViewById(R.id.v_bottom_line_indicator) : null;
                final View tabBottomLineIndicatorStart = Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED ? customLayout.findViewById(R.id.v_bottom_line_indicator_start) : null;
                final View tabBottomLineIndicatorEnd = Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED ? customLayout.findViewById(R.id.v_bottom_line_indicator_end) : null;
                if (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED && index == 2) {
                    tabIcon.setImageResource(R.mipmap.pic_new_filter);
                    /*View tabTouchFeedbackView = customLayout.findViewById(R.id.v_touch_feedback);
                    if (tabTouchFeedbackView != null) {
                        tabTouchFeedbackView.setOnClickListener(new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {

                            }
                        });
                    }*/
                }

                CommonPagerTitleView view = new CommonPagerTitleView(activity) {
                    @Override
                    public void onSelected(int index, int totalCount) {
                        super.onSelected(index, totalCount);
                        if (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
                            if (index != FILTER_BTN_IDX) {
                                tabName.setTextColor(getResources().getColor(R.color.color_secondary_standalone_idle));
                                tabNum.setTextColor(getResources().getColor(R.color.color_surface_1_fg_minor_idle));
                                tabIcon.setImageResource(index == 0 ? R.mipmap.g_filled : R.mipmap.restaurant_filled);
                                if (tabBottomLineIndicator != null) {
                                    tabBottomLineIndicator.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_default_idle));
                                    tabBottomLineIndicatorStart.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_default_idle));
                                    tabBottomLineIndicatorEnd.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_default_idle));
                                }
                            }
                        } else {
                            customLayout.setBackground(ContextCompat.getDrawable(activity, R.drawable.shape_white_radius_90));
                            tabName.setTextColor(getResources().getColor(R.color.color_secondary_standalone_idle));
                            tabNum.setTextColor(getResources().getColor(R.color.color_surface_1_fg_minor_idle));
                            tabIcon.setImageResource(index == 0 ? R.mipmap.g_filled : R.mipmap.restaurant_filled);
                        }
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        super.onDeselected(index, totalCount);
                        if (Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
                            if (index != FILTER_BTN_IDX) {
                                tabName.setTextColor(getResources().getColor(R.color.color_surface_2_fg_default_idle));
                                tabNum.setTextColor(getResources().getColor(R.color.color_surface_1_fg_minor_idle));
                                tabIcon.setImageResource(index == 0 ? R.mipmap.g_outline : R.mipmap.restaurant_outline);
                                if (tabBottomLineIndicator != null) {
                                    //tabBottomLineIndicator.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_minor_idle));
                                    tabBottomLineIndicator.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_hairline_idle));
                                    tabBottomLineIndicatorStart.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_hairline_idle));
                                    tabBottomLineIndicatorEnd.setBackground(ContextCompat.getDrawable(getContext(), R.color.color_surface_1_fg_hairline_idle));
                                }
                            }
                        } else {
                            customLayout.setBackground(null);
                            tabName.setTextColor(getResources().getColor(R.color.color_surface_2_fg_default_idle));
                            tabNum.setTextColor(getResources().getColor(R.color.color_surface_1_fg_minor_idle));
                            tabIcon.setImageResource(index == 0 ? R.mipmap.g_outline : R.mipmap.restaurant_outline);
                        }
                    }
                };

                if (!isTypePostEditSearch && Constants.SearchV2.CUSTOM_NAVIGATOR_ENABLED) {
                    if (index == FILTER_BTN_IDX) {
                        int w = getFilterBtnSizePix();
                        view.addView(customLayout, new FrameLayout.LayoutParams(w, FrameLayout.LayoutParams.MATCH_PARENT, Gravity.CENTER_VERTICAL | Gravity.END));
                    } else {
                        view.addView(customLayout, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
                    }
                } else {
                    view.addView(customLayout, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
                }

                view.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (isSearchV2ResultsEnabled()) {
                            showResults(searchBean);
                        } else {
                            viewModel.setSearchType(tabBean.serp_tab_key, pageType);
                        }
                        mainTab.handlePageSelected(index);
                        //顶部tab
                        EagleTrackManger.get().trackEagleClickAction(null,
                                -1,
                                null,
                                -1,
                                tabBean.serp_tab_key,
                                index,
                                EagleTrackEvent.TargetType.FILTER_BUTTON,
                                EagleTrackEvent.ClickType.VIEW);
                    }
                });
                return view;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });
        mainTab.setNavigator(navigator);
        mainTab.handlePageSelected(0);
    }

    private void initGroceryTab(SearchBean bean) {
        if (isSearchV2ResultsEnabled()
                && SearchResultsFragmentV2.USE_SEARCH_FILTERS_BUTTON_ON_SEARCH_BAR && !isSearchV2SectionTabsEnabled())
            return;

        final boolean hideFilter = isSearchV2SectionTabsEnabled() && getSearchResultsV2Context().hasFilterSections();
        final boolean filtersButtonAsFirstItem = isSearchV2SectionTabsEnabled() && !hideFilter && Constants.SearchV2.ENABLE_SECTION_TABS_FILTERS_SCROLLABLE;
        final boolean hideGroceryTab = isSearchV2SectionTabsEnabled() && !getSearchResultsV2Context().shouldShowSectionTabs();
        if (hideGroceryTab) {
            layoutSubTab.setVisibility(View.GONE);
            groceryViewPager2.setOverScrollMode(View.OVER_SCROLL_NEVER);
            groceryViewPager2.setUserInputEnabled(false);
        } else {
            groceryViewPager2.setOverScrollMode(View.OVER_SCROLL_IF_CONTENT_SCROLLS);
            groceryViewPager2.setUserInputEnabled(true);
        }

        List<FilterProductListBean.CategoriesBean> list = bean.groceryTabList;
        if (!EmptyUtils.isEmpty(list) && !isSearchV2SectionTabsEnabled()) {
            list.get(0).catalogue_name = getString(R.string.s_cate_all);
        }
        TrackNavigator navigator = new TrackNavigator(activity);
        int margin = CommonTools.dp2px(6);
        int padding = CommonTools.dp2px(10);
        navigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return list.size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, int index) {
                final FilterProductListBean.CategoriesBean subCateBean = list.get(index);

                final View titleView;
                final View buttonView;
                final AppCompatTextView textView;

                final int titleViewTextColor;
                final int titleViewSelectedTextColor = R.color.color_surface_400_fg_default;
                final Drawable titleViewBackground;
                final Drawable titleViewSelectedBackground;

                final SearchResultSection section;

                if (isSearchV2SectionTabsEnabled()) {
                    section = getSearchResultsV2Context().getSectionTab(index);

                    if (filtersButtonAsFirstItem && index == 0 && layoutSectionTabsFilter != null) {
                        ViewGroup parent = (ViewGroup) layoutSectionTabsFilter.getParent();
                        if (parent != null) {
                            parent.removeView(layoutSectionTabsFilter);
                        }
                        titleView = layoutSectionTabsFilter;
                        buttonView = layoutSectionTabsFilter.findViewById(R.id.search_v2_section_tabs_button_view);
                    } else {
                        titleView = buttonView = View.inflate(context, R.layout.search_v2_section_tabs_text_icon_view, null);
                    }
                    textView = titleView.findViewById(R.id.search_v2_section_tabs_text_view);
                    CustomImageView iconView = titleView.findViewById(R.id.search_v2_section_tabs_icon_view);
                    if (iconView != null) {
                        iconView.setClipToOutline(true);
                    }
                    if (iconView != null && !EmptyUtils.isEmpty(subCateBean.catalogue_image_url)) {
                        iconView.cancelAndClear();
                        iconView.setVisibility(View.VISIBLE);

                        int desiredImageSize = CommonTools.dp2px(26);
                        iconView.load(subCateBean.catalogue_image_url,
                                desiredImageSize, desiredImageSize,
                                subCateBean.catalogue_image_filter, subCateBean.catalogue_image_flags,
                                null, null).show();
                    } else {
                        iconView.setVisibility(View.GONE);
                    }

                    titleViewTextColor = R.color.color_surface_100_fg_minor;
                    titleViewBackground = ContextCompat.getDrawable(context, R.drawable.search_v2_section_tabs_item_bg);
                    titleViewSelectedBackground = ContextCompat.getDrawable(context, R.drawable.search_v2_section_tabs_item_selected_bg);
                } else {
                    section = null;

                    titleView = buttonView = textView = new AppCompatTextView(context);
                    textView.setPadding(padding, 0, padding, 0);
                    textView.setMaxLines(1);
                    textView.setGravity(Gravity.CENTER);
                    ViewTools.applyTextStyle(textView, R.style.style_body_xs_medium);

                    int titleViewRadius = CommonTools.dp2px(12);
                    titleViewTextColor = R.color.color_surface_100_fg_minor;
                    titleViewBackground = ShapeHelper.buildDrawable(
                            ContextCompat.getColor(context, R.color.color_surface_100_bg),
                            titleViewRadius,
                            ContextCompat.getColor(context, R.color.color_surface_100_hairline),
                            CommonTools.dp2px(1.5f)
                    );
                    titleViewSelectedBackground = ShapeHelper.buildSolidDrawable(ContextCompat.getColor(activity, R.color.color_btn_secondary_bg), titleViewRadius);
                }
                if (textView != null) textView.setText(subCateBean.catalogue_name);

                CommonPagerTitleView view = new TrackCommonPagerTitleView(activity) {
                    @Override
                    public void onImpressionTrigger() {
                        String key = index + "_" + subCateBean.catalogue_num + pageType;
                        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_SEARCH, key)) {
                            Map filterMap = null;
                            try {
                                filterMap = JSON.parseObject(viewModel.searchManager.filter);
                            } catch (Exception ignored) {

                            }
                            if (filterMap != null) {
                                filterMap.remove("catalogue_num");
                            }
                            Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, catalogueNum, viewModel.searchManager.getSorts(), filterMap, viewModel.searchManager.getKeyWord(), null, null, null, traceId);
                            if (isSearchV2SectionTabsEnabled() && section != null) {
                                SearchResultSection subSection;
                                SearchResultSection searchResultSectionDisplayed = searchResultsSelectedTabFragment != null ? searchResultsSelectedTabFragment.getSearchResultSectionDisplayed() : null;
                                if (searchResultSectionDisplayed != null && searchResultSectionDisplayed.isFilterSection()) {
                                    subSection = searchResultSectionDisplayed;
                                } else {
                                    subSection = null;
                                }

                                JSONObject sectionData = section.getSectionData();
                                String sectionTag = sectionData != null ? sectionData.optString(SearchJsonField.ANALYTICS_TAG) : null;
                                SearchV2Manager.get().appendEagleTrackContextParams(ctx,
                                        sectionTag,
                                        section.getSectionKey(),
                                        SearchResultsFragmentV2.TRACK_EVENT_SECTION_TYPE_TOPBAR,
                                        SearchResultsFragmentV2.TRACK_EVENT_SECTION_DISPLAY_TYPE_TOPBAR,
                                        subSection != null ? subSection.getSectionKey() : null,
                                        subSection != null ? subSection.getSectionTag() : null
                                );
                            }
                            Map<String, Object> params = new EagleTrackModel.Builder()
                                    .setMod_nm("filter_buttons")
                                    .setMod_pos(0)
                                    .setButton_nm(subCateBean.catalogue_num)
                                    .setButton_pos(index)
                                    .setIs_selected(subCateBean.selected)
                                    .addCtx(ctx)
                                    .build().getParams();
                            AppAnalytics.logFilterButtonImp(params);
                            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_SEARCH, key);
                        }
                    }

                    @Override
                    public void onSelected(int index, int totalCount) {
                        super.onSelected(index, totalCount);
                        buttonView.setBackground(titleViewSelectedBackground);
                        textView.setTextColor(ContextCompat.getColor(activity, titleViewSelectedTextColor));
                        catalogueNum = subCateBean.catalogue_num;
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        super.onDeselected(index, totalCount);
                        buttonView.setBackground(titleViewBackground);
                        textView.setTextColor(ContextCompat.getColor(activity, titleViewTextColor));
                    }
                };

                FrameLayout.MarginLayoutParams params =
                        new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, CommonTools.dp2px(32));
                int firstItemLeftMargin = hideFilter || filtersButtonAsFirstItem
                        ? getResources().getDimensionPixelSize(R.dimen.prop_size_spacing_300)
                        : CommonTools.dp2px(22 + 6);
                params.leftMargin = index == 0 ? firstItemLeftMargin : CommonTools.dp2px(6);
                params.rightMargin = index == list.size() - 1 ? margin : 0;
                view.addView(titleView, params);
                View clickView = filtersButtonAsFirstItem ? buttonView : view;
                clickView.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        setGroceryViewPage(index);
//                        Objects.requireNonNull(groceryViewPager2.getAdapter()).notifyItemChanged(index);
                        //副分类
                        Map<String, Object> ctx = new EagleContext().setPageTarget(viewModel.searchManager.getKeyWord()).setTraceId(traceId).asMap();
                        if (isSearchV2SectionTabsEnabled() && section != null) {
                            SearchResultSection subSection;
                            SearchResultSection searchResultSectionDisplayed = searchResultsSelectedTabFragment != null ? searchResultsSelectedTabFragment.getSearchResultSectionDisplayed() : null;
                            if (searchResultSectionDisplayed != null && searchResultSectionDisplayed.isFilterSection()) {
                                subSection = searchResultSectionDisplayed;
                            } else {
                                subSection = null;
                            }

                            JSONObject sectionData = section.getSectionData();
                            String sectionTag = sectionData != null ? sectionData.optString(SearchJsonField.ANALYTICS_TAG) : null;
                            SearchV2Manager.get().appendEagleTrackContextParams(ctx,
                                    sectionTag,
                                    section.getSectionKey(),
                                    SearchResultsFragmentV2.TRACK_EVENT_SECTION_TYPE_TOPBAR,
                                    SearchResultsFragmentV2.TRACK_EVENT_SECTION_DISPLAY_TYPE_TOPBAR,
                                    subSection != null ? subSection.getSectionKey() : null,
                                    subSection != null ? subSection.getSectionTag() : null
                            );
                        }
                        EagleTrackManger.get().trackEagleClickAction("filter_buttons",
                                0,
                                null,
                                -1,
                                subCateBean.catalogue_num,
                                index,
                                "filter_button",
                                EagleTrackEvent.ClickType.VIEW,
                                ctx, null, null);
                        if (viewModel.getRealTimeFilter(subCateBean.catalogue_num) != null) {
                            groceryFilter(viewModel.getRealTimeFilter(subCateBean.catalogue_num));//切副分类时使用缓存替换filter dialog
                            initFilterShowOutTab();//切副分类时使用缓存替换filter show out
                        }
                    }
                });
                return view;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });

        if (isSearchV2SectionTabsEnabled() && Constants.SearchV2.ENABLE_SECTION_TABS_PRELOAD_VISIBLE_TABS) {
            navigator.setFirstTimeVisibleListener((index, view) -> {
                SearchResultsV2Context searchContext = getSearchResultsV2Context();
                if (searchContext == null || searchContext.isReleased()) return;

                SearchResultSection searchResultSection = getSearchResultsV2Context().getSectionTab(index);
                if (searchResultSection != null && !searchResultSection.hasSectionData()) {
                    searchResultSection.preloadIfNeededAsync(SearchResultSection.PRELOAD_ACTION_SECTION_DATA);
                }
            }, 1000);
        }

        subTab.setNavigator(navigator);
        subTab.bindSafely(groceryViewPager2);

        if (filtersButtonAsFirstItem && layoutSectionTabsFilter != null) {
            ViewParent parent = layoutSectionTabsFilter.getParent();
            while (parent instanceof ViewGroup) {
                ((ViewGroup) parent).setClipChildren(false);
                parent = parent != layoutSubTab ? parent.getParent() : null;
            }
        }
    }

    //filterShowOutTab模块
    public void initFilterShowOutTab() {
        if (getFiltersShowOut().isEmpty() || isTypePostEditSearch()) {
            if (getFiltersShowOut().isEmpty() && (searchBean != null && searchBean.t != null && !searchBean.t.show_categories)) {//category和filter全无特殊case
                ViewTools.setViewVisible(true, layoutSubSubTab);
                ViewTools.setViewVisible(false, subSubTab);
                setTopSearchBarFilterButtonVisibility(false);
            } else {
                ViewTools.setViewVisible(false, layoutSubSubTab);
                setTopSearchBarFilterButtonVisibility(true);
            }
            return;
        } else {
            ViewTools.setViewVisible(true, layoutSubSubTab, subSubTab);
            if (searchBean != null && searchBean.t != null && !searchBean.t.show_categories) {
                ViewTools.setViewVisible(false, layoutSubTab);//filter show out時，show_categories為false.
                ViewTools.setViewVisible(true, ivSubSort, tvSubFilterNum);
                isSubFilterBtnShow = true;
                setFilterQty();//filter show out時,设置选中qty展示.
            } else {
                ViewTools.setViewVisible(false, ivSubSort, tvSubFilterNum);
            }
        }

        filterNavigator = new TrackNavigator(activity);
        int margin = CommonTools.dp2px(6);
        int padding = CommonTools.dp2px(10);
        filterNavigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return getFiltersShowOut().size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, int index) {
                final ProductPropertyValueBean item = getFiltersShowOut().get(index);
                AppCompatTextView titleView = new AppCompatTextView(context);
                titleView.setText(item.value_name);
                titleView.setPadding(padding, 0, padding, 0);
                titleView.setMaxLines(1);
                titleView.setGravity(Gravity.CENTER);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    titleView.setTextAppearance(R.style.style_fluid_root_utility_sm);
                }
                CommonPagerTitleView view = new TrackCommonPagerTitleView(activity) {
                    @Override
                    public void onImpressionTrigger() {

                    }

                    @Override
                    public void onSelected(int index, int totalCount) {
                        //super.onSelected(index, totalCount);
                        titleView.setBackground(ShapeHelper.buildStrokeDrawable(ContextCompat.getColor(activity, R.color.text_main), CommonTools.dp2px(1.5f), CommonTools.dp2px(14)));
                        titleView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                        titleView.setTextColor(ContextCompat.getColor(activity, R.color.text_main));
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        //super.onDeselected(index, totalCount);
                        titleView.setBackground(ShapeHelper.buildStrokeDrawable(ContextCompat.getColor(activity, R.color.color_input_100_hairline_disabled), CommonTools.dp2px(1), CommonTools.dp2px(14)));
                        titleView.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                        titleView.setTextColor(ContextCompat.getColor(activity, R.color.text_minor));
                    }
                };

                FrameLayout.MarginLayoutParams params =
                        new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, CommonTools.dp2px(32));
                params.leftMargin = margin;
                params.rightMargin = index == getFiltersShowOut().size() - 1 ? margin : 0;
                view.addView(titleView, params);
                view.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (index < getFiltersShowOut().size()) {
                            ProductPropertyValueBean filter = getFiltersShowOut().get(index);
                            if (!filter.selected) {
                                filter.selected = true;
                                onFilterClick(filter, true);
                                view.onSelected(index, getFiltersShowOut().size());
                                if (isSingleType() && indexSelectedOld != -1 && indexSelectedOld != index) {
                                    subSubTab.getCommonPagerTitleView(indexSelectedOld).onDeselected(indexSelectedOld, getFiltersShowOut().size());
                                }
                                indexSelectedOld = index;
                            } else {
                                filter.selected = false;
                                onFilterClick(filter, false);
                                view.onDeselected(index, getFiltersShowOut().size());
                            }
                            setFiltersShowOutSelected(getFiltersShowOut());
                            //筛选条件分类
                            EagleTrackManger.get().trackEagleClickAction("filter_sub_buttons",
                                    2,
                                    null,
                                    -1,
                                    item.value_key,
                                    index,
                                    "filter_sub_button",
                                    EagleTrackEvent.ClickType.VIEW);
                        }
                    }
                });
                return view;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });
        subSubTab.setNavigator(filterNavigator);
        subSubTab.onPageSelected(-1);
        setFiltersShowOutSelected(getFiltersShowOut());
    }

    @NonNull
    private List<ProductPropertyValueBean> getFiltersShowOut() {
        List<ProductPropertyValueBean> list = new ArrayList<>();
        SearchBean searchBeanCache = viewModel.getRealTimeFilter(catalogueNum);//获取实时filters show out数据
        if (searchBeanCache == null) {
            return list;
        }
        FilterProductListBean bean = searchBeanCache.t;
        if (!EmptyUtils.isEmpty(bean.filters)) {
            for (ProductFilterBean filtersBean : bean.filters) {
                if (filtersBean.show_out) {
                    if (filtersBean.property_show_type.equals(FilterListAdapter.BOOLEAN)) {
                        ProductPropertyValueBean bean1 = new ProductPropertyValueBean();
                        bean1.show_type = filtersBean.property_show_type;
                        bean1.value_name = filtersBean.property_name;
                        bean1.value_key = filtersBean.property_key;
                        bean1.selected = filtersBean.property_values.get(0).selected;
                        list.add(bean1);
                    } else if ((filtersBean.property_show_type.equals(FilterListAdapter.MULTIPLE) || filtersBean.property_show_type.equals(FilterListAdapter.SINGLE)) && !EmptyUtils.isEmpty(filtersBean.property_values)) {
                        for (ProductPropertyValueBean property_value : filtersBean.property_values) {
                            if (property_value.show_out) {
                                ProductPropertyValueBean bean2 = new ProductPropertyValueBean();
                                bean2.show_type = filtersBean.property_show_type;
                                bean2.value_name = property_value.value_name;
                                bean2.value_key = property_value.value_key;
                                bean2.selected = property_value.selected;
                                list.add(bean2);
                            }
                        }
                    }
                }
            }
        }
        return list;
    }

    private void onFilterClick(ProductPropertyValueBean item, boolean isSelect) {
        SearchBean searchBeanCache = viewModel.getRealTimeFilter(catalogueNum);//点击filter show out模块
        if (searchBeanCache == null) {
            return;
        }
        FilterProductListBean bean = searchBeanCache.t;
        if (bean != null && !EmptyUtils.isEmpty(bean.filters)) {
            for (ProductFilterBean filter : bean.filters) {
                if (filter.property_show_type.equals(FilterListAdapter.BOOLEAN)) {
                    if (filter.property_key.equals(item.value_key) && filter.property_name.equals(item.value_name)) {
                        filter.property_values.get(0).selected = isSelect;
                        break;
                    }
                } else if ((filter.property_show_type.equals(FilterListAdapter.MULTIPLE) || filter.property_show_type.equals(FilterListAdapter.SINGLE))
                        && !EmptyUtils.isEmpty(filter.property_values)) {
                    for (ProductPropertyValueBean property_value : filter.property_values) {
                        if (property_value.value_key.equals(item.value_key) && property_value.value_name.equals(item.value_name)) {
                            property_value.selected = isSelect;
                            //单选情况下，其余全部不选中
                            if (filter.property_show_type.equals(FilterListAdapter.SINGLE) && isSelect) {
                                for (ProductPropertyValueBean value : filter.property_values) {
                                    if (!value.value_key.equals(item.value_key)) {
                                        value.selected = false;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
            setFilterQty();//点击filter show out tab
            filter();//filter show out tab
        }
    }

    /**
     * 填充页面数据
     *
     * @param bean 条目数据源
     */
    private void groceryViewPager(SearchBean bean) {
        List<FilterProductListBean.CategoriesBean> categories = bean.groceryTabList;

        if (isSearchV2SectionTabsEnabled()) {
            groceryViewPager2.setAdapter(null);
            if (refreshScrollHandlerPageChangeCallback != null) {
                groceryViewPager2.unregisterOnPageChangeCallback(refreshScrollHandlerPageChangeCallback);
            }
            searchResultsTabsFragments = new HashMap<>(searchResultsTabsFragmentsAmount);

            final SearchResultsV2Context searchResultsV2Context = getSearchResultsV2Context();

            FragmentStateAdapter adapter = new FragmentStateAdapter(this) {
                Map<String, Map<String, Object>> savedStates = new HashMap<>(categories.size());

                @Override
                public int getItemCount() {
                    return isValidAdapter() ? categories.size() : 0;
                }

                @NonNull
                @Override
                public Fragment createFragment(int position) {
                    long itemId = getItemId(position);
                    SearchResultSection searchResultSection = getSearchResultSection(position);
                    String sectionKeyPath = searchResultSection != null ? searchResultSection.getSectionKeyPath() : null;
                    SearchResultsFragmentV2 fragment = SearchResultsFragmentV2.newInstance(sectionKeyPath);
                    searchResultsTabsFragments.put(itemId, fragment);
                    return fragment;
                }

                @Override
                public boolean containsItem(long itemId) {
                    SearchResultsFragmentV2 fragment = searchResultsTabsFragments.get(itemId);
                    return fragment != null;
                }

                @Override
                public long getItemId(int position) {
                    long itemId = position % searchResultsTabsFragmentsAmount;
                    SearchResultsFragmentV2 fragment = searchResultsTabsFragments.get(itemId);
                    if (fragment != null) {
                        updateFragmentIfNeeded(position, fragment);
                    }
                    return itemId;
                }

                private boolean isValidAdapter() {
                    boolean isValid = searchResultsTabsFragments != null
                            && !searchResultsV2Context.isReleased();
                    return isValid;
                }

                private void updateFragmentIfNeeded(int position, SearchResultsFragmentV2 fragment) {
                    if (!isValidAdapter()) return;

                    SearchResultSection searchResultSection = getSearchResultSection(position);
                    SearchResultSection currentSearchResultSection = fragment != null
                            ? fragment.getSearchResultSection() : null;
                    boolean sectionChanged = currentSearchResultSection != searchResultSection;
                    if (sectionChanged) {
                        String currentSectionKey = currentSearchResultSection != null
                                ? currentSearchResultSection.getSectionKey()
                                : null;
                        SearchResultsV2MainSectionAdapter adapter = fragment.getListAdapter();
                        if (adapter != null && !adapter.isReleased() && !EmptyUtils.isEmpty(currentSectionKey)) {
                            Map<String, Object> currentState = new HashMap<>();
                            adapter.onSaveInstanceStateInMemory(currentState);
                            savedStates.put(currentSectionKey, currentState);
                        }

                        if (searchResultSection != null) {
                            String nextSectionKey = searchResultSection.getSectionKey();
                            Map<String, Object> savedState = savedStates.get(nextSectionKey);
                            fragment.setupSearchResultSection(searchResultSection, savedState);
                        }
                    }
                }

                private SearchResultSection getSearchResultSection(int position) {
                    if (!isValidAdapter()) return null;
                    SearchResultSection searchResultSection = searchResultsV2Context.getSectionTab(position);
                    return SearchResultSection.getSection(searchResultSection);
                }
            };
            adapter.setStateRestorationPolicy(RecyclerView.Adapter.StateRestorationPolicy.PREVENT);
            groceryViewPager2.setAdapter(adapter);

            refreshScrollHandlerPageChangeCallback = new ViewPager2.OnPageChangeCallback() {
                private boolean isCallbackValid() {
                    boolean isValid = refreshScrollHandlerPageChangeCallback != null
                            && groceryViewPager2 != null
                            && groceryViewPager2.getAdapter() != null
                            && groceryViewPager2.getAdapter() == adapter;
                    return isValid;
                }

                @Override
                public void onPageSelected(int position) {
                    if (!isCallbackValid()) return;

                    super.onPageSelected(position);

                    getSearchResultsV2Context().setSectionTabSelected(position);

                    RecyclerView.Adapter currentAdapter = groceryViewPager2 != null
                            ? groceryViewPager2.getAdapter() : null;
                    Long itemId = currentAdapter != null && currentAdapter == adapter
                            ? currentAdapter.getItemId(position) : null;
                    if (searchResultsTabsFragments != null && itemId != null && itemId >= 0 && itemId < searchResultsTabsFragments.size()) {
                        searchResultsSelectedTabFragment = searchResultsTabsFragments.get(itemId);
                        searchPopoverProductsView.setupAdapter(searchResultsSelectedTabFragment);
                    }
                    SearchV2Manager.get().updateSearchCrashInfo(SearchPanelActivity.this);
                }

                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                    super.onPageScrolled(position, positionOffset, positionOffsetPixels);

                    if (!EmptyUtils.isEmpty(searchResultsTabsFragments) && isCallbackValid()) {
                        long lItemId = adapter.getItemId(position);
                        long rItemId = adapter.getItemId(position + 1);
                        SearchResultsFragmentV2 lFragment = searchResultsTabsFragments.get(lItemId);
                        SearchResultsFragmentV2 rFragment = searchResultsTabsFragments.get(rItemId);
                        if (lFragment != null)
                            lFragment.onPageTransition(lFragment, rFragment, positionOffset);
                        if (rFragment != null)
                            rFragment.onPageTransition(lFragment, rFragment, positionOffset);
                    }
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                    super.onPageScrollStateChanged(state);

                    if (state == ViewPager2.SCROLL_STATE_IDLE && isCallbackValid()) {
                        int position = groceryViewPager2.getCurrentItem();
                        Long itemId = adapter.getItemId(position);
                        SearchResultsFragmentV2 fragmentV2 = searchResultsTabsFragments.get(itemId);
                        if (fragmentV2 != null) {
                            fragmentV2.refreshScrollHandler();
                        }
                    }
                }
            };
            groceryViewPager2.registerOnPageChangeCallback(refreshScrollHandlerPageChangeCallback);
        } else {
            groceryViewPager2.setAdapter(new FragmentStateAdapter(this) {

                @Override
                public int getItemCount() {
                    return categories.size();
                }

                @NonNull
                @Override
                public Fragment createFragment(int position) {
                    String pageId = "";
                    if (categories.get(position) != null) {
                        pageId = categories.get(position).catalogue_num;
                    }
                    int filterTotalCount = isMainTabShow ? -1 : DecimalTools.parseInt(categories.get(position).count);
                    return SearchPanelFragment.newInstance(bean.type, pageId, listChecked, pageType, filterTotalCount, position);
                }

            });
        }

        setGroceryViewPage(bean.getCategoryIndex());
    }

    private void setGroceryViewPage(int item) {
        groceryViewPager2.setCurrentItem(item, false);
        if (isSearchV2SectionTabsEnabled()) {
            getSearchResultsV2Context().setSectionTabSelected(item);
        }
    }

    private void clearGroceryViewPager() {
        groceryViewPager2.setAdapter(null);
        searchResultsTabsFragments = null;

        if (refreshScrollHandlerPageChangeCallback != null) {
            groceryViewPager2.unregisterOnPageChangeCallback(refreshScrollHandlerPageChangeCallback);
            refreshScrollHandlerPageChangeCallback = null;
        }

        groceryViewPager2.unregisterOnPageChangeCallback(pageChangeCallback);
        if (!fallbackSearchResultsTypeRequested) {
            SearchManager.get().release();
            SearchManager.get().filterCache.clear();//清除filter缓存
        }
    }


    /**
     * 筛选按钮处理
     *
     * @param searchBeanCache
     */
    public void groceryFilter(SearchBean searchBeanCache) {
        FilterProductListBean bean = searchBeanCache.t;
        ivFilterTag.setImageResource(R.drawable.drawable_filter_32x32);
        ivSubSort.setImageResource(R.drawable.drawable_filter_32x32);
        //被选中的筛选条件 数目
        tvFilterNum.setBackground(ShapeHelper.buildSolidDrawable(getResources().getColor(R.color.color_btn_secondary_bg), CommonTools.dp2px(12)));
        tvSubFilterNum.setBackground(ShapeHelper.buildSolidDrawable(getResources().getColor(R.color.color_btn_secondary_bg), CommonTools.dp2px(12)));
        setTopSearchBarFilterButtonCountBadgeBackground();
        setFilterQty();
        // 筛选按钮
        if (searchBean != null && searchBean.t != null) {
            View topBarFilterButton = topSearchBarFilterBtn;
            ViewTools.setViewOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    new FilterDialog(activity)
                            .setFilterData(searchBean.t.sorts, bean.filters, "search")
                            .setOnApplyListener((dialog, sorts, filters) -> {
                                dialog.dismiss();
                                searchBean.t.sorts.clear();
                                searchBean.t.sorts.addAll(sorts);
                                bean.filters.clear();
                                bean.filters.addAll(filters);

                                SearchBean temp = SearchManager.get().filterCache.get(catalogueNum);
                                if (temp != null && temp.t != null) {
                                    temp.t.filters = bean.filters;
                                }
                                setFilterQty();//点击filter dialog
                                filter();//filter dialog
                                //apply按钮click事件
                                Map filterMap = null;
                                try {
                                    filterMap = JSON.parseObject(viewModel.searchManager.filter);//埋点ctx
                                } catch (Exception ignored) {

                                }
                                Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, viewModel.searchManager.getSorts(), filterMap, null, null, null, sellerId);
                                EagleTrackManger.get().trackEagleClickAction(null, -1, null, -1, "apply", -1, EagleTrackEvent.TargetType.APPLY_BUTTON, EagleTrackEvent.ClickType.VIEW, ctx);
                                setFiltersShowOutSelected(getFiltersShowOut());//同步filter show out tab状态
                            })
                            .show();
                }
            }, ivFilterTag, sectionTabsFilter, ivSubSort, topBarFilterButton);
        }
    }

    private void setFiltersShowOutSelected(@NonNull List<ProductPropertyValueBean> filters) {
        if (layoutSubSubTab.getVisibility() == View.VISIBLE) {
            for (ProductPropertyValueBean bean : filters) {
                int i = filters.indexOf(bean);
                try {
                    if (bean.selected) {
                        if (isSingleType()) {
                            subSubTab.onPageSelected(i);
                        }
                        subSubTab.getCommonPagerTitleView(i).onSelected(i, filters.size());
                        indexSelectedOld = i;
                    } else {
                        subSubTab.getCommonPagerTitleView(i).onDeselected(i, filters.size());
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }

    private void setFilterQty() {
        int qty = 0;
        if (searchBean != null && searchBean.t != null && searchBean.t.sorts != null) {
            for (ProductSortBean sort : searchBean.t.sorts) {
                //精选（默认）不计算在内
                if (!sort.sort_def && sort.selected) {
                    qty = qty + 1;
                }
            }
        }
        SearchBean searchBeanCache = viewModel.getRealTimeFilter(catalogueNum);//设置filter qty
        if (SearchResultsFragmentV2.USE_SEARCH_FILTERS) {
            searchBeanCache = searchBean;
        }
        if (searchBeanCache != null) {
            FilterProductListBean filterCache = searchBeanCache.t;
            if (filterCache != null && !EmptyUtils.isEmpty(filterCache.filters)) {
                for (ProductFilterBean filter : filterCache.filters) {
                    for (ProductPropertyValueBean property_value : filter.property_values) {
                        if (!property_value.value_def && property_value.selected) {
                            qty = qty + 1;
                        }
                    }
                }
            }
        }
        tvFilterNum.setText(String.valueOf(qty));
        tvFilterNum.setVisibility(qty > 0 ? View.VISIBLE : View.INVISIBLE);
        if (searchBean != null && !searchBean.t.show_categories) {
            tvSubFilterNum.setText(String.valueOf(qty));
            tvSubFilterNum.setVisibility(qty > 0 ? View.VISIBLE : View.INVISIBLE);
        }

        setTopSearchBarFilterButtonCountValue(qty);
    }

    /**
     * 失败页面处理
     *
     * @param bean 错误源
     */
    private void failureStatus(FailureBean bean) {
        showEmpty(0);
        if (!EmptyUtils.isEmpty(keyWord)) {
            final String s;
            if (isSearchV2ResultsEnabled()) {
                s = getString(R.string.s_search_results_not_found, keyWord);
            } else {
                s = getString(R.string.s_search_sry, keyWord);
            }
            tvKeySearchNull.setText(s);
            layoutRequestProduct.setOnClickListener(v -> {
                startActivity(RequestProductActivity.getIntent(activity, keyWord));
            });
        }
    }

    public void toggleProduct(SimpleProductBean product) {
        if (listChecked == null) {
            return;
        }
        Iterator<SimpleProductBean> iterator = listChecked.iterator();
        while (iterator.hasNext()) {
            SimpleProductBean productBean = iterator.next();
            if (productBean.id == product.id) {
                iterator.remove();//取消选中
                refreshConfirmBtn();
                return;
            }
        }
        //最多选中20个商品
        int maxProductNumber = viewModel.getMaxProductNumber(comment, isAffiliate);
        if (!EmptyUtils.isEmpty(listChecked) && listChecked.size() >= maxProductNumber) {
            Toaster.showToast(String.format(getString(R.string.s_items_up_to), maxProductNumber));
            return;
        }
        listChecked.add(product);//选中
        refreshConfirmBtn();
    }


    //简易样式搜索列表页的确认按钮
    private void refreshConfirmBtn() {
        if (EmptyUtils.isEmpty(listChecked)) {
            tvConfirm.setVisibility(View.GONE);
        } else {
            tvConfirm.setVisibility(View.VISIBLE);
            tvConfirm.setText(new StringBuilder().append(getString(R.string.s_add)).append("(").append(listChecked.size()).append(")").toString());
        }
    }

    private void back(boolean b) {
        if (searchPopoverProductsView != null && searchPopoverProductsView.isOpen() && !searchPopoverProductsView.isClosing()) {
            searchPopoverProductsView.close(true);
            return;
        }

        final boolean checkBackToSearchResults = !EmptyUtils.isEmpty(keyWord)
                && (isSearchV2ResultsEnabled() || isSearchV2SuggestUIEnabled() || isSearchAutocompleteHomeXp());
        if (checkBackToSearchResults) {
            final boolean shouldBackToSearchResults;
            final boolean enableOnlyForSpecificXP = false;
            if (enableOnlyForSpecificXP) {
                shouldBackToSearchResults = (isRecordShowing() && (isSearchV2ResultsEnabled() || isSearchAutocompleteHomeXp()))
                        || (isSuggestShowing() && (isSearchV2ResultsEnabled() || isSearchV2SuggestUIEnabled()));
            } else {
                shouldBackToSearchResults = searchTermView.isSearchActive() || isRecordShowing() || isSuggestShowing();
            }
            if (shouldBackToSearchResults) {
                noResults = searchBean != null && searchBean.isShowEmpty;
                setSearchResultVisible(View.VISIBLE);
                showFragment(false, false);
                searchTermView.setEtText(keyWord);
                return;
            }
        }

        Intent intent = new Intent();
        intent.putExtra(KEY_LIST_FROM_EDIT, listChecked);
        intent.putExtra(KEY_IS_FINISH, b);
        setResult(RESULT_OK, intent);
        finish();
    }

    private void setTimerBannerVisible(boolean visible) {
        if (pageType != TYPE_MAIN_SEARCH) {
            visible = false;
        }
        if (visible) {
            TimerBannerManager.get().registerAndLog(timerChangedListener, findViewById(R.id.layout_timer_banner));
        } else {
            TimerBannerManager.get().unregisterTimerChangedListener(timerChangedListener);
            layoutTimerBanner.setVisibility(View.GONE);
        }
    }

    public void tabSwitch(int pos) {
        viewModel.tabSwitch(pos);//点击副标题切换副分类
    }

    public void logPv() {
        String pageName;
        Runnable runnable;
        if (pageType == TYPE_MAIN_SEARCH) {
            if (isFromSeller()) {
                pageName = WeeeEvent.PageView.SELLER_SEARCH_RESULT;
                runnable = () -> AppAnalytics.logPageView(pageName, this, null, AppAnalytics.putPvPageTarget(null, keyWord));
            } else if (global) {
                pageName = WeeeEvent.PageView.GLOBAL_SEARCH_RESULT;
                runnable = () -> AppAnalytics.logPageView(pageName, this, null, AppAnalytics.putPvPageTarget(null, keyWord));
            } else {
                pageName = WeeeEvent.PageView.SEARCH_RESULT;
                Map<String, Object> ctx = new ArrayMap<>();
                ctx.put("default_keyword", keywordByIntent);
                ctx.put("page_target", keyWord);
                ArrayMap<String, Object> params = new ArrayMap<>();
                params.put("ctx", ctx);
                runnable = () -> AppAnalytics.logPageView(pageName, this, null, params);
            }
        } else if (pageType == TYPE_POST_EDIT_SEARCH) {
            pageName = WeeeEvent.PageView.COMM_SEARCH_RESULT;
            runnable = () -> AppAnalytics.logPageView(pageName, this, null, AppAnalytics.putPvPageTarget(null, keyWord));
        } else {
            pageName = null;
            runnable = null;
        }
        logPageView(pageName, runnable);
    }

    public void logLandingPv() {
        String pageName;
        Runnable runnable;
        if (pageType == TYPE_MAIN_SEARCH) {
            if (isFromSeller()) {
                pageName = WeeeEvent.PageView.SELLER_SEARCH_LANDING;
            } else if (global) {
                pageName = WeeeEvent.PageView.GLOBAL_SEARCH_LANDING;
            } else {
                pageName = WeeeEvent.PageView.SEARCH_LANDING;
            }
            runnable = () -> AppAnalytics.logPageView(pageName, this, viewIdOnSearchLanding, null, null);
        } else {
            pageName = null;
            runnable = null;
        }
        logPageView(pageName, runnable);
    }

    protected void logPageView(@Nullable String pageName, @Nullable Runnable runnable) {
        if (reissuePageViewOnResume || (pageName != null && !pageName.equals(WeeeAnalytics.get().getCleanPageKey()))) {
            if (runnable != null) {
                runnable.run();
            }
            reissuePageViewOnResume = false;
        }
    }

    //backdoor
    private boolean filterSearch(String keyword) {
        if (keyword == null) {
            return false;
        }
        int tradeResult = DebugIntentCreator.handleDebugKeyword(activity, keyword);
        return tradeResult != UrlTradeProcessor.TRADE_NONE;
    }

    private void showMainTabVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_search_main_tab);
        View bg = findViewById(R.id.layout_search_main_tab_veil_bg);
        if (vl != null) {
            if (visible && !isSearchV2ResultsEnabled()) {
                bg.setVisibility(View.VISIBLE);
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                bg.setVisibility(View.GONE);
                vl.setVisibility(View.GONE);
                vl.unVeil();
            }
        }
    }

    private void showSubTabVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_search_sub_tab);
        if (vl != null) {
            if (visible && (!isSearchV2ResultsEnabled() || Constants.SearchV2.ENABLE_SECTION_TABS || !SearchResultsFragmentV2.USE_SEARCH_FILTERS_BUTTON_ON_SEARCH_BAR)) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.GONE);
                vl.unVeil();
            }
        }
    }

    public void showPagerVeil(boolean visible) {
        VeilLayout vl = findViewById(R.id.vl_search_result);
        if (vl != null) {
            if (visible) {
                vl.setVisibility(View.VISIBLE);
                vl.veil();
            } else {
                vl.setVisibility(View.INVISIBLE);
                vl.unVeil();
            }
        }
    }

    public boolean isTypePostEditSearch() {
        return TYPE_POST_EDIT_SEARCH == pageType;
    }

    private void filter() {
        long currentFilterTime = System.currentTimeMillis();
        if (isSearchV2ResultsEnabled()) {
            viewModel.filterV2(this, searchBean, pageType, currentFilterTime);
        } else {
            viewModel.filter(pageType, currentFilterTime);
        }
        showPagerVeil(true);
        ViewTools.setViewVisible(false, groceryViewPager2);
    }

    public void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        // 隐藏软键盘
        imm.hideSoftInputFromWindow(activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    public boolean isSingleType() {
        if (!EmptyUtils.isEmpty(getFiltersShowOut())) {
            return FilterListAdapter.SINGLE.equalsIgnoreCase(getFiltersShowOut().get(0).show_type);
        } else {
            return false;
        }
    }

    private void hideAllVeil() {
        showMainTabVeil(false);
        showSubTabVeil(false);
        showPagerVeil(false);
    }

    public boolean isFromSeller() {
        return !EmptyUtils.isEmpty(sellerId);//seller主页
    }

    public String getKeyWord() {
        return keyWord;
    }

    public SearchBean getSearchBean() {
        return searchBean;
    }

    private void searchUsingFallbackSearchResultsType(String keywork) {
        fallbackSearchResultsTypeRequested = true;
        hideKeyboard();
        finish();
        startActivity(SearchPanelActivity.getIntent(this, keywork, String.valueOf(SEARCH_RESULTS_DEFAULT_EC_API), null));
        this.overridePendingTransition(0, 0);
    }

    private void searchRestoringFromFallbackSearchResultsType(String keywork) {
        hideKeyboard();
        finish();
        startActivity(SearchPanelActivity.getIntent(this, keywork, null));
        this.overridePendingTransition(0, 0);
    }

    private Fragment getSearchSuggestFragment() {
        Fragment fragment = searchSuggestFragment;
        if (isSearchV2SuggestAvailable()) {
            if (searchSuggestFragmentV4 != null) {
                fragment = searchSuggestFragmentV4;
            }
        }
        return fragment;
    }

    private boolean isSearchResultsVisible() {
        if (isSearchV2ResultsEnabled() && searchRecordFragment != null && searchRecordFragment.isVisible()) {
            return false;
        }

        return (layoutSearchResults != null && layoutSearchResults.getVisibility() == View.VISIBLE);
    }

    public Integer getSearchResultsType() {
        return searchResultsType == null ? SEARCH_RESULTS_DEFAULT_EC_API : searchResultsType;
    }

    public Integer getSearchSuggestType() {
        return searchSuggestType == null ? SEARCH_SUGGEST_DEFAULT : searchSuggestType;
    }

    public String getCatalogueNum() {
        return catalogueNum;
    }

    public boolean isNewDesignV1Xp() {
        final boolean isNewDesignV1Xp = SearchV2Manager.get().isSearchResultsNewDesignXP()
                && pageType == TYPE_MAIN_SEARCH
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_GLOBAL_PLUS || !global)
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_SELLER || sellerId == null);
        return isNewDesignV1Xp;
    }

    public boolean isNewAutocompleteDesignV1Xp() {
        boolean inSearchAutocompleteNewDesignXP = SearchV2Manager.get().isSearchAutocompleteV2XP();
        final boolean isNewAutocompleteDesignV1Xp = inSearchAutocompleteNewDesignXP
                && pageType == TYPE_MAIN_SEARCH
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_GLOBAL_PLUS || !global)
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_SELLER || sellerId == null);
        return isNewAutocompleteDesignV1Xp;
    }

    public boolean isSearchAutocompleteHomeXp() {
        boolean inSearchAutocompleteHomeXP = SearchV2Manager.get().isSearchAutocompleteHomeXP();
        final boolean isSearchSuggestionsGroupsXP = inSearchAutocompleteHomeXP
                && pageType == TYPE_MAIN_SEARCH
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_GLOBAL_PLUS || !global)
                && (Constants.SearchV2.USE_SEARCH_V2_FOR_SELLER || sellerId == null);
        return isSearchSuggestionsGroupsXP;
    }

    public boolean isSearchV2ResultsEnabled() {
        return getSearchResultsType() == SEARCH_RESULTS_NEW_SEARCH_API_AND_UI;
    }

    public boolean isSearchV2SuggestUIEnabled() {
        return getSearchSuggestType() != SEARCH_SUGGEST_DEFAULT;
    }

    public boolean isSearchV2SuggestUIEnabled(int searchSuggestType) {
        return isSearchV2SuggestUIEnabled() && getSearchSuggestType() == searchSuggestType;
    }

    public boolean isSearchV2SuggestAvailable() {
        return isSearchV2SuggestUIEnabled() && (searchSuggestFragmentV4 != null);
    }

    public boolean isSearchV2SkuSuggestionsEnabled() {
        return Constants.SearchV2.USE_SEARCH_SKU_SUGGESTIONS && isSearchV2ResultsEnabled();
    }

    public boolean isSearchV2SectionTabsEnabled() {
        return Constants.SearchV2.ENABLE_SECTION_TABS
                && isSearchV2ResultsEnabled()
                && getSearchResultsV2Context() != null
                && getSearchResultsV2Context().hasSectionTabs();
    }

    public boolean isSearchV2SUIModeSingleList() {
        return isSearchV2SectionTabsEnabled()
                && getSearchResultsV2Context() != null
                && getSearchResultsV2Context().isUIModeSingleList();
    }

    public void showSectionProducts(JSONObject section, int position, String eagleTrackSource, String eagleTrackEventModNm, String eagleTrackSectionType) {
        if (searchResultsSelectedTabFragment == null || searchPopoverProductsView == null || section == null)
            return;

        searchPopoverProductsView.openSectionProducts(section, position, searchResultsSelectedTabFragment.onItemClickListener, true, eagleTrackSource, eagleTrackEventModNm, eagleTrackSectionType);
    }

    public void showRelatedProducts(JSONObject referenceProduct, int position) {
        if (searchResultsSelectedTabFragment == null || searchPopoverProductsView == null || referenceProduct == null)
            return;

        final int productId = referenceProduct.optInt(SearchJsonField._SKU, 0);
        if (productId <= 0) return;

        final Integer openRequestId = searchPopoverProductsView.openRelatedProducts(referenceProduct, position, searchResultsSelectedTabFragment.onItemClickListener, true);
        if (openRequestId != null) {
            viewModel.loadRelatedProducts(this, openRequestId, productId, keyWord);
        }
    }

    private void onRelatedProductsLoaded(JSONObject relatedProducts) {
        final int openRequestId = relatedProducts != null ? relatedProducts.optInt(SearchJsonField._OPEN_REQUEST_ID, 0) : 0;
        if (openRequestId > 0) {
            searchPopoverProductsView.setProductsSection(openRequestId, relatedProducts);
        }
    }

    @SuppressLint("SetTextI18n")
    private void setHostTag() {
        try {
            View view = findViewById(R.id.tv_host);
            if (view instanceof TextView) {
                final String host = AppConfig.HOST;

                List<String> hostTags = new ArrayList<>();

                if (!DevConfig.isProd()) {
                    String hostTag;
                    if (DevConfig.isFlavorLatino()) {
                        hostTag = host.replaceFirst(".*//(api.)?((.*)[.])masgusto.*", "$3");
                    } else {
                        hostTag = host.replaceFirst(".*//(api.)?((.*)[.])sayweee.*", "$3");
                    }
                    if (!EmptyUtils.isEmpty(hostTag)) {
                        hostTags.add(hostTag.toUpperCase());
                    } else {
                        hostTags.add(host.replace("https://", ""));
                    }
                }

                if (Constants.SearchV2.IS_SEARCH_V2_ENABLED && DevConfig.isDebug()) {
                    hostTags.add("sv" + Constants.SearchV2.SEARCH_V2_VERSION);

                    if (Constants.SearchV2.USE_SEARCH_V2_BETA_HOST) {
                        hostTags.add("Beta");
                    }
                }

                if (!hostTags.isEmpty()) {
                    final String hostLabel = String.join("-", hostTags);
                    ((TextView) view).setText(hostLabel);
                    view.setVisibility(View.VISIBLE);
                }
            }
        } catch (Exception e) {
            Logger.enable(DevConfig.isDebug()).e(e);
        }
    }

    @Override
    public void onSuggestionsGroupClick(int groupPosition, String keyWord) {
        goSearch(keyWord);
    }

    @Override
    public void onSuggestionsGroupItemClick(int groupPosition, int itemPosition, String keyWord) {
        goSearch(keyWord);
    }

    @Override
    public void onSuggestionsGroupsUpdated(SearchSuggestionsGroupsBean bean) {
        if (searchRecordFragment != null) searchRecordFragment.updateSuggestionsGroupsVisibility();
    }

    public SearchPanelViewModel getViewModel() {
        return viewModel;
    }

    public SearchResultsV2Context getSearchResultsV2Context() {
        return viewModel != null ? viewModel.getSearchResultContext() : null;
    }

    public SearchResultsFragmentV2 getSearchResultsSelectedTabFragment() {
        return searchResultsSelectedTabFragment;
    }

    public SearchRecordFragment getSearchRecordFragment() {
        return searchRecordFragment;
    }

    public SearchV2TrackingManager getSearchV2TrackingManager() {
        if (!Constants.SearchV2.ENABLE_EAGLE_IMPRESSION_TRACKER_ON_SEARCH_V2 && this.searchV2TrackingManager == null) {
            this.searchV2TrackingManager = new SearchV2TrackingManager();
        }
        return this.searchV2TrackingManager;
    }

    private void onSearchResultsContextData(SearchResultsV2Context searchResultsContext) {
        if (searchV2TrackingManager != null) {
            searchV2TrackingManager.clearTrackingHistory();
        }

        if (searchTermView != null) {
            searchTermView.setEtText(searchResultsContext.getSearchQuery());
        }

        if (searchSkuSuggestionsView != null) {
            searchSkuSuggestionsView.clearLastSkuSearchSuggestions();
        }

        searchResultsContext.setActivityListener(new SearchResultsV2Context.ActivityListener() {
            @Override
            public void onSearchExtras(JSONObject extras) {
                if (!isContextAvailable()) return;
                onSearchResultsExtras(searchResultsContext, extras);
            }

            @Override
            public void onSearchSuccess(SearchEntry currentSearch, SearchBean bean) {
                if (!isContextAvailable()) return;

                if (currentSearch != null && currentSearch.searchParams != null) {
                    final String catalogueNum = (String) currentSearch.searchParams.get(SearchResultsFragmentV2.SEARCH_PARAM_CATALOGUE_NUM);
                    final String sorts = (String) currentSearch.searchParams.get(SearchResultsFragmentV2.SEARCH_FILTER_SORT);
                    final Map<String, String> filtersMap = (Map<String, String>) currentSearch.searchParams.get(SearchResultsFragmentV2.SEARCH_FILTER_MAP);
                    final String filters = JSON.toJSONString(filtersMap);
                    SearchPanelActivity.this.catalogueNum = catalogueNum;
                    viewModel.searchManager.setFilterAndSorts(filters, sorts);
                }

                if (isSearchV2SectionTabsEnabled() && Constants.SearchV2.ENABLE_SECTION_TABS_FILTERS_SCROLLABLE && layoutSectionTabsFilter == null) {
                    ivFilterTag.setVisibility(View.GONE);
                    tvFilterNum.setVisibility(View.GONE);
                    layoutSectionTabsFilter = View.inflate(SearchPanelActivity.this, R.layout.search_v2_section_tabs_filters_view, null);
                    if (layoutSectionTabsFilter != null) {
                        sectionTabsFilter = layoutSectionTabsFilter.findViewById(R.id.search_v2_section_tabs_filters_button);
                        tvFilterNum = layoutSectionTabsFilter.findViewById(R.id.search_v2_section_tabs_filters_num);
                    }
                }
            }

            @Override
            public boolean onSearchRequestRedirectPageUrl(String customUrl) {
                if (!EmptyUtils.isEmpty(customUrl)) {
                    boolean isSearchUrl = customUrl.matches(Constants.UrlPattern.SEARCH);
                    if (isSearchUrl) {
                        Map<String, String> params = CommonTools.parseQueryParams(customUrl);
                        if (!params.containsKey("search_ui_type")) {
                            String keyword = params.get("keyword");
                            if (!EmptyUtils.isEmpty(keyword) && keyword.equalsIgnoreCase(keyWord)) {
                                // prevent search loop (stack overflow)
                                return false;
                            }
                        }
                    }
                    if (viewModel != null) {
                        try {
                            viewModel.endMonitorAction();
                        } catch (Exception e) {
                            if (DevConfig.isDebug()) {
                                Logger.e("SearchPanelActivity", e);
                            }
                        }
                        if (isContextAvailable()) {
                            viewModel.directUrlData.postValue(customUrl);
                        }
                    }
                    return true;
                }

                return false;
            }

            @Override
            public void onSearchFailure(SearchBean searchBean, boolean emptyResult, String message) {
                try {
                    if (!isContextAvailable()) return;

                    if (Constants.SearchV2.USE_SEARCH_V1_AS_FAILURE_FALLBACK && !emptyResult) {
                        searchUsingFallbackSearchResultsType(keyWord);
                    } else {
                        failureStatus(null);
                        if (!EmptyUtils.isEmpty(message)) {
                            tvKeySearchNull.setText(message);
                        }
                        hideAllVeil();

                        if (isSearchV2SectionTabsEnabled() && searchBean != null && searchBean.t != null && !EmptyUtils.isEmpty(searchBean.t.filters)) {
                            searchBean.isShowEmpty = false;
                            showResults(searchBean);
                        }
                    }
                } finally {
                    if (viewModel != null) viewModel.endMonitorAction();
                }
            }

            @Override
            public void onSearchResultsReadyToPresent(SearchEntry currentSearch, SearchBean bean) {
                try {
                    if (!isContextAvailable()) return;

                    if (bean != null) {
                        showResults(bean);
                    }
                } finally {
                    if (viewModel != null) viewModel.endMonitorAction();
                }
            }

            private boolean isContextAvailable() {
                return searchResultsContext != null && !searchResultsContext.isReleased() && searchResultsContext.equals(viewModel.getSearchResultContext());
            }
        });
    }

    private void onSearchResultsExtras(SearchResultsV2Context searchResultsContext, JSONObject extras) {
        if (extras == null) return;

        if (viewModel != null && viewModel.getSearchResultContext() != null &&  viewModel.getSearchResultContext() != searchResultsContext) return;

        SearchEntry searchEntry = searchResultsContext.getSearchEntry();

        final String newTraceId = extras.optString(SearchJsonField.TRACE_ID, null);
        if (!EmptyUtils.isEmpty(newTraceId)) {
            this.traceId = newTraceId;
        }

        if (extras.has(SearchJsonField.PRODUCT_AREA_LIST)) {
            JSONArray productAreaList = extras.optJSONArray(SearchJsonField.PRODUCT_AREA_LIST);
            if (productAreaList != null) {
                if (updateFilterBuilderParams(searchEntry, productAreaList, SearchJsonField.PRODUCT_AREA_LIST)) {
                    if (DevConfig.isDebug()) {
                        Logger.d("Search filter builder params updated: PRODUCT_AREA_LIST");
                    }
                }
            }
        }

        if (extras.has(SearchJsonField.PRODUCT_VENDOR_LIST)) {
            JSONArray productVendorList = extras.optJSONArray(SearchJsonField.PRODUCT_VENDOR_LIST);
            if (productVendorList != null) {
                if (updateFilterBuilderParams(searchEntry, productVendorList, SearchJsonField.PRODUCT_VENDOR_LIST)) {
                    if (DevConfig.isDebug()) {
                        Logger.d("Search filter builder params updated: PRODUCT_VENDOR_LIST");
                    }
                }
            }
        }
    }

    private boolean updateFilterBuilderParams(SearchEntry currentSearch, JSONArray list, String parentKey) {
        boolean updated = false;

        if (currentSearch == null || currentSearch.searchParams == null || list == null) {
            return updated;
        }

        int listLength = list.length();
        for (int i = 0; i < listLength; i++) {
            JSONObject item = list.optJSONObject(i);
            if (updateFilterBuilderParams(currentSearch, item, parentKey)) {
                updated = true;
            }
        }

        return updated;
    }

    private boolean updateFilterBuilderParams(SearchEntry currentSearch, JSONObject item, String parentKey) {
        boolean updated = false;

        if (currentSearch == null || currentSearch.searchParams == null || item == null) {
            return updated;
        }

        Double filterBuilderMinPrice = (Double) currentSearch.searchParams.get(SearchResultsFragmentV2.FILTER_BUILDER_MIN_PRICE);
        if (filterBuilderMinPrice == null) {
            filterBuilderMinPrice = 0.0;
            currentSearch.searchParams.put(SearchResultsFragmentV2.FILTER_BUILDER_MIN_PRICE, filterBuilderMinPrice);
            updated = true;
        }

        Double filterBuilderMaxPrice = (Double) currentSearch.searchParams.get(SearchResultsFragmentV2.FILTER_BUILDER_MAX_PRICE);
        if (filterBuilderMaxPrice == null) {
            filterBuilderMaxPrice = 0.0;
            currentSearch.searchParams.put(SearchResultsFragmentV2.FILTER_BUILDER_MAX_PRICE, filterBuilderMaxPrice);
            updated = true;
        }

        Map<String, String> filterBuilderAreaCodes = (Map<String, String>) currentSearch.searchParams.get(SearchResultsFragmentV2.FILTER_BUILDER_AREA_CODES);
        if (filterBuilderAreaCodes == null) {
            filterBuilderAreaCodes = new HashMap<>();
            currentSearch.searchParams.put(SearchResultsFragmentV2.FILTER_BUILDER_AREA_CODES, filterBuilderAreaCodes);
            updated = true;
        }

        Map<String, String> filterBuilderVendors = (Map<String, String>) currentSearch.searchParams.get(SearchResultsFragmentV2.FILTER_BUILDER_VENDORS);
        if (filterBuilderVendors == null) {
            filterBuilderVendors = new HashMap<>();
            currentSearch.searchParams.put(SearchResultsFragmentV2.FILTER_BUILDER_VENDORS, filterBuilderVendors);
            updated = true;
        }

        // ProductVendorList
        if (SearchJsonField.PRODUCT_VENDOR_LIST.equals(parentKey) && item.has(SearchJsonField.ID) && item.has(SearchJsonField.NAME)) {
            String vendorId = item.optString(SearchJsonField.ID);
            String vendorName = item.optString(SearchJsonField.NAME);
            if (!EmptyUtils.isEmpty(vendorId) && !EmptyUtils.isEmpty(vendorName)) {
                Object oldVendorName = filterBuilderVendors.get(vendorId);
                if (oldVendorName == null || !Objects.equals(vendorName, oldVendorName)) {
                    updated = true;
                }
                filterBuilderVendors.put(vendorId, vendorName);
            }
        }
        // productAreaList
        else if (SearchJsonField.PRODUCT_AREA_LIST.equals(parentKey) && item.has(SearchJsonField.CODE) && item.has(SearchJsonField.DESCRIPTION)) {
            String productAreaCode = item.optString(SearchJsonField.CODE);
            String productAreaDescription = item.optString(SearchJsonField.DESCRIPTION);
            if (!EmptyUtils.isEmpty(productAreaCode)) {
                if (EmptyUtils.isEmpty(productAreaDescription)) {
                    final String language = LanguageManager.get().getLanguage();
                    productAreaDescription = CountryUtils.getCountryName(language, productAreaCode);
                }
                if (!EmptyUtils.isEmpty(productAreaDescription)) {
                    String value = StringUtils.firstUpperForAllWords(productAreaDescription);
                    Object oldValue = filterBuilderAreaCodes.get(productAreaCode);
                    if (oldValue == null || !Objects.equals(value, oldValue)) {
                        updated = true;
                    }
                    filterBuilderAreaCodes.put(productAreaCode, value);
                }
            }
        }
        // product
        else if (SearchJsonField.CONVERTED_PRICED_PRODUCTS.equals(parentKey)) {
            double value = item.optDouble(SearchJsonField.PRICE);
            if (filterBuilderMinPrice == 0 || value < filterBuilderMinPrice)
                filterBuilderMinPrice = value;
            if (value > filterBuilderMaxPrice) filterBuilderMaxPrice = value;

            String vendorId = item.optString(SearchJsonField._VENDOR_ID);
            String vendorName = item.optString(SearchJsonField._VENDOR_NAME);
            if (!EmptyUtils.isEmpty(vendorId) && !EmptyUtils.isEmpty(vendorName)) {
                if (filterBuilderVendors == null) filterBuilderVendors = new HashMap<>();
                Object oldVendorName = filterBuilderVendors.get(vendorId);
                if (oldVendorName == null || !Objects.equals(vendorName, oldVendorName)) {
                    updated = true;
                }
                filterBuilderVendors.put(vendorId, vendorName);
            }
        }

        return updated;
    }

    public void setTopSearchBarFilterButtonCountValue(int qty) {
        if (topSearchBarFilterBtnCountBadge != null) {
            topSearchBarFilterBtnCountBadge.setText(String.valueOf(qty));
            topSearchBarFilterBtnCountBadge.setVisibility(qty > 0 ? View.VISIBLE : View.INVISIBLE);
        }
    }

    public void setTopSearchBarFilterButtonVisibility(boolean visible) {
        if (this.topSearchBarFilterBtn != null) {
            boolean isNotSellerSearch = EmptyUtils.isEmpty(sellerId);
            boolean shouldShowFilter = visible && (!isSearchV2SectionTabsEnabled() || (!getSearchResultsV2Context().isResultsNotFound() && isSearchV2SUIModeSingleList())) && isNotSellerSearch;
            this.topSearchBarFilterBtn.setVisibility(shouldShowFilter ? View.VISIBLE : View.GONE);
        }
    }

    public void setTopSearchBarFilterButtonCountBadgeBackground() {
        if (topSearchBarFilterBtnCountBadge != null)
            topSearchBarFilterBtnCountBadge.setBackground(ShapeHelper.buildSolidDrawable(getResources().getColor(R.color.color_btn_secondary_bg), CommonTools.dp2px(12)));
    }

    private void resetSearchFilter() {
        SearchBean searchBean = getSearchBean();
        if (searchBean == null) return;

        for (ProductSortBean sort : searchBean.t.sorts) {
            sort.selected = sort.sort_def;
        }
        for (ProductFilterBean filter : searchBean.t.filters) {
            for (ProductPropertyValueBean property_value : filter.property_values) {
                property_value.selected = property_value.value_def;
            }
        }
        groceryFilter(searchBean);
    }

    private String getMonitorPageKey() {
        switch (pageType) {
            case TYPE_MAIN_SEARCH:
                if (isFromSeller()) {
                    return WeeeEvent.PageView.SELLER_SEARCH_RESULT;
                } else if (global) {
                    return WeeeEvent.PageView.GLOBAL_SEARCH_RESULT;
                } else {
                    return WeeeEvent.PageView.SEARCH_RESULT;
                }
            case TYPE_POST_EDIT_SEARCH:
                return WeeeEvent.PageView.COMM_SEARCH_RESULT;
            default:
                return WeeeEvent.PageView.SEARCH_RESULT;
        }
    }

}
