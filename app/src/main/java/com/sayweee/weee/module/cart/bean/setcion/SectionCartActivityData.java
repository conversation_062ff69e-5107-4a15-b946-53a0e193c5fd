package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.adapter.SectionCartAdapter;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SectionCartActivityData extends SimpleSectionCartData implements Serializable {

    public SectionCartTipsData tipsData;
    private List<SectionCartProductData> productData;
    private SectionCartActivityFooterData footerData;

    public NewSectionBean.ActivityInfo targetData;
    private final NewSectionBean.VendorInfo vendorInfo;
    private final NewSectionBean.ShippingInfo shippingInfo;
    boolean displaySave4Later;
    int sectionIndex;
    int activityIndex;
    public Map<String, Object> element;//tracking element
    public Map<String, Object> ctx;
    private String cartId;
    public String sectionType;
    protected boolean hasServiceFee;

    public SectionCartActivityData(@CartType int cartType, NewSectionBean.ActivityInfo targetData, NewSectionBean.VendorInfo vendorInfo, NewSectionBean.ShippingInfo shippingInfo) {
        this.targetData = targetData;
        this.vendorInfo = vendorInfo;
        this.shippingInfo = shippingInfo;
        setCartType(cartType);
    }

    @Override
    public List<AdapterDataType> toAdapterData() {
        List<AdapterDataType> list = new ArrayList<>();
        if (isValid()) { // 检查有没有商品或者是RTG
            if (isGroceryCart()) {
                if (isActivityTradeIn()) {
                    //换购仅展示商品
                    addData(list, productData);
                } else {
                    addData(list, tipsData);
                    addData(list, productData);
                    addData(list, footerData);
                }
            }
        } else {
            //新版cart deal活動模块不需要校验是否有items
            if (isGroceryCart()) {
                if (isActivityCartDeal()) {
                    addData(list, tipsData);
                    addData(list, productData);
                }
                if (isActivityFreeShipping()) {
                    addData(list, tipsData);
                }
                if (isActivityTradeInNew()) {
                    addData(list, tipsData);
                    addData(list, productData);
                }
                if (isActivityBuy2get1()) {
                    tipsData.setFrameUiData(new CartFrameUiData(SectionCartAdapter.TOP_FRAME, SectionCartAdapter.BOTTOM_FRAME, false));
                    addData(list, tipsData);
                }
            }
        }
        return list;
    }

    @Override
    public void calc() {
        if (targetData != null) {
            boolean isCartDeal = targetData.isCartDealType();
            boolean isTradeIn = targetData.isTradeInType();
            boolean isTradeInNew = targetData.isTradeInNewType();
            if (isCartDeal) {
                if ("trade_in".equalsIgnoreCase(targetData.original_type)) {
                    ctx = new EagleContext().setDiffPriceUpsell(OrderHelper.formatMoney(targetData.diff_amount)).asMap();
                } else {
                    ctx = new EagleContext().setDiffPrice(OrderHelper.formatMoney(targetData.diff_amount)).asMap();
                }
            } else if (targetData.isFreeShippingType()) {
                ctx = new EagleContext().setDiffPrice(OrderHelper.formatMoney(targetData.diff_amount)).asMap();
            }

            if (isCartDeal) {
                tipsData = new SectionCartDealData().setCartId(cartId); // 新版活动模块
            } else if (targetData.isFreeShippingType()) {
                SectionCartFreeShippingData tip = new SectionCartFreeShippingData();
                tip.setHasServiceFee(hasServiceFee);
                tipsData = tip;
            } else {
                tipsData = new SectionCartTipsData(); // 老版活动模块
            }
            tipsData.setActivitySectionIndex(sectionIndex);
            tipsData.setActivityIndex(activityIndex);
            tipsData.setTrackingInfo(element, ctx);
            tipsData.setActivityInfo(targetData);
            tipsData.setCartType(targetType);
            tipsData.setFrameUiData(new CartFrameUiData(SectionCartAdapter.INNER_TOP_FRAME, SectionCartAdapter.MIDDLE_FRAME, false));
            if (isTradeInNew) {
                tipsData.setFrameUiData(new CartFrameUiData(-1, SectionCartAdapter.MIDDLE_FRAME, false));
            }
            tipsData.setSectionType(sectionType);

            productData = new ArrayList<>();
            List<NewItemBean> items = targetData.items;
            int itemCount = CollectionUtils.size(items);
            if (items != null && itemCount > 0) {
                int vendorId = vendorInfo != null ? vendorInfo.vendor_id : 0;
                for (int i = 0; i < itemCount; i++) {
                    NewItemBean item = items.get(i);
                    SectionCartProductData it;
                    CartFrameUiData uiData;
                    if (item.is_gift) {
                        uiData = !isActivityTradeIn()
                                ? new CartFrameUiData(
                                i == itemCount - 1 && (!targetData.is_offer || targetData.save_amount <= 0) ? SectionCartAdapter.INNER_BOTTOM_FRAME : SectionCartAdapter.INNER_MIDDLE_FRAME
                                , SectionCartAdapter.MIDDLE_FRAME
                                , i == 0)
                                : new CartFrameUiData();
                        it = new SectionCartProductData(CartSectionType.TYPE_PRODUCT_GIFT, item)
                                .setDisplaySave4Later(false)
                                .setGiftItems(targetData.gift_items);
                    } else {
                        uiData = new CartFrameUiData(
                                (isCartDeal || isTradeIn || isTradeInNew) ? -1 : (i == itemCount - 1 && (!targetData.is_offer || targetData.save_amount <= 0) ? SectionCartAdapter.INNER_BOTTOM_FRAME : SectionCartAdapter.INNER_MIDDLE_FRAME)
                                , SectionCartAdapter.MIDDLE_FRAME
                                , i == 0 && !isCartDeal);
                        it = new SectionCartProductData(CartSectionType.TYPE_PRODUCT_ACTIVITY, item)
                                .setDisplaySave4Later(displaySave4Later && !isActivityTradeIn()); //非换购和gift活动商品展示Save4Later
                    }
                    it.setActivityType(targetData.type);
                    it.setHotdishInfo(vendorId, shippingInfo.hotdish_wave != null ? shippingInfo.hotdish_wave.wave_seq : null);
                    it.setTag(targetData.tag);
                    it.setTrackElement(EagleTrackManger.get().getElement("cart", 0, sectionType, sectionIndex));
                    it.setFrameUiData(uiData);
                    productData.add(it);
                }
            }
            if (targetData.save_amount > 0) {
                footerData = new SectionCartActivityFooterData();
                footerData.saveAmount = targetData.save_amount;
            }
        }
    }

    @Override
    public boolean isValid() {
        return productData != null && !productData.isEmpty();
    }

    public List<SectionCartProductData> getProductData() {
        return productData;
    }

    private boolean isActivityTradeIn() {
        return targetData != null && Constants.PriceType.TRADE_IN.equalsIgnoreCase(targetData.type);
    }

    public boolean isActivityCartDeal() {
        return targetData != null && targetData.isCartDealType();
    }

    public boolean isActivityFreeShipping() {
        return targetData != null && targetData.isFreeShippingType();
    }

    public boolean isActivityTradeInNew() {
        return targetData != null && targetData.isTradeInNewType();
    }

    public boolean isActivityBuy2get1() {
        return targetData != null && targetData.type != null && targetData.type.startsWith("buy_2_get_1");
    }

    public SectionCartActivityData setDisplaySave4Later(boolean displaySave4Later) {
        this.displaySave4Later = displaySave4Later;
        return this;
    }

    public SectionCartActivityData setSectionIndex(int index) {
        this.sectionIndex = index;
        return this;
    }

    public SectionCartActivityData setActivityIndex(int index) {
        this.activityIndex = index;
        return this;
    }

    public SectionCartActivityData setElement(String secNm) {
        element = EagleTrackManger.get().getElement(
                /* modNm= */"cart",
                /* modPos= */0,
                /* secNm= */secNm,
                /* secPos= */sectionIndex
        );
        return this;
    }

    public SectionCartActivityData setCartId(String cartId) {
        this.cartId = cartId;
        return this;
    }

    public SectionCartActivityData setSectionType(String sectionType) {
        this.sectionType = sectionType;
        return this;
    }

    public String nextUrl() {
        return (targetData != null && targetData.hasNextUrl()) ? targetData.recommend.next_url : null;
    }

    public SectionCartActivityData setHasServiceFee(boolean hasServiceFee) {
        this.hasServiceFee = hasServiceFee;
        return this;
    }
}
