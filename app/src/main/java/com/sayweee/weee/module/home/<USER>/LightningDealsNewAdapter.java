package com.sayweee.weee.module.home.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.adapter.OnCartEditListener;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cart.service.ProductHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.widget.timer.WrapperOnCmsComponentTimerListener;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.home.bean.AdapterMoreData;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.spanner.DefaultURLClickListenerImpl;
import com.sayweee.weee.widget.HorizontalProgressBar;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.CartStatusLayout;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.product.ProductViewDisplayStrategy;
import com.sayweee.weee.widget.product.ProductViewElements;
import com.sayweee.weee.widget.product.ProductViewHelper;
import com.sayweee.weee.widget.timer.OnTimerListener;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  wld
 * Email:
 * Date:    2022/5/26.
 * Desc:
 */
public class LightningDealsNewAdapter
        extends BaseQuickAdapter<Object, AdapterViewHolder>
        implements EagleImpressionAdapter, ImpressionChild {

    public static final int TYPE_MORE = 100;
    private static final long DEFAULT_REMIND_THRESHOLD = AppConfig.REMIND_LIMIT - 1000L; // 5min - 1s

    private WrapperOnCmsComponentTimerListener timerListener;
    private RecyclerView attachView;
    protected String module;    //埋点所用
    private long currentTimestamp;
    private long systemTimestamp;
    private OnRemindListener listener;
    protected String mod_nm, sec_nm;//EagleTrack:埋点所用
    protected int mod_pos = -1, sec_pos = -1;//EagleTrack:埋点所用
    protected String componentId;

    protected OnCartEditListener onCartEditListener = null;
    boolean showProgress = true;//默认显示progress
    protected int productDisplayStyle = ProductView.STYLE_ITEM_SMALL;
    protected int cachedMinimumHeight = 0;

    private final RemindPayload REMIND_PAYLOAD = new RemindPayload();

    public LightningDealsNewAdapter() {
        super(R.layout.item_lightning_deals_product);
    }

    public void setData(@Nullable List<LightningDealsProductBean> data, long currentTimestamp, long systemTimestamp, boolean showMore, String moreLink) {
        this.currentTimestamp = currentTimestamp;
        this.systemTimestamp = systemTimestamp;
        List<Object> newData = CollectionUtils.newListOf(data);
        if (showMore) {
            newData.add(new AdapterMoreData(TYPE_MORE, moreLink));
        }
        setNewData(newData);
    }

    @Override
    public void setNewData(@Nullable List<Object> data) {
        cachedMinimumHeight = 0;
        super.setNewData(data);
    }

    public void setProductDisplayStyle(@ProductView.DisplayStyle int productDisplayStyle) {
        this.productDisplayStyle = productDisplayStyle;
    }

    protected int getDisplayStyle() {
        return productDisplayStyle;
    }

    public LightningDealsNewAdapter setOnCartEditListener(OnCartEditListener onCartEditListener) {
        this.onCartEditListener = onCartEditListener;
        return this;
    }

    @Override
    public void setAttachView(RecyclerView attachView) {
        this.attachView = attachView;
    }

    @Override
    public RecyclerView getAttachView() {
        return attachView;
    }

    /**
     * 用于埋点标示当前所属module
     *
     * @param module module
     */
    public LightningDealsNewAdapter setAdapterModule(String module) {
        this.module = module;
        return this;
    }

    /**
     * EagleTrack:用于埋点标示当前所属module 信息
     */
    public void setModInfo(String mod_nm, int mod_pos) {
        this.mod_nm = mod_nm;
        this.mod_pos = mod_pos;
    }


    /**
     * EagleTrack:用于埋点标示当前所属section 信息
     */
    public void setSecInfo(String sec_nm, int sec_pos) {
        this.sec_nm = sec_nm;
        this.sec_pos = sec_pos;
    }

    @Override
    protected int getDefItemViewType(int position) {
        return getItem(position) instanceof AdapterMoreData ? TYPE_MORE : super.getDefItemViewType(position);
    }

    @Override
    protected AdapterViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
        if (viewType == TYPE_MORE) {
            return createBaseViewHolder(parent, R.layout.item_list_more);
        }
        return super.onCreateDefViewHolder(parent, viewType);
    }

    @Override
    protected AdapterViewHolder createBaseViewHolder(ViewGroup parent, int layoutResId) {
        AdapterViewHolder helper = super.createBaseViewHolder(parent, layoutResId);
        if (layoutResId == R.layout.item_lightning_deals_product) {
            LifecycleOwner lifecycleOwner = ViewTreeLifecycleOwner.get(parent);
            if (lifecycleOwner != null) {
                TimerTextView tvTimer = helper.getView(R.id.tv_timer);
                TimerTextView tvBottomTimer = helper.getView(R.id.tv_bottom_timer);
                lifecycleOwner.getLifecycle().addObserver(tvTimer);
                lifecycleOwner.getLifecycle().addObserver(tvBottomTimer);
            }
        }
        return helper;
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        if (layoutParams instanceof RecyclerView.LayoutParams) {
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
            int position = params.getViewLayoutPosition();
            params.leftMargin = CommonTools.dp2px(position == 0 ? 20 : 8);
            params.rightMargin = CommonTools.dp2px(position == getItemCount() - 1 ? 20 : 0);
        }
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, Object item, @NonNull List<Object> payloads) {
        for (Object o : payloads) {
            if (o instanceof CmsLightingDealsData) {
                convert(helper, item);
            } else if (o instanceof RemindPayload && item instanceof LightningDealsProductBean) {
                hideRemindStatus(helper, (LightningDealsProductBean) item);
            } else if (ProductTraceViewHelper.shouldConvertPayload(o) && item instanceof ProductBean) {
                View productView = helper.getView(R.id.layout_product);
                ProductBean productBean = (ProductBean) item;
                if (productView != null) {
                    ProductTraceViewHelper.convert(
                            helper.itemView,
                            ProductTraceKey.of(productBean.id, productBean.recommendation_trace_id, ProductTraceKey.generateUniqueKey(mod_nm, sec_nm)));
                }
            }
        }
        ProductSyncHelper.convertPayloads(helper, item, payloads);
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, Object data) {
        if (data instanceof LightningDealsProductBean) {
            convertProduct(helper, (LightningDealsProductBean) data);
        } else if (data instanceof AdapterMoreData) {
            convertMore(helper, (AdapterMoreData) data);
        }
    }

    private void convertProduct(@NonNull AdapterViewHolder helper, LightningDealsProductBean item) {
        // R.layout.item_lightning_deals_product
        int viewWidth = getProductItemWidth(mContext, getDisplayStyle());
        ViewTools.updateViewSize(helper.itemView, viewWidth, ViewGroup.LayoutParams.WRAP_CONTENT);
        View view = helper.getView(R.id.layout_product);
        view.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                toProductDetail(item, helper.getLayoutPosition());
            }
        });
        String imgUrl = item.getHeadImageUrl();
        helper.loadImage(mContext, R.id.iv_icon, WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, imgUrl), R.mipmap.iv_product_placeholder);
        LabelHelper.setTitleLabel(helper.getView(R.id.tv_product_name), item, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                toProductDetail(item, helper.getLayoutPosition());
            }
        });

        // brand name
        TextView tvBrandName = helper.getView(R.id.tv_brand_name);
        boolean hasBrandName = !EmptyUtils.isEmpty(item.sponsored_text) || !EmptyUtils.isEmpty(item.brand_name);
        if (tvBrandName != null) {
            tvBrandName.setText(!EmptyUtils.isEmpty(item.sponsored_text) ? item.sponsored_text : item.brand_name);
            ViewTools.setViewVisibilityIfChanged(tvBrandName, hasBrandName);
        }

        //marker
        if (!EmptyUtils.isEmpty(item.label_list) && CmsLightingDealsData.STATUS_BE_GOING.equals(item.status)) {
            fillProductLabel(item.label_list, helper.getView(R.id.layout_marker));
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_marker), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_marker), false);
        }

        //价格模块
        boolean showBasePrice = item.base_price > 0;
        helper.setVisibleCompat(false, R.id.layout_volume, R.id.tv_price, R.id.tv_price_delete);
        if (item.showVolumePrice()) {//限时秒杀
            //Volume Pricing
            helper.setVisible(R.id.layout_volume, true);
            TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
            if (tvPriceVolume != null) {
                Spanny s = new Spanny()
                        .append(OrderHelper.formatUSMoney(item.price), new TextAppearanceSpan(mContext, R.style.style_fluid_root_numeral_base))
                        .append(mContext.getString(R.string.s_volume_threshold_simple, item.volume_threshold));
                helper.setText(R.id.tv_price_volume, s);
            }
            //Volume single price
            TextView tvPriceSingle = helper.getView(R.id.tv_price_single);
            if (tvPriceSingle != null) {
                helper.setText(R.id.tv_price_single
                        , (showBasePrice ? new Spanny(OrderHelper.formatUSMoney(item.base_price), new StrikethroughSpan()).append(" ") : new Spanny())
                                .append(mContext.getString(R.string.s_volume_threshold_one_qty, OrderHelper.formatUSMoney(item.volume_price))));
            }
        } else {
            helper.setVisible(R.id.tv_price, true);
            helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(item.special_price));
            if (showBasePrice) {
                helper.setVisibleText(R.id.tv_price_delete, new Spanny(OrderHelper.formatUSMoney(item.base_price), new StrikethroughSpan()));
            }
        }

        //倒计时
        TimerTextView tvTimer = helper.getView(R.id.tv_timer);
        TimerTextView tvBottomTimer = helper.getView(R.id.tv_bottom_timer);

        final boolean beGoing = CmsLightingDealsData.STATUS_BE_GOING.equals(item.status);
        final CartStatusLayout layoutStatus = helper.getView(R.id.layout_status);
        final View viewSoldOut = helper.getView(R.id.tv_sold_out);
        helper.getView(R.id.pb_indicator).setVisibility(beGoing || !showProgress ? View.GONE : View.VISIBLE);
        helper.getView(R.id.tv_indicator).setVisibility(beGoing || !showProgress ? View.GONE : View.VISIBLE);
        ViewTools.setViewVisible(viewSoldOut, false);
        final long current = (int) (System.currentTimeMillis() / 1000.0); // seconds
        final long endTime; // seconds
        if (beGoing) {
            endTime = item.start_time - currentTimestamp + systemTimestamp;
        } else {
            endTime = item.end_time - currentTimestamp + systemTimestamp;
        }
        if (beGoing) {
            tvTimer.setVisibility(View.GONE);
            tvTimer.setRestartEnable(false);
            tvTimer.stop();
            tvTimer.setOnTimerListener(null);

            tvBottomTimer.setVisibility(View.VISIBLE);
            tvBottomTimer.setRestartEnable(true);
            tvBottomTimer.setPrefixText(mContext.getString(R.string.s_start_in)).setSuffixText("");

            final long remindThreshold = getRemindThreshold();
            ViewTools.setViewVisibilityIfChanged(layoutStatus, (endTime - current >= (remindThreshold / 1000L)) ? View.VISIBLE : View.INVISIBLE);

            tvBottomTimer.start(endTime);
            tvBottomTimer.setOnTimerListener(timerListener);
        } else {
            tvBottomTimer.setVisibility(View.GONE);
            tvBottomTimer.setRestartEnable(false);
            tvBottomTimer.stop();
            tvBottomTimer.setOnTimerListener(null);

            ViewTools.setViewVisibilityIfChanged(layoutStatus, View.INVISIBLE);
            tvTimer.setVisibility(View.VISIBLE);
            tvTimer.setRestartEnable(true);
            tvTimer.setTextColor(Color.WHITE);
            tvTimer.setPrefixText("");

            tvTimer.start(endTime);
            tvTimer.setOnTimerListener(timerListener);
        }

        boolean isCollect = CollectManager.get().isProductCollect(item.id);
        CartOpLayout layoutOp = helper.getView(R.id.layout_op);
        layoutOp.setTipsMaxWidth(viewWidth - CommonTools.dp2px(20));
        if (beGoing) {
            setRemindStatusBeGoing(layoutStatus, isCollect, item.remind_set, item.id);
            helper.setVisibleCompat(R.id.layout_op, false);
            // layoutStatus handle by timer
            ViewTools.setViewOnSafeClickListener(layoutStatus, v -> toggleCollectBeGoing(v, item));
        } else {
            HorizontalProgressBar indicator = helper.getView(R.id.pb_indicator);
            indicator.setProgress(item.progress, HorizontalProgressBar.LEFT_TO_RIGHT);
            helper.setText(R.id.tv_indicator, String.format(mContext.getString(R.string.s_claimed_value), item.progress, "%"));
            helper.setVisibleCompat(R.id.layout_op, false);

            if (OrderManager.get().isReachLimit(item)) { // 已购买
                ProductViewHelper.setProductStatusPurchased(layoutStatus);
                ViewTools.setViewVisibilityIfChanged(layoutStatus, View.VISIBLE);
                ViewTools.setViewOnSafeClickListener(layoutStatus, v -> {
                });
            } else if (ProductHelper.isChangeOtherDay(item.sold_status)) { // 修改日期
                ProductViewHelper.setProductStatusChangeDate(layoutStatus);
                ViewTools.setViewVisibilityIfChanged(layoutStatus, View.VISIBLE);
                ViewTools.setViewOnSafeClickListener(layoutStatus, v -> {
                    mContext.startActivity(DateActivity.getIntent(mContext, String.valueOf(item.id), "product modify me"));
                });
            } else if (ProductHelper.isSoldOut(item.sold_status)) {
                setRemindStatusOnGoing(layoutStatus, isCollect);
                ViewTools.setViewVisibilityIfChanged(layoutStatus, View.VISIBLE);
                ViewTools.setViewOnSafeClickListener(layoutStatus, v -> toggleCollectOnGoing(v, item));
            } else {
                ViewTools.setViewVisibilityIfChanged(layoutStatus, View.INVISIBLE);
                layoutOp.setVisibility(View.VISIBLE);
                Map<String, Object> element = new EagleTrackModel.Builder()
                        .setMod_nm(mod_nm)
                        .setMod_pos(mod_pos)
                        .setSec_nm(sec_nm)
                        .setSec_pos(sec_pos).build().getElement();
                item.prod_pos = helper.getLayoutPosition();
                String source = ProductHelper.getAddCartSource(mod_nm);
                if (onCartEditListener != null) {
                    OpHelper.helperOpByDelegate(layoutOp, item, null, source, onCartEditListener, element, null);
                } else {
                    OpHelper.helperOp(layoutOp, item, item, source, element);
                }
            }
        }
        // mkpl ships from
        showVendorShips(helper, item);

        setDisplayMinimumHeight(helper.itemView);

        ProductTraceViewHelper.convert(
                helper.itemView,
                ProductTraceKey.of(item.id, item.recommendation_trace_id, ProductTraceKey.generateUniqueKey(mod_nm, sec_nm))
        );
    }

    private void hideRemindStatus(@NonNull AdapterViewHolder helper, LightningDealsProductBean item) {
        final boolean beGoing = CmsLightingDealsData.STATUS_BE_GOING.equals(item.status);
        if (beGoing) {
            final View layoutStatus = helper.getView(R.id.layout_status);
            ViewTools.setViewVisibilityIfChanged(layoutStatus, View.INVISIBLE);
        }
    }

    private void convertMore(@NonNull AdapterViewHolder helper, AdapterMoreData data) {
        int itemWidth = getProductItemWidth(mContext, getDisplayStyle());
        ViewTools.updateViewSize(helper.itemView, itemWidth, null);

        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(mod_nm)
                        .setMod_pos(mod_pos)
                        .setSec_nm(null)
                        .setSec_pos(-1)
                        .setTargetNm("explore_more")
                        .setTargetPos(-1)
                        .setTargetType(null)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .build().getParams());
                Context context = v != null ? v.getContext() : null;
                if (context != null) {
                    context.startActivity(WebViewActivity.getIntent(context, data.t));
                }
            }
        }, R.id.iv_more);
    }

    private boolean hasVendorShips() {
        if (mData != null) {
            for (Object item : mData) {
                if (item instanceof LightningDealsProductBean
                        && ((LightningDealsProductBean) item).isSeller()
                        && ((LightningDealsProductBean) item).vender_info_view != null
                        && !EmptyUtils.isEmpty(((LightningDealsProductBean) item).getVendorDeliveryNewDesc(((LightningDealsProductBean) item).freeShippingDescShow((LightningDealsProductBean) item, false, false)))) {
                    return true;
                }
            }
        }
        return false;
    }

    private void fillProductLabel(List<ProductBean.LabelListBean> labels, LinearLayout flowLayout) {
        List<ProductBean.LabelListBean> labelBeans = CollectionUtils.arrayListOf(labels.get(0));
        flowLayout.removeAllViews();
        int i = -1;
        for (ProductBean.LabelListBean labelBean : labelBeans) {
            i++;
            int finalI = i;
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, CommonTools.dp2px(21));
            params.leftMargin = CommonTools.dp2px(2);
            flowLayout.addView(ViewTools.getHelperView(flowLayout, R.layout.layout_product_label, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    ShapeTextView tvMarker = helper.getView(R.id.tv_marker);
                    tvMarker.setPadding(CommonTools.dp2px(10), 0, CommonTools.dp2px(10), 0);
                    if (finalI != 0 && labelBean.label_name.contains("off")) {
                        tvMarker.setText(new Spanny(labelBean.label_name, new StrikethroughSpan()));
                    } else {
                        tvMarker.setText(labelBean.label_name);
                    }
                    int badgeRadius = CommonTools.dp2px(tvMarker.getContext(), R.dimen.prop_badge_corner_size, 4f);
                    tvMarker.setTextColor(ViewTools.parseColor(labelBean.label_font_color, finalI != 0 ? Color.parseColor("#333333") : Color.WHITE));
                    tvMarker.setBackgroundDrawable(ViewTools.parseColor(labelBean.label_color, Color.WHITE), badgeRadius, Color.WHITE, CommonTools.dp2px(1.5f));
                    tvMarker.setVisibility(View.VISIBLE);
                }
            }), params);
        }
    }

    private boolean hasVendorShipsOngoing() {
        if (mData != null) {
            for (Object item : mData) {
                if (item instanceof LightningDealsProductBean
                        && ((LightningDealsProductBean) item).isSeller()
                        && ((LightningDealsProductBean) item).vender_info_view != null
                        && !EmptyUtils.isEmpty(((LightningDealsProductBean) item)
                        .getVendorDeliveryNewDesc(((LightningDealsProductBean) item)
                                .freeShippingDescShow((LightningDealsProductBean) item, false, false)))
                        && CmsLightingDealsData.STATUS_ON_GOING.equals(((LightningDealsProductBean) item).status)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void showVendorShips(@NonNull AdapterViewHolder helper, LightningDealsProductBean item) {
        TextView tvVendor = helper.getView(R.id.tv_vender);
        ViewTools.setViewVisible(hasVendorShips(), tvVendor);
        String vendorDeliveryDesc = item.getVendorDeliveryNewDesc(item.freeShippingDescShow(item, false, false));
        if (!EmptyUtils.isEmpty(vendorDeliveryDesc)) {
            ViewTools.setViewHtml(tvVendor, vendorDeliveryDesc, new DefaultURLClickListenerImpl<Void>());
            ViewGroup.LayoutParams params = tvVendor.getLayoutParams();
            if (params instanceof ConstraintLayout.LayoutParams) {
                if (CmsLightingDealsData.STATUS_BE_GOING.equals(item.status) && hasVendorShipsOngoing()) {
                    ((ConstraintLayout.LayoutParams) params).topMargin = CommonTools.dp2px(18);
                } else {
                    ((ConstraintLayout.LayoutParams) params).topMargin = CommonTools.dp2px(2);
                }
                tvVendor.setLayoutParams(params);
            }
        } else {
            tvVendor.setText(null);
        }
    }

    protected void toProductDetail(LightningDealsProductBean product, int position) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(mod_nm)
                .setMod_pos(mod_pos)
                .setSec_nm(sec_nm)
                .setSec_pos(sec_pos)
                .setTargetNm(String.valueOf(product.id))
                .setTargetPos(position)
                .setTargetType("product")
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .setIsMkpl(product.isSeller())
                .addCtx(new TrackParams().put("volume_price_support", product.volume_price_support).get())
                .build().getParams());

        mContext.startActivity(ProductIntentCreator.getIntent(mContext, product.id));
    }

    private void setRemindStatusBeGoing(CartStatusLayout layoutStatus, boolean isCollect, boolean remindSet, int id) {
        if (remindSet) {
            if (!isCollect) {
                CollectManager.get().setProductCollect(id);
            }
            ProductViewHelper.setProductStatusReminded(layoutStatus);
        } else {
            CollectManager.get().removeProductCollect(id);
            ProductViewHelper.setProductStatusRemindMe(layoutStatus);
        }
    }

    private void toggleCollectBeGoing(CartStatusLayout layoutStatus, LightningDealsProductBean item) {
        if (!AccountManager.get().isLogin()) {
            mContext.startActivity(AccountIntentCreator.getIntent(mContext));
        } else {
            boolean collect = CollectManager.get().toggleProductCollect(item.id);
            if (collect) {
                ProductViewHelper.setProductStatusReminded(layoutStatus);
            } else {
                ProductViewHelper.setProductStatusRemindMe(layoutStatus);
            }
            if (listener != null) {
                listener.onRemind(item, collect, mData.indexOf(item));
            }
            layoutStatus.setTitle(mContext.getString(CollectManager.get().isProductCollect(item.id) ? R.string.s_product_reminded : R.string.s_product_remind_me));
        }
    }

    private void setRemindStatusOnGoing(CartStatusLayout layoutStatus, boolean isCollect) {
        if (isCollect) {
            ProductViewHelper.setProductStatusNotificationSet(layoutStatus);
        } else {
            ProductViewHelper.setProductStatusNotifyMe(layoutStatus);
        }
    }

    private void toggleCollectOnGoing(CartStatusLayout view, LightningDealsProductBean item) {
        if (!AccountManager.get().isLogin()) {
            mContext.startActivity(AccountIntentCreator.getIntent(mContext));
        } else {
            boolean isCollected = CollectManager.get().isProductCollect(item.id);
            if (!isCollected) {
                boolean isCollect = CollectManager.get().toggleProductCollect(item.id);
                setRemindStatusOnGoing(view, isCollect);
            } else {
                setRemindStatusOnGoing(view, true);
            }
        }
    }

    public void setOnRemindListener(OnRemindListener listener) {
        this.listener = listener;
    }

    public void setOnTimerListener(@Nullable WrapperOnCmsComponentTimerListener listener) {
        if (this.timerListener != null) {
            this.timerListener.cleanup();
        }
        if (listener != null) {
            listener.setAdditionalListener(new OnTimerListener() {

                @Override
                public void onRestart(long lastCountdown, long countdown) {
                    if (lastCountdown > getRemindThreshold() && countdown <= getRemindThreshold()) {
                        notifyRemind();
                    }
                }

                @Override
                public void onEnd() {
                    // no op
                }

                @Override
                public void onTimer(long countdown) {
                    if (countdown == getRemindThreshold()) {
                        notifyRemind();
                    }
                }
            });
        }
        this.timerListener = listener;
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    protected ImpressionBean getEagleImpressionEvent(Object item) {
        if (item instanceof ProductBean) {
            ProductBean bean = (ProductBean) item;
            String productId = String.valueOf(bean.id);
            int pos = mData.indexOf(item);
            String key = pos + "_" + productId;

            Map<String, Object> element = EagleTrackManger.get().getElement(mod_nm, mod_pos, sec_nm, sec_pos);
            Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_ITEM_LIGHTING_DEAL).setTarget(bean, pos).setElement(element).get();

            return new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, key);
        }
        return null;
    }

    public void setShowProgress(boolean showProgress) {
        this.showProgress = showProgress;
    }

    protected long getRemindThreshold() {
        return DEFAULT_REMIND_THRESHOLD;
    }

    private void notifyRemind() {
        notifyItemRangeChanged(0, getItemCount(), REMIND_PAYLOAD);
    }

    private static class RemindPayload {

    }

    protected int getProductItemWidth(Context context, @ProductView.DisplayStyle int displayStyle) {
        return ProductViewHelper.getProductItemWidth(context, displayStyle);
    }

    protected void setDisplayMinimumHeight(View view) {
        if (view != null) {
            if (cachedMinimumHeight <= 0) {
                cachedMinimumHeight = getDisplayMinimumHeight(view.getContext(), ProductViewHelper.getStrategy(getDisplayStyle()));
            }
            ViewTools.setMinimumHeight(view, cachedMinimumHeight);
        }
    }

    protected int getDisplayMinimumHeight(Context context, ProductViewDisplayStrategy strategy) {
        int displayStyle = getDisplayStyle();
        int baseHeight = getProductItemWidth(context, displayStyle);
        List<Object> items = CollectionUtils.newListOf(getData());
        ProductViewMinHeightCalculator calculator = new ProductViewMinHeightCalculator(baseHeight);
        int minHeight = baseHeight;
        for (Object it : items) {
            if (it instanceof LightningDealsProductBean) {
                LightningDealsProductBean bean = (LightningDealsProductBean) it;
                ProductViewElements elements = strategy.getDisplayElements(bean);
                elements.hasDeliveryDesc = false;
                elements.hasProductTags = false;
                elements.hasRemainingTip = false;
                elements.hasSoldNum = false;
                elements.hasEtaRange = false;
                elements.hasTopX = false;
                int myMinHeight = calculator.getMinHeight(elements);
                boolean beGoing = CmsLightingDealsData.STATUS_BE_GOING.equals(bean.status);
                if (beGoing || showProgress) {
                    myMinHeight += CommonTools.dp2px(32);  // timer, progress
                }
                minHeight = Math.max(minHeight, myMinHeight);
            }
        }
        minHeight += CommonTools.dp2px(8); // bottom extra margin
        return minHeight;
    }
}
