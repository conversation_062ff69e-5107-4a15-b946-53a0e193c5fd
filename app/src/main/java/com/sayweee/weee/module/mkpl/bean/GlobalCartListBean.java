package com.sayweee.weee.module.mkpl.bean;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class GlobalCartListBean implements Serializable {

    public List<GlobalCartBean> seller_float_cart_list;

    @SuppressWarnings("unused")
    public GlobalCartListBean() {
        // Default constructor for serialization
    }

    public GlobalCartListBean(List<GlobalCartBean> seller_float_cart_list) {
        this.seller_float_cart_list = seller_float_cart_list;
    }

    public boolean isEmpty() {
        return EmptyUtils.isEmpty(seller_float_cart_list);
    }

    public int getCartCount() {
        return CollectionUtils.size(seller_float_cart_list);
    }

    @Nullable
    public GlobalCartBean getGlobalCartBean(@Nullable String sellerId) {
        if (EmptyUtils.isEmpty(sellerId)) return null;
        if (EmptyUtils.isEmpty(seller_float_cart_list)) return null;
        final int sellerIdInt = DecimalTools.intValue(sellerId, 0);
        if (sellerIdInt == 0) return null;
        return CollectionUtils.firstOrNull(
                seller_float_cart_list,
                cart -> cart != null && cart.vendor_info != null && cart.vendor_info.vendor_id == sellerIdInt
        );
    }

    @NonNull
    public List<GlobalCartBrief> getGlobalCartBriefList() {
        List<GlobalCartBrief> cartBriefList = new ArrayList<>();
        if (EmptyUtils.isEmpty(seller_float_cart_list)) return cartBriefList;
        for (GlobalCartBean cart : seller_float_cart_list) {
            int quantity = cart.quantity;
            GlobalCartBean.VendorInfo vendorInfo = cart.vendor_info;
            if (quantity <= 0 || vendorInfo == null) continue;
            GlobalCartBrief brief = new GlobalCartBrief(String.valueOf(vendorInfo.vendor_id));
            brief.setQuantity(quantity);
            brief.setIconUrl(vendorInfo.vender_logo_url);
            cartBriefList.add(brief);
        }
        return cartBriefList;
    }

    @Nullable
    public static GlobalCartBean getGlobalCartBean(@Nullable GlobalCartListBean bean, @Nullable String sellerId) {
        return bean != null ? bean.getGlobalCartBean(sellerId) : null;
    }

}
