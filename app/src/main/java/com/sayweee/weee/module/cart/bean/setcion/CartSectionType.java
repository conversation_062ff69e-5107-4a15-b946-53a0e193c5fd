package com.sayweee.weee.module.cart.bean.setcion;

import com.sayweee.weee.module.cart.adapter.CartAdapter;

public interface CartSectionType {

    int TYPE_TITLE = 10; //购物车标题
    int TYPE_REMIND = 20; //购物车提醒数据
    int TYPE_STATS = 30; //购物车统计数据
    int TYPE_TIPS = 40; //购物车提示数据 凑单、换购、bogo、promotion
    @Deprecated
    int TYPE_TIPS_CART_DEAL = 41; //购物车提示数据 凑单、换购、bogo、promotion(新版活动模块)
    int TYPE_TIPS_CART_DEAL_TRADE_IN = 42;//购物车提示数据 凑单、换购、bogo、promotion(新版活动模块)===》仅适用于 trade in
    int TYPE_TIPS_FREE_SHIPPING = 43;// 购物车提示数据 免邮
    int TYPE_ACTIVITY_FOOTER = 50; //活动底部
    int TYPE_PRODUCT = 60; //商品数据
    int TYPE_PRODUCT_GIFT = 61; //赠品数据
    int TYPE_PRODUCT_SAVE_4_LATER = 62; //save 4 later 数据
    int TYPE_PRODUCT_ACTIVITY = 70; //活动商品数据
    int TYPE_PRODUCT_COLLAPSED = 80; //商品折叠数据
    int TYPE_EMPTY = 90; //空数据
    int TYPE_BOTTOM = 100;//底部占位
    int TYPE_SAVE_FOR_LATER_LOAD_MORE = 110;
    int TYPE_RECOMMEND_ITEM = 120;//新版活动模块推荐商品
    int TYPE_RECOMMEND_ITEM_MORE = 121;//新版活动模块推荐商品察看更多
    int TYPE_TIPS_TOP_PROMOTION = 130;//新版赠品模块
    int TYPE_MKPL_SHOP_MORE = 140; // mkpl shop more

    int CART_SECTION_PANEL_TITLE = CartAdapter.TYPE_PANEL_TITLE;//172
    int CART_SECTION_PANEL_PRODUCT = CartAdapter.TYPE_PANEL_PRODUCT;//173
    int TYPE_GROUP_ORDER = CartAdapter.TYPE_SECTION_GROUP_ORDER;//204
    int TYPE_ADD_ON = 205;

    int TYPE_SECTION_BEGIN = 9999; // 购物车列表起始位置
    int TYPE_TOP_MESSAGE = 206;
    int TYPE_CHECKOUT_BUTTON = 207; //单独结算按钮
}
