package com.sayweee.weee.module.mkpl.provider;

import android.graphics.Color;
import android.view.View;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.iml.product.ProductItemProvider;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.cms.utils.CmsTools;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.widget.shape.helper.ShapeHelper;

//
// Created by <PERSON><PERSON> on 20/10/2023.
// Copyright (c) 2023 Weee LLC. All rights reserved.
//
public class CmsProductSingleProvider extends ProductItemProvider {

    @Override
    public int getItemType() {
        return CmsItemType.PRODUCT_SINGLE;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_cms_product_single;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        setFullSpan(helper);
    }

    @Override
    protected void adjustProductViewMargins(AdapterViewHolder helper) {
        // disable margins
    }

    @Override
    public void convert(AdapterViewHolder helper, ProductItemData item) {
        super.convert(helper, item);
        View productView = helper.getView(R.id.layout_product_view);
        ShapeHelper.setBackgroundSolidDrawable(productView, Color.WHITE, CommonTools.dp2px(12));
        CmsTools.applyBackgroundStyle(helper.itemView, item.getBackgroundStyle());

        View productTraceView = ProductTraceViewHelper.findTraceView(helper.itemView);
        ViewTools.updateMargins(productTraceView, CommonTools.dp2px(10), null, CommonTools.dp2px(10), null);
    }
}
