package com.sayweee.weee.module.home.provider.message.data;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.base.adapter.AdapterDataRefresh;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.service.IMultiDataSourceData;
import com.sayweee.weee.module.home.bean.ICache;
import com.sayweee.weee.module.home.bean.NewTopMessageBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.wrapper.bean.FailureBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  wld
 * Date:    2023/7/6.
 * Desc:
 */
public class CmsNewTopMessageData
        extends ComponentData<List<NewTopMessageBean>, Void>
        implements ICache, IMultiDataSourceData, AdapterDataRefresh {

    public long systemTime;
    private CmsDataSource baseDataSource;

    public CmsNewTopMessageData() {
        super(CmsItemType.NEW_TOP_MESSAGE);
    }

    @Override
    public boolean isValid() {
        //isValid固定为true,数据是否有效需要在Provider里自行判断
        return true;
    }

    @Override
    public List<? extends AdapterDataType> toComponentData() {
        if (isValid()) {
            return CollectionUtils.arrayListOf(this);
        }
        return null;
    }

    public boolean isSkuType() {
        // Do not use getProduct() == null, just check the data is ads sku type
        NewTopMessageBean topMessageBean = CollectionUtils.firstOrNull(t);
        return topMessageBean != null && CollectionUtils.isNotEmpty(topMessageBean.ads_product_list);
    }

    @Nullable
    public ProductBean getProduct() {
        NewTopMessageBean topMessageBean = CollectionUtils.firstOrNull(t);
        ProductBean product = topMessageBean != null
                ? CollectionUtils.firstOrNull(topMessageBean.ads_product_list)
                : null;
        boolean invalidProduct = product == null
                || OrderManager.get().isReachLimit(product)
                || ProductView.isChangeOtherDay(product.sold_status)
                || ProductView.isSoldOut(product.sold_status);
        return !invalidProduct ? product : null;
    }

    @Override
    public void setData(List<NewTopMessageBean> newTopMessageBeans) {
        systemTime = System.currentTimeMillis() / 1000;
        super.setData(newTopMessageBeans);
    }

    //基础类型message字段不能为空
    public boolean isContentValid() {
        return t != null && !t.isEmpty() && t.get(0) != null && t.get(0).message != null;
    }

    public boolean isSkipPopup() {
        //只有一个的情况才允许跳过
        return isContentValid() && t.size() == 1 && t.get(0).type == 1 && !EmptyUtils.isEmpty(t.get(0).link);
    }

    public boolean onlyTitle() {
        if (!isContentValid()) {
            return true;
        }
        NewTopMessageBean bean = t.get(0);
        return EmptyUtils.isEmpty(bean.icon_img) && EmptyUtils.isEmpty(bean.countdown) && EmptyUtils.isEmpty(bean.message.sub_message) && bean.progress == null;
    }

    public boolean contentIsVisible() {
        return isContentValid() && (!EmptyUtils.isEmpty(t.get(0).message) && !EmptyUtils.isEmpty(t.get(0).message.sub_content));
    }

    public boolean rightImgIsVisible() {
        return isContentValid() && !EmptyUtils.isEmpty(t.get(0).right_cta) && t.get(0).right_cta.type == 1 && !EmptyUtils.isEmpty(t.get(0).right_cta.img);
    }

    public boolean rightTextIsVisible() {
        return isContentValid() && !EmptyUtils.isEmpty(t.get(0).right_cta) && t.get(0).right_cta.type == 2 && !EmptyUtils.isEmpty(t.get(0).right_cta.title);
    }

    @Override
    public void setBaseDataSource(@Nullable CmsDataSource dataSource) {
        this.baseDataSource = dataSource;
    }

    @Nullable
    @Override
    public CmsDataSource getBaseDataSource() {
        return baseDataSource;
    }

    @Override
    public void updateDataSourceResponse(@NonNull CmsDataSource dataSource, Object object, FailureBean failure) {
        List<NewTopMessageBean> newData = new ArrayList<>();
        if (object instanceof List) {
            CollectionUtils.mapInstanceTo(newData, (Iterable<?>) object, NewTopMessageBean.class);
        }
        setData(newData);
    }

    private int lastQuantity;
    private int currentQuantity;

    @Override
    public int getProductId() {
        ProductBean product = getProduct();
        return product != null ? product.getProductId() : 0;
    }

    @Override
    public String getProductKey() {
        ProductBean product = getProduct();
        return product != null ? product.getProductKey() : "";
    }

    @Override
    public void setProductQuantity(int quantity) {
        lastQuantity = currentQuantity;
        currentQuantity = quantity;
    }

    @Override
    public boolean isDirty() {
        return lastQuantity != currentQuantity;
    }

    @Override
    public int getProductQuantity() {
        return currentQuantity;
    }

    @Override
    public int getOrderMaxQuantity() {
        ProductBean product = getProduct();
        return product != null ? product.getOrderMaxQuantity() : 0;
    }

    @Override
    public int getOrderMinQuantity() {
        ProductBean product = getProduct();
        return product != null ? product.min_order_quantity : 0;
    }
}
