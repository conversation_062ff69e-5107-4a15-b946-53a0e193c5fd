package com.sayweee.weee.module.home.provider.category;

import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.home.bean.CategoriesBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.widget.BadgeTextView;

import java.util.ArrayList;
import java.util.List;

public class CmsCategoryBarAdapter extends CmsCategoryBaseAdapter {

    public CmsCategoryBarAdapter() {
        super(R.layout.item_category_bar);
    }

    @Override
    protected AdapterViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
        AdapterViewHolder vh = super.onCreateDefViewHolder(parent, viewType);
        vh.setIsRecyclable(false);
        return vh;
    }

    @Override
    public void setAdapterData(List<CategoriesBean.CategoryListBean> data, String style, String moreImageUrl) {
        List<CategoriesBean.CategoryListBean> nonNullData = CollectionUtils.orEmptyList(data);
        this.list = nonNullData;
        List<CategoriesBean> newDataList = new ArrayList<>();
        for (CategoriesBean.CategoryListBean bean : nonNullData) {
            CategoriesBean item = new CategoriesBean();
            List<CategoriesBean.CategoryListBean> child = new ArrayList<>();
            child.add(bean);
            item.category_list = child;
            newDataList.add(item);
        }
        setNewData(newDataList);
    }

    @Override
    public void onViewRecycled(@NonNull AdapterViewHolder holder) {
        super.onViewRecycled(holder);
        BadgeTextView tvReward = holder.getView(R.id.tv_reward);
        if (tvReward != null) {
            tvReward.stopSwitch(true);
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, CategoriesBean bean) {
        CategoriesBean.CategoryListBean item = bean.category_list.get(0);
        fillItem(helper, helper.itemView, item);
    }
}
