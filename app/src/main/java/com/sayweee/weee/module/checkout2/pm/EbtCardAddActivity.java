package com.sayweee.weee.module.checkout2.pm;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.sayweee.service.payment.PaymentKt;
import com.sayweee.service.payment.bean.CardAttachBean;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.LayoutAddEbtCardBinding;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.module.checkout2.pm.widget.ForageWidget;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.textfield.AfterTextChangedWatcher;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.utils.KeyboardUtils;

import java.util.List;
import java.util.Map;

public class EbtCardAddActivity extends WrapperMvvmActivity<PmActionViewModel> {

    private static final String EXTRA_CHANNEL_CODE = "EXTRA_CHANNEL_CODE";
    private static final String EXTRA_CARD_ATTACH_BEAN = "EXTRA_CARD_ATTACH_BEAN";
    private static final String EXTRA_SOURCE_URL = "EXTRA_SOURCE_URL";

    private LayoutAddEbtCardBinding binding;

    private EbtInputHelper.Holder inCardNumber;
    private EbtInputHelper.Holder inCardNumber2;
    private ForageWidget forageWidget;
    private ForageWidget forageWidget2;

    public static Intent getIntent(Context context, String channelCode) {
        return getIntent(context, channelCode, null);
    }

    public static Intent getUrlTradeIntent(Context context, String sourceUrl) {
        return getIntent(context, VariantConfig.PAYMENT_CHANNEL_CODE_EBT, sourceUrl);
    }

    private static Intent getIntent(Context context, String channelCode, String sourceUrl) {
        return new Intent(context, EbtCardAddActivity.class)
                .putExtra(EXTRA_CHANNEL_CODE, channelCode)
                .putExtra(EXTRA_SOURCE_URL, sourceUrl);
    }

    @NonNull
    private String getExtraChannelCode() {
        return EmptyUtils.orEmpty(getIntent() != null ? getIntent().getStringExtra(EXTRA_CHANNEL_CODE) : null);
    }

    private boolean getExtraIsOpenByUrl() {
        return !EmptyUtils.isEmpty(getIntent() != null ? getIntent().getStringExtra(EXTRA_SOURCE_URL) : null);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_add_ebt_card;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        if (!PaymentKt.isPayChannelCodeEbt(getExtraChannelCode())) {
            finish();
            return;
        }

        binding = LayoutAddEbtCardBinding.bind(findViewById(R.id.layout_add_ebt_card));
        initWrapperTitle();
        initHelperView();
        initInputs();
        toggleBtnConfirmState();

        if (getExtraIsOpenByUrl()) {
            showVeilTemplate(/* visible= */true);
        } else {
            KeyboardUtils.showSoftInput(binding.etCardNumber);
        }
    }

    private void initWrapperTitle() {
        if (!useWrapper()) {
            return;
        }
        int backgroundColor = ViewTools.getColorByResId(this, R.color.color_surface_100_bg, Color.WHITE);
        ((View) getWrapperTitle().getParent()).setBackgroundColor(backgroundColor);
        getView().setBackgroundColor(backgroundColor);
        setWrapperDivider(null);
        setWrapperTitle(getString(R.string.add_ebt_snap_card));
        TextView tvTitleCenter = getWrapperTitle().findViewById(R.id.tv_title_center);
        tvTitleCenter.setTextColor(getColor(R.color.color_navbar_fg_default));
        ImageView ivTitleLeft = getWrapperTitle().getView(R.id.iv_title_left);
        ViewTools.tintImageView(ivTitleLeft, R.color.color_navbar_fg_default);
    }

    private void initHelperView() {
        ViewTools.setViewHtml(binding.tvPoweredBy, getString(R.string.powered_by_forage));
        ViewTools.setViewOnSafeClickListener(binding.btnConfirm, this::onBtnConfirmClick);
        ViewTools.disallowTouchEvents(binding.vMask);
    }

    private void initInputs() {
        forageWidget = ForageWidget.wrap(binding.ebtPan);
        forageWidget2 = ForageWidget.wrap(binding.ebtPan2);

        inCardNumber = EbtInputHelper.holderOf(binding.flCardNumber, binding.ivCardNumber, binding.layoutCardNumber, binding.etCardNumber);
        inCardNumber.editText.setOnFocusChangeListener((v, hasFocus) -> {
            boolean hasError = inCardNumber.getState() instanceof EbtInputHelper.ErrorState;
            EbtInputHelper.newState(/* hasFocus= */hasFocus, /* hasError= */hasError).apply(inCardNumber);
        });
        inCardNumber.editText.addTextChangedListener(new AfterTextChangedWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                String text = s.toString();
                forageWidget.setText(text);
                inCardNumber.setTextWithoutTextWatcher(forageWidget.getText(), this);
                toggleBtnConfirmState();
                EbtInputHelper.newState(/* hasFocus= */true, /* hasError= */false).apply(inCardNumber);
                binding.tvCardNumberTips.setText(null);
            }
        });

        inCardNumber2 = EbtInputHelper.holderOf(binding.flCardNumber2, binding.ivCardNumber2, binding.layoutCardNumber2, binding.etCardNumber2);
        inCardNumber2.editText.setOnFocusChangeListener((v, hasFocus) -> {
            boolean hasError = inCardNumber2.getState() instanceof EbtInputHelper.ErrorState;
            EbtInputHelper.newState(/* hasFocus= */hasFocus, /* hasError= */hasError).apply(inCardNumber2);
        });
        inCardNumber2.editText.addTextChangedListener(new AfterTextChangedWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                String text = s.toString();
                forageWidget2.setText(text);
                inCardNumber2.setTextWithoutTextWatcher(forageWidget2.getText(), this);
                toggleBtnConfirmState();
                EbtInputHelper.newState(/* hasFocus= */true, /* hasError= */false).apply(inCardNumber2);
                binding.tvCardNumberTips2.setText(null);
            }
        });
    }

    private void toggleBtnConfirmState() {
        boolean isEnable = !EmptyUtils.isEmpty(inCardNumber.getText())
                && !EmptyUtils.isEmpty(inCardNumber2.getText());
        binding.btnConfirm.setEnabled(isEnable);
        if (isEnable) {
            ShapeHelper.setBackgroundSolidDrawable(
                    binding.btnConfirm,
                    ContextCompat.getColor(this, R.color.color_btn_primary_bg),
                    CommonTools.dp2px(this, R.dimen.prop_size_radius_full, 99f)
            );
            ViewTools.applyTextColor(binding.btnConfirm, R.color.color_btn_primary_fg_default);
        } else {
            ShapeHelper.setBackgroundSolidDrawable(
                    binding.btnConfirm,
                    ContextCompat.getColor(this, R.color.color_btn_disabled_bg),
                    CommonTools.dp2px(this, R.dimen.prop_size_radius_full, 99f)
            );
            ViewTools.applyTextColor(binding.btnConfirm, R.color.color_btn_disabled_fg_default);
        }
    }

    private boolean checkInput(EbtInputHelper.Holder holder) {
        boolean isValid = true;
        if (holder == inCardNumber) {
            String cardNumber = holder.getText().replaceAll("\\s", "");
            isValid = cardNumber.length() >= 16 && cardNumber.length() <= 19;
            boolean isSdkValid = binding.ebtPan.getElementState().isValid();
            String errorTips = null;
            if (!isValid) {
                errorTips = getString(R.string.ebt_card_number_error_tips);
            } else if (!isSdkValid) {
                isValid = false;
                errorTips = getString(R.string.ebt_card_number_invalid_tips);
            }
            if (!isValid) {
                EbtInputHelper.newState(/* hasFocus= */true, /* hasError= */true).apply(holder);
                binding.tvCardNumberTips.setText(errorTips);
            } else {
                binding.tvCardNumberTips.setText(null);
            }
        } else if (holder == inCardNumber2) {
            isValid = TextUtils.equals(holder.getText(), inCardNumber.getText());
            if (!isValid) {
                EbtInputHelper.newState(/* hasFocus= */true, /* hasError= */true).apply(holder);
                binding.tvCardNumberTips2.setText(R.string.ebt_card_number_again_error_tips);
            } else {
                binding.tvCardNumberTips2.setText(null);
            }
        }
        return isValid;
    }

    private boolean checkInputsOnConfirm() {
        boolean allValid = true;
        List<EbtInputHelper.Holder> holders = CollectionUtils.arrayListOf(
                inCardNumber, inCardNumber2
        );
        for (EbtInputHelper.Holder holder : holders) {
            EbtInputHelper.newState(false, false).apply(holder);
        }
        for (EbtInputHelper.Holder holder : holders) {
            allValid = checkInput(holder);
            if (!allValid) {
                holder.editText.requestFocus();
                break;
            }
        }
        return allValid;
    }

    private void onBtnConfirmClick(View view) {
        if (!checkInputsOnConfirm()) {
            return;
        }
        trackClickAction(EagleTrackEvent.TargetNm.SAVE);
        forageWidget.setText(binding.etCardNumber.getText());
        viewModel.attachEbtCard(getExtraChannelCode(), binding.ebtPan);
    }

    @Override
    public void loadData() {
        if (getExtraIsOpenByUrl()) {
            viewModel.getEbtPaymentProfiles();
        }
    }

    @Override
    public void attachModel() {
        viewModel.ebtFailureData.observe(this, failureBean -> {
            String message = PmActionViewModel.getEbtExceptionMessage(
                    /* context= */this,
                    /* exception= */failureBean != null ? failureBean.getException() : null
            );
            if (!message.isEmpty()) {
                Toaster.showToast(message);
            }
        });
        viewModel.attachCardBeanData.observe(this, this::handleAttachCardResult);
        viewModel.ebtProfilesData.observe(this, this::handleEbtProfilesData);
    }

    private void handleAttachCardResult(CardAttachBean attachBean) {
        if (attachBean != null) {
            if (getExtraIsOpenByUrl()) {
                Toaster.showToast(getString(R.string.s_checkout_ebt_card_has_ben_added));
            }
            Intent intent = new Intent();
            intent.putExtra(EXTRA_CHANNEL_CODE, getExtraChannelCode());
            intent.putExtra(EXTRA_CARD_ATTACH_BEAN, attachBean);
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    @SuppressWarnings("SameParameterValue")
    private void trackClickAction(String targetName) {
        Map<String, Object> params = new EagleTrackModel.Builder()
                .setTargetNm(targetName)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build()
                .getParams();
        AppAnalytics.logClickAction(params);
    }

    private void handleEbtProfilesData(List<CardAttachBean> list) {
        if (CollectionUtils.isEmpty(list)) {
            // No card attached, show input form
            showVeilTemplate(/* visible= */false);
            KeyboardUtils.showSoftInput(binding.etCardNumber);
        } else {
            // Has card attached, goto account settings page
            String nextUrl = AppConfig.HOST_WEB + Constants.Url.ACCOUNT_SETTINGS_PAYMENT_METHOD;
            startActivity(WebViewActivity.getIntent(this, nextUrl));
            finish();
        }
    }

    private void showVeilTemplate(boolean visible) {
        VeilTools.show(findViewById(R.id.veil_layout), visible);
    }
}
