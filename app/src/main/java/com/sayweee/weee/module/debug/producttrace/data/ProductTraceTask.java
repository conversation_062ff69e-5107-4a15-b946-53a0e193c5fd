package com.sayweee.weee.module.debug.producttrace.data;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.sayweee.weee.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductTraceTask {

    private final String traceId;
    private final ArrayList<Integer> productIds;

    private String pageKey;
    private String modNm;
    private String uniqueKey;

    private String topic;

    public ProductTraceTask(String traceId, Collection<Integer> productIds) {
        this.traceId = traceId;
        if (productIds != null) {
            this.productIds = new ArrayList<>(productIds);
        } else {
            this.productIds = new ArrayList<>();
        }
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getPageKey() {
        return pageKey;
    }

    public void setPageKey(String pageKey) {
        this.pageKey = pageKey;
    }

    public String getModNm() {
        return modNm;
    }

    // set by builder
    private void setModNm(String modNm) {
        this.modNm = modNm;
    }

    // set by builder
    private void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    @NonNull
    public ArrayList<Integer> getProductIds() {
        return productIds;
    }

    public String getTraceId() {
        return traceId;
    }

    public static class Builder {

        private final Map<String, List<Integer>> traceMap;

        private String modNm;
        private String uniqueKey;

        public Builder() {
            traceMap = new HashMap<>();
        }

        public Builder add(String traceId, int productId) {
            String tId = traceId != null ? traceId : "";
            List<Integer> pIds = traceMap.get(tId);
            if (pIds == null) {
                pIds = new ArrayList<>();
                traceMap.put(tId, pIds);
            }
            pIds.add(productId);
            return this;
        }

        public Builder setModNm(String modNm) {
            this.modNm = modNm;
            return this;
        }

        @SuppressWarnings("UnusedReturnValue")
        public Builder setUniqueKey(String uniqueKey) {
            this.uniqueKey = uniqueKey;
            return this;
        }

        public boolean merge(@NonNull Builder another) {
            if (TextUtils.equals(modNm, another.modNm) && TextUtils.equals(uniqueKey, another.uniqueKey)) {
                for (Map.Entry<String, List<Integer>> entry : another.traceMap.entrySet()) {
                    String tId = entry.getKey();
                    List<Integer> pIds = traceMap.get(tId);
                    if (pIds == null) {
                        pIds = new ArrayList<>();
                        traceMap.put(tId, pIds);
                    }
                    pIds.addAll(entry.getValue());
                }
                return true;
            }
            return false;
        }

        @NonNull
        public List<ProductTraceTask> build() {
            List<ProductTraceTask> tasks = new ArrayList<>();
            for (Map.Entry<String, List<Integer>> entry : traceMap.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    ProductTraceTask task = new ProductTraceTask(entry.getKey(), entry.getValue());
                    task.setModNm(modNm);
                    task.setUniqueKey(uniqueKey);
                    tasks.add(task);
                }
            }
            return tasks;
        }

    }

    public static class Result {

        private final ProductTraceTask task;

        private Map<ProductTraceKey, ProductTraceData> result;

        public Result(@NonNull ProductTraceTask task) {
            this.task = task;
        }

        public String getTopic() {
            return task.getTopic();
        }

        @SuppressWarnings("unused")
        public String getUniqueKey() {
            return task.getUniqueKey();
        }

        public void setResult(Map<ProductTraceKey, ProductTraceData> result) {
            this.result = result;
        }

        public Map<ProductTraceKey, ProductTraceData> getResult() {
            if (result != null) {
                return result;
            }
            return CollectionUtils.emptyMap();
        }

        public boolean isEmpty() {
            return result == null || result.isEmpty();
        }

    }

    public interface SingleProvider {
        void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder);
    }

    /**
     * Same as {@link SingleProvider}, but used for sections that can contain multiple products.
     */
    public interface SectionProvider {
        void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder);
    }
}
