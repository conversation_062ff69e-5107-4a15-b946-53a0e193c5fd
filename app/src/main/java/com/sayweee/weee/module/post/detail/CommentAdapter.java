package com.sayweee.weee.module.post.detail;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.content.res.ColorStateList;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleMultiTypeAdapter;
import com.sayweee.weee.module.post.detail.bean.PostCommentData;
import com.sayweee.weee.module.post.detail.bean.PostReplyActionData;
import com.sayweee.weee.module.post.detail.bean.PostReplyData;
import com.sayweee.weee.module.post.detail.bean.ReviewCommentBean;
import com.sayweee.weee.module.post.detail.bean.ReviewReplyData;
import com.sayweee.weee.module.post.helper.CommentHelper;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.module.post.widget.CommentLinkMovementMethod;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.widget.component.DrawableTextView;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2021/7/3.
 * Desc:
 */
public class CommentAdapter extends SimpleMultiTypeAdapter<AdapterDataType, AdapterViewHolder> {

    public static final int TYPE_COMMENT = 10;
    public static final int TYPE_REPLY = 20;
    public static final int TYPE_REPLY_ACTION = 30;
    public static final int TYPE_LINE = 40;

    protected String uid;
    protected OnPostEventListener listener;

    public void setUid(String uid) {
        this.uid = uid;
    }

    /**
     * 给对应评论追加或重置回复
     *
     * @param data
     */
    public void setReplyData(ReviewReplyData data) {
        int targetId = data.targetId;
        for (int i = 0; i < mData.size(); i++) {
            AdapterDataType item = mData.get(i);
            if (item instanceof PostCommentData) {
                PostCommentData target = (PostCommentData) item;
                if (target.isTarget(targetId)) {
                    mData.removeAll(target.getCachedAdapterData());
                    if (data.isReset) {
                        target.resetReplyData(data.commentBean);
                    } else {
                        target.appendReplyData(data.commentBean);
                    }
                    mData.addAll(i, target.getAdapterData());
                    notifyDataSetChanged();
                    break;
                }
            }
        }
    }

    /**
     * 重置评论数据
     *
     * @param list
     */
    public void resetCommentData(List<ReviewCommentBean.CommentItemBean> list) {
        setCommentData(list, true);
    }

    /**
     * 添加评论数据
     *
     * @param list
     */
    public void appendCommentData(List<ReviewCommentBean.CommentItemBean> list) {
        setCommentData(list, false);
    }

    private void setCommentData(List<ReviewCommentBean.CommentItemBean> list, boolean isResetAdapter) {
        if (!EmptyUtils.isEmpty(list)) {
            List<AdapterDataType> temp = new ArrayList<>();
            for (ReviewCommentBean.CommentItemBean item : list) {
                PostCommentData comment = new PostCommentData(item);
                temp.addAll(comment.getAdapterData());
            }
            if (isResetAdapter) {
                setNewData(temp);
            } else {
                addData(temp);
            }
        }
    }

    /**
     * 评论或回复被删除
     *
     * @param parentId
     * @param targetId
     */
    public void setCommentOrReplyDeleted(int parentId, int targetId) {
        int deleteQty = 0;
        boolean isComment = parentId == targetId;
        for (int i = 0; i < mData.size(); i++) {
            AdapterDataType item = mData.get(i);
            if (item instanceof PostCommentData) {
                PostCommentData target = (PostCommentData) item;
                if (target.isTarget(parentId)) {
                    if (isComment) {
                        deleteQty = 1 + target.getCommentCount();
                        //删除评论
                        mData.removeAll(target.getCachedAdapterData());
                    } else {
                        deleteQty = 1;
                        boolean deleted = ((PostCommentData) item).removeReply(targetId);
                        if (deleted) {
                            mData.removeAll(target.getCachedAdapterData());
                            mData.addAll(i, target.getAdapterData());
                        }
                    }
                    notifyDataSetChanged();
                    break;
                }
            }
        }
        if (listener != null) {
            listener.onCommentDeleted(deleteQty);
        }
        int j = 0;
        for (int i = 0; i < mData.size(); i++) {
            AdapterDataType item = mData.get(i);

            if (item instanceof PostCommentData) {
                j++;
            }
        }
        if (j <= 0) {
            setEnableLoadMore(false);
        }
    }

    @Override
    protected void registerAdapterType() {
        registerItemType(TYPE_COMMENT, R.layout.item_review_detail_comment);
        registerItemType(TYPE_REPLY, R.layout.item_review_detail_reply);
        registerItemType(TYPE_REPLY_ACTION, R.layout.item_review_detail_action);
        registerItemType(TYPE_LINE, R.layout.item_review_detail_line);
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        switch (item.getType()) {
            case TYPE_COMMENT:
                convertComment(helper, (PostCommentData) item);
                break;
            case TYPE_REPLY:
                convertReply(helper, (PostReplyData) item);
                break;
            case TYPE_REPLY_ACTION:
                convertReplyAction(helper, (PostReplyActionData) item);
                break;
            case TYPE_LINE:

                break;
        }
    }

    private void convertComment(AdapterViewHolder helper, PostCommentData item) {
        int parentIndex = mData.indexOf(item);
        ReviewCommentBean.CommentItemBean comment = item.comment;
        int parentId = comment.id;
        setCommentAndReplyDetail(helper, comment);
        setCommentAndReplyContent(helper, comment, false);
        helper.getView(R.id.cl_comment).setBackgroundColor(
                (EmptyUtils.isEmpty(comment.active) || comment.active.equals("C"))
                        ? mContext.getResources().getColor(R.color.color_fore)
                        : mContext.getResources().getColor(R.color.color_back));
        TextView tvComment = helper.getView(R.id.tv_comment);
        tvComment.setTypeface(null, (EmptyUtils.isEmpty(comment.status) || comment.status.equals("A") || comment.status.equals("C")) ? Typeface.NORMAL : Typeface.ITALIC);
        tvComment.setTextColor((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A") || comment.status.equals("C"))
                ? mContext.getResources().getColor(R.color.text_main)
                : mContext.getResources().getColor(R.color.text_lesser));

        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (v.getId() == R.id.tv_translation) {
                    if ((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                        comment.toggleTranslateStatus();
                        setCommentAndReplyContent(helper, comment, false);
                    }
                } else if (v.getId() == R.id.tv_reply) {
                    if (listener != null) {
                        if (AccountManager.get().isLogin()) {
                            if ((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                                listener.onReplyClick(parentIndex, parentId, comment);
                            }
                        } else {
                            mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                        }
                    }
                } else if (v.getId() == R.id.layout_praise) {
                    if (listener != null && AccountManager.get().isLogin()) {
                        if ((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                            comment.is_set_like = !comment.is_set_like;
                            comment.like_count = comment.is_set_like ? comment.like_count + 1 : comment.like_count - 1;
                            String praiseNum = comment.like_count >= 1000 ? (comment.like_count / 1000) + "k" : comment.like_count > 0 ? String.valueOf(comment.like_count) : "";
                            helper.setText(R.id.tv_praise_num, praiseNum);
                            helper.setImageResource(R.id.iv_praise, comment.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
                            if (listener != null) {
                                listener.onReplyPraise(parentIndex, parentId, comment);
                            }
                        }
                    } else {
                        mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                    }
                } else if (v.getId() == R.id.cl_comment) {
                    if (listener != null) {
                        if ((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                            listener.onReplyContentClick(parentIndex, parentId, comment);
                        }
                    }
                } else if ((v.getId() == R.id.iv_header || v.getId() == R.id.tv_name) && (EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                        if (!VariantConfig.IS_VIEW_VISIBLE && AccountManager.get().getUserIdInt() != comment.user_id) {
                            return;
                        }
                        mContext.startActivity(ProfileActivity.getIntent(mContext, "comment", comment.uid));
                    }

            }
        }, R.id.tv_translation, R.id.tv_reply, R.id.layout_praise, R.id.cl_comment, R.id.iv_header, R.id.tv_name);
        //长按事件
        setItemLongClick(helper.getView(R.id.tv_comment), parentIndex, comment, parentId);
        setItemLongClick(helper.getView(R.id.cl_comment), parentIndex, comment, parentId);
    }

    private void setItemLongClick(View view, int parentIndex, ReviewCommentBean.CommentItemBean comment, int parentId) {
        view.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if (listener != null) {
                    if ((EmptyUtils.isEmpty(comment.status) || comment.status.equals("A"))) {
                        listener.onItemLongClick(parentIndex, parentId, comment);
                    }
                }
                return true;
            }
        });
    }


    private void convertReply(AdapterViewHolder helper, PostReplyData item) {
        ReviewCommentBean.CommentItemBean reply = item.reply;
        setCommentAndReplyDetail(helper, reply);
        setCommentAndReplyContent(helper, reply, true);
        helper.getView(R.id.cl_reply).setBackgroundColor(
                (EmptyUtils.isEmpty(reply.active) || reply.active.equals("C"))
                        ? mContext.getResources().getColor(R.color.color_fore)
                        : mContext.getResources().getColor(R.color.color_back));
        TextView tvComment = helper.getView(R.id.tv_comment);
        tvComment.setTypeface(null, (EmptyUtils.isEmpty(reply.status) || reply.status.equals("A") || reply.status.equals("C")) ? Typeface.NORMAL : Typeface.ITALIC);
        tvComment.setTextColor((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A")) || reply.status.equals("C")
                ? mContext.getResources().getColor(R.color.text_main)
                : mContext.getResources().getColor(R.color.text_lesser));

        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {

                if (v.getId() == R.id.tv_translation) {
                    if ((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A"))) {
                        item.reply.toggleTranslateStatus();
                        setCommentAndReplyContent(helper, reply, true);
                    }
                } else if (v.getId() == R.id.tv_reply) {
                    if (AccountManager.get().isLogin()) {
                        if ((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A"))) {
                            if (listener != null) {
                                listener.onReplyClick(-1, item.parentId, reply);
                            }
                        }
                    } else {
                        mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                    }
                } else if (v.getId() == R.id.layout_praise) {
                    if ((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A"))) {
                        if (listener != null && AccountManager.get().isLogin()) {
                            reply.is_set_like = !reply.is_set_like;
                            reply.like_count = reply.is_set_like ? reply.like_count + 1 : reply.like_count - 1;
                            String praiseNum = reply.like_count >= 1000 ? (reply.like_count / 1000) + "k" : reply.like_count <= 0 ? "" : reply.like_count + "";
                            helper.setText(R.id.tv_praise_num, praiseNum);
                            helper.setImageResource(R.id.iv_praise, reply.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
                            if (listener != null) {
                                listener.onReplyPraise(-1, reply.id, reply);
                            }
                        } else {
                            mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                        }
                    }
                } else if (v.getId() == R.id.iv_header || v.getId() == R.id.tv_name || v.getId() == R.id.fl_header) {
                    if ((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A"))) {
                        mContext.startActivity(ProfileActivity.getIntent(mContext, "comment", reply.uid));
                    }
                }
            }
        }, R.id.tv_translation, R.id.tv_reply, R.id.layout_praise, R.id.iv_header, R.id.tv_name, R.id.fl_header);
        helper.itemView.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if ((EmptyUtils.isEmpty(reply.status) || reply.status.equals("A"))) {
                    if (listener != null) {
                        listener.onReplyContentClick(-1, item.parentId, reply);
                    }
                }
            }
        });
        setItemLongClick(helper.itemView, -1, reply, item.parentId);
        setItemLongClick(helper.getView(R.id.tv_comment), -1, reply, item.parentId);
    }

    protected void setCommentAndReplyDetail(AdapterViewHolder helper, ReviewCommentBean.CommentItemBean item) {
        ImageLoader.load(mContext, helper.getView(R.id.iv_header), WebpManager.get().getConvertUrl(SPEC_AVATAR, item.user_avatar), R.mipmap.post_user_placeholder);
        TextView tv = helper.getView(R.id.tv_name);
        LinearLayout ll = helper.getView(R.id.layout_name);
        helper.setText(R.id.tv_name, item.user_name);
        helper.setVisibleCompat(R.id.tv_author, !EmptyUtils.isEmpty(uid) && uid.equalsIgnoreCase(item.uid));
//        helper.setVisibleCompat(R.id.iv_badge, !EmptyUtils.isEmpty(item.badge_img));
        ViewTreeObserver vt = ll.getViewTreeObserver();
        if (item.verified_seller) {
            helper.setImageResource(R.id.iv_badge, R.mipmap.ic_account_verified);
        } else {
//            if (!EmptyUtils.isEmpty(item.badge_img)) {
            ImageLoader.load(mContext, helper.getView(R.id.iv_badge), WebpManager.convert(
                    ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, item.badge_img));
//            }
        }
        vt.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (ll.getWidth() > 0) {
//                    tv.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                    int maxWidth = ll.getWidth();
                    if (maxWidth > 0) {
                        if (!EmptyUtils.isEmpty(uid) && uid.equalsIgnoreCase(item.uid)) {
                            maxWidth = maxWidth - helper.getView(R.id.tv_author).getWidth() - CommonTools.dp2px(4);
                        }
                        if (!EmptyUtils.isEmpty(item.badge_img)) {
                            maxWidth = maxWidth - CommonTools.dp2px(24);
                        }

                        tv.setMaxWidth(maxWidth);
                    }
                    tv.setText(item.user_name);
                }
            }
        });
        helper.setText(R.id.tv_date, formatDate(item.rec_create_time));
        String praiseNum = item.like_count >= 1000 ? (item.like_count / 1000) + "k" : item.like_count > 0 ? String.valueOf(item.like_count) : "";
        helper.setVisibleCompat(R.id.tv_praise_num, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        helper.setVisibleCompat(R.id.iv_praise, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        helper.setVisibleCompat(R.id.tv_reply, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        helper.setText(R.id.tv_praise_num, praiseNum);
        helper.setImageResource(R.id.iv_praise, item.is_set_like ? R.mipmap.post_collect_new : R.mipmap.post_uncollect_new);
    }

    protected void setCommentAndReplyContent(AdapterViewHolder helper, ReviewCommentBean.CommentItemBean item, boolean isReply) {
        String content = item.getCurrentContent();
        TextView tvComment = helper.getView(R.id.tv_comment);
        tvComment.setTypeface(null, (EmptyUtils.isEmpty(item.active) || item.active.equals("A")) ? Typeface.NORMAL : Typeface.BOLD_ITALIC);
        tvComment.setTextColor((EmptyUtils.isEmpty(item.active) || item.active.equals("A"))
                ? mContext.getResources().getColor(R.color.text_main)
                : mContext.getResources().getColor(R.color.text_lesser));
        helper.setVisibleCompat(R.id.tv_praise_num, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        helper.setVisibleCompat(R.id.iv_praise, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        helper.setVisibleCompat(R.id.tv_reply, (EmptyUtils.isEmpty(item.status) || item.status.equals("A")));
        //评论内容
        if (!EmptyUtils.isEmpty(content)) {
            Spanny resolvedContent = CommentHelper.resolveStyle(content, "comment|" + item.id);
            helper.setText(R.id.tv_comment, resolvedContent);
            //tvComment.setMovementMethod(LinkMovementMethod.getInstance());
            //定制的comment span click
            tvComment.setMovementMethod(CommentLinkMovementMethod.getInstance());
        }
        helper.setVisibleCompat(R.id.tv_translation, item.showTranslatePortal() && item.isStatusValid());
        helper.setText(R.id.tv_translation, item.useOrigin() ? R.string.s_see_translation : R.string.s_see_original);
    }

    private void convertReplyAction(AdapterViewHolder helper, PostReplyActionData data) {
        PostCommentData item = data.comment;
        DrawableTextView tvAction = helper.getView(R.id.tv_action);
        String text;
        Drawable drawable;
        if (item.isExpand) {
            if (item.isHasLoadAll()) {
                text = mContext.getString(R.string.collapse);
                drawable = ContextCompat.getDrawable(mContext, R.mipmap.pic_blue_up);
            } else {
                text = mContext.getString(R.string.view_more_replies);
                drawable = ContextCompat.getDrawable(mContext, R.mipmap.pic_blue_down);
            }
        } else {
            text = String.format(mContext.getString(R.string.view_replies), String.valueOf(item.comment.comments_count - (item.activeIndex >= 0 ? item.activeIndex + 1 : 1)));
            drawable = ContextCompat.getDrawable(mContext, R.mipmap.pic_blue_down);
        }
        if (drawable != null) {
            int tintColor = ResourcesCompat.getColor(mContext.getResources(), R.color.color_surface_1_fg_minor_focus, null);
            drawable.setTintList(ColorStateList.valueOf(tintColor));
        }
        tvAction.setRightDrawable(drawable, CommonTools.dp2px(7.2f), CommonTools.dp2px(12));
        tvAction.setText(text);
        tvAction.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (item.isExpand) {
                    if (item.isHasLoadAll()) {
                        //当前展开状态 且已加载全部数据
                        item.isExpand = false;
                        onReplyActionTrigger(data);
                    } else {
                        //加载更多
                        if (listener != null) {
                            listener.onReplyLoadMore(item.comment.id, item.getReplyPage());
                        }
                    }
                } else {
                    if (item.isHasLoad()) {
                        item.isExpand = true;
                        onReplyActionTrigger(data);
                    } else {
                        //加载更多
                        if (listener != null) {
                            listener.onReplyLoadMore(item.comment.id, item.getReplyPage());
                        }
                    }
                }
            }
        });
    }

    private void onReplyActionTrigger(PostReplyActionData data) {
        int index = mData.indexOf(data.comment);
        mData.removeAll(data.comment.getCachedAdapterData());
        AdapterDataType a = data.comment.getAdapterData().get(0);
        mData.addAll(index, data.comment.getAdapterData());
        notifyDataSetChanged();
    }

    private String formatDate(long time) {
        boolean isPtOrEs = LanguageManager.get().isPortuguese() || LanguageManager.get().isSpanish();
        return isPtOrEs ? DateUtils.getFormatTime(DateUtils.DD_MM_YY, DateUtils.convertServerTime(time), "") : DateUtils.formatMessageDate(mContext, time);
    }

    public void setOnPostEventListener(OnPostEventListener listener) {
        this.listener = listener;
    }

    public interface OnPostEventListener {

        void onReplyLoadMore(int parentId, int page);

        void onReplyClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean);

        void onReplyPraise(int position, int parentId, ReviewCommentBean.CommentItemBean bean);

        void onReplyContentClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean);

        void onCommentDeleted(int qty);

        void onItemLongClick(int position, int parentId, ReviewCommentBean.CommentItemBean bean);
    }
}
