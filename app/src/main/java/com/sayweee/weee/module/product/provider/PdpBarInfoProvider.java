package com.sayweee.weee.module.product.provider;

import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.request.RequestOptions;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.data.PdpBarInfoData;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BackgroundColorTransformation;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PdpBarInfoProvider extends SimpleSectionProvider<PdpBarInfoData, AdapterViewHolder>{


    @Override
    public int getItemType() {
        return PdpItemType.PDP_BAR_INFO;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_pdp_bar_info;
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder holder) {
        setLayoutParamsMargin(holder, CommonTools.dp2px(20));
        super.onViewAttachedToWindow(holder);
    }

    protected void setLayoutParamsMargin(AdapterViewHolder holder, int margin) {
        if (holder != null) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof RecyclerView.LayoutParams) {
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) layoutParams;
                params.leftMargin = margin;
                params.rightMargin = margin;
                params.topMargin = CommonTools.dp2px(5);
            }
        }
    }

    @Override
    public void convert(AdapterViewHolder helper, PdpBarInfoData item) {
        helper.setVisibleCompat(R.id.iv_shape, item.t.hasBrand());
        ImageLoader.load(context, helper.getView(R.id.iv_icon)
                ,  WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, item.t.icon)
                , new RequestOptions().placeholder(R.color.color_product_bg_img_tint)
                        .transform(new BackgroundColorTransformation()));
        helper.setText(R.id.tv_title, ViewTools.fromHtml(item.t.info_html));
        helper.setVisibleCompat(R.id.iv_arrow, !EmptyUtils.isEmpty(item.t.more_link) || "free_gifts".equalsIgnoreCase(item.t.module_type));
        if ("free_gifts".equalsIgnoreCase(item.t.module_type)) {
            helper.addOnClickListener(R.id.layout_bar_info);
        } else {
            helper.itemView.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    context.startActivity(WebViewActivity.getIntent(context, item.t.more_link));
                }
            });
        }
    }
}
