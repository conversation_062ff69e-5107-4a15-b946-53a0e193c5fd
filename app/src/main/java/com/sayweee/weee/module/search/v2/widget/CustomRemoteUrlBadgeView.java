package com.sayweee.weee.module.search.v2.widget;

import android.content.Context;
import android.content.res.Resources;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.utils.ViewTools;

public class CustomRemoteUrlBadgeView extends CustomImageView implements CustomBadgeView {

    static boolean DID_PRELOAD_IMAGES = false;

    boolean didLoadBadge = false;

    public CustomRemoteUrlBadgeView(@NonNull Context context) {
        super(context);
    }

    public CustomRemoteUrlBadgeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomRemoteUrlBadgeView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void loadPantryPlusBadge() {
        loadBadge(Constants.SearchV2.CDN_URL_PANTRY_PLUS_ICON);
    }

    public void loadGlobalPlusBadge() {
        loadBadge(Constants.SearchV2.CDN_URL_GLOBAL_PLUS_ICON);
    }

    public void loadBadge(String url) {
        if (didLoadBadge) return;

        didLoadBadge = true;

        this.load(
                url,
                ViewTools.tryGetWidth(this),
                ViewTools.tryGetHeight(this),
                null,
                CustomImageView.FLAG_IS_WEEECDN_URL,
                null,
                null
        ).show(new Callback() {
            @Override
            public void onError() {
                didLoadBadge = false;
            }

            @Override
            public void onSuccess() {

            }
        });
    }

    @Override
    public View getContainerView() {
        return this;
    }

    @Override
    public TextView getTextView() {
        return null;
    }

    public static void preloadRemoteImages(Context ctx) {
        if (DID_PRELOAD_IMAGES) {
            return;
        }

        DID_PRELOAD_IMAGES = true;

        try {
            Resources res = ctx.getResources();
            CustomImageView.preloadMedia(
                    ctx,
                    Constants.SearchV2.CDN_URL_PANTRY_PLUS_ICON,
                    res.getDimensionPixelSize(R.dimen.search_v2_remote_badge_width),
                    res.getDimensionPixelSize(R.dimen.search_v2_remote_badge_height),
                    null,
                    CustomImageView.FLAG_IS_WEEECDN_URL,
                    false
            );
        } catch (Exception e) {
            if (DevConfig.isDebug()) {
                Logger.e(e);
            }
        }
    }
}
