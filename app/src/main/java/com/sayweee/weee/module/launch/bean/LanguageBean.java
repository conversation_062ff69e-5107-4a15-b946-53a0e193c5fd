package com.sayweee.weee.module.launch.bean;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/4/28.
 * Desc:
 */
public class LanguageBean implements Serializable {

    public List<Lang> language;

    public static class Lang implements Serializable {
        public String key;
        public String label;
        public String label_en;
    }

    @JSONField(serialize = false, deserialize = false)
    public boolean isEmpty() {
        return language == null || language.isEmpty();
    }

}
