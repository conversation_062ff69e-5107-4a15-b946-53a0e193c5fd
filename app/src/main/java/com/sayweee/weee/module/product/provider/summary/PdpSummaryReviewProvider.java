package com.sayweee.weee.module.product.provider.summary;


import android.text.TextUtils;

import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ProviderPdpSummaryReviewBinding;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cms.track.ImpressionProvider;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.ViewTools;

import java.util.ArrayList;
import java.util.List;

//
// Created by <PERSON><PERSON> on 31/03/2025.
//
public class PdpSummaryReviewProvider extends SimpleSectionProvider<PdpSummaryReviewData, AdapterViewHolder>
        implements ImpressionProvider<PdpSummaryReviewData> {
    @Override
    public int getItemType() {
        return R.layout.provider_pdp_summary_review;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.provider_pdp_summary_review;
    }

    private ProviderPdpSummaryReviewBinding binding;


    @Override
    public void convert(AdapterViewHolder helper, PdpSummaryReviewData item) {
        binding = ProviderPdpSummaryReviewBinding.bind(helper.itemView);

        PostCategoryBean.ListBean bean = item.t;

        ImageLoader.load(context, binding.ivReview,
                WebpManager.convert(55, 98, bean.show_url));
        binding.tvTitle.setText(bean.comment_lang);

        String subTitle = "";
        if (bean.rec_create_time > 0) {
            String date = DateUtils.formatReviewData(context, bean.rec_create_time);
            subTitle += date;
        }
        if (!TextUtils.isEmpty(bean.user_name)) {
            subTitle += " · ";
            subTitle += bean.user_name;
        }
        ViewTools.setViewVisible(binding.tvSubTitle, !TextUtils.isEmpty(subTitle));
        binding.tvSubTitle.setText(subTitle);


    }

    @Override
    public List<ImpressionBean> fetchImpressionData(PdpSummaryReviewData item, int position) {
        List<ImpressionBean> list = new ArrayList<>();
        if (null != item.impressionBean) {
            list.add(item.impressionBean);
        }
        return list;

    }
}
