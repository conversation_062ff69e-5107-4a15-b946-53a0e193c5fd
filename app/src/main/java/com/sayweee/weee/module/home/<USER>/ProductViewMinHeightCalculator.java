package com.sayweee.weee.module.home.adapter;

import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.product.ProductViewElements;

public class ProductViewMinHeightCalculator {

    private final int baseHeight;

    public ProductViewMinHeightCalculator(int baseHeight) {
        this.baseHeight = baseHeight;
    }

    public int getMinHeight(ProductViewElements elements) {
        int height = baseHeight;
        if (elements.hasVolumePrice()) {
            height += getVolumePriceHeight();
        } else {
            height += getPriceHeight();
        }
        if (elements.hasProductBrandName()) {
            height += getBrandNameHeight();
        }
        if (elements.hasName()) {
            height += getNameHeight();
        }
        if (elements.hasDeliveryDesc()) {
            height += getDeliveryDescHeight();
        }
        if (elements.hasProductTags()) {
            height += getProductTagsHeight();
        }
        if (elements.hasRemainingTip()) {
            height += getRemainingTipHeight();
        }
        if (elements.hasSoldNum()) {
            height += getSoldNumHeight();
        }
        if (elements.hasEtaRange()) {
            height += getEtaRangeHeight();
        }
        if (elements.hasVendor()) {
            height += getVendorHeight();
        }
        if (elements.hasTopX()) {
            height += getTopXHeight();
        }
        return height;
    }

    protected int getVolumePriceHeight() {
        return CommonTools.dp2px(48f);
    }

    protected int getPriceHeight() {
        return CommonTools.dp2px(32f);
    }

    protected int getBrandNameHeight() {
        return CommonTools.dp2px(20f);
    }

    protected int getNameHeight() {
        return CommonTools.dp2px(40f);
    }

    protected int getDeliveryDescHeight() {
        return CommonTools.dp2px(20f);
    }

    protected int getProductTagsHeight() {
        return CommonTools.dp2px(24f);
    }

    protected int getRemainingTipHeight() {
        return CommonTools.dp2px(18f);
    }

    protected int getSoldNumHeight() {
        return CommonTools.dp2px(22f);
    }

    protected int getEtaRangeHeight() {
        return CommonTools.dp2px(20f);
    }

    protected int getVendorHeight() {
        return CommonTools.dp2px(20f);
    }

    protected int getTopXHeight() {
        return CommonTools.dp2px(24f);
    }
}