package com.sayweee.weee.module.post.adapter;

import static com.sayweee.weee.service.webp.ImageSpec.SPEC_AVATAR;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleMultiTypeAdapter;
import com.sayweee.weee.module.post.bean.LikeBean;
import com.sayweee.weee.module.post.helper.FollowButtonHelper;
import com.sayweee.weee.module.post.helper.SocialStatusHelper;
import com.sayweee.weee.module.post.profile.ProfileActivity;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.widget.shape.helper.ShapeHelper;

import java.util.List;

public class LikeAdapter extends SimpleMultiTypeAdapter<AdapterDataType, AdapterViewHolder> {

    public static final int TYPE_NORMAL = 10000;
    public static final String PAYLOADS_FOLLOW = "follow";

    public void setAdapterData(List<LikeBean.ListBean> list) {
        mData.clear();
        if (!EmptyUtils.isEmpty(list)) {
            mData.addAll(list);
        }
        notifyDataSetChanged();
    }

    public void appendAdapterData(List<LikeBean.ListBean> list) {
        if (!EmptyUtils.isEmpty(list)) {
            addData(list);
        }
    }

    @Override
    protected void registerAdapterType() {
        registerItemType(TYPE_NORMAL, R.layout.item_follower_user);
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        if (item instanceof LikeBean.ListBean) {
            convertLikeBean(helper, (LikeBean.ListBean) item);
        }
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, AdapterDataType item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        if (item instanceof LikeBean.ListBean) {
            LikeBean.ListBean bean = (LikeBean.ListBean) item;
            View followButton = helper.getView(R.id.tv_follow);
            FollowButtonHelper.bind(followButton, bean.profile_status);
        }
    }

    private void convertLikeBean(@NonNull AdapterViewHolder helper, LikeBean.ListBean bean) {
        ImageLoader.load(
                mContext,
                helper.getView(R.id.iv_header),
                WebpManager.get().getConvertUrl(SPEC_AVATAR, bean.avatar),
                R.mipmap.default_header
        );
        helper.setText(R.id.tv_name, bean.alias);

        View llStar = helper.getView(R.id.ll_star);
        if (bean.verified_seller) {
            // account verified icon
            ViewTools.setViewVisible(llStar, false);
            ViewTools.setViewVisible(helper.getView(R.id.iv_badge), true);
            helper.setImageResource(R.id.iv_badge, R.mipmap.ic_account_verified);
        } else if (!EmptyUtils.isEmpty(bean.user_star_label)) {
            ViewTools.setViewVisible(helper.getView(R.id.iv_badge), false);
            ViewTools.setViewVisible(llStar, true);
            helper.setVisible(R.id.ll_star, true);
            ImageLoader.load(
                    mContext,
                    helper.getView(R.id.iv_star),
                    WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, bean.badge_img)
            );
            TextView tvStar = helper.getView(R.id.tv_star);
            tvStar.setText(bean.user_star_label);
            ViewTools.applyTextColor(tvStar, bean.user_star_color, Color.TRANSPARENT);
            ShapeHelper.setBackgroundSolidDrawable(llStar, Color.parseColor(bean.user_star_bg_color), CommonTools.dp2px(100));
        } else if (!EmptyUtils.isEmpty(bean.badge_img)) {
            ViewTools.setViewVisible(llStar, false);
            ImageLoader.load(
                    mContext,
                    helper.getView(R.id.iv_badge),
                    WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, bean.badge_img)
            );
            ViewTools.setViewVisible(helper.getView(R.id.iv_badge), true);
        } else {
            ViewTools.setViewVisible(llStar, false);
            ViewTools.setViewVisible(helper.getView(R.id.iv_badge), false);
        }

        StringBuilder sb = new StringBuilder();
        if (!bean.isNoPost()) {
            String new_count = bean.isSimplePost()
                    ? mContext.getResources().getString(R.string.post)
                    : mContext.getResources().getString(R.string.posts);
            sb.append(bean.post_count_label).append(" ").append(new_count);
        }
        if (!bean.isNoFollowers()) {
            String followers = bean.isSimpleFollowers() && LanguageManager.get().getLanguage().equals("en") ?
                    mContext.getResources().getString(R.string.followers).toLowerCase().replace("s", "")
                    : mContext.getResources().getString(R.string.followers).toLowerCase();
            if (sb.length() != 0) {
                sb.append(" | ");
            }
            sb.append(bean.follower_count_label).append(" ").append(followers);
        }
        helper.setText(R.id.tv_followers_new_posts, sb.toString());
        helper.setGone(R.id.tv_followers_new_posts, sb.length() != 0);

        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                switch (v.getId()) {
                    case R.id.iv_header:
                    case R.id.cl_layout:
                        if (!VariantConfig.IS_VIEW_VISIBLE && AccountManager.get().getUserIdInt() != bean.user_id) {
                            return;
                        }
                        mContext.startActivity(ProfileActivity.getIntent(mContext, "post_like", bean.uid));
                        break;
                    case R.id.tv_follow:
                        if (AccountManager.get().isLogin()) {
                            boolean isFollowed = SocialStatusHelper.isFollowed(bean.profile_status);
                            SocialStatusHelper.setFollowStatus(!isFollowed, bean, true);
                            int pos = helper.getLayoutPosition();
                            notifyItemChanged(pos, PAYLOADS_FOLLOW);
                        } else {
                            mContext.startActivity(AccountIntentCreator.getIntent(mContext));
                        }
                        break;
                }
            }
        }, R.id.iv_header, R.id.tv_follow, R.id.cl_layout);

        View followButton = helper.getView(R.id.tv_follow);
        if (TextUtils.equals(AccountManager.get().getUserId(), String.valueOf(bean.user_id)) || !VariantConfig.IS_VIEW_VISIBLE) {
            followButton.setVisibility(View.GONE);
        } else {
            FollowButtonHelper.bind(followButton, bean.profile_status);
            followButton.setVisibility(View.VISIBLE);
        }
    }
}