package com.sayweee.weee.module.checkout.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleSectionProvider;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.checkout.PopOrderProductsFragment;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.DeliveryWindowBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.helper.AlcoholHelper;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.recycler.HorizontalItemDecoration;
import com.sayweee.weee.widget.span.ClickableImageSpan;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.util.ArrayList;
import java.util.List;

public class CheckoutOrderItemProvider extends SimpleSectionProvider<CheckoutReviewOrderData, AdapterViewHolder> {

    private CheckoutDeliveryWindowAdapter.OnDeliveryWindowActionListener onDeliveryWindowActionListener;

    @Override
    public int getItemType() {
        return CheckOutSectionAdapter.TYPE_REVIEW_ORDER_LIST;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_review_order;
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        RecyclerView recyclerView = helper.getView(R.id.rv_delivery_window);
        LinearLayoutManager layoutManager;
        layoutManager = new LinearLayoutManager(helper.itemView.getContext(), LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setNestedScrollingEnabled(false);
        CheckoutDeliveryWindowAdapter adapter = new CheckoutDeliveryWindowAdapter();
        adapter.setOnDeliveryWindowActionListener(onDeliveryWindowActionListener);
        RecyclerView.ItemDecoration itemDecoration = new HorizontalItemDecoration(
                CommonTools.dp2px(12f),
                CommonTools.dp2px(12f),
                CommonTools.dp2px(8f)
        );
        recyclerView.addItemDecoration(itemDecoration);
        recyclerView.setAdapter(adapter);

        helper.addOnClickListener(R.id.tv_support_change_date);
    }

    @Override
    public void convert(AdapterViewHolder helper, CheckoutReviewOrderData item) {
        // R.layout.item_review_order
        Context context = helper.itemView.getContext();
        PreCheckoutOrderReviewsBean bean = item.orderReviewsDTO;
        helper.loadImage(context, R.id.iv_local_delivery, WebpManager.convert(ImageSpec.SPEC_64, bean.shipping_info.shipping_icon_url));
        helper.setText(R.id.tv_title, bean.shipping_info.shipping_type_desc);
        helper.setText(R.id.tv_content, bean.shipping_info.shipping_desc);
        helper.setGone(R.id.tv_date, !bean.shipping_info.is_support_change_date);
        helper.setText(R.id.tv_date, bean.shipping_info.shipping_shipment_date);
        helper.setGone(R.id.tv_support_change_date, bean.shipping_info.is_support_change_date);
        if (bean.shipping_info.is_support_change_date) {
            Spanny spanny = new Spanny();
            Drawable drawable = ContextCompat.getDrawable(context, R.mipmap.ic_arrow_right_blue);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(16), CommonTools.dp2px(16));
            }
            spanny.append(bean.shipping_info.shipping_shipment_date).append("", new CenterImageSpan(drawable));
            helper.setText(R.id.tv_support_change_date, spanny);
        }
        helper.setTextHtml(R.id.tv_sub_total, context.getString(R.string.s_checkout_stats, String.valueOf(bean.quantity), OrderHelper.formatUSMoney(Double.parseDouble(bean.fee_info.sub_total_price))));
        helper.setGone(R.id.tv_free_delivery, Double.parseDouble(bean.fee_info.shipping_fee) <= 0);
        helper.setGone(R.id.tv_shipping_fee, Double.parseDouble(bean.fee_info.shipping_fee) > 0);
        helper.setGone(R.id.tv_desc, !EmptyUtils.isEmpty(bean.shipping_info.delivery_content));
        helper.setTextHtml(R.id.tv_shipping_fee, String.format(context.getString(R.string.s_checkout_shipping_fee_amount), OrderHelper.formatUSMoney(Double.parseDouble(bean.fee_info.shipping_fee))));
        String deliveryContent = !EmptyUtils.isEmpty(bean.shipping_info.delivery_time_desc) ? bean.shipping_info.delivery_time_desc + "\n" + bean.shipping_info.delivery_content : bean.shipping_info.delivery_content;
        helper.setText(R.id.tv_desc, deliveryContent);
        if (!EmptyUtils.isEmpty(bean.alcohol_desc)) {
            int alcoholAgreementType = Constants.OrderSubType.COMMISSION_PARTNER.equals(bean.sub_order_type)
                    ? Constants.AlcoholAgreementType.SELLER
                    : Constants.AlcoholAgreementType.GROCERY; // 有酒精提示的话一定包含酒类吧～
            String faqUrl = AlcoholHelper.getAlcoholFaqUrl(
                    alcoholAgreementType == Constants.AlcoholAgreementType.SELLER,
                    bean.vendor_info != null ? bean.vendor_info.vendor_id : null
            );
            Spanny spanny = new Spanny(bean.alcohol_desc).append(" ");
            Drawable drawable = ContextCompat.getDrawable(context, R.drawable.drawable_order_review_alcohol_desc);
            if (drawable != null) {
                drawable.setBounds(0, 0, CommonTools.dp2px(10), CommonTools.dp2px(10));
            }
            spanny.append(" ", new ClickableImageSpan(drawable) {
                @Override
                public void onClick(View view) {
                    Context ctx = view.getContext();
                    if (ctx != null) {
                        ctx.startActivity(WebViewActivity.getIntent(ctx, faqUrl));
                    }
                }
            });

            helper.setText(R.id.tv_alcohol_desc, spanny);
            helper.setGone(R.id.tv_alcohol_desc, true);
        } else {
            helper.setGone(R.id.tv_alcohol_desc, false);
        }

        convertProducts(helper, bean);
        convertDeliveryWindow(helper, bean);
        String deliveryWindowContent = bean.shipping_info != null ? bean.shipping_info.delivery_window_content : null;
        helper.setGone(R.id.tv_delivery_window_content, !EmptyUtils.isEmpty(deliveryWindowContent));
        helper.setText(R.id.tv_delivery_window_content, deliveryWindowContent);
        //click
        helper.setOnViewClickListener(v -> {
            if (context instanceof FragmentActivity && (!EmptyUtils.isEmpty(bean.group_buy_user_products) || !EmptyUtils.isEmpty(bean.order_lines))) {
                List<Object> list = new ArrayList<>();
                if (!EmptyUtils.isEmpty(bean.group_buy_user_products)) {
                    list.addAll(bean.group_buy_user_products);
                } else {
                    list.addAll(bean.order_lines);
                }
                String tag = PopOrderProductsFragment.class.getName() + bean.id;
                PopOrderProductsFragment orderProductsFragment;
                orderProductsFragment = PopOrderProductsFragment.newInstance(list, bean.quantity);
                orderProductsFragment.show(((FragmentActivity) context).getSupportFragmentManager(), tag);
            }
        }, R.id.ll_product, R.id.iv_arrow);
    }

    private void convertProducts(AdapterViewHolder helper, PreCheckoutOrderReviewsBean bean) {
        Context context = helper.itemView.getContext();
        LinearLayout llProduct = helper.getView(R.id.ll_product);
        ViewTools.removeAllViews(llProduct);
        if (EmptyUtils.isEmpty(bean.order_lines)) {
            return;
        }

        int size = (CommonTools.getWindowWidth(context) - CommonTools.dp2px(84)) / CommonTools.dp2px(56);
        int count = Math.min(size, bean.order_lines.size());
        for (int i = 0; i < count; i++) {
            PreCheckoutOrderReviewsBean.OrderLinesBean child = bean.order_lines.get(i);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    CommonTools.dp2px(54)
            );
            layoutParams.rightMargin = CommonTools.dp2px(2);
            int finalI = i;
            llProduct.addView(ViewTools.getHelperView(llProduct, R.layout.item_checkout_section_product, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    helper.setVisible(R.id.layout, true);
                    helper.setVisible(R.id.tv_num, child.quantity > 1);
                    helper.setText(R.id.tv_num, "x" + child.quantity);
                    helper.setVisible(R.id.tv_item_num, false);
                    if (count < bean.order_lines.size() - 1) {
                        if (finalI == count - 1) {
                            helper.setVisible(R.id.layout, false);
                            helper.setVisible(R.id.tv_num, false);
                            helper.setVisible(R.id.tv_item_num, true);
                            helper.setText(R.id.tv_item_num, "+" + (bean.order_lines.size() - finalI));
                        }
                    }
                    ImageLoader.load(
                            context,
                            helper.getView(R.id.iv),
                            WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, child.img),
                            R.color.color_place
                    );
                }
            }), layoutParams);
        }
    }

    private void convertDeliveryWindow(AdapterViewHolder helper, PreCheckoutOrderReviewsBean bean) {
        RecyclerView rvDeliveryWindow = helper.getView(R.id.rv_delivery_window);
        RecyclerView.Adapter<?> a = rvDeliveryWindow.getAdapter();
        List<DeliveryWindowBean> windows = bean.shipping_info != null ? bean.shipping_info.delivery_windows : null;
        if (!(a instanceof CheckoutDeliveryWindowAdapter) || EmptyUtils.isEmpty(windows)) {
            ViewTools.setViewVisibilityIfChanged(rvDeliveryWindow, false);
            return;
        }

        ((CheckoutDeliveryWindowAdapter) a).setNewData(windows);
        ViewTools.setViewVisibilityIfChanged(rvDeliveryWindow, true);
    }

    public void setOnDeliveryWindowActionListener(CheckoutDeliveryWindowAdapter.OnDeliveryWindowActionListener listener) {
        this.onDeliveryWindowActionListener = listener;
    }
}
