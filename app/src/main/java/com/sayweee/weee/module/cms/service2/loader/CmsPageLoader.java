package com.sayweee.weee.module.cms.service2.loader;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.service2.bean.CmsPageData;
import com.sayweee.weee.module.cms.service2.bean.ICmsPagingData;
import com.sayweee.weee.module.cms.service2.fetcher.CmsComponentFetcher;
import com.sayweee.weee.module.cms.service2.fetcher.CmsComponentLocalFetcher;
import com.sayweee.weee.module.cms.service2.fetcher.CmsComponentRemoteFetcher;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.core.compat.SimpleObserver;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * An abstract class responsible for loading data for a CMS page.
 * This loader manages the lifecycle of data fetching for a given {@link CmsPageData},
 * processing components sequentially and dispatching results via a {@link Callback}.
 */
public abstract class CmsPageLoader {

    public static final String TAG = "CmsPageLoader";

    protected final CmsPageData page;
    protected Consumer<? super Disposable> consumer;
    protected Map<String, CmsComponentFetcher> componentFetcherMap;
    protected Map<String, CmsComponentFetcher.Factory> componentFetcherFactoryMap;
    protected Callback callback;

    protected boolean isLoading = false;
    protected int pageNum = 0;
    protected String nextStartPoint;

    private boolean isAllComponentConsumedDispatched = false;

    /**
     * Constructs a CmsPageLoader.
     *
     * @param page The {@link CmsPageData} that defines the page structure.
     */
    protected CmsPageLoader(CmsPageData page) {
        this.page = page;
        this.componentFetcherMap = new HashMap<>();
        this.componentFetcherFactoryMap = new HashMap<>();
        init();
    }

    /**
     * Initializes the loader by setting the initial start point.
     */
    @CallSuper
    protected void init() {
        nextStartPoint = getInitialStartPoint();
    }

    /**
     * Sets the RxJava consumer to manage the lifecycle of disposables.
     *
     * @param consumer The consumer for {@link Disposable} objects.
     */
    public void setConsumer(Consumer<? super Disposable> consumer) {
        this.consumer = consumer;
    }

    /**
     * Registers a factory for creating custom {@link CmsComponentFetcher} instances.
     *
     * @param componentKey The unique key of the component.
     * @param factory      The factory that creates the fetcher.
     */
    public void registerComponentFetcherFactory(String componentKey, CmsComponentFetcher.Factory factory) {
        if (componentFetcherFactoryMap == null) {
            componentFetcherFactoryMap = new HashMap<>();
        }
        componentFetcherFactoryMap.put(componentKey, factory);
    }

    /**
     * Checks if the loader is currently fetching data.
     *
     * @return {@code true} if loading, {@code false} otherwise.
     */
    public boolean isLoading() {
        return isLoading;
    }

    /**
     * Sets the callback to receive data loading events.
     *
     * @param callback The callback implementation.
     */
    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    /**
     * Initiates the data loading process for the page.
     */
    public final void loadData() {
        Logger.i(TAG, "loadData() " + "pageNum=" + pageNum + ", initStartPoint=" + nextStartPoint);
        if (isLoading()) {
            return;
        }
        if (isDataLoadFinished()) {
            dispatchDataLoadFinished(pageNum);
            return;
        }

        isLoading = true;
        loadDataInternal();
    }

    /**
     * Abstract method for subclasses to implement the data loading logic.
     */
    protected abstract void loadDataInternal();

    /**
     * Retrieves all components from the starting component ID to the end of the list.
     *
     * @param componentId The component ID to start from.
     * @return A collection of remaining {@link ComponentData} instances.
     */
    protected final Collection<ComponentData<?, ?>> getRemainingComponents(String componentId) {
        List<ComponentData<?, ?>> list = new ArrayList<>();
        boolean hasFound = false;
        for (ComponentData<?, ?> component : page.getComponentDataList()) {
            if (!hasFound && componentId.equals(component.getComponentId())) {
                hasFound = true;
            }
            if (hasFound) {
                list.add(component);
            }
        }
        return list;
    }

    /**
     * Gets an existing {@link CmsComponentFetcher} or creates a new one.
     *
     * @param component The component for which to get or create a fetcher.
     * @return A {@link CmsComponentFetcher} for the specified component.
     */
    protected final CmsComponentFetcher getOrCreateComponentFetcher(ComponentData<?, ?> component) {
        String componentId = component.getComponentId();
        CmsDataSource dataSource = page.getDataSourceById(componentId);
        if (dataSource == null) {
            Logger.w(TAG, "==> getOrCreateComponentFetcher() " + "componentId=" + componentId + " dataSource not found!");
            dataSource = new CmsDataSource(componentId, component.getComponentKey());
        }
        CmsComponentFetcher fetcher = componentFetcherMap.get(component.getComponentId());
        if (fetcher == null) {
            fetcher = createComponentFetcher(component, dataSource);
            componentFetcherMap.put(component.getComponentId(), fetcher);
        }
        return fetcher;
    }

    /**
     * Creates a new {@link CmsComponentFetcher} for a given component.
     *
     * @param component  The component data.
     * @param dataSource The data source for the component.
     * @return A new {@link CmsComponentFetcher} instance.
     */
    @NonNull
    protected final CmsComponentFetcher createComponentFetcher(ComponentData<?, ?> component, CmsDataSource dataSource) {
        CmsComponentFetcher fetcher;
        CmsComponentFetcher.Factory factory = CollectionUtils.getOrNull(componentFetcherFactoryMap, component.getComponentKey());
        if (factory != null) {
            fetcher = factory.create(component, dataSource);
        } else {
            if (dataSource.isRemote()) {
                fetcher = new CmsComponentRemoteFetcher(component, dataSource);
            } else {
                fetcher = new CmsComponentLocalFetcher(component, dataSource);
            }
        }
        fetcher.setConsumer(consumer);
        return fetcher;
    }

    /**
     * Gets the initial component ID to start loading from.
     *
     * @return The first component's ID, or {@code null} if the page is empty.
     */
    @Nullable
    protected String getInitialStartPoint() {
        ComponentData<?, ?> component = CollectionUtils.firstOrNull(page.getComponentDataList());
        return component != null ? component.getComponentId() : null;
    }

    /**
     * Gets the ID of the component that immediately follows the current one.
     *
     * @param currentPoint The current component ID.
     * @return The next component ID, or {@code null} if it's the last one.
     */
    @Nullable
    protected String getNextStartPoint(String currentPoint) {
        boolean hasFound = false;
        String nextId = null;
        for (ComponentData<?, ?> component : page.getComponentDataList()) {
            if (!hasFound && currentPoint.equals(component.getComponentId())) {
                hasFound = true;
                continue;
            }
            if (hasFound) {
                nextId = component.getComponentId();
                break;
            }
        }
        return nextId;
    }

    /**
     * Executes a collection of {@link CmsComponentFetcher} instances concurrently.
     *
     * @param fetchers The collection of fetchers to execute.
     */
    protected final void performFetches(@NonNull final Collection<CmsComponentFetcher> fetchers) {
        Logger.d(TAG, "==> performFetches() " + "pageNum=" + pageNum + ", size=" + fetchers.size());
        int bufferSize = Math.max(Observable.bufferSize(), fetchers.size());
        Observer<? super CmsComponentFetcher> observer = new SimpleObserver<CmsComponentFetcher>() {

            private long startTime;

            @Override
            public void onSubscribe(Disposable d) {
                super.onSubscribe(d);
                startTime = System.currentTimeMillis();
            }

            @Override
            public void onComplete() {
                super.onComplete();
                long duration = System.currentTimeMillis() - startTime;
                Logger.d(TAG, "<== performFetches() size=" + fetchers.size() + ", duration=" + duration + "ms");
                handlePerformFetchesComplete(fetchers);
            }
        };

        Observable<CmsComponentFetcher> observable = Observable.fromIterable(fetchers);
        if (DevConfig.isDebug()) {
            observable = observable.flatMap(fetcher -> fetcher.rxFetch().compose(fetcher.debugTimer()), true, 10, bufferSize);
        } else {
            observable = observable.flatMap(CmsComponentFetcher::rxFetch, true, 10, bufferSize);
        }
        observable = observable.compose(DisposableTransformer.scheduler(consumer, true));
        observable.subscribe(observer);
    }

    /**
     * Processes the results after a batch of component fetches is complete.
     *
     * @param fetchers The collection of fetchers that have completed.
     */
    protected final void handlePerformFetchesComplete(@NonNull Collection<CmsComponentFetcher> fetchers) {
        String currentStartPoint = null;
        List<ComponentData<?, ?>> newDataList = new LinkedList<>();
        boolean hasPaging = false;
        boolean hasMoreData = false;
        List<AdapterDataType> appendAdapterData = new ArrayList<>();
        for (CmsComponentFetcher fetcher : fetchers) {
            CmsComponentFetcher.Result result = fetcher.getResult();
            ComponentData<?, ?> component = result.getComponent();
            if (fetcher.isPaging()) {
                if (result.isFirstPage()) {
                    newDataList.add(component);
                } else {
                    hasPaging = true;
                    if (component.isValid()) {
                        List<? extends AdapterDataType> data;
                        if (component instanceof ICmsPagingData) {
                            data = ((ICmsPagingData) component).toAdapterData(result.getPageNum());
                        } else {
                            data = component.toComponentData();
                        }
                        if (data != null) {
                            appendAdapterData.addAll(data);
                        }
                    }
                }
            } else {
                newDataList.add(component);
            }
            hasMoreData = fetcher.hasMoreData();
            currentStartPoint = component.getComponentId();
        }

        String nextPoint = getNextStartPoint(currentStartPoint);
        this.nextStartPoint = hasMoreData ? currentStartPoint : nextPoint;
        boolean isAllComponentConsumed = nextPoint == null;
        Logger.d(TAG, "handlePerformFetchesComplete() currentStartPoint=" + currentStartPoint
                + ", hasMoreData=" + hasMoreData
                + ", nextStartPoint=" + nextStartPoint
                + ", isAllComponentConsumed=" + isAllComponentConsumed
        );

        int pageNum = this.pageNum++;
        if (hasPaging) {
            dispatchAppendAdapterData(pageNum, appendAdapterData);
        } else {
            dispatchData(pageNum, newDataList);
        }
        if (isAllComponentConsumed) {
            dispatchAllComponentsConsumed();
        }
        if (isDataLoadFinished()) {
            dispatchDataLoadFinished(pageNum);
        }
    }

    /**
     * Checks if all data for the page has been loaded.
     *
     * @return {@code true} if data loading is finished, {@code false} otherwise.
     */
    protected boolean isDataLoadFinished() {
        return nextStartPoint == null;
    }

    /**
     * Dispatches newly loaded component data to the callback.
     *
     * @param pageNum     The current page number.
     * @param newDataList The list of new {@link ComponentData}.
     */
    protected void dispatchData(int pageNum, @NonNull List<ComponentData<?, ?>> newDataList) {
        isLoading = false;
        if (callback != null) {
            callback.onPageLoaderDataLoaded(pageNum, newDataList);
        }
    }

    /**
     * Dispatches paginated data to be appended to the UI.
     *
     * @param pageNum           The current page number.
     * @param appendAdapterData The list of {@link AdapterDataType} items to append.
     */
    protected void dispatchAppendAdapterData(int pageNum, @NonNull List<AdapterDataType> appendAdapterData) {
        isLoading = false;
        if (callback != null) {
            callback.onPageLoaderAppendAdapterDataLoaded(pageNum, appendAdapterData);
        }
    }

    /**
     * Notifies the callback that the entire page load is finished.
     *
     * @param pageNum The final page number.
     */
    protected void dispatchDataLoadFinished(int pageNum) {
        if (callback != null) {
            callback.onPageLoaderDataLoadFinished(pageNum);
        }
    }

    protected void dispatchAllComponentsConsumed() {
        if (isAllComponentConsumedDispatched) {
            return;
        }
        isAllComponentConsumedDispatched = true;
        if (callback != null) {
            callback.onPageLoaderAllComponentsConsumed();
        }
    }

    /**
     * Callback interface for {@link CmsPageLoader} events.
     */
    public interface Callback {

        /**
         * Called when a new batch of component data has been loaded.
         *
         * @param pageNum     The page number of the loaded data.
         * @param newDataList The list of new {@link ComponentData}.
         */
        void onPageLoaderDataLoaded(int pageNum, @NonNull List<ComponentData<?, ?>> newDataList);

        /**
         * Called when additional data for a paginated component has been loaded.
         *
         * @param pageNum            The page number of the appended data.
         * @param newAdapterDataList The list of new {@link AdapterDataType} items to append.
         */
        void onPageLoaderAppendAdapterDataLoaded(int pageNum, @NonNull List<AdapterDataType> newAdapterDataList);

        /**
         * Called when there is no more data to load for the entire page.
         *
         * @param pageNum The final page number.
         */
        void onPageLoaderDataLoadFinished(int pageNum);

        /**
         * Called when all components in the page layout have been processed.
         * Note: The last component may still have more data if it supports pagination.
         */
        void onPageLoaderAllComponentsConsumed();
    }
}
