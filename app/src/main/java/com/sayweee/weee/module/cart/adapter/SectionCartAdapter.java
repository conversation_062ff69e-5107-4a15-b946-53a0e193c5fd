package com.sayweee.weee.module.cart.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.cart.CartIndicatorHelper;
import com.sayweee.weee.module.cart.bean.AdapterCartData;
import com.sayweee.weee.module.cart.bean.AdapterPanelData;
import com.sayweee.weee.module.cart.bean.AdapterPanelTitleData;
import com.sayweee.weee.module.cart.bean.AdapterProductData;
import com.sayweee.weee.module.cart.bean.AddOnDetailBean;
import com.sayweee.weee.module.cart.bean.CartAddOnDetailData;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.CartSellerDetailData;
import com.sayweee.weee.module.cart.bean.CartTopMessageData;
import com.sayweee.weee.module.cart.bean.GroupBuySellerDetailBean;
import com.sayweee.weee.module.cart.bean.ICartImpression;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.bean.TagInfo;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartSectionData;
import com.sayweee.weee.module.cart.bean.setcion.AdapterCartTopSectionData;
import com.sayweee.weee.module.cart.bean.setcion.CartSectionType;
import com.sayweee.weee.module.cart.bean.setcion.RecommendItemData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartActivityFooterData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartCheckoutButtonData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartCollapsedData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartDealData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartFreeShippingData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartProductData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartRemindData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartSave4LaterMoreData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartTipsData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartTitleData;
import com.sayweee.weee.module.cart.bean.setcion.SectionCartTopPromotionData;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.iml.blank.BlankProvider;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.cms.iml.product.data.ProductItemData;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.mkpl.bean.GlobalMiniCartAction;
import com.sayweee.weee.module.product.ProductDetailActivity;
import com.sayweee.weee.module.product.bean.ProductPageParams;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.analytics.ImpressionHelper;
import com.sayweee.weee.service.analytics.factory.EagleFactory;
import com.sayweee.weee.service.analytics.factory.EagleType;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.SpannyUtils;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.indicator.CompatMagicIndicator;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OpActionHelper;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.refresh.CartLoadingMoreView;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.listener.OnAdapterChildClickListener;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class SectionCartAdapter extends SimpleMultiTypeSectionAdapter<AdapterDataType, AdapterViewHolder> implements EagleImpressionAdapter {

    public static final int TOP_FRAME = 1;
    public static final int MIDDLE_FRAME = 2;
    public static final int BOTTOM_FRAME = 3;
    public static final int INNER_TOP_FRAME = 10;
    public static final int INNER_MIDDLE_FRAME = 20;
    public static final int INNER_BOTTOM_FRAME = 30;

    private final int interval = CommonTools.dp2px(12);
    private final List<AdapterDataType> adapterCartItemList = new ArrayList<>();
    private final List<AdapterDataType> adapterSave4LaterList = new ArrayList<>();

    private AdapterCartTopSectionData cartTopSectionData;
    private AdapterCartSectionData save4laterCartData;
    private CartProductItemProvider provider;
    private SectionCartDealData cartDealDataNew;

    public void setPacketCartData(List<AdapterCartSectionData> adapterCartSectionDataList) {
        removeTopSectionItems(mData);
        //cart模块
        mData.removeAll(adapterCartItemList);
        adapterCartItemList.clear();
        //save4Later模块
        mData.removeAll(adapterSave4LaterList);
        adapterSave4LaterList.clear();
        List<AdapterDataType> list = new ArrayList<>();
        int itemCount = CollectionUtils.size(adapterCartSectionDataList);
        for (int i = 0; i < itemCount; i++) {
            AdapterCartSectionData adapterCartSectionData = adapterCartSectionDataList.get(i);
            if (adapterCartSectionData.isSaveForLaterCart()) {
                this.save4laterCartData = adapterCartSectionData;
                adapterSave4LaterList.addAll(adapterCartSectionData.toAdapterData());
                break;
            }
            List<AdapterDataType> dataTypes = adapterCartSectionData.toAdapterData();
            list.addAll(dataTypes);

        }
        adapterCartItemList.addAll(list);
        List<AdapterDataType> total = new ArrayList<>();
        total.addAll(adapterCartItemList);
        total.addAll(adapterSave4LaterList);
        this.mData.addAll(0, total);
        if (cartTopSectionData != null) {
            this.mData.addAll(0, cartTopSectionData.toAdapterData());
        }
        if (provider != null) {
            provider.notifyParentDataChanged(total);
        }
        notifyPacketDataSetChanged();
    }

    public void setPacketCartDataSilent(List<AdapterCartSectionData> list) {
        int lastSize = 0;
        if (!adapterCartItemList.isEmpty()) {
            lastSize = adapterCartItemList.size();
            this.mData.removeAll(adapterCartItemList);
            this.adapterCartItemList.clear();
        }
        int itemCount = CollectionUtils.size(list);
        for (int i = 0; i < itemCount; i++) {
            AdapterCartSectionData item = list.get(i);
            if (item.isSaveForLaterCart()) {
                save4laterCartData = item;
                break;
            }
            adapterCartItemList.addAll(item.toAdapterData());

        }
        int size = adapterCartItemList.size();
        int indexStart = CollectionUtils.indexOfFirst(mData, it -> it.getType() == CartSectionType.TYPE_SECTION_BEGIN);
        if (indexStart == -1) {
            indexStart = 0;
        } else {
            indexStart += 1;
            if (indexStart > this.mData.size()) {
                indexStart = this.mData.size();
            }
        }
        this.mData.addAll(indexStart, adapterCartItemList);

        if (provider != null) {
            provider.notifyParentDataChanged(adapterCartItemList);
        }

        if (lastSize > size) {
            notifyItemRangeRemoved(indexStart + size, lastSize - size);
            notifyItemRangeChanged(indexStart, size);
        } else if (lastSize == size) {
            notifyItemRangeChanged(indexStart, size);
        } else {
            notifyItemRangeChanged(indexStart, lastSize);
            notifyItemRangeInserted(indexStart + lastSize, size - lastSize);
        }
    }

    public void setTopSectionData(@NonNull AdapterCartTopSectionData cartTopSectionData) {
        this.cartTopSectionData = cartTopSectionData;
        removeTopSectionItems(mData);
        mData.addAll(0, cartTopSectionData.toAdapterData());
        notifyPacketDataSetChanged();
    }

    private void removeTopSectionItems(List<AdapterDataType> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        int position = CollectionUtils.indexOfFirst(dataList, it -> it.getType() == CartSectionType.TYPE_SECTION_BEGIN);
        for (int i = 0; i <= position; i++) {
            if (!dataList.isEmpty()) {
                dataList.remove(0);
            }
        }
    }

    public void setPacketPanelData(AdapterPanelData panelData) {
        if (!adapterPanelSet.isEmpty()) {
            mData.removeAll(adapterPanelSet);
        }
        adapterPanelSet.clear();

        int panelTitleIndex = CollectionUtils.indexOfFirst(mData, it -> it instanceof AdapterPanelTitleData);
        if (panelTitleIndex != -1) {
            mData.remove(panelTitleIndex);
            // remove fake margin top
            if (CollectionUtils.getOrNull(mData, panelTitleIndex - 1) instanceof CmsBlankData) {
                mData.remove(panelTitleIndex - 1);
            }
        }

        // add fake margin top
        AdapterDataType lastItem = CollectionUtils.lastOrNull(mData);
        if (!(lastItem instanceof CmsBlankData)) {
            mData.add(new CmsBlankData());
        }
        if (CollectionUtils.isNotEmpty(panelData.boughtData) && CollectionUtils.isNotEmpty(panelData.recommendData)) {
            //两种商品都有数据
            isMultiData = true;
            List<Integer> list = CollectionUtils.arrayListOf(R.string.s_cart_recommend_for_u, R.string.s_cart_bought);
            recommendData.clear();
            recommendData.addAll(panelData.recommendData);//缓存
            boughtData.clear();
            boughtData.addAll(panelData.boughtData);//缓存

            mData.add(new AdapterPanelTitleData(list));
            adapterPanelSet.addAll(panelData.recommendData);
        } else {
            //只有一种商品有数据
            isMultiData = false;
            if (panelData.boughtData != null && !panelData.boughtData.isEmpty()) {
                adapterPanelSet.add(new AdapterPanelTitleData(R.string.s_cart_bought, AdapterPanelTitleData.TYPE_BOUGHT));
                adapterPanelSet.addAll(panelData.boughtData);
            } else if (panelData.recommendData != null && !panelData.recommendData.isEmpty()) {
                adapterPanelSet.add(new AdapterPanelTitleData(R.string.s_cart_recommend_for_u, AdapterPanelTitleData.TYPE_RECOMMEND));
                adapterPanelSet.addAll(panelData.recommendData);
            }
        }
        mData.addAll(adapterPanelSet);
        notifyPacketDataSetChanged();
    }

    public void addPacketPanelData(AdapterPanelData panelData) {
        List<AdapterDataType> targetData = new ArrayList<>();
        if (isMultiData) {
            //两种数据
            if (indexIndicator == 0) {
                if (panelData.recommendData != null) {
                    recommendData.addAll(panelData.recommendData);
                    adapterPanelSet.addAll(panelData.recommendData);
                    targetData.addAll(panelData.recommendData);
                }
            } else {
                if (panelData.boughtData != null) {
                    boughtData.addAll(panelData.boughtData);
                    adapterPanelSet.addAll(panelData.boughtData);
                    targetData.addAll(panelData.boughtData);
                }
            }
        } else {
            //一种数据
            if (panelData.boughtData != null && !panelData.boughtData.isEmpty()) {
                adapterPanelSet.addAll(panelData.boughtData);
                targetData.addAll(panelData.boughtData);
            } else if (panelData.recommendData != null && !panelData.recommendData.isEmpty()) {
                adapterPanelSet.addAll(panelData.recommendData);
                targetData.addAll(panelData.recommendData);
            }
        }
        addData(targetData);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void notifyPacketDataSetChanged() {
        this.notifyDataSetChanged();
    }

    public void notifyProviderEditModeChanged() {
        if (provider != null && provider.getEditProductId() > 0) {
            int targetIndex = -1;
            int targetProductId = provider.getEditProductId();
            List<AdapterDataType> data = getData();
            for (int i = 0; i < data.size(); i++) {
                AdapterDataType item = data.get(i);
                if (item instanceof SectionCartProductData) {
                    SectionCartProductData temp = (SectionCartProductData) item;
                    if (provider.isEditable(temp.t.product_id, temp.t.product_key)) {
                        targetIndex = i;
                        break;
                    }
                }
            }
            if (targetIndex != -1) {
                clearEditMode();
                notifyItemChanged(targetIndex, new GlobalMiniCartAction.Collapse(targetProductId));
            }
        }
    }

    final List<SoftReference<BaseQuickAdapter>> childAdapterCaches = new ArrayList<>();

    public void onPageResume() {
        if (childAdapterCaches != null && childAdapterCaches.size() > 0) {
            List<SoftReference<BaseQuickAdapter>> invalids = new ArrayList<>();
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter != null) {
                        ProductSyncHelper.onPageResume(adapter);
                        onPageResumeImpression(adapter);
                    } else {
                        invalids.add(reference);
                    }
                }
            }
            if (invalids.size() > 0) {
                childAdapterCaches.removeAll(invalids);
            }
        }
    }

    public void onPagePause() {
        clearEditMode();
        if (childAdapterCaches != null && childAdapterCaches.size() > 0) {
            List<SoftReference<BaseQuickAdapter>> invalids = new ArrayList<>();
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter != null) {
                        onPagePauseImpression(adapter);
                    } else {
                        invalids.add(reference);
                    }
                }
            }
            if (invalids.size() > 0) {
                childAdapterCaches.removeAll(invalids);
            }
        }
    }

    public void clearEditMode() {
        if (provider != null) {
            provider.clearEditData();
        }
    }

    @Override
    protected void registerAdapterType() {
        super.registerAdapterType();
        registerItemType(CartSectionType.TYPE_SECTION_BEGIN, R.layout.item_cart_section_begin);
        registerItemType(CartSectionType.TYPE_ADD_ON, R.layout.item_cart_section_add_on);//add on banner
        registerItemType(CartSectionType.TYPE_GROUP_ORDER, R.layout.item_cart_section_group_order);//group order
        registerItemType(CartSectionType.TYPE_TITLE, R.layout.item_cart_section_title_new);//标题
        registerItemType(CartSectionType.TYPE_REMIND, R.layout.item_cart_section_remind);//提醒数据
        registerItemType(CartSectionType.TYPE_TIPS, R.layout.item_cart_section_tips_new);//促销数据
        registerItemType(CartSectionType.TYPE_TIPS_CART_DEAL, R.layout.item_cart_section_tips_cart_deal);//促销数据(新版活动模块)
        registerItemType(CartSectionType.TYPE_TIPS_CART_DEAL_TRADE_IN, R.layout.item_cart_section_tips_cart_deal_trade_in);
        registerItemType(CartSectionType.TYPE_TIPS_TOP_PROMOTION, R.layout.item_cart_top_promotion);//新版赠品模块，脱离于单个购物车之外的单独赠品样式
        registerItemType(CartSectionType.TYPE_ACTIVITY_FOOTER, R.layout.item_cart_section_activity_footer);//活动底部
        registerItemType(CartSectionType.TYPE_PRODUCT_COLLAPSED, R.layout.item_cart_section_products_pack);//商品折叠数据
        registerItemType(CartSectionType.TYPE_SAVE_FOR_LATER_LOAD_MORE, R.layout.item_cart_section_save_for_later_load_more);
        registerItemType(CartSectionType.TYPE_CHECKOUT_BUTTON, R.layout.item_cart_section_checkout_button);

        registerItemType(CartSectionType.CART_SECTION_PANEL_TITLE, R.layout.item_panel_title_section);
        registerItemType(CartSectionType.CART_SECTION_PANEL_PRODUCT, R.layout.item_product_card);

        registerItemType(CartSectionType.TYPE_BOTTOM, R.layout.item_cart_bottom);
        registerItemType(CartSectionType.TYPE_EMPTY, R.layout.item_cart_empty_section);
        mLayoutResId = R.layout.item_cart_blank;
    }

    @Override
    protected void addAdapterProvider() {
        super.addAdapterProvider();
        setLoadMoreView(new CartLoadingMoreView());
        provider = new CartProductItemProvider();
        addItemProvider(CartSectionType.TYPE_PRODUCT, provider);
        addItemProvider(CartSectionType.TYPE_PRODUCT_GIFT, provider);
        addItemProvider(CartSectionType.TYPE_PRODUCT_ACTIVITY, provider);
        addItemProvider(CartSectionType.TYPE_PRODUCT_SAVE_4_LATER, provider);
        addItemProvider(CmsItemType.BLANK, new BlankProvider());

        provider.setProductItemCallback(new CartProductItemProvider.OnProductItemCallback() {

            @Override
            public void notifyProviderDataSetChanged() {
                notifyPacketDataSetChanged();
            }

            public void notifyProviderSaveForLaterDataRemoved(SectionCartProductData item) {
                int targetIndex = mData.indexOf(item);
                if (targetIndex != -1) {
                    //找到对应的title
                    int titleIndex = -1;
                    SectionCartTitleData targetTitle = null;
                    int itemCount = getItemCount();
                    for (int i = 0; i < itemCount; i++) {
                        AdapterDataType temp = getItem(i);
                        if (temp instanceof SectionCartTitleData && ((SectionCartTitleData) temp).isSave4Later) {
                            titleIndex = i;
                            targetTitle = (SectionCartTitleData) temp;
                            break;
                        }
                    }
                    if (targetTitle != null) {
                        //手动修改当前商品数目
                        targetTitle.productCount -= 1;
                        if (save4laterCartData != null && save4laterCartData.titleData != null) {
                            save4laterCartData.titleData.productCount = targetTitle.productCount;
                        }
                        AdapterDataType nextData = getItem(targetIndex + 1);
                        //下一个不是save for later商品数据，即其为最后一个数据
                        if (nextData instanceof SectionCartProductData) {
                            notifyItemChanged(titleIndex);
                            remove(targetIndex);
                        } else {
                            int preIndex = targetIndex - 1;
                            AdapterDataType preData = getItem(preIndex);
                            if (preData instanceof SectionCartTitleData) {
                                //上个数据是标题，即表示只有一个save for later数据
                                if (nextData instanceof SectionCartSave4LaterMoreData) {
                                    notifyItemChanged(titleIndex);
                                    remove(targetIndex);
                                } else {
                                    mData.remove(preData);
                                    mData.remove(item);
                                    notifyItemRangeChanged(preIndex, 2);
                                }
                            } else if (preData instanceof SectionCartProductData) {
                                //其为最后一个商品，修改上个商品的边框效果
                                notifyItemChanged(titleIndex);
                                remove(targetIndex);
                                if (!(nextData instanceof SectionCartSave4LaterMoreData)) {
                                    CartFrameUiData uiData = ((SectionCartProductData) preData).getFrameUiData();
                                    uiData.outerFrame = SectionCartAdapter.BOTTOM_FRAME;
                                    notifyItemChanged(preIndex);
                                }
                            }
                        }
                    }
                }
            }
        });

        addItemProvider(CartSectionType.TYPE_STATS, new CartStatsProvider());
        addItemProvider(CartSectionType.TYPE_MKPL_SHOP_MORE, new CartMkplShopMoreProvider());
        addItemProvider(CartSectionType.TYPE_TIPS_FREE_SHIPPING, new CartFreeShippingProvider());
        addItemProvider(CartSectionType.TYPE_TOP_MESSAGE, new CartTopMessageProvider());
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder.getItemViewType() == CartSectionType.CART_SECTION_PANEL_PRODUCT) {
            setProductLayoutParamsMargin(holder, interval, interval / 2);
        } else if (holder.getItemViewType() == CmsItemType.BLANK) {
            setLayoutParamsMargin(holder, 0, 0, 0, true);
        } else {
            setLayoutParamsMargin(holder, interval, interval, 0, true);
        }
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, AdapterDataType item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        for (Object object : payloads) {
            if (object instanceof SectionCartDealData) {
                //骨架屏
                VeilTools.show(helper.getView(R.id.vl_items), false);
                //RecyclerView
                SectionCartDealData sectionCartDealData = (SectionCartDealData) object;
                boolean isNotEmpty = CollectionUtils.isNotEmpty(sectionCartDealData.getData());
                helper.setVisibleCompat(R.id.rv_list, isNotEmpty);
                if (isNotEmpty) {
                    RecommendItemAdapter adapter;
                    RecyclerView rv = helper.getView(R.id.rv_list);
                    RecyclerView.Adapter<?> rvAdapter = rv.getAdapter();
                    if (rvAdapter instanceof RecommendItemAdapter) {
                        adapter = (RecommendItemAdapter) rvAdapter;
                        adapter.submitData(sectionCartDealData.getData());
                    }
                } else {
                    helper.setVisibleCompat(false, R.id.vl_items, R.id.divider);
                }
            } else if (ProductTraceViewHelper.shouldConvertPayload(object) && item instanceof ProductItemData) {
                ProductTraceViewHelper.convert(helper.itemView, (ProductItemData) item);
            }
        }
    }

    @Override
    protected void convertNormal(@NonNull AdapterViewHolder helper, AdapterDataType item) {
        switch (item.getType()) {
            case CartSectionType.TYPE_ADD_ON: //add on
                convertAddOn(helper, (CartAddOnDetailData) item);
                break;
            case CartSectionType.TYPE_GROUP_ORDER: //seller detail
                convertGroupOrder(helper, (CartSellerDetailData) item);
                break;
            case CartSectionType.TYPE_TITLE:
                convertTitle(helper, (SectionCartTitleData) item);
                break;
            case CartSectionType.TYPE_REMIND:
                convertRemind(helper, (SectionCartRemindData) item);
                break;
            case CartSectionType.TYPE_TIPS:
                convertTips(helper, (SectionCartTipsData) item);
                break;
            case CartSectionType.TYPE_TIPS_CART_DEAL_TRADE_IN:
                convertTipsCartDeal(helper, (SectionCartDealData) item);
                break;
            case CartSectionType.TYPE_TIPS_TOP_PROMOTION:
                convertTipsTopPromotion(helper, (SectionCartTopPromotionData) item);
                break;
            case CartSectionType.TYPE_ACTIVITY_FOOTER:
                convertActivityFooter(helper, (SectionCartActivityFooterData) item);
                break;
            case CartSectionType.TYPE_PRODUCT_COLLAPSED:
                convertCollapsedProducts2(helper, (SectionCartCollapsedData) item);
                break;
            case CartSectionType.TYPE_SAVE_FOR_LATER_LOAD_MORE:
                convertSaveForLaterLoadMore(helper, (SectionCartSave4LaterMoreData) item);
                break;
            case CartSectionType.CART_SECTION_PANEL_TITLE: //推荐商品标题
                convertProductTitle(helper, (AdapterPanelTitleData) item);
                break;
            case CartSectionType.CART_SECTION_PANEL_PRODUCT: //推荐商品
                convertProduct(helper, (ProductItemData) item);
                break;
            case CartSectionType.TYPE_CHECKOUT_BUTTON: // 独立结算按钮
                convertCheckoutButton(helper, (SectionCartCheckoutButtonData) item);
                break;
            case CartSectionType.TYPE_EMPTY:
                helper.setText(R.id.btn_to_shopping, R.string.s_start_shopping_new);
                helper.addOnClickListener(R.id.btn_to_shopping);
                break;
            default:
                break;
        }
    }

    private void convertAddOn(AdapterViewHolder helper, CartAddOnDetailData item) {
        AddOnDetailBean bean = item.t;
        helper.setOnViewClickListener(R.id.layout_add_on, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_pos(0)
                        .setTargetNm("add_on")
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .build().getParams());
                toWeb(bean.addon_url);
            }
        });
        helper.setTextHtml(R.id.tv_content_top, bean.delivery_date_content_cart);
        helper.setText(R.id.tv_content_bottom, bean.expire_content);
    }

    private void convertGroupOrder(AdapterViewHolder helper, CartSellerDetailData item) {
        if (!item.isValid()) {
            return;
        }
        GroupBuySellerDetailBean bean = item.t;
        helper.setOnViewClickListener(R.id.layout_group_order, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Map<String, Object> ctx = new ArrayMap<>();
                ctx.put("global_vendor", bean.vendor_id);
                ctx.put("count_users", bean.count_users);
                ctx.put("count_items", bean.count_items);
                ctx.put("is_creator", bean.is_creator);
                ctx.put("group_order_id", bean.key);
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_pos(0)
                        .setTargetNm(EagleTrackEvent.TargetNm.SELLER_GROUP_ORDER)
                        .setTargetType(EagleTrackEvent.TargetType.BANNER_LINE)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .addCtx(ctx)
                        .build().getParams());
                toWeb(bean.group_buy_url_host);
            }
        });
        helper.setText(R.id.tv_group_order, String.format(mContext.getString(R.string.seller_group_order_name), bean.user_name));
        String items = String.format(mContext.getString(R.string.seller_group_order_items), bean.count_items + "");
        String people = String.format(mContext.getString(R.string.seller_group_order_people), bean.count_users + "");
        String dot = " • ";
        String s = (!EmptyUtils.isEmpty(bean.vendor_name) ? (bean.vendor_name + dot) : "") + items + dot + people;
        helper.setText(R.id.tv_info_group_order, s);
        String tips = bean.group_buy_tip;
        helper.setText(R.id.tv_group_order_tip, tips);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.fl_group_order_tip), !EmptyUtils.isEmpty(tips));
    }

    private void convertTitle(AdapterViewHolder helper, SectionCartTitleData item) {
        // CartSectionType.TYPE_TITLE
        // R.layout.item_cart_section_title_new
        if (item.isOnlyTitle()) {
            helper.setVisibleCompat(R.id.iv_icon, false);
            helper.setVisibleCompat(R.id.tv_sub_title, false);
            int qty = item.productCount;
            if (save4laterCartData != null && save4laterCartData.titleData != null) {
                qty = save4laterCartData.titleData.productCount;
            }
            helper.setText(R.id.tv_title, mContext.getString(item.titleRes, qty));
        } else {
            NewSectionBean.ShippingInfo shippingInfo = item.t;
            helper.setVisibleCompat(R.id.iv_icon, true);
            helper.setVisibleCompat(R.id.tv_sub_title, true);
            helper.loadImage(mContext, R.id.iv_icon, WebpManager.convert(ImageSpec.SPEC_32, shippingInfo.shipping_icon_url));
            helper.setText(R.id.tv_title, shippingInfo.shipping_type_desc);
            helper.setText(R.id.tv_sub_title, shippingInfo.shipping_desc);
            helper.addOnClickListener(R.id.layout_section_title);
        }
        if (item.isCollapsedStatus) {
            helper.setImageResource(R.id.iv_arrow, R.mipmap.ic_arrow_down_black);
            TalkBackHelper.setContentDescId(helper.getView(R.id.btn_arrow), R.string.a_expend_cart);
        } else {
            helper.setImageResource(R.id.iv_arrow, R.mipmap.ic_arrow_up_black);
            TalkBackHelper.setContentDescId(helper.getView(R.id.btn_arrow), R.string.a_collapse_cart);
        }
        helper.addOnClickListener(R.id.btn_arrow);
        helper.setVisibleCompat(R.id.iv_terms_title, item.isSeller || item.isPantry);
        helper.addOnClickListener(R.id.iv_terms_title);
    }

    private void convertRemind(AdapterViewHolder helper, SectionCartRemindData item) {
        // CartSectionType.TYPE_REMIND
        // R.layout.item_cart_section_remind
        boolean isEmpty = EmptyUtils.isEmpty(item.remindText);
        helper.setVisibleCompat(R.id.iv_terms_remind, isEmpty);
        ShapeConstraintLayout view = helper.getView(R.id.layout_section_remind);
        view.setBackgroundSolidDrawable(ContextCompat.getColor(mContext
                , isEmpty ? R.color.color_warning_surface_1_bg_idle : R.color.color_highlight_surface_1_bg_idle), CommonTools.dp2px(20));
        helper.setTextColor(R.id.tv_remind, ContextCompat.getColor(mContext
                , isEmpty ? R.color.color_warning_surface_1_fg_default_idle : R.color.color_highlight_surface_1_fg_default_idle));
        helper.setText(R.id.tv_remind, !isEmpty ? item.remindText : mContext.getString(R.string.s_cart_not_available));
    }

    private void convertTips(AdapterViewHolder helper, SectionCartTipsData item) {
        // CartSectionType.TYPE_TIPS
        // R.layout.item_cart_section_tips_new
        int padding = CommonTools.dp2px(12);
        View rootView = helper.getView(R.id.layout_item);
        if (item.getActivityIndex() == 0) {
            ViewTools.updatePaddings(rootView, padding, 0, padding, padding);
        } else {
            ViewTools.updatePaddings(rootView, padding, padding, padding, padding);
        }
        helper.getView(R.id.layout_tips).setBackgroundResource(R.drawable.bg_cart_item_inner);
        if (item.isActivityTips()) {
            if (item.uiData.innerFrame == INNER_TOP_FRAME) {
                ViewTools.updatePaddings(rootView, padding, CommonTools.dp2px(5), padding, 0);
                helper.getView(R.id.layout_tips).setBackgroundResource(R.drawable.bg_cart_item_top_inner);
            }
            NewSectionBean.ActivityInfo activityInfo = item.activityInfo;

            boolean displayTag = activityInfo.tag != null && !activityInfo.tag.trim().isEmpty();
            helper.setVisibleCompat(R.id.tv_tag, displayTag);
            if (displayTag) {
                helper.setText(R.id.tv_tag, activityInfo.tag);
                helper.setTextColor(R.id.tv_tag, ContextCompat.getColor(mContext, R.color.color_pricing_surface_1_fg_default_idle)); //
                helper.setBackground(R.id.tv_tag, ShapeHelper.buildSolidDrawable(ViewTools.parseColor(mContext, activityInfo.background_color, R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)));
            }
            TextView tvShippingTips = helper.getView(R.id.tv_content);
            ViewTools.setViewHtml(tvShippingTips, activityInfo.offer_content);
            TextView tvSubContent = helper.getView(R.id.tv_sub_content);
            ViewTools.setViewHtml(tvSubContent, activityInfo.content);
            helper.setVisibleCompat(R.id.tv_sub_content, activityInfo.content != null && !activityInfo.content.isEmpty());
            helper.setVisibleCompat(R.id.tv_action, !EmptyUtils.isEmpty(activityInfo.url));
            helper.setText(R.id.tv_action, activityInfo.desc);
            helper.setTextColor(R.id.tv_action, ContextCompat.getColor(mContext, R.color.color_interactive_standalone_idle));

            helper.setOnViewClickListener(R.id.layout_tips, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                            .addElement(item.element)
                            .setTargetNm("shop_more")
                            .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                            .setClickType(EagleTrackEvent.ClickType.VIEW)
                            .build().getParams());
                    toWeb(activityInfo.url);
                }
            });
        } else {
            NewSectionBean.ShopMoreInfo shopMoreInfo = item.shopMoreInfo;

            boolean displayTag = shopMoreInfo.tag != null;
            helper.setVisibleCompat(R.id.tv_tag, displayTag);
            if (displayTag) {
                helper.setText(R.id.tv_tag, shopMoreInfo.tag.tag_text);
                helper.setTextColor(R.id.tv_tag, ViewTools.parseColor(shopMoreInfo.tag.tag_type_text_color, Color.WHITE));
                helper.setBackground(R.id.tv_tag, ShapeHelper.buildSolidDrawable(ViewTools.parseColor(mContext, shopMoreInfo.tag.tag_type_bg_color, R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)));
            }

            TextView tvShippingTips = helper.getView(R.id.tv_content);
            ViewTools.setViewHtml(tvShippingTips, shopMoreInfo.content);
            helper.setVisibleCompat(R.id.tv_sub_content, false);

            boolean displayDesc = shopMoreInfo.desc != null;
            helper.setVisibleCompat(R.id.tv_action, displayDesc);
            if (displayDesc) {
                helper.setText(R.id.tv_action, shopMoreInfo.desc.tag_text);
                helper.setTextColor(R.id.tv_action, ViewTools.parseColor(shopMoreInfo.desc.tag_type_text_color, Color.BLACK));
            }
            helper.setOnViewClickListener(R.id.layout_tips, new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    toWeb(shopMoreInfo.url);
                }
            });
        }
    }

    private void convertTipsCartDeal(AdapterViewHolder helper, SectionCartDealData item) {
        int padding = CommonTools.dp2px(12);
        helper.getView(R.id.layout_item).setPadding(padding, 0, padding, padding);
        if (item.uiData.innerFrame == INNER_TOP_FRAME) {
            helper.getView(R.id.layout_item).setPadding(padding, CommonTools.dp2px(5), padding, 0);
        }
        NewSectionBean.ActivityInfo activityInfo = item.activityInfo;

        boolean displayTag = activityInfo.tag != null && activityInfo.tag.trim().length() > 0;
        helper.setVisibleCompat(R.id.tv_tag, displayTag);
        if (displayTag) {
            helper.setText(R.id.tv_tag, activityInfo.tag);
            helper.setTextColor(R.id.tv_tag, ContextCompat.getColor(mContext, R.color.color_pricing_surface_1_fg_default_idle)); //
            helper.setBackground(R.id.tv_tag, ShapeHelper.buildSolidDrawable(ViewTools.parseColor(mContext, activityInfo.background_color, R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)));
        }
        TextView tvShippingTips = helper.getView(R.id.tv_content);
        ViewTools.setViewHtml(tvShippingTips, activityInfo.offer_content);
        TextView tvSubContent = helper.getView(R.id.tv_sub_content);
        ViewTools.setViewHtml(tvSubContent, activityInfo.content);
        helper.setVisibleCompat(R.id.tv_sub_content, activityInfo.content != null && activityInfo.content.length() > 0);
        boolean displayDesc = activityInfo.desc != null && activityInfo.desc.length() > 0;
        helper.setVisibleCompat(R.id.tv_action, displayDesc);
        if (displayDesc) {
            helper.setText(R.id.tv_action, activityInfo.desc);
        }
        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .addElement(item.element)
                        .setTargetNm("shop_more")
                        .setTargetPos(0)
                        .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .addCtx(item.ctx)
                        .build().getParams());
                toWeb(activityInfo.url);
            }
        }, R.id.layout_tips);
        //差额气泡提示
        //推荐商品
        //推荐商品RecyclerView
        boolean hasNextUrl = item.hasNextUrl();
        helper.setBackgroundRes(R.id.layout_tips, hasNextUrl ? R.drawable.bg_cart_item_inner_cart_deal : R.drawable.bg_cart_item_inner);
        helper.setVisibleCompat(hasNextUrl, R.id.rv_list, R.id.divider, R.id.vl_items);
        if (!hasNextUrl) {
            return;
        }
        RecyclerView rv = helper.getView(R.id.rv_list);
        RecommendItemAdapter adapter;
        RecyclerView.Adapter<?> rvAdapter = rv.getAdapter();
        if (rvAdapter instanceof RecommendItemAdapter) {
            adapter = (RecommendItemAdapter) rvAdapter;
            if (cartDealDataNew != null && cartDealDataNew.getCartId().equalsIgnoreCase(item.getCartId())) {
                adapter.submitData(cartDealDataNew.getData());
            }
        } else {
            rv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
            adapter = new RecommendItemAdapter();
            childAdapterCaches.add(new SoftReference<>(adapter));
            rv.setAdapter(adapter);
            adapter.setAttachView(rv);
            if (cartDealDataNew != null && cartDealDataNew.getCartId().equalsIgnoreCase(item.getCartId())) {
                adapter.submitData(cartDealDataNew.getData());
            }
        }
        adapter.setUrl(activityInfo.url);
        rv.clearOnScrollListeners();
        RecommendItemAdapter finalAdapter = adapter;
        rv.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onPageScrollStateChangedImpression(finalAdapter);
                }
                OpActionHelper.notifyScrollStateChanged(newState, oldState);
            }
        });
        adapter.setOnSectionCartEditListener(listener);
        //推荐商品RecyclerView骨架屏
        boolean showVl = !EmptyUtils.isEmpty(item.activityInfo.recommend.next_url) && EmptyUtils.isEmpty(adapter.getData());
        VeilTools.show(helper.getView(R.id.vl_items), showVl);
    }

    private void convertTipsTopPromotion(AdapterViewHolder helper, SectionCartTopPromotionData item) {
        View layoutItem = helper.getView(R.id.layout_item);
        layoutItem.setPadding(0, CommonTools.dp2px(item.position == 0 ? 20 : 12), 0, 0);
        NewSectionBean.ActivityInfo activityInfo = item.activityInfo;
        boolean displayTag = activityInfo.tag != null && activityInfo.tag.trim().length() > 0;
        helper.setVisibleCompat(R.id.tv_tag, displayTag);
        if (displayTag) {
            helper.setText(R.id.tv_tag, activityInfo.tag);
            helper.setTextColor(R.id.tv_tag, ContextCompat.getColor(mContext, R.color.color_pricing_surface_1_fg_default_idle)); //
            helper.setBackground(R.id.tv_tag, ShapeHelper.buildSolidDrawable(ViewTools.parseColor(mContext, activityInfo.background_color, R.color.color_pricing_surface_1_bg_idle), CommonTools.dp2px(12)));
        }
        TextView tvShippingTips = helper.getView(R.id.tv_content);
        ViewTools.setViewHtml(tvShippingTips, activityInfo.offer_content);
        TextView tvSubContent = helper.getView(R.id.tv_sub_content);
        ViewTools.setViewHtml(tvSubContent, activityInfo.content);
        helper.setVisibleCompat(R.id.tv_sub_content, activityInfo.content != null && activityInfo.content.length() > 0);
        boolean displayDesc = activityInfo.desc != null && activityInfo.desc.length() > 0;
        helper.setVisibleCompat(R.id.tv_action, displayDesc);
        if (displayDesc) {
            helper.setText(R.id.tv_action, activityInfo.desc);
        }
        helper.setOnViewClickListener(R.id.layout_tips, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                toWeb(activityInfo.url);
            }
        });
        //items
        helper.setVisibleCompat(false, R.id.layout_product, R.id.rv_list);
        if (activityInfo.hasItems()) {
            if (activityInfo.items.size() == 1) {
                helper.setVisibleCompat(true, R.id.layout_product);
                //单个商品
                NewItemBean bean = activityInfo.items.get(0);
                ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.img), R.mipmap.iv_product_placeholder);
                //title & tag
                Spanny nameBuilder = new Spanny();
                if (!EmptyUtils.isEmpty(bean.tag_infos)) {
                    for (TagInfo tagInfo : bean.tag_infos) {
                        if ("alcohol".equalsIgnoreCase(tagInfo.tag_type)) {

                        } else if ("cold_pack".equalsIgnoreCase(tagInfo.tag_type)) {

                        } else {
                            SpannyUtils.buildStartTagWithSm(mContext, nameBuilder
                                    , tagInfo.tag_text
                                    , ViewTools.parseColor(tagInfo.tag_type_text_color, Color.WHITE)
                                    , ShapeHelper.buildSolidDrawable(ViewTools.parseColor(mContext,
                                                    tagInfo.tag_type_bg_color, R.color.color_pricing_surface_1_bg_idle),
                                            CommonTools.dp2px(12)), 9, 0, 6);
                        }
                    }
                }
                if (bean.is_colding_package) {
                    LabelHelper.buildColdPackTag(mContext, nameBuilder, false);
                }
                nameBuilder.append(bean.title, new TextAppearanceSpan(mContext, R.style.style_fluid_root_utility_xs_subdued));
                helper.setText(R.id.tv_name, nameBuilder);
                //price
                helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(bean.price));
                if (bean.base_price > 0) { //大于0显示划线价格
                    helper.setVisible(R.id.tv_price_delete, true);
                    helper.setText(R.id.tv_price_delete, new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()));
                }
                //pdp
                RecommendItemData itemData = new RecommendItemData(bean);
                helper.setOnViewClickListener(R.id.layout_product, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        mContext.startActivity(ProductDetailActivity.getIntent(mContext, new ProductPageParams()
                                .setProduct(OrderHelper.toProductBeanV5(bean, itemData.convertSoldStatus()))));
                    }
                });
                //qty
                helper.setVisibleCompat(false, R.id.tv_qty, R.id.iv_lock, R.id.layout_op);
                helper.setVisible(R.id.tv_qty, true);
                helper.setText(R.id.tv_qty, String.valueOf(bean.quantity));
            } else {
                helper.setVisibleCompat(true, R.id.rv_list);
                RecyclerView rv = helper.getView(R.id.rv_list);
                RecommendItemAdapter adapter;
                RecyclerView.Adapter rvAdapter = rv.getAdapter();
                if (rvAdapter instanceof RecommendItemAdapter) {
                    adapter = (RecommendItemAdapter) rvAdapter;
                    adapter.changeRecommendItems(item.getData());
                } else {
                    rv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
                    adapter = new RecommendItemAdapter();
                    //childAdapterCaches.add(new SoftReference<>(adapter));
                    rv.setAdapter(adapter);
                    adapter.setAttachView(rv);
                    adapter.setNewData(item.getData());
                }
                rv.clearOnScrollListeners();
            }
        }
    }


    private void toWeb(String url) {
        if (url != null) {
            mContext.startActivity(WebViewActivity.getIntent(mContext, url));
        }
    }

    private void convertActivityFooter(AdapterViewHolder helper, SectionCartActivityFooterData item) {
        helper.setText(R.id.tv_save_amount, OrderHelper.formatUSMoney(item.saveAmount));
        helper.getView(R.id.layout_footer).setBackground(ResourcesCompat.getDrawable(mContext.getResources(),
                item.uiData.outerFrame == BOTTOM_FRAME ? R.drawable.bg_cart_item_bottom : R.drawable.bg_cart_item_middle, null));
    }

    @Deprecated
    private void convertCollapsedProducts(AdapterViewHolder helper, SectionCartCollapsedData item) {
        helper.addOnClickListener(R.id.layout_cart_items);
        List<SectionCartProductData> productData = item.productData;
        LinearLayout layoutCartItems = helper.getView(R.id.layout_cart_items);
        int windowWidth = CommonTools.getCurrentWindowWidth(mContext);
        int canShowSize = (windowWidth - CommonTools.dp2px(44)) / CommonTools.dp2px(58);
        List<SectionCartProductData> cartItems4Show = new ArrayList<>();
        if (productData.size() <= canShowSize) {
            cartItems4Show.addAll(productData);
        } else {
            cartItems4Show.addAll(productData.subList(0, canShowSize - 1));
            cartItems4Show.add(new SectionCartProductData(0, null));//eg: +99
        }
        layoutCartItems.removeAllViews();
        for (SectionCartProductData itemBean : cartItems4Show) {
            layoutCartItems.addView(ViewTools.getHelperView(layoutCartItems, R.layout.item_cart_selection_items, new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    if (itemBean.getData() != null) {
                        ImageView iv = helper.getView(R.id.iv_icon);
                        NewItemBean data = itemBean.getData();
                        ImageLoader.load(mContext, iv, WebpManager.convert(ImageSpec.SPEC_PRODUCT, data.img), R.mipmap.iv_product_placeholder);
                        helper.setVisible(R.id.tv_item_qty, data.quantity > 1 && !item.isSave4Later);
                        helper.setText(R.id.tv_item_qty, "x" + data.quantity);
                        boolean isInvalidProduct;
                        if (!item.isSave4Later) {
                            isInvalidProduct = !OrderProvider.get().isValidProduct(data.status);
                        } else {
                            isInvalidProduct = OrderProvider.STATUS_PRODUCT_UNAVAILABLE.equals(data.status);
                        }
                        helper.setVisible(R.id.v_translucent, isInvalidProduct && !data.is_gift);
                    } else {
                        helper.setBackgroundDrawable(R.id.iv_icon, ShapeHelper.buildSolidDrawable(ContextCompat.getColor(mContext, R.color.color_surface_2_bg_idle), CommonTools.dp2px(8)));
                        helper.setVisible(R.id.tv_item_qty, false);
                        helper.setVisible(R.id.tv_items_count, true);
                        helper.setText(R.id.tv_items_count, "+" + (productData.size() - (cartItems4Show.size() - 1)));
                    }
                }
            }));
        }
    }

    // Buy a get b, add activtiy items to collapsed item line
    private void convertCollapsedProducts2(AdapterViewHolder helper, SectionCartCollapsedData item) {
        // CartSectionType.TYPE_PRODUCT_COLLAPSED
        // R.layout.item_cart_section_products_pack
        if (item.getFrameUiData().outerFrame == SectionCartAdapter.BOTTOM_FRAME) {
            helper.itemView.setBackgroundResource(R.drawable.bg_cart_item_bottom);
        } else {
            helper.itemView.setBackgroundResource(R.drawable.bg_cart_item_middle);
        }
        helper.addOnClickListener(R.id.layout_cart_items);
        LinearLayout layoutCartItems = helper.getView(R.id.layout_cart_items);
        List<SectionCartProductData> productData = item.productData;
        if (EmptyUtils.isEmpty(item.productData)) {
            ViewTools.removeAllViews(layoutCartItems);
            return;
        }

        int windowWidth = CommonTools.getCurrentWindowWidth(mContext);
        int canShowSize = (windowWidth - CommonTools.dp2px(54)) / CommonTools.dp2px(58);

        List<NewItemBean> allItems4Show = new ArrayList<>();
        for (SectionCartProductData data : productData) {
            NewItemBean it = data.t;
            allItems4Show.add(it);
            allItems4Show.addAll(it.getActivityItems());
        }

        List<NewItemBean> cartItems4Show;
        if (allItems4Show.size() <= canShowSize) {
            cartItems4Show = new ArrayList<>(allItems4Show);
        } else {
            cartItems4Show = new ArrayList<>(allItems4Show.subList(0, canShowSize - 1));
        }

        ViewTools.removeAllViews(layoutCartItems);
        for (NewItemBean itemBean : cartItems4Show) {
            OnViewHelper viewHelper = new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    ImageView iv = helper.getView(R.id.iv_icon);
                    ImageLoader.load(mContext, iv, WebpManager.convert(ImageSpec.SPEC_PRODUCT, itemBean.img), R.mipmap.iv_product_placeholder);
                    helper.setVisible(R.id.tv_item_qty, itemBean.quantity > 1 && !item.isSave4Later);
                    helper.setText(R.id.tv_item_qty, "x" + itemBean.quantity);
                    boolean isInvalidProduct;
                    if (!item.isSave4Later) {
                        isInvalidProduct = !OrderProvider.get().isValidProduct(itemBean.status);
                    } else {
                        isInvalidProduct = OrderProvider.STATUS_PRODUCT_UNAVAILABLE.equals(itemBean.status);
                    }
                    helper.setVisible(R.id.v_translucent, isInvalidProduct && !itemBean.is_gift);
                }
            };
            View childView = ViewTools.getHelperView(layoutCartItems, R.layout.item_cart_selection_items, viewHelper);
            layoutCartItems.addView(childView);
        }

        // more button
        if (allItems4Show.size() > canShowSize) {
            OnViewHelper viewHelper = new OnViewHelper() {
                @Override
                public void help(ViewHelper helper) {
                    helper.setBackgroundDrawable(R.id.iv_icon, ShapeHelper.buildSolidDrawable(ContextCompat.getColor(mContext, R.color.color_surface_2_bg_idle), CommonTools.dp2px(8)));
                    helper.setVisible(R.id.tv_item_qty, false);
                    helper.setVisible(R.id.tv_items_count, true);
                    helper.setText(R.id.tv_items_count, "+" + (allItems4Show.size() - cartItems4Show.size()));
                }
            };
            View childView = ViewTools.getHelperView(layoutCartItems, R.layout.item_cart_selection_items, viewHelper);
            layoutCartItems.addView(childView);
        }
    }

    private void convertSaveForLaterLoadMore(AdapterViewHolder helper, SectionCartSave4LaterMoreData item) {
        helper.setOnViewClickListener(R.id.layout_load_more, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (save4LaterListener != null) {
                    save4LaterListener.loadMore(item);
                }
            }
        });
    }

    protected void setProductLayoutParamsMargin(AdapterViewHolder holder, int margin, int specialMargin) {
        if (holder != null) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof StaggeredGridLayoutManager.LayoutParams) {
                StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) layoutParams;
                int spanIndex = params.getSpanIndex();
                if (spanIndex == 0) {
                    params.leftMargin = margin;
                    params.rightMargin = specialMargin;
                } else {
                    params.leftMargin = specialMargin;
                    params.rightMargin = margin;
                }
                params.topMargin = 0;
                params.bottomMargin = margin;
                params.setFullSpan(false);
            }
        }
    }

    protected void setLayoutParamsMargin(AdapterViewHolder holder, int left, int right, int bottom, boolean fullSpan) {
        if (holder != null) {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (layoutParams instanceof StaggeredGridLayoutManager.LayoutParams) {
                StaggeredGridLayoutManager.LayoutParams params = (StaggeredGridLayoutManager.LayoutParams) layoutParams;
                params.leftMargin = left;
                params.rightMargin = right;
                params.bottomMargin = bottom;
                params.setFullSpan(fullSpan);
            }
        }
    }

    OnSectionCartEditListener listener;

    public void setOnSectionCartEditListener(OnSectionCartEditListener listener) {
        this.listener = listener;
        if (provider != null) {
            provider.setOnSectionCartEditListener(listener);
        }
    }

    OnSectionCartSave4LaterListener save4LaterListener;

    public void setOnSectionCartSave4LaterListener(OnSectionCartSave4LaterListener listener) {
        this.save4LaterListener = listener;
        if (provider != null) {
            provider.setOnSectionCartSave4LaterListener(listener);
        }
    }


    OnIndicatorClickListener indicatorClickListener;

    public void setOnIndicatorClickListener(OnIndicatorClickListener listener) {
        this.indicatorClickListener = listener;
    }


    /**
     * 底部推荐/曾经购买panel。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。
     */
    private final Set<AdapterDataType> adapterPanelSet = new LinkedHashSet<>(); //购物车panel条目数据

    private void convertProductTitle(AdapterViewHolder helper, AdapterPanelTitleData item) {
        // CartSectionType.CART_SECTION_PANEL_TITLE
        // R.layout.item_panel_title_section
        Context context = helper.itemView.getContext();
        List<String> list;
        if (CollectionUtils.isNotEmpty(item.titleResList)) {
            list = CollectionUtils.arrayListOf(context.getString(R.string.s_cart_recommend_for_u), context.getString(R.string.s_cart_bought));
        } else {
            list = CollectionUtils.arrayListOf(context.getString(item.titleRes));
        }
        CompatMagicIndicator indicator = helper.getView(R.id.indicator);
        CartIndicatorHelper.fillAdapterPanelIndicator(
                context,
                indicator,
                interval,
                list,
                ContextCompat.getColor(context, R.color.root_color_white_static),
                ContextCompat.getColor(context, R.color.color_surface_1_fg_default_idle),
                ContextCompat.getColor(context, R.color.text_lesser),
                new OnAdapterChildClickListener() {
                    @Override
                    public void onAdapterChildClick(View view, int i) {
                        if (indicatorClickListener != null) {
                            indicatorClickListener.onClick(i, null);
                        }
                        handleIndicatorSelected(i);
                    }
                }
        );
        indicator.onPageSelected(indexIndicator);
    }

    private void convertProduct(AdapterViewHolder helper, ProductItemData item) {
        ProductBean bean = item.t;
        ProductView productView = helper.getView(R.id.layout_product_view);
        boolean isPreference = Constants.Source.CART_PREFERENCE.equalsIgnoreCase(item.source);
        String secNm = isPreference ? EagleTrackEvent.SecNm.PREFERENCE : EagleTrackEvent.SecNm.BOUGHT_BEFORE;
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST)
                .setMod_pos(2)
                .setSec_nm(secNm)
                .setSec_pos(indexIndicator).build().getElement();
        productView.setCollectClickCallback(new ProductView.OnCollectClickCallback() {
            @Override
            public void onCollectClick() {
                boolean isCollect = CollectManager.get().isProductCollect(bean.id);
                ArrayMap<String, Object> ctx = new ArrayMap<>();
                ctx.put("volume_price_support", bean.volume_price_support);
                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST,
                        2,
                        secNm,
                        indexIndicator,
                        String.valueOf(bean.id),
                        helper.getLayoutPosition(),
                        EagleTrackEvent.TargetType.PRODUCT,
                        isCollect ? EagleTrackEvent.ClickType.SAVE : EagleTrackEvent.ClickType.UNSAVE,
                        ctx,
                        null,
                        bean.isSeller());
            }
        });
        productView.setAttachedProduct(bean, ProductView.STYLE_CARD, new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                //购物车页面 source自己处理
                OpHelper.helperOpByDelegate(layoutOp, item.t, item, Constants.Source.CART_PREFERENCE, new OnSimpleCartEditListener() {
                    @Override
                    public void editProductData(List<AdapterCartData> list, AdapterProductData data, boolean isAdd) {
                        super.editProductData(list, data, isAdd);
                        if (listener != null) {
                            listener.editProductData(item, isAdd);
                        }
                    }
                }, element, null);
            }
        });
        helper.setOnViewClickListener(R.id.layout_product, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                productView.onProductClick(mContext, bean);
                //PDP click
                ArrayMap<String, Object> ctx = new ArrayMap<>();
                ctx.put("volume_price_support", bean.volume_price_support);
                EagleTrackManger.get().trackEagleClickAction(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST,
                        2,
                        secNm,
                        indexIndicator,
                        String.valueOf(bean.id),
                        helper.getLayoutPosition(),
                        EagleTrackEvent.TargetType.PRODUCT,
                        EagleTrackEvent.ClickType.VIEW,
                        ctx,
                        null,
                        bean.isSeller());
            }
        });

        ProductTraceViewHelper.convert(helper.itemView, item);
    }

    private void convertCheckoutButton(AdapterViewHolder helper, SectionCartCheckoutButtonData item) {
        // CartSectionType.TYPE_CHECKOUT_BUTTON: // 独立结算按钮
        // R.layout.item_cart_section_checkout_button
        if (item.getFrameUiData().outerFrame == SectionCartAdapter.BOTTOM_FRAME) {
            helper.itemView.setBackgroundResource(R.drawable.bg_cart_item_bottom);
        } else {
            helper.itemView.setBackground(null);
        }
        helper.addOnClickListener(R.id.btn_checkout_individual);
    }

    public List<AdapterProductData> recommendData = new ArrayList<>();
    public List<AdapterProductData> boughtData = new ArrayList<>();
    public int indexIndicator;
    private boolean isMultiData;

    public void handleIndicatorSelected(int i) {
        indexIndicator = i;
        if (CollectionUtils.isNotEmpty(adapterPanelSet)) {
            mData.removeAll(adapterPanelSet);
            adapterPanelSet.clear();
            adapterPanelSet.addAll(i == 0 ? recommendData : boughtData);
            mData.addAll(adapterPanelSet);
            notifyPacketDataSetChanged();
        }
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                ImpressionBean event = getEagleImpressionEvent(getItem(start));
                if (event != null) {
                    list.add(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    ImpressionBean event = getEagleImpressionEvent(getItem(i));
                    if (event != null) {
                        list.add(event);
                    }
                }
            }
        }
        return list;
    }

    public ImpressionBean getEagleImpressionEvent(AdapterDataType item) {
        int pos = mData.indexOf(item);
        if (item instanceof ICartImpression) {
            return ((ICartImpression) item).getImpressionBean();
        } else if (item instanceof AdapterProductData) {
            ProductBean bean = ((AdapterProductData) item).t;
            String secNm = Constants.Source.CART_PREFERENCE.equalsIgnoreCase(((AdapterProductData) item).source)
                    ? EagleTrackEvent.SecNm.PREFERENCE
                    : EagleTrackEvent.SecNm.BOUGHT_BEFORE;
            Map<String, Object> element = EagleTrackManger.get().getElement(EagleTrackEvent.ModNm.RECOMMEND_ITEM_LIST, 1, secNm, indexIndicator);
            Map<String, Object> params = EagleFactory.getFactory(EagleType.TYPE_CARD).setTarget(bean, pos).setElement(element).get();
            return new ImpressionBean(EagleTrackEvent.EventType.PROD_IMP, params, pos + "_" + bean.id);
        } else if (item instanceof CartSellerDetailData) {
            GroupBuySellerDetailBean groupBuyDetail = ((CartSellerDetailData) item).t;
            Map<String, Object> ctx = new ArrayMap<>();
            ctx.put("global_vendor", groupBuyDetail.vendor_id);
            ctx.put("count_users", groupBuyDetail.count_users);
            ctx.put("count_items", groupBuyDetail.count_items);
            ctx.put("is_creator", groupBuyDetail.is_creator);
            ctx.put("group_order_id", groupBuyDetail.key);
            Map<String, Object> params = new EagleTrackModel.Builder()
                    .setMod_nm(null)
                    .setMod_pos(-1)
                    .setBanner_key(groupBuyDetail.key)
                    .setBanner_pos(pos)
                    .setBanner_type(EagleTrackEvent.TargetNm.SELLER_GROUP_ORDER)
                    .setUrl(groupBuyDetail.group_buy_url_host)
                    .addCtx(ctx)
                    .build().getParams();
            return new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, pos + "_" + groupBuyDetail.key);
        } else if (item instanceof SectionCartFreeShippingData) {
            SectionCartFreeShippingData freeShippingData = (SectionCartFreeShippingData) item;
            NewSectionBean.ActivityInfo activityInfo = freeShippingData.activityInfo;
            if (activityInfo == null) {
                return null;
            }
            boolean enable = activityInfo.diff_amount <= 0; // 免邮
            if (enable) {
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .addElement(freeShippingData.element)
                        .setBanner_key(EagleTrackEvent.BannerKey.FREE_DELIVERY_INFO)
                        .setBanner_pos(0)
                        .setBanner_type(EagleTrackEvent.BannerType.BANNER_LINE)
                        .setUrl(activityInfo.url)
                        .build()
                        .getParams();
                return new ImpressionBean(
                        EagleTrackEvent.EventType.BANNER_IMP,
                        params,
                        pos + "_" + freeShippingData.getActivitySectionIndex() + "_free_delivery_info"
                );
            }
        } else if (item instanceof CartTopMessageData) {
            CartTopMessageData topMessageData = (CartTopMessageData) item;
            if (topMessageData.t.message != null) {
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(EagleTrackEvent.ModNm.REWARDS_ANNCMNT)
                        .setBannerId(null)
                        .setBanner_key(topMessageData.t.message.short_message)
                        .setBanner_pos(0)
                        .setBanner_type(EagleTrackEvent.BannerType.MESSAGE)
                        .setUrl(topMessageData.t.link)
                        .build()
                        .getParams();
                return new ImpressionBean(EagleTrackEvent.EventType.BANNER_IMP, params, pos + "_rewards_anncmnt");
            }
        }
        return null;
    }

    final EagleImpressionTrackerIml tracker = new EagleImpressionTrackerIml();

    public void onPageResumeImpression(BaseQuickAdapter adapter) {
        if (adapter instanceof ImpressionChild) {
            RecyclerView attachView = ((ImpressionChild) adapter).getAttachView();
            if (attachView != null && tracker != null) {
                tracker.onPageResume(attachView);
            }
        }
    }

    public void onPagePauseImpression(BaseQuickAdapter adapter) {
        if (adapter instanceof ImpressionChild) {
            RecyclerView attachView = ((ImpressionChild) adapter).getAttachView();
            if (attachView != null && tracker != null) {
                tracker.onPagePause(attachView);
            }
        }
    }

    public void onPageScrollStateChangedImpression(BaseQuickAdapter adapter) {
        if (adapter instanceof ImpressionChild) {
            RecyclerView attachView = ((ImpressionChild) adapter).getAttachView();
            if (attachView != null && tracker != null) {
                boolean visible = ImpressionHelper.isImpressionReportEnable(attachView);
                if (visible) {
                    tracker.onScrollStateChanged(attachView, RecyclerView.SCROLL_STATE_IDLE);
                }
            }
        }
    }

    public void setOnMkplShopMoreActionListener(CartMkplShopMoreProvider.OnMkplShopMoreActionListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(CartSectionType.TYPE_MKPL_SHOP_MORE);
        if (provider instanceof CartMkplShopMoreProvider) {
            ((CartMkplShopMoreProvider) provider).setOnMkplShopMoreActionListener(listener);
        }
    }

    public void changeSectionCartDealData(SectionCartDealData sectionCartDealData) {
        int count = getItemCount();
        for (int i = 0; i < count; i++) {
            AdapterDataType adapterDataType = getItem(i);
            if (adapterDataType instanceof SectionCartDealData) {
                SectionCartDealData sectionCartDealDataOld = (SectionCartDealData) adapterDataType;
                if (sectionCartDealDataOld.getCartId().equalsIgnoreCase(sectionCartDealData.getCartId())) {
                    cartDealDataNew = sectionCartDealData;
                    notifyItemChanged(i, sectionCartDealData);
                    break;
                }
            }
        }
    }
}
