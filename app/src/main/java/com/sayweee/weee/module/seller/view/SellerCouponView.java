package com.sayweee.weee.module.seller.view;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.content.Context;
import android.content.res.ColorStateList;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.res.ResourcesCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.databinding.ItemSellerCouponBinding;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.SellerTopBean;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.timer.OnSimpleTimerListener;

import java.util.Locale;

public class SellerCouponView extends ConstraintLayout {

    private ItemSellerCouponBinding binding;

    @Nullable
    private SellerTopBean.CouponPromotion data;

    private int targetPosition;

    @Nullable
    private OnSellerCouponViewActionListener listener;

    public SellerCouponView(@NonNull Context context) {
        this(context, null);
    }

    public SellerCouponView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SellerCouponView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(@NonNull Context context) {
        binding = ItemSellerCouponBinding.inflate(LayoutInflater.from(context), this, true);
        binding.tvTimer.setTimeTransformer(millis -> {
            if (millis <= 0) {
                return getResources().getString(R.string.s_mkpl_coupon_expired);
            }
            long days = millis / 1000 / 60 / 60 / 24;
            if (days > 1) {
                return getResources().getString(R.string.s_mkpl_coupon_x_days_left, String.valueOf(days));
            } else if (days == 1) {
                return getResources().getString(R.string.s_mkpl_coupon_1_day_left);
            } else {
                long hours = millis / 1000 / 60 / 60 % 24;
                long minutes = millis / 1000 / 60 % 60;
                long seconds = millis / 1000 % 60;
                return String.format(Locale.ROOT, "%1$02d:%2$02d:%3$02d ", hours, minutes, seconds);
            }
        });
        ViewTools.setViewOnSafeClickListener(binding.getRoot(), v -> {
            if (listener != null) {
                listener.onSellerCouponViewClick(SellerCouponView.this, data, targetPosition);
            }
        });
        ViewTools.setViewOnSafeClickListener(binding.btnClaim, v -> {
            if (listener != null) {
                listener.onSellerCouponViewClaim(SellerCouponView.this, data, targetPosition);
            }
        });
    }

    public void setOnSellerCouponViewActionListener(@Nullable OnSellerCouponViewActionListener listener) {
        this.listener = listener;
    }

    @Nullable
    public SellerTopBean.CouponPromotion getData() {
        return data;
    }

    public void setTargetPosition(int targetPosition) {
        this.targetPosition = targetPosition;
    }

    public void bind(@NonNull SellerTopBean.CouponPromotion coupon, long currentTimestamp, long serverTimestamp) {
        bindCommon(coupon);

        binding.tvTimer.stop();
        boolean isCouponPlan = coupon.isCouponPlan();
        int endTime = coupon.endTime;
        long timeIntervalSeconds = endTime - serverTimestamp;
        boolean isExpired = timeIntervalSeconds <= 0;
        if (isExpired) {
            bindExpired();
        } else {
            bindNormal(isCouponPlan);
            if (!isCouponPlan) {
                bindNormalOnTick(isCouponPlan, timeIntervalSeconds);
            }
        }
        binding.tvTimer.start(timeIntervalSeconds + currentTimestamp);
        binding.tvTimer.setOnTimerListener(new OnSimpleTimerListener() {

            @Override
            public void onTimer(long millis) {
                bindNormalOnTick(isCouponPlan, millis);
            }

            @Override
            public void onEnd() {
                binding.tvTimer.stop();
                bindExpired();
            }
        });
    }

    public void update(@NonNull CouponClaimBean claimBean, int currentTimestamp, int serverTimestamp) {
        SellerTopBean.CouponPromotion data = this.data;
        if (data == null || !data.isCouponPlan()) return;
        int myPlanId = data.id;
        int couponPlanId = claimBean.coupon_plan_id;
        if (couponPlanId != myPlanId) return;
        data.id = claimBean.coupon_id;
        data.type = SellerTopBean.CouponPromotion.TYPE_COUPON;
        data.endTime = claimBean.apply_expiration_time;

        binding.tvTimer.stop();
        int endTime = data.endTime;
        int timeIntervalSeconds = endTime - serverTimestamp;
        boolean isExpired = timeIntervalSeconds <= 0;
        if (isExpired) {
            bindExpired();
        } else {
            binding.tvTimer.start((timeIntervalSeconds + currentTimestamp));
            binding.tvTimer.setOnTimerListener(new OnSimpleTimerListener() {

                @Override
                public void onTimer(long millis) {
                    if (millis < (timeIntervalSeconds * 1000L) - 2000L) { // delay for animation
                        bindNormalOnTick(false, millis);
                    }
                }

                @Override
                public void onEnd() {
                    binding.tvTimer.stop();
                    bindExpired();
                }
            });

            int days = timeIntervalSeconds / 60 / 60 / 24;
            if (days > 30) {
                transitionToClaimed();
            } else {
                transitionToTimer();
            }
        }
    }

    private void transitionToClaimed() {
        PropertyValuesHolder pvhAlphaOut = PropertyValuesHolder.ofFloat("alpha", 1f, 0f);
        PropertyValuesHolder pvhAlphaIn = PropertyValuesHolder.ofFloat("alpha", 0f, 1f);

        final AnimatorSet firstSet = new AnimatorSet();
        firstSet.setDuration(300L);
        firstSet.playTogether(
                ObjectAnimator.ofPropertyValuesHolder(binding.btnClaim, pvhAlphaOut.clone()),
                ObjectAnimator.ofPropertyValuesHolder(binding.btnClaimed, pvhAlphaIn.clone())
        );

        final AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.play(firstSet);
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {
                binding.btnClaim.setAlpha(1);
                binding.btnClaimed.setAlpha(0);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.VISIBLE);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                onFinish();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {
                onFinish();
            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }

            private void onFinish() {
                ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.GONE);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.VISIBLE);
                binding.btnClaim.setAlpha(1);
                binding.btnClaimed.setAlpha(1);
            }
        });
        animatorSet.start();
    }

    private void transitionToTimer() {
        PropertyValuesHolder pvhAlphaOut = PropertyValuesHolder.ofFloat("alpha", 1f, 0f);
        PropertyValuesHolder pvhAlphaIn = PropertyValuesHolder.ofFloat("alpha", 0f, 1f);

        final AnimatorSet firstSet = new AnimatorSet();
        firstSet.setDuration(300L);
        firstSet.playTogether(
                ObjectAnimator.ofPropertyValuesHolder(binding.btnClaim, pvhAlphaOut.clone()),
                ObjectAnimator.ofPropertyValuesHolder(binding.btnClaimed, pvhAlphaIn.clone())
        );

        final AnimatorSet secondSet = new AnimatorSet();
        secondSet.setDuration(300L);
        secondSet.setStartDelay(600L);
        secondSet.playTogether(
                ObjectAnimator.ofPropertyValuesHolder(binding.btnClaimed, pvhAlphaOut.clone()),
                ObjectAnimator.ofPropertyValuesHolder(binding.tvTimer, pvhAlphaIn.clone())
        );

        final AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playSequentially(firstSet, secondSet);
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                binding.btnClaim.setAlpha(1);
                binding.btnClaimed.setAlpha(0);
                binding.tvTimer.setAlpha(0);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.VISIBLE);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.VISIBLE);
                ViewTools.setViewVisibilityIfChanged(binding.tvTimer, View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                onFinish();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                onFinish();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }

            private void onFinish() {
                ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.GONE);
                ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.GONE);
                ViewTools.setViewVisibilityIfChanged(binding.tvTimer, View.VISIBLE);
                binding.btnClaim.setAlpha(1);
                binding.btnClaimed.setAlpha(1);
                binding.tvTimer.setAlpha(1);
            }
        });
        animatorSet.start();
    }

    private void bindCommon(@NonNull SellerTopBean.CouponPromotion coupon) {
        this.data = coupon;
        binding.tvTitle.setText(coupon.promoteTitle);
        binding.tvSubTitle.setText(coupon.subtitle);
        ViewTools.setViewVisibilityIfChanged(
                binding.tvSubTitle,
                !EmptyUtils.isEmpty(coupon.subtitle) ? View.VISIBLE : View.GONE
        );
    }

    private void bindNormal(boolean isCouponPlan) {
        binding.getRoot().setBackgroundResource(R.color.root_color_orange_spectrum_1);
        int iconTintColor = ResourcesCompat.getColor(getResources(), R.color.root_color_orange_spectrum_15, null);
        binding.ivIcon.setImageTintList(ColorStateList.valueOf(iconTintColor));
        ViewTools.applyTextColor(binding.tvTitle, R.color.color_surface_1_fg_default_idle);
        ViewTools.applyTextColor(binding.tvSubTitle, R.color.color_surface_1_fg_minor_idle);
        ViewTools.applyTextColor(binding.tvTimer, R.color.root_color_orange_spectrum_15);
        if (isCouponPlan) {
            ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.VISIBLE);
            ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.GONE);
            ViewTools.setViewVisibilityIfChanged(binding.tvTimer, View.GONE);
        } else {
            ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.GONE);
            ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.GONE);
            ViewTools.setViewVisibilityIfChanged(binding.tvTimer, View.VISIBLE);
        }
    }

    private void bindNormalOnTick(boolean isCouponPlan, long millis) {
        if (isCouponPlan) return;
        if (millis <= 0) return;
        long days = millis / 1000 / 60 / 60 / 24;
        ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.GONE);
        ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, days > 30L ? View.VISIBLE : View.GONE);
        ViewTools.setViewVisibilityIfChanged(binding.tvTimer, days > 30L ? View.GONE : View.VISIBLE);
    }

    private void bindExpired() {
        binding.getRoot().setBackgroundResource(R.color.root_color_black_tint_1);
        int tintColor = ResourcesCompat.getColor(getResources(), R.color.color_surface_1_fg_minor_idle, null);
        ColorStateList textColor = ColorStateList.valueOf(tintColor);
        binding.ivIcon.setImageTintList(textColor);
        binding.tvTitle.setTextColor(textColor);
        binding.tvSubTitle.setTextColor(textColor);
        ViewTools.setViewVisibilityIfChanged(binding.btnClaim, View.GONE);
        ViewTools.setViewVisibilityIfChanged(binding.btnClaimed, View.GONE);
        binding.tvTimer.setText(R.string.s_mkpl_coupon_expired);
        binding.tvTimer.setTextColor(textColor);
        ViewTools.setViewVisibilityIfChanged(binding.tvTimer, View.VISIBLE);
    }

    public interface OnSellerCouponViewActionListener {
        void onSellerCouponViewClick(@NonNull SellerCouponView view, @Nullable SellerTopBean.CouponPromotion coupon, int position);

        void onSellerCouponViewClaim(@NonNull SellerCouponView view, @Nullable SellerTopBean.CouponPromotion coupon, int position);
    }
}
