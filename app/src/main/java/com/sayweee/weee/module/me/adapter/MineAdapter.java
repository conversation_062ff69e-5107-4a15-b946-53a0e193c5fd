package com.sayweee.weee.module.me.adapter;


import static com.sayweee.weee.service.webp.ImageSpec.SPEC_375_AUTO;
import static com.sayweee.weee.service.webp.ImageSpec.SPEC_64;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.SimpleMultiTypeAdapter;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.home.bean.CarouselBean;
import com.sayweee.weee.module.home.bean.CombineFreeShippingBanner;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.module.me.MineViewModel;
import com.sayweee.weee.module.me.bean.AccountSectionData;
import com.sayweee.weee.module.me.bean.ActivitySectionData;
import com.sayweee.weee.module.me.bean.AdapterSectionData;
import com.sayweee.weee.module.me.bean.CommonSectionData;
import com.sayweee.weee.module.me.bean.IconColorSectionProperty;
import com.sayweee.weee.module.me.bean.IconCommonSectionData;
import com.sayweee.weee.module.me.bean.IconImageSectionData;
import com.sayweee.weee.module.me.bean.IconSurveyData;
import com.sayweee.weee.module.me.bean.IconSurveyProperty;
import com.sayweee.weee.module.me.bean.LoyaltyInfo;
import com.sayweee.weee.module.me.bean.MyBannerSectionData;
import com.sayweee.weee.module.me.bean.MyListSectionData;
import com.sayweee.weee.module.me.bean.MyListSectionProperty;
import com.sayweee.weee.module.me.bean.MyPerksSectionProperty;
import com.sayweee.weee.module.me.bean.MyPicksSectionData;
import com.sayweee.weee.module.me.bean.NewAccountSectionProperty;
import com.sayweee.weee.module.me.bean.OrderSectionData;
import com.sayweee.weee.module.me.bean.OrderSectionProperty;
import com.sayweee.weee.module.me.bean.RewardData;
import com.sayweee.weee.module.me.bean.SectionBean;
import com.sayweee.weee.module.me.bean.ShareOrderSectionData;
import com.sayweee.weee.module.me.bean.ShareOrderSectionProperty;
import com.sayweee.weee.module.me.bean.TodayOrderSectionData;
import com.sayweee.weee.module.me.bean.TodayOrderSectionProperty;
import com.sayweee.weee.module.post.profile.ProfileIntentCreator;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.module.web.WebViewFragment;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionAdapter;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.ImpressionChild;
import com.sayweee.weee.service.analytics.ImpressionHelper;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.span.ImgHtmlSpanner;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.weee.widget.CountDownView;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.banner.CarouselBanner;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.weee.widget.ripple.AbsRippleLayout;
import com.sayweee.weee.widget.ripple.RippleLayout;
import com.sayweee.weee.widget.timer.OnSimpleTimerListener;
import com.sayweee.weee.widget.timer.OnTimerListener;
import com.sayweee.widget.round.RoundImageView;
import com.sayweee.widget.shape.ShapeConstraintLayout;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.listener.OnBannerListener;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.SoftReference;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  winds
 * Date:    2021/6/7.
 * Desc:
 */
public class MineAdapter extends SimpleMultiTypeAdapter<AdapterSectionData, AdapterViewHolder> implements EagleImpressionAdapter {

    public static final int TYPE_ACCOUNTS = 10;
    public static final int TYPE_ACCOUNTS_NO_LOGIN_NEW = 12;
    public static final int TYPE_ORDER = 20;
    public static final int TYPE_TODAY_ORDER = 21;
    public static final int TYPE_MY_LISTS = 30;
    public static final int TYPE_MY_BANNERS = 31;
    public static final int TYPE_ACTIVITY = 40;
    public static final int TYPE_MY_PERKS = 50;
    public static final int TYPE_SHARE_ORDER = 60;
    public static final int TYPE_ICON_COMMON = 70;
    public static final int TYPE_ICON_COLOR = 80;
    public static final int TYPE_ICON_SIMPLE = 90;
    public static final int TYPE_BUTTON_SIMPLE = 100;
    public static final int TYPE_BOTTOM_PLACE = 110;
    public static final int TYPE_QUESTIONNAIRE = 120;
    public static final int TYPE_BLANK = 130;
    public static final int TYPE_LINE = 140;
    public static final int TYPE_ICON_IMAGE = 150;
    public static final int TYPE_ICON_SIMPLE_ONLY = 160;

    private EagleImpressionTrackerIml eagleImpressionTracker;//新的impression埋点
    private final List<SoftReference<BaseQuickAdapter>> childAdapterCaches = new ArrayList<>();

    public void setAdapterData(List<AdapterSectionData> list) {
        if (list == null) {
            return;
        }

        mData.clear();
        mData.addAll(list);
        mData.add(new CommonSectionData(TYPE_BOTTOM_PLACE, null));

        if (!EmptyUtils.isEmpty(list)) {
            int index = 0;
            for (AdapterSectionData item : list) {
                if (item.getType() != TYPE_BLANK && item.getType() != TYPE_LINE && item.getType() != TYPE_BOTTOM_PLACE) {
                    if (item instanceof IconCommonSectionData) {
                        List<SectionBean> items = ((IconCommonSectionData) item).p.items;
                        for (SectionBean bean : items) {
                            if (bean != null) {
                                //  index for IconCommonSectionData
                                ((IconCommonSectionData) item).setIndex(index++);
                            }
                        }
                    } else {
                        // index for regular items
                        item.setIndex(index++);
                    }
                }
            }
        }
        notifyDataSetChanged();
    }

    @Override
    protected void registerAdapterType() {
        eagleImpressionTracker = new EagleImpressionTrackerIml();
        registerItemType(TYPE_ACCOUNTS, R.layout.item_mine_accounts_new);
        registerItemType(TYPE_ACCOUNTS_NO_LOGIN_NEW, R.layout.item_mine_accounts_no_login_new);//未登录New
        registerItemType(TYPE_ICON_IMAGE, R.layout.item_mine_icon_image);
        registerItemType(TYPE_ORDER, R.layout.item_mine_order);
        registerItemType(TYPE_TODAY_ORDER, R.layout.item_mine_today_delivery);
        registerItemType(TYPE_MY_LISTS, R.layout.item_mine_my_lists);
        registerItemType(TYPE_MY_BANNERS, R.layout.item_mine_my_banners);         //banners
        registerItemType(TYPE_ACTIVITY, R.layout.item_mine_activity);
        registerItemType(TYPE_MY_PERKS, R.layout.item_mine_my_perks);
        registerItemType(TYPE_SHARE_ORDER, R.layout.item_mine_share_order);
        registerItemType(TYPE_ICON_COMMON, R.layout.item_mine_icon_color);
        registerItemType(TYPE_ICON_COLOR, R.layout.item_mine_icon_color);
        registerItemType(TYPE_ICON_SIMPLE, R.layout.item_mine_icon_simple);
        registerItemType(TYPE_ICON_SIMPLE_ONLY, R.layout.item_mine_icon_simple);
        registerItemType(TYPE_BUTTON_SIMPLE, R.layout.item_mine_button_simple);
        registerItemType(TYPE_BOTTOM_PLACE, R.layout.item_mine_place);
        registerItemType(TYPE_QUESTIONNAIRE, R.layout.item_mine_quesition);
        registerItemType(TYPE_BLANK, R.layout.item_home_blank);
        registerItemType(TYPE_LINE, R.layout.item_home_line);
        mLayoutResId = R.layout.item_mine_place;
    }

    @Override
    protected void convertPayloads(@NonNull AdapterViewHolder helper, AdapterSectionData item, @NonNull List<Object> payloads) {
        super.convertPayloads(helper, item, payloads);
        Object o = CollectionUtils.getOrNull(payloads, 0);
        if (o instanceof LoyaltyInfo) {
            LoyaltyInfo loyaltyInfo = (LoyaltyInfo) o;
            List<LoyaltyInfo.LoyaltyPointsBuyItems> loyaltyPointsBuyItems = loyaltyInfo.loyalty_points_buy_items;
            if (CollectionUtils.isNotEmpty(loyaltyPointsBuyItems)) {
                if (loyaltyPointsBuyItems.size() == 1) {
                    helper.setVisibleCompat(true, R.id.layout_item_loyalty_points_buy);
                    helper.setVisibleCompat(false, R.id.rv_loyalty_points);
                    setSingleLoyaltyPointsBuyItem(helper, loyaltyInfo.points_buy_close_show, loyaltyPointsBuyItems, item.bean.section_name);
                } else {
                    helper.setVisibleCompat(false, R.id.layout_item_loyalty_points_buy);
                    helper.setVisibleCompat(true, R.id.rv_loyalty_points);
                }
                ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_loyalty_points_buy), true);
            } else {
                ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_loyalty_points_buy), false);
            }
        }
    }

    @Override
    protected void convert(@NonNull AdapterViewHolder helper, AdapterSectionData item) {
        switch (helper.getItemViewType()) {
            case TYPE_ACCOUNTS:
                convertNewAccounts(helper, (AccountSectionData) item);
                break;
            case TYPE_ACCOUNTS_NO_LOGIN_NEW:
                convertAccountsNoLoginNew(helper, (AccountSectionData) item);
                break;
            case TYPE_ORDER:
                convertOrder(helper, (OrderSectionData) item);
                break;
            case TYPE_TODAY_ORDER:
                convertTodayOrder(helper, (TodayOrderSectionData) item);
                break;
            case TYPE_MY_LISTS:
                convertMyLists(helper, (MyListSectionData) item);
                break;
            case TYPE_MY_BANNERS:
                convertMyBanners(helper, (MyBannerSectionData) item);
                break;
            case TYPE_ACTIVITY:
                convertActivity(helper, (ActivitySectionData) item);
                break;
            case TYPE_MY_PERKS:
                convertMyPerks(helper, (MyPicksSectionData) item);
                break;
            case TYPE_SHARE_ORDER:
                convertShareOrder(helper, (ShareOrderSectionData) item);
                break;
            case TYPE_ICON_COLOR:
            case TYPE_ICON_SIMPLE:
            case TYPE_ICON_SIMPLE_ONLY:
            case TYPE_ICON_COMMON:
                convertIconColor(helper, (AdapterSectionData) item);
                break;
            case TYPE_BUTTON_SIMPLE:
                convertButtonSimple(helper, (AdapterSectionData) item);
                break;
            case TYPE_QUESTIONNAIRE:
                convertQuestionnaire(helper, (IconSurveyData) item);
                break;
            case TYPE_ICON_IMAGE:
                convertIconImage(helper, (IconImageSectionData) item);
                break;
        }
    }

    @Override
    public void onViewAttachedToWindow(@NonNull AdapterViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (TYPE_MY_BANNERS == holder.getItemViewType()) {
            View view = holder.getView(R.id.cb_main_banner);
            if (view instanceof CarouselBanner) {
                AdapterDataType item = getItem(holder.getLayoutPosition());
                if (item instanceof MyBannerSectionData) {
                    if (((MyBannerSectionData) item).isAutoplay()) {
                        ((CarouselBanner) view).setLoopTime(((MyBannerSectionData) item).getLoopInterval());
                        ((CarouselBanner) view).start();
                    }
                }
            }
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull AdapterViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        if (TYPE_MY_BANNERS == holder.getItemViewType()) {
            View view = holder.getView(R.id.cb_main_banner);
            if (view instanceof CarouselBanner) {
                ((CarouselBanner) view).stop();
            }
        }
    }

    private void convertNewAccounts(AdapterViewHolder helper, AccountSectionData item) {
        // R.layout.item_mine_accounts_new
        convertAccountHeader(helper, item);
        convertLoyaltyProgress(helper, item);
        convertRewards(helper, item);
        convertPointsBuy(helper, item);
    }

    private void convertAccountHeader(AdapterViewHolder helper, AccountSectionData item) {
        // R.layout.layout_mine_accounts_header
        NewAccountSectionProperty p = item.p;
        LoyaltyInfo loyaltyInfo = p.loyalty_info;
        Context context = helper.itemView.getContext();

        //name
        helper.setText(R.id.tv_name, p.alias);

        // account_loyalty_titles
        TextView tvLoyaltyTitle = helper.getView(R.id.tv_loyalty_title);
        TextView tvLoyaltySubtitle = helper.getView(R.id.tv_loyalty_subtitle);
        ImageView ivArrow = helper.getView(R.id.iv_loyalty_arrow);
        if (loyaltyInfo != null && loyaltyInfo.account_loyalty_titles != null) {
            int itemCount = loyaltyInfo.account_loyalty_titles.size();
            int dotColorInt = ViewTools.parseColor(context, loyaltyInfo.account_loyalty_titles.get(0).color, R.color.color_surface_1_fg_minor_idle);
            LoyaltyInfo.TitleBean firstTitle = loyaltyInfo.account_loyalty_titles.get(0);
            tvLoyaltyTitle.setText(firstTitle.title);
            tvLoyaltyTitle.setTextColor(dotColorInt);
            Spanny loyaltySubtitle = new Spanny();
            if (itemCount > 1) {
                for (int i = 1; i < itemCount; i++) {
                    LoyaltyInfo.TitleBean t = loyaltyInfo.account_loyalty_titles.get(i);
                    int textColorInt = ViewTools.parseColor(context, t.color, R.color.color_surface_1_fg_minor_idle);
                    if (i != 1) {
                        loyaltySubtitle.append(" • ", Spans.foregroundColorIntSpan(context, textColorInt));
                    }
                    loyaltySubtitle.append(t.title, Spans.foregroundColorIntSpan(context, textColorInt));
                }
                tvLoyaltySubtitle.setText(loyaltySubtitle);
                ViewTools.setViewVisibilityIfChanged(tvLoyaltySubtitle, true);
            } else {
                ViewTools.setViewVisibilityIfChanged(tvLoyaltySubtitle, false);
            }
            ivArrow.setImageTintList(ColorStateList.valueOf(dotColorInt));
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_loyalty_title), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(tvLoyaltySubtitle, false);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_loyalty_title), false);
        }

        String linkUrl = item.bean.link_url;
        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (!EmptyUtils.isEmpty(linkUrl)) {
                    EagleTrackManger.get().trackEagleClickAction(item.bean.section_name, item.pos, null, -1, "rewards_dash", 0, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
                    toWeb(linkUrl);
                }
            }
        }, R.id.layout_loyalty_title, R.id.tv_loyalty_subtitle);

        //头像
        String loyaltyIconUrl = loyaltyInfo != null ? loyaltyInfo.loyalty_icon : null;
        if (!EmptyUtils.isEmpty(loyaltyIconUrl)) {
            helper.loadImage(mContext, R.id.iv_avatar_background, WebpManager.get().getConvertUrl(ImageSpec.SPEC_AVATAR, loyaltyIconUrl));
            helper.loadImage(mContext, R.id.iv_avatar_badge, WebpManager.get().getConvertUrl(ImageSpec.SPEC_AVATAR, loyaltyIconUrl));
        } else {
            ((ImageView) helper.getView(R.id.iv_avatar_background)).setImageDrawable(null);
            ((ImageView) helper.getView(R.id.iv_avatar_badge)).setImageDrawable(null);
        }
        if (!EmptyUtils.isEmpty(p.head_img_url)) {
            helper.loadImage(mContext, R.id.iv_avatar, WebpManager.get().getConvertUrl(ImageSpec.SPEC_AVATAR, p.head_img_url), R.mipmap.ic_avatar_loyalty_default);
        } else {
            ((ImageView) helper.getView(R.id.iv_avatar_background)).setImageResource(R.mipmap.ic_avatar_loyalty_default);
        }

        LoyaltyInfo.TitleBean accountTitleTip = loyaltyInfo != null ? loyaltyInfo.account_title_tip : null;
        if (accountTitleTip != null && !EmptyUtils.isEmpty(accountTitleTip.title) && accountTitleTip.is_show) {
            TextView tvAccountTitleTip = helper.getView(R.id.tv_account_title_tip);
            tvAccountTitleTip.setText(accountTitleTip.title);
            ViewTools.applyTextColor(tvAccountTitleTip, accountTitleTip.color);
            ShapeHelper.setBackgroundSolidDrawable(tvAccountTitleTip, ViewTools.parseColor(accountTitleTip.bg_color, Color.WHITE), CommonTools.dp2px(2));
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_account_title_tip), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_account_title_tip), false);
        }

        //点击去profile
        helper.setOnViewClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Context ctx = v.getContext();
                if (ctx != null) {
                    Intent a = ProfileIntentCreator.getIntentOnClickMineProfileAvatar(ctx);
                    a.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    ctx.startActivity(a);
                }
            }
        }, R.id.layout_account_header);
    }

    private void convertLoyaltyProgress(AdapterViewHolder helper, AccountSectionData item) {
        // R.layout.layout_mine_accounts_progress
        NewAccountSectionProperty p = item.p;
        LoyaltyInfo loyaltyInfo = p.loyalty_info;
        boolean progressShow = loyaltyInfo != null && loyaltyInfo.progress_show;
        if (!progressShow) {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_mine_accounts_progress), false);
            return;
        }

        // progress colors, bg, fg, numbers
        ShapeHelper.setBackgroundSolidDrawable(helper.getView(R.id.view_level_amount_bg), ViewTools.parseColor(loyaltyInfo.progress_bg_color, Color.WHITE), CommonTools.dp2px(28));
        ShapeHelper.setBackgroundSolidDrawable(helper.getView(R.id.view_current_level_amount), ViewTools.parseColor(loyaltyInfo.progress_color, Color.WHITE), CommonTools.dp2px(28));
        ViewTools.applyTextColor(helper.getView(R.id.level_value2), loyaltyInfo.progress_num_color);
        helper.setText(R.id.level_value2, null);
        ViewTools.applyTextColor(helper.getView(R.id.level_value3), loyaltyInfo.progress_num_color);
        helper.setText(R.id.level_value3, null);
        if (loyaltyInfo.current_level < 3) {
            ViewTools.applyTextColor(helper.getView(R.id.tv_current_level_amount), R.color.root_color_white_static);
        } else {
            ViewTools.applyTextColor(helper.getView(R.id.tv_current_level_amount), R.color.color_surface_1_fg_default_idle);
        }
        int levelTitleMaxWidth = (int) ((CommonTools.getWindowWidth(mContext) - CommonTools.dp2px(20) * 2) / 3f - CommonTools.dp2px(8));
        ViewTools.setMaxWidth(helper.getView(R.id.level_title1), levelTitleMaxWidth);
        ViewTools.setMaxWidth(helper.getView(R.id.level_title2), levelTitleMaxWidth);
        ViewTools.setMaxWidth(helper.getView(R.id.level_title3), levelTitleMaxWidth);

        LoyaltyInfo.LoyaltyRewardItems level3 = CollectionUtils.getOrNull(loyaltyInfo.loyalty_reward_items, 2);
        int maxLevelValue = 0;
        if (level3 != null) {
            maxLevelValue = level3.level_value;
            helper.setText(R.id.level_value3, OrderHelper.formatUSMoney(OrderHelper.formatConciseMoney(level3.level_value)));
        }
        LoyaltyInfo.LoyaltyRewardItems level2 = CollectionUtils.getOrNull(loyaltyInfo.loyalty_reward_items, 1);
        int middleLevelValue = 0;
        if (level2 != null) {
            middleLevelValue = level2.level_value;
            float middleProgress = Math.min((float) DecimalTools.divide(level2.level_value, maxLevelValue), 1f);
            View guideMiddle = helper.getView(R.id.guide_middle);
            ViewGroup.LayoutParams lp = guideMiddle.getLayoutParams();
            if (lp instanceof ConstraintLayout.LayoutParams) {
                ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) lp;
                clp.guidePercent = middleProgress;
                guideMiddle.setLayoutParams(clp);
            }
            helper.setText(R.id.level_value2, OrderHelper.formatUSMoney(OrderHelper.formatConciseMoney(level2.level_value)));
        }
        float currentProgress = Math.min((float) DecimalTools.divide(loyaltyInfo.current_amount, maxLevelValue), 1f);
        float preCurrentProgress = Math.min((float) DecimalTools.divide(loyaltyInfo.pre_current_amount, maxLevelValue), 1f);
        int maxWidth = CommonTools.getWindowWidth(mContext) - CommonTools.dp2px(40);
        View viewCurrentProgress = helper.getView(R.id.view_current_level_amount);
        View viewCurrentProgressDotted = helper.getView(R.id.view_current_level_amount_dotted);
        ViewGroup.LayoutParams lp = viewCurrentProgress.getLayoutParams();
        helper.setVisibleCompat(R.id.view_current_level_amount_dotted, loyaltyInfo.pre_current_amount > 0);
        helper.getView(R.id.layout_current_level_amount_dotted).setPadding(CommonTools.dp2px(20), 0, preCurrentProgress == 1 ? 0 : CommonTools.dp2px(20), 0);
        if (lp instanceof ConstraintLayout.LayoutParams) {
            ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) lp;
            int currentMaxWidth = (int) (currentProgress * maxWidth);
            int currentWidth = currentProgress == 1 ? maxWidth : currentMaxWidth;
            clp.matchConstraintDefaultWidth = currentWidth == 0 ? CommonTools.dp2px(10) : currentWidth;
            int value = (int) (maxWidth - CommonTools.dp2px(20) - (maxWidth * preCurrentProgress));
            int scrollX = preCurrentProgress == 1 ? 0 : value;
            viewCurrentProgressDotted.setTranslationX(-scrollX);
            String sOldValue = AccountManager.get().getCurrentAmount();
            boolean hasAnimator = sOldValue != null && loyaltyInfo.current_amount > DecimalTools.parseInt(sOldValue);
            if (!hasAnimator) {
                helper.setText(R.id.tv_current_level_amount, OrderHelper.formatUSMoney(OrderHelper.formatConciseMoney(loyaltyInfo.current_amount)));
                clp.width = currentWidth == 0 ? CommonTools.dp2px(10) : currentWidth;
                viewCurrentProgress.setLayoutParams(clp);
            } else if (loyaltyInfo.current_amount > DecimalTools.parseInt(sOldValue)) {
                float oldCurrentProgress = Math.min((float) DecimalTools.divide(DecimalTools.parseInt(sOldValue), maxLevelValue), 1f);
                int oldCurrentMaxWidth = (int) (oldCurrentProgress * maxWidth);
                int oldCurrentWidth = oldCurrentProgress == 1 ? maxWidth : oldCurrentMaxWidth;
                clp.width = oldCurrentWidth == 0 ? CommonTools.dp2px(10) : oldCurrentWidth;
                viewCurrentProgress.setLayoutParams(clp);
            }
            // 定义起始和结束值
            int startValue = DecimalTools.parseInt(sOldValue);
            int endValue = loyaltyInfo.current_amount;
            float oldCurrentProgress = Math.min((float) DecimalTools.divide(startValue, maxLevelValue), 1f);
            int startMargin = (int) Math.ceil(maxWidth * oldCurrentProgress);  // 起始位置
            int endMargin = (int) Math.ceil(maxWidth * currentProgress) + CommonTools.dp2px(20);  // 结束位置
            ImageLoader.load(mContext, helper.getView(R.id.view_current_level_amount_dotted), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, loyaltyInfo.pre_current_amount_bg));
            Glide.with(mContext).load(loyaltyInfo.pre_current_amount_bg).into(new DrawableImageViewTarget(helper.getView(R.id.view_current_level_amount_dotted)) {
                @Override
                public void onResourceReady(@NonNull @NotNull Drawable resource, @Nullable @org.jetbrains.annotations.Nullable Transition<? super Drawable> transition) {
                    super.onResourceReady(resource, transition);
                    LifecycleProvider.get().getTopActivity().runOnUiThread(() -> {
                        if (hasAnimator) {
                            view.postDelayed(() -> {
                                // 创建ValueAnimator
                                ValueAnimator animator = ValueAnimator.ofInt(startMargin, endMargin);  // 滑动距离
                                animator.setDuration(1000); // 动画持续时间1秒
                                int totalValueChange = endValue - startValue;
                                // 添加更新监听器
                                animator.addUpdateListener(animation -> {
                                    int currentMargin = (Integer) animation.getAnimatedValue();
                                    int currentValue = startValue + (int) ((currentMargin - startMargin) / (float) (endMargin - startMargin) * totalValueChange);
                                    // 更新视图的位置
                                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) viewCurrentProgress.getLayoutParams();

                                    params.width = currentMargin == 0 ? CommonTools.dp2px(10) : currentMargin; // 更新左边距
                                    if (params.width > currentMaxWidth) {
                                        params.width = currentMaxWidth;
                                    }
                                    viewCurrentProgress.setLayoutParams(params); // 应用新的布局参数
                                    // 更新数字
                                    helper.setText(R.id.tv_current_level_amount, OrderHelper.formatUSMoney(OrderHelper.formatConciseMoney(currentValue)));// 将数字更新为当前的margin值
                                });
                                // 开始动画
                                animator.start();
                            }, 500);
                        }
                    });
                }
            });
        }
        AccountManager.get().setCurrentAmount(String.valueOf(loyaltyInfo.current_amount));

        LoyaltyInfo.TitleBean levelTitle1 = CollectionUtils.getOrNull(loyaltyInfo.account_progress_items, 0);
        if (levelTitle1 != null) {
            helper.setText(R.id.level_title1, levelTitle1.title);
            ViewTools.applyTextColor(helper.getView(R.id.level_title1), levelTitle1.color);
        } else {
            helper.setText(R.id.level_title1, null);
        }
        LoyaltyInfo.TitleBean levelTitle2 = CollectionUtils.getOrNull(loyaltyInfo.account_progress_items, 1);
        if (levelTitle2 != null) {
            helper.setText(R.id.level_title2, levelTitle2.title);
            ViewTools.applyTextColor(helper.getView(R.id.level_title2), levelTitle2.color);
        } else {
            helper.setText(R.id.level_title2, null);
        }
        LoyaltyInfo.TitleBean levelTitle3 = CollectionUtils.getOrNull(loyaltyInfo.account_progress_items, 2);
        if (levelTitle3 != null) {
            helper.setText(R.id.level_title3, levelTitle3.title);
            ViewTools.applyTextColor(helper.getView(R.id.level_title3), levelTitle3.color);
        } else {
            helper.setText(R.id.level_title3, null);
        }

        // hide level2
        int visibilityLevel2 = (middleLevelValue > 0 && loyaltyInfo.current_amount > middleLevelValue - 15) ? View.INVISIBLE : View.VISIBLE;
        helper.getView(R.id.level_value2).setVisibility(visibilityLevel2);
        int visibilityLevel2Title = (middleLevelValue > 0 && loyaltyInfo.current_amount >= middleLevelValue) ? View.INVISIBLE : View.VISIBLE;
        helper.getView(R.id.level_arrow2).setVisibility(visibilityLevel2Title);
        helper.getView(R.id.level_title2).setVisibility(visibilityLevel2Title);

        // hide level3
        int visibilityLevel3 = (maxLevelValue > 0 && loyaltyInfo.current_amount > maxLevelValue - 40) ? View.INVISIBLE : View.VISIBLE;
        helper.getView(R.id.level_value3).setVisibility(visibilityLevel3);
        int visibilityLevel3Title = (maxLevelValue > 0 && loyaltyInfo.current_amount >= maxLevelValue) ? View.INVISIBLE : View.VISIBLE;
        helper.getView(R.id.level_arrow3).setVisibility(visibilityLevel3Title);
        helper.getView(R.id.level_title3).setVisibility(visibilityLevel3Title);

        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_mine_accounts_progress), true);
    }

    private void convertRewards(AdapterViewHolder helper, AccountSectionData item) {
        // R.layout.layout_mine_accounts_rewards
        NewAccountSectionProperty p = item.p;
        LoyaltyInfo loyaltyInfo = p.loyalty_info;
        LoyaltyInfo.LoyaltyRewardTitleItems rewardItems = loyaltyInfo != null ? loyaltyInfo.loyalty_reward_title_items : null;
        if (rewardItems == null
                || (EmptyUtils.isEmpty(rewardItems.loyalty_reward_total_savings) && EmptyUtils.isEmpty(rewardItems.loyalty_reward_items))) {
            helper.setVisibleCompat(false, R.id.layout_reward);
            return;
        }
        int rewardTitleColor = ViewTools.parseColor(helper.itemView.getContext(), loyaltyInfo.account_loyalty_reward_title_color, R.color.black);
        String sectionName = item.bean.section_name;
        // 左半边
        List<LoyaltyInfo.LoyaltyRewardTitleItems.LoyaltyRewardItems> flipsLeft = CollectionUtils.newListOf(rewardItems.loyalty_reward_total_savings);
        ViewFlipper vfLeft = helper.getView(R.id.vf_reward_left);
        setLoyaltyFlipper(rewardTitleColor, flipsLeft, vfLeft, sectionName);
        // 右半边
        List<LoyaltyInfo.LoyaltyRewardTitleItems.LoyaltyRewardItems> flips = CollectionUtils.newListOf(rewardItems.loyalty_reward_items);
        ViewFlipper vf = helper.getView(R.id.vf_reward);
        setLoyaltyFlipper(rewardTitleColor, flips, vf, sectionName);
        helper.setVisibleCompat(true, R.id.layout_reward);
    }

    private void setLoyaltyFlipper(int rewardTitleColor, List<LoyaltyInfo.LoyaltyRewardTitleItems.LoyaltyRewardItems> flips, ViewFlipper vf, String sectionName) {
        boolean isFlip = flips.size() > 1;//是否自动切换
        ViewTools.removeAllViews(vf);
        int position = 0;
        for (LoyaltyInfo.LoyaltyRewardTitleItems.LoyaltyRewardItems rewardTitleItem : flips) {
            View view = LayoutInflater.from(mContext).inflate(R.layout.item_reward_scroll, vf, false);
            TextView titleTv = view.findViewById(R.id.tv_title);
            titleTv.setText(rewardTitleItem.reward_title);
            titleTv.setTextColor(rewardTitleColor);
            TextView titleSubTv = view.findViewById(R.id.tv_sub_title);
            titleSubTv.setText(rewardTitleItem.reward_sub_title);
            String linkUrl = rewardTitleItem.reward_url;
            String targetNm = rewardTitleItem.reward_key != null ? rewardTitleItem.reward_key : "rewards_info";
            int targetPos = position;
            ViewTools.setViewOnSafeClickListener(view, v -> {
                if (!EmptyUtils.isEmpty(linkUrl)) {
                    EagleTrackManger.get().trackEagleClickAction(
                            /* modNm = */sectionName,
                            /* modPos = */0,
                            /* secNm = */null,
                            /* secPos = */-1,
                            /* targetNm = */targetNm,
                            /* targetPos = */targetPos,
                            /* targetType = */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                            /* clickType = */EagleTrackEvent.ClickType.VIEW
                    );
                    toWeb(linkUrl);
                }
            });
            vf.addView(view);
            position++;
        }
        if (isFlip) {
            vf.startFlipping();
        } else {
            vf.stopFlipping();
        }
    }

    private void convertPointsBuy(AdapterViewHolder helper, AccountSectionData item) {
        NewAccountSectionProperty p = item.p;
        LoyaltyInfo loyaltyInfo = p.loyalty_info;
        List<LoyaltyInfo.LoyaltyPointsBuyItems> pointsBuyItems = loyaltyInfo != null ? loyaltyInfo.loyalty_points_buy_items : null;
        helper.setVisibleCompat(false, R.id.layout_loyalty_points_buy);
        if (loyaltyInfo == null || CollectionUtils.isEmpty(pointsBuyItems)) {
            return;
        }
        String sectionName = item.bean.section_name;
        //横向RV或者单个banner
        int position = helper.getLayoutPosition();
        boolean showRv = pointsBuyItems.size() > 1;
        if (showRv) {
            helper.setVisibleCompat(true, R.id.rv_loyalty_points);
            helper.setVisibleCompat(false, R.id.layout_item_loyalty_points_buy);
            helper.setVisibleCompat(false, R.id.iv_ftu);
            helper.setVisibleCompat(false, R.id.layout_ftu);
            RecyclerView rv = helper.getView(R.id.rv_loyalty_points);
            RewardItemAdapter rewardItemAdapter;
            RecyclerView.Adapter<?> rvAdapter = rv.getAdapter();
            if (rvAdapter instanceof RewardItemAdapter) {
                rewardItemAdapter = (RewardItemAdapter) rvAdapter;
            } else {
                rv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
                rewardItemAdapter = new RewardItemAdapter();
                rewardItemAdapter.setSectionName(sectionName);
                childAdapterCaches.add(new SoftReference<>(rewardItemAdapter));
                rv.setAdapter(rewardItemAdapter);
                rewardItemAdapter.setAttachView(rv);
                rv.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {
                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                            reportImpressionEagleEvent(recyclerView);
                        }
                    }
                });
            }
            rewardItemAdapter.setCloseIconShow(loyaltyInfo.points_buy_close_show);
            rewardItemAdapter.setNewData(loyaltyInfo.getLoyaltyPointsBuyItemsData());
            RewardItemAdapter finalRewardItemAdapter = rewardItemAdapter;
            rewardItemAdapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
                @Override
                public void onItemChildClickSafely(BaseQuickAdapter adapter, View view, int index) {
                    Object o = adapter.getItem(index);
                    if (o instanceof RewardData) {
                        closeLoyalty(((RewardData) o).item.item_value, index, sectionName);
                        finalRewardItemAdapter.closeLoyalty(index);
                        //
                        LoyaltyInfo payload = new LoyaltyInfo();
                        pointsBuyItems.remove(index);
                        payload.loyalty_points_buy_items = pointsBuyItems;
                        payload.points_buy_close_show = loyaltyInfo.points_buy_close_show;
                        notifyItemChanged(position, payload);
                    }
                }
            });
        } else {
            LoyaltyInfo.LoyaltyPointsBuyItems buyItem = pointsBuyItems.get(0);
            boolean isImage = buyItem.isImage();
            trackEagleBannerImp(
                    /* mod_nm= */sectionName,
                    /* mod_pos= */0,
                    /* sec_nm= */null,
                    /* sec_pos= */-1,
                    /* banner_id= */buyItem.item_value,
                    /* banner_key= */null,
                    /* banner_pos= */pointsBuyItems.indexOf(buyItem),
                    /* banner_type= */EagleTrackEvent.BannerType.MESSAGE,
                    /* message_type= */buyItem.item_key,
                    /* value= */isImage ? buyItem.banner_url : buyItem.buy_url
            );
            helper.setVisibleCompat(false, R.id.rv_loyalty_points);
            helper.setVisibleCompat(!isImage, R.id.layout_item_loyalty_points_buy);
            helper.setVisibleCompat(isImage, R.id.iv_ftu);
            helper.setVisibleCompat(isImage, R.id.layout_ftu);
            if (isImage) {
                ImageLoader.load(mContext, helper.getView(R.id.iv_ftu), WebpManager.convert(ImageSpec.SPEC_375_AUTO, buyItem.banner_image));
                helper.setOnViewClickListener(R.id.iv_ftu, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        Map<String, Object> ctx = new ArrayMap<>();
                        ctx.put("upgrade_level", buyItem.item_value);
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(sectionName)
                                .setMod_pos(0)
                                .setTargetNm(buyItem.item_key)
                                .setTargetPos(0)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .addCtx(ctx)
                                .build()
                                .getParams()
                        );
                        toWeb(buyItem.banner_url);
                    }
                });
            } else {
                setSingleLoyaltyPointsBuyItem(helper, loyaltyInfo.points_buy_close_show, pointsBuyItems, sectionName);
            }
        }
        helper.setVisibleCompat(true, R.id.layout_loyalty_points_buy);
    }

    private void setSingleLoyaltyPointsBuyItem(AdapterViewHolder helper, boolean showClose, List<LoyaltyInfo.LoyaltyPointsBuyItems> pointsBuyItems, String sectionName) {
        Context context = helper.itemView.getContext();
        LoyaltyInfo.LoyaltyPointsBuyItems buyItem = pointsBuyItems.get(0);
        ShapeConstraintLayout view = helper.getView(R.id.layout_item_loyalty_points_buy);
        view.setBackgroundSolidDrawable(ViewTools.parseColor(context, buyItem.background_color, R.color.color_place), CommonTools.dp2px(8));
        int itemColor = ViewTools.parseColor(context, buyItem.item_color, R.color.color_surface_1_fg_default_idle);
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_title_loyalty), 500);
        helper.setTextColor(R.id.tv_title_loyalty, itemColor);
        helper.setText(R.id.tv_title_loyalty, ViewTools.fromHtml(buyItem.title));
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_title_loyalty_second), 500);
        helper.setTextColor(R.id.tv_title_loyalty_second, itemColor);
        helper.setText(R.id.tv_title_loyalty_second, ViewTools.fromHtml(buyItem.title_second));
        ViewTools.setTextFontWeight(helper.getView(R.id.tv_title_loyalty_second), 500);
        Spanny subTitle = new Spanny(ViewTools.fromHtml(buyItem.sub_title), Spans.textAppearanceSpan(mContext, R.style.style_fluid_root_utility_xs_subdued));
        helper.setText(R.id.tv_sub_title_loyalty, subTitle);
        helper.setText(R.id.tv_label, buyItem.buy_label);
        helper.setTextColor(R.id.tv_label, itemColor);
        helper.setOnViewClickListener(R.id.layout_item_loyalty_points_buy, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Map<String, Object> ctx = new ArrayMap<>();
                ctx.put("upgrade_level", buyItem.item_value);
                AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                        .setMod_nm(sectionName)
                        .setMod_pos(0)
                        .setTargetNm("point_sale")
                        .setTargetPos(0)
                        .setClickType(EagleTrackEvent.ClickType.VIEW)
                        .addCtx(ctx)
                        .build()
                        .getParams()
                );
                toWeb(buyItem.buy_url);
            }
        });

        TextView tvSubContent = helper.getView(R.id.tv_sub_title_loyalty);
        tvSubContent.setMaxLines(buyItem.sub_title_tip != null ? 2 : 3);
        int height = 1;
        TextView tvSubTip = helper.getView(R.id.tv_sub_title_tip);
        if (buyItem.sub_title_tip != null) {
            Object tag = tvSubTip.getTag();
            if (!Objects.equals(tag, buyItem.sub_title_tip)) {
                helper.setText(R.id.tv_sub_title_tip, new ImgHtmlSpanner(tvSubTip)
                        .fromHtml(buyItem.sub_title_tip));
                tvSubTip.setTag(buyItem.sub_title_tip);
            }
            height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        tvSubTip.getLayoutParams().height = height;

        helper.setVisibleCompat(!EmptyUtils.isEmpty(buyItem.icon_url), R.id.iv_icon);
        if (!EmptyUtils.isEmpty(buyItem.icon_url)) {
            helper.loadImage(mContext, R.id.iv_icon, WebpManager.get().getConvertUrl(ImageSpec.SPEC_64, buyItem.icon_url), R.color.color_place);
        }
        ImageView cancel = helper.getView(R.id.iv_loyalty_points_buy_close);
        ViewTools.clearIvTintColor(cancel);
        ViewTools.applyIvTintColor(buyItem.item_color, mContext, cancel);
        helper.setOnViewClickListener(R.id.iv_loyalty_points_buy_close, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                closeLoyalty(buyItem.item_value, 0, sectionName);
                pointsBuyItems.clear();
                notifyItemChanged(helper.getLayoutPosition(), new LoyaltyInfo());
            }
        });
        helper.setVisibleCompat(showClose, R.id.iv_loyalty_points_buy_close);
    }

    private void convertAccountsNoLoginNew(AdapterViewHolder helper, AccountSectionData item) {
        // TYPE_ACCOUNTS_NO_LOGIN_NEW
        // R.layout.item_mine_accounts_no_login_new
        NewAccountSectionProperty data = item.p;
        boolean hasFtu = !EmptyUtils.isEmpty(data.banner_item);
        helper.setVisibleCompat(R.id.iv_icon, hasFtu);
        helper.setVisibleCompat(R.id.iv_avatar, !hasFtu);
        if (hasFtu) {
            NewAccountSectionProperty.BannerItem bannerItem = data.banner_item;
            ImageLoader.load(
                    mContext,
                    helper.getView(R.id.iv_icon),
                    WebpManager.convert(ImageSpec.SPEC_375_AUTO, bannerItem.item_image),
                    new RequestOptions().override(Target.SIZE_ORIGINAL)
            );
            trackEagleBannerImp(
                    /* mod_nm= */item.bean.section_name,
                    /* mod_pos= */0,
                    /* sec_nm= */null,
                    /* sec_pos= */-1,
                    /* banner_id= */data.banner_item.item_value,
                    /* banner_key= */null,
                    /* banner_pos= */0,
                    /* banner_type= */EagleTrackEvent.BannerType.MESSAGE,
                    /* message_type= */data.banner_item.item_key,
                    /* value= */null
            );
        } else {
            ImageLoader.load(mContext, helper.getView(R.id.iv_avatar), WebpManager.convert(ImageSpec.SPEC_64, data.user_image));
        }
        helper.addOnClickListener(R.id.iv_icon);
        helper.addOnClickListener(R.id.btn_login);
        helper.addOnClickListener(R.id.item_mine_accounts_no_login_root);

    }

    private void convertOrder(AdapterViewHolder helper, OrderSectionData item) {
        OrderSectionProperty data = item.p;
        helper.setTextHtml(R.id.tv_title, item.bean.section_title);
        setAdapterClickListener(helper.getView(R.id.tv_title), item.bean.link_url);
        helper.setVisibleCompat(data.have_today_delivery, R.id.layout_delivery);
        helper.setVisibleCompat(R.id.layout_alcohol_tips, false);
        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, item.bean.icon_url));
        if (data.have_today_delivery) {
            helper.setTextHtml(R.id.tv_delivery_title, data.today_delivery_title);
            setAdapterClickListener(helper.getView(R.id.tv_delivery_title), data.today_delivery_url);
            helper.setTextHtml(R.id.tv_delivery_desc, data.today_delivery_content);
            RippleLayout deliveryIndicator = helper.getView(R.id.v_delivery_indicator);
            helper.setTextHtml(R.id.tv_alcohol_tips, data.alcohol_copy_writing);
            helper.setVisibleCompat(R.id.layout_alcohol_tips, !TextUtils.isEmpty(data.alcohol_copy_writing));
            if (!EmptyUtils.isEmpty(data.today_delivery_progress)) {
                int selected = 0;
                List<String> list = new ArrayList<>();
                for (int i = 0; i < data.today_delivery_progress.size(); i++) {
                    OrderSectionProperty.TodayDeliveryProgressBean progressBean = data.today_delivery_progress.get(i);
                    if (progressBean != null) {
                        list.add(progressBean.status);
                        if (progressBean.active) {
                            selected = i;
                        }
                    }
                }
                deliveryIndicator.setStatus(list, selected);
            }
        }

        LinearLayout layoutOrderStatusContainer = helper.getView(R.id.layout_order_status_container);
        boolean visible = !EmptyUtils.isEmpty(data.items);
        if (visible) {
            layoutOrderStatusContainer.removeAllViews();
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
            for (int i = 0; i < data.items.size(); i++) {
                int finalI = i;
                OrderSectionProperty.OrderSectionItemBean statusBean = data.items.get(i);
                layoutOrderStatusContainer.addView(ViewTools.getHelperView(layoutOrderStatusContainer, R.layout.item_mine_order_status_child, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper helper) {
                        helper.setText(R.id.tv_status_name, statusBean.item_name);
                        int value = DecimalTools.parseInt(statusBean.item_value);
                        helper.setText(R.id.tv_status_num, value > 99 ? "99+" : String.valueOf(value));
                        helper.setVisible(R.id.tv_status_num, value > 0);
                        ImageLoader.load(mContext, helper.getView(R.id.iv_status_icon), WebpManager.convert(ImageSpec.SPEC_64, statusBean.icon_url), R.color.color_place);
                        helper.getItemView().setTag(R.string.tag_test_config, statusBean.item_type);
                        helper.getItemView().setOnClickListener(new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                EagleTrackManger.get().trackEagleClickAction(item.bean.section_name, item.pos, null, -1, statusBean.item_type, finalI, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
                                toWeb(statusBean.link_url);

                            }
                        });
                    }
                }), params);
            }
        }
        helper.setVisibleCompat(layoutOrderStatusContainer, visible);
        CombineFreeShippingBanner shippingBanner = data.combineFreeShippingBanner;
        ShapeConstraintLayout shapeConstraintLayout = helper.getView(R.id.combine_free_shipping_banner);
        helper.setVisibleCompat(shapeConstraintLayout, shippingBanner != null);
        if (shippingBanner != null) {
            shapeConstraintLayout.setBackgroundSolidDrawable(Color.parseColor(shippingBanner.bg_color), CommonTools.dp2px(12));
            helper.loadImage(mContext, R.id.iv_image_url, WebpManager.get().getConvertUrl(SPEC_64, shippingBanner.product_image_url), R.color.color_place);
            helper.setText(R.id.tv_content, ViewTools.fromHtml(shippingBanner.content));
            CountDownView tvTimer = helper.getView(R.id.tv_timer);
            long timeInterval = (long) shippingBanner.cutoff_end_time - shippingBanner.current_time;
            tvTimer.start(timeInterval + item.systemTime);
            tvTimer.setOnTimerListener(new OnSimpleTimerListener() {
                @Override
                public void onEnd() {
                    if (listener != null) {
                        listener.pageRefresh();
                    }
                }
            });
            helper.setOnViewClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    EagleTrackManger.get().trackEagleClickAction(item.bean.section_name, item.pos, null, -1, "add_on_items", -1, "message", EagleTrackEvent.ClickType.VIEW);
                    toWeb(shippingBanner.shop_url);
                }
            }, R.id.combine_free_shipping_banner);
        }
    }

    private void convertTodayOrder(AdapterViewHolder helper, TodayOrderSectionData item) {
        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, item.bean.icon_url));
        helper.setText(R.id.tv_title, item.bean.section_title + (item.p.today_orders_num > 0 ? "（" + item.p.today_orders_num + "）" : ""));
        setAdapterClickListener(helper.itemView, item.bean.link_url);
        LinearLayout layoutDelivery = helper.getView(R.id.layout_delivery);
        layoutDelivery.removeAllViews();
        boolean haveData = item.p.today_orders_num > 0;
        layoutDelivery.setVisibility(haveData ? View.VISIBLE : View.GONE);
        if (haveData) {
            if (item.p.ondemand_data != null) {
                layoutDelivery.addView(ViewTools.getHelperView(layoutDelivery, R.layout.item_mine_today_order, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper helper) {
                        TodayOrderSectionProperty.DemandDataBean demandData = item.p.ondemand_data;
                        helper.setText(R.id.tv_delivery_title, demandData.ondemand_delivery_title);
                        helper.setVisible(R.id.tv_delivery_title, !EmptyUtils.isEmpty(demandData.ondemand_delivery_title));
                        ViewTools.setViewHtml(helper.getView(R.id.tv_delivery_desc), demandData.ondemand_delivery_content);

                        List<TodayOrderSectionProperty.DeliveryStatusBean> list = demandData.ondemand_delivery_progress;
                        boolean hasProgress = !EmptyUtils.isEmpty(list);
                        if (hasProgress) {
                            AbsRippleLayout rippleLayout = null;
                            if (list.size() == 2) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_2);
                            } else if (list.size() == 3) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_3);
                            } else if (list.size() >= 4) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_4);
                            }
                            if (rippleLayout != null) {
                                rippleLayout.setVisibility(View.VISIBLE);
                                rippleLayout.setStatus(item.p.getDeliveryProgressText(list), item.p.getDeliveryProgressIndex(list), demandData.is_not_delivered);
                            }
                        }
                        helper.setVisible(R.id.layout_delivery_map, false);
                        if (demandData.show_map) {
                            String url = item.decodeStaticMapUrl();
                            if (url != null) {
                                ImageLoader.load(mContext, helper.getView(R.id.iv_delivery_map), url);
                                helper.setVisible(R.id.layout_delivery_map, true);
                            }
                        }
                        fillShareOrderChild(helper.getView(R.id.layout_share_order_container), demandData.order_share_info, item.current_timestamp);
                    }
                }));
            }

            if (!EmptyUtils.isEmpty(item.p.pickup_data)) {
                for (int i = 0; i < item.p.pickup_data.size(); i++) {
                    TodayOrderSectionProperty.PickupDataBean child = item.p.pickup_data.get(i);
                    layoutDelivery.addView(ViewTools.getHelperView(layoutDelivery, R.layout.item_mine_today_order, new OnViewHelper() {
                        @Override
                        public void help(ViewHelper helper) {
                            helper.setText(R.id.tv_delivery_title, child.pickup_title);
                            helper.setVisible(R.id.tv_delivery_title, !EmptyUtils.isEmpty(child.pickup_title));
                            ViewTools.setViewHtml(helper.getView(R.id.tv_delivery_desc), child.pickup_content);

                            List<TodayOrderSectionProperty.DeliveryStatusBean> list = child.today_pickup_progress;
                            boolean hasProgress = !EmptyUtils.isEmpty(list);
                            if (hasProgress) {
                                AbsRippleLayout rippleLayout = null;
                                if (list.size() == 2) {
                                    rippleLayout = helper.getView(R.id.v_delivery_indicator_2);
                                } else if (list.size() == 3) {
                                    rippleLayout = helper.getView(R.id.v_delivery_indicator_3);
                                } else if (list.size() >= 4) {
                                    rippleLayout = helper.getView(R.id.v_delivery_indicator_4);
                                }
                                if (rippleLayout != null) {
                                    rippleLayout.setVisibility(View.VISIBLE);
                                    rippleLayout.setStatus(item.p.getDeliveryProgressText(list), item.p.getDeliveryProgressIndex(list), child.is_not_delivered);
                                }
                            }
                            fillShareOrderChild(helper.getView(R.id.layout_share_order_container), child.order_share_info, item.current_timestamp);
                        }
                    }));
                }
            }
            if (item.p.delivery_data != null && item.p.delivery_data.have_today_delivery) {
                layoutDelivery.addView(ViewTools.getHelperView(layoutDelivery, R.layout.item_mine_today_order, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper helper) {
                        helper.setText(R.id.tv_delivery_title, item.p.delivery_data.today_delivery_title);
                        helper.setVisible(R.id.tv_delivery_title, !EmptyUtils.isEmpty(item.p.delivery_data.today_delivery_title));
                        ViewTools.setViewHtml(helper.getView(R.id.tv_delivery_desc), item.p.delivery_data.today_delivery_content);
                        helper.setText(R.id.tv_alcohol_tips, item.p.delivery_data.alcohol_copy_writing);
                        helper.setVisible(R.id.layout_alcohol_tips, !EmptyUtils.isEmpty(item.p.delivery_data.alcohol_copy_writing));

                        List<TodayOrderSectionProperty.DeliveryStatusBean> list = item.p.delivery_data.today_delivery_progress;
                        boolean hasProgress = !EmptyUtils.isEmpty(list);
                        if (hasProgress) {
                            AbsRippleLayout rippleLayout = null;
                            if (list.size() == 2) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_2);
                            } else if (list.size() == 3) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_3);
                            } else if (list.size() >= 4) {
                                rippleLayout = helper.getView(R.id.v_delivery_indicator_4);
                            }
                            if (rippleLayout != null) {
                                rippleLayout.setVisibility(View.VISIBLE);
                                rippleLayout.setStatus(item.p.getDeliveryProgressText(list), item.p.getDeliveryProgressIndex(list), item.p.delivery_data.is_not_delivered);
                            }
                        }
                        setAdapterClickListener(helper.getItemView(), item.p.delivery_data.today_delivery_url);
                        fillShareOrderChild(helper.getView(R.id.layout_share_order_container), item.p.delivery_data.order_share_info, item.current_timestamp);
                    }
                }));
            }
        }
    }

    private void convertMyLists(AdapterViewHolder helper, MyListSectionData item) {
        helper.setTextHtml(R.id.tv_title, item.bean.section_title);
        setAdapterClickListener(helper.getView(R.id.tv_title), item.bean.link_url);
        boolean visible = item.p != null && !EmptyUtils.isEmpty(item.p.items);

        LinearLayout layoutMyListsContainer = helper.getView(R.id.layout_my_lists_container);
        if (visible) {
            layoutMyListsContainer.removeAllViews();
            int size = item.p.items.size();
            for (int i = 0; i < size; i++) {
                MyListSectionProperty.MyListSectionItemBean child = item.p.items.get(i);
                if (child != null) {
                    int layoutRes = R.layout.item_mine_my_lists_child;
                    int finalI = i;
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(0, CommonTools.dp2px(50), 1);
                    layoutMyListsContainer.addView(ViewTools.getHelperView(layoutMyListsContainer, layoutRes, new OnViewHelper() {
                        @Override
                        public void help(ViewHelper helper) {
                            helper.setText(R.id.tv_name, ViewTools.fromHtml(child.item_name));
                            ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, child.icon_url));
                            helper.getItemView().setOnClickListener(new OnSafeClickListener() {
                                @Override
                                public void onClickSafely(View v) {
                                    toWeb(child.link_url);
                                    EagleTrackManger.get().trackEagleClickAction(item.bean.section_name, item.pos, null, -1, child.item_type, finalI, EagleTrackEvent.TargetType.NORMAL_BUTTON, EagleTrackEvent.ClickType.VIEW);
                                }
                            });
                        }
                    }), params);
                }
            }
        }
        helper.setVisibleCompat(layoutMyListsContainer, visible);
    }

    private void convertMyBanners(AdapterViewHolder helper, MyBannerSectionData item) {
        LinearLayout layoutIndicator = helper.getView(R.id.layout_main_banner_indicator);
        layoutIndicator.removeAllViews();
        fillBanner(helper, item, layoutIndicator, item.bean);
    }

    private void fillBanner(AdapterViewHolder helper, MyBannerSectionData item, LinearLayout layoutIndicator, SectionBean sectionBean) {
        List<CarouselBean> list = item.p.filter;
        CarouselBanner banner = helper.getView(R.id.cb_main_banner);
        banner.removeIndicator();
        int count = list.size();
        helper.setVisibleCompat(R.id.layout_main_banner_indicator, count > 1);
        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal_dark_grey, R.drawable.shape_line_banner_line_normal_selected, true, count, 0);
        banner.isAutoLoop(item.isAutoplay());
        banner.setAdapter(
                        new BannerImageAdapter<CarouselBean>(list) {
                            @Override
                            public BannerImageHolder onCreateHolder(ViewGroup parent, int viewType) {
                                ImageView imageView = new RoundImageView(parent.getContext());
                                //注意，必须设置为match_parent，这个是viewpager2强制要求的
                                ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                                        ViewGroup.LayoutParams.MATCH_PARENT,
                                        ViewGroup.LayoutParams.MATCH_PARENT);
                                imageView.setLayoutParams(params);
                                imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                                return new BannerImageHolder(imageView);
                            }

                            @Override
                            public void onBindView(BannerImageHolder holder, CarouselBean data, int position, int size) {
                                ImageLoader.load(mContext, holder.imageView, WebpManager.get().getConvertUrl(SPEC_375_AUTO, data.img_url));
                            }
                        }, item.isLoop())
                .setOnBannerListener(new OnBannerListener<CarouselBean>() {
                    @Override
                    public void OnBannerClick(CarouselBean data, int position) {
                        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                                .setMod_nm(EagleTrackEvent.BannerType.BANNER_LINE)
                                .setMod_pos(4)
                                .setTargetNm(String.valueOf(data.id))
                                .setTargetPos(position)
                                .setTargetType(EagleTrackEvent.BannerType.BANNER_LINE)
                                .setClickType(EagleTrackEvent.ClickType.VIEW)
                                .setUrl(data.link_url)
                                .build().getParams());
                        if (list.size() > position) {
                            toWeb(list.get(position).link_url);
                        }
                    }
                })
                .addOnPageChangeListener(new CarouselBanner.OnBannerPageChangeListener() {
                    @Override
                    public void onPageSelected(int position, boolean isAuto) {
                        banner.fillIndicator(layoutIndicator, R.drawable.shape_line_banner_line_normal_dark_grey, R.drawable.shape_line_banner_line_normal_selected, false, count, position);
                        CarouselBean bean = list.get(position);
                        if (!isAuto) {
                            if (ImpressionHelper.isImpressionEnableOnVertical(banner)) {
                                trackEagleBannerImp(EagleTrackEvent.BannerType.BANNER_LINE
                                        , item.index
                                        , null
                                        , -1
                                        , bean.id
                                        , bean.key
                                        , position
                                        , EagleTrackEvent.BannerType.BANNER_LINE
                                        , bean.link_url);
                            }
                        }
                    }
                });
    }

    private void convertActivity(AdapterViewHolder helper, ActivitySectionData item) {
        helper.setTextHtml(R.id.tv_order_task_content, item.p.content);
        helper.setBackgroundRes(R.id.tv_weeks_1, item.p.week_progress > 0 ? R.mipmap.achieve : R.mipmap.not_achieve);
        helper.setBackgroundRes(R.id.tv_weeks_2, item.p.week_progress > 1 ? R.mipmap.achieve : R.mipmap.not_achieve);
        helper.setBackgroundRes(R.id.tv_weeks_3, item.p.week_progress > 2 ? R.mipmap.achieve : R.mipmap.not_achieve);
        setAdapterClickListener(helper.itemView, item.bean.link_url);
    }

    private void convertMyPerks(AdapterViewHolder helper, MyPicksSectionData item) {
        LinearLayout layoutMyPerksContainer = helper.getView(R.id.layout_my_perks_container);
        layoutMyPerksContainer.removeAllViews();
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonTools.dp2px(56), 1);
        for (MyPerksSectionProperty.MyPerksItemSectionBean child : item.p.items) {
            if (child != null) {
                layoutMyPerksContainer.addView(ViewTools.getHelperView(layoutMyPerksContainer, R.layout.item_mine_my_perks_child, new OnViewHelper() {
                    @Override
                    public void help(ViewHelper viewHelper) {
                        viewHelper.setText(R.id.tv_name, child.item_name);
                        viewHelper.setVisible(R.id.v_dot, child.show_dot);
                        ViewTools.setViewHtml(viewHelper.getView(R.id.tv_value), child.item_value);
                        setAdapterClickListener(viewHelper.getItemView(), child.link_url);
                    }
                }), params);
            }
        }
    }

    private void convertShareOrder(AdapterViewHolder helper, ShareOrderSectionData item) {
        convertIconColor(helper, item);
        if (item.p.new_share_count > 0) {
            helper.setVisibleCompat(R.id.tv_badge, true);
            helper.setText(R.id.tv_badge, String.valueOf(item.p.new_share_count));
        }
        boolean visible = item.p != null && item.p.have_new_share;
        helper.setVisibleCompat(R.id.layout_share_order_container, visible);
        if (visible) {
            fillShareOrderChild(helper.getView(R.id.layout_share_order_container), item.p, item.current_timestamp);
        }
    }

    private void fillShareOrderChild(ViewGroup container, ShareOrderSectionProperty property, long systemCurrentTimestamp) {
        if (container == null) {
            return;
        }
        if (property == null) {
            container.setVisibility(View.GONE);
        } else {
            container.setVisibility(View.VISIBLE);
            TextView tvEarning = container.findViewById(R.id.tv_earning);
            ImageView ivShareImage = container.findViewById(R.id.iv_share_image);
            ImageView ivShareImage1 = container.findViewById(R.id.iv_share_image1);
            TextView tvShareEarned = container.findViewById(R.id.tv_share_earned);
            TextView tvShareEarnedTitle = container.findViewById(R.id.tv_share_earned_title);
            TimerTextView tvShareExpireDate = container.findViewById(R.id.tv_share_expire_date);
            TextView tvShareMore = container.findViewById(R.id.tv_share_more);
            TextView tvShareExpiresIn = container.findViewById(R.id.tv_share_expires_in);

            tvEarning.setVisibility(View.GONE);
            ivShareImage.setVisibility(View.GONE);
            ivShareImage1.setVisibility(View.GONE);
            tvShareEarned.setVisibility(View.GONE);
            tvShareEarnedTitle.setVisibility(View.GONE);
            tvShareExpireDate.setVisibility(View.GONE);
            tvShareMore.setVisibility(View.GONE);
            tvShareExpiresIn.setVisibility(View.GONE);

            if (property.share_image_url != null && property.share_image_url.size() > 0) {
                ivShareImage.setVisibility(View.VISIBLE);
                ImageLoader.load(mContext, ivShareImage, WebpManager.convert(ImageSpec.SPEC_PRODUCT, property.share_image_url.get(0)));
                if (property.share_image_url.size() > 1) {
                    ivShareImage1.setVisibility(View.VISIBLE);
                    ImageLoader.load(mContext, ivShareImage1, WebpManager.convert(ImageSpec.SPEC_PRODUCT, property.share_image_url.get(1)));
                }
            }

            if (property.share_tip != null) {
                tvEarning.setVisibility(View.VISIBLE);
                ViewTools.setViewHtml(tvEarning, property.share_tip);
            }

            if (property.share_more_content != null) {
                tvShareMore.setVisibility(View.VISIBLE);
                ViewTools.setViewHtml(tvShareMore, property.share_more_content);
            }

            long expires = DecimalTools.parseLong(property.expires_in);
            if (expires > 0 && expires > systemCurrentTimestamp) {
                tvShareEarnedTitle.setVisibility(View.VISIBLE);
                tvShareExpiresIn.setVisibility(View.VISIBLE);
                String points = formatPointsDisplay(property.gained_points);
//                String content = String.format("<span style=font-size:19px;font-color:#1EBA9C>%1$s</span>%2$s", property.gained_points, property.gained_content != null ? property.gained_content : "");
                ViewTools.setViewHtml(tvShareEarnedTitle, "+" + points + " " + (property.gained_content != null ? property.gained_content : ""));
                tvShareExpireDate.setVisibility(View.VISIBLE);
                tvShareExpiresIn.setText(property.expires_pre);
                tvShareExpireDate.start(expires, systemCurrentTimestamp);
            } else {
                tvShareEarnedTitle.setVisibility(View.VISIBLE);
                ViewTools.setViewHtml(tvShareEarnedTitle, property.gained_content);
            }
            setAdapterClickListener(container, property.share_more_link);
        }
    }

    private void convertIconCommon(AdapterViewHolder helper, IconCommonSectionData item) {
        LinearLayout container = helper.getView(R.id.layout_container);
        container.removeAllViews();
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonTools.dp2px(47));
        for (SectionBean bean : item.p.items) {
            String name;
            if (MineViewModel.SectionName.LANGUAGE.equalsIgnoreCase(bean.section_name)) {
                name = String.format(bean.section_title, mContext.getString(LanguageManager.get().getLanguageStringRes()));
            } else {
                name = bean.section_title;
            }
            container.addView(getChildView(container, bean.icon_url, name, bean.section_sub_title, bean.link_url), params);
        }
    }

    private void convertIconColor(AdapterViewHolder helper, AdapterSectionData item) {
        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, item.bean.icon_url));
        String name;
        helper.itemView.setOnLongClickListener(null);
        if (MineViewModel.SectionName.LANGUAGE.equalsIgnoreCase(item.bean.section_name)) {
            name = String.format(item.bean.section_title, mContext.getString(LanguageManager.get().getLanguageStringRes()));
        } else {
            name = item.bean.section_title;
        }
        if (MineViewModel.SectionName.USER_INFO.equalsIgnoreCase(item.bean.section_name)
                && !EmptyUtils.isEmpty(item.bean.section_title)) {
            helper.itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    copyLink(item.bean.section_title);
                    return false;
                }
            });
        }
        ViewTools.setViewHtml(helper.getView(R.id.tv_name), name);

        if (!EmptyUtils.isEmpty(item.bean.section_sub_title)) {
            ViewTools.setViewHtml(helper.getView(R.id.tv_desc), item.bean.section_sub_title);
        }
        helper.setVisibleCompat(R.id.tv_desc, !TextUtils.isEmpty(item.bean.section_sub_title));
        helper.setVisibleCompat(false, R.id.iv_arrow, R.id.tv_badge, R.id.tv_value, R.id.tv_sub_value, R.id.layout_timer, R.id.view, R.id.ll_vip);
        if (item.p instanceof IconColorSectionProperty) {
            IconColorSectionProperty property = (IconColorSectionProperty) item.p;
            if (property.getCount() > 0) {
                helper.setVisibleCompat(true, R.id.tv_badge, R.id.iv_arrow);
                helper.setText(R.id.tv_badge, property.count);
            }
            if (!TextUtils.isEmpty(property.item_value)) {
                String style = "<span style=font-weight:bold;font-size:17px>%1$s</span>";
                helper.setTextHtml(R.id.tv_value, String.format(style, property.item_value));
                helper.setVisibleCompat(R.id.tv_value, true);
            }
            if (!TextUtils.isEmpty(property.item_sub_value)) {
                helper.setText(R.id.tv_sub_value, property.item_sub_value);
                helper.setVisibleCompat(R.id.tv_sub_value, true);
            }
            if (TextUtils.isEmpty(property.item_value) && TextUtils.isEmpty(property.item_sub_value)) {
                helper.setVisibleCompat(R.id.iv_arrow, true);
            }
            boolean showTimer = property.countdown_time > 0 && property.countdown_time > property.now_time;
            int padding = showTimer ? CommonTools.dp2px(18) : 0;
            helper.itemView.setPadding(helper.itemView.getPaddingLeft(), padding, helper.itemView.getPaddingRight(), padding);
            if (showTimer) {
                helper.setVisible(R.id.layout_timer, true);
                helper.setVisible(R.id.tv_badge, property.referrals_expiring_num > 0);
                helper.setText(R.id.tv_badge, String.valueOf(property.referrals_expiring_num));
                TimerTextView timerTextView = helper.getView(R.id.tv_timer);
                long currentTime = ((IconColorSectionProperty) item.p).countdown_time;
                long nowTime = ((IconColorSectionProperty) item.p).now_time;
                timerTextView.setFormatText(mContext.getString(R.string.you_hava_invites_expiring_in)).start(currentTime, nowTime);
                timerTextView.setOnTimerListener(new OnTimerListener() {
                    @Override
                    public void onRestart(long lastCountdown, long countdown) {

                    }

                    @Override
                    public void onEnd() {
                        if (listener != null) {
                            listener.pageRefresh();
                        }
                    }

                    @Override
                    public void onTimer(long countdown) {

                    }
                });
            }
            //social profile badge
            helper.setVisibleCompat(false, R.id.ll_star, R.id.tv_social_badge, R.id.ll_vip);
            ShapeTextView tvBadge = helper.getView(R.id.tv_social_badge);
            ShapeConstraintLayout llStar = helper.getView(R.id.ll_star);
            TextView tvStar = helper.getView(R.id.tv_star);
            if (!EmptyUtils.isEmpty(property.user_tier_info) && !EmptyUtils.isEmpty(property.user_tier_info.img)) {
                ViewTools.setViewHtml(helper.getView(R.id.tv_name_profile), name);
                helper.setVisibleCompat(R.id.ll_vip, true);
                helper.setVisibleCompat(R.id.tv_social_badge, true);
                helper.setVisibleCompat(R.id.ll_star, true);
                tvBadge.setBackgroundSolidDrawable(Color.parseColor(property.user_tier_info.bg_color), CommonTools.dp2px(25));
                tvBadge.setTextColor(Color.parseColor(property.user_tier_info.color));
                tvBadge.setText(property.user_tier_info.label);
                llStar.setBackgroundSolidDrawable(Color.parseColor(property.user_star_info.bg_color), CommonTools.dp2px(25));
                tvStar.setText(property.user_star_info.label);
                tvStar.setTextColor(Color.parseColor(property.user_star_info.color));
                if (!EmptyUtils.isEmpty(property.user_star_info)) {
                    llStar.setVisibility(View.VISIBLE);
                    tvStar.setText(property.user_star_info.label);
                } else {
                    llStar.setVisibility(View.GONE);
                }
                ImageLoader.load(mContext, helper.getView(R.id.iv_star), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, property.user_tier_info.img));
            }
            helper.setOnViewClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    if (property.social_badge_info != null && property.social_badge_info.badge_url != null) {
                        mContext.startActivity(WebViewActivity.getIntent(mContext, property.social_badge_info.badge_url));
                    }
                }
            }, R.id.tv_social_badge, R.id.ll_star);
        } else {
            helper.setVisibleCompat(helper.getItemViewType() != TYPE_ICON_SIMPLE_ONLY, R.id.iv_arrow);
        }
        setAdapterClickListener(helper.itemView, item.bean.link_url);
    }

    private void convertButtonSimple(AdapterViewHolder helper, AdapterSectionData item) {
        ViewTools.setViewHtml(helper.getView(R.id.tv_name), item.bean.section_title);
        if (MineViewModel.SectionName.LOGOUT.equalsIgnoreCase(item.bean.section_name)) {
            helper.addOnClickListener(R.id.tv_name);
        } else {
            setAdapterClickListener(helper.itemView, item.bean.link_url);
        }
    }

    private View getChildView(LinearLayout container, String icon, String name, String desc, String link) {
        return ViewTools.getHelperView(container, R.layout.item_mine_icon_common_child, new OnViewHelper() {
            @Override
            public void help(ViewHelper helper) {
                ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, icon));
                ViewTools.setViewHtml(helper.getView(R.id.tv_name), name);
                ViewTools.setViewHtml(helper.getView(R.id.tv_desc), desc);
                helper.setVisible(R.id.tv_desc, !TextUtils.isEmpty(desc));
                setAdapterClickListener(helper.getItemView(), link);
            }
        });
    }

    public void setAdapterClickListener(View view, String url) {
        if (view != null) {
            view.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    toWeb(url);
                }
            });
        }
    }

    private void toWeb(String url) {
        if (mContext != null && url != null) {
            mContext.startActivity(WebViewActivity.getIntent(mContext, WebViewFragment.TYPE_URL_NONE_DECODE, null, url));
        }
    }

    private void convertQuestionnaire(AdapterViewHolder helper, IconSurveyData item) {
        // TYPE_QUESTIONNAIRE
        // R.layout.item_mine_quesition
        TextView submit = helper.getView(R.id.tv_submit);
        submit.setText(item.p.btn_value);
        helper.setText(R.id.tv_content, item.bean.section_sub_title);
        helper.setText(R.id.tv_title, item.bean.section_title);

        helper.addOnClickListener(R.id.tv_submit).addOnClickListener(R.id.iv_close);
        helper.addOnClickListener(R.id.tv_continue).addOnClickListener(R.id.iv_second_close);

        IconSurveyProperty.IconSurveyItemBean child = item.p.options.get(0);
        helper.setText(R.id.tv_likely, child.max.title);
        helper.setText(R.id.tv_not_at_all, child.min.title);
        helper.setText(R.id.tv_zero, child.min.rating);
        helper.setText(R.id.tv_max, child.max.rating);
        if (item.p.isAnswerQuestions() || item.layoutChanged) {
            convertSecondLayout(helper, item);
        } else if (item.p.isRecommendScore()) {
            initSeekBar(helper, item);
        }
    }

    private void initSeekBar(AdapterViewHolder helper, IconSurveyData outItem) {
        helper.setGone(R.id.layout_first, true);
        helper.setGone(R.id.layout_second, false);
        final TextView num = helper.getView(R.id.tv_num);
        num.setVisibility(View.INVISIBLE);
        AppCompatSeekBar seekBar = helper.getView(R.id.seekbar);
        outItem.progress = -1;
        seekBar.setProgress(800);
        seekBar.setMax(1000);
        ShapeTextView tvSubmit = helper.getView(R.id.tv_submit);
        tvSubmit.setTextColor(mContext.getColor(R.color.color_surface_1_fg_subtle_idle));
        tvSubmit.setBackgroundStrokeDrawable(mContext.getColor(R.color.color_surface_2_bg_idle), CommonTools.dp2px(1), CommonTools.dp2px(25));
        seekBar.setOnSeekBarChangeListener(new AppCompatSeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                outItem.progress = (int) Math.floor(progress / 100.0);
                num.setText(String.valueOf(outItem.progress));

                float textWidth = num.getWidth();
                float left = seekBar.getLeft();
                float max = Math.abs(seekBar.getMax());
                //这不叫thumb的宽度,叫seekbar距左边宽度,实验了一下，seekbar 不是顶格的，两头都存在一定空间，所以xml 需要用paddingStart 和 paddingEnd 来确定具体空了多少值
                float thumb = CommonTools.dp2px(20);
                seekBar.setThumb(ContextCompat.getDrawable(mContext, R.mipmap.pic_mine_nps_progress));
                //每移动1个单位，text应该变化的距离 = (seekBar的宽度 - 两头空的空间) / 总的progress长度
                float average = ((((float) seekBar.getWidth()) - 2 * thumb) / max);
                //textview 应该所处的位置 = seekbar最左端 + seekbar左端空的空间 + 当前progress应该加的长度 - textview宽度的一半(保持居中作用)
                float pox = left - textWidth / 2 + thumb + average * (float) progress;
                num.setX(pox);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                tvSubmit.setTextColor(mContext.getColor(R.color.color_surface_1_fg_default_idle));
                tvSubmit.setBackgroundStrokeDrawable(mContext.getColor(R.color.color_surface_1_fg_hairline_idle), CommonTools.dp2px(1), CommonTools.dp2px(25));
                num.setVisibility(View.VISIBLE);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
//                num.setVisibility(View.INVISIBLE);
                seekBar.setThumb(ContextCompat.getDrawable(mContext, R.mipmap.pic_mine_nps_progress));
            }
        });
    }

    private void convertSecondLayout(AdapterViewHolder helper, IconSurveyData item) {
        helper.setGone(R.id.layout_first, false);
        helper.setGone(R.id.layout_second, true);
        ImageLoader.load(mContext, (ImageView) helper.getView(R.id.iv_feedback), item.p.submit_result.tip_image);
        helper.setText(R.id.tv_earn_more, item.p.submit_result.tip);
        helper.setText(R.id.tv_continue, item.p.submit_result.btn_value);
        helper.setTextHtml(R.id.tv_complete, item.p.submit_result.sub_tip);
    }

    private void convertIconImage(AdapterViewHolder helper, IconImageSectionData item) {
        ImageLoader.load(mContext, helper.getView(R.id.iv_icon), WebpManager.convert(ImageSpec.SPEC_32, item.bean.icon_url));
        helper.setVisibleCompat(true, R.id.iv_arrow);
        helper.setVisibleCompat(!EmptyUtils.isEmpty(item.bean.section_sub_title), R.id.tv_desc);
        ViewTools.setViewHtml(helper.getView(R.id.tv_desc), item.bean.section_sub_title);
        helper.setVisibleCompat(false, R.id.tv_name, R.id.iv_section_title_image, R.id.iv_badge_image, R.id.tv_sub_value);
        if (!EmptyUtils.isEmpty(item.bean.section_title_image)) {
            helper.setVisibleCompat(true, R.id.iv_section_title_image);
            ImageLoader.load(mContext, helper.getView(R.id.iv_section_title_image), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, item.bean.section_title_image));
        } else {
            helper.setVisibleCompat(true, R.id.tv_name);
            helper.setText(R.id.tv_name, item.bean.section_title);
        }
        if (!EmptyUtils.isEmpty(item.p.badge_image_url)) {
            helper.setVisibleCompat(true, R.id.iv_badge_image);
            ImageLoader.load(mContext, helper.getView(R.id.iv_badge_image), WebpManager.convert(ImageSpec.Size.SIZE_AUTO, ImageSpec.Size.SIZE_32, item.p.badge_image_url));
        } else {
            helper.setVisibleCompat(true, R.id.tv_sub_value);
            helper.setText(R.id.tv_sub_value, item.p.item_value);
        }


        setAdapterClickListener(helper.itemView, item.bean.link_url);
    }


    private String formatPointsDisplay(int value) {
        DecimalFormat decimalFormat;
        String formattedPoints;
        if (LanguageManager.get().isVietnamese()) {
            Locale vietnameseLocale = new Locale("vi", "VN");
            // 创建 DecimalFormatSymbols 对象并设置分组分隔符为逗号
            DecimalFormatSymbols symbols = new DecimalFormatSymbols(vietnameseLocale);
            symbols.setGroupingSeparator(',');
            // 创建 DecimalFormat 对象，并设置格式化模式和所用的 DecimalFormatSymbol 对象
            decimalFormat = new DecimalFormat("#,##0", symbols);
            // 格式化数值
            formattedPoints = decimalFormat.format(value);
        } else {
            NumberFormat numberFormat = NumberFormat.getNumberInstance();
            // 设置千位分隔符
            numberFormat.setGroupingUsed(true);
            formattedPoints = numberFormat.format(value);
        }
        return formattedPoints;
    }

    private void copyLink(String section_title) {
        CommonTools.copyText(mContext, section_title);
        Toaster.showToast(mContext.getString(R.string.text_copied));
    }

    @Override
    public List<ImpressionBean> getEagleImpressionData(int start, int end) {
        List<ImpressionBean> list = new ArrayList<>();
        if (start != RecyclerView.NO_POSITION && end != RecyclerView.NO_POSITION && end >= start) {
            int headerCount = getHeaderLayoutCount();
            if (headerCount > 0) {
                start -= headerCount;
                end -= headerCount;
            }
            if (start == end) {
                List<ImpressionBean> event = getEagleImpressionEvent(getItem(start));
                if (!EmptyUtils.isEmpty(event)) {
                    list.addAll(event);
                }
            } else {
                for (int i = start; i <= end; i++) {
                    List<ImpressionBean> event = getEagleImpressionEvent(getItem(i));
                    if (!EmptyUtils.isEmpty(event)) {
                        list.addAll(event);
                    }
                }
            }
        }
        return list;
    }

    private List<ImpressionBean> getEagleImpressionEvent(AdapterSectionData item) {
        List<ImpressionBean> list = new ArrayList<>();
        if (item != null && item.bean != null) {
            if (item instanceof IconCommonSectionData) {
                List<SectionBean> items = ((IconCommonSectionData) item).p.items;
                for (SectionBean bean : items) {
                    if (bean != null) {
                        Map<String, Object> params = new EagleTrackModel.Builder()
                                .setMod_nm(bean.section_name)
                                .setMod_pos(item.index)
                                .build().getParams();
                        list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, bean.section_name));
                    }
                }
            } else if (item instanceof MyBannerSectionData) {
                //触发banner的impression埋点
                MyBannerSectionData data = (MyBannerSectionData) item;
                if (data.bean != null && data.p != null && !EmptyUtils.isEmpty(data.p.filter)) {
                    List<CarouselBean> filter = data.p.filter;
                    trackEagleBannerImp(EagleTrackEvent.BannerType.BANNER_LINE
                            , data.index
                            , null
                            , -1
                            , filter.get(0).id
                            , filter.get(0).key
                            , 0
                            , EagleTrackEvent.BannerType.BANNER_LINE
                            , filter.get(0).link_url);
                }
            } else {
                Map<String, Object> params = new EagleTrackModel.Builder()
                        .setMod_nm(item.bean.section_name)
                        .setMod_pos(item.index)
                        .build().getParams();
                list.add(new ImpressionBean(EagleTrackEvent.EventType.PAGE_SEC_IMP, params, item.bean.section_name));
            }
        }
        return list;
    }

    private void trackEagleBannerImp(String mod_nm, int mod_pos, String sec_nm, int sec_pos, int banner_id, String banner_key, int banner_pos, String banner_type, String value) {
        trackEagleBannerImp(mod_nm, mod_pos, sec_nm, sec_pos, banner_id, banner_key, banner_pos, banner_type, null, value);
    }

    private void trackEagleBannerImp(String mod_nm, int mod_pos, String sec_nm, int sec_pos, int banner_id, String banner_key, int banner_pos, String banner_type, String message_type, String value) {
        if (!EagleTrackManger.get().isEventTracked(EagleTrackManger.PAGE_ME, value)) {
            AppAnalytics.logBannerImp(new EagleTrackModel.Builder()
                    .setMod_nm(mod_nm)
                    .setMod_pos(mod_pos)
                    .setSec_nm(sec_nm)
                    .setSec_pos(sec_pos)
                    .setBanner_id(banner_id)
                    .setBanner_key(banner_key)
                    .setBanner_pos(banner_pos)
                    .setBanner_type(banner_type)
                    .setMessage_type(message_type)
                    .setUrl(value)
                    .build().getParams());
            EagleTrackManger.get().setEventTracked(EagleTrackManger.PAGE_ME, value);
        }
    }

    public void onPageResume() {
        if (childAdapterCaches != null && childAdapterCaches.size() > 0) {
            List<SoftReference<BaseQuickAdapter>> invalids = new ArrayList<>();
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter != null) {
                        ProductSyncHelper.onPageResume(adapter);
                        onPageResumeImpression(adapter);
                    } else {
                        invalids.add(reference);
                    }
                }
            }
            if (invalids.size() > 0) {
                childAdapterCaches.removeAll(invalids);
            }
        }
    }

    public void onPagePause() {
        if (childAdapterCaches != null && childAdapterCaches.size() > 0) {
            List<SoftReference<BaseQuickAdapter>> invalids = new ArrayList<>();
            for (SoftReference<BaseQuickAdapter> reference : childAdapterCaches) {
                if (reference != null) {
                    BaseQuickAdapter adapter = reference.get();
                    if (adapter != null) {
                        onPagePauseImpression(adapter);
                    } else {
                        invalids.add(reference);
                    }
                }
            }
            if (invalids.size() > 0) {
                childAdapterCaches.removeAll(invalids);
            }
        }
    }

    public void onPageResumeImpression(BaseQuickAdapter adapter) {
        if (adapter instanceof ImpressionChild) {
            RecyclerView attachView = ((ImpressionChild) adapter).getAttachView();
            if (attachView != null) {
                if (eagleImpressionTracker != null) {
                    eagleImpressionTracker.onPageResume(attachView);
                }
            }
        }
    }

    public void onPagePauseImpression(BaseQuickAdapter adapter) {
        if (adapter instanceof ImpressionChild) {
            RecyclerView attachView = ((ImpressionChild) adapter).getAttachView();
            if (attachView != null) {
                if (eagleImpressionTracker != null) {
                    eagleImpressionTracker.onPagePause(attachView);
                }
            }
        }
    }

    private void reportImpressionEagleEvent(RecyclerView view) {
        if (eagleImpressionTracker != null && view != null && view.getVisibility() == View.VISIBLE) {
            view.post(() -> eagleImpressionTracker.trackImpression(view));
        }
    }

    private onPageRefreshListener listener;

    public void setOnPageRefresh(onPageRefreshListener listener) {
        this.listener = listener;
    }

    public interface onPageRefreshListener {
        void pageRefresh();
    }

    private onLoyaltyCloseListener loyaltyCloseListener;

    public void setOnLoyaltyClose(onLoyaltyCloseListener listener) {
        this.loyaltyCloseListener = listener;
    }

    public interface onLoyaltyCloseListener {
        void loyaltyClose(int itemValue);
    }

    private void closeLoyalty(int itemValue, int index, String sectionName) {
        if (loyaltyCloseListener != null) {
            loyaltyCloseListener.loyaltyClose(itemValue);
        }
        Map<String, Object> ctx = new ArrayMap<>();
        ctx.put("upgrade_level", itemValue);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(sectionName)
                .setMod_pos(0)
                .setTargetNm("point_sale")
                .setTargetPos(index)
                .setClickType("banner")
                .addCtx(ctx)
                .build().getParams());
    }
}
