package com.sayweee.weee.module.checkout2;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelKt;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.service.impl.payment.agents.BraintreeCardPaymentAgent;
import com.sayweee.service.impl.payment.bean.PaymentAgentRequest;
import com.sayweee.service.payment.bean.PaymentChannel;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.LayoutMemberPlanStickyBarBinding;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.widget.CheckOutBottomView;
import com.sayweee.weee.module.checkout.CheckOutAddEmailActivity;
import com.sayweee.weee.module.checkout.CouponActivity;
import com.sayweee.weee.module.checkout.DeliveryAddressPickerActivity;
import com.sayweee.weee.module.checkout.adapter.CheckOutPointsProvider;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.adapter.CheckoutDeliveryWindowAdapter;
import com.sayweee.weee.module.checkout.bean.CheckoutCouponData;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.DeliveryWindowBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutTipInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout.service.CheckOutSectionViewModel;
import com.sayweee.weee.module.checkout2.adapter.OnPurchaseChannelActionListener;
import com.sayweee.weee.module.checkout2.bean.CheckoutV4Bean;
import com.sayweee.weee.module.checkout2.data.CheckoutPurchaseChannelData;
import com.sayweee.weee.module.checkout2.data.CheckoutUsePointsData;
import com.sayweee.weee.module.checkout2.data.EbtAmountArgs;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelExtraData;
import com.sayweee.weee.module.checkout2.pm.PmEditActivity;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.pay.PaymentCallbackHelper;
import com.sayweee.weee.service.pay.PaymentHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.FullNameDialog;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.weee.widget.snackbar.ActionToastView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.toast.ToastOptions;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.utils.KeyboardUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

@SuppressWarnings({"UnsafeIntentLaunch", "Convert2Lambda"})
public class CheckoutSectionActivity extends WrapperMvvmActivity<CheckoutSectionViewModel> {

    private static final String EXTRA_CART_DOMAIN = "cart_domain";
    private static final String EXTRA_CART_ID = "cart_id";

    public static Intent getIntent(Context context, String cartDomain, String cartId) {
        return new Intent(context, CheckoutSectionActivity.class)
                .putExtra(EXTRA_CART_DOMAIN, cartDomain)
                .putExtra(EXTRA_CART_ID, cartId);
    }

    private static final int REQUEST_COUPON = 101;
    private static final int REQUEST_DELIVERY_ADDRESS = 102;
    private static final int REQUEST_ORDER_EMAIL = 103;
    private static final int REQUEST_DATE = 104;

    private RecyclerView rvList;
    private CheckOutSectionAdapter adapter;
    private View vShadow;
    private View layoutAlertTips;
    private ImageView ivTipsAlert;
    private SmartRefreshLayout mSmartRefreshLayout;
    private TextView tvTipsAlert;
    private TextView tvPoints;
    private CheckOutBottomView bottomView;
    private LayoutMemberPlanStickyBarBinding memberPlanBarBinding;

    private int refreshFlag = AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE;
    /**
     * 连续点击points需要前端先直接赋值，再展示后端数据。该字段控制是否刷新
     */
    private boolean noRefresh;

    private EagleImpressionTrackerIml eagleImpressionTracker;
    private KeyboardChangeHelper keyboardChangeHelper;
    private boolean hasFullNameChecked;
    private FullNameDialog fullNameDialog;

    private ActivityResultLauncher<Intent> purchaseChannelLauncher;

    @Nullable
    protected String getExtraCartDomain() {
        return getIntent() != null ? getIntent().getStringExtra(EXTRA_CART_DOMAIN) : null;
    }

    @Nullable
    protected String getExtraCartId() {
        return getIntent() != null ? getIntent().getStringExtra(EXTRA_CART_ID) : null;
    }

    @Override
    protected void onDestroy() {
        keyboardChangeHelper.endObserve();
        super.onDestroy();
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_checkout_section;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        initWrapperTitle();

        vShadow = findViewById(R.id.v_shadow);
        rvList = findViewById(R.id.rv_list);
        ivTipsAlert = findViewById(R.id.iv_tips_alert);
        tvTipsAlert = findViewById(R.id.tv_tips_alert);
        tvPoints = findViewById(R.id.tv_points);
        layoutAlertTips = findViewById(R.id.layout_alert_tips);
        mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        mSmartRefreshLayout.setOnRefreshListener(refreshLayout -> viewModel.refreshPreCheckoutData());
        bottomView = findViewById(R.id.bottom_view);
        bottomView.setOnViewClickListener(R.id.tv_checkout, new OnSafeClickListener(1000L) {
            @Override
            public void onClickSafely(View v) {
                trackClickAction(
                        /* targetNm= */EagleTrackEvent.TargetNm.CHECKOUT,
                        /* clickType= */EagleTrackEvent.ClickType.NORMAL
                );
                onPlaceOrderButtonClick();
            }
        });
        ViewTools.setViewVisible(bottomView, false);

        memberPlanBarBinding = LayoutMemberPlanStickyBarBinding.bind(findViewById(R.id.layout_member_plan_sticky_bar));
        memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, false);
        ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.getRoot(), false);

        eagleImpressionTracker = new EagleImpressionTrackerIml();
        initRecyclerView();
        initObserver();
        showVeilTemplate(true);
    }

    private void initObserver() {
        keyboardChangeHelper = new KeyboardChangeHelper(getContentView());
        keyboardChangeHelper.setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
                adapter.updateOtherTipStatus();
            }
        });
        keyboardChangeHelper.startObserve();

        purchaseChannelLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                this::handleActivityResultPurchaseChannel
        );
    }

    private void initRecyclerView() {
        rvList.setLayoutManager(new LinearLayoutManager(activity));
        adapter = new CheckOutSectionAdapter().setOnViewChangedCallback(new CheckOutSectionAdapter.OnViewCallback() {
            @Override
            public void onEditTextFocusChanged(View v) {
                // move to onPurchaseChannelActionListener
            }

            @Override
            public void updateTipsData(PreCheckoutTipInfoBean.OptionsBean bean) {
                viewModel.getCheckoutPreAgent().updateOptions(bean);
                viewModel.refreshPreCheckoutData();
            }

            @Override
            public void updatePoints(boolean checked) {
                // move to onPurchaseChannelActionListener
            }

            @Override
            public void onCvcEditTextNoFocus(String cvcText, boolean isCvcInvalid) {
                // move to onPurchaseChannelActionListener
            }
        });

        adapter.setOnCheckOutPointsListener(new CheckOutPointsProvider.OnCheckoutPointsListener() {
            @Override
            public void updateMemberPointsChecked(String planId, double planPrice, int position) {
                if (bottomView.isLoading()) {
                    noRefresh = true;
                }
                adapter.updatePointsPosition(position);
                viewModel.getCheckoutPreAgent().updatePlan(planId, planPrice);
                bottomView.setCanCheckOut(false, R.string.s_place_order);
                bottomView.loading();
                viewModel.refreshPreCheckoutData();
            }
        });

        adapter.setOnDeliveryWindowActionListener(new CheckoutDeliveryWindowAdapter.OnDeliveryWindowActionListener() {
            @Override
            public void onDeliveryWindowClick(@NonNull DeliveryWindowBean window) {
                String selectedWindowIds = adapter != null ? adapter.getSelectedWindowIds() : null;
                viewModel.getCheckoutPreAgent().updateDeliveryWindowIds(selectedWindowIds);
                viewModel.refreshPreCheckoutData();
            }

            @Override
            public void onDeliveryWindowInfoClick(@NonNull DeliveryWindowBean window) {
                showDeliveryWindowInfoDialog(window.viewMoreUrl);
            }
        });

        adapter.setOnPurchaseChannelActionListener(onPurchaseChannelActionListener);

        rvList.setAdapter(adapter);
        adapter.setImpressionTracker(eagleImpressionTracker);
        rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                PreCheckoutV2Bean preCheckoutV2Bean = viewModel.getCheckoutPreAgent().getData();
                boolean isNotTop = !EmptyUtils.isEmpty(preCheckoutV2Bean) && EmptyUtils.isEmpty(preCheckoutV2Bean.reminder_content) && recyclerView.canScrollVertically(-1);
                ViewTools.setViewVisibilityIfChanged(vShadow, isNotTop);

                invalidateMemberPlanStickyBar(recyclerView);
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent();
                }
            }
        });
        adapter.setOnItemChildClickListener(this::handleAdapterItemClick);
    }

    @SuppressWarnings("rawtypes")
    private void handleAdapterItemClick(BaseQuickAdapter quickAdapter, View view, int position) {
        PreCheckoutV2Bean preCheckoutV2Bean = viewModel.getCheckoutPreAgent().getData();
        if (preCheckoutV2Bean == null) {
            return;
        }
        Object item = adapter.getItem(position);
        if (item instanceof CheckoutDeliveryData) {
            startActivityForResult(DeliveryAddressPickerActivity.getIntent(activity, preCheckoutV2Bean, "checkout"), REQUEST_DELIVERY_ADDRESS);
        } else if (item instanceof CheckoutReviewOrderData) {
            PreCheckoutOrderReviewsBean bean = ((CheckoutReviewOrderData) item).orderReviewsDTO;
            if (view.getId() == R.id.tv_support_change_date) {
                changeDateTrackClickAction(((CheckoutReviewOrderData) item).orderReviewsDTO.type);
                boolean confirmShippingFee = bean.shipping_info != null && bean.shipping_info.isShowShippingDialog();
                startActivityForResult(DateActivity.getIntent(activity, confirmShippingFee), REQUEST_DATE);
            }
        } else if (item instanceof CheckoutCouponData) {
            startActivityForResult(CouponActivity.getIntent(activity, getExtraCartDomain(), preCheckoutV2Bean.coupon_info != null ? preCheckoutV2Bean.coupon_info.code : null), REQUEST_COUPON);
        }
    }

    @Override
    public void loadData() {

    }

    protected void loadCheckoutPreData(boolean showLoading) {
        viewModel.preCheckoutV2(!showLoading);
    }

    @Override
    public void attachModel() {
        viewModel.setCartDomain(getExtraCartDomain());
        viewModel.setCartId(getExtraCartId());

        viewModel.adapterData.observe(this, adapterDataTypes -> {
            if (noRefresh) {
                noRefresh = false;
                return;
            }
            adapter.setNewData(adapterDataTypes);
            mSmartRefreshLayout.finishRefresh();
            showVeilTemplate(false);
            setImpressionEnable(true);
            reportImpressionEvent();
        });

        // viewModel.preCheckoutV2() response
        viewModel.checkoutPreLiveData.observe(this, new Observer<PreCheckoutV2Bean>() {
            @Override
            public void onChanged(PreCheckoutV2Bean checkoutPreData) {
                if (checkoutPreData == null) {
                    return;
                }
                viewModel.setCheckoutPreData(checkoutPreData);

                ViewTools.setViewVisible(bottomView, true);
                bottomView.setCanCheckOut(true, R.string.s_place_order);
                if (checkoutPreData.fee_info != null) {
                    bottomView.setAmount(checkoutPreData.fee_info.final_display_amount, null);
                }

                ViewTools.setViewVisible(tvPoints, checkoutPreData.pointsIsNotNull());
                if (checkoutPreData.pointsIsNotNull()) {
                    bottomView.setCouponReminder(checkoutPreData.point_info.total_reward_points_text);
                }
                if (fullNameDialog != null && fullNameDialog.isShowing()) {
                    updateFullNameDialog(checkoutPreData);
                }
                bottomView.setAttachedData();
                fillTipsAlert();
                bindMemberPlanStickyBar();
                showShippoDialog();

                viewModel.renderAdapterData(checkoutPreData);
            }
        });

        // viewModel.checkout() response
        viewModel.checkoutLiveData.observe(this, this::handleCheckoutV4Response);

        viewModel.getLoadPreCheckoutDataSignal().observe(this, this::loadCheckoutPreData);

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                String messageId = failureBean.getMessageId();
                String message = failureBean.getMessage();
                if (messageId != null) {
                    switch (messageId) {
                        case CheckoutSectionViewModel.ORDER_EMPTY:
                            if (message != null && !message.isEmpty()) {
                                CheckoutSectionViewModel.showToast(message);
                            }
                            finish();
                            return;
                        case CheckoutSectionViewModel.ORDER_ADDRESS_EMPTY:
                        case CheckoutSectionViewModel.ORDER_ADDRESS_ERROR:
                        case CheckoutSectionViewModel.ORDER_PHONE_ERROR:
                            setAddressError(failureBean.getMessage());
                            return;
                        case CheckoutSectionViewModel.ORDER_NOT_MAIL:
                            showAddressErrorDialog(failureBean.getMessage());
                            return;
                        case CheckoutSectionViewModel.ORDER_COUPON_ERROR:
                        case CheckoutSectionViewModel.ORDER_SHOW_ERROR:
                        case CheckoutSectionViewModel.ORDER_FORCE_REFRESH:
                        case CheckoutSectionViewModel.ORDER_FORCE_REFRESH_2:
                            String tag = failureBean.getObject();
                            //仅checkout接口允许强制刷新，避免死锁逻辑
                            if (CheckoutSectionViewModel.TAG_FORCE_REFRESH.equals(tag)) {
                                viewModel.refreshPreCheckoutData();
                            }
                            break;
                        default:
                            break;
                    }
                }
                CheckoutSectionViewModel.showToast(message);
                bottomView.setCanCheckOut(true, R.string.s_place_order);
            }
        });

        viewModel.couponLiveData.observe(this, new Observer<CouponBean>() {
            @Override
            public void onChanged(CouponBean couponBean) {
                if (!EmptyUtils.isEmpty(couponBean) && !EmptyUtils.isEmpty(couponBean.coupons_valid)) {
                    viewModel.couponSize = couponBean.coupons_valid.size();
                }
                adapter.updateCoupon(couponBean);
            }
        });

        viewModel.toastLiveData.observe(this, this::showToast);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 禁用自动刷新逻辑 0 不禁用 1 禁用一次 其他 禁用多次
        if (refreshFlag != AbstractPreCheckoutViewModel.REFRESH_FLAG_DISABLE) {
            if (refreshFlag == AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE) {
                viewModel.getCheckoutPreAgent().updatePlan(null, 0);
                viewModel.refreshPreCheckoutData();
            } else {
                refreshFlag = AbstractPreCheckoutViewModel.REFRESH_FLAG_ENABLE;
            }
        }
        AppAnalytics.logPageView(WeeeEvent.PageView.CHECKOUT, this, new TrackParams().put("is_rtg", false).get());
        setImpressionEnable(false);
        eagleImpressionTracker.onPageResume(rvList);
    }

    private void setImpressionEnable(boolean isEnabled) {
        if (rvList != null) {
            rvList.setTag(R.id.tag_impression_tracker, isEnabled);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        eagleImpressionTracker.onPagePause(rvList);
    }

    private void fillTipsAlert() {
        PreCheckoutV2Bean preCheckoutV2Bean = viewModel.getCheckoutPreAgent().getData();
        PreCheckoutV2Bean.ReminderContentBean reminder;
        reminder = preCheckoutV2Bean != null ? preCheckoutV2Bean.reminder_content : null;
        if (reminder == null) {
            ViewTools.setViewVisibilityIfChanged(layoutAlertTips, false);
            return;
        }
        layoutAlertTips.setBackgroundColor(ViewTools.parseColor(reminder.reminder_bg_color, Color.WHITE));
        if (!EmptyUtils.isEmpty(reminder.reminder_icon_url)) {
            ImageLoader.load(activity, ivTipsAlert, WebpManager.convert(ImageSpec.SPEC_32, reminder.reminder_icon_url), R.color.color_place);
            ViewTools.setViewVisibilityIfChanged(ivTipsAlert, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(ivTipsAlert, false);
        }
        ViewTools.setViewHtml(tvTipsAlert, reminder.reminder_text);
        ViewTools.setViewVisibilityIfChanged(layoutAlertTips, true);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_ORDER_EMAIL) {
                onPlaceOrderButtonClick();
            } else if (requestCode == REQUEST_DELIVERY_ADDRESS || requestCode == REQUEST_DATE) {
                viewModel.getCheckoutPreAgent().updateOptions(null);
                viewModel.getCheckoutPreAgent().updateDeliveryWindowIds(null);
                viewModel.getCheckoutPreAgent().setEbtBalance(null);
            } else if (requestCode == REQUEST_COUPON && data != null) {
                Serializable result = data.getSerializableExtra("applied_coupon");
                if (result instanceof PreCheckoutV2Bean.CouponInfoBean) {
                    adapter.quickUpdateCoupon((PreCheckoutV2Bean.CouponInfoBean) result);
                } else {
                    if (result instanceof CouponBean) {
                        adapter.updateCoupon((CouponBean) result);
                        viewModel.couponSize = ((CouponBean) result).coupons_valid.size();
                    } else {
                        viewModel.couponSize = 0;
                        adapter.updateCoupon(null);
                    }
                    adapter.quickUpdateCoupon(null);
                }
            }
        }
    }

    private void handleActivityResultPurchaseChannel(ActivityResult activityResult) {
        Intent data = activityResult.getData();
        EbtAmountArgs args = data != null ? (EbtAmountArgs) data.getSerializableExtra(PmEditActivity.EXTRA_EBT_AMOUNT_ARGS) : null;
        boolean isPaymentUpdated = data != null && data.getBooleanExtra(PmEditActivity.EXTRA_PAYMENT_UPDATED, false);
        if (args != null) {
            if (isPaymentUpdated) {
                viewModel.getCheckoutPreAgent().setEbtUserAmount(args.getUserAmount());
            }
            viewModel.getCheckoutPreAgent().setEbtBalance(args.getBalance());
        }
        if (isPaymentUpdated) {
            showToast(R.string.s_checkout_payment_updated);
        }
    }

    private void setKeyboardOnFocus(final View target) {
        if (target != null) {
            target.postDelayed(() -> KeyboardUtils.setKeyboardVisible(activity, target, true), 50L);
        }
    }

    private void checkout() {
        viewModel.checkout();
    }

    private void reportImpressionEvent() {
        if (rvList != null) {
            rvList.postDelayed(() -> {
                if (eagleImpressionTracker != null) {
                    eagleImpressionTracker.trackImpression(rvList);
                }
            }, 200L);
        }
    }

    private void handlePaymentResult(String url) {
        if (url != null && !url.isEmpty()) {
            startActivity(WebViewActivity.getIntent(activity, url));
        }
        finish();
    }

    private void handlePaymentRedirect(String url, String cancelUrl) {
        if (url != null && !url.isEmpty()) {
            startActivity(PayPaymentWebViewActivity.getIntent(activity, url, cancelUrl));
        }
        finish();
    }

    private void showAddressErrorDialog(String message) {
        trackPopup(message);
        new WrapperDialog(activity, R.style.CommonDialogTheme) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_not_mail;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
            }

            @Override
            public void help(ViewHelper helper) {
                helper.setText(R.id.tv_title, message);
                helper.setOnClickListener(R.id.tv_update_address, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        trackClickAction(
                                /* targetNm= */EagleTrackEvent.TargetNm.UPDATE_ADDR,
                                /* clickType= */EagleTrackEvent.ClickType.VIEW
                        );
                        dismiss();

                        PreCheckoutV2Bean preCheckoutV2Bean = viewModel.getCheckoutPreAgent().getData();
                        if (preCheckoutV2Bean != null) {
                            Intent a = DeliveryAddressPickerActivity.getIntent(activity, preCheckoutV2Bean, "checkout");
                            startActivityForResult(a, REQUEST_DELIVERY_ADDRESS);
                        }
                    }
                });
                helper.setOnClickListener(R.id.tv_modify_cart, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        trackClickAction(
                                /* targetNm= */EagleTrackEvent.TargetNm.CART,
                                /* clickType= */EagleTrackEvent.ClickType.VIEW
                        );
                        dismiss();
                        finish();
                    }
                });
            }
        }.show();
    }

    private void updateFullNameDialog(@NonNull PreCheckoutV2Bean preCheckoutV2Bean) {
        if (fullNameDialog != null) {
            fullNameDialog.setData(preCheckoutV2Bean.address_info, hasFullNameChecked);
        }
    }

    private void showFullNameDialog(@NonNull PreCheckoutV2Bean preCheckoutV2Bean) {
        if (fullNameDialog != null) {
            fullNameDialog.dismiss();
            fullNameDialog = null;
        }
        FullNameDialog dialog = new FullNameDialog(this);
        fullNameDialog = dialog;
        dialog.setOnListener(new FullNameDialog.OnActionListener() {
            @Override
            public void onCommit(boolean isChecked) {
                hasFullNameChecked = true;
                onPlaceOrderButtonClick();
            }

            @Override
            public void onCheckedChange(boolean isChecked) {
                hasFullNameChecked = isChecked;
            }
        });
        updateFullNameDialog(preCheckoutV2Bean);
        dialog.show();
    }

    private void onPlaceOrderButtonClick() {
        PreCheckoutV2Bean preCheckoutV2Bean = viewModel.getCheckoutPreAgent().getData();
        if (preCheckoutV2Bean == null) {
            return;
        }

        // 有 full_name_tip 并且没有选中过则打开弹窗校验
        if (preCheckoutV2Bean.hasFullName() && !hasFullNameChecked) {
            showFullNameDialog(preCheckoutV2Bean);
            return;
        }

        // 检查是否有地址
        if (!ensureDeliveryInfo(preCheckoutV2Bean)) {
            return;
        }

        // 使用积分能全额抵扣
        boolean isPointsAllDeducted = viewModel.getCheckoutPreAgent().isPointsAllDeducted();
        if (!isPointsAllDeducted && !ensurePointsNotAllDeductedOnPlaceOrder()) {
            return;
        }

        if (!preCheckoutV2Bean.hasEmail()) {
            startActivityForResult(CheckOutAddEmailActivity.getIntent(activity), REQUEST_ORDER_EMAIL);
            return;
        }

        prepareCheckout();
    }

    private boolean ensureDeliveryInfo(@NonNull PreCheckoutV2Bean preCheckoutV2Bean) {
        if (!viewModel.ensureDeliveryInfo(preCheckoutV2Bean)) {
            int deliveryInfoPosition = CollectionUtils.indexOfFirst(adapter.getData(), item -> item instanceof CheckoutDeliveryData);
            if (deliveryInfoPosition != -1) {
                Toaster.showToast(getString(R.string.please_choose_an_address));
                rvList.scrollToPosition(deliveryInfoPosition);
            }
            return false;
        }
        return true;
    }

    private boolean ensurePointsNotAllDeductedOnPlaceOrder() {
        CheckoutPreAgent agent = viewModel.getCheckoutPreAgent();
        int paymentPosition = CollectionUtils.indexOfFirst(adapter.getData(), item -> item instanceof CheckoutPurchaseChannelData);
        AdapterDataType item = CollectionUtils.getOrNull(adapter.getData(), paymentPosition);
        CheckoutPurchaseChannelData purchaseChannelItem = null;
        if (item instanceof CheckoutPurchaseChannelData) {
            purchaseChannelItem = (CheckoutPurchaseChannelData) item;
        }
        if (purchaseChannelItem == null) {
            return false;
        }

        PurchaseChannelExtraData extraData = agent.getPurchaseChannelExtraDataOnPlaceOrder();
        purchaseChannelItem.setPurchaseChannelExtraData(extraData);
        if (extraData.hasError()) {
            adapter.notifyItemChanged(paymentPosition, extraData);
            rvList.scrollToPosition(paymentPosition);
            return false;
        }
        return true;
    }

    private void prepareCheckout() {
        CheckoutPreAgent agent = viewModel.getCheckoutPreAgent();
        agent.setCardTokenizeResult(null);
        boolean isPointsAllDeducted = agent.isPointsAllDeducted();
        if (!isPointsAllDeducted) {
            PaymentChannel cardChannel = agent.getCardChannel();
            String cvcText = agent.cvcText;
            // Obtain deviceData whatever the card channel isCheckCvv
            if (cardChannel != null && DecimalTools.compare(cardChannel.getChannelAmount(), "0") > 0) {
                viewModel.setLoadingStatus(true);
                BraintreeCardPaymentAgent.tokenizeCard(
                        /* coroutineScope= */ViewModelKt.getViewModelScope(viewModel),
                        /* context= */this,
                        /* channelCode= */cardChannel.getChannelCode(),
                        /* isCheckCvv= */CheckoutPreAgent.isCheckCvv(cardChannel),
                        /* cvv= */cvcText,
                        /* callback= */clientPaymentInfo -> {
                            viewModel.setLoadingStatus(false);
                            if (clientPaymentInfo.isSuccess()) {
                                agent.setCardTokenizeResult(clientPaymentInfo);
                                checkout();
                            }
                            return Unit.INSTANCE;
                        }
                );
            } else {
                checkout();
            }
        } else {
            checkout();
        }
    }

    private void handleCheckoutV4Response(@NonNull CheckoutV4Bean bean) {
        String successUrl = !EmptyUtils.isEmpty(bean.success_url)
                ? bean.success_url
                : (bean.pay_payment != null ? bean.pay_payment.getSuccessUrl() : null);
        String cancelUrl = !EmptyUtils.isEmpty(bean.cancel_url)
                ? bean.cancel_url
                : (bean.pay_payment != null ? bean.pay_payment.getCancelUrl() : null);
        if (EmptyUtils.isEmpty(bean.order_ids) || bean.pay_payment == null) {
            // Api error, should never happen
            handlePaymentResult(cancelUrl);
            return;
        }

        refreshFlag = CheckOutSectionViewModel.REFRESH_FLAG_DISABLE;
        PaymentHelper.OnPaymentRedirectCallback c = new PaymentHelper.OnPaymentRedirectCallback() {

            @Override
            public void onPaymentRedirect(String url, String cancelUrl) {
                PaymentCallbackHelper.get().unregisterPaymentCallback(this);
                handlePaymentRedirect(url, cancelUrl);
            }

            @Override
            public void onResult(boolean result, String url) {
                PaymentCallbackHelper.get().unregisterPaymentCallback(this);
                handlePaymentResult(url);
            }
        };
        PaymentCallbackHelper.get().registerPaymentCallback(activity, c);

        PaymentAgentRequest request = new PaymentAgentRequest.Builder(
                /* orderIds= */bean.order_ids,
                /* successUrl= */successUrl,
                /* cancelUrl= */cancelUrl,
                /* payPayment= */bean.pay_payment
        ).build();
        List<String> purchaseChannelCodes = viewModel.getCheckoutPreAgent().getPurchaseChannelCodes();
        Intent a = PayPaymentActivity.getIntent(
                this,
                request,
                purchaseChannelCodes,
                viewModel.getCheckoutPreAgent().getCardTokenizeResult()
        );
        startActivity(a);
    }

    private void setAddressError(String message) {
        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType item = adapter.getData().get(i);
            if (item instanceof CheckoutDeliveryData) {
                ((CheckoutDeliveryData) item).errorTipMessage = message;
                adapter.notifyItemChanged(i);
                return;
            }
        }
    }

    private void showVeilTemplate(boolean visible) {
        VeilTools.show(findViewById(R.id.vl_checkout), visible);
        VeilTools.show(findViewById(R.id.vl_checkout_seil), visible);
        ViewTools.setViewVisible(findViewById(R.id.iv_shadow_veil), visible);
    }

    private void trackPopup(String name) {
        Map<String, Object> co = new TrackParams()
                .put("action", "view")
                .put("name", name)
                .put("content_type", null)
                .put("id", null)
                .put("url", null)
                .put("target_url", null)
                .put("other_parameter", null)
                .get();
        AppAnalytics.logEvent(
                EagleTrackEvent.EventType.POPUP_IMP,
                new EagleTrackModel.Builder().addContent(co).build().getParams()
        );
    }

    private void trackClickAction(String targetNm, String clickType) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(targetNm)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(clickType)
                .build().getParams());
    }

    private void changeDateTrackClickAction(String secNm) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(EagleTrackEvent.TargetNm.DELIVERY_DATE)
                .setMod_nm(EagleTrackEvent.ModNm.CART)
                .setSec_nm(secNm)
                .setMod_pos(5)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    private WrapperDialog previousDialog = null;

    private void showDeliveryWindowInfoDialog(String url) {
        if (EmptyUtils.isEmpty(url)) {
            return;
        }
        if (previousDialog != null) {
            previousDialog.dismiss();
        }

        PopupSlideDialog dialog = new PopupSlideDialog();
        dialog.loadUrl(url);
        int dialogHeight = (int) (getResources().getDisplayMetrics().heightPixels * 0.5);
        dialog.callDialogSize(-1, dialogHeight);
        dialog.show();
        previousDialog = dialog;
    }

    private void bindMemberPlanStickyBar() {
        PreCheckoutV2Bean preCheckoutBean = viewModel.getCheckoutPreAgent().getData();
        if (preCheckoutBean == null
                || preCheckoutBean.member_upgrade_plan_sticky_bar == null
                || CollectionUtils.any(preCheckoutBean.member_upgrade_plans, p -> p.selected)
        ) {
            memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, false);
            ViewTools.setViewOnSafeClickListener(memberPlanBarBinding.getRoot(), null);
            invalidateMemberPlanStickyBar(rvList);
            return;
        }

        PreCheckoutV2Bean.MemberUpgradeStickyBar bar = preCheckoutBean.member_upgrade_plan_sticky_bar;

        // background
        Integer backgroundColor = ViewTools.getColorByString(
                memberPlanBarBinding.getRoot().getContext(),
                bar.bg_color,
                R.color.color_root_durian_yellow_light_1
        );
        if (backgroundColor == null) {
            backgroundColor = ViewTools.parseColor("#FFFEC4", Color.WHITE);
        }
        memberPlanBarBinding.getRoot().setBackgroundColor(backgroundColor);

        // icon
        if (!EmptyUtils.isEmpty(bar.icon)) {
            ImageLoader.load(this, memberPlanBarBinding.ivIcon, WebpManager.convert(ImageSpec.SPEC_PRODUCT, bar.icon));
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivIcon, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivIcon, false);
        }

        // title
        if (!EmptyUtils.isEmpty(bar.title)) {
            memberPlanBarBinding.tvTitle.setText(ViewTools.fromHtml(bar.title));
        } else {
            memberPlanBarBinding.tvTitle.setText(null);
        }

        if (!EmptyUtils.isEmpty(bar.right_arrow_icon)) {
            ImageLoader.load(this, memberPlanBarBinding.ivArrow, WebpManager.convert(ImageSpec.SPEC_PRODUCT, bar.right_arrow_icon));
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivArrow, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivArrow, false);
        }
        ViewTools.setViewOnSafeClickListener(memberPlanBarBinding.getRoot(), v -> {
            CheckOutSectionAdapter a = adapter;
            if (a != null) {
                int memberPlanPosition = CollectionUtils.indexOfFirst(
                        a.getData(),
                        item -> item.getType() == CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS
                );
                RecyclerViewTools.smoothScrollTo(rvList, memberPlanPosition);
            }
        });
        memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, true);
        invalidateMemberPlanStickyBar(rvList);
    }

    private void invalidateMemberPlanStickyBar(RecyclerView recyclerView) {
        if (recyclerView == null) {
            return;
        }
        LayoutMemberPlanStickyBarBinding b = memberPlanBarBinding;
        Object isValid = b.getRoot().getTag(R.id.tag_item_data);
        if (isValid == null || Boolean.FALSE.equals(isValid)) {
            b.getRoot().post(() -> ViewTools.setViewVisibilityIfChanged(b.getRoot(), false));
            return;
        }

        CheckOutSectionAdapter a = null;
        if (recyclerView.getAdapter() instanceof CheckOutSectionAdapter) {
            a = ((CheckOutSectionAdapter) recyclerView.getAdapter());
        }
        if (a == null) {
            return;
        }

        Pair<Integer, Integer> positions = RecyclerViewTools.findVisibleItemPositionRange(recyclerView, false);
        if (positions != null) {
            int memberPlanPosition = CollectionUtils.indexOfFirst(
                    a.getData(),
                    item -> item.getType() == CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS
            );
            boolean isInvisible = memberPlanPosition < positions.first || memberPlanPosition > positions.second;
            b.getRoot().post(() -> ViewTools.setViewVisibilityIfChanged(b.getRoot(), isInvisible));
        }
    }

    private void showShippoDialog() {
        PreCheckoutV2Bean preCheckoutBean = viewModel.getCheckoutPreAgent().getData();
        if (preCheckoutBean != null
                && preCheckoutBean.address_info != null
                && !TextUtils.isEmpty(preCheckoutBean.address_info.shippo_not_support_desc)
        ) {
            showAddressErrorDialog(preCheckoutBean.address_info.shippo_not_support_desc);
        }
    }

    private void initWrapperTitle() {
        setWrapperTitle(R.string.s_checkout);
        setWrapperDivider(null);
        if (useWrapper()) {
            TextView tvTitleCenter = getWrapperTitle().findViewById(R.id.tv_title_center);
            tvTitleCenter.setTextColor(getColor(R.color.color_navbar_fg_default));
            ImageView ivTitleLeft = getWrapperTitle().getView(R.id.iv_title_left);
            ViewTools.tintImageView(ivTitleLeft, R.color.color_navbar_fg_default);
        }
    }

    private void toPaymentMethodsPage() {
        CheckoutPreAgent agent = viewModel.getCheckoutPreAgent();
        if (!agent.isPaymentMethodSelectable()) {
            return;
        }

        EbtAmountArgs ebtAmountArgs;
        ebtAmountArgs = agent.getEbtAmountArgs();
        Intent a = PmEditActivity.getIntent(
                /* context= */CheckoutSectionActivity.this,
                /* source= */PmEditActivity.SOURCE_CHECKOUT,
                /* usePoints= */agent.isUsePoints(),
                /* isAvailableEbt= */agent.isAvailableEbt,
                /* ebtAmountArgs= */ebtAmountArgs,
                /* firstPurchasedAmount= */null
        );
        purchaseChannelLauncher.launch(a);
    }

    private void showToast(@StringRes int resId) {
        String title;
        try {
            title = ContextCompat.getString(this, resId);
        } catch (Exception ex) {
            return;
        }
        ActionSnackBarData.Builder dataBuilder = new ActionSnackBarData.Builder()
                .setTitle(title)
                .setActionIconResId(R.drawable.drawable_checkmark_circle_outline_green_1);
        ActionToastView toastView = new ActionToastView(this);
        toastView.convert(dataBuilder.build());
        Toaster.asToast(this).setView(toastView)
                .setOptions(new ToastOptions().setGravity(Gravity.FILL_HORIZONTAL | Gravity.BOTTOM))
                .build()
                .show();
    }

    private void onUsePointsChecked(CheckoutUsePointsData item) {
        trackClickAction(
                /* targetNm= */EagleTrackEvent.TargetNm.WEEE_POINTS,
                /* clickType= */EagleTrackEvent.ClickType.VIEW
        );
        if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_MEMBER_PLAN) {
            Toaster.showToast(getString(R.string.s_checkout_rewards_toast));
        } else if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_USE_EBT) {
            trackPopup(/* name= */EagleTrackEvent.PopupNm.WEEE_POINTS_POPUP);
            showConfirmUsePointsDialog(() -> updateUsePoints(item, /* usePoints= */true, /* removeEbt= */true));
        } else if (item.unavailableType == CheckoutUsePointsData.UNAVAILABLE_TYPE_COMMISSION_PARTNER) {
            // do nothing, just show the unavailable state
            if (DevConfig.isDevelop() && !EmptyUtils.isEmpty(item.getUnavailableDesc())) {
                Toaster.showToast(item.getUnavailableDesc());
            }
        } else {
            updateUsePoints(item, /* usePoints= */!item.checked, /* removeEbt= */false);
        }
    }

    private void showConfirmUsePointsDialog(Runnable onConfirm) {
        new WrapperDialog(this, R.style.BottomDialogTheme) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_checkout_use_points_confirm;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }

            @Override
            public void help(ViewHelper helper) {
                ViewTools.setViewOnClickListener(helper.getView(R.id.iv_close), v -> dismiss());
                ViewTools.setViewOnClickListener(helper.getView(R.id.btn_confirm), v -> {
                    onConfirm.run();
                    dismiss();
                });
            }
        }.show();
    }

    private void updateUsePoints(CheckoutUsePointsData item, boolean usePoints, boolean removeEbt) {
        item.checked = usePoints;
        item.pointsCanChecked = false;
        int pointsPosition = CollectionUtils.indexOfFirst(adapter.getData(), it -> it instanceof CheckoutUsePointsData);
        adapter.notifyItemChanged(pointsPosition);
        viewModel.getCheckoutPreAgent().setCustomerUsePoints(usePoints);
        viewModel.updatePaymentCategory(usePoints, removeEbt);
        bottomView.setCanCheckOut(false, R.string.s_place_order);
        bottomView.loading();
    }

    private final OnPurchaseChannelActionListener onPurchaseChannelActionListener = new OnPurchaseChannelActionListener() {

        @Override
        public void onPurchaseChannelClick() {
            toPaymentMethodsPage();
        }

        @Override
        public void onCardCvvInputFocusChange(View view, boolean hasFocus) {
            if (hasFocus) {
                setKeyboardOnFocus(view);
            } else {
                if (view instanceof TextView) {
                    CharSequence cs = ((TextView) view).getText();
                    viewModel.setCvcText(cs != null ? cs.toString() : null);
                }
            }
        }

        @Override
        public void onUsePointsChecked(CheckoutUsePointsData item) {
            CheckoutSectionActivity.this.onUsePointsChecked(item);
        }
    };
}
