package com.sayweee.weee.module.home.adapter;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.OpLayout;
import com.sayweee.weee.widget.product.ProductView;

import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/9/15.
 * Desc:
 */
public class UpSellItemAdapter extends ProductItemMoreAdapter {

    public UpSellItemAdapter(@Nullable List<ProductBean> list) {
        super(list);
    }

    @Override
    protected void convertProduct(@NonNull AdapterViewHolder helper, ProductBean item) {
        ProductView layoutProductView = helper.getView(R.id.layout_product_view);
        layoutProductView.setAttachedProduct(item, getDisplayStyle(), source, null, null);
        Map<String, Object> element = new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .setSec_nm(secNm)
                .setSec_pos(secPos)
                .build()
                .getElement();
        layoutProductView.setAttachedProduct(item, getDisplayStyle(), new ProductView.OnOpCallback() {
            @Override
            public void onOp(CartOpLayout layoutOp, ProductBean bean) {
                OpHelper.helperOp(layoutOp, item, item, source, listener == null ? null : new OpLayout.OnOperateListener() {
                    @Override
                    public void operateLeft(View view) {
                        listener.onOperate(item, false);
                    }

                    @Override
                    public void operateRight(View view) {
                        listener.onOperate(item, true);
                    }
                }, element, null, false);
            }
        });
        LabelHelper.setTitleLabel(layoutProductView.findViewById(R.id.tv_product_name), item, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (toProductDetailsListener != null) {
                    toProductDetailsListener.toDetails(item);
                } else {
                    layoutProductView.onProductClick(mContext, item);
                }
            }
        });
        helper.setOnViewClickListener(R.id.layout_product, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (toProductDetailsListener != null) {
                    toProductDetailsListener.toDetails(item);
                } else {
                    layoutProductView.onProductClick(mContext, item);
                }
            }
        });
        setDisplayMinimumHeight(layoutProductView);
        helper.setVisible(R.id.iv_collect, true);
        onCollectClick(helper, item, layoutProductView, new EagleContext());

        ProductTraceViewHelper.convert(
                helper.itemView,
                ProductTraceKey.of(item.id, item.recommendation_trace_id, ProductTraceKey.generateUniqueKey(modNm, secNm))
        );
    }

    public interface OnToProductDetailsListener {
        void toDetails(ProductBean bean);
    }

    public interface OnProductOperateListener {
        void onOperate(ProductBean bean, boolean isAdd);
    }

    protected OnProductOperateListener listener;

    public UpSellItemAdapter setOnProductOperateListener(OnProductOperateListener listener) {
        this.listener = listener;
        return this;
    }

    protected OnToProductDetailsListener toProductDetailsListener;

    public UpSellItemAdapter setToProductDetailsListener(OnToProductDetailsListener toProductDetailsListener) {
        this.toProductDetailsListener = toProductDetailsListener;
        return this;
    }
}
