package com.sayweee.weee.module.checkout2.adapter;

import android.content.Context;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.sayweee.service.payment.PaymentKt;
import com.sayweee.service.payment.bean.PaymentChannel;
import com.sayweee.service.payment.bean.PaymentChannelDetail;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.checkout2.CheckoutPreAgent;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelExtraData;
import com.sayweee.weee.module.checkout2.data.PurchaseChannelSubData;
import com.sayweee.weee.module.checkout2.pm.PmHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.span.Spans;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.wrapper.utils.Spanny;

public class PurchaseChannelItemProvider extends PurchaseChannelChildProvider {

    @Override
    public int getItemType() {
        return PurchaseChannelSubData.SUB_TYPE_ITEM;
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.item_checkout_section_purchase_channel_item;
    }

    @Override
    public void convert(AdapterViewHolder helper, PurchaseChannelSubData item) {
        convert(helper, (PurchaseChannelSubData.ItemData) item);
    }

    public void convert(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item) {
        // R.layout.item_checkout_section_purchase_channel_item;
        if (item.isSingleChannel()) {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_action), item.getExtraData().isChannelSelectable);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_amount), false);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_action), false);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_amount), true);
        }

        if (PaymentKt.isPayChannelCodeCard(item.getChannel().getChannelCode())) {
            convertCardChannel(helper, item);
        } else {
            convertVirtualChannel(helper, item);
        }

        // amount
        String channelAmount = item.getChannel().getChannelAmount();
        if (!EmptyUtils.isEmpty(channelAmount)) {
            double dFirstChannelAmount = DecimalTools.doubleValue(item.getFirstPurchasedAmount(), 0.0);
            double dAmount = DecimalTools.doubleValue(channelAmount, 0.0);
            String sAmount = OrderHelper.formatUSMoney(dAmount);
            // Prepend minus sign for EBT channel if the amount is greater than 0
            if (PaymentKt.isPayChannelCodeEbt(item.getChannel().getChannelCode()) // is EBT channel
                    && dAmount > 0 // purchase amount > 0
                    && dFirstChannelAmount > 0 // first purchase amount > 0
            ) {
                sAmount = "-" + sAmount;
            }
            ((TextView) helper.getView(R.id.tv_amount)).setText(sAmount);
        } else {
            ((TextView) helper.getView(R.id.tv_amount)).setText(null);
        }

        // desc
        String desc = item.getChannel().getDesc();
        if (!EmptyUtils.isEmpty(desc)) {
            ((TextView) helper.getView(R.id.tv_desc)).setText(desc);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_desc), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_desc), false);
        }

        // unavailable
        convertUnavailable(helper, item);

        convertCvcCheck(helper, item);
    }

    private void convertCvcCheck(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item) {
        // R.layout.item_checkout_section_purchase_channel_item;
        Context context = helper.itemView.getContext();
        PaymentChannel channel = item.getChannel();
        boolean isAvailable = channel.isAvailable();
        boolean isCheckCvc = !item.getExtraData().isPointsAllDeducted // 积分未能全额抵扣
                && CheckoutPreAgent.isCheckCvv(channel); // 需要验证 CVC
        boolean showCvcInput = isAvailable && isCheckCvc;
        if (!showCvcInput) {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.cl_cvc_input), false);
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_cvc_error), false);
            return;
        }

        EditText etCvc = helper.getView(R.id.et_cvc);
        etCvc.setText(item.getCvcText());
        etCvc.setHint(context.getString(R.string.enter_cvc));
        etCvc.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                clearFocus(v);
            }
            return false;
        });
        PurchaseChannelExtraData extraData = item.getExtraData();
        boolean invalidCardCvv = extraData.invalidCardCvv;
        updateCvvState(helper, invalidCardCvv);

        etCvc.setOnFocusChangeListener((v, hasFocus) -> {
            EditText et = (EditText) v;
            String cvc = et.getText() != null ? et.getText().toString() : null;
            if (hasFocus) {
                et.setHint(null);
                updateCvvState(helper, false);
            } else {
                et.setHint(R.string.enter_cvc);
                item.setCvcText(cvc);
                if (invalidCardCvv) {
                    validateCvcAgain(helper, item, cvc);
                }
            }
            if (actionListener != null) {
                actionListener.onCardCvvInputFocusChange(v, hasFocus);
            }
        });

        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.cl_cvc_input), true);
    }

    private void validateCvcAgain(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item, String cvcText) {
        boolean invalidCardCvc = CheckoutPreAgent.isInvalidCvc(cvcText);
        item.getExtraData().invalidCardCvv = invalidCardCvc;
        updateCvvState(helper, invalidCardCvc);
    }

    private void updateCvvState(AdapterViewHolder helper, boolean isInvalid) {
        Context context = helper.itemView.getContext();
        EditText etCvc = helper.getView(R.id.et_cvc);
        TextView tvCvcError = helper.getView(R.id.tv_cvc_error);
        String errorMessage = isInvalid
                ? helper.itemView.getContext().getString(R.string.please_enter_valid_cvc_number)
                : helper.itemView.getContext().getString(R.string.confirm_cvc_number);
        if (isInvalid) {
            helper.getView(R.id.cl_cvc_input).setBackgroundResource(R.drawable.shape_bg_purchase_channel_cvv_input_error);
            etCvc.setTextColor(ContextCompat.getColor(context, R.color.color_root_tomato_red_base_7));
            etCvc.setHintTextColor(ContextCompat.getColor(context, R.color.color_root_tomato_red_base_7));
            tvCvcError.setTextColor(ContextCompat.getColor(context, R.color.color_root_tomato_red_base_7));
        } else {
            helper.getView(R.id.cl_cvc_input).setBackgroundResource(R.drawable.shape_bg_purchase_channel_cvv_input);
            etCvc.setTextColor(ContextCompat.getColor(context, R.color.color_primary_5));
            etCvc.setHintTextColor(ContextCompat.getColor(context, R.color.color_surface_500_hairline));
            tvCvcError.setTextColor(ContextCompat.getColor(context, R.color.color_shade_cool_base_5));
        }
        tvCvcError.setText(errorMessage);
        ViewTools.setViewVisibilityIfChanged(tvCvcError, !EmptyUtils.isEmpty(errorMessage));
    }

    private void clearFocus(View view) {
        if (view != null) {
            view.clearFocus();
        }
    }

    private void convertUnavailable(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item) {
        Context context = helper.itemView.getContext();
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.v_mask), !item.getChannel().isAvailable());
        String unavailableReason = item.getChannel().getUnavailableReason();
        if (!EmptyUtils.isEmpty(unavailableReason)) {
            helper.setText(R.id.tv_unavailable_reason, unavailableReason);
            int bgColor;
            int fgColor;
            if ("success".equals(item.getChannel().getUnavailableType())) {
                bgColor = ContextCompat.getColor(context, R.color.color_root_jade_green_light_1);
                fgColor = ContextCompat.getColor(context, R.color.color_root_jade_green_dark_1);
                ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_unavailable_reason), true);
            } else {
                bgColor = ContextCompat.getColor(context, R.color.color_root_durian_yellow_light_1);
                fgColor = ContextCompat.getColor(context, R.color.color_root_durian_yellow_dark_3);
                ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_unavailable_reason), false);
            }
            View linearLayout = helper.getView(R.id.ll_unavailable_reason);
            ShapeHelper.setBackgroundSolidDrawable(linearLayout, bgColor, CommonTools.dp2px(10f));
            helper.setTextColor(R.id.tv_unavailable_reason, fgColor);
            ViewTools.applyIvTintColor(fgColor, helper.getView(R.id.iv_unavailable_reason));
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.ll_unavailable_reason), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.ll_unavailable_reason), false);
        }
    }

    private void convertCardChannel(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item) {
        PaymentChannel channel = item.getChannel();
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_card), true);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_card), true);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_icon), false);

        PaymentChannelDetail detail = channel.getPaymentDetail();
        Context context = helper.itemView.getContext();
        if (detail != null) {
            ((ImageView) helper.getView(R.id.iv_card)).setImageResource(PmHelper.getCardIcon(detail.getCardType()));

            // "Ending in 3706 • Expired"
            Spanny spanny = new Spanny();
            spanny.append(
                    context.getString(R.string.s_card_tail_profile, detail.getTail()),
                    Spans.foregroundColorSpan(context, R.color.color_primary_pepper_black)
            );
            if (detail.isExpired()) {
                spanny.append(
                        context.getString(R.string.s_card_tail_profile_dot),
                        Spans.foregroundColorSpan(context, R.color.color_shade_cool_base_2)
                );
                spanny.append(
                        context.getString(R.string.payment_expired),
                        Spans.foregroundColorSpan(context, R.color.color_root_tomato_red_base_7)
                );
            }
            ((TextView) helper.getView(R.id.tv_card)).setText(spanny);
        }
    }

    private void convertVirtualChannel(AdapterViewHolder helper, PurchaseChannelSubData.ItemData item) {
        PaymentChannel channel = item.getChannel();
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_card), false);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.tv_card), false);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.iv_icon), true);

        ((ImageView) helper.getView(R.id.iv_icon)).setImageResource(PmHelper.getChannelIcon(channel.getChannelCode()));
    }
}
