package com.sayweee.weee.module.post.widget;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.post.bean.VideoDealRightBean;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.widget.component.DrawableTextView;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;

/**
 * Author:  Chuan
 * Email:   <EMAIL>
 * Date:    2022/2/8
 * Desc: https://docs.google.com/document/d/12xdlCEDWUo8djSwNVme5vNAtWQVhvTBjaSk6NJJQsSE/edit
 */
public class VideoDealRightDialog extends WrapperDialog {

    private OnDialogClickListener mListener;
    private DrawableTextView mPinVideo;
    private DrawableTextView mViewInsights, mTvEdit, mTvDelete, mTvReport;
    private VideoDealRightBean mVideoDealRightBean;

    public VideoDealRightDialog(Context context) {
        super(context, R.style.BottomDialogTheme);
    }

    @Override
    public void help(ViewHelper helper) {
        mPinVideo = helper.getView(R.id.tv_pin_video);
        ViewTools.setViewOnClickListener(mPinVideo, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
                if (mListener != null) {
                    mListener.onPinToTopClick(mVideoDealRightBean);
                }
            }
        });
        mViewInsights = helper.getView(R.id.tv_view_insights);
        ViewTools.setViewOnClickListener(mViewInsights, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
                if (mListener != null) {
                    mListener.viewInsight(mVideoDealRightBean.insightsUrl);
                }
            }
        });
        mTvEdit = helper.getView(R.id.tv_edit);
        ViewTools.setViewOnClickListener(mTvEdit, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
                if (mListener != null) {
                    mListener.onEditClick();
                }
            }
        });
        mTvDelete = helper.getView(R.id.tv_delete);
        ViewTools.setViewOnClickListener(mTvDelete, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
                if (mListener != null) {
                    mListener.onDeleteClick();
                }
            }
        });
        mTvReport = helper.getView(R.id.tv_report);
        ViewTools.setViewOnClickListener(mTvReport, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
                if (mListener != null) {
                    mListener.onReportClick();
                }
            }
        });
        ViewTools.setViewOnClickListener(helper.getView(R.id.iv_close), new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                dialog.dismiss();
            }
        });
    }

    public VideoDealRightDialog setData(VideoDealRightBean bean) {
        mVideoDealRightBean = bean;
        handleShowStatus();
        return this;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.dialog_video_detail_bottom;
    }

    @Override
    protected void setDialogParams(Dialog dialog) {
        setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
    }

    public VideoDealRightDialog setOnClickListener(OnDialogClickListener listener) {
        this.mListener = listener;
        return this;
    }

    public interface OnDialogClickListener {
        void onPinToTopClick(VideoDealRightBean videoDealRightBean);

        void viewInsight(String inSightUrl);

        void onEditClick();

        void onDeleteClick();

        void onReportClick();
    }

    private void handleShowStatus() {
        if (mVideoDealRightBean == null) return;
        if (mVideoDealRightBean.isShowPin()) {
            int drawableId = -1;
            int stringId = -1;
            float alpha = 1;
            if (mVideoDealRightBean.isPinTop()) {
                drawableId = R.mipmap.iv_unpin_video;
                stringId = R.string.s_video_unpin;
            } else if (mVideoDealRightBean.isUnpin()) {
                drawableId = R.mipmap.iv_pin_video_top;
                stringId = R.string.s_pin_on_top;
            } else if (mVideoDealRightBean.isPinToLimit()) {
                drawableId = R.mipmap.iv_pin_video_top;
                stringId = R.string.s_pin_on_top;
                alpha = .5f;
            }
            mPinVideo.setAlpha(alpha);
            mPinVideo.setCompoundDrawablesWithIntrinsicBounds(null, ContextCompat.getDrawable(context, drawableId), null, null);
            mPinVideo.setText(stringId);
            mPinVideo.setVisibility(View.VISIBLE);
        } else {
            mPinVideo.setVisibility(View.GONE);
        }
        mViewInsights.setVisibility(mVideoDealRightBean.showInsights && VariantConfig.IS_VIEW_VISIBLE ? View.VISIBLE : View.GONE);
        mTvDelete.setVisibility(mVideoDealRightBean.privilege ? View.VISIBLE : View.GONE);
        mTvEdit.setVisibility(mVideoDealRightBean.privilege ? View.VISIBLE : View.GONE);
        mTvReport.setVisibility(mVideoDealRightBean.userId != AccountManager.get().getUserIdInt() ? View.VISIBLE : View.GONE);
    }
}
