package com.sayweee.weee.module.cart.bean.setcion;

import androidx.annotation.NonNull;

import com.sayweee.core.order.OrderProvider;
import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.adapter.SectionCartAdapter;
import com.sayweee.weee.module.cart.bean.CartFrameUiData;
import com.sayweee.weee.module.cart.bean.IFrameUi;
import com.sayweee.weee.module.cart.bean.NewItemBean;
import com.sayweee.weee.module.cart.bean.NewSectionBean;
import com.sayweee.weee.module.cart.bean.NextUrlBean;
import com.sayweee.weee.module.cart.bean.SaveForLaterResponseBean;
import com.sayweee.weee.module.cart.bean.ServiceFeeItem;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 思想：用数据来自动组装购物车对像
 * 为方便后期维护，尽量拆分item，保证能更灵活的组装
 * titleData：标题数据，包含折叠和展开逻辑
 * remindData：提醒数据，支持多个提醒类型的数据
 * statsData： 统计数据，包含配送信息、订单金额、订单数目等
 * tipsData： 提示数据，比如凑单、换购等，同时兼容活动类型提示数据
 * activityData： 活动数据，活动数据属于二级组装数据，内部有一个独立的Converter
 * productData： 商品数据，包含赠品、普通商品
 * productCollapsedData： 商品折叠样式时的数据
 * emptyData： 数据为空时的数据
 */
public class AdapterCartSectionData extends SimpleSectionCartData implements Serializable {

    public SectionCartTitleData titleData;      //购物车标题
    public SectionCartRemindData remindData;    //购物车提醒数据
    public SectionCartStatsData statsData;      //购物车统计数据
    public SectionCartTipsData tipsData;        //购物车提示数据 凑单、换购、bogo、promotion
    public List<SectionCartActivityData> activityData;      //活动数据
    public List<SectionCartProductData> productData;        //商品数据
    public SectionCartCollapsedData productCollapsedData;   //商品折叠数据
    public SectionCartEmptyData emptyData;   //空数据
    public SectionCartSave4LaterMoreData sectionCartSave4LaterMoreData;//save4later加载更多footer
    private SectionCartCheckoutButtonData sectionCartCheckoutButtonData;//单独结算按钮

    public NewSectionBean targetData;
    protected SaveForLaterResponseBean targetSaveForLaterData;//save4later专用源数据
    protected boolean isCollapsedStatus; //是否折叠状态
    protected boolean isAllInvalid;
    protected boolean displaySave4Later;
    int index;
    public NextUrlBean nextUrlBean;
    protected boolean hasServiceFee;

    public AdapterCartSectionData() {

    }

    public AdapterCartSectionData(SaveForLaterResponseBean targetData, boolean isCollapsedStatus, int index) {
        this.targetSaveForLaterData = targetData;
        this.index = index;
        setCartType(AdapterCartSectionData.TYPE_SAVE_FOR_LATER);
        setCollapsedStatus(isCollapsedStatus);
        calc();
    }

    public AdapterCartSectionData(NewSectionBean targetData, boolean isCollapsedStatus, boolean displaySave4Later, int index) {
        this.targetData = targetData;
        this.displaySave4Later = displaySave4Later;
        this.index = index;
        setCartType(AdapterCartSectionData.TYPE_GROCERY);
        setCollapsedStatus(isCollapsedStatus);
        calc();
    }

    public AdapterCartSectionData(@CartType int cartType, NewSectionBean targetData, int index) {
        this.targetData = targetData;
        this.index = index;
        setCartType(cartType);
        calc();
    }

    @Override
    public void calc() {
        if (isGroceryCart()) {
            parseGroceryData();
        } else if (isSaveForLaterCart()) {
            parseSaveForLaterData();
        } else if (isEmptyCart()) {
            parseEmptyData();
        }
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public List<AdapterDataType> toAdapterData() {
        if (isValid()) {
            if (isGroceryCart()) {
                return packetGroceryData();
            } else if (isSaveForLaterCart()) {
                return packetSaveForLaterData();
            } else if (isEmptyCart()) {
                return packetEmptyData();
            }
        }
        return Collections.emptyList();
    }

    /**********************************************************************************************/

    private void parseGroceryData() {
        if (targetData != null) {
            if (targetData.shipping_info != null) {
                titleData = new SectionCartTitleData(targetData.cart_id, isCollapsedStatus);
                titleData.t = targetData.shipping_info;
                titleData.setImpressionBean(targetData, index);//t2_cart_imp
                titleData.setSellerInfo(targetData.isSeller(), targetData.vendor_info != null ? targetData.vendor_info.vendor_id : 0);
                titleData.setPantryInfo(targetData.isPantry());
            }

            statsData = parseStatsData(targetData);

            if (targetData.shipping_info != null && !EmptyUtils.isEmpty(targetData.shipping_info.shipping_delay_desc)) {
                remindData = new SectionCartRemindData(targetData.shipping_info.shipping_delay_desc);
            }

            if (targetData.shop_more_info != null) {
                if (!targetData.isSeller()) {
                    tipsData = new SectionCartTipsData();
                    tipsData.setShopMoreInfo(targetData.shop_more_info);
                    tipsData.setSectionType(targetData.type);
                } else {
                    tipsData = new SectionCartTipsData(CartSectionType.TYPE_MKPL_SHOP_MORE);
                    tipsData.setShopMoreInfo(targetData.shop_more_info);
                    tipsData.setVendorId(targetData.getVendorIdString());
                    tipsData.setSectionType(targetData.type);
                }
            }

            if (targetData.activity_info != null && !targetData.activity_info.isEmpty()) {
                activityData = new ArrayList<>();
                int activityIndex = 0;
                for (NewSectionBean.ActivityInfo activityInfo : targetData.activity_info) {
                    SectionCartActivityData sectionCartActivityData;
                    sectionCartActivityData = new SectionCartActivityData(targetType, activityInfo, targetData.vendor_info, targetData.shipping_info)
                            .setDisplaySave4Later(displaySave4Later)
                            .setSectionIndex(index)
                            .setActivityIndex(activityIndex++)
                            .setElement(targetData.type)
                            .setCartId(targetData.getCartId())
                            .setSectionType(targetData.type)
                            .setHasServiceFee(hasServiceFee);
                    sectionCartActivityData.calc();
                    String nextUrl = sectionCartActivityData.nextUrl();
                    if (!EmptyUtils.isEmpty(nextUrl)) {
                        nextUrlBean = new NextUrlBean(nextUrl, sectionCartActivityData);
                    }
                    activityData.add(sectionCartActivityData);
                }
            }

            //普通商品
            List<NewItemBean> items = targetData.items;
            if (items != null && !items.isEmpty()) {
                productData = new ArrayList<>();
                for (NewItemBean item : items) {
                    CartFrameUiData cartFrameUiData = new CartFrameUiData(-1
                            , items.indexOf(item) == items.size() - 1 ? SectionCartAdapter.BOTTOM_FRAME : SectionCartAdapter.MIDDLE_FRAME
                            , items.indexOf(item) != 0);
                    SectionCartProductData sectionCartProductData = new SectionCartProductData(CartSectionType.TYPE_PRODUCT, item)
                            .setHotdishInfo(targetData.vendor_info != null ? targetData.vendor_info.vendor_id : 0, targetData.shipping_info.hotdish_wave != null ? targetData.shipping_info.hotdish_wave.wave_seq : null)
                            .setDisplaySave4Later(displaySave4Later)
                            .setTrackElement(EagleTrackManger.get().getElement("cart", 0, targetData.type, index));
                    sectionCartProductData.setFrameUiData(cartFrameUiData);
                    productData.add(sectionCartProductData);
                }
            }

            List<SectionCartProductData> temp = new ArrayList<>();
            if (activityData != null) {
                for (SectionCartActivityData activityDatum : activityData) {
                    addData(temp, activityDatum.getProductData());
                }
            }
            addData(temp, productData);
            if (!temp.isEmpty()) {
                productCollapsedData = new SectionCartCollapsedData().setProductData(temp).setCartId(targetData.cart_id);
            }

            isAllInvalid = !temp.isEmpty();
            for (SectionCartProductData item : temp) {
                if (OrderProvider.get().isValidProduct(item.getData().status)) {
                    isAllInvalid = false;
                    break;
                }
            }
            if (isAllInvalid) {
                //全部无效商品提示
                remindData = new SectionCartRemindData(null);
            }

            //单独结算按钮
            if (targetData.isIndividualCheckout()) {
                SectionCartProductData lastProductData = CollectionUtils.lastOrNull(productData);
                if (lastProductData != null) {
                    lastProductData.uiData.outerFrame = SectionCartAdapter.MIDDLE_FRAME;
                }
                if (productCollapsedData != null) {
                    productCollapsedData.getFrameUiData().outerFrame = SectionCartAdapter.MIDDLE_FRAME;
                }
                sectionCartCheckoutButtonData = new SectionCartCheckoutButtonData(targetData);
                sectionCartCheckoutButtonData.setEagleElement(titleData != null ? titleData.getEagleElement() : null);
            }
        }
    }

    @NonNull
    private SectionCartStatsData parseStatsData(@NonNull NewSectionBean targetData) {
        SectionCartStatsData data = new SectionCartStatsData();
        data.quantity = targetData.quantity;
        data.sectionType = targetData.type;
        data.shippingShipmentDate = targetData.shipping_info != null ? targetData.shipping_info.shipping_shipment_date : null;
        data.isSupportChangeDate = targetData.shipping_info != null && targetData.shipping_info.is_support_change_date;
        data.coldPackageFee = targetData.shipping_info != null ? targetData.shipping_info.cold_package_fee : null;
        data.totalPriceWithActivity = targetData.fee_info != null ? targetData.fee_info.total_price_with_activity : null;
        data.shippingFee = targetData.shipping_info != null ? targetData.shipping_info.shipping_fee : null;
        data.originalShippingFee = targetData.shipping_info != null ? targetData.shipping_info.orignal_shipping_fee : 0.0;
        data.deliveryMode = targetData.shipping_info != null ? targetData.shipping_info.delivery_mode : null;
        data.shippingFeePopupUrl = targetData.shipping_info != null ? targetData.shipping_info.shipping_fee_popup_url : null;

        hasServiceFee = false;
        List<ServiceFeeItem> serviceFeeList = targetData.service_fee_list;
        if (serviceFeeList != null) {
            for (ServiceFeeItem fee : serviceFeeList) {
                if (ServiceFeeItem.TYPE_DELIVERY_SERVICE_FEE.equals(fee.type)) {
                    data.serviceFee = fee.amount;
                    data.originalServiceFee = fee.originalAmount;
                    data.serviceFeePopupUrl = fee.popup_url;
                    hasServiceFee = true;
                } else if (ServiceFeeItem.TYPE_COLD_PACKAGE_FEE.equals(fee.type)) {
                    data.coldPackageFee = fee.amount;
                }
            }
        }

        return data;
    }

    private List<AdapterDataType> packetGroceryData() {
        List<AdapterDataType> list = new ArrayList<>();
        addData(list, titleData);
        if (isAllInvalid) {
            if (isCollapsedStatus) {
                addData(list, remindData);
            }
        } else {
            addData(list, statsData);
            addData(list, remindData);
            addData(list, tipsData); // shop more
        }
        if (isCollapsedStatus) {
            if (activityData != null && !activityData.isEmpty()) {
                for (SectionCartActivityData activityDatum : activityData) {
                    if (activityDatum.isActivityCartDeal()) {
                        addData(list, activityDatum.tipsData);//收起状态加入新版本活动模块:68换购
                    } else if (activityDatum.isActivityTradeInNew()) {
                        addData(list, activityDatum.tipsData);//收起状态加入新版本活动模块
                    } else if (activityDatum.isActivityFreeShipping()) {
                        if (targetData != null && targetData.isPresale()) {
                            addData(list, activityDatum.tipsData);
                        } else {
                            if (activityDatum.targetData.diff_amount > 0) {
                                // 未免邮
                                addData(list, activityDatum.tipsData);
                            }
                        }
                    }
                }
            }
            addData(list, productCollapsedData);
            addData(list, sectionCartCheckoutButtonData);
        } else {
            if (activityData != null && !activityData.isEmpty()) {
                for (SectionCartActivityData activityDatum : activityData) {
                    addData(list, activityDatum.toAdapterData());
                }
            }
            if (CollectionUtils.lastOrNull(list) instanceof SectionCartProductData) {
                SectionCartProductData previous = (SectionCartProductData) CollectionUtils.lastOrNull(list);
                if (previous.isActivityCartDeal() && CollectionUtils.firstOrNull(productData) != null) {
                    SectionCartProductData first = CollectionUtils.firstOrNull(productData);
                    if (first.getFrameUiData() != null) {
                        first.getFrameUiData().hasTopDivider = false;
                    }
                }
            }
            addData(list, productData);
            addData(list, sectionCartCheckoutButtonData);
        }
        attachBottomFrame(list);
        return list;
    }

    private void attachBottomFrame(List<AdapterDataType> list) {
        if (list != null && !list.isEmpty()) {
            AdapterDataType cartSectionEnd = list.get(list.size() - 1);
            if (cartSectionEnd instanceof IFrameUi) {
                CartFrameUiData uiData = ((IFrameUi) cartSectionEnd).getFrameUiData();
                uiData.outerFrame = SectionCartAdapter.BOTTOM_FRAME;
            }
        }
    }

    /**********************************************************************************************/

    private void parseSaveForLaterData() {
        if (targetSaveForLaterData != null && targetSaveForLaterData.items != null && targetSaveForLaterData.items.size() > 0) {
            List<NewItemBean> items = targetSaveForLaterData.items;
            titleData = new SectionCartTitleData(targetSaveForLaterData.getCartId(), isCollapsedStatus);
            titleData.setOnlyTitle(targetSaveForLaterData.total_count > 1 ? R.string.save_for_later_items : R.string.save_for_later_item, targetSaveForLaterData.total_count);
            titleData.setSaveForLaterImpressionBean(targetSaveForLaterData, index);
            titleData.setIsSave4Later(true);

            productData = new ArrayList<>();
            for (NewItemBean item : items) {
                productData.add(new SectionCartProductData(CartSectionType.TYPE_PRODUCT_SAVE_4_LATER, item)
                        .setDisplaySave4Later(true).setTrackElement(EagleTrackManger.get().getElement("cart", 0, "save_for_later", index)));
            }
            productCollapsedData = new SectionCartCollapsedData().setProductData(productData).setCartId(targetSaveForLaterData.getCartId()).setIsSave4Later(true);
            //save4later是否有加载更多footer
            if (targetSaveForLaterData.total_count > items.size()) {
                sectionCartSave4LaterMoreData = new SectionCartSave4LaterMoreData(items.size(), targetSaveForLaterData.page_size);
            }
        }
    }

    private List<AdapterDataType> packetSaveForLaterData() {
        List<AdapterDataType> list = new ArrayList<>();
        addData(list, titleData);
        if (isCollapsedStatus) {
            addData(list, productCollapsedData);
        } else {
            addData(list, productData);
            if (sectionCartSave4LaterMoreData != null) {
                addData(list, sectionCartSave4LaterMoreData);
            }
        }
        attachBottomFrame(list);
        return list;
    }

    /**********************************************************************************************/

    private void parseEmptyData() {
        emptyData = new SectionCartEmptyData();
    }

    private List<AdapterDataType> packetEmptyData() {
        List<AdapterDataType> list = new ArrayList<>();
        addData(list, emptyData);
        return list;
    }

    /**********************************************************************************************/

    public AdapterCartSectionData setCollapsedStatus(boolean status) {
        this.isCollapsedStatus = status;
        return this;
    }
}
