package com.sayweee.weee.module.product.provider;

import android.widget.EditText;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.ISectionProvider;
import com.sayweee.weee.module.base.adapter.SimpleItemAdapter;
import com.sayweee.weee.module.base.adapter.payload.RecyclerItemVisiblePositions;
import com.sayweee.weee.module.cate.product.bean.AdapterProductGroupData;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.mkpl.LabelScrollAdapter;
import com.sayweee.weee.module.mkpl.feed.CmsContentFeedProvider;
import com.sayweee.weee.module.post.bean.PostCategoryBean;
import com.sayweee.weee.module.product.adapter.OnAiQuestionListener;
import com.sayweee.weee.module.product.bean.PdpItemBean;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpSummaryBean;
import com.sayweee.weee.module.product.bean.PdpToCheckoutBean;
import com.sayweee.weee.module.product.data.PdpAiQuestionData;
import com.sayweee.weee.module.product.data.PdpGiftCardBannerData;
import com.sayweee.weee.module.product.data.PdpGiftCardContentData;
import com.sayweee.weee.module.product.data.PdpGiftCardThemeData;
import com.sayweee.weee.module.product.data.PdpGlobalData;
import com.sayweee.weee.module.product.data.PdpItemData;
import com.sayweee.weee.module.product.data.PdpPostData;
import com.sayweee.weee.module.product.data.PdpProductBannerData;
import com.sayweee.weee.module.product.data.PdpProductGroupData;
import com.sayweee.weee.module.product.data.PdpProductsData;
import com.sayweee.weee.module.product.data.PdpSummaryData;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.widget.banner.ex.PlayerTriggerAdapter;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class PdpItemAdapter extends SimpleItemAdapter<AdapterDataType, AdapterViewHolder>
        implements PlayerTriggerAdapter,
        LabelScrollAdapter {

    private final String playerKey;
    private PdpGiftCardContentProvider.OnPreviewClick listener;
    private PdpSummaryProvider.OnSummaryClickListener summaryClickListener;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;

    public PdpItemAdapter(String playerKey, @Nullable RecyclerViewScrollStatePersist scrollStatePersist) {
        this.playerKey = playerKey;
        this.scrollStatePersist = scrollStatePersist;
    }

    @Override
    protected void addAdapterProvider() {
        super.addAdapterProvider();
        addItemProvider(new PdpBannerProvider());
//        addItemProvider(new PdpProductInfoProvider());
//        addItemProvider(new PdpBnplProvider()); //bnpl展示
//        addItemProvider(new PdpSellingPointsProvider()); //卖点
        addItemProvider(new PdpGlobalProvider()); //
        addItemProvider(new PdpProductGroupProvider());
        addItemProvider(new PdpNewProductGroupProvider());
        addItemProvider(new PdpAiQuestionProvider());
        addItemProvider(new PdpOldReviewProvider()); //review old
        addItemProvider(new PdpRecommendProvider()); // mkpl 推荐商品
        addItemProvider(new PdpProductDetailProvider()); //商品详情webview
        addItemProvider(new PdpOftenPairedWithProvider());//一件加购模块
        addItemProvider(new PdpProductLineProvider());//横向商品list模块
        addItemProvider(new PdpEmptyProvider());//非全数据时骨架效果
        addItemProvider(new PdpNoProductEmptyProvider()); //骨架效果
        addItemProvider(new PdpBlankProvider());
//        addItemProvider(new PdpSkuCouponProvider());
        addItemProvider(new PdpItemProvider()); // fbw

        // gift card
        addItemProvider(new PdpTitleProvider()); //标题
        addItemProvider(new PdpGiftCardBannerProvider());
        addItemProvider(new PdpGiftCardThemeProvider());
        addItemProvider(new PdpGiftCardGroupProvider());
        addItemProvider(new PdpGiftCardDescProvider());

        // redesign
        addItemProvider(new PdpBarInfoProvider());
        addItemProvider(new PdpTagProvider());
        addItemProvider(new PdpQuickLinksProvider());
        addItemProvider(new PdpCarouselProvider().setOnProviderCallback(this::getItem));


    }

    @Nullable
    @Override
    protected SectionProviderFactory getSectionProviderFactory() {
        return new SectionProviderFactory(super.getSectionProviderFactory()) {
            @Nullable
            @Override
            public ISectionProvider getItemProvider(int viewType) {
                ISectionProvider provider;
                if (viewType == PdpItemType.PDP_PRODUCT_POST) {
                    provider = new PdpPostProvider(playerKey);
                } else if (viewType == PdpItemType.PDP_SUMMARY) {
                    // redesign
                    PdpSummaryProvider summaryProvider = new PdpSummaryProvider(playerKey);
                    summaryProvider.setOnSummaryClickListener(summaryClickListener);
                    provider = summaryProvider;
                } else if (viewType == CmsItemType.CONTENT_FEED) {
                    provider = new CmsContentFeedProvider(OnIndicatorClickListener.EMPTY, getSupportManager());
                } else if (viewType == PdpItemType.PDP_GIFT_CONTENT) {
                    PdpGiftCardContentProvider p = new PdpGiftCardContentProvider();
                    p.setOnPreviewClick(listener);
                    provider = p;
                } else {
                    provider = parentFactory != null ? parentFactory.getItemProvider(viewType) : null;
                }
                return provider;
            }
        };
    }

    @Override
    public void onPageResume(RecyclerView view) {
        super.onPageResume(view);
        notifyOftenPairedWith();
    }

    public ISectionProvider getPdpBannerProvider() {
        return getItemProvider(PdpItemType.PDP_BANNER);
    }

    public ISectionProvider getPdpTagProvider() {
        return getItemProvider(PdpItemType.PDP_TAG);
    }

    public ISectionProvider getPdpAiQuestionProvider() {
        return getItemProvider(PdpItemType.PDP_AI_QUESTION);
    }

    public ISectionProvider getPdpQuickLinksProvider() {
        return getItemProvider(PdpItemType.PDP_QUICK_LINKS);
    }

    public ISectionProvider getPdpProductGroupProvider() {
        return getItemProvider(PdpItemType.PDP_NEW_PRODUCT_PRODUCT_GROUPING);
    }

    public void setBannerTracker() {
        ISectionProvider pdpBannerProvider = getItemProvider(PdpItemType.PDP_BANNER);
        if (pdpBannerProvider instanceof PdpBannerProvider) {
            ((PdpBannerProvider) pdpBannerProvider).setTracker(tracker);
        }
    }

    public EditText getGiftEditText() {
        ISectionProvider pdpGiftContentProvider = getItemProvider(PdpItemType.PDP_GIFT_CONTENT);
        if (pdpGiftContentProvider instanceof PdpGiftCardContentProvider) {
            return ((PdpGiftCardContentProvider) pdpGiftContentProvider).getEditText();
        }
        return null;
    }

    public PdpItemAdapter setOnPreviewListener(PdpGiftCardContentProvider.OnPreviewClick listener) {
        this.listener = listener;
        return this;
    }

    public void scrollToTop() {
        ISectionProvider<?, ?> provider = providers.get(CmsItemType.CONTENT_FEED);
        if (provider instanceof CmsContentFeedProvider) {
            ((CmsContentFeedProvider) provider).scrollToTop(true);
        }
    }

    @Override
    public void notifyItemPlayByPosition(int start, int end, int status) {
        ISectionProvider provider = getPdpBannerProvider();
        if (provider instanceof PlayerTriggerAdapter) {
            ((PlayerTriggerAdapter) provider).notifyItemPlayByPosition(start, end, status);
        }
    }

    public void notifyCollectsChange() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpProductsData) {
                notifyItemChanged(mData.indexOf(data), PayloadKey.COLLECT);
            }
        }
    }

    public void notifyOftenPairedWith() {
        for (AdapterDataType mDatum : mData) {
            if (mDatum instanceof PdpProductsData && (PdpItemType.OFTEN_PAIRED_WITH == mDatum.getType())) {
                notifyItemChanged(mData.indexOf(mDatum), PayloadKey.CART_QTY);
            }
        }
    }

    public void notifyAlcoholDataChanged(String desc) {
        if (desc != null && desc.trim().length() > 0) {
            int count = getItemCount();
            for (int i = 0; i < count; i++) {
                AdapterDataType item = getItem(count);
                if (item instanceof PdpItemData) {
                    PdpItemBean bean = ((PdpItemData) item).t;
                    if (bean.isAlcohol()) {
                        bean.title = desc;
                        bean.needItemClick = true;
                        notifyItemChanged(i);
                        break;
                    }
                }
            }
        }
    }

    public void setOnPdpBannerContentListener(PdpBannerProvider.OnPdpBannerClickListener listener) {
        ISectionProvider provider = getPdpBannerProvider();
        if (provider instanceof PdpBannerProvider) {
            ((PdpBannerProvider) provider).setOnItemContentListener(listener);
        }
    }

    public void setOnTagListener(PdpTagProvider.OnTagClick listener) {
        ISectionProvider provider = getPdpTagProvider();
        if (provider instanceof PdpTagProvider) {
            ((PdpTagProvider) provider).setOnTagListener(listener);
        }
    }

    public void setOnAiQuestionListener(OnAiQuestionListener listener) {
        ISectionProvider provider = getPdpAiQuestionProvider();
        if (provider instanceof PdpAiQuestionProvider) {
            ((PdpAiQuestionProvider) provider).setOnAiQuestionListener(listener);
        }
    }

    public void setOnQuickLinksListener(PdpQuickLinksProvider.OnTabClickListener listener) {
        ISectionProvider provider = getPdpQuickLinksProvider();
        if (provider instanceof PdpQuickLinksProvider) {
            ((PdpQuickLinksProvider) provider).setOnTabClickListener(listener);
        }
    }

    public void setOnProductGroupListener(PdpNewProductGroupProvider.OnProductGroupListener listener) {
        ISectionProvider provider = getPdpProductGroupProvider();
        if (provider instanceof PdpNewProductGroupProvider) {
            ((PdpNewProductGroupProvider) provider).setOnProductGroupListener(listener);
        }
    }

    public void setOnProductSummaryListener(PdpSummaryProvider.OnSummaryClickListener listener) {
        this.summaryClickListener = listener;
    }


    public void notifyButtonText(String text) {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGlobalData) {
                ((PdpGlobalData) data).buttonText = text;
                notifyItemChanged(mData.indexOf(data), data);
                break;
            }
        }
    }

    public PdpToCheckoutBean getCheckOutData(int productId, int themeId) {
        PdpToCheckoutBean pdpToCheckoutBean = new PdpToCheckoutBean();
        pdpToCheckoutBean.product_id = productId;
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardContentData) {
                PdpGiftCardContentData item = ((PdpGiftCardContentData) data);
                pdpToCheckoutBean.message = item.message;
                pdpToCheckoutBean.quantity = item.quantity;
                pdpToCheckoutBean.recipient_email = item.recipient_email;
                pdpToCheckoutBean.send_date = item.sendDate;
                pdpToCheckoutBean.sender_name = item.sender_name;
                pdpToCheckoutBean.theme_id = themeId;
                return pdpToCheckoutBean;
            }
        }
        return null;
    }

    public void notifyGiftMessage(String msg, boolean messageIsChanged) {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardContentData) {
                PdpGiftCardContentData item = ((PdpGiftCardContentData) data);
                if (item.msgCanChanged) {
                    item.message = msg;
                    notifyItemChanged(mData.indexOf(data), data);
                }
                break;
            }
        }
    }

    public void notifyProductGroup(List<AdapterProductGroupData> groupData) {
        int i = -1;
        for (AdapterDataType data : mData) {
            if (data instanceof PdpProductGroupData) {
                PdpProductGroupData item = ((PdpProductGroupData) data);
                if (item.groupData != null) {
                    i++;
                    item.canClick = false;
                    item.groupData = groupData.get(i);
                    notifyItemChanged(mData.indexOf(data), data);
                }
            }
        }

    }

    public String getGiftMessage() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardContentData) {
                PdpGiftCardContentData item = ((PdpGiftCardContentData) data);
                return item.message;
            }
        }
        return null;
    }

    public String getGiftCategoryName() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardThemeData) {
                PdpGiftCardThemeData item = ((PdpGiftCardThemeData) data);
                return item.categoryName;
            }
        }
        return null;
    }

    public String getGiftLanguageName() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardThemeData) {
                PdpGiftCardThemeData item = ((PdpGiftCardThemeData) data);
                return item.languageName;
            }
        }
        return null;
    }

    public int getGiftThemePosition() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardThemeData) {
                PdpGiftCardThemeData item = ((PdpGiftCardThemeData) data);
                return item.picIndex;
            }
        }
        return 0;
    }

    public boolean getMessageChangeStatus() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardContentData) {
                PdpGiftCardContentData item = ((PdpGiftCardContentData) data);
                return item.msgCanChanged;
            }
        }
        return true;
    }

    public void notifyGiftCardBanner(String themeCoverImg) {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpGiftCardBannerData) {
                PdpGiftCardBannerData item = ((PdpGiftCardBannerData) data);
                if (!Objects.equals(item.themeCoverImg, themeCoverImg)) {
                    item.themeCoverImg = themeCoverImg;
                    notifyItemChanged(mData.indexOf(data), data);
                }
                break;
            }
        }
    }

    public void notifyCartButtonData(String tips) {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpProductBannerData) {
                ((PdpProductBannerData) data).tips = tips;
                notifyItemChanged(mData.indexOf(data), data);
                break;
            }
        }
    }

    public void notifyAiQuestion() {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpAiQuestionData) {
                notifyItemChanged(mData.indexOf(data));
                break;
            }
        }
    }

    public void notifyCartButtonNum(int num) {
        for (AdapterDataType data : mData) {
            if (data instanceof PdpProductBannerData) {
                notifyItemChanged(mData.indexOf(data), data);
                break;
            }
        }
    }

    public void setOnPdpCouponActionListener(PdpSkuCouponProvider.OnPdpCouponActionListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(PdpItemType.PDP_PRODUCT_SKU_COUPON);
        if (provider instanceof PdpSkuCouponProvider) {
            ((PdpSkuCouponProvider) provider).setOnPdpCouponActionListener(listener);
        }
    }

    public void setOnPdpGiftCardThemeActionListener(PdpGiftCardThemeProvider.OnPdpGiftCardThemeActionListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(PdpItemType.PDP_GIFT_CARD_THEME);
        if (provider instanceof PdpGiftCardThemeProvider) {
            ((PdpGiftCardThemeProvider) provider).setOnPdpGiftCardThemeActionListener(listener);
        }
    }

    public void setOnPdpGiftCardGroupActionListener(PdpGiftCardGroupProvider.OnPdpProductGroupTiledActionListener listener) {
        ISectionProvider<?, ?> provider = getItemProvider(PdpItemType.PDP_PRODUCT_GIFT_CARD_GROUPING);
        if (provider instanceof PdpGiftCardGroupProvider) {
            ((PdpGiftCardGroupProvider) provider).setOnPdpGiftCardGroupActionListener(listener);
        }
    }

    @Override
    public void notifyItemScrollByPosition(int start, int end) {
        RecyclerItemVisiblePositions positions = RecyclerItemVisiblePositions.of(start, end);
        notifyItemRangeChanged(0, getItemCount(), positions);
    }

    public void toggleCollect(Map<String, Serializable> map) {
        int id = 0;
        Boolean status = null;
        Integer count = null;
        Serializable reviewIdObj = map.get("reviewId");
        if (reviewIdObj instanceof Integer) {
            id = (int) reviewIdObj;
        }
        Serializable statusObj = map.get("status");
        if (statusObj instanceof Boolean) {
            status = (Boolean) statusObj;
        }
        Serializable countObj = map.get("count");
        if (countObj instanceof Integer) {
            count = (Integer) countObj;
        }

        if (id != 0 && status != null && count != null) {
            for (AdapterDataType item : mData) {
                if (item instanceof PdpPostData) {
                    PostCategoryBean categoryBean = ((PdpPostData) item).t;
                    for (PostCategoryBean.ListBean bean : categoryBean.list) {
                        if (bean != null && bean.id == id) {
                            bean.is_set_like = status;
                            bean.like_count = count;
                            break;
                        }
                    }
                }
                if (item instanceof PdpSummaryData) {
                    PdpSummaryBean summaryBean = ((PdpSummaryData) item).t;
                    for (PostCategoryBean.ListBean bean : summaryBean.contents) {
                        if (bean != null && bean.id == id) {
                            bean.is_set_like = status;
                            bean.like_count = count;
                            break;
                        }
                    }
                }
            }
        }
    }

    public void trendingPostDataUpdated(ArrayMap<String, Integer> map) {
        for (int i = 0; i < map.size(); i++) {
            String uid = map.keyAt(i);
            if (EmptyUtils.isEmpty(uid)) {
                return;
            }
            Integer status = map.get(uid);
            if (status == null) {
                return;
            }
            List<AdapterDataType> dataList = mData;
            for (int index = 0; index < dataList.size(); index++) {
                AdapterDataType item = dataList.get(index);
                if (item instanceof PdpPostData) {
                    PostCategoryBean categoryBean = ((PdpPostData) item).t;
                    for (PostCategoryBean.ListBean bean : categoryBean.list) {
                        if (bean != null && String.valueOf(bean.uid).equals(uid)) {
                            bean.setFollowStatus(status);
                            break;
                        }
                    }
                }
                if (item instanceof PdpSummaryData) {
                    PdpSummaryBean summaryBean = ((PdpSummaryData) item).t;
                    for (PostCategoryBean.ListBean bean : summaryBean.contents) {
                        if (bean != null && String.valueOf(bean.uid).equals(uid)) {
                            bean.setFollowStatus(status);
                            break;
                        }
                    }
                }
            }
        }
    }

    public FragmentManager getSupportManager() {
        FragmentManager manager = null;
        if (mContext instanceof FragmentActivity) {
            manager = ((FragmentActivity) mContext).getSupportFragmentManager();
        }
        return manager;
    }

    public void destroyBanner() {
        ISectionProvider<?, ?> provider = getItemProvider(PdpItemType.PDP_BANNER);
        if (provider instanceof PdpBannerProvider) {
            ((PdpBannerProvider) provider).destroy();
        }
    }


    public void resetBanner() {
        ISectionProvider<?, ?> provider = getItemProvider(PdpItemType.PDP_BANNER);
        if (provider instanceof PdpBannerProvider) {
            ((PdpBannerProvider) provider).resetBanner();
        }
    }

}
