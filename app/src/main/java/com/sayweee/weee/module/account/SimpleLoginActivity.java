package com.sayweee.weee.module.account;

import com.sayweee.storage.WStore;
import com.sayweee.weee.global.manager.DeepLinkManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.module.MainActivity;
import com.sayweee.weee.module.account.bean.LoginBean;
import com.sayweee.weee.module.account.service.AccountHelper;
import com.sayweee.weee.module.launch.ZipCodeInputActivity;
import com.sayweee.weee.module.launch.service.OnboardingHelper;
import com.sayweee.weee.module.launch.service.ReferrerManager;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.model.ILoaderModel;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

public abstract class SimpleLoginActivity<VM extends BaseViewModel<? extends ILoaderModel>> extends WrapperMvvmActivity<VM> {
    protected static final String KEY_LAST_LOGIN_NAME = "last_login_name";
    protected static final String KEY_LAST_LOGIN_SUCCESS_NAME = "last_login_success_name";

    protected void onLoginSuccess(LoginBean data, boolean lackOrder, String account, String channel) {
        boolean isOnlyBindMobile = !data.is_binded;
        boolean toBind = data.is_need_bind == 1;
        boolean isNewUser = data.is_new_user;
        //当前请求订单信息失败或者order manager中缺少订单信息
        //在每次登陆成功时会重新获取订单信息
        boolean isOnboarding = OnboardingHelper.get().isInOnboarding();
        boolean lackOfOrder = lackOrder || OrderManager.get().isLackOfOrder();
        boolean lackOfStore = StoreManager.get().lackOfStore();
        if (!EmptyUtils.isEmpty(data.app_next_url)) {
            if (lackOfStore) {//优先选择store页面
                DeepLinkManager.get().setCurrentLink(data.app_next_url);
            } else {
                //需要跳转到web页面
                boolean decide = ReferrerManager.get().decideRouting();
                if (decide) {
                    DeepLinkManager.get().processUrlIfNeedSetCurrentLink(data.app_next_url);
                }
            }
        }
        String popupAction = PopupCenterManager.ACTION_LOGIN_REGISTER_SUCCESS;
        boolean isTriggerPopup = PopupCenterManager.get().isTriggerCheck(popupAction);

        if (!isTriggerPopup && (isOnlyBindMobile || toBind)) {
            startActivity(AccountBindActivity.getIntent(activity, false, isOnlyBindMobile, data.bindTitle, data.bindDesc, channel, isOnboarding, isNewUser, lackOfOrder));
        } else {
            if (lackOfOrder || (isOnboarding && isNewUser)) { //2022年4月14日修正为新注册用户逻辑
                //需要输入zipCode 重新获取订单信息
                //如果还是onboarding流程 把邮箱带入
                startActivity(ZipCodeInputActivity.getIntent(activity, account, isOnboarding, isNewUser));
            } else {
                if (isOnboarding) {//从launch流程
                    startActivity(MainActivity.getIntent(activity));
                }
            }
        }

        if (isTriggerPopup) {
            PopupCenterManager.get().execTriggerAction(popupAction);
        }
        ReferrerManager.get().onReferrerFinish();
        OnboardingHelper.get().setGuided();
        OnboardingHelper.get().setInOnboarding(false);
        SharedViewModel.get().loginStatusData.postValue(true);
        setResult(RESULT_OK);
        AccountHelper.finishAccountFlow();
        //save last login success name
        WStore.mmkv(MMKVManager.ID_CONFIG).edit()
                .putString(KEY_LAST_LOGIN_SUCCESS_NAME, getLastLoginName())
                .apply();
    }

    protected void saveLastLogin(String name) {
        WStore.mmkv(MMKVManager.ID_CONFIG).edit()
                .putString(KEY_LAST_LOGIN_NAME, name)
                .apply();
    }

    protected String getLastLoginName() {
        return WStore.mmkv(MMKVManager.ID_CONFIG).getString(KEY_LAST_LOGIN_NAME, "");
    }

    protected String getLastLoginSuccessName() {
        return WStore.mmkv(MMKVManager.ID_CONFIG).getString(KEY_LAST_LOGIN_SUCCESS_NAME, "");
    }
}
