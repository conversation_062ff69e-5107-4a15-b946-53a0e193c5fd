package com.sayweee.weee.module.launch.service;

import android.app.ActivityManager;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;

import com.sayweee.service.ConfigService;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.launch.bean.AppIconBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.utils.PreferenceUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  Chuan
 * Email:   <EMAIL>
 * Date:    2022/1/17
 * Desc: 详情查看https://github.com/myinnos/AppIconNameChanger
 */
@SuppressWarnings({"squid:S6548"})
public final class AppIconManager {

    private static final String TAG = "AppIconManager";
    private static final String APP_ICON_CONFIG = "app_icon_key_config";// AppIcon更新
    private static final String LANGUAGE_ICON = "language_icon"; // 语言对应的Icon
    // 服务端定义的标识名
    static final String FLAG_DEFAULT_ICON = "default_icon";

    // Weee
    @Deprecated
    static final String FLAG_SPRING_FESTIVAL_ICON = "2022_chinese_new_year";
    @Deprecated
    static final String FLAG_7_ANNIVERSARY_ICON = "7th_anniversary";
    @Deprecated
    static final String FLAG_CHRISTMAS_2023 = "2023_christmas";
    @Deprecated
    static final String FLAG_NEW_YEAR_2024 = "2024_new_year";
    @Deprecated
    static final String FLAG_2024_SPRING = "2024_spring";
    @Deprecated
    static final String FLAG_2024_AAPI = "2024_aapi";
    @Deprecated
    static final String FLAG_9_ANNIVERSARY_ICON = "9th_anniversary";
    @Deprecated
    static final String FLAG_2024_MID_AUTUMN_FESTIVAL = "2024_mid_autumn_festival";
    @Deprecated
    static final String FLAG_2024_THANKSGIVING = "2024_thanksgiving";
    @Deprecated
    static final String FLAG_2024_HOLIDAY_SEASON = "2024_holiday_season";
    @Deprecated
    static final String FLAG_2025_NEW_YEAR = "2025_new_year";
    @Deprecated
    static final String FLAG_2025_AAPI = "2025_aapi";
    static final String FLAG_2025_10TH_ANNI = "10th_anniversary";
    static final String FLAG_2025_MID_AUTUMN_FESTIVAL = "2025_mid_autumn_festival";

    // Latino
    // TBD

    // 客户端对应的别名
    private static final String FLAG_DEFAULT_ACTIVITY_ALIAS = "com.sayweee.weee.module.launch.SplashActivity";
    private static final String FLAG_ICON_PLACEHOLDER_ALIAS = "com.sayweee.weee.IconPlaceholder";
    private static final String FLAG_ICON_PLACEHOLDER_ALIAS_2 = "com.sayweee.weee.IconPlaceholder2";

    private final IAppIconConfig config;
    private final List<Icon> mIcons;

    @SuppressWarnings("squid:S3824")
    private AppIconManager(IAppIconConfig config) {
        this.config = config;

        // 添加所有图标列表
        List<Icon> icons = new ArrayList<>();
        icons.add(new Icon(FLAG_DEFAULT_ICON, FLAG_DEFAULT_ACTIVITY_ALIAS));
        String newIcon = config.getNewIcon();
        icons.add(new Icon(newIcon, FLAG_ICON_PLACEHOLDER_ALIAS));
        String newIcon2 = config.getNewIcon2();
        icons.add(new Icon(newIcon2, FLAG_ICON_PLACEHOLDER_ALIAS_2));
        mIcons = icons;
    }

    public static AppIconManager get() {
        return Builder.instance;
    }

    private static final class Builder {
        private static final AppIconManager instance = new AppIconManager(new AppIconConfig());
    }

    public void upgradeIconStyle() {
        Map<String, AppIconBean> iconMap;
        iconMap = (Map<String, AppIconBean>) ConfigService.get().getServiceConfigs().get(VariantConfig.CONFIG_APP_ICON_KEY);
        if (DevConfig.isDebug()) {
            return;
        }
        if (iconMap == null) {
            //接口报错或者没请求到
            return;
        }
        String lastIcon = getAppIconPreferences().getString(LANGUAGE_ICON, FLAG_DEFAULT_ICON);
        if (!EmptyUtils.isEmpty(iconMap)) {
            //不为空进行是否更新icon的判断
            String currentIcon = null;
            String language = LanguageManager.get().getLanguage();
            AppIconBean activeIcon = iconMap.get(language);
            if (activeIcon != null && activeIcon.start > 0 && activeIcon.end > activeIcon.start) {
                long localTime = System.currentTimeMillis();
                if (DateUtils.convertServerTime(activeIcon.start) <= localTime && localTime <= DateUtils.convertServerTime(activeIcon.end)) {
                    currentIcon = activeIcon.icon;
                }
            }
            if (currentIcon != null) {
                if (currentIcon.equalsIgnoreCase(lastIcon)) {
                    // 已经修改过Icon图标，下次不需要进行修改
                    return;
                }
                final String ic = currentIcon;
                Icon icon = CollectionUtils.firstOrNull(mIcons, it -> ic.equalsIgnoreCase(it.key));
                if (icon != null) {
                    applyIconUpgrade(currentIcon, icon.alias);
                    return;
                }
            }
        }
        //为空、未返回icon或者返回的icon标识不支持，更换回原icon
        if (!FLAG_DEFAULT_ICON.equalsIgnoreCase(lastIcon)) {
            applyIconUpgrade(FLAG_DEFAULT_ICON, FLAG_DEFAULT_ACTIVITY_ALIAS);
        }
    }

    private void applyIconUpgrade(String key, String alias) {
        try {
            getAppIconPreferences().edit().putString(LANGUAGE_ICON, key).apply();
            Application application = LifecycleProvider.get().getApplication();
            PackageManager manager = application.getPackageManager();
            // enable在前
            manager.setComponentEnabledSetting(
                    new ComponentName(DevConfig.APPLICATION_ID, alias),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    PackageManager.DONT_KILL_APP);
            for (Icon icon : mIcons) {
                if (alias.equalsIgnoreCase(icon.alias)) {
                    continue;
                }
                manager.setComponentEnabledSetting(
                        new ComponentName(DevConfig.APPLICATION_ID, icon.alias),
                        PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                        PackageManager.DONT_KILL_APP);
            }
            restartSystemLauncher(application, manager);
        } catch (Exception ignored) {
            // ignored.printStackTrace();
        }
    }

    private SharedPreferences getAppIconPreferences() {
        return PreferenceUtils.getSharedPreferences(APP_ICON_CONFIG);
    }

    private void restartSystemLauncher(Context context, PackageManager pm) {
        try {
            ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            Intent i = new Intent(Intent.ACTION_MAIN);
            i.addCategory(Intent.CATEGORY_HOME);
            i.addCategory(Intent.CATEGORY_DEFAULT);
            List<ResolveInfo> resolves = pm.queryIntentActivities(i, 0);
            for (ResolveInfo res : resolves) {
                if (res.activityInfo != null && am != null) {
                    am.killBackgroundProcesses(res.activityInfo.packageName);
                }
            }
        } catch (Exception ignored) {
            // no op
        }
    }

    public void toggleDefault() {
        applyIconUpgrade(FLAG_DEFAULT_ICON, FLAG_DEFAULT_ACTIVITY_ALIAS);
    }

    public void toggleNewIcon() {
        applyIconUpgrade(config.getNewIcon(), FLAG_ICON_PLACEHOLDER_ALIAS);
    }

    public void toggleNewIcon2() {
        applyIconUpgrade(config.getNewIcon2(), FLAG_ICON_PLACEHOLDER_ALIAS_2);
    }

    protected static class Icon {
        public final String key;
        public final String alias;

        public Icon(String key, String alias) {
            this.key = key;
            this.alias = alias;
        }
    }
}
