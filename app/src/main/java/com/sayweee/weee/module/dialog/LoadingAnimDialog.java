package com.sayweee.weee.module.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.sayweee.weee.R;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/21.
 * Desc:
 */
public class LoadingAnimDialog extends WrapperDialog {

    private final int DIALOG_SIZE;
    private boolean isFinishing = false;

    public LoadingAnimDialog(Context context) {
        this(context, R.style.LottieDialogTheme);
    }

    public LoadingAnimDialog(Context context, int themeResId) {
        super(context, themeResId);
        // Any size bigger than animation view size is ok
        // This will fix the issue that sliding enter or exit animation is clipped
        DIALOG_SIZE = context.getResources().getDimensionPixelOffset(R.dimen.prop_size_loading_anim_parent);
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.dialog_loading_compat;
    }

    @Override
    protected void setDialogParams(Dialog dialog) {
        dialog.setCanceledOnTouchOutside(false);
        dialog.setOnShowListener(this::onDialogShow);
    }

    @Override
    public void help(ViewHelper helper) {

    }


    public void onDialogShow(DialogInterface dialog) {
        int size = DIALOG_SIZE;//CommonTools.dp2px(120);//context.getResources().getDimensionPixelOffset(R.dimen.prop_size_loading_anim_in_screen);
        setDialogParams((Dialog) dialog, size, size, Gravity.CENTER);
    }

    @Override
    public WrapperDialog show() {
        final LottieAnimationView loadingView = helper.getView(R.id.loading);
        if (loadingView != null) {
            loadingView.postDelayed(() -> {
                if (!isFinishing) {
                    showAndPlayLoading(loadingView);
                }
            }, 300L);
        }
        return super.show();
    }

    @Override
    public WrapperDialog dismiss() {
        isFinishing = true;
        stopAndHideLoading(helper.getView(R.id.loading));
        return super.dismiss();
    }

    private void showAndPlayLoading(LottieAnimationView view) {
        if (view != null) {
            view.setVisibility(View.VISIBLE);
            view.playAnimation();
        }
    }

    private void stopAndHideLoading(LottieAnimationView view) {
        if (view != null) {
            view.setVisibility(View.INVISIBLE);
            view.cancelAnimation();
        }
    }

    public void setCancelable(boolean enable) {
        if (dialog != null) {
            dialog.setCancelable(enable);
        }
    }
}
