package com.sayweee.weee.module.web.bean;

import com.sayweee.weee.module.cart.bean.ProductUpdateBean;
import com.sayweee.weee.utils.JsonUtils;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.List;

public class JSFunction<T> {

    public String functionname;
    public String callback;
    public T args;

    public static <T> JSFunction<T> parseFunction(String json, Class<T> t) {
        return JsonUtils.parseObject(json, JSFunction.class, new Type[]{t});
    }


    //popupWindowDidFinish
    public static class PopupSizeArgs {
        public int width;
        public int height;
    }

    //closeWindow
    public static class PopCloseArgs {
        public String eventType;  //jump_page  //coupon_confirmed
        public String pageUrl;
    }


    //share
    public static class ShareArgs {

        /**
         * title : heardown邀请您成为Weee!VIP会员
         * description : 快来加入Weee!会员吧！2%返利，专属会员折扣，以及更多会员服务！
         * link_timeline : https://www.sayweee.com/member/portal?lang=zh
         * link : https://www.sayweee.com/member/portal?lang=zh
         * imgUrl : https://www.sayweee.com/css/img/member-badge.png
         */
        public String title;
        public String description;
        public String link_timeline;
        public String link;
        public String imgUrl;
        public String language;
    }


    public static class ShareToArgs {

        /**
         * shareTo : WeChatSession
         * type : link
         * imgUrl : https://cdn01.sayweee.net/2021-01/pRqTe98-QBipBbHSEY4hdg-square-160.jpg
         * title : Weee! 韩国周特价最后一天❤️地道韩系美食+美妆超值折扣
         * link : https://www.sayweee.com/shopping_list/collect/wa7p0?lang=zh
         * description :  Wuli亲故们！ Weee!韩国周火热开启🥳 各色🇰🇷地道美味，人气品牌加盟 从生鲜到速食再到美妆，应有尽有 还可享多重福利💝 1️⃣ 单笔订单购满$68 可bug价换购20余款人气商品 🐟 韩国进口 济州岛银带鱼 原价$9.99 🆚 换购价$4.99 🐟 康威 黑鱼片 原价$11.99 🆚 换购价$1.99 🐸 杰克船长 麻辣牛蛙方便料理包 原价$12.99 🆚 换购价$1.99 …… 2️⃣...
         */
        public String shareTo;
        public String type;
        public String imgUrl;
        public String title;
        public String link;
        public String language;
        public String description;
    }

    public static class OrderArgs {
        public String pre_order_id;
        public String token;
        public String status;
    }

    public static class ShareContent {
        public String data;
    }

    /**
     * 连续扫描结果回调
     */
    public static class ScanHandleResult {
        public boolean success;
        public String code;
        public boolean is_finished;
        public String message;
    }

    public static class OrderAliPay {
        public int order_id;
        public BigDecimal amount;
        public String pay_method;
        public List<Integer> order_ids;
        public String cancel_url;
        public String success_url;

        public boolean isCombinePay() {
            return order_ids != null && order_ids.size() > 0;
        }

        public boolean isPayByCitcon() {
            return "citcon".equalsIgnoreCase(pay_method);
        }
    }

    public static class OrderPay extends OrderAliPay {

        public String type;

        public boolean isPayByWechat() {
            return "wechat".equalsIgnoreCase(type);
        }

        public boolean isPayByVenmo() {
            return "venmo".equalsIgnoreCase(type);
        }

        public boolean isPayByPayPal() {
            return "paypal".equalsIgnoreCase(type);
        }

        public boolean isPayByCashApp() {
            return "cashapp".equalsIgnoreCase(type);
        }
    }

    public static class NativePay {
        public String data; // PayPaymentResult.class
    }

    public static class ShowPopupArgs {
        public static final String STYLE_SLIDE = "slide";
        public static final String STYLE_NORMAL = "normal";

        public String pop_style; // slide: 从底部弹出; normal: 从中间弹出
        public String content_url;
        public String content_type;
        public int popup_id;
        public String content_img_url;
        public String content_html;
        public String link_url;
    }

    public static class StatusMode {
        public String StatusColor;

        public boolean isDarkMode() {
            return StatusColor == null || "1".equals(StatusColor);
        }
    }

    public static class StatusBarArgs {
        public String statusBarColor;
        public int darkMode; //1 深色 2 浅色 0 不修改
        public boolean useStatusArea;

        public boolean isDarkMode() {
            return darkMode == 1;
        }

        public boolean isLightMode() {
            return darkMode == 2;
        }
    }

    public static class PurchaseStatusArgs {
        public List<ProductUpdateBean> products;
    }

    public static class AccountStatusArgs {

        public static final String BIND_SUCCESS = "bindSuccess";
        public static final String LOGOUT = "logout";
        //loyalty等级变化
        public static final String LEVEL_UPDATE = "levelUpdate";

        public String type; //bindSuccess logout
        public String token; // logout anonymous token
    }

    public static class CollectStatusArgs {
        public String type; //product
        public String status;
        public List<Integer> ids;

        public boolean isProduct() {
            return "product".equalsIgnoreCase(type);
        }

        public boolean isStatusAdd() {
            return "add".equalsIgnoreCase(status);
        }

        public boolean isStatusRemove() {
            return "remove".equalsIgnoreCase(status);
        }

        public boolean isStatusReset() {
            return "reset".equalsIgnoreCase(status);
        }
    }

    public static class WriteLogArgs {
        public String content;
    }

    public static class PaySupportArgs {
        public String type;
        public String pay_method;

        public boolean isWechatPayByCitcon() {
            return "wechat".equalsIgnoreCase(type) && "citcon".equalsIgnoreCase(pay_method);
        }

        public boolean isVenmoPayByBraintree() {
            return "venmo".equalsIgnoreCase(type) && "braintree".equalsIgnoreCase(pay_method);
        }

        public boolean isPayPalPayByBraintree() {
            return "paypal".equalsIgnoreCase(type) && "braintree".equalsIgnoreCase(pay_method);
        }

        public boolean isCashAppPayByCitcon() {
            return "cashapp".equalsIgnoreCase(type) && "citcon".equalsIgnoreCase(pay_method);
        }
    }

    public static class AffiliateAddArgs {
        public List<Integer> product_ids;
        public int affiliate_id;
    }

    public static class BizStatusChangedArgs {
        public String type;
        public Coupon coupon;

        public static class Coupon {
            public int plan_id;
            public String coupon_status;
        }
    }

    public static class NativeStoreArgs {
        public String method;
        public String key;
        public String value;
    }

    public static class AlcoholCheckoutArgs {
        public String type;
    }
}
