package com.sayweee.weee.module.mkpl.home;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.lifecycle.MutableLiveData;

import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.bean.ComponentData;
import com.sayweee.weee.module.cms.iml.blank.data.CmsBlankData;
import com.sayweee.weee.module.cms.iml.couponlist.CmsCouponListParser;
import com.sayweee.weee.module.cms.service.CmsViewModel;
import com.sayweee.weee.module.cms.service.ComponentPool;
import com.sayweee.weee.module.debug.producttrace.ProductTraceManager;
import com.sayweee.weee.module.debug.producttrace.ProductTraceTaskAssembler;
import com.sayweee.weee.module.home.bean.CategoriesProperty;
import com.sayweee.weee.module.home.provider.bar.SearchBarParser;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.home.provider.category.CategoryParser;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;
import com.sayweee.weee.module.home.provider.product.LightingDealsParser;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedPacket;
import com.sayweee.weee.module.mkpl.provider.parser.CmsContentFeedParser;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.CouponClaimRequest;
import com.sayweee.weee.module.seller.bean.CouponClaimResponse;
import com.sayweee.weee.service.live.UnPeekLiveData;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.BaseLoaderModel;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Desc:
 */
public class GlobalPlusViewModel extends CmsViewModel<BaseLoaderModel<OrderApi>> implements IContentFeedSharedViewModel {

    private static final String PAGE_TYPE = "8";

    public MutableLiveData<ShareBean> shareData = new MutableLiveData<>();
    public MutableLiveData<CouponClaimResponse> sellerCouponClaimResponseLiveData = new UnPeekLiveData<>();

    public MutableLiveData<Map<String, Object>> remindData = new MutableLiveData<>();

    public String pageKey;

    @Nullable
    private CmsContentFeedPacket contentFeedPacket;

    public GlobalPlusViewModel(@NonNull Application application) {
        super(application);

        registerCmsParser(ComponentPool.Key.COMPONENT_CM_SEARCH_BAR, new SearchBarParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CATEGORIES, new CategoryParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED, new CmsContentFeedParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_CONTENT_FEED_V2, new CmsContentFeedParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_COUPON_LIST, new CmsCouponListParser());
        registerCmsParser(ComponentPool.Key.COMPONENT_CM_LIGHTNING_DEALS, new LightingDealsParser());
    }

    public void getGlobalData(String pageKey, String model) {
        this.pageKey = pageKey;
        fetchCmsData(new RequestParams()
                .put("page_type", PAGE_TYPE)
                .put("page_key", pageKey)
                .putNonNull("model", model)
                .get());
    }

    @Override
    protected void dispatchDataChanged() {
        super.dispatchDataChanged();
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_LOAD,
                WeeeEvent.PageView.MKPL_WATERFALL, String.valueOf(hashCode()));
    }

    @Override
    protected void beforeFetchCmsChildDataByUrl(String componentKey, ComponentData data, String path, Map<String, String> params, Class<?> clazz) {
        if (data instanceof CmsContentFeedPacket) {
            // add recommend session for cm_content_feed
            if (ComponentPool.Key.COMPONENT_CM_CONTENT_FEED.equalsIgnoreCase(componentKey)) {
                setContentFeedParams((CmsContentFeedPacket) data, params);
            } else if (ComponentPool.Key.COMPONENT_CM_CONTENT_FEED_V2.equalsIgnoreCase(componentKey)) {
                setCategoryContentFeedParams((CmsContentFeedPacket) data, params);
            }
            contentFeedPacket = (CmsContentFeedPacket) data;

            CmsDataSource dataSource = CmsContentFeedParser.parseDataSource(data, path, params);
            contentFeedPacket.setBaseDataSource(dataSource);
        } else {
            super.beforeFetchCmsChildDataByUrl(componentKey, data, path, params, clazz);
        }
    }

    private void setContentFeedParams(CmsContentFeedPacket data, Map<String, String> params) {
        params.put("recommend_session", data.recommendSession);
        params.put("page_num", "1");
        params.put("from_page", getFromPageKey());
        params.put("key", "");
    }

    private void setCategoryContentFeedParams(CmsContentFeedPacket data, Map<String, String> params) {
        params.put("recommend_session", data.recommendSession);
        params.put("page_num", "1");
        params.put("from_page", getFromPageKey());
        params.put("key", "");
    }

    public void getGlobalShare(String pageKey) {
        getLoader()
                .getHttpService()
                .getGlobalShare(pageKey, PAGE_TYPE)
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<ShareBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ShareBean> response) {
                        shareData.postValue(response.getData());
                    }
                });
    }

    @Override
    protected void beforePreCreateData() {
        if (componentDataMap != null) {
            int pos = 0;
            for (Map.Entry<String, ComponentData> entry : componentDataMap.entrySet()) {
                ComponentData data = entry.getValue();
                if (data != null && data.isValid()) {
                    data.position = pos;
                    pos++;
                }
                if (data instanceof CmsSearchBarData) {
                    ((CmsSearchBarData) data).setTargetName(CmsSearchBarData.TARGET_NAME_SEARCH_BAR);
                } else if (data instanceof CmsCategoryData) {
                    CmsCategoryData categoryData = (CmsCategoryData) data;
                    if (categoryData.isValid()
                            && CategoriesProperty.STYLE_LINE_1.equals(categoryData.getDisplayStyle())
                            && CollectionUtils.size(categoryData.t.category_list) == 5
                    ) {
                        categoryData.changeGridStyle(true);
                    }
                }
            }
        }
    }

    @Override
    protected List<AdapterDataType> beforeDispatchData(List<AdapterDataType> data) {
        List<AdapterDataType> list = super.beforeDispatchData(data);
        AdapterDataType firstItem = CollectionUtils.firstOrNull(list);
        if (firstItem instanceof CmsBlankData) {
            list.remove(0);
        }
        Pair<Integer, AdapterDataType> categoryEntry;
        categoryEntry = CollectionUtils.firstOrNullWithIndex(list, item -> item instanceof CmsCategoryData);
        if (categoryEntry != null) {
            AdapterDataType maybeBlankItem = CollectionUtils.getOrNull(list, categoryEntry.first + 1);
            if (maybeBlankItem instanceof CmsBlankData) {
                list.remove(categoryEntry.first + 1);
            }
        }

        ProductTraceTaskAssembler taskAssembler = ProductTraceTaskAssembler.create();
        taskAssembler.addAll(list);
        ProductTraceManager.get().addTasks(taskAssembler.assemble(getProductTraceTopic(), WeeeEvent.PageView.MKPL_WATERFALL));

        return list;
    }

    protected String getFromPageKey() {
        return GlobalPlusFragment.FROM_PAGE;
    }

    public void claimSellerCoupon(final CouponClaimRequest request) {
        ResponseObserver<ResponseBean<CouponClaimBean>> observer;
        observer = new ResponseObserver<ResponseBean<CouponClaimBean>>() {

            @Override
            public void onResponse(ResponseBean<CouponClaimBean> response) {
                sellerCouponClaimResponseLiveData.postValue(
                        new CouponClaimResponse(request, response, null)
                );
            }

            @Override
            public void onError(FailureBean failure) {
                sellerCouponClaimResponseLiveData.postValue(
                        new CouponClaimResponse(request, null, failure)
                );
            }
        };
        getLoader().getHttpService()
                .claimSellerCoupon(request.getSellerId(), request.asRequestBody())
                .compose(ResponseTransformer.scheduler())
                .subscribe(observer);
    }

    public void changeLightningDealsRemind(int productId, boolean isRemind) {
        getLoader().getHttpService()
                .changeLightningDealsRemind(new RequestParams()
                        .put("product_id", productId)
                        .put("status", isRemind ? "A" : "X")
                        .create())
                .compose(DisposableTransformer.scheduler(this, false))
                .subscribe(new ResponseObserver<SimpleResponseBean>() {
                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("product_id", productId);
                        map.put("remind", isRemind);
                        remindData.postValue(map);
                    }
                });
    }

    @Nullable
    @Override
    public CmsContentFeedPacket getContentFeedPacket() {
        return contentFeedPacket;
    }

    @Nullable
    @Override
    public String getContentFeedFromPageKey() {
        return getFromPageKey();
    }

    @Override
    public boolean isOnCartEditListenerEnabled() {
        return true;
    }

    public String getProductTraceTopic() {
        String topic = WeeeEvent.PageView.MKPL_WATERFALL;
        if (pageKey != null) {
            topic += "-" + pageKey;
        }
        return topic;
    }
}
