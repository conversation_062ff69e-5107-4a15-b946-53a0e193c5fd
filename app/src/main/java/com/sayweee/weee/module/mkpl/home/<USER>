package com.sayweee.weee.module.mkpl.home;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.DialogMkplCouponClaimedBottomBinding;
import com.sayweee.weee.databinding.FragmentGlobalPlusBinding;
import com.sayweee.weee.databinding.LayoutHomeSearchBarInnerBinding;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.base.ProductSyncHelper;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.base.adapter.SimpleSectionItemDecoration;
import com.sayweee.weee.module.cms.adapter.CmsItemType;
import com.sayweee.weee.module.cms.bean.CmsDataSource;
import com.sayweee.weee.module.cms.iml.couponlist.CmsCouponListProvider;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponClaimBean;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponListData;
import com.sayweee.weee.module.cms.iml.couponlist.data.CmsCouponListItemBean;
import com.sayweee.weee.module.cms.widget.timer.CmsComponentTimerHandler;
import com.sayweee.weee.module.debug.producttrace.ProductTraceObserver;
import com.sayweee.weee.module.debug.producttrace.ProductTraceViewHelper;
import com.sayweee.weee.module.home.adapter.OnRemindListener;
import com.sayweee.weee.module.home.bean.LightningDealsProductBean;
import com.sayweee.weee.module.home.provider.bar.data.CmsSearchBarData;
import com.sayweee.weee.module.home.provider.product.data.CmsLightingDealsData;
import com.sayweee.weee.module.mkpl.GlobalMiniCartViewModel;
import com.sayweee.weee.module.mkpl.GlobalOnCartEditListener;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedModelProvider;
import com.sayweee.weee.module.mkpl.feed.IContentFeedSharedViewModel;
import com.sayweee.weee.module.mkpl.feed.IContentScrollTop;
import com.sayweee.weee.module.popup.PopupCenterManager;
import com.sayweee.weee.module.post.widget.BottomDialog;
import com.sayweee.weee.module.search.SearchPanelActivity;
import com.sayweee.weee.module.seller.bean.CouponClaimBean;
import com.sayweee.weee.module.seller.bean.CouponClaimRequest;
import com.sayweee.weee.module.seller.bean.CouponClaimResponse;
import com.sayweee.weee.module.seller.common.mpager.MPagerEntity;
import com.sayweee.weee.module.seller.common.mpager.OnIndicatorClickListener;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.player.mute.PostCoverVideoHelper;
import com.sayweee.weee.player.mute.PostCoverVideoManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.helper.StatusHelper;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.SearchTextSwitcher;
import com.sayweee.weee.widget.nested.ParentNestedRecyclerView;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;
import com.sayweee.weee.widget.recycler.SafeLinearLayoutManager;
import com.sayweee.weee.widget.recycler.StatefulRecyclerViewOnScrollListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.core.view.WrapperMvvmStatusFragment;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Desc:
 */
public class GlobalPlusFragment
        extends WrapperMvvmStatusFragment<GlobalPlusViewModel>
        implements OnRefreshListener, IContentFeedSharedModelProvider, IContentScrollTop,
        CmsCouponListProvider.OnCmsCouponListItemClaimListener {

    public static final String FROM_PAGE = "global";

    private static final String BUNDLE_IN_TAB = "in_tab";

    private GlobalMiniCartViewModel cartViewModel;

    private FragmentGlobalPlusBinding binding;
    private GlobalPlusAdapter adapter;

    private long backgroundTime;
    private boolean needForceRefresh;

    @Nullable
    private RecyclerViewScrollStatePersist scrollStatePersist;

    private ProductTraceObserver productTraceObserver;

    public static GlobalPlusFragment newInstance(String pageKey, boolean inTab) {
        GlobalPlusFragment fragment = new GlobalPlusFragment();
        Bundle bundle = new Bundle();
        bundle.putString("pageKey", pageKey);
        bundle.putBoolean(BUNDLE_IN_TAB, inTab);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getExtraInTab()) {
            WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.MKPL_WATERFALL,
                    String.valueOf(hashCode()));
        }
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_global_plus;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        scrollStatePersist = new RecyclerViewScrollStatePersist(savedInstanceState);

        binding = FragmentGlobalPlusBinding.bind(contentView);
        setupPage();
        initListener();

        showListVeilTemplated(true);
    }

    private void initListener() {
        ViewTools.setViewOnSafeClickListener(binding.layoutTop, v -> scrollToTop());

        setOnClickListener(this::click, R.id.tv_remind_revoke);
    }

    private void click(View view) {
        int id = view.getId();
        if (id == R.id.tv_remind_revoke) {
            revokeRemind();
        }
    }

    public void scrollToTop() {
        binding.recyclerView.scrollToPosition(0);
        adapter.scrollToTop();
    }

    private void setupPage() {
        Context context = getContext();
        if (context == null) return;
        binding.smartRefreshLayout.setOnRefreshListener(this);

        SafeLinearLayoutManager manager = new SafeLinearLayoutManager(context);
        binding.recyclerView.setLayoutManager(manager);

        adapter = new GlobalPlusAdapter(String.valueOf(hashCode()), onCartEditListener);
        adapter.setEnableLoadMore(false);
        adapter.setOnIndicatorClickListener(new OnIndicatorClickListener() {
            @Override
            public void onClick(int index, MPagerEntity entity) {
                int pos = adapter.getCategoryPosition();
                if (pos > -1) {
                    binding.recyclerView.smoothScrollToPosition(pos);
                }
            }
        });
        adapter.setOnCmsCouponListActionListener(this);

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        adapter.setOnLightingDealTimerListener(new CmsComponentTimerHandler(viewLifecycleOwner) {
            @Override
            public void onEndSafely(String componentId) {
                super.onEndSafely(componentId);
                if (!EmptyUtils.isEmpty(componentId)) {
                    CmsDataSource dataSource = viewModel.getDataSource(componentId);
                    if (dataSource != null) {
                        viewModel.requestMultiDataSource(dataSource, 1500L);
                        return;
                    }
                }
                loadData();
            }
        });
        adapter.setOnRemindListener(lightningRemindListener);

        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.addItemDecoration(new SimpleSectionItemDecoration());
        binding.recyclerView.addOnScrollListener(new StatefulRecyclerViewOnScrollListener() {

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState, int oldState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    adapter.onPageScrollStateChanged(recyclerView, newState);
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView rv, int dx, int dy) {
                ParentNestedRecyclerView recyclerView = (ParentNestedRecyclerView) rv;
                showScrollTop(recyclerView);
                if (!recyclerView.isStickyTop()) {
                    RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                    if (!(layoutManager instanceof LinearLayoutManager)) return;
                    int firstVisibleItemPosition = ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
                    boolean visible = false;
                    int index = adapter.getSearchPosition();
                    if (index > -1 && firstVisibleItemPosition >= index) {
                        visible = true;
                        if (firstVisibleItemPosition == index) {
                            View view = layoutManager.findViewByPosition(index);
                            visible = view != null && view.getY() < 0;
                        }
                    }
                    setSearchBarDisplay(visible);
                }
            }
        });

        binding.recyclerView.setStickyListener(new ParentNestedRecyclerView.StickyListener() {
            @Override
            public void onSticky(boolean isAtTop) {
                if (isAtTop) {
                    binding.inLayoutSearchShadow.getRoot().setVisibility(View.GONE);
                }
                adapter.setCategoryShadow(isAtTop);
            }
        });

        PostCoverVideoHelper.attachScrollPlay(binding.recyclerView);
    }

    GlobalOnCartEditListener onCartEditListener = new GlobalOnCartEditListener(
            null, new GlobalOnCartEditListener.SimpleOnGlobalCartUpdateListener() {
        @Override
        public void onGlobalCartItemDataRemove(@NonNull AdapterDataType item) {
        }

        @Override
        public void onGlobalCartListUpdate(@NonNull GlobalCartListResponse response) {
            handleGlobalCartListResponse(response);
        }
    });

    private void handleGlobalCartListResponse(@Nullable GlobalCartListResponse response) {
        if (cartViewModel == null) return;
        cartViewModel.notifyGlobalCartListResponseLiveDataChange(response);
    }

    public void showScrollTop(@Nullable RecyclerView recyclerView) {
        if (recyclerView == null) return;
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager instanceof LinearLayoutManager) {
            LinearLayoutManager lm = (LinearLayoutManager) layoutManager;
            int firstVisiblePosition = lm.findFirstVisibleItemPosition();
            if (firstVisiblePosition >= 0) {
                ViewTools.setViewVisibilityIfChanged(binding.layoutTop, firstVisiblePosition > 3);
            }
        }

        resetScrollTopParams(hasMiniCart());
    }

    private boolean hasMiniCart() {
        boolean hasMiniCart = false;
        if (getParentFragment() instanceof GlobalExpFragment) {
            hasMiniCart = ((GlobalExpFragment) getParentFragment()).hasGlobalMiniCart();
        }
        return hasMiniCart;
    }

    public void resetScrollTopParams(boolean hasMiniCart) {
        int marginBottom;
        if (getExtraInTab()) {
            marginBottom = hasMiniCart ? CommonTools.dp2px(142) : CommonTools.dp2px(72);
        } else {
            marginBottom = hasMiniCart ? CommonTools.dp2px(108) : CommonTools.dp2px(38);
        }
        ViewGroup.LayoutParams lp = binding.ivGotoTop.getLayoutParams();
        if (lp instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) lp;
            if (layoutParams.bottomMargin != marginBottom) {
                layoutParams.bottomMargin = marginBottom;
                binding.ivGotoTop.postDelayed(() -> {
                    binding.ivGotoTop.setLayoutParams(layoutParams);
                }, 420);
            }
        }
    }

    @Override
    public void loadData() {
        Bundle arguments = getArguments();
        String pageKey = null;
        if (arguments != null) {
            pageKey = arguments.getString("pageKey");
        }
        viewModel.pageKey = pageKey;
        if (!EmptyUtils.isEmpty(pageKey)) {
            WeeeMonitor.getInstance().startAction(WeeeMonitor.PAGE_LOAD, WeeeEvent.PageView.MKPL_WATERFALL,
                    String.valueOf(viewModel.hashCode()));
            initProductTraceObserver();
            viewModel.getGlobalData(pageKey, null);
        }
    }

    @Override
    public <VM> VM createModel() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            cartViewModel = ViewModelProviders.of(activity).get(GlobalMiniCartViewModel.class);
            cartViewModel.injectLifecycle(getLifecycle());
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> list) {
                binding.smartRefreshLayout.finishRefresh();

                binding.recyclerView.removeAllViews();
                binding.recyclerView.removeAllViewsInLayout();
                binding.recyclerView.getRecycledViewPool().clear();
                binding.recyclerView.swapAdapter(adapter, true);
                adapter.setNewData(list);
                adapter.loadMoreComplete();
                adapter.notifyPageDataSetChanged(binding.recyclerView);
                ViewTools.setViewVisible(EmptyUtils.isEmpty(list), findViewById(R.id.layout_empty));
                showListVeilTemplated(false);

                AdapterDataType data = adapter.getTargetData(CmsItemType.SEARCH_BAR, adapter.getSearchPosition());
                if (data instanceof CmsSearchBarData) {
                    initSearchBar((CmsSearchBarData) data, binding.layoutSearchInner);
                }
                setSearchBarHeight();
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                showListVeilTemplated(false);
                binding.smartRefreshLayout.setVisibility(View.GONE);
                StatusHelper.showStatus(getStatusManager(), failureBean, true,
                        new OnSafeClickListener() {
                            @Override
                            public void onClickSafely(View v) {
                                getStatusManager().hideStatus();
                                showListVeilTemplated(true);
                                binding.smartRefreshLayout.setVisibility(View.VISIBLE);
                                loadData();
                            }
                        });
            }
        });

        SharedViewModel.get().postCollectsData.observe(this, new Observer<Map<String, Serializable>>() {
            @Override
            public void onChanged(Map<String, Serializable> map) {
                adapter.toggleCollect(map);
            }
        });

        attachCartViewModel();

        viewModel.sellerCouponClaimResponseLiveData.observe(this, new Observer<CouponClaimResponse>() {
            @Override
            public void onChanged(CouponClaimResponse response) {
                handleCouponClaimResponse(response);
            }
        });

        if (getExtraInTab()) {
            SharedOrderViewModel.get().preOrderRecreateData.observe(this, new Observer<Integer>() {
                @Override
                public void onChanged(Integer integer) {
                    needForceRefresh = true;
                }
            });
        }

        viewModel.componentDataUpdateLiveData.observe(this, adapter::notifyMultiDataSourceUpdate);

        viewModel.remindData.observe(this, new Observer<Map<String, Object>>() {
            @Override
            public void onChanged(Map<String, Object> map) {
                refreshRemindSet(map);
            }
        });
    }

    private void attachCartViewModel() {
        if (cartViewModel == null) return;
        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;

        cartViewModel.getGlobalMiniCartPageStateLiveData().observe(viewLifecycleOwner, pageState -> {
            if (!isSupportVisible()) return;
            if (pageState.isExpended()) {
                Map<String, Object> ctx = null;
                EagleTrackManger.get().trackEagleClickAction(
                        /* modNm= */"mkpl_mini_cart",
                        /* modPos= */-1,
                        /* secNm= */null,
                        /* secPos= */-1,
                        /* targetNm= */pageState.getTargetId(),
                        /* targetPos= */pageState.getTargetPosition(),
                        /* targetType= */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                        /* clickType= */EagleTrackEvent.ClickType.VIEW,
                        /* ctx= */ctx
                );
            } else {
                AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_WATERFALL, this);
                adapter.onPageResume(binding.recyclerView);
                ProductSyncHelper.onPageResume(adapter);
            }
        });
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (scrollStatePersist != null) {
            scrollStatePersist.onSaveInstanceState(outState);
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        String pageIdentifier = String.valueOf(hashCode());
        if (!getExtraInTab() && getActivity() != null) {
            pageIdentifier = String.valueOf(getActivity().hashCode());
        }
        WeeeMonitor.getInstance().endAction(WeeeMonitor.PAGE_INIT, WeeeEvent.PageView.MKPL_WATERFALL,
                pageIdentifier);
        AppAnalytics.logPageView(WeeeEvent.PageView.MKPL_WATERFALL, this);
        PopupCenterManager.get().onPageResumed(WeeeEvent.POPUP_PAGE_MKPL_WATERFALL);

        adapter.onPageResume(binding.recyclerView);
        ProductSyncHelper.onPageResume(adapter);
        binding.layoutSearchInner.tvSearchTips.startSwitch();
        autoRefresh();
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        adapter.onPagePause(binding.recyclerView);
        binding.layoutSearchInner.tvSearchTips.stopSwitch(false);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PostCoverVideoManager.clearVideo(String.valueOf(hashCode()));
    }

    private void autoRefresh() {
        long now = System.currentTimeMillis();
        boolean refresh = false;
        long fiveMin = (5 * 60 * 1000);
        if (backgroundTime > 0 && (now - backgroundTime) > fiveMin) {
            refresh = true;
        }
        backgroundTime = 0;
        if (refresh || needForceRefresh) {
            needForceRefresh = false;
            scrollToTop();
            refresh();
        }
    }

    public void setBackgroundTime() {
        backgroundTime = System.currentTimeMillis();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        adapter.setEnableLoadMore(false);
        loadData();
    }

    private void refresh() {
        adapter.setEnableLoadMore(false);
        adapter.setNewData(null);
        showListVeilTemplated(true);
        loadData();
    }

    protected void loadMore() {

    }

    private void showListVeilTemplated(boolean visible) {
        VeilTools.show(findViewById(R.id.vl_global_list), visible);
    }

    private void setSearchBarHeight() {
        int height = 0;
        int pos = adapter.getSearchPosition();
        if (pos > -1) {
            height = getResources().getDimensionPixelOffset(R.dimen.prop_size_search_bar_height);
        }
        binding.recyclerView.setStickyHeight(height);
    }

    private void setSearchBarDisplay(boolean visible) {
        ViewTools.setViewVisibilityIfChanged(binding.layoutSearchInner.tvSearchBtn, false);
        if (visible) {
            ViewTools.setViewVisibilityIfChanged(binding.layoutSearch, true);
            ViewTools.setViewVisibilityIfChanged(binding.inLayoutSearchShadow.getRoot(), true);
        } else {
            ViewTools.setViewVisibilityIfChanged(binding.layoutSearch, false);
            ViewTools.setViewVisibilityIfChanged(binding.inLayoutSearchShadow.getRoot(), false);
        }
    }

    private void initSearchBar(CmsSearchBarData item, LayoutHomeSearchBarInnerBinding binding) {
        List<String> keywords = item.getKeywords();
        SearchTextSwitcher tvSearchTips = binding.tvSearchTips;
        View tvSearch = binding.tvSearchBtn;
        TextView tvHint = binding.tvSearchHint;

        tvSearchTips.stopSwitch(true);
        if (!EmptyUtils.isEmpty(keywords)) {
            tvSearchTips.setVisibility(View.VISIBLE);
            tvHint.setVisibility(View.GONE);
            tvSearchTips.bindData(keywords);
            if (keywords.size() == 1) {
                tvSearchTips.setCurrentText(keywords.get(0));
            } else {
                tvSearchTips.startSwitch();
            }
            tvSearch.setVisibility(View.VISIBLE);
        } else {
            tvSearchTips.setVisibility(View.GONE);
            tvSearch.setVisibility(View.GONE);
            tvHint.setVisibility(View.VISIBLE);
            tvHint.setText(item.property.tips);
        }

        binding.getRoot().setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (tvHint.getVisibility() == View.VISIBLE) {
                    trackSearchBarEagleClickAction(item, null);
                    Context context = getContext();
                    if (context != null) {
                        context.startActivity(SearchPanelActivity.getIntentByGlobal(context, null, null));
                    }
                }
            }
        });
        binding.tvSearchTips.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //带入hint，到达搜索onboarding页面
                trackSearchBarEagleClickAction(item, null);
                Context context = getContext();
                if (context != null) {
                    context.startActivity(SearchPanelActivity.getIntentByGlobal(context, null, tvSearchTips.getKeyword()));
                }
            }
        });
        binding.tvSearchBtn.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                String keyword = tvSearchTips.getKeyword();
                trackSearchBarEagleClickAction(item, keyword);
                if (!EmptyUtils.isEmpty(keyword)) {
                    // 带入关键字，直接到达搜索结果页面
                    Context context = getContext();
                    if (context != null) {
                        context.startActivity(SearchPanelActivity.getIntentByGlobal(context, keyword, null));
                    }
                }
            }
        });
        binding.ivCamera.setVisibility(View.GONE);
        binding.ivSearch.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {

            }
        });
    }

    private void trackSearchBarEagleClickAction(CmsSearchBarData item, @Nullable String keyword) {
        Map<String, Object> ctx = EagleTrackManger.get().getCtx(null, null, null, null, keyword, null);
        EagleTrackManger.get().trackEagleClickAction(
                /* modNm = */item.getEventKey(),
                /* modPos = */0,
                /* secNm = */null,
                /* secPos = */-1,
                /* targetNm = */CmsSearchBarData.TARGET_NAME_SEARCH_BAR,
                /* targetPos = */-1,
                /* targetType = */EagleTrackEvent.TargetType.NORMAL_BUTTON,
                /* clickType = */EagleTrackEvent.ClickType.VIEW,
                /* ctx = */ctx
        );
    }

    public boolean getExtraInTab() {
        return getArguments() != null && getArguments().getBoolean(BUNDLE_IN_TAB, false);
    }

    private void onCmsCouponListItemClick(@NonNull CmsCouponListItemBean item) {
        FragmentActivity activity = getActivity();
        String linkUrl = item.useUrl;
        if (activity != null && linkUrl != null) {
            activity.startActivity(WebViewActivity.getIntent(activity, linkUrl));
        }
    }

    /**
     * implementation of CmsCouponListProvider.OnCmsCouponListItemClaimListener
     */
    @Override
    public void onCmsCouponListItemClaim(@NonNull CmsCouponListItemBean item, int position) {
        claimCoupon(item, position);
    }

    private void claimCoupon(@Nullable CmsCouponListItemBean coupon, int targetPosition) {
        if (!AccountManager.get().isLogin()) {
            needForceRefresh = true;
            startActivity(AccountIntentCreator.getIntent(activity));
            return;
        }

        if (coupon == null || !coupon.isCouponPlan()) return;
        int couponPlanId = coupon.planId;
        String sellerId = String.valueOf(coupon.sellerId);
        CouponClaimRequest request = new CouponClaimRequest(sellerId, couponPlanId);
        request.setTargetPosition(targetPosition);
        viewModel.claimSellerCoupon(request);
    }

    private void handleCouponClaimResponse(@Nullable CouponClaimResponse response) {
        if (response == null) return;
        if (!response.isSuccess()) {
            needForceRefresh = true;
            autoRefresh();
            return;
        }

        CouponClaimBean data = response.requireResponse();
        CmsCouponClaimBean preload = new CmsCouponClaimBean();
        preload.coupon_id = data.coupon_id;
        preload.coupon_plan_id = data.coupon_plan_id;
        preload.apply_expiration_time = data.apply_expiration_time;
        preload.server_timestamp = data.server_timestamp;

        Pair<Integer, AdapterDataType> withIndex;
        withIndex = CollectionUtils.firstOrNullWithIndex(
                adapter.getData(),
                CmsCouponListData.class::isInstance
        );

        if (withIndex != null) {
            adapter.notifyItemChanged(withIndex.first, preload);

            CmsCouponListData coupons = (CmsCouponListData) withIndex.second;
            Pair<Integer, CmsCouponListItemBean> withIndex2;
            withIndex2 = CollectionUtils.firstOrNullWithIndex(
                    coupons.t.coupons,
                    c -> c != null && c.planId == data.coupon_plan_id
            );
            if (withIndex2 != null) {
                CmsCouponListItemBean couponItem = withIndex2.second;
                couponItem.id = data.coupon_id;
                couponItem.planId = data.coupon_plan_id;
                couponItem.endTime = data.apply_expiration_time;
                couponItem.serverTimestamp = data.server_timestamp;
                showCouponClaimedDialog(withIndex2.second, withIndex2.first);
            }
        }
    }

    private void showCouponClaimedDialog(CmsCouponListItemBean couponItem, int position) {
        new BottomDialog(activity) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_mkpl_coupon_claimed_bottom;
            }
        }.addHelperCallback((Dialog dialog, ViewHelper viewHelper) -> {
            DialogMkplCouponClaimedBottomBinding binding =
                    DialogMkplCouponClaimedBottomBinding.bind(viewHelper.itemView);

            binding.tvContent.setText(couponItem.promoteTitle);
            binding.tvContentSub.setText(couponItem.subTitle);

            binding.btnToSeller.setText(binding.getRoot().getResources().getString(
                    R.string.s_mkpl_coupon_shop_seller, couponItem.sellerTitle));

            long deadline = TimeUnit.SECONDS.toMillis(couponItem.endTime);
            String date = DateUtils.getFormatTime(DateUtils.MM_DD_YY, deadline, "");
            String expireDate = binding.getRoot().getResources().getString(R.string.s_mkpl_coupon_expires_date, date);
            binding.tvExpiredDate.setText(expireDate);

            binding.btnToSeller.setOnClickListener(new OnSafeClickListener() {
                @Override
                public void onClickSafely(View v) {
                    Map<String, Object> params;
                    Map<String, Object> ctx;
                    ctx = new EagleContext()
                            .setGlobalVendor("" + couponItem.sellerId)
                            .setTagId("" + couponItem.getMixedId())
                            .asMap();
                    params = new EagleTrackModel.Builder()
                            .setMod_nm("coupon_popup")
                            .setMod_pos(-1)
                            .setTargetNm(EagleTrackEvent.TargetNm.USE)
                            .setTargetPos(position)
                            .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                            .setClickType(EagleTrackEvent.ClickType.NORMAL)
                            .setUrl(couponItem.useUrl)
                            .addCtx(ctx)
                            .build()
                            .getParams();
                    AppAnalytics.logClickAction(params);

                    onCmsCouponListItemClick(couponItem);
                    dialog.dismiss();
                }
            });

            binding.ivCancel.setOnClickListener((targetView) -> {
                dialog.dismiss();
            });
        }).show();
    }

    @Override
    public IContentFeedSharedViewModel getContentFeedSharedViewModel() {
        return viewModel;
    }

    private OnRemindListener lightningRemindListener = new OnRemindListener() {

        @Override
        public void onRemind(LightningDealsProductBean bean, boolean remind, int position) {
            if (remind) {
                showRemindTips(bean.id);
            } else {
                setRemindEquals(bean.id);
            }
            viewModel.changeLightningDealsRemind(bean.id, remind);
        }
    };

    public void resetRemindTipsParams(boolean hasMiniCart) {
        int marginBottom;
        if (getExtraInTab()) {
            marginBottom = hasMiniCart ? CommonTools.dp2px(130) : CommonTools.dp2px(60);
        } else {
            marginBottom = hasMiniCart ? CommonTools.dp2px(108) : CommonTools.dp2px(38);
        }
        ViewGroup.LayoutParams lp = binding.layoutRemindTips.getRoot().getLayoutParams();
        if (lp instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) lp;
            if (layoutParams.bottomMargin != marginBottom) {
                layoutParams.bottomMargin = marginBottom;
                binding.layoutRemindTips.getRoot().postDelayed(() -> {
                    binding.layoutRemindTips.getRoot().setLayoutParams(layoutParams);
                }, 20);
            }
        }
    }

    private void showRemindTips(int id) {
        resetRemindTipsParams(hasMiniCart());
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().postDelayed(hideRemindTipsRunnable, 3000);
        binding.layoutRemindTips.getRoot().setTag(id);
        binding.layoutRemindTips.getRoot().setVisibility(View.VISIBLE);
    }

    private void setRemindEquals(int id) {
        if (binding.layoutRemindTips.getRoot().getVisibility() == View.VISIBLE) {
            Object tag = binding.layoutRemindTips.getRoot().getTag();
            if (tag instanceof Integer) {
                if ((Integer) tag == id) {
                    hideRemindTips();
                }
            }
        }
    }

    private void revokeRemind() {
        Object tag = binding.layoutRemindTips.getRoot().getTag();
        if (tag instanceof Integer) {
            adapter.revokeRemind((Integer) tag);
            hideRemindTips();
            viewModel.changeLightningDealsRemind((Integer) tag, false);
        }
    }

    private void hideRemindTips() {
        binding.layoutRemindTips.getRoot().removeCallbacks(hideRemindTipsRunnable);
        binding.layoutRemindTips.getRoot().setVisibility(View.GONE);
    }

    private final Runnable hideRemindTipsRunnable = this::hideRemindTips;

    private void refreshRemindSet(Map<String, Object> map) {
        for (AdapterDataType item : adapter.getData()) {
            if (item instanceof CmsLightingDealsData) {
                for (LightningDealsProductBean productBean : ((CmsLightingDealsData) item).t.products) {
                    if (map.get("product_id") != null && Objects.equals(map.get("product_id"), productBean.id)) {
                        Object remind = map.get("remind");
                        if (remind instanceof Boolean) {
                            productBean.remind_set = (boolean) remind;
                        }
                    }
                }
            }
        }
    }

    private void initProductTraceObserver() {
        if (productTraceObserver == null) {
            productTraceObserver = new ProductTraceObserver(this) {
                @Override
                protected void handleProductSalesTraceChange() {
                    ProductTraceViewHelper.notify(binding.recyclerView);
                }
            };
            productTraceObserver.start();
        }
        if (viewModel != null) {
            productTraceObserver.setExtraTopic(viewModel.getProductTraceTopic());
        }
    }
}
