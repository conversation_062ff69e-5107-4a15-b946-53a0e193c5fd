package com.sayweee.weee.module.mkpl.home;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.LifecycleOwner;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.sayweee.weee.R;
import com.sayweee.weee.databinding.FragmentGlobalExpBinding;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.MainActivity;
import com.sayweee.weee.module.base.MainTabFragment;
import com.sayweee.weee.module.dialog.ShareDialog;
import com.sayweee.weee.module.mkpl.GlobalMiniCartFragment;
import com.sayweee.weee.module.mkpl.GlobalMiniCartViewModel;
import com.sayweee.weee.module.mkpl.GlobalTabFragment;
import com.sayweee.weee.module.mkpl.base.MkplBaseFragment;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListBean;
import com.sayweee.weee.module.mkpl.bean.GlobalCartListResponse;
import com.sayweee.weee.module.mkpl.service.GlobalViewModel;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackManger;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.tab.ITabEventDispatch;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.core.lifecycle.ViewModelProviders;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.Map;

//
// Created by Thomsen on 14/07/2023.
// Copyright (c) 2023 Weee LLC. All rights reserved.
//
public class GlobalExpFragment extends MkplBaseFragment<GlobalViewModel> implements MainTabFragment, ITabEventDispatch {

    private static final String DEFAULT_PAGE_KEY = "home";
    private static final String BUNDLE_IN_TAB = "in_tab";

    private WrapperDialog shareDialog;
    private PopupSlideDialog termsDialog;

    private FragmentGlobalExpBinding binding;

    private GlobalMiniCartViewModel cartViewModel;

    private boolean isInTab() {
        return getArguments() != null && getArguments().getBoolean(BUNDLE_IN_TAB, false);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.fragment_global_exp;
    }

    public static GlobalExpFragment newInstance(boolean inTab) {
        GlobalExpFragment fragment = new GlobalExpFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean(BUNDLE_IN_TAB, inTab);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void initView(View view, Bundle bundle) {
        binding = FragmentGlobalExpBinding.bind(contentView);
        setupTitle();
        initListener();
    }

    @Override
    public void loadData() {
        setupPage(true);
    }

    @Override
    public <VM> VM createModel() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            cartViewModel = ViewModelProviders.of(activity).get(GlobalMiniCartViewModel.class);
            cartViewModel.injectLifecycle(getLifecycle());
        }
        return super.createModel();
    }

    @Override
    public void attachModel() {
        viewModel.shareData.observe(this, shareBean -> {
            if (shareDialog == null) {
                shareDialog = new ShareDialog(activity)
                        .setShareData(shareBean);
                shareDialog.show();
                trackShare();
            } else {
                if (!shareDialog.isShowing()) {
                    shareDialog.show();
                    trackShare();
                }
            }
        });

        attachCartViewModel();
    }

    private void attachCartViewModel() {
        if (cartViewModel == null) return;
        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner == null) return;

        cartViewModel.getGlobalCartListResponseLiveData().observe(viewLifecycleOwner, this::handleGlobalCartListResponse);
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentPause();
        StatusBarManager.setStatusBar(this, binding.vStatus, true);
        showGlobalMiniCartRoot(true);
        if (cartViewModel != null) {
            cartViewModel.getSellerCartFloat(/* sellerId= */null);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (LifecycleProvider.get().getTopActivity().getClass().isAssignableFrom(GlobalPlusActivity.class)) {
            if (getCurrentFragment() instanceof GlobalPlusFragment) {
                ((GlobalPlusFragment) getCurrentFragment()).setBackgroundTime();
            }
        }
    }

    @Override
    public void onTabDoubleTap() {
        if (getCurrentFragment() instanceof GlobalPlusFragment) {
            ((GlobalPlusFragment) getCurrentFragment()).scrollToTop();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (shareDialog != null) {
            shareDialog = null;
        }
    }

    private void setupTitle() {
        binding.layoutTitle.ivBack.setVisibility(isInTab() ? View.GONE : View.VISIBLE);

        binding.layoutTitle.tvTitle.setText(R.string.s_tab_global);
        binding.layoutTitle.ivShare.setVisibility(View.VISIBLE);
        binding.layoutTitle.ivTerms.setVisibility(View.GONE);
//        termsDialog = new PopupSlideDialog() {
//            @Override
//            public void onJsCallback(WebView view, JSFunction<?> jsFunction) {
//                super.onJsCallback(view, jsFunction);
//                if (jsFunction != null && Constants.JSBridgeFunction.ON_POPUP_WINDOW_DID_FINISH.equalsIgnoreCase(jsFunction.functionname)) {
//                    ViewTools.setViewVisible(true, findViewById(R.id.iv_terms));
//                }
//            }
//        }.loadUrl(AppConfig.HOST_WEB + Constants.Url.TERMS_OF_GLOBAL);
    }

    private void setupPage(final boolean isWaterfall) {
        //noinspection UnnecessaryLocalVariable
        final boolean isWaterfallOverride = isWaterfall;
        binding.viewpager2.setUserInputEnabled(false);
        binding.viewpager2.setAdapter(new FragmentStateAdapter(this) {

            @NonNull
            @Override
            public Fragment createFragment(int position) {
                if (isWaterfallOverride) {
                    return GlobalPlusFragment.newInstance(DEFAULT_PAGE_KEY, isInTab());
                } else {
                    return GlobalTabFragment.newInstance(DEFAULT_PAGE_KEY, true);
                }
            }

            @Override
            public int getItemCount() {
                return 1;
            }
        });
    }

    private void initListener() {
        ViewTools.setViewOnSafeClickListener(binding.layoutTitle.ivBack, v -> {
            if (getActivity() != null) {
                getActivity().finish();
            }
        });

        ViewTools.setViewOnSafeClickListener(binding.layoutTitle.ivShare, v -> {
            if (shareDialog == null) {
                String pageKey = "";
                if (getCurrentFragment() instanceof GlobalTabFragment) {
                    pageKey = DEFAULT_PAGE_KEY;
                }
                viewModel.getGlobalShare(pageKey);
            } else {
                if (!shareDialog.isShowing()) {
                    shareDialog.show();
                    trackShare();
                }
            }
        });

        ViewTools.setViewOnSafeClickListener(binding.layoutTitle.ivTerms, v -> {
            if (termsDialog != null && !termsDialog.isShowing()) {
                termsDialog.show();
            }
        });

        LifecycleOwner viewLifecycleOwner = getViewLifecycleOwnerLiveData().getValue();
        if (viewLifecycleOwner != null) {
            getGlobalCartFragmentManager().setFragmentResultListener(
                    GlobalMiniCartFragment.REQUEST_KEY,
                    viewLifecycleOwner,
                    (requestKey, result) -> {
                        boolean dismiss = result.getBoolean(GlobalMiniCartFragment.RESULT_BUNDLE_DISMISS, false);
                        resetScrollTopParams(!dismiss);
                    }
            );
        }
    }

    private void trackShare() {
        String targetType;
        Fragment currentFragment = getCurrentFragment();
        if (currentFragment instanceof GlobalTabFragment) {
            targetType = WeeeEvent.PageView.MKPL_VENDOR_LANDING;
        } else {
            targetType = WeeeEvent.PageView.MKPL_WATERFALL;
        }
        EagleTrackManger.get().trackEagleClickAction("global_page",
                -1,
                targetType,
                EagleTrackEvent.ClickType.SHARE
        );
    }

    @Nullable
    public Fragment getCurrentFragment() {
        int currentItem = binding.viewpager2.getCurrentItem();
        return getChildFragmentManager().findFragmentByTag("f" + currentItem);
    }

    @Override
    public void onPageSelected(Map<String, Object> params) {
    }

    @Override
    public void onPageDeselected() {
        showGlobalMiniCartRoot(false);
    }

    @NonNull
    private FragmentManager getGlobalCartFragmentManager() {
        return isInTab() ? getParentFragmentManager() : getChildFragmentManager();
    }

    private void handleGlobalCartListResponse(@Nullable GlobalCartListResponse response) {
        if (response == null) return;
        if (!EmptyUtils.isEmpty(response.getRequest().getToken())) return;
        if (!response.isSuccess(false)) return;
        GlobalCartListBean cartListBean = response.getResponse();
        if (cartListBean == null || cartListBean.isEmpty()) {
            dismissGlobalMiniCart();
        } else {
            showGlobalMiniCart(cartListBean);
        }
    }

    private void showGlobalMiniCartRoot(boolean shouldShow) {
        FragmentActivity activity = getActivity();
        View container = null;
        if (activity instanceof MainActivity) {
            container = activity.findViewById(R.id.fragment_container_main);
        }
        if (container == null) {
            return;
        }

        if (isInTab()) {
            boolean isShow = shouldShow && OrderManager.get().isSupportGlobalPlusTab();
            ViewTools.setViewVisible(container, isShow);
        } else {
            ViewTools.setViewVisible(container, false);
        }
    }

    @Nullable
    private GlobalMiniCartFragment getGlobalMiniCartFragment() {
        FragmentManager fragmentManager = getGlobalCartFragmentManager();
        String fragmentTag = GlobalMiniCartFragment.getFragmentTag(null);
        return (GlobalMiniCartFragment) fragmentManager.findFragmentByTag(fragmentTag);
    }

    private void dismissGlobalMiniCart() {
        GlobalMiniCartFragment fragment = getGlobalMiniCartFragment();
        if (fragment != null) {
            fragment.dismissCart();
        }
    }

    private void showGlobalMiniCart(@Nullable GlobalCartListBean cartListBean) {
        GlobalMiniCartFragment fragment = getGlobalMiniCartFragment();
        if (fragment == null) {
            fragment = GlobalMiniCartFragment.getFragment(cartListBean, isInTab());
            FragmentTransaction transaction = getGlobalCartFragmentManager().beginTransaction();
            transaction.setCustomAnimations(GlobalMiniCartFragment.getEnterAnim(), GlobalMiniCartFragment.getExitAnim());
            if (isInTab()) {
                transaction.add(R.id.fragment_container_main, fragment, GlobalMiniCartFragment.getFragmentTag(null));
            } else {
                transaction.add(R.id.fragment_container, fragment, GlobalMiniCartFragment.getFragmentTag(null));
            }
            transaction.commitAllowingStateLoss();
        } else {
            fragment.updateCartListData(cartListBean);
        }
        resetScrollTopParams(true);
    }

    private void resetScrollTopParams(boolean hasMiniCart) {
        if (getCurrentFragment() instanceof GlobalPlusFragment) {
            ((GlobalPlusFragment) getCurrentFragment()).resetScrollTopParams(hasMiniCart);
        }
    }

    public boolean hasGlobalMiniCart() {
        return getGlobalMiniCartFragment() != null;
    }
}
