package com.sayweee.weee.module.home.bean;

import com.sayweee.weee.utils.EmptyUtils;

import java.util.Map;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/30.
 * Desc:
 */
public class MainBannerProperty implements IProperty {

    public String version;  //v2表示为新版
    public String scroll;   //auto 自动滚动 unset 未设置
    public String event_key;
    public String autoplay;

    @Override
    public IProperty parseProperty(Map<String, String> map) {
        if (!EmptyUtils.isEmpty(map)) {
            this.scroll = map.get("scroll");
            this.version = map.get("version");
            this.event_key = map.get("event_key");
            this.autoplay = map.get("autoplay");
        }
        return this;
    }

    /**
     * 当前是否自动滚动
     *
     * @return
     */
    public boolean isAutoScroll() {
        return "auto".equalsIgnoreCase(scroll) || isAutoPlay();
    }

    public boolean isAutoPlay() {
        return "yes".equalsIgnoreCase(autoplay);
    }

    public boolean isV3() {
        return "v3".equalsIgnoreCase(version);
    }
}
