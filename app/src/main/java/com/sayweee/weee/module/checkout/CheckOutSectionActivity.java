package com.sayweee.weee.module.checkout;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.braintreepayments.api.Card;
import com.braintreepayments.api.CardClient;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sayweee.weee.R;
import com.sayweee.weee.databinding.LayoutMemberPlanStickyBarBinding;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.WeChatManager;
import com.sayweee.weee.module.account.helper.KeyboardChangeHelper;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.base.adapter.AdapterDataType;
import com.sayweee.weee.module.cart.widget.CheckOutBottomView;
import com.sayweee.weee.module.checkout.adapter.CheckOutPointsProvider;
import com.sayweee.weee.module.checkout.adapter.CheckOutSectionAdapter;
import com.sayweee.weee.module.checkout.adapter.CheckoutDeliveryWindowAdapter;
import com.sayweee.weee.module.checkout.bean.CashAppPayBean;
import com.sayweee.weee.module.checkout.bean.CheckoutBean;
import com.sayweee.weee.module.checkout.bean.CheckoutCouponData;
import com.sayweee.weee.module.checkout.bean.CheckoutDeliveryData;
import com.sayweee.weee.module.checkout.bean.CheckoutPaymentData;
import com.sayweee.weee.module.checkout.bean.CheckoutReviewOrderData;
import com.sayweee.weee.module.checkout.bean.CouponBean;
import com.sayweee.weee.module.checkout.bean.DeliveryWindowBean;
import com.sayweee.weee.module.checkout.bean.PayPalPayBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutOrderReviewsBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutTipInfoBean;
import com.sayweee.weee.module.checkout.bean.PreCheckoutV2Bean;
import com.sayweee.weee.module.checkout.bean.VenmoPayBean;
import com.sayweee.weee.module.checkout.service.CheckOutSectionViewModel;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleImpressionTrackerIml;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.pay.BraintreePaymentHelper;
import com.sayweee.weee.service.pay.PaymentCallbackHelper;
import com.sayweee.weee.service.pay.PaymentHelper;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.VeilTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnItemChildSafeClickListener;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.widget.FullNameDialog;
import com.sayweee.weee.widget.recycler.RecyclerViewTools;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;
import com.sayweee.wrapper.utils.KeyboardUtils;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;
import com.stripe.android.ApiResultCallback;
import com.stripe.android.PaymentConfiguration;
import com.stripe.android.Stripe;
import com.stripe.android.model.Token;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Author:  wld
 */
public class CheckOutSectionActivity extends WrapperMvvmActivity<CheckOutSectionViewModel> {

    public static final int REQUEST_PAY_METHOD = 101;
    public static final int REQUEST_COUPON = 102;
    public static final int REQUEST_DELIVERY_ADDRESS = 103;
    public static final int REQUEST_ORDER_NOTE = 105;
    public static final int REQUEST_ORDER_EMAIL = 106;
    public static final int REQUEST_DATE = 107;

    private RecyclerView rvList;
    private CheckOutSectionAdapter adapter;
    private View vShadow;
    private View layoutAlertTips;
    private ImageView ivTipsAlert;
    private SmartRefreshLayout mSmartRefreshLayout;
    private TextView tvTipsAlert;
    private TextView tvPoints;
    private CheckOutBottomView bottomView;
    private LayoutMemberPlanStickyBarBinding memberPlanBarBinding;
    private String cartDomain;
    private String planId;
    private PreCheckoutV2Bean preCheckoutV2Bean;
    private PreCheckoutTipInfoBean.OptionsBean optionsBean;

    private final BraintreePaymentHelper braintreePaymentHelper = new BraintreePaymentHelper();
    private Stripe stripe;

    private boolean usePoints;
    private int refreshFlag = CheckOutSectionViewModel.REFRESH_FLAG_ENABLE;
    EagleImpressionTrackerIml eagleImpressionTracker;
    private static final String DEFAULT_PAYMENT = "B";
    private KeyboardChangeHelper keyboardChangeHelper;
    private boolean hasFullNameChecked;
    private boolean isCvcInvalid;
    /**
     * 连续点击points需要前端先直接赋值，再展示后端数据。该字段控制是否刷新
     */
    private boolean noRefresh;
    private FullNameDialog fullNameDialog;
    private String cvcText;
    private double totalPlanTip = 0;
    private double planPrice = 0;

    private String selectedWindowIds;

    public static Intent getIntent(Context context, String cartDomain, String cartId) {
        return new Intent(context, CheckOutSectionActivity.class)
                .putExtra("cart_domain", cartDomain)
                .putExtra("cart_id", cartId);
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_checkout_section;
    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {
        setWrapperTitle(R.string.s_checkout);
        setWrapperDivider(null);
        setWrapperColor();

        cartDomain = getIntent().getStringExtra("cart_domain");
        eagleImpressionTracker = new EagleImpressionTrackerIml();
        vShadow = findViewById(R.id.v_shadow);
        rvList = findViewById(R.id.rv_list);
        ivTipsAlert = findViewById(R.id.iv_tips_alert);
        tvTipsAlert = findViewById(R.id.tv_tips_alert);
        tvPoints = findViewById(R.id.tv_points);
        layoutAlertTips = findViewById(R.id.layout_alert_tips);
        mSmartRefreshLayout = findViewById(R.id.mSmartRefreshLayout);
        bottomView = findViewById(R.id.bottom_view);
        memberPlanBarBinding = LayoutMemberPlanStickyBarBinding.bind(findViewById(R.id.layout_member_plan_sticky_bar));
        memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, false);
        ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.getRoot(), false);
        ViewTools.setViewVisible(bottomView, false);
        stripe = new Stripe(getApplicationContext(), PaymentConfiguration.getInstance(getApplicationContext()).getPublishableKey());
        setKeyboardObserver();
        rvList.setLayoutManager(new LinearLayoutManager(activity));
        adapter = new CheckOutSectionAdapter().setOnViewChangedCallback(new CheckOutSectionAdapter.OnViewCallback() {
            @Override
            public void onEditTextFocusChanged(View v) {
                setKeyboardOnFocus(v);
            }

            @Override
            public void updateTipsData(PreCheckoutTipInfoBean.OptionsBean bean) {
                optionsBean = bean;
                viewModel.preCheckoutV2(true, cartDomain, optionsBean, getPlanId(), getSelectedWindowIds());
            }

            @Override
            public void updatePoints(boolean checked) {
                bottomView.setCanCheckOut(false, R.string.s_place_order);
                bottomView.loading();
                setServerUsePoints(checked);
            }

            @Override
            public void onCvcEditTextNoFocus(String cvcText, boolean isCvcInvalid) {
                CheckOutSectionActivity.this.cvcText = cvcText;
                CheckOutSectionActivity.this.isCvcInvalid = isCvcInvalid;
            }
        });

        adapter.setOnCheckOutPointsListener(new CheckOutPointsProvider.OnCheckoutPointsListener() {
            @Override
            public void updateMemberPointsChecked(String planId, double planPrice, int position) {
                if (bottomView.isLoading()) {
                    noRefresh = true;
                }
                adapter.updatePointsPosition(position);
                CheckOutSectionActivity.this.planId = planId;
                CheckOutSectionActivity.this.planPrice = planPrice;
                bottomView.setCanCheckOut(false, R.string.s_place_order);
                bottomView.loading();
                viewModel.preCheckoutV2(true, cartDomain, optionsBean, getPlanId(), getSelectedWindowIds());
            }

        });

        adapter.setOnDeliveryWindowActionListener(new CheckoutDeliveryWindowAdapter.OnDeliveryWindowActionListener() {
            @Override
            public void onDeliveryWindowClick(@NonNull DeliveryWindowBean window) {
                selectedWindowIds = adapter != null ? adapter.getSelectedWindowIds() : null;
                viewModel.refreshData.postValue(""); // reload
            }

            @Override
            public void onDeliveryWindowInfoClick(@NonNull DeliveryWindowBean window) {
                showDeliveryWindowInfoDialog(window.viewMoreUrl);
            }
        });

        rvList.setAdapter(adapter);
        adapter.setImpressionTracker(eagleImpressionTracker);
        rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                boolean isNotTop = !EmptyUtils.isEmpty(preCheckoutV2Bean) && EmptyUtils.isEmpty(preCheckoutV2Bean.reminder_content) && recyclerView.canScrollVertically(-1);
                ViewTools.setViewVisibilityIfChanged(vShadow, isNotTop);

                invalidateMemberPlanStickyBar(recyclerView);
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    reportImpressionEvent();
                }
            }
        });
        adapter.setOnItemChildClickListener(new OnItemChildSafeClickListener() {
            @Override
            public void onItemChildClickSafely(BaseQuickAdapter quickAdapter, View view, int position) {
                Object item = adapter.getItem(position);
                if (item instanceof CheckoutDeliveryData) {
                    startActivityForResult(DeliveryAddressPickerActivity.getIntent(activity, preCheckoutV2Bean, "checkout"), REQUEST_DELIVERY_ADDRESS);
                } else if (item instanceof CheckoutPaymentData) {
                    PreCheckoutV2Bean.PaymentInfoBean bean = ((CheckoutPaymentData) item).paymentInfoDTO;
                    startActivityForResult(PaymentMethodActivity.getIntent(activity, bean.payment_category, bean.payment_infos, usePoints), REQUEST_PAY_METHOD);
                } else if (item instanceof CheckoutReviewOrderData) {
                    PreCheckoutOrderReviewsBean bean = ((CheckoutReviewOrderData) item).orderReviewsDTO;
                    if (view.getId() == R.id.ll_product || view.getId() == R.id.iv_arrow) {
                        if (!EmptyUtils.isEmpty(bean.group_buy_user_products) || !EmptyUtils.isEmpty(bean.order_lines)) {
                            List<Object> list = new ArrayList<>();
                            if (!EmptyUtils.isEmpty(bean.group_buy_user_products)) {
                                list.addAll(bean.group_buy_user_products);
                            } else {
                                list.addAll(bean.order_lines);
                            }
                            String tag = PopOrderProductsFragment.class.getName() + preCheckoutV2Bean.checkout_pre_id;
                            PopOrderProductsFragment orderProductsFragment;
                            orderProductsFragment = PopOrderProductsFragment.newInstance(list, bean.quantity);
                            orderProductsFragment.show(getSupportFragmentManager(), tag);
                        }
                    } else if (view.getId() == R.id.tv_support_change_date) {
                        changeDateTrackClickAction(((CheckoutReviewOrderData) item).orderReviewsDTO.type);
                        boolean confirmShippingFee = bean.shipping_info != null && bean.shipping_info.isShowShippingDialog();
                        startActivityForResult(DateActivity.getIntent(activity, confirmShippingFee), REQUEST_DATE);
                    }
                } else if (item instanceof CheckoutCouponData) {
                    startActivityForResult(CouponActivity.getIntent(activity, cartDomain, preCheckoutV2Bean.coupon_info != null ? preCheckoutV2Bean.coupon_info.code : null), REQUEST_COUPON);
                }
            }
        });

        bottomView.setOnViewClickListener(R.id.tv_checkout, new OnSafeClickListener(1000L) {
            @Override
            public void onClickSafely(View v) {
                trackClickAction("checkout", EagleTrackEvent.ClickType.NORMAL);
                checkout();
            }
        });

        mSmartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                viewModel.preCheckoutV2(true, cartDomain, optionsBean, getPlanId(), getSelectedWindowIds());
            }
        });

        if (AdsManager.ENABLE_POST_CHECKOUT_POPUP) {
            AdsManager.get().preloadPostCheckoutAdsCreative();
        }

        showVeilTemplate(true);
    }

    @Override
    public void loadData() {
        // no op
        String cartId = getIntent().getStringExtra("cart_id");
        viewModel.setCartId(cartId);
    }

    @Override
    public void attachModel() {
        viewModel.adapterData.observe(this, new Observer<List<AdapterDataType>>() {
            @Override
            public void onChanged(List<AdapterDataType> adapterDataTypes) {
                if (noRefresh) {
                    noRefresh = false;
                    return;
                }
                adapter.setNewData(adapterDataTypes);
            }
        });

        // viewModel.preCheckoutV2() response
        viewModel.responseData.observe(this, new Observer<PreCheckoutV2Bean>() {
            @Override
            public void onChanged(PreCheckoutV2Bean preCheckoutV2Bean) {
                if (noRefresh) {
                    return;
                }
                CheckOutSectionActivity.this.preCheckoutV2Bean = preCheckoutV2Bean;
                ViewTools.setViewVisible(bottomView, true);
                bottomView.setCanCheckOut(true, R.string.s_place_order);
                if (!EmptyUtils.isEmpty(preCheckoutV2Bean.fee_info)) {
                    totalPlanTip = preCheckoutV2Bean.fee_info.total_without_member_plan_and_tip;
                    bottomView.setAmount(preCheckoutV2Bean.fee_info.final_amount, null);
                }

                if (!EmptyUtils.isEmpty(preCheckoutV2Bean.point_info)) {
                    usePoints = preCheckoutV2Bean.point_info.points_price > 0;
                }

                if (preCheckoutV2Bean.tipIsNotNull()) {
                    PreCheckoutTipInfoBean tipBean = preCheckoutV2Bean.tip_info;
                    optionsBean = tipBean.options.get(tipBean.selected_option_index);
                }

                ViewTools.setViewVisible(tvPoints, preCheckoutV2Bean.pointsIsNotNull());
                if (preCheckoutV2Bean.pointsIsNotNull()) {
                    bottomView.setCouponReminder(preCheckoutV2Bean.point_info.total_reward_points_text);
                }
                if (fullNameDialog != null && fullNameDialog.isShowing()) {
                    showFullNameDialog();
                }
                bottomView.setAttachedData();
                fillTipsAlert();
                bindMemberPlanStickyBar();
                preCheckBraintreeDeviceData();
                reportImpressionEvent();
                mSmartRefreshLayout.finishRefresh();
                showVeilTemplate(false);
                showShippoDialog();
            }
        });

        // viewModel.checkout() response
        viewModel.checkoutData.observe(this, new Observer<CheckoutBean>() {
            @Override
            public void onChanged(CheckoutBean bean) {
                if (bean.need_pay && bean.final_amount != null) {
                    double amount = bean.final_amount.doubleValue();
                    if (preCheckoutV2Bean.isPayByAlipayStripe()) {
                        viewModel.execAlipay(false, true, bean.order_ids, amount);
                    } else if (preCheckoutV2Bean.isPayByAlipayCitcon()) {
                        viewModel.execAlipay(true, true, bean.order_ids, amount);
                    } else if (preCheckoutV2Bean.isPayByWechatCitcon()) {
                        viewModel.execWechatPay(true, true, bean.order_ids, amount);
                    } else if (preCheckoutV2Bean.isPayByPayPalBraintree()) {
                        execPayPalPay(bean.order_ids, amount, bean.success_url, bean.cancel_url);
                    } else if (preCheckoutV2Bean.isPayByVenmoBraintree()) {
                        execVenmoPay(bean.order_ids, amount, bean.success_url, bean.cancel_url);
                    } else if (preCheckoutV2Bean.isPayByCashApp()) {
                        execCashAppPay(bean.order_ids, amount, bean.success_url, bean.cancel_url);
                    } else {
                        toWeb(bean.next_url);
                    }
                } else {
                    toWeb(bean.next_url);
                }
            }
        });

        viewModel.payLoadingData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer flag) {
                //设置本次禁止刷新 避免支付后重复请求
                refreshFlag = flag;
            }
        });

        viewModel.payResultData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String url) {
                toWeb(url);
            }
        });

        viewModel.refreshData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                viewModel.preCheckoutV2(true, cartDomain, optionsBean, getPlanId(), getSelectedWindowIds());
            }
        });

        viewModel.failureData.observe(this, new Observer<FailureBean>() {
            @Override
            public void onChanged(FailureBean failureBean) {
                String messageId = failureBean.getMessageId();
                if (messageId != null) {
                    switch (messageId) {
                        case CheckOutSectionViewModel.ORDER_EMPTY:
                            String message = failureBean.getMessage();
                            if (message != null) {
                                Toaster.showToast(message);
                            }
                            finish();
                            return;
                        case CheckOutSectionViewModel.ORDER_ADDRESS_EMPTY:
                        case CheckOutSectionViewModel.ORDER_ADDRESS_ERROR:
                        case CheckOutSectionViewModel.ORDER_PHONE_ERROR:
                            setAddressError(failureBean.getMessage());
                            return;
                        case CheckOutSectionViewModel.ORDER_NOT_MAIL:
                            showAddressErrorDialog(failureBean.getMessage());
                            return;
                        case CheckOutSectionViewModel.ORDER_COUPON_ERROR:
                        case CheckOutSectionViewModel.ORDER_SHOW_ERROR:
                        case CheckOutSectionViewModel.ORDER_FORCE_REFRESH:
                        case CheckOutSectionViewModel.ORDER_FORCE_REFRESH_2:
                            String tag = failureBean.getObject();
                            //仅checkout接口允许强制刷新，避免死锁逻辑
                            if (CheckOutSectionViewModel.TAG_FORCE_REFRESH.equals(tag)) {
                                viewModel.preCheckoutV2(true, cartDomain, optionsBean, getPlanId(), getSelectedWindowIds());
                            }
                            break;
                        default:
                            break;
                    }
                }
                viewModel.showToast(failureBean.getMessage());
            }
        });

        viewModel.couponData.observe(this, new Observer<CouponBean>() {
            @Override
            public void onChanged(CouponBean couponBean) {
                if (!EmptyUtils.isEmpty(couponBean) && !EmptyUtils.isEmpty(couponBean.coupons_valid)) {
                    viewModel.couponSize = couponBean.coupons_valid.size();
                }
                adapter.updateCoupon(couponBean);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 禁用自动刷新逻辑 0 不禁用 1 禁用一次 其他 禁用多次
        if (refreshFlag == CheckOutSectionViewModel.REFRESH_FLAG_ENABLE) {
            planId = null;
            viewModel.preCheckoutV2(true, cartDomain, optionsBean, null, getSelectedWindowIds());
        } else if (refreshFlag == CheckOutSectionViewModel.REFRESH_FLAG_DISABLE_ONCE) {
            refreshFlag = 0;
        } else if (refreshFlag == CheckOutSectionViewModel.REFRESH_FLAG_CITCON) {
            PaymentHelper.onCitconPayResume();
        }
        AppAnalytics.logPageView(WeeeEvent.PageView.CHECKOUT, this, new TrackParams().put("is_rtg", false).get());
        eagleImpressionTracker.onPageResume(rvList);
    }

    @Override
    protected void onPause() {
        super.onPause();
        eagleImpressionTracker.onPagePause(rvList);
    }

    private void onPaymentResult(Intent intent) {
        String payType = intent.getStringExtra("payType");
        String profile = intent.getStringExtra("cardProfile");
        String type = intent.getStringExtra("cardType");
        if ((Constants.PayType.CREDIT_CARD.equals(payType) || Constants.PayType.STRIPE.equals(payType) || Constants.PayType.BRAINTREE.equals(payType))
                && EmptyUtils.isEmpty(profile) && EmptyUtils.isEmpty(type)
        ) {
            //校验卡支付信息
            return;
        }

        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType item = adapter.getData().get(i);
            if (item instanceof CheckoutPaymentData) {
                PreCheckoutV2Bean.PaymentInfoBean bean = ((CheckoutPaymentData) item).paymentInfoDTO;
                if (bean.default_payment_info != null) {
                    bean.default_payment_info.tail_num = profile;
                    bean.default_payment_info.card_type = type;
                }
                bean.payment_category = payType;
                adapter.notifyItemChanged(i);
                break;
            }
        }
    }

    private void fillTipsAlert() {
        PreCheckoutV2Bean.ReminderContentBean reminder;
        reminder = preCheckoutV2Bean != null ? preCheckoutV2Bean.reminder_content : null;
        if (reminder == null) {
            ViewTools.setViewVisibilityIfChanged(layoutAlertTips, false);
            return;
        }
        layoutAlertTips.setBackgroundColor(ViewTools.parseColor(reminder.reminder_bg_color, Color.WHITE));
        if (!EmptyUtils.isEmpty(reminder.reminder_icon_url)) {
            ImageLoader.load(activity, ivTipsAlert, WebpManager.convert(ImageSpec.SPEC_32, reminder.reminder_icon_url), R.color.color_place);
            ViewTools.setViewVisibilityIfChanged(ivTipsAlert, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(ivTipsAlert, false);
        }
        ViewTools.setViewHtml(tvTipsAlert, reminder.reminder_text);
        ViewTools.setViewVisibilityIfChanged(layoutAlertTips, true);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_ORDER_EMAIL) {
                executePay();
            } else if (requestCode == REQUEST_DELIVERY_ADDRESS || requestCode == REQUEST_DATE) {
                optionsBean = null;
                selectedWindowIds = null;
            } else {
                if (data != null) {
                    switch (requestCode) {
                        case REQUEST_PAY_METHOD:
                            onPaymentResult(data);
                            break;
                        case REQUEST_COUPON:
                            Serializable result = data.getSerializableExtra("applied_coupon");
                            if (result instanceof PreCheckoutV2Bean.CouponInfoBean) {
                                adapter.quickUpdateCoupon((PreCheckoutV2Bean.CouponInfoBean) result);
                            } else {
                                if (result instanceof CouponBean) {
                                    adapter.updateCoupon((CouponBean) result);
                                    viewModel.couponSize = ((CouponBean) result).coupons_valid.size();
                                } else {
                                    viewModel.couponSize = 0;
                                    adapter.updateCoupon(null);
                                }
                                adapter.quickUpdateCoupon(null);
                            }
                            break;
                        case REQUEST_ORDER_NOTE:
                            //rtg使用，暂时用不到
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private void setKeyboardOnFocus(final View target) {
        if (target != null) {
            target.postDelayed(new Runnable() {
                @Override
                public void run() {
                    KeyboardUtils.setKeyboardVisible(activity, target, true);
                }
            }, 50);
        }
    }

    protected void setKeyboardObserver() {
        keyboardChangeHelper = new KeyboardChangeHelper(getContentView());
        keyboardChangeHelper.startObserve().setOnKeyboardStatusListener(new KeyboardChangeHelper.OnSimpleKeyboardStatusListener() {

            @Override
            public void onKeyboardHide() {
                adapter.updateOtherTipStatus();
            }
        });
    }

    //***********************************************************************************************

    /**
     * braintree pay
     **/

    private void preCheckBraintreeDeviceData() {
        PreCheckoutV2Bean preCheckoutBean = preCheckoutV2Bean;
        if (preCheckoutBean == null) {
            return;
        }

        if (preCheckoutBean.isPayByBraintree()
                || preCheckoutBean.isPayByVenmoBraintree()
                || preCheckoutBean.isPayByPayPalBraintree()
        ) {
            braintreePaymentHelper.collectDeviceData(this, (deviceData, error) -> {
                // no op
            });
        }
    }

    // Braintree card
    private void execBraintreeCardPay(String cvc) {
        final CheckOutSectionViewModel.CheckoutRequest request = prepareCheckoutRequest();
        viewModel.setLoadingStatus(true);
        braintreePaymentHelper.collectDeviceData(this, (deviceData, error) -> {
            if (deviceData != null) {
                request.deviceData = deviceData;
                Card card = new Card();
                card.setCvv(cvc);
                CardClient client = new CardClient(braintreePaymentHelper.getBraintreeClient(CheckOutSectionActivity.this));
                client.tokenize(card, (cardNonce, ex) -> {
                    if (cardNonce != null) {
                        request.cvv_token = cardNonce.getString();
                        viewModel.checkout(request);
                    } else {
                        viewModel.setLoadingStatus(false);
                    }
                });
            } else {
                viewModel.setLoadingStatus(false);
            }
        });
    }

    //***********************************************************************************************

    /**
     * stripe pay
     **/

    private void execStripeCardPay(String cvc) {
        final CheckOutSectionViewModel.CheckoutRequest request = prepareCheckoutRequest();
        viewModel.setLoadingStatus(true);
        stripe.createCvcUpdateToken(cvc, new ApiResultCallback<Token>() {
            @Override
            public void onSuccess(@NonNull Token token) {
                request.cvv_token = token.getId();
                viewModel.checkout(request);
            }

            @Override
            public void onError(@NonNull Exception e) {
                viewModel.setLoadingStatus(false);
            }
        });
    }

    // PayPal pay
    private void execPayPalPay(List<Integer> orderIds, double amount, String successUrl, String cancelUrl) {
        viewModel.setLoadingStatus(true);
        braintreePaymentHelper.collectDeviceData(this, (deviceData, error) -> {
            if (deviceData != null) {
                PayPalPayBean payBean = new PayPalPayBean();
                payBean.setOrderIds(orderIds);
                payBean.setAmount(amount);
                payBean.setDeviceData(deviceData);
                payBean.setSuccessUrl(successUrl);
                payBean.setCancelUrl(cancelUrl);

                PaymentHelper.OnPaymentCallback c = (result, url) -> {
                    if (!EmptyUtils.isEmpty(url)) {
                        toWeb(url);
                    }
                };
                PaymentCallbackHelper.get().registerPaymentCallback(activity, c);
                viewModel.payLoadingData.postValue(CheckOutSectionViewModel.REFRESH_FLAG_DISABLE);
                startActivity(PayPalPaySetupActivity.getIntent(this, payBean));
            } else {
                viewModel.setLoadingStatus(false);
                toWeb(cancelUrl);
            }
        });
    }

    // Venmo pay
    private void execVenmoPay(List<Integer> orderIds, double amount, String successUrl, String cancelUrl) {
        viewModel.setLoadingStatus(true);
        braintreePaymentHelper.collectDeviceData(this, (deviceData, error) -> {
            if (deviceData != null) {
                VenmoPayBean payBean = new VenmoPayBean();
                payBean.setOrderIds(orderIds);
                payBean.setAmount(amount);
                payBean.setDeviceData(deviceData);
                payBean.setSuccessUrl(successUrl);
                payBean.setCancelUrl(cancelUrl);

                PaymentHelper.OnPaymentCallback c = (result, url) -> {
                    if (!EmptyUtils.isEmpty(url)) {
                        toWeb(url);
                    }
                };
                PaymentCallbackHelper.get().registerPaymentCallback(activity, c);
                viewModel.payLoadingData.postValue(CheckOutSectionViewModel.REFRESH_FLAG_DISABLE);
                startActivity(VenmoPaySetupActivity.getIntent(this, payBean));
            } else {
                viewModel.setLoadingStatus(false);
                toWeb(cancelUrl);
            }
        });
    }

    // Cash App Pay
    private void execCashAppPay(List<Integer> orderIds, double amount, String successUrl, String cancelUrl) {
        CashAppPayBean payBean = new CashAppPayBean();
        payBean.setOrderIds(orderIds);
        payBean.setAmount(amount);
        payBean.setSuccessUrl(successUrl);
        payBean.setCancelUrl(cancelUrl);

        PaymentHelper.OnPaymentCallback c = (result, url) -> {
            if (!EmptyUtils.isEmpty(url)) {
                toWeb(url);
            }
        };
        PaymentCallbackHelper.get().registerPaymentCallback(activity, c);
        viewModel.setLoadingStatus(true);
        viewModel.payLoadingData.postValue(CheckOutSectionViewModel.REFRESH_FLAG_DISABLE);
        startActivity(CashAppPaySetupActivity.getIntent(this, payBean));
    }

    //***********************************************************************************************

    private void execPay() {
        final CheckOutSectionViewModel.CheckoutRequest request = prepareCheckoutRequest();
        if (preCheckoutV2Bean.isPayByBraintree()
                || preCheckoutV2Bean.isPayByPayPalBraintree()
                || preCheckoutV2Bean.isPayByVenmoBraintree()
        ) {
            viewModel.setLoadingStatus(true);
            braintreePaymentHelper.collectDeviceData(this, (deviceData, error) -> {
                if (deviceData != null) {
                    request.deviceData = deviceData;
                    viewModel.checkout(request);
                } else {
                    viewModel.setLoadingStatus(false);
                }
            });
        } else {
            viewModel.setLoadingStatus(true);
            viewModel.checkout(request);
        }
    }

    private CheckOutSectionViewModel.CheckoutRequest prepareCheckoutRequest() {
        CheckOutSectionViewModel.CheckoutRequest request = new CheckOutSectionViewModel.CheckoutRequest();
        request.cart_domain = cartDomain;
        request.checkoutAmount = preCheckoutV2Bean.fee_info.final_amount;
        request.checkout_pre_id = preCheckoutV2Bean.checkout_pre_id;
        request.cvv_token = null;
        request.tipBean = optionsBean;
        request.deviceData = null;
        request.plan_id = getPlanId();
        request.deliveryWindowIds = getSelectedWindowIdsOnCheckout();
        return request;
    }

    private void reportImpressionEvent() {
        if (rvList != null) {
            rvList.post(new Runnable() {
                @Override
                public void run() {
                    if (eagleImpressionTracker != null) {
                        eagleImpressionTracker.trackImpression(rvList);
                    }
                }
            });
        }
    }

    private void setServerUsePoints(boolean isUse) {
        if (viewModel != null && preCheckoutV2Bean != null) {
            int profileId = preCheckoutV2Bean.getProfileId();
            String paymentCategory = preCheckoutV2Bean.payment_info.payment_category;
            if (EmptyUtils.isEmpty(paymentCategory)) {
                paymentCategory = DEFAULT_PAYMENT;
            }
            viewModel.usePoints(paymentCategory, profileId, isUse);
        }
    }

    public void toWeb(String url) {
        startActivity(WebViewActivity.getIntent(activity, url));
        finish();
    }

    private void showAddressErrorDialog(String message) {
        trackT2Popup(message);
        new WrapperDialog(activity, R.style.CommonDialogTheme) {
            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_not_mail;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
            }

            @Override
            public void help(ViewHelper helper) {
                helper.setText(R.id.tv_title, message);
                helper.setOnClickListener(R.id.tv_update_address, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        trackClickAction("update_addr", EagleTrackEvent.ClickType.VIEW);
                        dismiss();
                        Intent a = DeliveryAddressPickerActivity.getIntent(activity, preCheckoutV2Bean, "checkout");
                        startActivityForResult(a, REQUEST_DELIVERY_ADDRESS);
                    }
                });
                helper.setOnClickListener(R.id.tv_modify_cart, new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        trackClickAction("cart", EagleTrackEvent.ClickType.VIEW);
                        dismiss();
                        finish();
                    }
                });
            }
        }.show();
    }

    private void showFullNameDialog() {
        if (fullNameDialog == null) {
            fullNameDialog = new FullNameDialog(activity);
        } else if (!fullNameDialog.isShowing()) {
            fullNameDialog.setData(preCheckoutV2Bean.address_info, hasFullNameChecked).show();
            fullNameDialog.setOnListener(new FullNameDialog.OnActionListener() {
                @Override
                public void onCommit(boolean isChecked) {
                    hasFullNameChecked = true;
                    checkout();
                }

                @Override
                public void onCheckedChange(boolean isChecked) {
                    hasFullNameChecked = isChecked;
                }
            });
        } else {
            fullNameDialog.setData(preCheckoutV2Bean.address_info, hasFullNameChecked);
        }
    }

    private void checkout() {
        //有full_name_tip 并且 没有选中过滤则打开弹窗校验
        if (preCheckoutV2Bean.hasFullName() && !hasFullNameChecked) {
            showFullNameDialog();
            return;
        }

        // 检查是否有地址
        if (!ensureDeliveryInfo(preCheckoutV2Bean)) {
            return;
        }

        // 全部抵扣
        boolean allDeducted = (!preCheckoutV2Bean.hasFinalAmount()) && usePoints;
        if (!allDeducted && !ensureNotAllDeducted()) {
            return;
        }

        if (preCheckoutV2Bean != null && !preCheckoutV2Bean.hasEmail()) {
            startActivityForResult(CheckOutAddEmailActivity.getIntent(activity), REQUEST_ORDER_EMAIL);
            return;
        }

        executePay();
    }

    private boolean ensureDeliveryInfo(@Nullable PreCheckoutV2Bean preCheckoutV2Bean) {
        if (preCheckoutV2Bean == null) {
            return false;
        }
        if (!viewModel.ensureDeliveryInfo(preCheckoutV2Bean)) {
            int deliveryInfoPosition = CollectionUtils.indexOfFirst(adapter.getData(), item -> item instanceof CheckoutDeliveryData);
            if (deliveryInfoPosition != -1) {
                Toaster.showToast(getString(R.string.please_choose_an_address));
                rvList.scrollToPosition(deliveryInfoPosition);
            }
            return false;
        }
        return true;
    }

    private boolean ensureNotAllDeducted() {
        //未全额抵扣的情况下，以下情况需要提示
        int paymentPosition = CollectionUtils.indexOfFirst(adapter.getData(), item -> item instanceof CheckoutPaymentData);
        //1. 支付方式为空
        //2. 使用信用卡支付 但没卡信息
        //3. 微信支付 但未安装微信
        if (preCheckoutV2Bean.paymentCategoryIsNull()
                || ((preCheckoutV2Bean.isPayByStripe() || preCheckoutV2Bean.isPayByBraintree()) && preCheckoutV2Bean.paymentDefaultIsNull())
                || (preCheckoutV2Bean.isPayByWechatCitcon() && !WeChatManager.getInstance().isWXAppInstalled())
        ) {
            AdapterDataType item = CollectionUtils.getOrNull(adapter.getData(), paymentPosition);
            if (item instanceof CheckoutPaymentData) {
                ((CheckoutPaymentData) item).seeErrorCard = true;
                adapter.notifyItemChanged(paymentPosition);
            }
            rvList.scrollToPosition(paymentPosition);
            return false;
        }
        //4. cvc本地校验失败
        if (preCheckoutV2Bean.isCheckCvv() && (isCvcInvalid || EmptyUtils.isEmpty(cvcText))) {
            Toaster.showToast(getString(R.string.please_enter_valid_cvc_number));
            rvList.scrollToPosition(paymentPosition);
            return false;
        }
        return true;
    }

    private void executePay() {
        if (preCheckoutV2Bean == null) {
            return;
        }
        boolean allDeducted = (!preCheckoutV2Bean.hasFinalAmount()) && usePoints;
        if (!allDeducted && preCheckoutV2Bean.isCheckCvv()) {
            if (preCheckoutV2Bean.isPayByBraintree()) {
                execBraintreeCardPay(cvcText);
            } else {
                execStripeCardPay(cvcText);
            }
        } else {
            execPay();
        }
    }

    private String getPlanId() {
        double tip = 0;
        if (!EmptyUtils.isEmpty(optionsBean)) {
            tip = optionsBean.tip;
        }
        return tip + totalPlanTip > planPrice ? null : planId;
    }

    private void setAddressError(String message) {
        for (int i = 0; i < adapter.getData().size(); i++) {
            AdapterDataType item = adapter.getData().get(i);
            if (item instanceof CheckoutDeliveryData) {
                ((CheckoutDeliveryData) item).errorTipMessage = message;
                adapter.notifyItemChanged(i);
                return;
            }
        }
    }

    private void showVeilTemplate(boolean visible) {
        VeilTools.show(findViewById(R.id.vl_checkout), visible);
        VeilTools.show(findViewById(R.id.vl_checkout_seil), visible);
        ViewTools.setViewVisible(findViewById(R.id.iv_shadow_veil), visible);
    }

    @Override
    protected void onDestroy() {
        keyboardChangeHelper.endObserve();
        super.onDestroy();
    }

    private void trackT2Popup(String message) {
        AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                .setMod_pos(0)
                .setSec_pos(0)
                .addContent(new TrackParams()
                        .put("action", "view")
                        .put("name", message)
                        .put("content_type", null)
                        .put("id", null)
                        .put("url", null)
                        .put("target_url", null)
                        .put("other_parameter", null)
                        .get())
                .build()
                .getParams());
    }

    private void trackClickAction(String targetNm, String clickType) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(targetNm)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(clickType)
                .build().getParams());
    }

    private void changeDateTrackClickAction(String secNm) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setTargetNm(EagleTrackEvent.TargetNm.DELIVERY_DATE)
                .setMod_nm(EagleTrackEvent.ModNm.CART)
                .setSec_nm(secNm)
                .setMod_pos(5)
                .setTargetType(EagleTrackEvent.TargetType.NORMAL_BUTTON)
                .setClickType(EagleTrackEvent.ClickType.VIEW)
                .build().getParams());
    }

    @Nullable
    private String getSelectedWindowIds() {
        return selectedWindowIds;
    }

    @Nullable
    private String getSelectedWindowIdsOnCheckout() {
        return adapter.getSelectedWindowIds();
    }

    private WrapperDialog previousDialog = null;

    private void showDeliveryWindowInfoDialog(String url) {
        if (EmptyUtils.isEmpty(url)) {
            return;
        }
        if (previousDialog != null) {
            previousDialog.dismiss();
        }

        PopupSlideDialog dialog = new PopupSlideDialog();
        dialog.loadUrl(url);
        int dialogHeight = (int) (getResources().getDisplayMetrics().heightPixels * 0.5);
        dialog.callDialogSize(-1, dialogHeight);
        dialog.show();
        previousDialog = dialog;
    }

    private void bindMemberPlanStickyBar() {
        PreCheckoutV2Bean preCheckoutBean = this.preCheckoutV2Bean;
        if (preCheckoutBean == null
                || preCheckoutBean.member_upgrade_plan_sticky_bar == null
                || CollectionUtils.any(preCheckoutBean.member_upgrade_plans, p -> p.selected)
        ) {
            memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, false);
            ViewTools.setViewOnSafeClickListener(memberPlanBarBinding.getRoot(), null);
            invalidateMemberPlanStickyBar(rvList);
            return;
        }

        PreCheckoutV2Bean.MemberUpgradeStickyBar bar = preCheckoutBean.member_upgrade_plan_sticky_bar;

        // background
        Integer backgroundColor = ViewTools.getColorByString(
                memberPlanBarBinding.getRoot().getContext(),
                bar.bg_color,
                R.color.color_root_durian_yellow_light_1
        );
        if (backgroundColor == null) {
            backgroundColor = ViewTools.parseColor("#FFFEC4", Color.WHITE);
        }
        memberPlanBarBinding.getRoot().setBackgroundColor(backgroundColor);

        // icon
        if (!EmptyUtils.isEmpty(bar.icon)) {
            ImageLoader.load(this, memberPlanBarBinding.ivIcon, WebpManager.convert(ImageSpec.SPEC_PRODUCT, bar.icon));
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivIcon, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivIcon, false);
        }

        // title
        if (!EmptyUtils.isEmpty(bar.title)) {
            memberPlanBarBinding.tvTitle.setText(ViewTools.fromHtml(bar.title));
        } else {
            memberPlanBarBinding.tvTitle.setText(null);
        }

        if (!EmptyUtils.isEmpty(bar.right_arrow_icon)) {
            ImageLoader.load(this, memberPlanBarBinding.ivArrow, WebpManager.convert(ImageSpec.SPEC_PRODUCT, bar.right_arrow_icon));
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivArrow, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(memberPlanBarBinding.ivArrow, false);
        }
        ViewTools.setViewOnSafeClickListener(memberPlanBarBinding.getRoot(), v -> {
            CheckOutSectionAdapter a = adapter;
            if (a != null) {
                int memberPlanPosition = CollectionUtils.indexOfFirst(
                        a.getData(),
                        item -> item.getType() == CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS
                );
                RecyclerViewTools.smoothScrollTo(rvList, memberPlanPosition);
            }
        });
        memberPlanBarBinding.getRoot().setTag(R.id.tag_item_data, true);
        invalidateMemberPlanStickyBar(rvList);
    }

    private void invalidateMemberPlanStickyBar(RecyclerView recyclerView) {
        if (recyclerView == null) {
            return;
        }
        LayoutMemberPlanStickyBarBinding b = memberPlanBarBinding;
        Object isValid = b.getRoot().getTag(R.id.tag_item_data);
        if (isValid == null || Boolean.FALSE.equals(isValid)) {
            b.getRoot().post(() -> ViewTools.setViewVisibilityIfChanged(b.getRoot(), false));
            return;
        }

        CheckOutSectionAdapter a = null;
        if (recyclerView.getAdapter() instanceof CheckOutSectionAdapter) {
            a = ((CheckOutSectionAdapter) recyclerView.getAdapter());
        }
        if (a == null) {
            return;
        }

        Pair<Integer, Integer> positions = RecyclerViewTools.findVisibleItemPositionRange(recyclerView, false);
        if (positions != null) {
            int memberPlanPosition = CollectionUtils.indexOfFirst(
                    a.getData(),
                    item -> item.getType() == CheckOutSectionAdapter.TYPE_SAVE_MORE_REWARDS
            );
            boolean isInvisible = memberPlanPosition < positions.first || memberPlanPosition > positions.second;
            b.getRoot().post(() -> ViewTools.setViewVisibilityIfChanged(b.getRoot(), isInvisible));
        }
    }

    private void showShippoDialog() {
        if (preCheckoutV2Bean.address_info != null && !TextUtils.isEmpty(preCheckoutV2Bean.address_info.shippo_not_support_desc)) {
            showAddressErrorDialog(preCheckoutV2Bean.address_info.shippo_not_support_desc);
        }
    }

    private void setWrapperColor() {
        if (useWrapper()) {
            TextView tvTitleCenter = getWrapperTitle().findViewById(R.id.tv_title_center);
            tvTitleCenter.setTextColor(getColor(R.color.color_navbar_fg_default));
            ImageView ivTitleLeft = getWrapperTitle().getView(R.id.iv_title_left);
            ViewTools.tintImageView(ivTitleLeft, R.color.color_navbar_fg_default);
        }
    }
}
