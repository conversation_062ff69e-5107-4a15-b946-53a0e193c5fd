package com.sayweee.weee.module.debug.producttrace;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.storage.WStore;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.module.debug.producttrace.bean.ProductTraceBean;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceData;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.module.debug.producttrace.service.ProductTraceApi;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class ProductTraceManager {

    private static final boolean ENABLED = true;
    private static final String KEY_PRODUCT_TRACE_ENABLED = "product_trace_enabled";

    public static final String CONFIG_CHANGED = "product_trace_config_changed";
    public static final String DOMAIN_CHANGED = "product_trace_domain_changed";

    private final AtomicBoolean isEnabled = new AtomicBoolean();
    private final AtomicBoolean isRunning = new AtomicBoolean(true);

    private final Map<ProductTraceKey, ProductTraceData> traceMap;
    private final List<ProductTraceChangeListener> observers;

    private final List<String> traceDomains = new ArrayList<>();
    private int currentTraceDomainIndex = 0;

    private static class Builder {
        private static final ProductTraceManager INSTANCE = new ProductTraceManager();
    }

    private ProductTraceManager() {
        traceMap = new ConcurrentHashMap<>();
        observers = new ArrayList<>();
    }

    public static ProductTraceManager get() {
        return Builder.INSTANCE;
    }

    public void init() {
        isEnabled.set(getEnabledLocal());
    }

    public void clear() {
        traceMap.clear();
        traceDomains.clear();
        currentTraceDomainIndex = 0;
    }

    void registerObserver(@NonNull ProductTraceChangeListener observer) {
        observers.add(observer);
    }

    void unregisterObserver(@NonNull ProductTraceChangeListener observer) {
        observers.remove(observer);
    }

    public boolean isEnabled() {
        return ENABLED && isEnabled.get();
    }

    public void setEnabled(boolean isEnabled) {
        if (ENABLED) {
            boolean oldValue = this.isEnabled.get();
            this.isEnabled.set(isEnabled);
            if (oldValue && !isEnabled) { // on -> off
                clear();
            }
            dispatchTraceChange(CONFIG_CHANGED);
            setEnabledLocal(isEnabled);
        }
    }

    private void setEnabledLocal(boolean isEnabled) {
        WStore.mmkv(MMKVManager.ID_CONFIG).putBoolean(KEY_PRODUCT_TRACE_ENABLED, isEnabled);
    }

    private boolean getEnabledLocal() {
        return WStore.mmkv(MMKVManager.ID_CONFIG).getBoolean(KEY_PRODUCT_TRACE_ENABLED, false);
    }

    public boolean isRunning() {
        return isRunning.get();
    }

    public void setRunning(boolean isRunning) {
        if (ENABLED) {
            this.isRunning.set(isRunning);
            dispatchTraceChange(DOMAIN_CHANGED);
        }
    }

    @Nullable
    public String getTraceDomain() {
        return CollectionUtils.getOrNull(traceDomains, currentTraceDomainIndex);
    }

    public void toggleTraceDomain() {
        int index = currentTraceDomainIndex;
        int max = CollectionUtils.size(traceDomains);
        if (max > 0) {
            index = index + 1;
            if (index >= max) {
                index = 0;
            }
            currentTraceDomainIndex = index;
        }
        dispatchTraceChange(DOMAIN_CHANGED);
    }

    @Nullable
    public ProductTraceData getTrace(@Nullable ProductTraceKey key) {
        if (key != null) {
            return traceMap.get(key);
        }
        return null;
    }

    public void addTasks(@NonNull List<ProductTraceTask> tasks) {
        if (isEnabled()) {
            for (ProductTraceTask task : tasks) {
                startTask(task);
            }
        }
    }

    private void startTask(@NonNull ProductTraceTask task) {
        RequestParams requestParams = new RequestParams();
        requestParams.putNonNull("page_key", task.getPageKey());
        requestParams.putNonNull("mod_nm", task.getModNm());
        requestParams.putNonNull("product_ids", task.getProductIds());
        requestParams.putNonNull("recommendation_trace_id", task.getTraceId());
        requestParams.putNonNull("unique_key", task.getUniqueKey());

        ProductTraceTask.Result result = new ProductTraceTask.Result(task);
        RetrofitIml.get().getHttpService(ProductTraceApi.class)
                .getProductTrace(requestParams.create())
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .flatMap(resp -> mapProductRecommendTrace(resp, result))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new ResponseObserver<ProductTraceTask.Result>() {
                    @Override
                    public void onResponse(ProductTraceTask.Result result) {
                        handleProductSalesTraceResult(result);
                    }
                });
    }

    private Observable<ProductTraceTask.Result> mapProductRecommendTrace(
            ResponseBean<ProductTraceBean> response,
            ProductTraceTask.Result taskResult
    ) {
        return Observable.fromCallable(() -> {
            if (response == null || !response.isSuccess() || response.getData() == null) {
                taskResult.setResult(CollectionUtils.emptyMap());
                return taskResult;
            }
            Map<ProductTraceKey, ProductTraceData> result = new HashMap<>();
            ProductTraceBean data = response.getData();
            String traceId = data.recommendation_trace_id;
            String uniqueKey = data.unique_key;
            for (ProductTraceBean.TraceItem traceItem : data.traces) {
                if (traceItem == null) {
                    continue;
                }
                int status = traceItem.product_status;
                ProductTraceKey key = ProductTraceKey.of(traceItem.product_id, traceId, uniqueKey);
                ProductTraceData bean = new ProductTraceData(key);
                if (traceItem.datas != null) {
                    for (ProductTraceBean.TraceData traceData : traceItem.datas) {
                        CharSequence trace = ProductTraceViewHelper.formatTraceData(traceData, status);
                        bean.putData(traceData.title, trace);
                    }
                }
                result.put(key, bean);
            }
            taskResult.setResult(result);
            return taskResult;
        });
    }

    private void handleProductSalesTraceResult(@NonNull ProductTraceTask.Result result) {
        if (result.isEmpty()) {
            return;
        }

        if (traceDomains.isEmpty()) {
            for (Map.Entry<ProductTraceKey, ProductTraceData> entry : result.getResult().entrySet()) {
                traceDomains.addAll(entry.getValue().getTraceDomains());
                break;
            }
        }
        traceMap.putAll(result.getResult());
        dispatchTraceChange(result.getTopic());
    }

    private void dispatchTraceChange(String topic) {
        for (ProductTraceChangeListener observer : observers) {
            observer.onProductTraceChanged(topic);
        }
    }

    public interface ProductTraceChangeListener {
        void onProductTraceChanged(String topic);
    }

}
