package com.sayweee.weee.module.product.data;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceKey;
import com.sayweee.weee.module.debug.producttrace.data.ProductTraceTask;
import com.sayweee.weee.module.product.bean.PdpItemType;
import com.sayweee.weee.module.product.bean.PdpProductsBean;
import com.sayweee.weee.utils.EmptyUtils;

public class PdpProductsData extends PdpTrackingData<PdpProductsBean>
        implements ProductTraceTask.SectionProvider {

    public int vendorId, productId;

    public PdpProductsData(PdpProductsBean pdpProductsBean) {
        super("often_paired_with".equalsIgnoreCase(pdpProductsBean.module_key) ? PdpItemType.OFTEN_PAIRED_WITH : PdpItemType.PRODUCT_LINE,
                pdpProductsBean);
        modNm = pdpProductsBean.module_key;
    }

    public PdpProductsData(int type, PdpProductsBean pdpProductsBean, int modPos, String traceId) {
        super(type, pdpProductsBean);
        modNm = pdpProductsBean.module_key;
        this.modPos = modPos;
        this.traceId = traceId;
    }

    public boolean hasMoreLink() {
        return t != null
                && !EmptyUtils.isEmpty(t.more_link)
                && !EmptyUtils.isEmpty(t.product_list)
                && t.total_count > t.product_list.size();
    }

    public PdpProductsData setVendorId(int vendorId) {
        this.vendorId = vendorId;
        return this;
    }

    public PdpProductsData setProductId(int productId) {
        this.productId = productId;
        return this;
    }

    public boolean isSeller() {
        return t != null && "seller_recommand".equalsIgnoreCase(t.module_key);
    }

    // =========================================
    // Implementation of ProductSalesTraceTask.SectionProvider interface
    // =========================================
    @Override
    public void assembleProductSalesTraceTask(@NonNull ProductTraceTask.Builder builder) {
        if (t != null && t.isValid()) {
            for (ProductBean product : t.product_list) {
                builder.add(product.recommendation_trace_id, product.id);
            }
            builder.setModNm(modNm);
            builder.setUniqueKey(ProductTraceKey.generateUniqueKey(modNm, null));
        }
    }
}
