package com.sayweee.weee.module.home.provider.category;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.weee.R;
import com.sayweee.weee.module.base.adapter.AdapterViewHolder;
import com.sayweee.weee.module.base.adapter.BoundViewHolders;
import com.sayweee.weee.module.base.adapter.SimpleHorizontalImpressionProvider;
import com.sayweee.weee.module.home.provider.category.data.CmsCategoryData;
import com.sayweee.weee.widget.recycler.RecyclerViewScrollStatePersist;

abstract class CategoryAutoScrollProvider extends SimpleHorizontalImpressionProvider<CmsCategoryData, AdapterViewHolder> {

    private static final RecyclerViewScrollStatePersist.ScrollStateKeyProvider SCROLL_STATE_KEY_PROVIDER =
            new RecyclerViewScrollStatePersist.DefaultScrollStateKeyProviderImpl();

    private final RecyclerViewScrollStatePersist scrollStatePersist;

    protected final BoundViewHolders<AdapterViewHolder> boundViewHolders;

    public CategoryAutoScrollProvider(@NonNull RecyclerViewScrollStatePersist scrollStatePersist) {
        this.scrollStatePersist = scrollStatePersist;
        this.boundViewHolders = new BoundViewHolders<>();
    }

    @Override
    public void onViewAttachedToWindow(AdapterViewHolder helper) {
        super.onViewAttachedToWindow(helper);
        RecyclerView recyclerView = helper.getView(R.id.recyclerView);
        if (recyclerView != null) {
            scrollStatePersist.restoreScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
        }
    }

    @Override
    public void onViewDetachedFromWindow(AdapterViewHolder helper) {
        super.onViewDetachedFromWindow(helper);
        RecyclerView recyclerView = helper.getView(R.id.recyclerView);
        if (recyclerView != null) {
            scrollStatePersist.saveScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
        }
    }

    @Override
    public void onViewHolderCreated(AdapterViewHolder helper) {
        super.onViewHolderCreated(helper);
        setFullSpan(helper);

        RecyclerView recyclerView = helper.getView(R.id.recyclerView);
        recyclerView.getRecycledViewPool().setMaxRecycledViews(0, 30);
        recyclerView.setItemAnimator(null);
        scrollStatePersist.setupRecyclerView(recyclerView, RecyclerView.HORIZONTAL, SCROLL_STATE_KEY_PROVIDER);

        StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, RecyclerView.HORIZONTAL);
        recyclerView.setLayoutManager(layoutManager);

        CmsCategoryBaseAdapter adapter = createAdapter();
        adapter.setAttachView(recyclerView);
        recyclerView.setAdapter(adapter);

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onPageScrollStateChangedImpression(adapter);
                }
            }
        });
        addAdapterToCache(adapter);

        recyclerView.addItemDecoration(createItemDecoration());

        CategoryAutoScroller autoScroller = new CategoryAutoScroller(recyclerView);
        recyclerView.setTag(autoScroller);
        autoScroller.init();
    }

    @Override
    public void convert(AdapterViewHolder helper, CmsCategoryData item) {
        boundViewHolders.add(helper);

        RecyclerView recyclerView = helper.getView(R.id.recyclerView);
        recyclerView.setTag(R.id.tag_scroll_state_key, item.getEventKey());

        int spanCount = item.getDisplaySpanCount();
        RecyclerView.LayoutManager lp = recyclerView.getLayoutManager();
        if (lp instanceof StaggeredGridLayoutManager && ((StaggeredGridLayoutManager) lp).getSpanCount() != spanCount) {
            ((StaggeredGridLayoutManager) lp).setSpanCount(spanCount);
            ((StaggeredGridLayoutManager) lp).invalidateSpanAssignments();
        }

        updateAdapter(recyclerView, item);

        scrollStatePersist.restoreScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);

        CategoryAutoScroller autoScroller = getAutoScroller(helper);
        if (autoScroller != null) {
            autoScroller.setDisabled(!item.isAutoScroll());
            autoScroller.attach();
        }
    }

    @Override
    public void onViewRecycled(AdapterViewHolder helper) {
        super.onViewRecycled(helper);
        boundViewHolders.remove(helper);

        CategoryAutoScroller autoScroller = getAutoScroller(helper);
        if (autoScroller != null) {
            autoScroller.detach();
        }
    }

    @Override
    public void onPageResume(RecyclerView view) {
        super.onPageResume(view);
        for (AdapterViewHolder helper : boundViewHolders.getBoundViewHolders()) {
            RecyclerView recyclerView = helper.getView(R.id.recyclerView);
            scrollStatePersist.restoreScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
            CategoryAutoScroller autoScroller = getAutoScroller(helper);
            if (autoScroller != null) {
                autoScroller.attach();
            }
        }
    }

    @Override
    public void onPagePause(RecyclerView view) {
        super.onPagePause(view);
        for (AdapterViewHolder helper : boundViewHolders.getBoundViewHolders()) {
            RecyclerView recyclerView = helper.getView(R.id.recyclerView);
            CategoryAutoScroller autoScroller = getAutoScroller(helper);
            if (autoScroller != null) {
                if (!autoScroller.isDisabled()) {
                    // Clear scroll state only for auto scroll recyclerView
                    scrollStatePersist.clearScrollState(recyclerView, SCROLL_STATE_KEY_PROVIDER);
                }
                autoScroller.detach();
            }
        }
    }

    @Nullable
    private CategoryAutoScroller getAutoScroller(AdapterViewHolder helper) {
        return (CategoryAutoScroller) helper.getView(R.id.recyclerView).getTag();
    }

    protected abstract CmsCategoryBaseAdapter createAdapter();

    protected abstract RecyclerView.ItemDecoration createItemDecoration();

    protected abstract void updateAdapter(RecyclerView recyclerView, CmsCategoryData item);
}
