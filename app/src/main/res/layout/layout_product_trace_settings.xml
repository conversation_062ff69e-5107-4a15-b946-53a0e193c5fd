<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/root_color_white_static"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="12dp">

        <TextView
            android:id="@+id/tv_function_enable_title"
            style="@style/style_body_sm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:text="Show product recommendation trace"
            android:textColor="@color/color_surface_1_fg_default_idle"
            app:layout_constraintEnd_toStartOf="@id/switch_enable"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_function_enable_desc"
            style="@style/style_fluid_root_badge_label_sm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:text="Show factors of recommendation algorithm, internal use only"
            android:textColor="@color/color_surface_1_fg_minor_idle"
            app:layout_constraintEnd_toEndOf="@id/tv_function_enable_title"
            app:layout_constraintStart_toStartOf="@id/tv_function_enable_title"
            app:layout_constraintTop_toBottomOf="@id/tv_function_enable_title" />

        <com.sayweee.weee.widget.SimpleSwitch
            android:id="@+id/switch_enable"
            android:layout_width="52dp"
            android:layout_height="28dp"
            android:layout_gravity="center_vertical"
            android:thumb="@drawable/selector_simple_switch_thumb"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:track="@drawable/selector_simple_switch_track"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_place" />


</LinearLayout>